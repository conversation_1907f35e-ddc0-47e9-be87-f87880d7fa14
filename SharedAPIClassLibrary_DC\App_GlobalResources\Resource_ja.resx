﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Abandon" xml:space="preserve">
    <value>Ja Abandon</value>
  </data>
  <data name="AbandonDelayReason" xml:space="preserve">
    <value>Ja Abandon &amp; Delay Reason</value>
  </data>
  <data name="Abandoned" xml:space="preserve">
    <value>Ja Abandoned</value>
  </data>
  <data name="Accept" xml:space="preserve">
    <value>Ja Accept</value>
  </data>
  <data name="AcceptedQuantity" xml:space="preserve">
    <value>Ja Accepted Quantity</value>
  </data>
  <data name="AcceptedQuantityCannotbegreaterthanOrderedQty" xml:space="preserve">
    <value>Ja Accepted Quantity Cannot be greater than Ordered Quantity</value>
  </data>
  <data name="AccountNumber" xml:space="preserve">
    <value>Ja Account Number</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Ja Action</value>
  </data>
  <data name="ActionBy" xml:space="preserve">
    <value>Ja Action By</value>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>Ja Action Date</value>
  </data>
  <data name="ActionForNextService" xml:space="preserve">
    <value>Ja Action For Next Service</value>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>SelectPartNumber</value>
  </data>
  <data name="ActionRemarks" xml:space="preserve">
    <value>Ja Action Remarks</value>
  </data>
  <data name="ActionRemarksMaxlimitexceeded" xml:space="preserve">
    <value>Ja Action remarks max limit exceeded</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Ja Actions</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Ja Is Active?</value>
  </data>
  <data name="ActiveFrom" xml:space="preserve">
    <value>Ja Active From</value>
  </data>
  <data name="ActiveFromdatecannotbegreaterthanActiveTodate" xml:space="preserve">
    <value>Ja Active from date cannot be greater than active to date</value>
  </data>
  <data name="ActiveTo" xml:space="preserve">
    <value>Ja Active To</value>
  </data>
  <data name="ActualEndDate" xml:space="preserve">
    <value>Ja Actual End Date</value>
  </data>
  <data name="ActualHours" xml:space="preserve">
    <value>Ja Actual Hours</value>
  </data>
  <data name="ActualStartDate" xml:space="preserve">
    <value>Ja Actual Start Date</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Ja Add</value>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>Ja Add Action</value>
  </data>
  <data name="AddActivity" xml:space="preserve">
    <value>Ja Add Activity</value>
  </data>
  <data name="AddArticle" xml:space="preserve">
    <value>Ja Add Article</value>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>Ja Add Branch</value>
  </data>
  <data name="AddBranchTaxDetails" xml:space="preserve">
    <value>Ja Add Branch Tax Details</value>
  </data>
  <data name="AddBrands" xml:space="preserve">
    <value>Ja Add Brands</value>
  </data>
  <data name="AddCampaingDetails" xml:space="preserve">
    <value>Ja Add Campaign</value>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>Ja Add Company</value>
  </data>
  <data name="AddCompanyRelation" xml:space="preserve">
    <value>Ja Add Company Relation</value>
  </data>
  <data name="AddCompanyTaxDetails" xml:space="preserve">
    <value>Ja Add Company Tax Details</value>
  </data>
  <data name="AddCompanyTerms" xml:space="preserve">
    <value>Ja Add Company Terms</value>
  </data>
  <data name="addcomponentdetails" xml:space="preserve">
    <value>Ja Add Component Details</value>
  </data>
  <data name="addcustomer" xml:space="preserve">
    <value>Ja Add Customer</value>
  </data>
  <data name="addcustomerorderclass" xml:space="preserve">
    <value>Ja Add Customer Order Class</value>
  </data>
  <data name="AddCustomerQuotation" xml:space="preserve">
    <value>Ja Add Customer Quotation</value>
  </data>
  <data name="AddEmployee" xml:space="preserve">
    <value>Ja Add Employee</value>
  </data>
  <data name="AddEvents" xml:space="preserve">
    <value>Ja Add Events</value>
  </data>
  <data name="AddFilter" xml:space="preserve">
    <value>Ja Add Filter</value>
  </data>
  <data name="addfreestock" xml:space="preserve">
    <value>Ja Add Parts Free Stock</value>
  </data>
  <data name="AddFunctionGroup" xml:space="preserve">
    <value>Ja Add Function Group</value>
  </data>
  <data name="AddGDRSettlement" xml:space="preserve">
    <value>Ja Add GDR Settlement</value>
  </data>
  <data name="AddInternalInvoice" xml:space="preserve">
    <value>Ja Add Internal Invoice</value>
  </data>
  <data name="AddInternalInvoiceReturn" xml:space="preserve">
    <value>Ja Add Internal Invoice Return</value>
  </data>
  <data name="AddJobCard" xml:space="preserve">
    <value>Ja Add Job Card</value>
  </data>
  <data name="AddJobCardPartsReturn" xml:space="preserve">
    <value>Ja Add Job Card Parts Return</value>
  </data>
  <data name="AddKitBom" xml:space="preserve">
    <value>Ja Add Kit Bom</value>
  </data>
  <data name="AddKitBreaking" xml:space="preserve">
    <value>Ja Add Kit Breaking</value>
  </data>
  <data name="AddMakingofKit" xml:space="preserve">
    <value>Ja Add Making of Kit</value>
  </data>
  <data name="AddMandatoryClaim" xml:space="preserve">
    <value>Ja Add Mandatory Claim</value>
  </data>
  <data name="AddMaster" xml:space="preserve">
    <value>Ja Add Master</value>
  </data>
  <data name="addmodel" xml:space="preserve">
    <value>Ja Add Model</value>
  </data>
  <data name="addnewpart" xml:space="preserve">
    <value>Ja Add New Part</value>
  </data>
  <data name="AddNullPick" xml:space="preserve">
    <value>Ja Add Null Pick</value>
  </data>
  <data name="addoperation" xml:space="preserve">
    <value>Ja Add Operation</value>
  </data>
  <data name="addoperation1" xml:space="preserve">
    <value>Ja Add Operation Details</value>
  </data>
  <data name="AddOperationEmployeeDetails" xml:space="preserve">
    <value>Ja Add Operation Employee Details</value>
  </data>
  <data name="AddPart" xml:space="preserve">
    <value>Ja Add Part</value>
  </data>
  <data name="AddPartFreeStockDetails" xml:space="preserve">
    <value>Ja Add Part Free Stock Details</value>
  </data>
  <data name="addpartprice" xml:space="preserve">
    <value>Ja Add Part Price</value>
  </data>
  <data name="AddParts" xml:space="preserve">
    <value>Ja Add Parts</value>
  </data>
  <data name="AddParty" xml:space="preserve">
    <value>Ja Add Party</value>
  </data>
  <data name="AddPrefixSuffix" xml:space="preserve">
    <value>Ja Add Prefix Suffix</value>
  </data>
  <data name="addproduct" xml:space="preserve">
    <value>Ja Add Product</value>
  </data>
  <data name="addproductdetail" xml:space="preserve">
    <value>Ja Add Product Detail</value>
  </data>
  <data name="addproducttype" xml:space="preserve">
    <value>Ja Add Product Type</value>
  </data>
  <data name="addproducttypedetails" xml:space="preserve">
    <value>Ja Add Part Product Type Details</value>
  </data>
  <data name="AddPurchaseGRN" xml:space="preserve">
    <value>Ja Add Purchase GRN</value>
  </data>
  <data name="AddPurchaseInvoice" xml:space="preserve">
    <value>Ja Add Purchase Invoice</value>
  </data>
  <data name="AddPurchaseOrder" xml:space="preserve">
    <value>Ja Add Purchase Order</value>
  </data>
  <data name="AddPurchaseOrderCancellation" xml:space="preserve">
    <value>Ja Add Purchase Order Cancellation</value>
  </data>
  <data name="AddRequest" xml:space="preserve">
    <value>Ja Add Request</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Ja Address</value>
  </data>
  <data name="Address1" xml:space="preserve">
    <value>Ja Address Line 1</value>
  </data>
  <data name="Address2" xml:space="preserve">
    <value>Ja Address Line 2</value>
  </data>
  <data name="Address3" xml:space="preserve">
    <value>Ja Address Line 3</value>
  </data>
  <data name="AddresseFlag" xml:space="preserve">
    <value>Ja Addresse Flag</value>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>Ja Add Role</value>
  </data>
  <data name="addServiceCharge" xml:space="preserve">
    <value>Ja Add Service Charge</value>
  </data>
  <data name="addServiceChargeDetails" xml:space="preserve">
    <value>Ja Add Service Charge Details</value>
  </data>
  <data name="addservicecharges" xml:space="preserve">
    <value>Ja Add Service Charges</value>
  </data>
  <data name="AddServiceType" xml:space="preserve">
    <value>Ja Add Service Type</value>
  </data>
  <data name="AddSibling" xml:space="preserve">
    <value>Ja Add Sibling</value>
  </data>
  <data name="addsiteaddress" xml:space="preserve">
    <value>Ja Add Site Address</value>
  </data>
  <data name="AddSkills" xml:space="preserve">
    <value>Ja Add Skills</value>
  </data>
  <data name="AddSpecialization" xml:space="preserve">
    <value>Ja Add Specialization</value>
  </data>
  <data name="AddStep" xml:space="preserve">
    <value>Ja Add Step</value>
  </data>
  <data name="AddStepLink" xml:space="preserve">
    <value>Ja Add Step Link</value>
  </data>
  <data name="AddStockBlocking" xml:space="preserve">
    <value>Ja Add Stock Blocking</value>
  </data>
  <data name="AddStockCheckRequest" xml:space="preserve">
    <value>Ja Add Stock Check Request</value>
  </data>
  <data name="AddSubfolder" xml:space="preserve">
    <value>Ja Add Subfolder</value>
  </data>
  <data name="AddSundry" xml:space="preserve">
    <value>Ja Add Sundry</value>
  </data>
  <data name="AddSupersession" xml:space="preserve">
    <value>Ja Add Supersession</value>
  </data>
  <data name="addSupplierorderclass" xml:space="preserve">
    <value>Ja Add Supplier order class</value>
  </data>
  <data name="AddTaxCode" xml:space="preserve">
    <value>Ja Add Tax Code</value>
  </data>
  <data name="addtaxstructure" xml:space="preserve">
    <value>Ja Add tax Structure</value>
  </data>
  <data name="AddTaxStructureDetails" xml:space="preserve">
    <value>Ja Add Tax Structure Details</value>
  </data>
  <data name="addtaxtstructuredetails" xml:space="preserve">
    <value>Ja Add Tax Structure Details</value>
  </data>
  <data name="AddTool" xml:space="preserve">
    <value>Ja Add Tool</value>
  </data>
  <data name="AddTravel" xml:space="preserve">
    <value>Ja Add Travel</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>Ja Add User</value>
  </data>
  <data name="addwarrantydetails" xml:space="preserve">
    <value>Ja Add Warranty Details</value>
  </data>
  <data name="AddWorkDetails" xml:space="preserve">
    <value>Ja Add Work Details</value>
  </data>
  <data name="Adjusted" xml:space="preserve">
    <value>Ja Adjusted</value>
  </data>
  <data name="advancesearch" xml:space="preserve">
    <value>Ja Advance Search</value>
  </data>
  <data name="aliaspartnumber" xml:space="preserve">
    <value>Ja Alias Part Number</value>
  </data>
  <data name="all" xml:space="preserve">
    <value>Ja All</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>Ja Allocate</value>
  </data>
  <data name="AllocatedHours" xml:space="preserve">
    <value>Ja Allocated Hours</value>
  </data>
  <data name="AllocatedQty" xml:space="preserve">
    <value>Ja Allocated Quantity</value>
  </data>
  <data name="Allocation" xml:space="preserve">
    <value>Ja Allocation</value>
  </data>
  <data name="AllocationNotPossible" xml:space="preserve">
    <value>Ja Allocation Not Possible</value>
  </data>
  <data name="AllocationofParts" xml:space="preserve">
    <value>Ja Allocation of Parts</value>
  </data>
  <data name="AllocationPriority" xml:space="preserve">
    <value>Ja Allocation Priority</value>
  </data>
  <data name="AlloctedSuccessfully" xml:space="preserve">
    <value>Ja Allocated Successfully</value>
  </data>
  <data name="AlloctedSuccessfully1" xml:space="preserve">
    <value>Ja Allocted successfully</value>
  </data>
  <data name="AlloctionFailed" xml:space="preserve">
    <value>Ja Allocation failed</value>
  </data>
  <data name="AllOperationShouldBeCompleted" xml:space="preserve">
    <value>Ja All operation should be completed</value>
  </data>
  <data name="AllQueue" xml:space="preserve">
    <value>Ja All Queue</value>
  </data>
  <data name="alreadyakitpart" xml:space="preserve">
    <value>Ja Already a kitpart</value>
  </data>
  <data name="AlreadyAssociatedPleaseSelectfromDropDown" xml:space="preserve">
    <value>Ja Already Associated Please Select from Drop Down</value>
  </data>
  <data name="alreadyexists" xml:space="preserve">
    <value>Ja already exists</value>
  </data>
  <data name="alreadyexistsforthelocation" xml:space="preserve">
    <value>Ja already exists for the location</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Ja Amount</value>
  </data>
  <data name="AmountBlank" xml:space="preserve">
    <value>Ja Amount is  blank</value>
  </data>
  <data name="AmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>Ja Amount is beyond acceptable limit</value>
  </data>
  <data name="AmountShouldbeLessThan" xml:space="preserve">
    <value>Ja Amount should be less than</value>
  </data>
  <data name="AND" xml:space="preserve">
    <value>Ja AND</value>
  </data>
  <data name="ApprovalLimit" xml:space="preserve">
    <value>Ja Approval Limit</value>
  </data>
  <data name="ApprovalStatus" xml:space="preserve">
    <value>Ja Approval Status</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>Ja Approve</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Ja Approved</value>
  </data>
  <data name="ApprovedAmount" xml:space="preserve">
    <value>Ja Approved Amount</value>
  </data>
  <data name="ApprovedAmountCannotBeGreaterThanClaimedAmount" xml:space="preserve">
    <value>Ja Approved amount cannot be greater Than claimed amount</value>
  </data>
  <data name="ApprovedQuantity" xml:space="preserve">
    <value>Ja Approved Quantity</value>
  </data>
  <data name="ApprovedQuantityCannotBeGreaterThanQuantity" xml:space="preserve">
    <value>Ja Approved quantity cannot be greater than quantity</value>
  </data>
  <data name="April" xml:space="preserve">
    <value>Ja Apr</value>
  </data>
  <data name="Aresurewanttodelete" xml:space="preserve">
    <value>Ja Are sure want to delete</value>
  </data>
  <data name="Areyousurewanttocancel" xml:space="preserve">
    <value>Ja Are you sure you want to cancel?</value>
  </data>
  <data name="AreYouSureWantToDel" xml:space="preserve">
    <value>Ja Are you sure want to Delete </value>
  </data>
  <data name="Areyousurewanttodelete" xml:space="preserve">
    <value>Ja Are you sure you want to delete?</value>
  </data>
  <data name="AreyousureyouwanttoAbandontheRequest" xml:space="preserve">
    <value>Ja Are you sure you want to abandon the request</value>
  </data>
  <data name="AreyousureyouwanttoLogout" xml:space="preserve">
    <value>Ja Are you sure you want to Log out</value>
  </data>
  <data name="areyousureyouwanttomovetheevent" xml:space="preserve">
    <value>Ja Are you sure you want to move the event?</value>
  </data>
  <data name="ArticleName" xml:space="preserve">
    <value>Ja Article Name</value>
  </data>
  <data name="Assaign" xml:space="preserve">
    <value>Ja Assign</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>Ja Assign</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>Ja Assigned To</value>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>Ja Assign To</value>
  </data>
  <data name="atleastonerecordshouldbeaddedtopartsdetailgrid" xml:space="preserve">
    <value>Ja Atleast one record should be added to Parts Detail grid</value>
  </data>
  <data name="atleastonerecordshouldbeaddedtosuperseedingpartpartsdetailgrid" xml:space="preserve">
    <value>Ja Atleast one record should be added to Superseeding Parts Detail grid</value>
  </data>
  <data name="Atleastselectonedetail" xml:space="preserve">
    <value>Ja Atleast select one detail</value>
  </data>
  <data name="AttachmentDetail" xml:space="preserve">
    <value>Ja Attachment Detail</value>
  </data>
  <data name="AttachmentDetails" xml:space="preserve">
    <value>Ja Attachment Details</value>
  </data>
  <data name="Attachments" xml:space="preserve">
    <value>Ja Attachments</value>
  </data>
  <data name="August" xml:space="preserve">
    <value>Ja Aug</value>
  </data>
  <data name="AuthorizedSignatory" xml:space="preserve">
    <value>Ja Authorized Signatory</value>
  </data>
  <data name="autoallocate" xml:space="preserve">
    <value>Ja Auto Allocate</value>
  </data>
  <data name="AutoAllocationAllowed" xml:space="preserve">
    <value>Ja Auto Allocation Allowed</value>
  </data>
  <data name="AutoAllocationCondition" xml:space="preserve">
    <value>Ja Auto Allocation Condition</value>
  </data>
  <data name="AutoAllocationFieldName" xml:space="preserve">
    <value>Ja Auto Allocation Field Name</value>
  </data>
  <data name="AverageResolutionTime" xml:space="preserve">
    <value>Ja Average Resolution Time</value>
  </data>
  <data name="averageresolutiontimeyearwise" xml:space="preserve">
    <value>Ja Average Resolution Time - Year Wise</value>
  </data>
  <data name="averageresponsetime" xml:space="preserve">
    <value>Ja Average Response Time</value>
  </data>
  <data name="averageresponsetimeyearwise" xml:space="preserve">
    <value>Ja Average Response Time -Year Wise</value>
  </data>
  <data name="AverageTime" xml:space="preserve">
    <value>Ja Average Time</value>
  </data>
  <data name="AvgResolutionTime" xml:space="preserve">
    <value>Ja Avg Resolution Time</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Ja Back</value>
  </data>
  <data name="BackOrderQty" xml:space="preserve">
    <value>Ja BO Quantity</value>
  </data>
  <data name="BacktoBackOrder" xml:space="preserve">
    <value>Ja Back to Back Order</value>
  </data>
  <data name="BankName" xml:space="preserve">
    <value>Ja Bank Name</value>
  </data>
  <data name="BarChart" xml:space="preserve">
    <value>Ja Bar Chart</value>
  </data>
  <data name="BillingCurrency" xml:space="preserve">
    <value>Ja Billing Currency</value>
  </data>
  <data name="binlocation" xml:space="preserve">
    <value>Ja Bin Location</value>
  </data>
  <data name="BinLocationCannotbesameasBufferBinLocation" xml:space="preserve">
    <value>Ja Bin Location Cannot be same as Buffer Bin Location</value>
  </data>
  <data name="BinLocationDetails" xml:space="preserve">
    <value>Ja Bin Location Details</value>
  </data>
  <data name="binstock" xml:space="preserve">
    <value>Ja Bin Stock</value>
  </data>
  <data name="BlockedBy" xml:space="preserve">
    <value>Ja Blocked By</value>
  </data>
  <data name="BlockedQuantity" xml:space="preserve">
    <value>Ja Blocked  Quantity</value>
  </data>
  <data name="BlockedQuantityisGreaterthenFreeStock" xml:space="preserve">
    <value>Ja Blocked Quantity is Greater then FreeStock</value>
  </data>
  <data name="BOQuantity" xml:space="preserve">
    <value>Ja BO Quantity</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Ja Branch</value>
  </data>
  <data name="BranchAlrearySelected" xml:space="preserve">
    <value>Ja Branch Alreary Selected</value>
  </data>
  <data name="BranchAssociation" xml:space="preserve">
    <value>Ja Branch Association</value>
  </data>
  <data name="branchdetail" xml:space="preserve">
    <value>Ja Branch Detail</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>Ja Branch Name</value>
  </data>
  <data name="BranchNameisalreadypresent" xml:space="preserve">
    <value>Ja Duplicate Branch Name</value>
  </data>
  <data name="BranchTaxCode" xml:space="preserve">
    <value>Ja Branch Tax Code</value>
  </data>
  <data name="BranchTaxDetails" xml:space="preserve">
    <value>Ja Branch Tax Details</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Ja Brand</value>
  </data>
  <data name="BrandName" xml:space="preserve">
    <value>Ja Brand Name</value>
  </data>
  <data name="BrandsAssociation" xml:space="preserve">
    <value>Ja Brands Association</value>
  </data>
  <data name="Break" xml:space="preserve">
    <value>Ja Break</value>
  </data>
  <data name="BreakFrom" xml:space="preserve">
    <value>Ja Break From</value>
  </data>
  <data name="BreakFromandtotimeshouldbewithininworkinghour" xml:space="preserve">
    <value>Ja Break from time and Break to time Should be Within Working hours</value>
  </data>
  <data name="BreakFromTimeCannotbeGreaterThanBreakTotime" xml:space="preserve">
    <value>Ja Break From Time Can not be Greater Than Break To Time</value>
  </data>
  <data name="BreakHourscannotbegreatorthanWorkingHours" xml:space="preserve">
    <value>Ja Break Hours cannot be greator than Working Hours</value>
  </data>
  <data name="BreakTo" xml:space="preserve">
    <value>Ja Break To</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>Ja Browse</value>
  </data>
  <data name="BtnCancelCampaingHeader" xml:space="preserve">
    <value>Ja Cancel</value>
  </data>
  <data name="bufferbinlocation" xml:space="preserve">
    <value>Ja Buffer Bin Location</value>
  </data>
  <data name="BufferBinLocationCannotbesameasBinLocation" xml:space="preserve">
    <value>Ja Buffer Bin Location Cannot be same as Bin Location</value>
  </data>
  <data name="BuyingCurrency" xml:space="preserve">
    <value>Ja Buying Currency</value>
  </data>
  <data name="calculateformula" xml:space="preserve">
    <value>Ja Calculate Formula</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>Ja Calendar</value>
  </data>
  <data name="CallBackDate" xml:space="preserve">
    <value>Ja Call Back Date</value>
  </data>
  <data name="CallBackTime" xml:space="preserve">
    <value>Ja Call Back Time</value>
  </data>
  <data name="CallClosureDate" xml:space="preserve">
    <value>Ja Call Closure Date</value>
  </data>
  <data name="CallClosureTime" xml:space="preserve">
    <value>Ja Call Closure Time</value>
  </data>
  <data name="CallComplexity" xml:space="preserve">
    <value>Ja Call Complexity</value>
  </data>
  <data name="CallDate" xml:space="preserve">
    <value>Ja Call Date</value>
  </data>
  <data name="CallDateCanNotBeGreaterThanCurrentDate" xml:space="preserve">
    <value>Ja Call Date and Time cannot be greater than current Date and Time</value>
  </data>
  <data name="CallDescription" xml:space="preserve">
    <value>Ja Call Description</value>
  </data>
  <data name="CallDetails" xml:space="preserve">
    <value>Ja Call Details</value>
  </data>
  <data name="CallHistory" xml:space="preserve">
    <value>Ja Call History</value>
  </data>
  <data name="CallMode" xml:space="preserve">
    <value>Ja Call Mode</value>
  </data>
  <data name="CallNature" xml:space="preserve">
    <value>Ja Call Nature</value>
  </data>
  <data name="CallPriority" xml:space="preserve">
    <value>Ja Call Priority</value>
  </data>
  <data name="CallStatus" xml:space="preserve">
    <value>Ja Call Status</value>
  </data>
  <data name="CallTime" xml:space="preserve">
    <value>Ja Call Time</value>
  </data>
  <data name="CallType" xml:space="preserve">
    <value>Ja Call Type</value>
  </data>
  <data name="Campaign" xml:space="preserve">
    <value>Ja Campaign</value>
  </data>
  <data name="CampaignCode" xml:space="preserve">
    <value>Ja Campaign Code</value>
  </data>
  <data name="CampaignHeader" xml:space="preserve">
    <value>Ja Header</value>
  </data>
  <data name="CampaignName" xml:space="preserve">
    <value>Ja Campaign Name</value>
  </data>
  <data name="CampaingMachineDetails" xml:space="preserve">
    <value>Ja Campaing Machine Details</value>
  </data>
  <data name="CampaingOperationDetails" xml:space="preserve">
    <value>Ja Campaing Operation Details</value>
  </data>
  <data name="CampaingPartsDetails" xml:space="preserve">
    <value>Ja Campaing Parts Details</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Ja Cancel</value>
  </data>
  <data name="CancellationNumber" xml:space="preserve">
    <value>Ja Cancellation Number</value>
  </data>
  <data name="CancellationQuantitycannotbegreaterthan" xml:space="preserve">
    <value>Ja Cancellation Quantity cannot be greater than</value>
  </data>
  <data name="CancelledQuantity" xml:space="preserve">
    <value>Ja Cancelled Quantity</value>
  </data>
  <data name="CancelledQuantityCannotbegreaterthan" xml:space="preserve">
    <value>Ja Cancelled Quantity Cannot be greater than </value>
  </data>
  <data name="CancelledReason" xml:space="preserve">
    <value>Ja Cancelled Reason</value>
  </data>
  <data name="cannotclosethejobwithoutproductdetails" xml:space="preserve">
    <value>Ja Cannot close the job without product details</value>
  </data>
  <data name="CannotdeleteasTaxTypeisreferenced" xml:space="preserve">
    <value>Ja Cannot delete as Tax Type is referenced</value>
  </data>
  <data name="CannotDeleteinEditMode" xml:space="preserve">
    <value>Ja Cannot delete in edit mode</value>
  </data>
  <data name="cannotselectpartfromparentcompany" xml:space="preserve">
    <value>Ja Cannot select part from parent company</value>
  </data>
  <data name="capslockison" xml:space="preserve">
    <value>Ja Caps Lock is on</value>
  </data>
  <data name="CaseDetails" xml:space="preserve">
    <value>Ja Case Details</value>
  </data>
  <data name="CaseDistributionChart" xml:space="preserve">
    <value>Ja Case Distribution Chart</value>
  </data>
  <data name="CaseHistoryReport" xml:space="preserve">
    <value>Ja Case History</value>
  </data>
  <data name="CaseNumber" xml:space="preserve">
    <value>Ja Case Number</value>
  </data>
  <data name="CaseProgress" xml:space="preserve">
    <value>Ja Case Progress</value>
  </data>
  <data name="CaseProgressHistory" xml:space="preserve">
    <value>Ja Case Progress History</value>
  </data>
  <data name="CaseRegistration" xml:space="preserve">
    <value>Ja Case Registration</value>
  </data>
  <data name="CaseRegistrationCount" xml:space="preserve">
    <value>Ja Case Registration Count</value>
  </data>
  <data name="caseregistrationnumber" xml:space="preserve">
    <value>Ja Case Registration Number</value>
  </data>
  <data name="CaseSummary" xml:space="preserve">
    <value>Ja Case Summary</value>
  </data>
  <data name="CaseType" xml:space="preserve">
    <value>Ja Case Type</value>
  </data>
  <data name="CauseofFailure" xml:space="preserve">
    <value>Ja Cause of Failure</value>
  </data>
  <data name="CausingPartDetails" xml:space="preserve">
    <value>Ja Causing Part Details</value>
  </data>
  <data name="center" xml:space="preserve">
    <value>Ja center</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Ja Change Password</value>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed" xml:space="preserve">
    <value>Ja Are you sure you want to cancel?</value>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed1" xml:space="preserve">
    <value>Ja Changes will be lost do you want to proceed</value>
  </data>
  <data name="ChargeTo" xml:space="preserve">
    <value>Ja Charge To</value>
  </data>
  <data name="Checklist" xml:space="preserve">
    <value>Ja Checklist</value>
  </data>
  <data name="checklistdetail" xml:space="preserve">
    <value>Ja Check List Detail</value>
  </data>
  <data name="ChooseColumnNames" xml:space="preserve">
    <value>Ja Choose column names</value>
  </data>
  <data name="ClaimDate" xml:space="preserve">
    <value>Ja Claim Date</value>
  </data>
  <data name="ClaimedAmount" xml:space="preserve">
    <value>Ja Claimed Amount</value>
  </data>
  <data name="ClaimedAmountcannotbezero" xml:space="preserve">
    <value>Ja Claimed Amount cannot be zero</value>
  </data>
  <data name="ClaimHistory" xml:space="preserve">
    <value>Ja Claim History</value>
  </data>
  <data name="ClaimNumber" xml:space="preserve">
    <value>Ja Claim #</value>
  </data>
  <data name="ClaimStatus" xml:space="preserve">
    <value>Ja Claim Status</value>
  </data>
  <data name="ClearanceCharge" xml:space="preserve">
    <value>Ja Clearance Charge</value>
  </data>
  <data name="clearformula" xml:space="preserve">
    <value>Ja Clear Formula</value>
  </data>
  <data name="ClearingAgent" xml:space="preserve">
    <value>Ja Clearing Agent</value>
  </data>
  <data name="close" xml:space="preserve">
    <value>Ja Close</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Ja Closed</value>
  </data>
  <data name="ClosedCount" xml:space="preserve">
    <value>Ja Closed Count</value>
  </data>
  <data name="ClosedDate" xml:space="preserve">
    <value>Ja Closed Date</value>
  </data>
  <data name="ClosureDate" xml:space="preserve">
    <value>Ja Closure Date</value>
  </data>
  <data name="ClosureDetails" xml:space="preserve">
    <value>Ja Closure Details</value>
  </data>
  <data name="closurereason" xml:space="preserve">
    <value>Ja Closure Reason</value>
  </data>
  <data name="ClosureRemarks" xml:space="preserve">
    <value>Ja Closure Remarks</value>
  </data>
  <data name="ClosureType" xml:space="preserve">
    <value>Ja Closure Type</value>
  </data>
  <data name="code" xml:space="preserve">
    <value>Ja Code</value>
  </data>
  <data name="ColumnNames" xml:space="preserve">
    <value>Ja Column Names</value>
  </data>
  <data name="CombinationAlreadyExists" xml:space="preserve">
    <value>Ja Combination already exists</value>
  </data>
  <data name="commissioningdate" xml:space="preserve">
    <value>Ja Commissioning Date</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Ja Company</value>
  </data>
  <data name="CompanyBrands" xml:space="preserve">
    <value>Ja Company Brands</value>
  </data>
  <data name="CompanyCalender" xml:space="preserve">
    <value>Ja Company Calendar</value>
  </data>
  <data name="CompanyEmployeeName" xml:space="preserve">
    <value>Ja Company Employee Name</value>
  </data>
  <data name="companyemployeesearch" xml:space="preserve">
    <value>Ja Company Employee Search</value>
  </data>
  <data name="CompanyFinancialYear" xml:space="preserve">
    <value>Ja Company Financial Year</value>
  </data>
  <data name="CompanyHeader" xml:space="preserve">
    <value>Ja Header</value>
  </data>
  <data name="CompanyMaster" xml:space="preserve">
    <value>Ja Company Master</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Ja Company Name</value>
  </data>
  <data name="CompanyNameisalreadypresent" xml:space="preserve">
    <value>Ja Company Name is already present</value>
  </data>
  <data name="CompanyRelation" xml:space="preserve">
    <value>Ja Company-Company Relation</value>
  </data>
  <data name="CompanyRelationships" xml:space="preserve">
    <value>Ja Company Relationships</value>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand" xml:space="preserve">
    <value>Ja Company header saved success fully, Please associate atleast one Brand, Please Create One Branch, Employee and associate Employeee to Branch</value>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand1" xml:space="preserve">
    <value>Ja Company header saved successfully, please associate at least one Brand, Please Create One Branch, Employee and associate Employee to Branch</value>
  </data>
  <data name="CompanyTaxCode" xml:space="preserve">
    <value>Ja Company Tax Code</value>
  </data>
  <data name="CompanyTaxDetails" xml:space="preserve">
    <value>Ja Company Tax Details</value>
  </data>
  <data name="CompanyTerms" xml:space="preserve">
    <value>Ja Company Terms</value>
  </data>
  <data name="CompanyTheme" xml:space="preserve">
    <value>Ja Company Theme</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Ja Company Type</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Ja Completed</value>
  </data>
  <data name="CompletedCount" xml:space="preserve">
    <value>Ja Completed Count</value>
  </data>
  <data name="CompleteEnteringDetail" xml:space="preserve">
    <value>Ja Complete entering detail</value>
  </data>
  <data name="CompleteEnteringDetailParts" xml:space="preserve">
    <value>Ja Complete entering parts detail</value>
  </data>
  <data name="CompleteEnteringDetailService" xml:space="preserve">
    <value>Ja Complete entering service detail</value>
  </data>
  <data name="CompleteEnteringDetailSundry" xml:space="preserve">
    <value>Ja Complete entering sundry detail</value>
  </data>
  <data name="Complexity" xml:space="preserve">
    <value>Ja Complexity</value>
  </data>
  <data name="componentdetails" xml:space="preserve">
    <value>Ja Component Details</value>
  </data>
  <data name="ComponentReading" xml:space="preserve">
    <value>Ja Component Reading</value>
  </data>
  <data name="componentreadingdetails" xml:space="preserve">
    <value>Ja Component Reading Details</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Ja Confirm Password</value>
  </data>
  <data name="Consignee" xml:space="preserve">
    <value>Ja Consignee</value>
  </data>
  <data name="ConsigneeAddress" xml:space="preserve">
    <value>Ja Consignee Address</value>
  </data>
  <data name="ConsigneeLocation" xml:space="preserve">
    <value>Ja Consignee Location</value>
  </data>
  <data name="ConsumptionCode" xml:space="preserve">
    <value>Ja Consumption Code</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>Ja Contact Person</value>
  </data>
  <data name="ContactPersonMobileNumber" xml:space="preserve">
    <value>Ja Contact Person Mobile Number</value>
  </data>
  <data name="ContactPersons" xml:space="preserve">
    <value>Ja Contact Persons</value>
  </data>
  <data name="ConversionFactor" xml:space="preserve">
    <value>Ja Conversion Factor</value>
  </data>
  <data name="CopyRoleFrom" xml:space="preserve">
    <value>Ja Copy Role From</value>
  </data>
  <data name="CorrectiveAction" xml:space="preserve">
    <value>Ja Corrective Action</value>
  </data>
  <data name="CostCenter" xml:space="preserve">
    <value>Ja Cost Center</value>
  </data>
  <data name="CostCenterDescription" xml:space="preserve">
    <value>Ja Cost Center Description</value>
  </data>
  <data name="CostPrice" xml:space="preserve">
    <value>Ja Cost Price</value>
  </data>
  <data name="Count" xml:space="preserve">
    <value>Ja Count</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Ja Country</value>
  </data>
  <data name="CouponNumber" xml:space="preserve">
    <value>Ja Coupon Number</value>
  </data>
  <data name="CRCount" xml:space="preserve">
    <value>Ja Case Registration Count</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Ja Created</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>Ja Created By</value>
  </data>
  <data name="CreatedDate" xml:space="preserve">
    <value>Ja Created Date</value>
  </data>
  <data name="CreateJobCard" xml:space="preserve">
    <value>Ja Create JobCard</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Ja Create New</value>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>Ja Create Quotation</value>
  </data>
  <data name="CreateServiceRequest" xml:space="preserve">
    <value>Ja Create Service Request</value>
  </data>
  <data name="CreditDebitNote" xml:space="preserve">
    <value>Ja Credit Debit Note</value>
  </data>
  <data name="CreditOrDebitNoteNumber" xml:space="preserve">
    <value>Ja Credit/Debit Note #</value>
  </data>
  <data name="crictical" xml:space="preserve">
    <value>Ja Critical</value>
  </data>
  <data name="Critical" xml:space="preserve">
    <value>Ja Critical</value>
  </data>
  <data name="ServiceAgreementDetails" xml:space="preserve">
    <value>Ja Service Agreement Details</value>
  </data>
  <data name="ServiceAgreementNumber" xml:space="preserve">
    <value>Ja Service Agreement Number</value>
  </data>
  <data name="ServiceAgreementServiceHistory" xml:space="preserve">
    <value>Ja Service Agreement Service History</value>
  </data>
  <data name="ServiceAgreementType" xml:space="preserve">
    <value>Ja Service Agreement Type</value>
  </data>
  <data name="ServiceAgreementValue" xml:space="preserve">
    <value>Ja Service Agreement Value</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Ja Currency</value>
  </data>
  <data name="CurrentReading" xml:space="preserve">
    <value>Ja Current Reading</value>
  </data>
  <data name="CurrentSiteAddress" xml:space="preserve">
    <value>Ja Current Site Address</value>
  </data>
  <data name="CurrentStep" xml:space="preserve">
    <value>Ja Current Step</value>
  </data>
  <data name="CustomDuty" xml:space="preserve">
    <value>Ja Custom's Duty</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Ja Customer</value>
  </data>
  <data name="CustomerComplaint" xml:space="preserve">
    <value>Ja Customer Complaint</value>
  </data>
  <data name="CustomerContact" xml:space="preserve">
    <value>Ja Contact Person</value>
  </data>
  <data name="CustomerContact1" xml:space="preserve">
    <value>Ja Customer Contact</value>
  </data>
  <data name="CustomerContactPhone" xml:space="preserve">
    <value>Ja Contact Person Mobile</value>
  </data>
  <data name="CustomerContactPhone1" xml:space="preserve">
    <value>Ja Customer Contact Phone</value>
  </data>
  <data name="customerdetails" xml:space="preserve">
    <value>Ja Customer Details</value>
  </data>
  <data name="customerdueservices" xml:space="preserve">
    <value>Ja Customer Due for Service</value>
  </data>
  <data name="customerdueservices1" xml:space="preserve">
    <value>Ja Customer Due Services</value>
  </data>
  <data name="CustomerisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>Ja Customer is locked, do you want to continue?</value>
  </data>
  <data name="Customerisnotactive" xml:space="preserve">
    <value>Ja Customer is not active</value>
  </data>
  <data name="CustomerLocation" xml:space="preserve">
    <value>Ja Customer Location</value>
  </data>
  <data name="customername" xml:space="preserve">
    <value>Ja Customer Name</value>
  </data>
  <data name="CustomerNameorBranch" xml:space="preserve">
    <value>Ja Customer Name/Branch</value>
  </data>
  <data name="CustomerNotFound" xml:space="preserve">
    <value>Ja Customer not found</value>
  </data>
  <data name="customerorderclass" xml:space="preserve">
    <value>Ja Customer Order Class</value>
  </data>
  <data name="CustomerProvided" xml:space="preserve">
    <value>Ja Customer Provided</value>
  </data>
  <data name="CustomerQuotation" xml:space="preserve">
    <value>Ja Customer Quotation</value>
  </data>
  <data name="CustomerQuotation1" xml:space="preserve">
    <value>Ja Quotation</value>
  </data>
  <data name="CustomerQuotationArchived" xml:space="preserve">
    <value>Ja Customer Quotation Archived</value>
  </data>
  <data name="CustomerQuotationDate" xml:space="preserve">
    <value>Ja Quotation Date</value>
  </data>
  <data name="CustomerQuotationNumber" xml:space="preserve">
    <value>Ja Quotation #</value>
  </data>
  <data name="CustomerQuotationSummary" xml:space="preserve">
    <value>Ja Customer Quotation Summary</value>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>Ja Customer Rating</value>
  </data>
  <data name="customersearch" xml:space="preserve">
    <value>Ja Customer Search</value>
  </data>
  <data name="CustomerServiceHistory" xml:space="preserve">
    <value>Ja Customer Service History</value>
  </data>
  <data name="customerspecific" xml:space="preserve">
    <value>Ja Customer Specific</value>
  </data>
  <data name="CustomerWithDetailsAlreadyExistsCombinationOfNameMobileandEmailShouldbeUnique" xml:space="preserve">
    <value>Ja Customer with these details already exists.\nCombination of Name, Mobile and Email should be unique</value>
  </data>
  <data name="customscode" xml:space="preserve">
    <value>Ja Customs Code</value>
  </data>
  <data name="CustomsDocReference" xml:space="preserve">
    <value>Ja Customs Doc. Reference</value>
  </data>
  <data name="DamageQuantity" xml:space="preserve">
    <value>Ja Damage Quantity</value>
  </data>
  <data name="Data" xml:space="preserve">
    <value>Ja </value>
  </data>
  <data name="DataSavedSuccessfully" xml:space="preserve">
    <value>Ja Data saved successfully</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Ja Date</value>
  </data>
  <data name="DateandTime" xml:space="preserve">
    <value>Ja Date and Time</value>
  </data>
  <data name="DateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>Ja Effective from date cannot be less than current date</value>
  </data>
  <data name="Datecannotbelessthenpreviousdate" xml:space="preserve">
    <value>Ja Date cannot be less then or equal to the date of previous records</value>
  </data>
  <data name="DateOfFailure" xml:space="preserve">
    <value>Ja Date Of Failure</value>
  </data>
  <data name="DateOfRepair" xml:space="preserve">
    <value>Ja Date Of Repair</value>
  </data>
  <data name="dateselectedmustbegreaterthenpreviouscustomer" xml:space="preserve">
    <value>Ja Date selected must be greater than previous customer</value>
  </data>
  <data name="DaysLeft" xml:space="preserve">
    <value>Ja Days Left</value>
  </data>
  <data name="DCType" xml:space="preserve">
    <value>Ja DC Type</value>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>Ja Dealer</value>
  </data>
  <data name="DealerName" xml:space="preserve">
    <value>Ja Dealer Name</value>
  </data>
  <data name="DealersorCompany" xml:space="preserve">
    <value>Ja Dealers/Company</value>
  </data>
  <data name="December" xml:space="preserve">
    <value>Ja Dec</value>
  </data>
  <data name="DefaultGridSize" xml:space="preserve">
    <value>Ja Default Grid Size</value>
  </data>
  <data name="Defaultgridsizealreadyavailable" xml:space="preserve">
    <value>Ja Default grid size already available</value>
  </data>
  <data name="DefaultgridSizeshouldbebetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>Ja Default grid size should be between 0 and 255</value>
  </data>
  <data name="DefectCode" xml:space="preserve">
    <value>Ja Defect Code</value>
  </data>
  <data name="DefectGroup" xml:space="preserve">
    <value>Ja Defect Group</value>
  </data>
  <data name="DefectivePartsDisposal" xml:space="preserve">
    <value>Ja Defective Parts Disposal</value>
  </data>
  <data name="DefectiveQuantity" xml:space="preserve">
    <value>Ja Defective Quantity</value>
  </data>
  <data name="DefectiveQuantityCannotbegreaterthanfailedstock" xml:space="preserve">
    <value>Ja Defective Quantity Cannot be greater than failed stock</value>
  </data>
  <data name="DefectName" xml:space="preserve">
    <value>Ja Defect Name</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Ja Delete</value>
  </data>
  <data name="DeleteAction" xml:space="preserve">
    <value>Ja Delete Action</value>
  </data>
  <data name="DeleteArticle" xml:space="preserve">
    <value>Ja Delete Article</value>
  </data>
  <data name="DeleteAttachment" xml:space="preserve">
    <value>Ja Delete attachment</value>
  </data>
  <data name="DeleteBranch" xml:space="preserve">
    <value>Ja Delete Branch</value>
  </data>
  <data name="DeleteBranchTaxDetails" xml:space="preserve">
    <value>Ja Delete Branch Tax Details</value>
  </data>
  <data name="DeleteBrands" xml:space="preserve">
    <value>Ja Delete Brands</value>
  </data>
  <data name="DeleteCompany" xml:space="preserve">
    <value>Ja Delete Company</value>
  </data>
  <data name="DeleteCompanyRelation" xml:space="preserve">
    <value>Ja Delete Company Relation</value>
  </data>
  <data name="DeleteCompanyTerms" xml:space="preserve">
    <value>Ja Delete Company Terms</value>
  </data>
  <data name="deletedsuccessfully" xml:space="preserve">
    <value>Ja Deleted Successfully</value>
  </data>
  <data name="DeleteEmployee" xml:space="preserve">
    <value>Ja Delete Employee</value>
  </data>
  <data name="deletefreestock" xml:space="preserve">
    <value>Ja Delete Part Free Stock</value>
  </data>
  <data name="DeleteFunctionGroup" xml:space="preserve">
    <value>Ja Delete Function Group</value>
  </data>
  <data name="DeleteJobCard" xml:space="preserve">
    <value>Ja Delete Job Card</value>
  </data>
  <data name="DeleteMaster" xml:space="preserve">
    <value>Ja Delete Master</value>
  </data>
  <data name="deletemodel" xml:space="preserve">
    <value>Ja Delete Model</value>
  </data>
  <data name="deleteoperation" xml:space="preserve">
    <value>Ja Delete Operation</value>
  </data>
  <data name="DeleteOperationEmployeeDetails" xml:space="preserve">
    <value>Ja Delete Operation Employee Details</value>
  </data>
  <data name="deletepart" xml:space="preserve">
    <value>Ja Delete Part</value>
  </data>
  <data name="deletepartprice" xml:space="preserve">
    <value>Ja Delete Part Price</value>
  </data>
  <data name="deletepartproducttype" xml:space="preserve">
    <value>Ja Delete Part product Type</value>
  </data>
  <data name="DeleteParts" xml:space="preserve">
    <value>Ja Delete Parts</value>
  </data>
  <data name="DeleteParty" xml:space="preserve">
    <value>Ja Delete Party</value>
  </data>
  <data name="DeletePrefixSuffix" xml:space="preserve">
    <value>Ja Delete Prefix Suffix</value>
  </data>
  <data name="deleteproduct" xml:space="preserve">
    <value>Ja Delete Product</value>
  </data>
  <data name="deleteproductdetail" xml:space="preserve">
    <value>Ja Delete Product Detail</value>
  </data>
  <data name="deleteproducttype" xml:space="preserve">
    <value>Ja Delete Product Type</value>
  </data>
  <data name="DeleteQuotation" xml:space="preserve">
    <value>Ja Delete Quotation</value>
  </data>
  <data name="DeleteRequest" xml:space="preserve">
    <value>Ja Delete Request</value>
  </data>
  <data name="DeleteRole" xml:space="preserve">
    <value>Ja Delete Role</value>
  </data>
  <data name="deleteServiceCharge" xml:space="preserve">
    <value>Ja Delete Service Charge</value>
  </data>
  <data name="deleteServiceChargeDetails" xml:space="preserve">
    <value>Ja Delete Service Charge Details</value>
  </data>
  <data name="DeleteServiceType" xml:space="preserve">
    <value>Ja Delete Service Type</value>
  </data>
  <data name="DeleteSkills" xml:space="preserve">
    <value>Ja Delete Skills</value>
  </data>
  <data name="DeleteSpecialization" xml:space="preserve">
    <value>Ja Delete Specialization</value>
  </data>
  <data name="DeleteStep" xml:space="preserve">
    <value>Ja Delete Step</value>
  </data>
  <data name="DeleteStepLink" xml:space="preserve">
    <value>Ja Delete Step Link</value>
  </data>
  <data name="DeleteSundry" xml:space="preserve">
    <value>Ja Delete Sundry</value>
  </data>
  <data name="DeleteTaxCode" xml:space="preserve">
    <value>Ja Delete Tax Code</value>
  </data>
  <data name="DeleteTaxDetails" xml:space="preserve">
    <value>Ja Delete Tax Details</value>
  </data>
  <data name="deletetaxstructure" xml:space="preserve">
    <value>Ja Delete Tax Structure</value>
  </data>
  <data name="deletetaxtstructuredetails" xml:space="preserve">
    <value>Ja Delete Tax Structure Details</value>
  </data>
  <data name="DeleteUser" xml:space="preserve">
    <value>Ja Delete User</value>
  </data>
  <data name="deletewarrantydetails" xml:space="preserve">
    <value>Ja Delete Warranty Details</value>
  </data>
  <data name="DeleteWorkDetails" xml:space="preserve">
    <value>Ja Delete Work Details</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Ja Delivery Date</value>
  </data>
  <data name="NoteNumber" xml:space="preserve">
    <value>Ja Note Number</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Ja Department</value>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>Ja Dependency found cannot delete the records</value>
  </data>
  <data name="description" xml:space="preserve">
    <value>Ja Description</value>
  </data>
  <data name="Designation" xml:space="preserve">
    <value>Ja Designation</value>
  </data>
  <data name="DestinationColumns" xml:space="preserve">
    <value>Ja Destination Columns</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Ja Detail</value>
  </data>
  <data name="DeviationHours" xml:space="preserve">
    <value>Ja Deviation Hours</value>
  </data>
  <data name="DeviationPercentage" xml:space="preserve">
    <value>Ja Deviation%</value>
  </data>
  <data name="DeviationQuantity" xml:space="preserve">
    <value>Ja Deviation Quantity</value>
  </data>
  <data name="DeviationStock" xml:space="preserve">
    <value>Ja Deviation Stock</value>
  </data>
  <data name="dimension" xml:space="preserve">
    <value>Ja Dimension</value>
  </data>
  <data name="Direct" xml:space="preserve">
    <value>Ja Direct</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Ja Discount</value>
  </data>
  <data name="Discount1" xml:space="preserve">
    <value>Ja Discount%</value>
  </data>
  <data name="Discount11" xml:space="preserve">
    <value>Ja Discount</value>
  </data>
  <data name="Discountamount" xml:space="preserve">
    <value>Ja Discount Amount</value>
  </data>
  <data name="DiscountedAmount" xml:space="preserve">
    <value>Ja Discounted Amount</value>
  </data>
  <data name="DiscountPer" xml:space="preserve">
    <value>Ja Discount Percentage</value>
  </data>
  <data name="DiscountPercentage" xml:space="preserve">
    <value>Ja Discount Percentage</value>
  </data>
  <data name="DiscountShouldbeLessThan" xml:space="preserve">
    <value>Ja Discount should be less than</value>
  </data>
  <data name="DiscoutAmount" xml:space="preserve">
    <value>Ja Discout Amount</value>
  </data>
  <data name="DisposalNumber" xml:space="preserve">
    <value>Ja Disposal Number</value>
  </data>
  <data name="DivideByZeroException" xml:space="preserve">
    <value>Ja Application error occured</value>
  </data>
  <data name="DNINumber" xml:space="preserve">
    <value>Ja Delivery Note/Invoice Number</value>
  </data>
  <data name="DocumentExport" xml:space="preserve">
    <value>Ja Document Export</value>
  </data>
  <data name="DonotEnterSpace" xml:space="preserve">
    <value>Ja Do not enter space</value>
  </data>
  <data name="DoyouwanttoaddthisSerialNumber" xml:space="preserve">
    <value>Ja Do you want to add this serial number</value>
  </data>
  <data name="Doyouwanttochangetheassociation" xml:space="preserve">
    <value>Ja Do you want to change the association?</value>
  </data>
  <data name="Doyouwanttocreatenewversion" xml:space="preserve">
    <value>Ja Do you want to create New Version?</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Ja Due Date</value>
  </data>
  <data name="DueDatecannotbeBlank" xml:space="preserve">
    <value>Ja Due date cannot be blank</value>
  </data>
  <data name="DueRange" xml:space="preserve">
    <value>Ja Due Range</value>
  </data>
  <data name="Duplicate" xml:space="preserve">
    <value>Ja Duplicate</value>
  </data>
  <data name="DuplicateArticle" xml:space="preserve">
    <value>Ja Duplicate Article</value>
  </data>
  <data name="DuplicateAttachment" xml:space="preserve">
    <value>Ja Duplicate Attachment</value>
  </data>
  <data name="duplicatebinlocation" xml:space="preserve">
    <value>Ja Duplicate Bin Location</value>
  </data>
  <data name="duplicatebranch" xml:space="preserve">
    <value>Ja Duplicate Branch</value>
  </data>
  <data name="DuplicateBranchName" xml:space="preserve">
    <value>Ja Branch name is already present</value>
  </data>
  <data name="DuplicateBrand" xml:space="preserve">
    <value>Ja Duplicate Brand</value>
  </data>
  <data name="DuplicateCode" xml:space="preserve">
    <value>Ja Duplicate Code</value>
  </data>
  <data name="DuplicateCode1" xml:space="preserve">
    <value>Ja Duplicate Description</value>
  </data>
  <data name="DuplicateCodeandDescription" xml:space="preserve">
    <value>Ja Duplicate Code and Description</value>
  </data>
  <data name="DuplicateCodeandDescription1" xml:space="preserve">
    <value>Ja Duplicate Code and Description</value>
  </data>
  <data name="DuplicateCompanyName" xml:space="preserve">
    <value>Ja Duplicate Company Name</value>
  </data>
  <data name="duplicateconsigneeaddress" xml:space="preserve">
    <value>Ja Duplicate Consignee Address</value>
  </data>
  <data name="duplicateconsigneelocation" xml:space="preserve">
    <value>Ja Duplicate Consignee Location</value>
  </data>
  <data name="DuplicateCustomer" xml:space="preserve">
    <value>Ja Duplicate Customer</value>
  </data>
  <data name="duplicatedate" xml:space="preserve">
    <value>Ja Duplicate Date</value>
  </data>
  <data name="duplicateDefectGroupCode" xml:space="preserve">
    <value>Ja Duplicate Defect Group Code</value>
  </data>
  <data name="duplicateDefectGroupcodeandName" xml:space="preserve">
    <value>Ja Duplicate Defect Group code and name</value>
  </data>
  <data name="duplicateDefectGroupName" xml:space="preserve">
    <value>Ja Duplicate Defect Group Name</value>
  </data>
  <data name="Duplicatedefectname" xml:space="preserve">
    <value>Ja Duplicate Defect Name</value>
  </data>
  <data name="DuplicateDefectNameCodeAndDescription" xml:space="preserve">
    <value>Ja Duplicate Defect Name Code And Description</value>
  </data>
  <data name="DuplicateDefectName_Code" xml:space="preserve">
    <value>Ja Duplicate Defect Name Code</value>
  </data>
  <data name="DuplicateDefectName_Description" xml:space="preserve">
    <value>Ja Duplicate Defect Name Description</value>
  </data>
  <data name="DuplicateDescription" xml:space="preserve">
    <value>Ja Duplicate description</value>
  </data>
  <data name="DuplicateEmailsFound" xml:space="preserve">
    <value>Ja Duplicate emails found</value>
  </data>
  <data name="DuplicateEmployee" xml:space="preserve">
    <value>Ja Duplicate Employee</value>
  </data>
  <data name="Duplicateentries" xml:space="preserve">
    <value>Ja Duplicate Entries</value>
  </data>
  <data name="Duplicateentriesof" xml:space="preserve">
    <value>Ja Duplicate</value>
  </data>
  <data name="Duplicateentriesof1" xml:space="preserve">
    <value>Ja Duplicate entries of</value>
  </data>
  <data name="DuplicateFunctionGroup" xml:space="preserve">
    <value>Ja Duplicate Function Group</value>
  </data>
  <data name="duplicateholidayname" xml:space="preserve">
    <value>Ja Duplicate Holiday Name</value>
  </data>
  <data name="DuplicateIssueSubAreaCodeAndDescription" xml:space="preserve">
    <value>Ja Duplicate IssueSubArea-Code And Name</value>
  </data>
  <data name="DuplicateIssueSubAreaDescription" xml:space="preserve">
    <value>Ja Duplicate Issue Sub-Area Name</value>
  </data>
  <data name="DuplicateIssueSubAreaShortName" xml:space="preserve">
    <value>Ja Duplicate Issue Sub-Area Code</value>
  </data>
  <data name="duplicateLinkName" xml:space="preserve">
    <value>Ja Duplicate link name</value>
  </data>
  <data name="duplicateLinkURL" xml:space="preserve">
    <value>Ja Duplicate link URL</value>
  </data>
  <data name="DuplicateLoginId" xml:space="preserve">
    <value>Ja Duplicate Login Id</value>
  </data>
  <data name="DuplicateMaster" xml:space="preserve">
    <value>Ja Duplicate Master</value>
  </data>
  <data name="duplicatemodel" xml:space="preserve">
    <value>Ja Duplicate model</value>
  </data>
  <data name="DuplicateModule" xml:space="preserve">
    <value>Ja Duplicate Module</value>
  </data>
  <data name="duplicatemovementype" xml:space="preserve">
    <value>Ja Duplicate Movement Type</value>
  </data>
  <data name="DuplicateOperationCode" xml:space="preserve">
    <value>Ja Duplicate Operation Code</value>
  </data>
  <data name="Duplicatepartnumber" xml:space="preserve">
    <value>Ja Duplicate part number</value>
  </data>
  <data name="DuplicatePartsCategory" xml:space="preserve">
    <value>Ja Duplicate Parts Category</value>
  </data>
  <data name="DuplicatePhoneNumbersFound" xml:space="preserve">
    <value>Ja Duplicate phone numbers found</value>
  </data>
  <data name="duplicateproducttype" xml:space="preserve">
    <value>Ja Duplicate product type</value>
  </data>
  <data name="DuplicateQuestion" xml:space="preserve">
    <value>Ja Duplicate Question</value>
  </data>
  <data name="DuplicateRole" xml:space="preserve">
    <value>Ja Duplicate Role</value>
  </data>
  <data name="duplicatesecondarysegment" xml:space="preserve">
    <value>Ja Duplicate secondary segment</value>
  </data>
  <data name="Duplicateservicecode" xml:space="preserve">
    <value>Ja Duplicate Service code</value>
  </data>
  <data name="duplicateservicedate" xml:space="preserve">
    <value>Ja Duplicate service date</value>
  </data>
  <data name="duplicateservicetype" xml:space="preserve">
    <value>Ja Duplicate service type</value>
  </data>
  <data name="DuplicateServiceTypeisnotallowed" xml:space="preserve">
    <value>Ja Duplicate service type is not allowed</value>
  </data>
  <data name="DuplicateSibling" xml:space="preserve">
    <value>Ja Duplicate Sibling</value>
  </data>
  <data name="duplicatestate" xml:space="preserve">
    <value>Ja Duplicate state</value>
  </data>
  <data name="duplicatestatename" xml:space="preserve">
    <value>Ja Duplicate state name</value>
  </data>
  <data name="DuplicateSubfolder" xml:space="preserve">
    <value>Ja Duplicate Subfolder</value>
  </data>
  <data name="DuplicateSundryDescriptionisnotAllowed" xml:space="preserve">
    <value>Ja Duplicate sundry job description</value>
  </data>
  <data name="DuplicateSupplier" xml:space="preserve">
    <value>Ja Duplicate Supplier</value>
  </data>
  <data name="DuplicateTaxname" xml:space="preserve">
    <value>Ja Duplicate tax name</value>
  </data>
  <data name="DuplicateTaxType" xml:space="preserve">
    <value>Ja Duplicate tax type</value>
  </data>
  <data name="DuplicateUniqueidentifier" xml:space="preserve">
    <value>Ja Duplicate Unique identifier</value>
  </data>
  <data name="edit" xml:space="preserve">
    <value>Ja Edit</value>
  </data>
  <data name="EditAction" xml:space="preserve">
    <value>Ja Edit Action</value>
  </data>
  <data name="EditCompany" xml:space="preserve">
    <value>Ja Edit Company</value>
  </data>
  <data name="editcomponentdetails" xml:space="preserve">
    <value>Ja Edit Component Details</value>
  </data>
  <data name="EditEvents" xml:space="preserve">
    <value>Ja Edit Events</value>
  </data>
  <data name="EditJobCard" xml:space="preserve">
    <value>Ja Edit Job Card</value>
  </data>
  <data name="editmodel" xml:space="preserve">
    <value>Ja Edit Model</value>
  </data>
  <data name="editoperation" xml:space="preserve">
    <value>Ja Edit Operation</value>
  </data>
  <data name="EditOperationDetails" xml:space="preserve">
    <value>Ja Edit Operation Details</value>
  </data>
  <data name="EditPartsMaster" xml:space="preserve">
    <value>Ja Edit Parts Master</value>
  </data>
  <data name="editproduct" xml:space="preserve">
    <value>Ja Edit Product</value>
  </data>
  <data name="editproducttype" xml:space="preserve">
    <value>Ja Edit Product Type</value>
  </data>
  <data name="EditPurchaseInvoice" xml:space="preserve">
    <value>Ja Edit Purchase Invoice</value>
  </data>
  <data name="EditPurchaseOrder" xml:space="preserve">
    <value>Ja Edit Purchase Order</value>
  </data>
  <data name="EditPurchaseOrderCancellation" xml:space="preserve">
    <value>Ja Edit Purchase Order Cancellation</value>
  </data>
  <data name="editsecondarysegment" xml:space="preserve">
    <value>Ja Edit Secondary Segment</value>
  </data>
  <data name="editsevicecharges" xml:space="preserve">
    <value>Ja Edit Service Charges</value>
  </data>
  <data name="editsiteaddress" xml:space="preserve">
    <value>Ja Edit Site Address</value>
  </data>
  <data name="editstate" xml:space="preserve">
    <value>Ja Edit State</value>
  </data>
  <data name="EditStockCheckRequest" xml:space="preserve">
    <value>Ja Edit Stock Check Request</value>
  </data>
  <data name="EditSupersession" xml:space="preserve">
    <value>Ja Edit Supersession</value>
  </data>
  <data name="edittaxstructure" xml:space="preserve">
    <value>Ja Edit Tax Structure</value>
  </data>
  <data name="editwarrantydeatils" xml:space="preserve">
    <value>Ja Edit Warranty Deatils</value>
  </data>
  <data name="effectivefrom" xml:space="preserve">
    <value>Ja Effective From</value>
  </data>
  <data name="EightHour" xml:space="preserve">
    <value>Ja Less Than 8 Hour</value>
  </data>
  <data name="EightHour1" xml:space="preserve">
    <value>Ja Less Than 8 Hours</value>
  </data>
  <data name="EightHour11" xml:space="preserve">
    <value>Ja &lt;8 Hour</value>
  </data>
  <data name="EightToSixteenHours" xml:space="preserve">
    <value>Ja 8 To 16 Hours</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Ja Email</value>
  </data>
  <data name="EmailToAddresse" xml:space="preserve">
    <value>Ja Email To Addresse</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>Ja Email To Customer</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Ja Employee</value>
  </data>
  <data name="EmployeeBranch" xml:space="preserve">
    <value>Ja Employee - Branch</value>
  </data>
  <data name="EmployeeBranch1" xml:space="preserve">
    <value>Ja Employee-Branch</value>
  </data>
  <data name="EmployeeBranch11" xml:space="preserve">
    <value>Ja Employee - Branch</value>
  </data>
  <data name="EmployeeDetails" xml:space="preserve">
    <value>Ja Employee Details</value>
  </data>
  <data name="EmployeeID" xml:space="preserve">
    <value>Ja Employee Code</value>
  </data>
  <data name="EmployeeID1" xml:space="preserve">
    <value>Ja Employee ID</value>
  </data>
  <data name="EmployeeIDisalreadyused" xml:space="preserve">
    <value>Ja Duplicate Employee code</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeBranch" xml:space="preserve">
    <value>Ja Employee is already associated with the branch</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeSpecialization" xml:space="preserve">
    <value>Ja Employee is already associated with the specialization</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Ja Employee</value>
  </data>
  <data name="EmployeeNameIsAlreadySelected" xml:space="preserve">
    <value>Ja Employee name is already selected</value>
  </data>
  <data name="EmployeeNotFound" xml:space="preserve">
    <value>Ja Employee not found</value>
  </data>
  <data name="EmployeeSearch" xml:space="preserve">
    <value>Ja Employee Search</value>
  </data>
  <data name="EmployeeSkills" xml:space="preserve">
    <value>Ja Skills</value>
  </data>
  <data name="EmployeeSpecialization" xml:space="preserve">
    <value>Ja Specialization</value>
  </data>
  <data name="EmployeeTools" xml:space="preserve">
    <value>Ja Employee/Tools</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>Ja End Date</value>
  </data>
  <data name="EndDatecannotbelessthanStartDate" xml:space="preserve">
    <value>Ja End date cannot be less than start date</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Ja English</value>
  </data>
  <data name="EnquiryDate" xml:space="preserve">
    <value>Ja Enquiry Date</value>
  </data>
  <data name="EnquiryNumber" xml:space="preserve">
    <value>Ja Enquiry Number</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>Ja Enter Code</value>
  </data>
  <data name="EnterDescription" xml:space="preserve">
    <value>Ja Enter Description</value>
  </data>
  <data name="EnteredNumberdoesnotbelongstocustomerorprospect" xml:space="preserve">
    <value>Ja Entered number doesnot belongs to customer or prospect</value>
  </data>
  <data name="enterfromdate" xml:space="preserve">
    <value>Ja Enter From Date</value>
  </data>
  <data name="EnterMasterName" xml:space="preserve">
    <value>Ja Enter master name</value>
  </data>
  <data name="enterNonTaxableothercharges" xml:space="preserve">
    <value>Ja Enter non taxable other charges</value>
  </data>
  <data name="enterNonTaxableothercharges1" xml:space="preserve">
    <value>Ja Enter non taxable other charges 1</value>
  </data>
  <data name="enterNonTaxableothercharges2" xml:space="preserve">
    <value>Ja Enter non taxable other charges 2</value>
  </data>
  <data name="enterTaxableothercharges" xml:space="preserve">
    <value>Ja Enter taxable other charges</value>
  </data>
  <data name="enterTaxableothercharges1" xml:space="preserve">
    <value>Ja Enter taxable other charges 1</value>
  </data>
  <data name="enterTaxableothercharges2" xml:space="preserve">
    <value>Ja Enter taxable other charges 2</value>
  </data>
  <data name="enteryear" xml:space="preserve">
    <value>Ja Enter Year</value>
  </data>
  <data name="EntryTax" xml:space="preserve">
    <value>Ja Entry Tax</value>
  </data>
  <data name="EntryTaxPercentage" xml:space="preserve">
    <value>Ja Entry Tax Percentage</value>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>Ja Equal</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Ja Error</value>
  </data>
  <data name="ErrorInChecking" xml:space="preserve">
    <value>Ja Error in checking..</value>
  </data>
  <data name="ErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>Ja Error in uploaded parts. Please open excel</value>
  </data>
  <data name="ErrorOccuredwhileLocking" xml:space="preserve">
    <value>Ja Error occured while locking</value>
  </data>
  <data name="ErrorSaving" xml:space="preserve">
    <value>Ja Error Saving</value>
  </data>
  <data name="EventName" xml:space="preserve">
    <value>Ja Event Name</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Ja Events</value>
  </data>
  <data name="EventStartDateCannotbeLessthanJobCardDate" xml:space="preserve">
    <value>Ja Event start date cannot be less than Job card date</value>
  </data>
  <data name="Eventstarttimecoincideswithothereventsdoyouwanttocontinue" xml:space="preserve">
    <value>Ja Event start time coincides with other events, do you want to continue?</value>
  </data>
  <data name="ExceededTime" xml:space="preserve">
    <value>Ja Exceeded Time</value>
  </data>
  <data name="Excel" xml:space="preserve">
    <value>Ja Excel</value>
  </data>
  <data name="ExcessQuantity" xml:space="preserve">
    <value>Ja Excess Quantity</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>Ja Exchange Rate</value>
  </data>
  <data name="ExpectedDeliveryDate" xml:space="preserve">
    <value>Ja Expected Delivery Date</value>
  </data>
  <data name="ExpensesDetail" xml:space="preserve">
    <value>Ja Expenses Detail</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Ja Export</value>
  </data>
  <data name="ExportAction" xml:space="preserve">
    <value>Ja Export Action</value>
  </data>
  <data name="ExporttoDocument" xml:space="preserve">
    <value>Ja Export to Document</value>
  </data>
  <data name="ExporttoExcel" xml:space="preserve">
    <value>Ja Export to Excel</value>
  </data>
  <data name="FailedPartsStock" xml:space="preserve">
    <value>Ja Failed Parts Stock</value>
  </data>
  <data name="FailedtosavenewContactPerson" xml:space="preserve">
    <value>Ja Failed to save new contact person</value>
  </data>
  <data name="FailedtosavenewParty" xml:space="preserve">
    <value>Ja Failed to save new party</value>
  </data>
  <data name="FailedtosavenewSerialNumber" xml:space="preserve">
    <value>Ja Failed to save new serial number</value>
  </data>
  <data name="FailedToSaveSerial" xml:space="preserve">
    <value>Ja Failed to save serial</value>
  </data>
  <data name="FailureOrRepairReadingCannotBeLessThanCurrentReading" xml:space="preserve">
    <value>Ja Failure or repair reading cannot be less than current reading</value>
  </data>
  <data name="FailureReading" xml:space="preserve">
    <value>Ja Failure Reading</value>
  </data>
  <data name="Fast" xml:space="preserve">
    <value>Ja Fast</value>
  </data>
  <data name="FastCannotbeSmallerthanorEqualtoMedium" xml:space="preserve">
    <value>Ja Fast Cannot be Smaller than or Equal to Medium</value>
  </data>
  <data name="FAX" xml:space="preserve">
    <value>Ja FAX</value>
  </data>
  <data name="February" xml:space="preserve">
    <value>Ja Feb</value>
  </data>
  <data name="Fieldshighlightedaremandatory" xml:space="preserve">
    <value>Ja Fields highlighted are mandatory</value>
  </data>
  <data name="FieldsmarkedwithStararemandatory" xml:space="preserve">
    <value>Ja Fields marked with * are mandatory</value>
  </data>
  <data name="FileAlreadyExistsDoYouWantToReplace" xml:space="preserve">
    <value>Ja File already exists, Do you want to replace?</value>
  </data>
  <data name="FileDescription" xml:space="preserve">
    <value>Ja File Description</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>Ja File Name</value>
  </data>
  <data name="FileSizeShouldNotExceed2MB" xml:space="preserve">
    <value>Ja File size should not exceed 2MB</value>
  </data>
  <data name="FileSizeShouldNotExceed5MB" xml:space="preserve">
    <value>Ja File size should not exceed 5 MB</value>
  </data>
  <data name="FileTypeNotSupportedSupportedFormatsAre" xml:space="preserve">
    <value>Ja File type not supported, supported formats are :</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Ja Filter</value>
  </data>
  <data name="FilterAllQueBranch" xml:space="preserve">
    <value>Ja Filter All Que Branch Specific ?</value>
  </data>
  <data name="FilterCriteria" xml:space="preserve">
    <value>Ja Filter Criteria</value>
  </data>
  <data name="FinalAmount" xml:space="preserve">
    <value>Ja Final Amount</value>
  </data>
  <data name="FinancialYear" xml:space="preserve">
    <value>Ja Financial Year</value>
  </data>
  <data name="financialyearalredyselected" xml:space="preserve">
    <value>Ja Financial year already selected</value>
  </data>
  <data name="FinancialyearcannotbeGreaterthannextrowsfinancialyear" xml:space="preserve">
    <value>Ja Financial year cannot be greater than next rows Financial year</value>
  </data>
  <data name="Financialyearcannotbelessthanpreviousrowsfinancialyear" xml:space="preserve">
    <value>Ja Financial year cannot be less than previous rows Financial year</value>
  </data>
  <data name="FindRootCause" xml:space="preserve">
    <value>Ja FindRootCause</value>
  </data>
  <data name="For" xml:space="preserve">
    <value>Ja For</value>
  </data>
  <data name="forgotpassword" xml:space="preserve">
    <value>Ja Forgot Password?</value>
  </data>
  <data name="formula" xml:space="preserve">
    <value>Ja Formula</value>
  </data>
  <data name="FormulaCostPrice" xml:space="preserve">
    <value>Ja Formula Cost Price</value>
  </data>
  <data name="formulasummary" xml:space="preserve">
    <value>Ja Formula Summary</value>
  </data>
  <data name="FourtyEightToNintyHours" xml:space="preserve">
    <value>Ja 48 To 90 Hours</value>
  </data>
  <data name="FreeHours" xml:space="preserve">
    <value>Ja Free Hours</value>
  </data>
  <data name="freestock" xml:space="preserve">
    <value>Ja Free Stock</value>
  </data>
  <data name="FreeStockisnotenoughforKitBreaking" xml:space="preserve">
    <value>Ja Free Stock is not enough for Kit Breaking</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Ja Freight</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Ja Friday</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>Ja From</value>
  </data>
  <data name="fromdate" xml:space="preserve">
    <value>Ja From Date</value>
  </data>
  <data name="FromDateandTodatecannotbeEmpty" xml:space="preserve">
    <value>Ja From date and To date cannot be empty</value>
  </data>
  <data name="FromDatecannotbegreaterthanToDate" xml:space="preserve">
    <value>Ja From date cannot be greater than To date</value>
  </data>
  <data name="FromDateCannotbegreaterThanToday" xml:space="preserve">
    <value>Ja From date cannot be greater than Current date</value>
  </data>
  <data name="fromdatecannotbegreaterthentodate" xml:space="preserve">
    <value>Ja From date cannot be greater than To date</value>
  </data>
  <data name="fromdatecannotbegreatorthantodate" xml:space="preserve">
    <value>Ja From date cannot be greater than To date</value>
  </data>
  <data name="Fromdatecannotbelessthanorequaltopreviousrowtodate" xml:space="preserve">
    <value>Ja From date cannot be less than or equal to previous rows To date</value>
  </data>
  <data name="FromDatecannotbelessthanToDate" xml:space="preserve">
    <value>Ja From date cannot be greater than To date</value>
  </data>
  <data name="FromDatecannotbelessthanToDate1" xml:space="preserve">
    <value>Ja From date cannot be less than To date</value>
  </data>
  <data name="fromdatecannotbelessthencurrentdate" xml:space="preserve">
    <value>Ja From date cannot be less than current date</value>
  </data>
  <data name="fromdatecannotbelessthencurrentdate1" xml:space="preserve">
    <value>Ja From date cannot be less then Current date</value>
  </data>
  <data name="fromdatemustbegreaterthanorequaltoissuedate" xml:space="preserve">
    <value>Ja From date must be greater than or equal to Issue date</value>
  </data>
  <data name="fromdatemustbelesserthentodate" xml:space="preserve">
    <value>Ja From date must be lesser than To date</value>
  </data>
  <data name="fromdatemustbelesserthentodate1" xml:space="preserve">
    <value>Ja From date must be lesser then To date</value>
  </data>
  <data name="FromStep" xml:space="preserve">
    <value>Ja From Step</value>
  </data>
  <data name="FromTimeAndToTimeCannotBeEqual" xml:space="preserve">
    <value>Ja From Time and To Time can not be equal</value>
  </data>
  <data name="FromTimecannotbegreaterthanToTime" xml:space="preserve">
    <value>Ja From Time cannot be greater than To Time</value>
  </data>
  <data name="FromTimeCanNotBeGreaterToTime" xml:space="preserve">
    <value>Ja From Time Can not be Greater than To time</value>
  </data>
  <data name="FunctionGroup" xml:space="preserve">
    <value>Ja Function Group</value>
  </data>
  <data name="FunctionGroupHeader" xml:space="preserve">
    <value>Ja Function Group Header</value>
  </data>
  <data name="FunctionGroupID" xml:space="preserve">
    <value>Ja Function Group ID</value>
  </data>
  <data name="functiongroupisnotactive" xml:space="preserve">
    <value>Ja Function Group is not active</value>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>Ja Function Group Name</value>
  </data>
  <data name="FunctionGroupNative" xml:space="preserve">
    <value>Ja Function Group Native</value>
  </data>
  <data name="FunctionGroupOperations" xml:space="preserve">
    <value>Ja Function Group Operations</value>
  </data>
  <data name="FunctionGroupSearch" xml:space="preserve">
    <value>Ja Function Group Search</value>
  </data>
  <data name="GatePass" xml:space="preserve">
    <value>Ja Gate Pass</value>
  </data>
  <data name="GatePassAlreadyCreated" xml:space="preserve">
    <value>Ja Gate Pass already Created</value>
  </data>
  <data name="GatePassDate" xml:space="preserve">
    <value>Ja Gate Pass Date</value>
  </data>
  <data name="GatePassNumber" xml:space="preserve">
    <value>Ja Gate Pass #</value>
  </data>
  <data name="GDRNumber" xml:space="preserve">
    <value>Ja GDR Number</value>
  </data>
  <data name="GDRSettlement" xml:space="preserve">
    <value>Ja GDR Settlement</value>
  </data>
  <data name="GDRSettlementNumber" xml:space="preserve">
    <value>Ja GDR Settlement Number</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>Ja General</value>
  </data>
  <data name="GeneralShift" xml:space="preserve">
    <value>Ja General Shift</value>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>Ja Generate Report</value>
  </data>
  <data name="GoodsinTransit" xml:space="preserve">
    <value>Ja Goods in Transit</value>
  </data>
  <data name="GraphCategorySelection" xml:space="preserve">
    <value>Ja Graph Category Selection</value>
  </data>
  <data name="GraphType" xml:space="preserve">
    <value>Ja Graph Type</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>Ja Greater Than</value>
  </data>
  <data name="GRNDate" xml:space="preserve">
    <value>Ja GRN Date</value>
  </data>
  <data name="GRNNumber" xml:space="preserve">
    <value>Ja GRN #</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Ja Group</value>
  </data>
  <data name="GroupQue" xml:space="preserve">
    <value>Ja Group Queue</value>
  </data>
  <data name="GroupQueue" xml:space="preserve">
    <value>Ja Group Queue</value>
  </data>
  <data name="Header" xml:space="preserve">
    <value>Ja Header</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Ja Heading</value>
  </data>
  <data name="high" xml:space="preserve">
    <value>Ja High</value>
  </data>
  <data name="HighlightedFieldsareMandatory" xml:space="preserve">
    <value>Ja Highlighted fields are mandatory</value>
  </data>
  <data name="HMR" xml:space="preserve">
    <value>Ja HMR</value>
  </data>
  <data name="Hold" xml:space="preserve">
    <value>Ja Hold</value>
  </data>
  <data name="Holidays" xml:space="preserve">
    <value>Ja Holidays</value>
  </data>
  <data name="HourlyRate" xml:space="preserve">
    <value>Ja Hourly Rate</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>Ja ID</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Ja Import</value>
  </data>
  <data name="ImportAction" xml:space="preserve">
    <value>Ja Import Action</value>
  </data>
  <data name="ImportedSuccessfully" xml:space="preserve">
    <value>Ja Imported successfully</value>
  </data>
  <data name="ImportIntoDatabase" xml:space="preserve">
    <value>Ja Import Into Database</value>
  </data>
  <data name="ImportParts" xml:space="preserve">
    <value>Ja Import Parts</value>
  </data>
  <data name="inactiveproduct" xml:space="preserve">
    <value>Ja In Active Product</value>
  </data>
  <data name="IncompleteOperationsarepresent" xml:space="preserve">
    <value>Ja Incomplete operations are present, do you want to close the Job card?</value>
  </data>
  <data name="IncorrectPassword" xml:space="preserve">
    <value>Ja Incorrect Password</value>
  </data>
  <data name="IncorrectPassword1" xml:space="preserve">
    <value>Ja Invalid Password</value>
  </data>
  <data name="IndexOutOfRangeException" xml:space="preserve">
    <value>Ja Application error occured</value>
  </data>
  <data name="InformationCollected" xml:space="preserve">
    <value>Ja Information Collected</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>Ja In Progress</value>
  </data>
  <data name="InProgressCount" xml:space="preserve">
    <value>Ja In Progress Count</value>
  </data>
  <data name="InsertedSuccessfully" xml:space="preserve">
    <value>Ja Inserted successfully</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Ja Insurance</value>
  </data>
  <data name="InsuranceJob" xml:space="preserve">
    <value>Ja Is Insurance ?</value>
  </data>
  <data name="InsuranceParty" xml:space="preserve">
    <value>Ja Insurance Party</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>Ja Internal</value>
  </data>
  <data name="Internal Invoice Return" xml:space="preserve">
    <value>Ja Internal Invoice Return</value>
  </data>
  <data name="InternalInvoice" xml:space="preserve">
    <value>Ja Internal Invoice</value>
  </data>
  <data name="InternalInvoice1" xml:space="preserve">
    <value>Ja Internal Invoice</value>
  </data>
  <data name="InternalInvoiceDate" xml:space="preserve">
    <value>Ja Internal Invoice Date</value>
  </data>
  <data name="InternalInvoiceNumber" xml:space="preserve">
    <value>Ja Internal Invoice #</value>
  </data>
  <data name="InternalInvoiceReturn" xml:space="preserve">
    <value>Ja Internal Invoice Return</value>
  </data>
  <data name="InternalInvoiceReturnDate" xml:space="preserve">
    <value>Ja Internal Invoice Return Date</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>Ja Invalid</value>
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value>Ja Invalid Amount</value>
  </data>
  <data name="InvalidBranchSelection" xml:space="preserve">
    <value>Ja Prefixsuffix is created as company specific. Cannot change to Branch.</value>
  </data>
  <data name="InvalidCastException" xml:space="preserve">
    <value>Ja Application error occured</value>
  </data>
  <data name="InvalidCompanySelection" xml:space="preserve">
    <value>Ja Prefixsuffix is created as Branch specific. Cannot change to Company. </value>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>Ja Invalid date</value>
  </data>
  <data name="InvalidDateFormat" xml:space="preserve">
    <value>Ja Invalid Date Format</value>
  </data>
  <data name="InvalidDecimal" xml:space="preserve">
    <value>Ja Invalid Decimal</value>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>Ja Invalid email</value>
  </data>
  <data name="InvalidFile" xml:space="preserve">
    <value>Ja Invalid file</value>
  </data>
  <data name="Invalidfunctiongroup" xml:space="preserve">
    <value>Ja Invalid Function Group</value>
  </data>
  <data name="InvalidJobCardNumber" xml:space="preserve">
    <value>Ja Invalid Job Card Number</value>
  </data>
  <data name="InvalidkitMakingNumber" xml:space="preserve">
    <value>Ja Invalid kit Making Number</value>
  </data>
  <data name="InvalidMobile" xml:space="preserve">
    <value>Ja Invalid Mobile</value>
  </data>
  <data name="InvalidMobile1" xml:space="preserve">
    <value>Ja Invalid Mobile Number</value>
  </data>
  <data name="InvalidModel" xml:space="preserve">
    <value>Ja Invalid model</value>
  </data>
  <data name="invalidmodelormodelisinactive" xml:space="preserve">
    <value>Ja Invalid model or model is inactive</value>
  </data>
  <data name="InvalidName" xml:space="preserve">
    <value>Ja Invalid party name</value>
  </data>
  <data name="InvalidOperationException" xml:space="preserve">
    <value>Ja Database error occured</value>
  </data>
  <data name="Invalidpartnumber" xml:space="preserve">
    <value>Ja Invalid part number</value>
  </data>
  <data name="invalidpartnumberorpartnumberisinactive" xml:space="preserve">
    <value>Ja Invalid part number or part number is inactive</value>
  </data>
  <data name="InvalidPartsOrderNumber" xml:space="preserve">
    <value>Ja Invalid Parts Order Number</value>
  </data>
  <data name="InvalidParty" xml:space="preserve">
    <value>Ja Invalid Party</value>
  </data>
  <data name="InvalidPhone" xml:space="preserve">
    <value>Ja Invalid Phone</value>
  </data>
  <data name="InvalidPhoneNo" xml:space="preserve">
    <value>Ja Invalid Mobile No</value>
  </data>
  <data name="InvalidProduct" xml:space="preserve">
    <value>Ja Invalid product</value>
  </data>
  <data name="InvalidProductUniqueNumber" xml:space="preserve">
    <value>Ja Invalid Product Unique Identifier</value>
  </data>
  <data name="InvalidProductUniqueNumber1" xml:space="preserve">
    <value>Ja Invalid Product Unique Identifier or Product is not active</value>
  </data>
  <data name="InvalidQuantity" xml:space="preserve">
    <value>Ja Invalid Quantity.</value>
  </data>
  <data name="InvalidQuotationNumber" xml:space="preserve">
    <value>Ja Invalid quotation number</value>
  </data>
  <data name="InvalidRate" xml:space="preserve">
    <value>Ja Invalid Rate.</value>
  </data>
  <data name="InvalidRegisteredMobileNumber" xml:space="preserve">
    <value>Ja Invalid registered mobile number</value>
  </data>
  <data name="invalidselection" xml:space="preserve">
    <value>Ja Invalid selection</value>
  </data>
  <data name="InvalidSerialNumber" xml:space="preserve">
    <value>Ja Invalid serial number</value>
  </data>
  <data name="InvalidServiceRequestNumber" xml:space="preserve">
    <value>Ja Invalid service request number</value>
  </data>
  <data name="InvalidSupplier" xml:space="preserve">
    <value>Ja Invalid Supplier</value>
  </data>
  <data name="InValidUniqueIdentificationNumber" xml:space="preserve">
    <value>Ja Invalid unique identification number</value>
  </data>
  <data name="invalidWarrantyClaimNumber" xml:space="preserve">
    <value>Ja Invalid Warranty Claim Number</value>
  </data>
  <data name="InvoiceAddress" xml:space="preserve">
    <value>Ja Invoice Address</value>
  </data>
  <data name="InvoiceAlreadyDone" xml:space="preserve">
    <value>Ja Invoice already done</value>
  </data>
  <data name="InvoiceAmount" xml:space="preserve">
    <value>Ja Invoice Amount</value>
  </data>
  <data name="InvoicedQuantity" xml:space="preserve">
    <value>Ja Invoiced Quantity</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Ja Invoice Number</value>
  </data>
  <data name="InvoiceQuantity" xml:space="preserve">
    <value>Ja Invoice Quantity</value>
  </data>
  <data name="InvoiceRate" xml:space="preserve">
    <value>Ja Invoice Rate</value>
  </data>
  <data name="InvoiceTotalAmount" xml:space="preserve">
    <value>Ja Invoice Total Amount</value>
  </data>
  <data name="Invoicetype" xml:space="preserve">
    <value>Ja Invoice Type</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>Ja Is Active?</value>
  </data>
  <data name="IsAdmin" xml:space="preserve">
    <value>Ja Is Admin</value>
  </data>
  <data name="IsAllPartsClosed" xml:space="preserve">
    <value>Ja Is All Parts Closed</value>
  </data>
  <data name="isbaseamountincluded" xml:space="preserve">
    <value>Ja Is Base Amount Included?</value>
  </data>
  <data name="IsCompanySpecific" xml:space="preserve">
    <value>Ja Is Company Specific</value>
  </data>
  <data name="iscomponent" xml:space="preserve">
    <value>Ja Is Component</value>
  </data>
  <data name="IsConsiderForDemand" xml:space="preserve">
    <value>Ja Is Consider For Demand?</value>
  </data>
  <data name="IsCSA" xml:space="preserve">
    <value>Ja Is CSA</value>
  </data>
  <data name="IsCustomer" xml:space="preserve">
    <value>Ja IsCustomer</value>
  </data>
  <data name="IsDealer" xml:space="preserve">
    <value>Ja Is Dealer?</value>
  </data>
  <data name="IsDefault" xml:space="preserve">
    <value>Ja Is Default ?</value>
  </data>
  <data name="IsDefaultBinLocationRequired" xml:space="preserve">
    <value>Ja Is Default Bin Location Required</value>
  </data>
  <data name="IsDefaultConsigneeRequired" xml:space="preserve">
    <value>Ja Is Default Consignee Required</value>
  </data>
  <data name="IsDefaultContact" xml:space="preserve">
    <value>Ja Is Default Contact?</value>
  </data>
  <data name="IsDefaultWarehouseRequired" xml:space="preserve">
    <value>Ja Is Default Warehouse Required</value>
  </data>
  <data name="IsExternal" xml:space="preserve">
    <value>Ja Is External</value>
  </data>
  <data name="IsFullReturn" xml:space="preserve">
    <value>Ja Is Full Return</value>
  </data>
  <data name="IsGeneralshift" xml:space="preserve">
    <value>Ja Is General Shift?</value>
  </data>
  <data name="isHazardous" xml:space="preserve">
    <value>Ja Is Hazardous</value>
  </data>
  <data name="IsHeadOffice" xml:space="preserve">
    <value>Ja Is Head Office</value>
  </data>
  <data name="IsExport" xml:space="preserve">
    <value>Ja Is Export?</value>
  </data>
  <data name="isinactive" xml:space="preserve">
    <value>Ja is Inactive</value>
  </data>
  <data name="IsKitPart" xml:space="preserve">
    <value>Ja Is Kit Part</value>
  </data>
  <data name="IsLocal" xml:space="preserve">
    <value>Ja Is Local?</value>
  </data>
  <data name="IsMandatory" xml:space="preserve">
    <value>Ja Is Mandatory  ?</value>
  </data>
  <data name="IsOEM" xml:space="preserve">
    <value>Ja Is OEM?</value>
  </data>
  <data name="IsOperationCompleted" xml:space="preserve">
    <value>Ja Is Operation Completed</value>
  </data>
  <data name="IsPartialReturn" xml:space="preserve">
    <value>Ja Is Partial Return</value>
  </data>
  <data name="IsPartofKit" xml:space="preserve">
    <value>Ja Is Part of Kit</value>
  </data>
  <data name="IsProductValidate" xml:space="preserve">
    <value>Ja Is Product Validate?</value>
  </data>
  <data name="IsReserved" xml:space="preserve">
    <value>Ja Is Reserved</value>
  </data>
  <data name="IsReturnableBasis" xml:space="preserve">
    <value>Ja Is Returnable Basis</value>
  </data>
  <data name="IsReWork" xml:space="preserve">
    <value>Ja Is Re-Work</value>
  </data>
  <data name="IsStockUnBlocked" xml:space="preserve">
    <value>Ja Is Stock UnBlocked</value>
  </data>
  <data name="IssueArea" xml:space="preserve">
    <value>Ja Issue Area</value>
  </data>
  <data name="issueddate" xml:space="preserve">
    <value>Ja Issued Date</value>
  </data>
  <data name="issueddatesholudbelessthanorequaltocurrentdate" xml:space="preserve">
    <value>Ja Issued date sholud be less than or equal to current date</value>
  </data>
  <data name="IssueDetails" xml:space="preserve">
    <value>Ja Issue Details</value>
  </data>
  <data name="IssuedQty" xml:space="preserve">
    <value>Ja Issued Quantity</value>
  </data>
  <data name="IssueSubArea" xml:space="preserve">
    <value>Ja Issue Sub-Area</value>
  </data>
  <data name="IssueType" xml:space="preserve">
    <value>Ja Issue Type</value>
  </data>
  <data name="IsUnderBreakDown" xml:space="preserve">
    <value>Ja Is Under Break Down?</value>
  </data>
  <data name="isunderwarranty" xml:space="preserve">
    <value>Ja Is Under Warranty?</value>
  </data>
  <data name="isversionallowed" xml:space="preserve">
    <value>Ja Is Version Allowed?</value>
  </data>
  <data name="IsVersionEnabled" xml:space="preserve">
    <value>Ja Is Version Enabled ?</value>
  </data>
  <data name="IsWarranty" xml:space="preserve">
    <value>Ja Is Warranty ?</value>
  </data>
  <data name="January" xml:space="preserve">
    <value>Ja Jan</value>
  </data>
  <data name="Jobamendedwillcreatenewversionwanttoproceed" xml:space="preserve">
    <value>Ja Job amended, will create new version want to proceed?</value>
  </data>
  <data name="JobCard" xml:space="preserve">
    <value>Ja Job Card</value>
  </data>
  <data name="JobCardAbandonReason" xml:space="preserve">
    <value>Ja Job Card Abandon Reason</value>
  </data>
  <data name="JobCardActivity" xml:space="preserve">
    <value>Ja Job Card Activity</value>
  </data>
  <data name="JobCardActivityDetail" xml:space="preserve">
    <value>Ja Job Card Activity Detail</value>
  </data>
  <data name="JobcardArchived" xml:space="preserve">
    <value>Ja Job Card Archived</value>
  </data>
  <data name="JobCardClosureDate" xml:space="preserve">
    <value>Ja Job Card Closure Date</value>
  </data>
  <data name="JobCardCushionHours" xml:space="preserve">
    <value>Ja Job Card Cushion Hours</value>
  </data>
  <data name="JobCardDate" xml:space="preserve">
    <value>Ja Job Card Date</value>
  </data>
  <data name="JobCardDelayReason" xml:space="preserve">
    <value>Ja Job Card Delay Reason</value>
  </data>
  <data name="JobCardFieldSearch" xml:space="preserve">
    <value>Ja Job Card Field Search</value>
  </data>
  <data name="JobCardHOApproval" xml:space="preserve">
    <value>Ja Job Card HO Approval</value>
  </data>
  <data name="JobCardisalreadycreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>Ja Job card is already created for this service request number</value>
  </data>
  <data name="JobCardNumber" xml:space="preserve">
    <value>Ja Job Card #</value>
  </data>
  <data name="JobCardNumberNotExists" xml:space="preserve">
    <value>Ja Invalid jobcard number is entered</value>
  </data>
  <data name="JobcardNumbernotfound" xml:space="preserve">
    <value>Ja Job card number not found</value>
  </data>
  <data name="JobCardNumberSearch" xml:space="preserve">
    <value>Ja Job Card Number Search</value>
  </data>
  <data name="JobCardOEMApproval" xml:space="preserve">
    <value>Ja Job Card OEM Approval</value>
  </data>
  <data name="JobCardOpenDate" xml:space="preserve">
    <value>Ja Job Card Open Date</value>
  </data>
  <data name="JobCardPartsReturn" xml:space="preserve">
    <value>Ja Job Card Parts Return</value>
  </data>
  <data name="JobCardPendingCount" xml:space="preserve">
    <value>Ja Job Card Pending Count</value>
  </data>
  <data name="JobCardSearch" xml:space="preserve">
    <value>Ja Job Card Search</value>
  </data>
  <data name="JobCardStatus" xml:space="preserve">
    <value>Ja Job Card Status</value>
  </data>
  <data name="JobCardSummary" xml:space="preserve">
    <value>Ja Job Card Summary</value>
  </data>
  <data name="JobCardVersion" xml:space="preserve">
    <value>Ja Version</value>
  </data>
  <data name="JobCardWIPCount" xml:space="preserve">
    <value>Ja Job Card WIP Count</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>Ja Job Description</value>
  </data>
  <data name="JobDetails" xml:space="preserve">
    <value>Ja Job Details</value>
  </data>
  <data name="JobEndDate" xml:space="preserve">
    <value>Ja Job End Date</value>
  </data>
  <data name="JobPriority" xml:space="preserve">
    <value>Ja Job Priority</value>
  </data>
  <data name="JobSiteAddress" xml:space="preserve">
    <value>Ja Job Site Address</value>
  </data>
  <data name="JobStartDate" xml:space="preserve">
    <value>Ja Job Start Date</value>
  </data>
  <data name="JoinedTables" xml:space="preserve">
    <value>Ja Joined Tables</value>
  </data>
  <data name="JoinWith" xml:space="preserve">
    <value>Ja  Join With</value>
  </data>
  <data name="July" xml:space="preserve">
    <value>Ja Jul</value>
  </data>
  <data name="June" xml:space="preserve">
    <value>Ja Jun</value>
  </data>
  <data name="KitBom" xml:space="preserve">
    <value>Ja Kit Bom</value>
  </data>
  <data name="KitBreaking" xml:space="preserve">
    <value>Ja Kit Breaking</value>
  </data>
  <data name="KitBreakingDate" xml:space="preserve">
    <value>Ja Kit Breaking Date</value>
  </data>
  <data name="KitBreakingNumber" xml:space="preserve">
    <value>Ja Kit Breaking #</value>
  </data>
  <data name="KitMaking" xml:space="preserve">
    <value>Ja Kit Making</value>
  </data>
  <data name="KitMakingDate" xml:space="preserve">
    <value>Ja Kit Making Date</value>
  </data>
  <data name="KitMakingNumber" xml:space="preserve">
    <value>Ja Kit Making #</value>
  </data>
  <data name="KitMakingNumer" xml:space="preserve">
    <value>Ja Kit Making #</value>
  </data>
  <data name="KitPartDescription" xml:space="preserve">
    <value>Ja Kit Part Description</value>
  </data>
  <data name="KitPartFreeStock" xml:space="preserve">
    <value>Ja Kit Part Free Stock</value>
  </data>
  <data name="KitPartNumber" xml:space="preserve">
    <value>Ja Kit Part #</value>
  </data>
  <data name="KnowledgeBase" xml:space="preserve">
    <value>Ja KnowledgeBase</value>
  </data>
  <data name="LandingCost" xml:space="preserve">
    <value>Ja Landing Cost</value>
  </data>
  <data name="LandingCostRate" xml:space="preserve">
    <value>Ja Landing Cost Rate</value>
  </data>
  <data name="LandingPage" xml:space="preserve">
    <value>Ja Landing Page</value>
  </data>
  <data name="Landline" xml:space="preserve">
    <value>Ja Landline</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Ja Language</value>
  </data>
  <data name="LanguageName" xml:space="preserve">
    <value>Ja Language</value>
  </data>
  <data name="LastInvoicedDate" xml:space="preserve">
    <value>Ja Last Invoiced Date</value>
  </data>
  <data name="LastStockDate" xml:space="preserve">
    <value>Ja Last Stock Date</value>
  </data>
  <data name="leadtime" xml:space="preserve">
    <value>Ja Lead Time</value>
  </data>
  <data name="LeadTimeInDays" xml:space="preserve">
    <value>Ja Lead Time in Days</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>Ja Less Than</value>
  </data>
  <data name="Like" xml:space="preserve">
    <value>Ja Like</value>
  </data>
  <data name="Link" xml:space="preserve">
    <value>Ja Link</value>
  </data>
  <data name="LinkIsInactive" xml:space="preserve">
    <value>Ja Link is inactive</value>
  </data>
  <data name="LinkName" xml:space="preserve">
    <value>Ja Link Name</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Ja Links</value>
  </data>
  <data name="LinkURL" xml:space="preserve">
    <value>Ja Link URL</value>
  </data>
  <data name="ListPrice" xml:space="preserve">
    <value>Ja List Price</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Ja Loading</value>
  </data>
  <data name="Local" xml:space="preserve">
    <value>Ja Local</value>
  </data>
  <data name="Locale" xml:space="preserve">
    <value>Ja Locale</value>
  </data>
  <data name="LocaleDetails" xml:space="preserve">
    <value>Ja Locale Details</value>
  </data>
  <data name="LocaledetailsarenotavaliableforBrand" xml:space="preserve">
    <value>Ja Locale details are not avaliable for brand</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductModel" xml:space="preserve">
    <value>Ja Locale details are not avaliable for product model</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductType" xml:space="preserve">
    <value>Ja Locale details are not avaliable for product type</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Ja Location</value>
  </data>
  <data name="Lock" xml:space="preserve">
    <value>Ja Lock</value>
  </data>
  <data name="locked" xml:space="preserve">
    <value>Ja Locked</value>
  </data>
  <data name="lockedby" xml:space="preserve">
    <value>Ja Locked By</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Ja Login</value>
  </data>
  <data name="LoginID" xml:space="preserve">
    <value>Ja Login ID</value>
  </data>
  <data name="LogoName" xml:space="preserve">
    <value>Ja Logo Name</value>
  </data>
  <data name="low" xml:space="preserve">
    <value>Ja Low</value>
  </data>
  <data name="MachineDetails" xml:space="preserve">
    <value>Ja Machine Details</value>
  </data>
  <data name="MachineStatus" xml:space="preserve">
    <value>Ja Machine Status</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Ja Manager</value>
  </data>
  <data name="MandatoryClaim" xml:space="preserve">
    <value>Ja Mandatory Claim</value>
  </data>
  <data name="MandatoryClaimOEMApproval" xml:space="preserve">
    <value>Ja Mandatory Claim OEM Approval</value>
  </data>
  <data name="MandatoryServiceDetails" xml:space="preserve">
    <value>Ja Mandatory Service Details</value>
  </data>
  <data name="mandatoryservices" xml:space="preserve">
    <value>Ja Mandatory Services</value>
  </data>
  <data name="manual" xml:space="preserve">
    <value>Ja Manual</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Ja Manufacturer</value>
  </data>
  <data name="MapColumns" xml:space="preserve">
    <value>Ja Map Columns</value>
  </data>
  <data name="MappedColumns" xml:space="preserve">
    <value>Ja Mapped Columns</value>
  </data>
  <data name="March" xml:space="preserve">
    <value>Ja Mar</value>
  </data>
  <data name="MasterExists" xml:space="preserve">
    <value>Ja Master already exists</value>
  </data>
  <data name="MasterID" xml:space="preserve">
    <value>Ja Master ID</value>
  </data>
  <data name="MasterName" xml:space="preserve">
    <value>Ja Master Name</value>
  </data>
  <data name="MaxSundryAmount" xml:space="preserve">
    <value>Ja Max Sundry Value</value>
  </data>
  <data name="MaxTravelExpense" xml:space="preserve">
    <value>Ja Max Travel Expense</value>
  </data>
  <data name="May" xml:space="preserve">
    <value>Ja May</value>
  </data>
  <data name="medium" xml:space="preserve">
    <value>Ja Medium</value>
  </data>
  <data name="Medium1" xml:space="preserve">
    <value>Ja Medium</value>
  </data>
  <data name="MediumCannotbegreaterthanorEqualtoFast" xml:space="preserve">
    <value>Ja Medium Cannot be greater than or Equal to Fast</value>
  </data>
  <data name="MediumCannotbeSmallerthanorEqualtoSlow" xml:space="preserve">
    <value>Ja Medium Cannot be Smaller than or Equal to Slow</value>
  </data>
  <data name="MenuDetail" xml:space="preserve">
    <value>Ja Menu Details</value>
  </data>
  <data name="MenuDetails" xml:space="preserve">
    <value>Ja Menu Details</value>
  </data>
  <data name="MenuName" xml:space="preserve">
    <value>Ja Menu Name</value>
  </data>
  <data name="MenuNamecannotbeblank" xml:space="preserve">
    <value>Ja Menu name can not be blank</value>
  </data>
  <data name="MenuPath" xml:space="preserve">
    <value>Ja Menu Path</value>
  </data>
  <data name="MinimumOrderQty" xml:space="preserve">
    <value>Ja Minimum Order Qty</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Ja Mobile</value>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>Ja Mobile Number</value>
  </data>
  <data name="Mode" xml:space="preserve">
    <value>Ja Mode</value>
  </data>
  <data name="model" xml:space="preserve">
    <value>Ja Model</value>
  </data>
  <data name="modelenglish" xml:space="preserve">
    <value>Ja Model English</value>
  </data>
  <data name="ModelFieldSearch" xml:space="preserve">
    <value>Ja Model Field Search</value>
  </data>
  <data name="modelisinactive" xml:space="preserve">
    <value>Ja Model is inactive</value>
  </data>
  <data name="modellocale" xml:space="preserve">
    <value>Ja Model Locale</value>
  </data>
  <data name="modelmaster" xml:space="preserve">
    <value>Ja Model Master</value>
  </data>
  <data name="modelname" xml:space="preserve">
    <value>Ja Model Name</value>
  </data>
  <data name="modelnamealreadyexists" xml:space="preserve">
    <value>Ja Model Name already Exists</value>
  </data>
  <data name="modelnotfound" xml:space="preserve">
    <value>Ja Model not found</value>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>Ja Model Search</value>
  </data>
  <data name="ModifiedBy" xml:space="preserve">
    <value>Ja Modified By</value>
  </data>
  <data name="ModifiedDate" xml:space="preserve">
    <value>Ja Modified Date</value>
  </data>
  <data name="Module" xml:space="preserve">
    <value>Ja Module</value>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>Ja Module Name</value>
  </data>
  <data name="ModuleNameCannotbeblank" xml:space="preserve">
    <value>Ja Module name cannot be blank</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Ja Monday</value>
  </data>
  <data name="month" xml:space="preserve">
    <value>Ja Month</value>
  </data>
  <data name="MovementType" xml:space="preserve">
    <value>Ja Movement Type</value>
  </data>
  <data name="MovementTypeDefinition" xml:space="preserve">
    <value>Ja Movement Type Definition</value>
  </data>
  <data name="movementtypedetails" xml:space="preserve">
    <value>Ja Movement Type Details</value>
  </data>
  <data name="MRPPrice" xml:space="preserve">
    <value>Ja MRP Price</value>
  </data>
  <data name="MyQue" xml:space="preserve">
    <value>Ja My Queue</value>
  </data>
  <data name="MyQueue" xml:space="preserve">
    <value>Ja My Queue</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Ja Name</value>
  </data>
  <data name="NameShouldNotBeBlank" xml:space="preserve">
    <value>Ja Name should not be blank</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Ja New Password</value>
  </data>
  <data name="newpasswordandConfirmpasswordarenotmatching" xml:space="preserve">
    <value>Ja New Password and Confirm Password are not matching</value>
  </data>
  <data name="NextServiceType" xml:space="preserve">
    <value>Ja Next Service Type</value>
  </data>
  <data name="NintyHour" xml:space="preserve">
    <value>Ja More Than 90 Hour</value>
  </data>
  <data name="NintyHour1" xml:space="preserve">
    <value>Ja More Than 90 Hours</value>
  </data>
  <data name="NintyHour11" xml:space="preserve">
    <value>Ja &gt;90 Hour</value>
  </data>
  <data name="no" xml:space="preserve">
    <value>Ja No</value>
  </data>
  <data name="NoChangesMade" xml:space="preserve">
    <value>Ja No changes made</value>
  </data>
  <data name="NochangesmadetoSave" xml:space="preserve">
    <value>Ja No changes made to save</value>
  </data>
  <data name="Noeditpermissionforproductmaster" xml:space="preserve">
    <value>Ja No Edit permission for Product Master</value>
  </data>
  <data name="NoJobCards" xml:space="preserve">
    <value>Ja No Job Cards</value>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd" xml:space="preserve">
    <value>Ja Party is InActive or No matching record found, Do you want to add this party</value>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd1" xml:space="preserve">
    <value>Ja No matching record found, Do you want to add this Party</value>
  </data>
  <data name="NonTaxable" xml:space="preserve">
    <value>Ja Non Taxable</value>
  </data>
  <data name="NonTaxableothercharges" xml:space="preserve">
    <value>Ja Non Taxable Other Charges</value>
  </data>
  <data name="NonTaxableothercharges1" xml:space="preserve">
    <value>Ja Non Taxable other charges 1</value>
  </data>
  <data name="NonTaxableothercharges1Amount" xml:space="preserve">
    <value>Ja Non Taxable other charges 1 Amount</value>
  </data>
  <data name="NonTaxableothercharges2" xml:space="preserve">
    <value>Ja Non Taxable other charges 2</value>
  </data>
  <data name="NonTaxableothercharges2Amount" xml:space="preserve">
    <value>Ja Non Taxable other charges 2 Amount</value>
  </data>
  <data name="NonTaxableotherchargesAmount" xml:space="preserve">
    <value>Ja Non Taxable other charges  Amount</value>
  </data>
  <data name="NoOfPicksPerYear" xml:space="preserve">
    <value>Ja No Of Picks Per Year</value>
  </data>
  <data name="NoOfSRCompleted" xml:space="preserve">
    <value>Ja Number Of Case Completed</value>
  </data>
  <data name="NoOfSRCompleted1" xml:space="preserve">
    <value>Ja Number Of Service Request Completed</value>
  </data>
  <data name="NoOfSRRecieved" xml:space="preserve">
    <value>Ja Number Of Case Recieved</value>
  </data>
  <data name="NoOfSRRecieved1" xml:space="preserve">
    <value>Ja Number Of Service Request Recieved</value>
  </data>
  <data name="Noproductisassociatedwithselectedcustomer" xml:space="preserve">
    <value>Ja No product is associated with selected customer</value>
  </data>
  <data name="NoRecords" xml:space="preserve">
    <value>Ja No Records</value>
  </data>
  <data name="Norecordstoview" xml:space="preserve">
    <value>Ja No records to view</value>
  </data>
  <data name="NotApplicable" xml:space="preserve">
    <value>Ja Not Applicable</value>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>Ja Not Equal</value>
  </data>
  <data name="NotEqual1" xml:space="preserve">
    <value>Ja NotEqual</value>
  </data>
  <data name="NotesDetails" xml:space="preserve">
    <value>Ja Notes Details</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Ja Not Found</value>
  </data>
  <data name="notspeciafied" xml:space="preserve">
    <value>Ja Not Specified</value>
  </data>
  <data name="November" xml:space="preserve">
    <value>Ja Nov</value>
  </data>
  <data name="NullPick" xml:space="preserve">
    <value>Ja Null Pick</value>
  </data>
  <data name="NullReferenceException" xml:space="preserve">
    <value>Ja Application error occured</value>
  </data>
  <data name="number" xml:space="preserve">
    <value>Ja Number</value>
  </data>
  <data name="NumberOfCasesBasedonIssueArea" xml:space="preserve">
    <value>Ja Number Of Cases Based on IssueArea</value>
  </data>
  <data name="NumberofDays" xml:space="preserve">
    <value>Ja Number of Days</value>
  </data>
  <data name="NumberofKitsMade" xml:space="preserve">
    <value>Ja Number of Kits Made</value>
  </data>
  <data name="NumberofKitsToBreak" xml:space="preserve">
    <value>Ja Number of Kits To Break</value>
  </data>
  <data name="NumberofKitstobreakcannotbezero" xml:space="preserve">
    <value>Ja Number of Kits to break cannot be zero</value>
  </data>
  <data name="NumberofKitstoMake" xml:space="preserve">
    <value>Ja Number of Kits to Make</value>
  </data>
  <data name="ObjectDescription" xml:space="preserve">
    <value>Ja Object Description</value>
  </data>
  <data name="ObjectDescriptioncannotbeblank" xml:space="preserve">
    <value>Ja Object description cannot be blank</value>
  </data>
  <data name="ObjectMaster" xml:space="preserve">
    <value>Ja Object</value>
  </data>
  <data name="ObjectMaster1" xml:space="preserve">
    <value>Ja Object Master</value>
  </data>
  <data name="ObjectName" xml:space="preserve">
    <value>Ja Object Name</value>
  </data>
  <data name="ObjectNamecannotbeblank" xml:space="preserve">
    <value>Ja Object name cannot be blank</value>
  </data>
  <data name="ObjectssavedSuccessfully" xml:space="preserve">
    <value>Ja Objects saved successfully</value>
  </data>
  <data name="October" xml:space="preserve">
    <value>Ja Oct</value>
  </data>
  <data name="of" xml:space="preserve">
    <value>Ja of</value>
  </data>
  <data name="OldBinLocation" xml:space="preserve">
    <value>Ja Old Bin Location</value>
  </data>
  <data name="OldBufferBinLocation" xml:space="preserve">
    <value>Ja Old Buffer Bin Location</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Ja Old Password</value>
  </data>
  <data name="OnDate" xml:space="preserve">
    <value>Ja On Date</value>
  </data>
  <data name="OnHoldCount" xml:space="preserve">
    <value>Ja OnHold Count</value>
  </data>
  <data name="onlyactivecustomerdeatilscanbeedited" xml:space="preserve">
    <value>Ja Only active customer deatils can be edited</value>
  </data>
  <data name="onlyactivewarrantycanbeedited" xml:space="preserve">
    <value>Ja Only active warranty can be edited</value>
  </data>
  <data name="open" xml:space="preserve">
    <value>Ja Open</value>
  </data>
  <data name="OpenReport" xml:space="preserve">
    <value>Ja Open Report</value>
  </data>
  <data name="operation" xml:space="preserve">
    <value>Ja Operation</value>
  </data>
  <data name="OperationandEmployeeisalreadyassociated" xml:space="preserve">
    <value>Ja Operation and employee is already associated</value>
  </data>
  <data name="OperationCode" xml:space="preserve">
    <value>Ja Operation Code</value>
  </data>
  <data name="Operationcodealreadyexists" xml:space="preserve">
    <value>Ja Operation Code already Exists</value>
  </data>
  <data name="OperationCodeAlreadySelected" xml:space="preserve">
    <value>Ja Operation code aready selected</value>
  </data>
  <data name="OperationCodeNotFound" xml:space="preserve">
    <value>Ja Operation code not found</value>
  </data>
  <data name="operationdes" xml:space="preserve">
    <value>Ja Operation Description</value>
  </data>
  <data name="OperationDescription" xml:space="preserve">
    <value>Ja Operation Description</value>
  </data>
  <data name="OperationDetail" xml:space="preserve">
    <value>Ja Operation Detail</value>
  </data>
  <data name="OperationDeviationReport" xml:space="preserve">
    <value>Ja Operation Deviation Report</value>
  </data>
  <data name="OperationEmployeeDetails" xml:space="preserve">
    <value>Ja Operation Employee Details</value>
  </data>
  <data name="OperationEndDate" xml:space="preserve">
    <value>Ja Operation End Date</value>
  </data>
  <data name="OperationEndDateCannotbelessthanoperationStartDate" xml:space="preserve">
    <value>Ja Operation end date cannot be less than operation start date</value>
  </data>
  <data name="operationenglish" xml:space="preserve">
    <value>Ja Operation English</value>
  </data>
  <data name="OperationFieldSearch" xml:space="preserve">
    <value>Ja Operation Field Search</value>
  </data>
  <data name="operationheader" xml:space="preserve">
    <value>Ja Operation Header</value>
  </data>
  <data name="OperationHours" xml:space="preserve">
    <value>Ja Operation Hours</value>
  </data>
  <data name="Operationisalreadyassociatedwithanotheremployee" xml:space="preserve">
    <value>Ja Operation is already associated with another employee</value>
  </data>
  <data name="operationlocale" xml:space="preserve">
    <value>Ja Operation Locale</value>
  </data>
  <data name="operationmaster" xml:space="preserve">
    <value>Ja Operation Master</value>
  </data>
  <data name="OperationStartDate" xml:space="preserve">
    <value>Ja Operation Start Date</value>
  </data>
  <data name="OperationStartdatecannotbelessthanJobcarddate" xml:space="preserve">
    <value>Ja Operation start date cannot be less than job card date</value>
  </data>
  <data name="operationtime" xml:space="preserve">
    <value>Ja Operation Time</value>
  </data>
  <data name="Operator" xml:space="preserve">
    <value>Ja Operator</value>
  </data>
  <data name="OR" xml:space="preserve">
    <value>Ja OR</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Ja Order</value>
  </data>
  <data name="OrderClass" xml:space="preserve">
    <value>Ja Order Class</value>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>Ja Order Date</value>
  </data>
  <data name="OrderedQuantity" xml:space="preserve">
    <value>Ja Ordered Quantity</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Ja Order Number</value>
  </data>
  <data name="OrderQuantity" xml:space="preserve">
    <value>Ja Order Quantity</value>
  </data>
  <data name="OrderStatus" xml:space="preserve">
    <value>Ja Order Status</value>
  </data>
  <data name="OrderType" xml:space="preserve">
    <value>Ja Order Type</value>
  </data>
  <data name="OtherCharge" xml:space="preserve">
    <value>Ja Other Charge</value>
  </data>
  <data name="OtherDetail" xml:space="preserve">
    <value>Ja Other Detail</value>
  </data>
  <data name="OtherDetails" xml:space="preserve">
    <value>Ja Other Details</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>Ja Others</value>
  </data>
  <data name="OutOfMemoryException" xml:space="preserve">
    <value>Ja Application error occured</value>
  </data>
  <data name="Owner" xml:space="preserve">
    <value>Ja Owner</value>
  </data>
  <data name="OwnerName" xml:space="preserve">
    <value>Ja Owner Name</value>
  </data>
  <data name="ParentCaseNumber" xml:space="preserve">
    <value>Ja Parent Case Number</value>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>Ja Parent Company</value>
  </data>
  <data name="parentcompanyoperationcannotbedeleted" xml:space="preserve">
    <value>Ja Parent company operation cannot be deleted</value>
  </data>
  <data name="parentcompanyoperationcannotbeedited" xml:space="preserve">
    <value>Ja Parent company operation cannot be edited</value>
  </data>
  <data name="ParentMenu" xml:space="preserve">
    <value>Ja Parent Menu</value>
  </data>
  <data name="PartAlreadyAdded" xml:space="preserve">
    <value>Ja Part Already Added.</value>
  </data>
  <data name="partcategory" xml:space="preserve">
    <value>Ja Part Category</value>
  </data>
  <data name="partdescription" xml:space="preserve">
    <value>Ja Part Description</value>
  </data>
  <data name="PartDetails" xml:space="preserve">
    <value>Ja Part Details</value>
  </data>
  <data name="partfunctiongroup" xml:space="preserve">
    <value>Ja Part Function Group</value>
  </data>
  <data name="partisnotactive" xml:space="preserve">
    <value>Ja Part is not active</value>
  </data>
  <data name="PartList" xml:space="preserve">
    <value>Ja Parts List</value>
  </data>
  <data name="PartListDetails" xml:space="preserve">
    <value>Ja PartList Details</value>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>Ja Partner</value>
  </data>
  <data name="PartnerName" xml:space="preserve">
    <value>Ja Partner</value>
  </data>
  <data name="partnumber" xml:space="preserve">
    <value>Ja Part #</value>
  </data>
  <data name="partnumberalreadyexists" xml:space="preserve">
    <value>Ja Part Number Already Exists</value>
  </data>
  <data name="PartNumberalreadyselected" xml:space="preserve">
    <value>Ja Part Number already selected</value>
  </data>
  <data name="PartnumberexitsinPartsDetailgrid" xml:space="preserve">
    <value>Ja Part number exits in Parts Detail grid</value>
  </data>
  <data name="PartnumberexitsinSuperseedingPartsDetailgrid" xml:space="preserve">
    <value>Ja Part number exits in Superseeding Parts Detail grid</value>
  </data>
  <data name="PartNumberisBlank" xml:space="preserve">
    <value>Ja Part Number is blank.</value>
  </data>
  <data name="PartnumberisSuperseded" xml:space="preserve">
    <value>Ja Part number is Superseded</value>
  </data>
  <data name="PartNumbernotassociatedtotheselectedproductdetails" xml:space="preserve">
    <value>Ja Part Number not associated to the selected product details.</value>
  </data>
  <data name="PartNumbernotfound" xml:space="preserve">
    <value>Ja Part number not found</value>
  </data>
  <data name="partnumberr" xml:space="preserve">
    <value>Ja Part #</value>
  </data>
  <data name="partnumbersearch" xml:space="preserve">
    <value>Ja Part Number Search</value>
  </data>
  <data name="Partprefix" xml:space="preserve">
    <value>Ja Part Prefix</value>
  </data>
  <data name="PartPrefixisBlank" xml:space="preserve">
    <value>Ja PartPrefix is Blank</value>
  </data>
  <data name="partprice" xml:space="preserve">
    <value>Ja Part Price</value>
  </data>
  <data name="partpricepdetails" xml:space="preserve">
    <value>Ja Part Price Details</value>
  </data>
  <data name="partproducttypedetails" xml:space="preserve">
    <value>Ja Parts Product Details</value>
  </data>
  <data name="partproducttypedetails1" xml:space="preserve">
    <value>Ja Parts Product Type Details</value>
  </data>
  <data name="partproducttypedetails11" xml:space="preserve">
    <value>Ja Part Product Type Details</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>Ja Parts</value>
  </data>
  <data name="PartsAlreadyReturnedforselectedjobcard" xml:space="preserve">
    <value>Ja Parts already returned for selected job card</value>
  </data>
  <data name="PartsCategory" xml:space="preserve">
    <value>Ja Parts Category</value>
  </data>
  <data name="PartsCategoryDefinition" xml:space="preserve">
    <value>Ja Parts Category Definition</value>
  </data>
  <data name="PartsCreditLimit" xml:space="preserve">
    <value>Ja Parts Credit Limit</value>
  </data>
  <data name="PartsDetail" xml:space="preserve">
    <value>Ja Parts Detail</value>
  </data>
  <data name="PartsDetailCannotbeblank" xml:space="preserve">
    <value>Ja Parts detail cannot be blank</value>
  </data>
  <data name="PartsDetails" xml:space="preserve">
    <value>Ja Parts Details</value>
  </data>
  <data name="PartsDetailsCannotbeEmpty" xml:space="preserve">
    <value>Ja Parts Details Cannot be Empty</value>
  </data>
  <data name="PartsDisposalisalreadydoneforselectedclaimnumber" xml:space="preserve">
    <value>Ja Parts Disposal is already done for selected claim #</value>
  </data>
  <data name="partsenglish" xml:space="preserve">
    <value>Ja Parts </value>
  </data>
  <data name="partsenglish1" xml:space="preserve">
    <value>Ja Parts English</value>
  </data>
  <data name="PartsFieldSearch" xml:space="preserve">
    <value>Ja Parts Field Search</value>
  </data>
  <data name="partsfreestockdetails" xml:space="preserve">
    <value>Ja Parts Free Stock details</value>
  </data>
  <data name="PartsList" xml:space="preserve">
    <value>Ja Parts List</value>
  </data>
  <data name="partslocale" xml:space="preserve">
    <value>Ja Parts </value>
  </data>
  <data name="partslocale1" xml:space="preserve">
    <value>Ja Parts Locale</value>
  </data>
  <data name="partsmaster" xml:space="preserve">
    <value>Ja Parts Master</value>
  </data>
  <data name="partsmasterlocale" xml:space="preserve">
    <value>Ja Parts Master </value>
  </data>
  <data name="partsmasterlocale1" xml:space="preserve">
    <value>Ja Parts Master Locale</value>
  </data>
  <data name="PartsOrder" xml:space="preserve">
    <value>Ja Parts Order</value>
  </data>
  <data name="PartsOrderAcceptance" xml:space="preserve">
    <value>Ja Parts Order Acceptance</value>
  </data>
  <data name="PartsOrderCancellation" xml:space="preserve">
    <value>Ja Parts Order Cancellation</value>
  </data>
  <data name="PartsOrderDate" xml:space="preserve">
    <value>Ja Parts Order Date</value>
  </data>
  <data name="PartsOrderNumber" xml:space="preserve">
    <value>Ja Parts Order Number</value>
  </data>
  <data name="PartsOrderSummary" xml:space="preserve">
    <value>Ja Parts Order Summary</value>
  </data>
  <data name="PartsOrderType" xml:space="preserve">
    <value>Ja Parts Order Type</value>
  </data>
  <data name="partspmasterheader" xml:space="preserve">
    <value>Ja Parts Master </value>
  </data>
  <data name="partspmasterheader1" xml:space="preserve">
    <value>Ja Parts Master Header</value>
  </data>
  <data name="partspricedetails" xml:space="preserve">
    <value>Ja Parts Price Details</value>
  </data>
  <data name="partsproducttypelocale" xml:space="preserve">
    <value>Ja Parts Product Type </value>
  </data>
  <data name="partsproducttypelocale1" xml:space="preserve">
    <value>Ja Parts Product Type Locale</value>
  </data>
  <data name="PartsStatus" xml:space="preserve">
    <value>Ja Parts Status</value>
  </data>
  <data name="partsstockdetails" xml:space="preserve">
    <value>Ja Parts Stock Details</value>
  </data>
  <data name="partsSupplierdetails" xml:space="preserve">
    <value>Ja Parts Supplier Details</value>
  </data>
  <data name="PartsTemplate" xml:space="preserve">
    <value>Ja Parts Template</value>
  </data>
  <data name="PartsTotalAmount" xml:space="preserve">
    <value>Ja Parts Total Amount</value>
  </data>
  <data name="Party" xml:space="preserve">
    <value>Ja Party</value>
  </data>
  <data name="PartyDetails" xml:space="preserve">
    <value>Ja Party Details</value>
  </data>
  <data name="PartyFielSearch" xml:space="preserve">
    <value>Ja Party Field Search</value>
  </data>
  <data name="PartyLocation" xml:space="preserve">
    <value>Ja Party Location</value>
  </data>
  <data name="PartyMobile" xml:space="preserve">
    <value>Ja Party Mobile</value>
  </data>
  <data name="PartyName" xml:space="preserve">
    <value>Ja Party Name</value>
  </data>
  <data name="partynamesearch" xml:space="preserve">
    <value>Ja Party Name Search</value>
  </data>
  <data name="PartyNotFound" xml:space="preserve">
    <value>Ja Party Not Found</value>
  </data>
  <data name="PartyOrderref" xml:space="preserve">
    <value>Ja Party Order Reference</value>
  </data>
  <data name="PartyPhone" xml:space="preserve">
    <value>Ja Party Phone</value>
  </data>
  <data name="PartySchedule" xml:space="preserve">
    <value>Ja Party Schedule</value>
  </data>
  <data name="PartySearch" xml:space="preserve">
    <value>Ja Party Search</value>
  </data>
  <data name="PartyType" xml:space="preserve">
    <value>Ja Party Type</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Ja Password</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch" xml:space="preserve">
    <value>Ja Confirm password does not match with password given</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch1" xml:space="preserve">
    <value>Ja Password and Confirm Password should match</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch11" xml:space="preserve">
    <value>Ja Confirm password does not match with password given</value>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>Ja Payment Terms</value>
  </data>
  <data name="PCD" xml:space="preserve">
    <value>Ja Promised Completion Date</value>
  </data>
  <data name="PCDShouldBeGreaterThanCallDate" xml:space="preserve">
    <value>Ja PCD should be greater than Call Date</value>
  </data>
  <data name="PDF" xml:space="preserve">
    <value>Ja PDF</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Ja Pending</value>
  </data>
  <data name="PendingforHOApproval" xml:space="preserve">
    <value>Ja Pending for HO Approval</value>
  </data>
  <data name="PendingforOEMApproval" xml:space="preserve">
    <value>Ja Pending for OEM Approval</value>
  </data>
  <data name="PendingPartsOrder" xml:space="preserve">
    <value>Ja Pending Parts Order</value>
  </data>
  <data name="PendingPurchaseOrder" xml:space="preserve">
    <value>Ja Pending Purchase Order</value>
  </data>
  <data name="PercentageDeviation" xml:space="preserve">
    <value>Ja Percentage Deviation</value>
  </data>
  <data name="PeriodCannotbeGreaterthan12Months" xml:space="preserve">
    <value>Ja Period Cannot be Greater than 12 Months</value>
  </data>
  <data name="PeriodInMonth" xml:space="preserve">
    <value>Ja Period In Month</value>
  </data>
  <data name="Personal" xml:space="preserve">
    <value>Ja Personal</value>
  </data>
  <data name="PersonalCalender" xml:space="preserve">
    <value>Ja Personal Calendar</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Ja Phone</value>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>Ja Phone</value>
  </data>
  <data name="PickedBy" xml:space="preserve">
    <value>Ja Picked By</value>
  </data>
  <data name="PickedQty" xml:space="preserve">
    <value>Ja Picked Qty</value>
  </data>
  <data name="PickedQuantity" xml:space="preserve">
    <value>Ja Picked Quantity</value>
  </data>
  <data name="PickListConfirmation" xml:space="preserve">
    <value>Ja Pick List Confirmation</value>
  </data>
  <data name="PickListConfirmationDate" xml:space="preserve">
    <value>Ja Pick List Confirmation Date</value>
  </data>
  <data name="PieChart" xml:space="preserve">
    <value>Ja Pie Chart</value>
  </data>
  <data name="PlannedCompletionDate" xml:space="preserve">
    <value>Ja Planned Completion Date</value>
  </data>
  <data name="PlannedCompletionDatecannotbelessthanStartDate" xml:space="preserve">
    <value>Ja Planned completion date cannot be less than start date</value>
  </data>
  <data name="PlannedStartDate" xml:space="preserve">
    <value>Ja Planned Start Date</value>
  </data>
  <data name="Pleaseselectthewarehouse" xml:space="preserve">
    <value>Ja Please select the warehouse</value>
  </data>
  <data name="Pleaseaddatleastoneoperationdetail" xml:space="preserve">
    <value>Ja Please add atleast one operation detail</value>
  </data>
  <data name="pleasecompleteenteringdetails" xml:space="preserve">
    <value>Ja Please complete entering details</value>
  </data>
  <data name="Pleasedefineworkingdays" xml:space="preserve">
    <value>Ja Please define working days</value>
  </data>
  <data name="PleaseEnterArticleContent" xml:space="preserve">
    <value>Ja Please enter article content</value>
  </data>
  <data name="PleaseEnterArticleName" xml:space="preserve">
    <value>Ja Please enter article name</value>
  </data>
  <data name="PleaseenteratleastonePartDetails" xml:space="preserve">
    <value>Ja Please enter atleast one Part Details</value>
  </data>
  <data name="PleaseEnterDateAs" xml:space="preserve">
    <value>Ja Please Enter Date As</value>
  </data>
  <data name="PleaseenterIntegerValue" xml:space="preserve">
    <value>Ja PleaseenterIntegerValue</value>
  </data>
  <data name="PleaseenterIntegerValue1" xml:space="preserve">
    <value>Ja Please enter integer value</value>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>Ja Please enter login ID</value>
  </data>
  <data name="PleaseEnterPartPrefix" xml:space="preserve">
    <value>Ja Please Enter Part Prefix</value>
  </data>
  <data name="PleaseenterPartyName" xml:space="preserve">
    <value>Ja Please enter party name</value>
  </data>
  <data name="PleaseEnterPassword" xml:space="preserve">
    <value>Ja Please enter password</value>
  </data>
  <data name="PleaseenterReportHeader" xml:space="preserve">
    <value>Ja Please enter report header</value>
  </data>
  <data name="PleaseenterReportName" xml:space="preserve">
    <value>Ja Please enter report name</value>
  </data>
  <data name="Pleaseenterserailnumber" xml:space="preserve">
    <value>Ja Please enter serial number</value>
  </data>
  <data name="PleaseEnterSiblingName" xml:space="preserve">
    <value>Ja Please enter sibling name</value>
  </data>
  <data name="PleaseEnterSubfolderName" xml:space="preserve">
    <value>Ja Please enter subfolder name</value>
  </data>
  <data name="Pleaseenteruserdetails" xml:space="preserve">
    <value>Ja Please enter user details</value>
  </data>
  <data name="pleaseentervalidmodel" xml:space="preserve">
    <value>Ja Please enter valid model</value>
  </data>
  <data name="Pleaseentervalue" xml:space="preserve">
    <value>Ja Please enter value</value>
  </data>
  <data name="Pleaseentervalue1" xml:space="preserve">
    <value>Ja Please enter value1</value>
  </data>
  <data name="PleaseprovideMenuName" xml:space="preserve">
    <value>Ja Please provide menu name</value>
  </data>
  <data name="PleaseprovideModuleName" xml:space="preserve">
    <value>Ja Please provide module name</value>
  </data>
  <data name="Pleaseprovidepassword" xml:space="preserve">
    <value>Ja Please provide password</value>
  </data>
  <data name="PleaseProvideTheProperBreakTime" xml:space="preserve">
    <value>Ja Please provide the proper break time</value>
  </data>
  <data name="PleaseProvideTheProperWorkingTime" xml:space="preserve">
    <value>Ja Please provide the proper working time</value>
  </data>
  <data name="pleasesavetheOperationdata" xml:space="preserve">
    <value>Ja Please save the operation details</value>
  </data>
  <data name="pleasesavethepartsdata" xml:space="preserve">
    <value>Ja Please save the parts details</value>
  </data>
  <data name="pleasesavetheServicedata" xml:space="preserve">
    <value>Ja Please save the service details</value>
  </data>
  <data name="pleasesavethesundrydata" xml:space="preserve">
    <value>Ja Please save the sundry details</value>
  </data>
  <data name="PleaseselectaColumn" xml:space="preserve">
    <value>Ja Please select a Column</value>
  </data>
  <data name="PleaseselectaCondition" xml:space="preserve">
    <value>Ja Please select a Condition</value>
  </data>
  <data name="Pleaseselectafiletoupload" xml:space="preserve">
    <value>Ja Please select a file to upload</value>
  </data>
  <data name="PleaseselectaOperator" xml:space="preserve">
    <value>Ja Please select a Operator</value>
  </data>
  <data name="PleaseSelectBranch" xml:space="preserve">
    <value>Ja Please select branch</value>
  </data>
  <data name="PleaseselectBrand" xml:space="preserve">
    <value>Ja Please select brand</value>
  </data>
  <data name="PleaseselectBrandandProductType" xml:space="preserve">
    <value>Ja Please Select Brand and ProductType</value>
  </data>
  <data name="Pleaseselectcompany" xml:space="preserve">
    <value>Ja Please select company</value>
  </data>
  <data name="Pleaseselectcondition" xml:space="preserve">
    <value>Ja Please select condition</value>
  </data>
  <data name="PleaseselectFile" xml:space="preserve">
    <value>Ja Please select file</value>
  </data>
  <data name="PleaseSelectGraphCategory" xml:space="preserve">
    <value>Ja Please Select Graph Category</value>
  </data>
  <data name="PleaseSelectGraphType" xml:space="preserve">
    <value>Ja Please Select Graph Type</value>
  </data>
  <data name="PleaseselectIssueArea" xml:space="preserve">
    <value>Ja Please select Issue Area</value>
  </data>
  <data name="pleaseselectModel" xml:space="preserve">
    <value>Ja Please select model</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber" xml:space="preserve">
    <value>Ja Pleases select model and serial number</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber1" xml:space="preserve">
    <value>Ja Pleases elect Model and Serial Number</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber11" xml:space="preserve">
    <value>Ja Pleases select model and serial number</value>
  </data>
  <data name="Pleaseselectoperator" xml:space="preserve">
    <value>Ja Please select operator</value>
  </data>
  <data name="Pleaseselectorderclass" xml:space="preserve">
    <value>Ja Please select order class</value>
  </data>
  <data name="PleaseSelectProduct" xml:space="preserve">
    <value>Ja Please select product</value>
  </data>
  <data name="PleaseselectQuestionnaireLevel1" xml:space="preserve">
    <value>Ja Please select Questionnaire Level1</value>
  </data>
  <data name="PleaseselectQuestionnaireLevel2" xml:space="preserve">
    <value>Ja Please select Questionnaire Level2</value>
  </data>
  <data name="Pleaseselectrecordstodelete" xml:space="preserve">
    <value>Ja Please select records to delete</value>
  </data>
  <data name="PleaseselectServicerequestnumber" xml:space="preserve">
    <value>Ja Please select service request number</value>
  </data>
  <data name="PleaseselecttheColumnName" xml:space="preserve">
    <value>Ja Please select the column name</value>
  </data>
  <data name="Pleaseselectthecolumnstomap" xml:space="preserve">
    <value>Ja Please select the columns to map</value>
  </data>
  <data name="Pleaseselecttheoperator" xml:space="preserve">
    <value>Ja Please select the operator</value>
  </data>
  <data name="Pleaseselectthesupplier" xml:space="preserve">
    <value>Ja Please select the supplier</value>
  </data>
  <data name="PleaseselecttheTableName" xml:space="preserve">
    <value>Ja Please select the table name</value>
  </data>
  <data name="PleaseselectUserstosave" xml:space="preserve">
    <value>Ja Please select users to save</value>
  </data>
  <data name="POCancellation" xml:space="preserve">
    <value>Ja Purchase Order Cancellation</value>
  </data>
  <data name="POCancellationNumber" xml:space="preserve">
    <value>Ja Cancellation Number</value>
  </data>
  <data name="PreffixSuffixDoesntExists" xml:space="preserve">
    <value>Ja Preffix suffix doesnt exists</value>
  </data>
  <data name="prefix" xml:space="preserve">
    <value>Ja Prefix</value>
  </data>
  <data name="prefixsuffix" xml:space="preserve">
    <value>Ja Prefix Suffix</value>
  </data>
  <data name="prefixsuffix1" xml:space="preserve">
    <value>Ja PrefixSuffix</value>
  </data>
  <data name="prefixsuffix11" xml:space="preserve">
    <value>Ja Prefix Suffix</value>
  </data>
  <data name="PreviousdateCannotbeempty" xml:space="preserve">
    <value>Ja Previous date cannot be empty</value>
  </data>
  <data name="PricecannotbeBlankorZero" xml:space="preserve">
    <value>Ja Price cannot be blank or zero</value>
  </data>
  <data name="PrimarySegment" xml:space="preserve">
    <value>Ja Primary Segment</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Ja Print</value>
  </data>
  <data name="PrintAction" xml:space="preserve">
    <value>Ja Print Action</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Ja Priority</value>
  </data>
  <data name="PriorityShouldBeBetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>Ja Priority should be between 0 and 255</value>
  </data>
  <data name="product" xml:space="preserve">
    <value>Ja Product</value>
  </data>
  <data name="ProductAssociation" xml:space="preserve">
    <value>Ja Product Association</value>
  </data>
  <data name="productdetail" xml:space="preserve">
    <value>Ja Product Detail</value>
  </data>
  <data name="productdetails" xml:space="preserve">
    <value>Ja Product Details</value>
  </data>
  <data name="productid" xml:space="preserve">
    <value>Ja Product ID</value>
  </data>
  <data name="Productisnotasscociatedwithanycustomer" xml:space="preserve">
    <value>Ja Product is currently not asscociated with any customer</value>
  </data>
  <data name="ProductLocation" xml:space="preserve">
    <value>Ja Product Location</value>
  </data>
  <data name="ProductReading" xml:space="preserve">
    <value>Ja Product Reading</value>
  </data>
  <data name="ProductSchedule" xml:space="preserve">
    <value>Ja Product Schedule</value>
  </data>
  <data name="ProductServiceHistory" xml:space="preserve">
    <value>Ja Product Service History</value>
  </data>
  <data name="Producttype" xml:space="preserve">
    <value>Ja Product Type</value>
  </data>
  <data name="producttypeenglish" xml:space="preserve">
    <value>Ja Product Type English</value>
  </data>
  <data name="producttypelocale" xml:space="preserve">
    <value>Ja Product Type Locale</value>
  </data>
  <data name="producttypemaster" xml:space="preserve">
    <value>Ja Product Type Master</value>
  </data>
  <data name="producttypename" xml:space="preserve">
    <value>Ja Product Type Name</value>
  </data>
  <data name="producttypenamealreadyexists" xml:space="preserve">
    <value>Ja Product Type Name already Exists</value>
  </data>
  <data name="ProductUniqueNo" xml:space="preserve">
    <value>Ja Unique Identifier</value>
  </data>
  <data name="ProfitValue" xml:space="preserve">
    <value>Ja Profit Value</value>
  </data>
  <data name="Prospect" xml:space="preserve">
    <value>Ja Prospect</value>
  </data>
  <data name="PurchaseGRN" xml:space="preserve">
    <value>Ja Purchase GRN</value>
  </data>
  <data name="PurchaseInvoice" xml:space="preserve">
    <value>Ja Purchase Invoice</value>
  </data>
  <data name="PurchaseInvoiceBinningList" xml:space="preserve">
    <value>Ja Purchase Invoice  Binning List</value>
  </data>
  <data name="PurchaseInvoiceNumber" xml:space="preserve">
    <value>Ja Purchase Invoice Number</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Ja Purchase Order</value>
  </data>
  <data name="PurchaseOrderDate" xml:space="preserve">
    <value>Ja Purchase Order Date</value>
  </data>
  <data name="PurchaseOrderNumber" xml:space="preserve">
    <value>Ja Purchase Order Number</value>
  </data>
  <data name="PurchaseOrderSummary" xml:space="preserve">
    <value>Ja Purchase Order Summary</value>
  </data>
  <data name="PurchaseType" xml:space="preserve">
    <value>Ja Purchase Type</value>
  </data>
  <data name="QtyofBOM" xml:space="preserve">
    <value>Ja Qty of BOM</value>
  </data>
  <data name="QtyRequired" xml:space="preserve">
    <value>Ja Qty Required</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Ja Quantity</value>
  </data>
  <data name="Quantitycannotbeblank" xml:space="preserve">
    <value>Ja Quantity cannot be blank</value>
  </data>
  <data name="QuantityCannotbeZero" xml:space="preserve">
    <value>Ja Quantity Cannot be zero</value>
  </data>
  <data name="QuantityDetails" xml:space="preserve">
    <value>Ja Quantity Details</value>
  </data>
  <data name="QuantityIssued" xml:space="preserve">
    <value>Ja Quantity Issued</value>
  </data>
  <data name="QuantityReleased" xml:space="preserve">
    <value>Ja Quantity Released</value>
  </data>
  <data name="QuantityRequested" xml:space="preserve">
    <value>Ja Quantity Requested</value>
  </data>
  <data name="QuantityRequired" xml:space="preserve">
    <value>Ja Quantity Required</value>
  </data>
  <data name="QuantityUsed" xml:space="preserve">
    <value>Ja Quantity Used</value>
  </data>
  <data name="QuestInformaticsPrivateLimited" xml:space="preserve">
    <value>Ja Quest Informatics Private Limited</value>
  </data>
  <data name="Questionaries" xml:space="preserve">
    <value>Ja Questionnaires</value>
  </data>
  <data name="QuestionLevel1" xml:space="preserve">
    <value>Ja Question Level1</value>
  </data>
  <data name="QuestionLevel2" xml:space="preserve">
    <value>Ja Question Level2</value>
  </data>
  <data name="QuestionLevel3" xml:space="preserve">
    <value>Ja Question Level3</value>
  </data>
  <data name="QuestionnaireLevel1" xml:space="preserve">
    <value>Ja Questionnaire Level1</value>
  </data>
  <data name="QuestionnaireLevel2" xml:space="preserve">
    <value>Ja Questionnaire Level2</value>
  </data>
  <data name="QuestionnaireLevel3" xml:space="preserve">
    <value>Ja Questionnaire Level3</value>
  </data>
  <data name="Queue" xml:space="preserve">
    <value>Ja Queue</value>
  </data>
  <data name="Quicklinks" xml:space="preserve">
    <value>Ja Quick Links</value>
  </data>
  <data name="Quotation" xml:space="preserve">
    <value>Ja Quotation</value>
  </data>
  <data name="QuotationAmount" xml:space="preserve">
    <value>Ja Quotation Amount</value>
  </data>
  <data name="QuotationDetail" xml:space="preserve">
    <value>Ja Quotation Detail</value>
  </data>
  <data name="QuotationNumber" xml:space="preserve">
    <value>Ja Quotation #</value>
  </data>
  <data name="QuotationPriority" xml:space="preserve">
    <value>Ja Quotation Priority</value>
  </data>
  <data name="RangecannotbeBlank" xml:space="preserve">
    <value>Ja Range cannot be blank</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Ja Rate</value>
  </data>
  <data name="RateCannotbeZero" xml:space="preserve">
    <value>Ja Rate cannot be zero</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Ja Rating</value>
  </data>
  <data name="Ratingshouldbebetween1and10" xml:space="preserve">
    <value>Ja Rating should be between 1 and 10</value>
  </data>
  <data name="RatingshouldBetween1and10" xml:space="preserve">
    <value>Ja Rating should between 1 and 10</value>
  </data>
  <data name="Read" xml:space="preserve">
    <value>Ja Read</value>
  </data>
  <data name="ReadAction" xml:space="preserve">
    <value>Ja Read Action</value>
  </data>
  <data name="reading" xml:space="preserve">
    <value>Ja Reading</value>
  </data>
  <data name="ReadingDetails" xml:space="preserve">
    <value>Ja Reading Details</value>
  </data>
  <data name="readinglimit" xml:space="preserve">
    <value>Ja Reading Limit</value>
  </data>
  <data name="readinglog" xml:space="preserve">
    <value>Ja Reading Log</value>
  </data>
  <data name="realizationreport" xml:space="preserve">
    <value>Ja Realization Report</value>
  </data>
  <data name="ReAllocatedQuantityAndAcceptedquantityisnotmatching" xml:space="preserve">
    <value>Ja Re Allocated Quantity and Accepted Quantity is not matching</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanAcceptedquantity" xml:space="preserve">
    <value>Ja ReAllocated Quantity cannot be greater than Accepted Quantity</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanAllocatedQty" xml:space="preserve">
    <value>Ja Re Allocated Quantity cannot be greater than Allocated Quantity</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanfreestock" xml:space="preserve">
    <value>Ja Re Allocated Quantity cannot be greater than Free Stock</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Ja Reason</value>
  </data>
  <data name="reasonforinactive" xml:space="preserve">
    <value>Ja Reason for Inactive</value>
  </data>
  <data name="ReceivedCount" xml:space="preserve">
    <value>Ja Received Count</value>
  </data>
  <data name="ReceivedQuantity" xml:space="preserve">
    <value>Ja Received Quantity</value>
  </data>
  <data name="RecentActivityLinks" xml:space="preserve">
    <value>Ja Recent Activity Links</value>
  </data>
  <data name="RecievedTime" xml:space="preserve">
    <value>Ja Received Time</value>
  </data>
  <data name="RecievedTime1" xml:space="preserve">
    <value>Ja Recieved Time</value>
  </data>
  <data name="RecieviedCount" xml:space="preserve">
    <value>Ja Received Count</value>
  </data>
  <data name="RecieviedCount1" xml:space="preserve">
    <value>Ja Recievied Count</value>
  </data>
  <data name="ReClaim" xml:space="preserve">
    <value>Ja Re-Claim</value>
  </data>
  <data name="Recordsavedsuccessfully" xml:space="preserve">
    <value>Ja Record saved successfully</value>
  </data>
  <data name="ReferecneDateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>Ja Referecne Date Cannot be less then CurrentDate</value>
  </data>
  <data name="ReferenceDate" xml:space="preserve">
    <value>Ja Reference Date</value>
  </data>
  <data name="ReferenceDetail" xml:space="preserve">
    <value>Ja Reference Detail</value>
  </data>
  <data name="ReferenceMasters" xml:space="preserve">
    <value>Ja Reference Masters</value>
  </data>
  <data name="ReferenceNumber" xml:space="preserve">
    <value>Ja Reference #</value>
  </data>
  <data name="ReferenceTables" xml:space="preserve">
    <value>Ja Reference Tables</value>
  </data>
  <data name="refresh" xml:space="preserve">
    <value>Ja Refresh</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Ja Region</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Ja Register</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>Ja Registered</value>
  </data>
  <data name="RegisteredMobile" xml:space="preserve">
    <value>Ja Registered Mobile</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>Ja Rejected</value>
  </data>
  <data name="Rejected1" xml:space="preserve">
    <value>Ja Rejected</value>
  </data>
  <data name="RelatedIssue" xml:space="preserve">
    <value>Ja Related Issue</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>Ja Re-Login</value>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>Ja Remarks</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>Ja Remove Filter</value>
  </data>
  <data name="Rename" xml:space="preserve">
    <value>Ja Rename</value>
  </data>
  <data name="reopen" xml:space="preserve">
    <value>Ja Re-open</value>
  </data>
  <data name="ReOrder" xml:space="preserve">
    <value>Ja Re-Order</value>
  </data>
  <data name="ReOrderLevel" xml:space="preserve">
    <value>Ja Re-Order Level</value>
  </data>
  <data name="ReOrderLevelQty" xml:space="preserve">
    <value>Ja Re-Order Level Qty</value>
  </data>
  <data name="RepairReading" xml:space="preserve">
    <value>Ja Repair Reading</value>
  </data>
  <data name="ReplacingCode" xml:space="preserve">
    <value>Ja Replacing Code</value>
  </data>
  <data name="ReportWizard" xml:space="preserve">
    <value>Ja Report Wizard</value>
  </data>
  <data name="ReqDate" xml:space="preserve">
    <value>Ja Req Date</value>
  </data>
  <data name="ReqNumber" xml:space="preserve">
    <value>Ja Req #</value>
  </data>
  <data name="RequestDescription" xml:space="preserve">
    <value>Ja Request Description</value>
  </data>
  <data name="RequestedQuantity" xml:space="preserve">
    <value>Ja Requested Quantity</value>
  </data>
  <data name="RequestForOEMApproval" xml:space="preserve">
    <value>Ja Request For OEM Approval</value>
  </data>
  <data name="RequestForParts" xml:space="preserve">
    <value>Ja Request For Parts</value>
  </data>
  <data name="RequestForPartsSuccessfull" xml:space="preserve">
    <value>Ja Request for parts is successfull</value>
  </data>
  <data name="RequestNumber" xml:space="preserve">
    <value>Ja Request #</value>
  </data>
  <data name="RequiredQuantityisGreaterthenFreeStock" xml:space="preserve">
    <value>Ja Required Quantity is Greater then Free Stock</value>
  </data>
  <data name="ReservedQty" xml:space="preserve">
    <value>Ja Reserved Qty</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Ja Reset</value>
  </data>
  <data name="resolutiontime" xml:space="preserve">
    <value>Ja Resolution Time</value>
  </data>
  <data name="ResolutionTimeOfClosedCases" xml:space="preserve">
    <value>Ja Resolution Time of Closed Cases</value>
  </data>
  <data name="Resolutiontimewithtimeslots" xml:space="preserve">
    <value>Ja Resolution Time With Time Slots</value>
  </data>
  <data name="ResourceDetail" xml:space="preserve">
    <value>Ja Resource Detail</value>
  </data>
  <data name="ResourceType" xml:space="preserve">
    <value>Ja Resource Type</value>
  </data>
  <data name="ResourceUtilizationReport" xml:space="preserve">
    <value>Ja Resource Utilization Report</value>
  </data>
  <data name="responsetime" xml:space="preserve">
    <value>Ja Response Time</value>
  </data>
  <data name="returnbypartysearch" xml:space="preserve">
    <value>Ja Return By Party Search</value>
  </data>
  <data name="PromisedReturnDate" xml:space="preserve">
    <value>Ja Promised Return Date</value>
    <comment>Message</comment>
  </data>
  <data name="ReturnedBy" xml:space="preserve">
    <value>Ja Return By</value>
  </data>
  <data name="ReturnedByParty" xml:space="preserve">
    <value>Ja Returned By Party</value>
  </data>
  <data name="ReturnedDate" xml:space="preserve">
    <value>Ja Returned Date</value>
  </data>
  <data name="ReturnedQuantity" xml:space="preserve">
    <value>Ja Returned Quantity</value>
  </data>
  <data name="ReturnNumber" xml:space="preserve">
    <value>Ja Return #</value>
  </data>
  <data name="ReturnParts" xml:space="preserve">
    <value>Ja Return Parts</value>
  </data>
  <data name="ReturnQuantity" xml:space="preserve">
    <value>Ja Return Quantity</value>
  </data>
  <data name="ReturnQuantityCannotBeGreaterThanQuantity" xml:space="preserve">
    <value>Ja Return quantity cannot be greater than quantity</value>
  </data>
  <data name="RevenuecannotbeBlankorzero" xml:space="preserve">
    <value>Ja Revenue cannot be blank</value>
  </data>
  <data name="RevenueGenerated" xml:space="preserve">
    <value>Ja Revenue Generated</value>
  </data>
  <data name="revenuemorethen" xml:space="preserve">
    <value>Ja Revenue More Then</value>
  </data>
  <data name="RoleDefinition" xml:space="preserve">
    <value>Ja Role Definition</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Ja Role Name</value>
  </data>
  <data name="RoleNameCannotbeblank" xml:space="preserve">
    <value>Ja Role name cannot be blank</value>
  </data>
  <data name="RoleObject" xml:space="preserve">
    <value>Ja Role Object</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Ja Roles</value>
  </data>
  <data name="RootCause" xml:space="preserve">
    <value>Ja Root Cause</value>
  </data>
  <data name="RootCauseAnalysis" xml:space="preserve">
    <value>Ja Root Cause Analysis</value>
  </data>
  <data name="RoundOff" xml:space="preserve">
    <value>Ja Round Off</value>
  </data>
  <data name="SalesCreditLimit" xml:space="preserve">
    <value>Ja Sales Credit Limit</value>
  </data>
  <data name="SalesInvoice" xml:space="preserve">
    <value>Ja Sales Invoice</value>
  </data>
  <data name="SalesInvoiceDate" xml:space="preserve">
    <value>Ja Sales Invoice Date</value>
  </data>
  <data name="SalesOrderNumber" xml:space="preserve">
    <value>Ja Sales Order #</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Ja Saturday</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Ja Save</value>
  </data>
  <data name="SaveAction" xml:space="preserve">
    <value>Ja Save Action</value>
  </data>
  <data name="SavedSuccessfully" xml:space="preserve">
    <value>Ja Saved Successfully</value>
  </data>
  <data name="SaveFormatandGenerateReport" xml:space="preserve">
    <value>Ja Save Format and Generate Report</value>
  </data>
  <data name="savefreestockdetails" xml:space="preserve">
    <value>Ja Save Parts Free Stock Details</value>
  </data>
  <data name="saveheader" xml:space="preserve">
    <value>Ja Save Header</value>
  </data>
  <data name="savepartprice" xml:space="preserve">
    <value>Ja Save Part Price Details</value>
  </data>
  <data name="SavePrefixSuffix" xml:space="preserve">
    <value>Ja Save Prefix Suffix</value>
  </data>
  <data name="saveproductdetail" xml:space="preserve">
    <value>Ja Save Product Detail</value>
  </data>
  <data name="saveproducttype" xml:space="preserve">
    <value>Ja Save Part Product Type</value>
  </data>
  <data name="SaveRole" xml:space="preserve">
    <value>Ja Save Role</value>
  </data>
  <data name="SaveStep" xml:space="preserve">
    <value>Ja Save Step</value>
  </data>
  <data name="SaveStepLink" xml:space="preserve">
    <value>Ja Save Step Link</value>
  </data>
  <data name="SaveSuccesfull" xml:space="preserve">
    <value>Ja Save Succesfull</value>
  </data>
  <data name="SaveSuccessfull" xml:space="preserve">
    <value>Ja Saved Successfully</value>
  </data>
  <data name="savetaxstructure" xml:space="preserve">
    <value>Ja Save Tax Structure</value>
  </data>
  <data name="SaveUser" xml:space="preserve">
    <value>Ja Save User</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Ja Schedule Type</value>
  </data>
  <data name="ScrapDate" xml:space="preserve">
    <value>Ja Date</value>
  </data>
  <data name="ScrapDetails" xml:space="preserve">
    <value>Ja Scrap Details</value>
  </data>
  <data name="ScrapNumber" xml:space="preserve">
    <value>Ja Scrap #</value>
  </data>
  <data name="ScrapQuantity" xml:space="preserve">
    <value>Ja Scrap Quantity</value>
  </data>
  <data name="SecondarySegment" xml:space="preserve">
    <value>Ja Secondary Segment</value>
  </data>
  <data name="SecondarySegmentdescription" xml:space="preserve">
    <value>Ja Secondary Segment Description</value>
  </data>
  <data name="secondarysegmentenglish" xml:space="preserve">
    <value>Ja Secondary Segment English</value>
  </data>
  <data name="secondarysegmentlocale" xml:space="preserve">
    <value>Ja Secondary Segment Locale</value>
  </data>
  <data name="SegmentDetail" xml:space="preserve">
    <value>Ja Segment Detail</value>
  </data>
  <data name="select" xml:space="preserve">
    <value>Ja Select</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Ja Select All</value>
  </data>
  <data name="SelectatleastoneWorkingDay" xml:space="preserve">
    <value>Ja Select Atleast One Working Day</value>
  </data>
  <data name="selectbrand" xml:space="preserve">
    <value>Ja Select Brand</value>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>Ja Select Column</value>
  </data>
  <data name="selectcompany" xml:space="preserve">
    <value>Ja Select Company</value>
  </data>
  <data name="SelectCondition" xml:space="preserve">
    <value>Ja Select Condition</value>
  </data>
  <data name="SelectDDl" xml:space="preserve">
    <value>Ja ---------Select---------</value>
  </data>
  <data name="SelectDDl1" xml:space="preserve">
    <value>Ja ----Select----</value>
  </data>
  <data name="SelectedFileisnotanExcelFile" xml:space="preserve">
    <value>Ja Selected file is not an excel file</value>
  </data>
  <data name="SelectedYearOfThisHolidayIsNot" xml:space="preserve">
    <value>Ja Selected year of this holiday is not</value>
  </data>
  <data name="SelectGeneralShift" xml:space="preserve">
    <value>Ja Select General Shift</value>
  </data>
  <data name="SelectionCriteria" xml:space="preserve">
    <value>Ja Selection Criteria</value>
  </data>
  <data name="SelectIssueArea" xml:space="preserve">
    <value>Ja Select IssueArea</value>
  </data>
  <data name="SelectkitPartNumber" xml:space="preserve">
    <value>Ja Select Kit Part #</value>
  </data>
  <data name="SelectModel" xml:space="preserve">
    <value>Ja Select Model</value>
  </data>
  <data name="SelectModelandRequest" xml:space="preserve">
    <value>Ja Select Model and Request</value>
  </data>
  <data name="SelectOperator" xml:space="preserve">
    <value>Ja Select Operator</value>
  </data>
  <data name="SelectPartyType" xml:space="preserve">
    <value>Ja Select Party Type</value>
  </data>
  <data name="selectproducttype" xml:space="preserve">
    <value>Ja Select Product Type</value>
  </data>
  <data name="SelectRecordstoDelete" xml:space="preserve">
    <value>Ja Select Records to Delete</value>
  </data>
  <data name="SelectReportFromPreviouslyStoredFormats" xml:space="preserve">
    <value>Ja Select Report From Previously Stored Formats</value>
  </data>
  <data name="SelectReqNumber" xml:space="preserve">
    <value>Ja Select Request Number</value>
  </data>
  <data name="SelectServiceRequest" xml:space="preserve">
    <value>Ja Select Service Request</value>
  </data>
  <data name="selectshift" xml:space="preserve">
    <value>Ja Select Shift</value>
  </data>
  <data name="SelectTableName" xml:space="preserve">
    <value>Ja Select Table Name</value>
  </data>
  <data name="SelectWarrantyClaimNumber" xml:space="preserve">
    <value>Ja Select Warranty Claim Number</value>
  </data>
  <data name="September" xml:space="preserve">
    <value>Ja Sep</value>
  </data>
  <data name="sequenceno" xml:space="preserve">
    <value>Ja Sequence No</value>
  </data>
  <data name="Serial" xml:space="preserve">
    <value>Ja Serial</value>
  </data>
  <data name="serialnumber" xml:space="preserve">
    <value>Ja Serial #</value>
  </data>
  <data name="serialnumberalreadyexistsforthismodel" xml:space="preserve">
    <value>Ja Serial number already exists for this model</value>
  </data>
  <data name="SerialNumberFieldSearch" xml:space="preserve">
    <value>Ja Serial Number Field Search</value>
  </data>
  <data name="SerialNumbernotfoundfortheselectedmodel" xml:space="preserve">
    <value>Ja Serial number not found for the selected model</value>
  </data>
  <data name="SerialNumberr" xml:space="preserve">
    <value>Ja Serial #</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Ja Service</value>
  </data>
  <data name="ServiceCharge" xml:space="preserve">
    <value>Ja Service Charge</value>
  </data>
  <data name="ServiceChargeCode" xml:space="preserve">
    <value>Ja Service Charge Code</value>
  </data>
  <data name="servicechargecodenotfound" xml:space="preserve">
    <value>Ja Service charge code not found</value>
  </data>
  <data name="ServiceChargeDetail" xml:space="preserve">
    <value>Ja Service Charge Detail</value>
  </data>
  <data name="ServiceChargeFieldSearch" xml:space="preserve">
    <value>Ja Service Charge Field Search</value>
  </data>
  <data name="ServiceChargeIsMandatory" xml:space="preserve">
    <value>Ja Service Charge Detail is Mandatory</value>
  </data>
  <data name="servicecharges" xml:space="preserve">
    <value>Ja Service Charges</value>
  </data>
  <data name="servicechargesdetail" xml:space="preserve">
    <value>Ja Service Charges Detail</value>
  </data>
  <data name="ServiceChargesDetails" xml:space="preserve">
    <value>Ja Service Charges Details</value>
  </data>
  <data name="servicechargesenglish" xml:space="preserve">
    <value>Ja Service Charges</value>
  </data>
  <data name="servicechargesenglish1" xml:space="preserve">
    <value>Ja Service Charges English</value>
  </data>
  <data name="servicechargesheader" xml:space="preserve">
    <value>Ja Service Charges </value>
  </data>
  <data name="servicechargesheader1" xml:space="preserve">
    <value>Ja Service Charges Header</value>
  </data>
  <data name="servicechargeslocale" xml:space="preserve">
    <value>Ja Service Charges </value>
  </data>
  <data name="servicechargeslocale1" xml:space="preserve">
    <value>Ja Service Charges Locale</value>
  </data>
  <data name="servicechargesmaster" xml:space="preserve">
    <value>Ja Service Charges Master</value>
  </data>
  <data name="ServiceChargesTotalAmount" xml:space="preserve">
    <value>Ja Service Charges Total Amount</value>
  </data>
  <data name="ServiceCode" xml:space="preserve">
    <value>Ja Service Code</value>
  </data>
  <data name="servicecodealreadyexists" xml:space="preserve">
    <value>Ja Service code already exists</value>
  </data>
  <data name="servicedate" xml:space="preserve">
    <value>Ja Service Date</value>
  </data>
  <data name="servicedatecannotbelessthancurrentdate" xml:space="preserve">
    <value>Ja Service date cannot be less than Current date</value>
  </data>
  <data name="ServiceDetail" xml:space="preserve">
    <value>Ja Service Detail</value>
  </data>
  <data name="serviceduedays" xml:space="preserve">
    <value>Ja Service Due Days</value>
  </data>
  <data name="serviceduehours" xml:space="preserve">
    <value>Ja Service Due Hours</value>
  </data>
  <data name="ServiceFrequency" xml:space="preserve">
    <value>Ja Service Frequency</value>
  </data>
  <data name="servicehistory" xml:space="preserve">
    <value>Ja Service History</value>
  </data>
  <data name="ServiceInvoice" xml:space="preserve">
    <value>Ja Service Invoice</value>
  </data>
  <data name="ServiceInvoiceNumber" xml:space="preserve">
    <value>Ja Service Invoice #</value>
  </data>
  <data name="ServiceInvoiceReturn" xml:space="preserve">
    <value>Ja Service Invoice Return</value>
  </data>
  <data name="ServiceLevelAgreement" xml:space="preserve">
    <value>Ja Service Level Agreement</value>
  </data>
  <data name="ServicePriority" xml:space="preserve">
    <value>Ja Service Priority</value>
  </data>
  <data name="ServiceQuotationNumber" xml:space="preserve">
    <value>Ja Quotation #</value>
  </data>
  <data name="ServiceRequest" xml:space="preserve">
    <value>Ja Service Request</value>
  </data>
  <data name="ServiceRequest1" xml:space="preserve">
    <value>Ja Case Registration</value>
  </data>
  <data name="ServiceRequest11" xml:space="preserve">
    <value>Ja Service Request</value>
  </data>
  <data name="ServiceRequestAbandoned" xml:space="preserve">
    <value>Ja Service Request Abandoned</value>
  </data>
  <data name="ServiceRequestCount" xml:space="preserve">
    <value>Ja Service Request Count</value>
  </data>
  <data name="ServiceRequestDate" xml:space="preserve">
    <value>Ja Service Request Date</value>
  </data>
  <data name="ServiceRequestDistributionChart" xml:space="preserve">
    <value>Ja Service Request Distribution Chart</value>
  </data>
  <data name="ServicerequestFieldSearch" xml:space="preserve">
    <value>Ja Service request Field Search</value>
  </data>
  <data name="ServiceRequestNumber" xml:space="preserve">
    <value>Ja Service Request #</value>
  </data>
  <data name="ServiceRequestNumbernotfound" xml:space="preserve">
    <value>Ja Service request number not found</value>
  </data>
  <data name="ServiceRequestSummary" xml:space="preserve">
    <value>Ja Service Request Summary</value>
  </data>
  <data name="serviceschdule" xml:space="preserve">
    <value>Ja service schdule</value>
  </data>
  <data name="ServiceSchedule" xml:space="preserve">
    <value>Ja Service Schedule</value>
  </data>
  <data name="ServiceType" xml:space="preserve">
    <value>Ja Service Type</value>
  </data>
  <data name="ServiceTypeName" xml:space="preserve">
    <value>Ja Service Type Name</value>
  </data>
  <data name="ServiceType_Active" xml:space="preserve">
    <value>Ja Is Active ?</value>
  </data>
  <data name="SettlementNumber" xml:space="preserve">
    <value>Ja Settlement #</value>
  </data>
  <data name="SettlementType" xml:space="preserve">
    <value>Ja Settlement Type</value>
  </data>
  <data name="Shift" xml:space="preserve">
    <value>Ja Shift</value>
  </data>
  <data name="ShortName" xml:space="preserve">
    <value>Ja Short Name</value>
  </data>
  <data name="ShortQuantity" xml:space="preserve">
    <value>Ja Short Quantity</value>
  </data>
  <data name="siteaddress" xml:space="preserve">
    <value>Ja Site Address</value>
  </data>
  <data name="siteaddressdetails" xml:space="preserve">
    <value>Ja Site Address Details</value>
  </data>
  <data name="SixteenToTwentyFourHours" xml:space="preserve">
    <value>Ja 16 To 24 Hours</value>
  </data>
  <data name="skill" xml:space="preserve">
    <value>Ja Skill</value>
  </data>
  <data name="Skillisalreadyassociatedwiththeemployee" xml:space="preserve">
    <value>Ja Skill is already associated with the employee</value>
  </data>
  <data name="skilllevel" xml:space="preserve">
    <value>Ja Skill Level</value>
  </data>
  <data name="skilllevelshouldbebetween1to10" xml:space="preserve">
    <value>Ja Skill level should be between 1 to 10</value>
  </data>
  <data name="Skillset" xml:space="preserve">
    <value>Ja Skill Set</value>
  </data>
  <data name="SLAExceeded" xml:space="preserve">
    <value>Ja SLA Exceeded</value>
  </data>
  <data name="SLAHours" xml:space="preserve">
    <value>Ja SLA Hours</value>
  </data>
  <data name="SLAReport" xml:space="preserve">
    <value>Ja SLA Report</value>
  </data>
  <data name="SLAWithin" xml:space="preserve">
    <value>Ja SLA Within</value>
  </data>
  <data name="slno" xml:space="preserve">
    <value>Ja Sl No</value>
  </data>
  <data name="Slow" xml:space="preserve">
    <value>Ja Slow</value>
  </data>
  <data name="SlowCannotbegreaterthanorEqualtoMedium" xml:space="preserve">
    <value>Ja Slow Cannot be greater than or Equal to Medium</value>
  </data>
  <data name="SlowCannotbeSmallerthanorEqualtoUnMoved" xml:space="preserve">
    <value>Ja Slow Cannot be Smaller than or Equal to UnMoved</value>
  </data>
  <data name="sms" xml:space="preserve">
    <value>Ja SMS</value>
  </data>
  <data name="SMSToAddressee" xml:space="preserve">
    <value>Ja SMS To Addressee</value>
  </data>
  <data name="SMSToCustomer" xml:space="preserve">
    <value>Ja SMS To Customer</value>
  </data>
  <data name="SMTPMailBox" xml:space="preserve">
    <value>Ja SMTP Mail Box</value>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>Ja SMTP Password</value>
  </data>
  <data name="SMTPServerName" xml:space="preserve">
    <value>Ja SMTP Server Name</value>
  </data>
  <data name="SMTPUserName" xml:space="preserve">
    <value>Ja SMTP User Name</value>
  </data>
  <data name="Sn" xml:space="preserve">
    <value>Ja Sn</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>Ja Sort Order</value>
  </data>
  <data name="SortOrdercannotbeblank" xml:space="preserve">
    <value>Ja Sort order can not be blank</value>
  </data>
  <data name="sortordercannotbeblankforMenu" xml:space="preserve">
    <value>Ja Sort order can not be blank for menu</value>
  </data>
  <data name="SortOrderCannotbegreaterthan" xml:space="preserve">
    <value>Ja Sort order cannot be greater than 255</value>
  </data>
  <data name="SourceColumns" xml:space="preserve">
    <value>Ja Source Columns</value>
  </data>
  <data name="Specialization" xml:space="preserve">
    <value>Ja Specialization</value>
  </data>
  <data name="SpecializationMaster" xml:space="preserve">
    <value>Ja Specialization Master</value>
  </data>
  <data name="SqlException" xml:space="preserve">
    <value>Ja Database error occured</value>
  </data>
  <data name="SRBasedonCalltype" xml:space="preserve">
    <value>Ja Service Request Based on Call Type</value>
  </data>
  <data name="SRCount" xml:space="preserve">
    <value>Ja Service Requests Count</value>
  </data>
  <data name="SRNotFound" xml:space="preserve">
    <value>Ja Service request not found</value>
  </data>
  <data name="StandardHours" xml:space="preserve">
    <value>Ja Standard Hours</value>
  </data>
  <data name="standardtime" xml:space="preserve">
    <value>Ja Standard Time</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Ja Start Date</value>
  </data>
  <data name="StartdatecannotbegreaterthanEnddate" xml:space="preserve">
    <value>Ja Start date cannot be greater than end date</value>
  </data>
  <data name="startnumber" xml:space="preserve">
    <value>Ja Start Number</value>
  </data>
  <data name="startnumbercannotbenullorzero" xml:space="preserve">
    <value>Ja Start number cannot be null or zero</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Ja State</value>
  </data>
  <data name="stateenglish" xml:space="preserve">
    <value>Ja State English</value>
  </data>
  <data name="statelocale" xml:space="preserve">
    <value>Ja State Locale</value>
  </data>
  <data name="status" xml:space="preserve">
    <value>Ja Status</value>
  </data>
  <data name="StatusHistory" xml:space="preserve">
    <value>Ja Status History</value>
  </data>
  <data name="StdPackingQty" xml:space="preserve">
    <value>Ja Standard Packing Qty</value>
  </data>
  <data name="StepLink" xml:space="preserve">
    <value>Ja Step Link</value>
  </data>
  <data name="StepName" xml:space="preserve">
    <value>Ja Step Name</value>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>Ja Steps</value>
  </data>
  <data name="StepStatus" xml:space="preserve">
    <value>Ja Step Status</value>
  </data>
  <data name="StepType" xml:space="preserve">
    <value>Ja Step Type</value>
  </data>
  <data name="StockBlocking" xml:space="preserve">
    <value>Ja Stock Blocking</value>
  </data>
  <data name="StockBlockingNumber" xml:space="preserve">
    <value>Ja Stock Blocking Number</value>
  </data>
  <data name="StockBlockingNumberAlreadyExists" xml:space="preserve">
    <value>Ja Stock Blocking is already created for this Service Request Number</value>
  </data>
  <data name="StockCheckRequest" xml:space="preserve">
    <value>Ja Stock Check Request</value>
  </data>
  <data name="StockCheckRequestNumber" xml:space="preserve">
    <value>Ja Stock Check Request #</value>
  </data>
  <data name="StockDetailsNotAvailableforthepart" xml:space="preserve">
    <value>Ja Stock Details Not Available for the Part</value>
  </data>
  <data name="StockGreaterThanZero" xml:space="preserve">
    <value>Ja Stock Greater Than Zero</value>
  </data>
  <data name="StockTransferRequestNumber" xml:space="preserve">
    <value>Ja Stock Transfer Request #</value>
  </data>
  <data name="StockUsedInKits" xml:space="preserve">
    <value>Ja Stock Used In Kits</value>
  </data>
  <data name="StockZeroTransactedParts" xml:space="preserve">
    <value>Ja Stock Zero Transacted Parts</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Ja Success</value>
  </data>
  <data name="suffix" xml:space="preserve">
    <value>Ja Suffix</value>
  </data>
  <data name="SuffixalreadySelected" xml:space="preserve">
    <value>Ja Suffix already Selected</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Ja Summary</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>Ja Sunday</value>
  </data>
  <data name="SundryDetail" xml:space="preserve">
    <value>Ja Sundry Detail</value>
  </data>
  <data name="SundryDetails" xml:space="preserve">
    <value>Ja Sundry Details</value>
  </data>
  <data name="SundryJobDescription" xml:space="preserve">
    <value>Ja Sundry Job Description</value>
  </data>
  <data name="SundryTotalAmount" xml:space="preserve">
    <value>Ja Sundry Total Amount</value>
  </data>
  <data name="SuperseedingPartsDetail" xml:space="preserve">
    <value>Ja Superseeding Parts Detail</value>
  </data>
  <data name="Supersession" xml:space="preserve">
    <value>Ja Supersession</value>
  </data>
  <data name="SupersessionDetail" xml:space="preserve">
    <value>Ja Supersession Detail</value>
  </data>
  <data name="SupersessionDetails" xml:space="preserve">
    <value>Ja Supersession Details</value>
  </data>
  <data name="SupersessionType" xml:space="preserve">
    <value>Ja Supersession Type</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Ja Supplier</value>
  </data>
  <data name="SupplierDetails" xml:space="preserve">
    <value>Ja Supplier Details</value>
  </data>
  <data name="SupplierFieldSearch" xml:space="preserve">
    <value>Ja Supplier Field Search</value>
  </data>
  <data name="SupplierInvoiceDate" xml:space="preserve">
    <value>Ja Supplier Invoice Date</value>
  </data>
  <data name="SupplierInvoiceNumber" xml:space="preserve">
    <value>Ja Supplier Invoice Number</value>
  </data>
  <data name="supplierisnotactive" xml:space="preserve">
    <value>Ja Supplier is not active</value>
  </data>
  <data name="SupplierName" xml:space="preserve">
    <value>Ja Supplier Name</value>
  </data>
  <data name="supplierorderclass" xml:space="preserve">
    <value>Ja Supplier Order Class</value>
  </data>
  <data name="SupplierPartNumber" xml:space="preserve">
    <value>Ja Supplier Part #</value>
  </data>
  <data name="SupplierPrice" xml:space="preserve">
    <value>Ja Supplier Price</value>
  </data>
  <data name="SupplierReferenceDate" xml:space="preserve">
    <value>Ja Supplier Reference Date</value>
  </data>
  <data name="SupplierReferenceNumber" xml:space="preserve">
    <value>Ja Supplier Reference Number</value>
  </data>
  <data name="Supplierspecific" xml:space="preserve">
    <value>Ja Supplier Specific</value>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>Ja Table Name</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Ja Tax</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>Ja Taxable</value>
  </data>
  <data name="Taxableothercharges" xml:space="preserve">
    <value>Ja Taxable Other Charges</value>
  </data>
  <data name="Taxableothercharges1" xml:space="preserve">
    <value>Ja Taxable Other Charges 1</value>
  </data>
  <data name="Taxableothercharges1Amount" xml:space="preserve">
    <value>Ja Taxable Other Charges 1 Amount</value>
  </data>
  <data name="Taxableothercharges2" xml:space="preserve">
    <value>Ja Taxable Other Charges 2</value>
  </data>
  <data name="Taxableothercharges2Amount" xml:space="preserve">
    <value>Ja Taxable Other Charges 2 Amount</value>
  </data>
  <data name="TaxableotherchargesAmount" xml:space="preserve">
    <value>Ja Taxable Other Charges  Amount</value>
  </data>
  <data name="Taxamount" xml:space="preserve">
    <value>Ja Tax Amount</value>
  </data>
  <data name="Taxamount1" xml:space="preserve">
    <value>Ja Tax Aamount</value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>Ja Tax Code</value>
  </data>
  <data name="TaxCode1" xml:space="preserve">
    <value>Ja TaxCode</value>
  </data>
  <data name="TaxCode11" xml:space="preserve">
    <value>Ja Tax Code</value>
  </data>
  <data name="TaxCodeName" xml:space="preserve">
    <value>Ja Tax Code Name</value>
  </data>
  <data name="TaxDetail" xml:space="preserve">
    <value>Ja Tax Detail</value>
  </data>
  <data name="TaxDetails" xml:space="preserve">
    <value>Ja Tax Details</value>
  </data>
  <data name="Taxnamealreadyexists" xml:space="preserve">
    <value>Ja Tax name already exists</value>
  </data>
  <data name="TaxonTaxableOtherCharges" xml:space="preserve">
    <value>Ja Tax Structure Other Charges</value>
  </data>
  <data name="TaxonTaxableOtherChargesAmount" xml:space="preserve">
    <value>Ja Tax on Taxable Other Charges</value>
  </data>
  <data name="taxpercentage" xml:space="preserve">
    <value>Ja Tax percentage</value>
  </data>
  <data name="TaxStructure" xml:space="preserve">
    <value>Ja Tax Structure</value>
  </data>
  <data name="taxstructuredetail" xml:space="preserve">
    <value>Ja Tax Structure Detail</value>
  </data>
  <data name="taxstructuredetails" xml:space="preserve">
    <value>Ja Tax Structure Details</value>
  </data>
  <data name="taxstructureenglish" xml:space="preserve">
    <value>Ja Tax Structure </value>
  </data>
  <data name="taxstructureenglish1" xml:space="preserve">
    <value>Ja Tax Structure English</value>
  </data>
  <data name="taxstructureheader" xml:space="preserve">
    <value>Ja Tax Structure Header</value>
  </data>
  <data name="taxstructurelocale" xml:space="preserve">
    <value>Ja Tax Structure </value>
  </data>
  <data name="taxstructurelocale1" xml:space="preserve">
    <value>Ja Tax Structure Locale</value>
  </data>
  <data name="taxstructurename" xml:space="preserve">
    <value>Ja Tax Structure Name</value>
  </data>
  <data name="taxtype" xml:space="preserve">
    <value>Ja Tax Type</value>
  </data>
  <data name="taxtypealreadyselected" xml:space="preserve">
    <value>Ja Tax Type Already Selected</value>
  </data>
  <data name="taxtypealrearyselected" xml:space="preserve">
    <value>Ja Tax Type alreary Selected</value>
  </data>
  <data name="taxtypealrearyselected1" xml:space="preserve">
    <value>Ja Tax type already selected</value>
  </data>
  <data name="taxtypeisreferencedinformulacannotdelete" xml:space="preserve">
    <value>Ja Tax type is referenced in formula cannot delete</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Ja Team</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Ja Terms</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>Ja Terms And Conditions</value>
  </data>
  <data name="TermsConditions" xml:space="preserve">
    <value>Ja Terms &amp; Conditions</value>
  </data>
  <data name="Thebrandhasalreadybeenselected" xml:space="preserve">
    <value>Ja The brand has already been selected</value>
  </data>
  <data name="TheCompanyDealerhasalreadybeenassociated" xml:space="preserve">
    <value>Ja The company/dealer has already been associated</value>
  </data>
  <data name="themodelandserialnumberalreadyexists" xml:space="preserve">
    <value>Ja The Model and Serial Number already exists</value>
  </data>
  <data name="ThereisNoQuantityToReturn" xml:space="preserve">
    <value>Ja There is NoQuantity To Return</value>
  </data>
  <data name="thesecondarysegmentalreadyexists" xml:space="preserve">
    <value>Ja The Secondary Segment already exists</value>
  </data>
  <data name="thestatealreadyexists" xml:space="preserve">
    <value>Ja The State already exists</value>
  </data>
  <data name="ThetaxStructurehasalreadybeenselected" xml:space="preserve">
    <value>Ja The tax structure has already been selected </value>
  </data>
  <data name="ThirtySixToFourtyEightHours" xml:space="preserve">
    <value>Ja 36 To 48 Hours</value>
  </data>
  <data name="ThisCaseIsClosed" xml:space="preserve">
    <value>Ja This case is closed</value>
  </data>
  <data name="ThisLoginIDisalreadyexists" xml:space="preserve">
    <value>Ja This Login Id is already exists</value>
  </data>
  <data name="ThisModuleisalreadyexists" xml:space="preserve">
    <value>Ja This module is already exists</value>
  </data>
  <data name="ThisRoleisalreadyexists" xml:space="preserve">
    <value>Ja This Role is already exists</value>
  </data>
  <data name="Thisroleisalreadyselectedfortheuser" xml:space="preserve">
    <value>Ja This role is already selected for the user</value>
  </data>
  <data name="ThisSerialNumberisalreadyassociatedwiththecustomer" xml:space="preserve">
    <value>Ja This serial number is already associated with the customer</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Ja Thursday</value>
  </data>
  <data name="TimeRemaining" xml:space="preserve">
    <value>Ja Time Remaining</value>
  </data>
  <data name="TimeWiseDistributionOfPendingCases" xml:space="preserve">
    <value>Ja Time Wise Distribution Of Pending Cases</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>Ja To</value>
  </data>
  <data name="todate" xml:space="preserve">
    <value>Ja To Date</value>
  </data>
  <data name="ToDatecannotbegreaterthanCurrentDate" xml:space="preserve">
    <value>Ja To date cannot be greater than current date</value>
  </data>
  <data name="ToDateCannotbegreaterThanToday" xml:space="preserve">
    <value>Ja To date cannot be greater than Current date</value>
  </data>
  <data name="ToDatecannotbelessthanCurrentDate" xml:space="preserve">
    <value>Ja To date cannot be less than current date</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate" xml:space="preserve">
    <value>Ja To date cannot be less than from date</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate1" xml:space="preserve">
    <value>Ja To date cannot be less then From date</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate11" xml:space="preserve">
    <value>Ja To date cannot be less then From date</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate111" xml:space="preserve">
    <value>Ja To date cannot be less then From date</value>
  </data>
  <data name="todatemustbegreaterthanorequaltofromdate" xml:space="preserve">
    <value>Ja To date must be greater than or equal to From date</value>
  </data>
  <data name="ToOEM" xml:space="preserve">
    <value>Ja To OEM</value>
  </data>
  <data name="Tools" xml:space="preserve">
    <value>Ja Tools</value>
  </data>
  <data name="ToolsMaster" xml:space="preserve">
    <value>Ja Tools Master</value>
  </data>
  <data name="ToolsName" xml:space="preserve">
    <value>Ja Tools Name</value>
  </data>
  <data name="ToolsValue" xml:space="preserve">
    <value>Ja Tools Value</value>
  </data>
  <data name="Top10Model" xml:space="preserve">
    <value>Ja Top 10 Models</value>
  </data>
  <data name="ToParty" xml:space="preserve">
    <value>Ja To Party</value>
  </data>
  <data name="TopModel" xml:space="preserve">
    <value>Ja Top 10 Models</value>
  </data>
  <data name="TopModelwithMaximumCase" xml:space="preserve">
    <value>Ja Top Models with Maximum Case</value>
  </data>
  <data name="TopModelwithMaximumSR" xml:space="preserve">
    <value>Ja Top 10 Models with Maximum Case</value>
  </data>
  <data name="TopModelwithMaximumSR1" xml:space="preserve">
    <value>Ja Top 10 Models with Maximum Service Request</value>
  </data>
  <data name="ToStep" xml:space="preserve">
    <value>Ja To Step</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Ja Total</value>
  </data>
  <data name="TotalAllocatedHours" xml:space="preserve">
    <value>Ja Total Allocated Hours</value>
  </data>
  <data name="TotalAmount" xml:space="preserve">
    <value>Ja Total Amount</value>
  </data>
  <data name="TotalAmountisbeyondacceptablelimit" xml:space="preserve">
    <value>Ja Total Amount is beyond acceptable limit</value>
  </data>
  <data name="TotalCancellationAmountShouldbeGreaterthanzero" xml:space="preserve">
    <value>Ja Total Cancellation Amount should be greater than zero</value>
  </data>
  <data name="TotalExpenses" xml:space="preserve">
    <value>Ja Total Expenses</value>
  </data>
  <data name="TotalGRNAmountShouldbeGreaterthanzero" xml:space="preserve">
    <value>Ja Total GRN Amount should be greater than zero</value>
  </data>
  <data name="Totalinvoiceamountcannotbezero" xml:space="preserve">
    <value>Ja Total invoice amount cannot be zero</value>
  </data>
  <data name="TotalOn" xml:space="preserve">
    <value>Ja Total On</value>
  </data>
  <data name="TotalPartsAmount" xml:space="preserve">
    <value>Ja Total Parts Amount</value>
  </data>
  <data name="TotalQuotationAmount" xml:space="preserve">
    <value>Ja Total Quotation Amount</value>
  </data>
  <data name="TotalServiceChargesAmount" xml:space="preserve">
    <value>Ja Total Service Charges Amount</value>
  </data>
  <data name="totalStock" xml:space="preserve">
    <value>Ja Total Stock</value>
  </data>
  <data name="TotalSundryAmount" xml:space="preserve">
    <value>Ja Total Sundry Amount</value>
  </data>
  <data name="TotalTaxableAmount" xml:space="preserve">
    <value>Ja Total Taxable Amount</value>
  </data>
  <data name="TotalTaxableAmountBlank" xml:space="preserve">
    <value>Ja Total taxable amount is blank</value>
  </data>
  <data name="TotalWorkingHours" xml:space="preserve">
    <value>Ja Total Working Hours</value>
  </data>
  <data name="Track" xml:space="preserve">
    <value>Ja Track</value>
  </data>
  <data name="TransactionisalreadybeenLocked" xml:space="preserve">
    <value>Ja Transaction has already been locked</value>
  </data>
  <data name="TransactionisalreadybeenUnLocked" xml:space="preserve">
    <value>Ja Transaction has already been unlocked</value>
  </data>
  <data name="TransactionLockedSuccessfully" xml:space="preserve">
    <value>Ja Transaction Locked Successfully</value>
  </data>
  <data name="TransactionUnLockedSuccessfully" xml:space="preserve">
    <value>Ja Transaction Unlocked Successfully</value>
  </data>
  <data name="Transporter" xml:space="preserve">
    <value>Ja Transporter</value>
  </data>
  <data name="TransportMode" xml:space="preserve">
    <value>Ja Transport Mode</value>
  </data>
  <data name="TravelDetail" xml:space="preserve">
    <value>Ja Travel Detail</value>
  </data>
  <data name="TravelDetail1" xml:space="preserve">
    <value>Ja Travel Detail</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Ja Tuesday</value>
  </data>
  <data name="TwentyFourToThirtySixHours" xml:space="preserve">
    <value>Ja 24 To 36 Hours</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Ja Type</value>
  </data>
  <data name="TypeofPurchase" xml:space="preserve">
    <value>Ja Type of Purchase</value>
  </data>
  <data name="UniqueIdentifier" xml:space="preserve">
    <value>Ja Unique Identifier</value>
  </data>
  <data name="uniqueidentifieralreadyexists" xml:space="preserve">
    <value>Ja Unique identifier already exists</value>
  </data>
  <data name="unitofmeasurement" xml:space="preserve">
    <value>Ja Unit Of Measurement</value>
  </data>
  <data name="UnLock" xml:space="preserve">
    <value>Ja UnLock</value>
  </data>
  <data name="UnMoved" xml:space="preserve">
    <value>Ja UnMoved</value>
  </data>
  <data name="UnMovedCannotbeGreaterthanorEqualtoSlow" xml:space="preserve">
    <value>Ja UnMoved Cannot be Greater than or Equal to Slow</value>
  </data>
  <data name="UnRegisteredServiceRequest" xml:space="preserve">
    <value>Ja UnRegisteredServiceRequest</value>
  </data>
  <data name="UnRegisteredServiceRequest1" xml:space="preserve">
    <value>Ja Verification Queue</value>
  </data>
  <data name="uom" xml:space="preserve">
    <value>Ja UOM</value>
  </data>
  <data name="UpdateArticle" xml:space="preserve">
    <value>Ja Update Article</value>
  </data>
  <data name="UploadAllPartsFromExcel" xml:space="preserve">
    <value>Ja Upload All Parts From Excel</value>
  </data>
  <data name="UploadBy" xml:space="preserve">
    <value>Ja Upload By</value>
  </data>
  <data name="UploadBy1" xml:space="preserve">
    <value>Ja Uploaded By</value>
  </data>
  <data name="UploadDate" xml:space="preserve">
    <value>Ja Upload Date</value>
  </data>
  <data name="UploadDate1" xml:space="preserve">
    <value>Ja Uploaded Date</value>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>Ja Upload File</value>
  </data>
  <data name="UploadParts" xml:space="preserve">
    <value>Ja Upload Parts</value>
  </data>
  <data name="usageenvironment" xml:space="preserve">
    <value>Ja Usage Environment</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Ja User</value>
  </data>
  <data name="UserDataSavedSuccessfully" xml:space="preserve">
    <value>Ja User data saved successfully</value>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>Ja User Detail</value>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>Ja User Details</value>
  </data>
  <data name="Userdonthaveaccesstopartymaster" xml:space="preserve">
    <value>Ja User dont have access to party master</value>
  </data>
  <data name="Userdonthaveaccesstoproductmaster" xml:space="preserve">
    <value>Ja User dont have access to Product Master</value>
  </data>
  <data name="Userdonthaveeditaccess" xml:space="preserve">
    <value>Ja User dont have Edit Access</value>
  </data>
  <data name="Userdonthaveeditaccesstoproductmaster" xml:space="preserve">
    <value>Ja User dont have Edit access to Product Master</value>
  </data>
  <data name="Userislocked" xml:space="preserve">
    <value>Ja User is locked</value>
  </data>
  <data name="Userisnotactive" xml:space="preserve">
    <value>Ja User is not active</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Ja User Name</value>
  </data>
  <data name="UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain" xml:space="preserve">
    <value>Ja User name or password you entered is incorrect. Please try again..</value>
  </data>
  <data name="UserRoleDetails" xml:space="preserve">
    <value>Ja User-Role Details</value>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>Ja User Roles</value>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>Ja User Type</value>
  </data>
  <data name="UtilizationPercentage" xml:space="preserve">
    <value>Ja Utilization %</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Ja Value</value>
  </data>
  <data name="Value1" xml:space="preserve">
    <value>Ja  Value1</value>
  </data>
  <data name="ValueisMandatoryforselectedColumn" xml:space="preserve">
    <value>Ja Value is Mandatory for selected Column</value>
  </data>
  <data name="Ver" xml:space="preserve">
    <value>Ja Ver</value>
  </data>
  <data name="VerificationQueue" xml:space="preserve">
    <value>Ja Verification Queue</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Ja Version</value>
  </data>
  <data name="VersionDate" xml:space="preserve">
    <value>Ja Version Date</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>Ja Version Number</value>
  </data>
  <data name="VersionNumberandDate" xml:space="preserve">
    <value>Ja Version Number and Date</value>
  </data>
  <data name="view" xml:space="preserve">
    <value>Ja View</value>
  </data>
  <data name="ViewArticle" xml:space="preserve">
    <value>Ja View Article</value>
  </data>
  <data name="ViewJobCard" xml:space="preserve">
    <value>Ja View Job Card</value>
  </data>
  <data name="ViewJobCard1" xml:space="preserve">
    <value>Ja View JobCard</value>
  </data>
  <data name="ViewJobCard11" xml:space="preserve">
    <value>Ja View Job Card</value>
  </data>
  <data name="ViewPartsMaster" xml:space="preserve">
    <value>Ja View Parts Master</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Ja Warehouse</value>
  </data>
  <data name="WarrantyClaim" xml:space="preserve">
    <value>Ja Warranty Claim</value>
  </data>
  <data name="WarrantyClaimDate" xml:space="preserve">
    <value>Ja Date</value>
  </data>
  <data name="WarrantyClaimID" xml:space="preserve">
    <value>Ja Warranty Claim ID</value>
  </data>
  <data name="WarrantyClaimNumber" xml:space="preserve">
    <value>Ja Warranty Claim #</value>
  </data>
  <data name="WarrantyClaimOEMApproval" xml:space="preserve">
    <value>Ja Warranty Claim OEM Approval</value>
  </data>
  <data name="WarrantyDate" xml:space="preserve">
    <value>Ja Warranty Date</value>
  </data>
  <data name="warrantydetails" xml:space="preserve">
    <value> Ja Warranty Details</value>
  </data>
  <data name="warrantydetails1" xml:space="preserve">
    <value>Ja Warranty Details</value>
  </data>
  <data name="WarrantyType" xml:space="preserve">
    <value>Ja Warranty Type</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Ja Website</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Ja Wednesday</value>
  </data>
  <data name="weight" xml:space="preserve">
    <value>Ja Weight</value>
  </data>
  <data name="WeightedAverageCost" xml:space="preserve">
    <value>Ja Weighted Average Cost</value>
  </data>
  <data name="WeightedAverageCostisZero" xml:space="preserve">
    <value>Ja Weighted Average Cost is Zero</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Ja Welcome :</value>
  </data>
  <data name="Where" xml:space="preserve">
    <value>Ja Where</value>
  </data>
  <data name="WorkFlow" xml:space="preserve">
    <value>Ja Work Flow</value>
  </data>
  <data name="WorkFlowID" xml:space="preserve">
    <value>Ja Work Flow ID</value>
  </data>
  <data name="WorkFlowName" xml:space="preserve">
    <value>Ja Work Flow Name</value>
  </data>
  <data name="WorkFlowSteps" xml:space="preserve">
    <value>Ja Work Flow Steps</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Ja Working Days</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Ja Working Time</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Ja Year</value>
  </data>
  <data name="Yearshouldbebetween2000and2999" xml:space="preserve">
    <value>Ja Year should be between 2000 and 2999</value>
  </data>
  <data name="yes" xml:space="preserve">
    <value>Ja Yes</value>
  </data>
  <data name="YouChangedTheEmailDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>Ja You changed the Email. Details Will be lost.\nDo you want to proceed?</value>
  </data>
  <data name="YouChangedTheLocationDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>Ja You changed the Location. Details Will be lost.\nDo you want to proceed?</value>
  </data>
  <data name="YouChangedTheMobileDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>Ja You changed the Mobile. Details Will be lost.\nDo you want to proceed?</value>
  </data>
  <data name="YouChangedTheNameDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>Ja You changed the Name. Details Will be lost.\nDo you want to proceed?</value>
  </data>
  <data name="Youdonothaveaddpermission" xml:space="preserve">
    <value>Ja You do not have Add permission</value>
  </data>
  <data name="Youdonothaveeditpermission" xml:space="preserve">
    <value>Ja You do not have edit permission</value>
  </data>
  <data name="Youdonothaveeditpermission1" xml:space="preserve">
    <value>Ja You donot have edit permission</value>
  </data>
  <data name="youdonthaveaccesspermissiontoallocationpriority" xml:space="preserve">
    <value>Ja You dont have access permission to Allocation Priority</value>
  </data>
  <data name="YouDontHaveAccessRights" xml:space="preserve">
    <value>Ja You dont have access rights</value>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>Ja You have been Logged out successfully</value>
  </data>
  <data name="ZipCode" xml:space="preserve">
    <value>Ja Zip Code</value>
  </data>
  <data name="DuplicateWarehouse" xml:space="preserve">
    <value>Ja Duplicate Warehouse</value>
    <comment>Message</comment>
  </data>
  <data name="Partisnotassociatedwiththeselectedsupplier" xml:space="preserve">
    <value>Ja Part is not associated with the selected supplier</value>
  </data>
  <data name="Pleaseentereffectivefromdate" xml:space="preserve">
    <value>Ja Please enter effective from date</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectsupplier" xml:space="preserve">
    <value>Ja Please select supplier</value>
    <comment>Message</comment>
  </data>
  <data name="AddStockCheckConfirmation" xml:space="preserve">
    <value>Ja Add Stock Check Confirmation</value>
  </data>
  <data name="ConfirmedBy" xml:space="preserve">
    <value>Ja Confirmed By</value>
  </data>
  <data name="EditStockCheckConfirmation" xml:space="preserve">
    <value>Ja Edit Stock Check Confirmation</value>
  </data>
  <data name="ExcessQty" xml:space="preserve">
    <value>Ja Excess Qty</value>
  </data>
  <data name="NumberOfCounts" xml:space="preserve">
    <value>Ja Number Of Counts</value>
  </data>
  <data name="PhysicalStock" xml:space="preserve">
    <value>Ja Physical Stock</value>
  </data>
  <data name="Requestdate" xml:space="preserve">
    <value>Ja Request date</value>
  </data>
  <data name="ShortQty" xml:space="preserve">
    <value>Ja Short Qty</value>
  </data>
  <data name="StockAdjustmentNo" xml:space="preserve">
    <value>Ja Stock Adjustment #</value>
  </data>
  <data name="StockCheckConfirmation" xml:space="preserve">
    <value>Ja Stock Check Confirmation</value>
  </data>
  <data name="StockCheckConfirmationNo" xml:space="preserve">
    <value>Ja Stock Check Confirmation #</value>
  </data>
  <data name="SystemStock" xml:space="preserve">
    <value>Ja System Stock</value>
  </data>
  <data name="SalesInvoiceNumber" xml:space="preserve">
    <value>Ja Sales Invoice #</value>
  </data>
  <data name="invalidStockCheckRequestNumber" xml:space="preserve">
    <value>Ja Invalid Stock Check Request Number</value>
  </data>
  <data name="StockCheckConfirmationSearch" xml:space="preserve">
    <value>Ja Stock Check Confirmation Search</value>
  </data>
  <data name="InvalidStandardPackingQuantity" xml:space="preserve">
    <value>Ja Invalid standard packing quantity</value>
    <comment>Message</comment>
  </data>
  <data name="Noneofthepartswereuploaded" xml:space="preserve">
    <value>Ja None of the parts in the excel were uploaded</value>
    <comment>Message</comment>
  </data>
  <data name="Uploadedsuccesfully" xml:space="preserve">
    <value>Ja Uploaded succesfully</value>
    <comment>Message</comment>
  </data>
  <data name="uploadedwithsomeerrorcheckerrorexcel" xml:space="preserve">
    <value>Ja Uploaded with some error check error excel</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidorinactiveParty" xml:space="preserve">
    <value>Ja Invalid or inactive Party</value>
  </data>
  <data name="MRPFactor" xml:space="preserve">
    <value>Ja MRP Factor</value>
    <comment>Label</comment>
  </data>
  <data name="AddStockTransferRequest" xml:space="preserve">
    <value>Ja Add Stock Transfer Request</value>
    <comment>Label</comment>
  </data>
  <data name="EditStockTransferRequest" xml:space="preserve">
    <value>Ja Edit Stock Transfer Request</value>
    <comment>Label</comment>
  </data>
  <data name="StockTransferRequest" xml:space="preserve">
    <value>Ja Stock Transfer Request</value>
    <comment>Label</comment>
  </data>
  <data name="SupplyingBranch" xml:space="preserve">
    <value>Ja Supplying Branch</value>
    <comment>Label</comment>
  </data>
  <data name="SupplyingBranchDetails" xml:space="preserve">
    <value>Ja Supplying Branch Details</value>
    <comment>Label</comment>
  </data>
  <data name="SupplyingWarehouse" xml:space="preserve">
    <value>Ja Supplying Warehouse</value>
    <comment>Label</comment>
  </data>
  <data name="BinningList" xml:space="preserve">
    <value>Ja Binning List</value>
    <comment>Label</comment>
  </data>
  <data name="Partsnotavailable" xml:space="preserve">
    <value>Ja Parts not available</value>
  </data>
  <data name="TotalSalesInvoiceAmount" xml:space="preserve">
    <value>Ja Tota lSales Invoice Amount</value>
  </data>
  <data name="NoteDate" xml:space="preserve">
    <value>Ja Note Date</value>
    <comment>Label</comment>
  </data>
  <data name="IssuedTo" xml:space="preserve">
    <value>Ja Issued To</value>
    <comment>Label</comment>
  </data>
  <data name="NonReturnable" xml:space="preserve">
    <value>Ja Non-Returnable</value>
    <comment>Label</comment>
  </data>
  <data name="Returnable" xml:space="preserve">
    <value>Ja Returnable</value>
    <comment>Label</comment>
  </data>
  <data name="OrderClassServiceType" xml:space="preserve">
    <value>Order Class Service Type</value>
  </data>
  <data name="OrderClassServiceTypeLocale" xml:space="preserve">
    <value>Ja Order Class Service Type Locale</value>
  </data>
  <data name="PaymentDueDate" xml:space="preserve">
    <value>Ja Payment Due Date</value>
  </data>
  <data name="DeliveryNote" xml:space="preserve">
    <value>Ja Delivery Note</value>
    <comment>Label</comment>
  </data>
  <data name="PackingListNumber" xml:space="preserve">
    <value>Ja Packing List #</value>
  </data>
  <data name="SalesInvoiceReturnNumber" xml:space="preserve">
    <value>Ja Sales Invoice Return #</value>
  </data>
  <data name="AddStockReceiptGRN" xml:space="preserve">
    <value>Ja Add Stock Receipt GRN</value>
    <comment>Label</comment>
  </data>
  <data name="EditStockReceiptGRN" xml:space="preserve">
    <value>Ja Edit Stock Receipt GRN</value>
    <comment>Label</comment>
  </data>
  <data name="GDRStatus" xml:space="preserve">
    <value>Ja GDR Status</value>
    <comment>Label</comment>
  </data>
  <data name="OtherCharges" xml:space="preserve">
    <value>Ja Other Charges</value>
    <comment>Label</comment>
  </data>
  <data name="StockReceiptGRN" xml:space="preserve">
    <value>Ja Stock Receipt GRN</value>
    <comment>Label</comment>
  </data>
  <data name="StockReceiptGRNNumber" xml:space="preserve">
    <value>Ja Stock Receipt GRN Number</value>
    <comment>Label</comment>
  </data>
  <data name="StockTransferNoteDate" xml:space="preserve">
    <value>Ja Stock Transfer Note Date</value>
    <comment>Label</comment>
  </data>
  <data name="StockTransferNoteNumber" xml:space="preserve">
    <value>Ja Stock Transfer Note Number</value>
    <comment>Label</comment>
  </data>
  <data name="UpdateGDR" xml:space="preserve">
    <value>Ja Update GDR</value>
    <comment>Label</comment>
  </data>
  <data name="Quantitycannotbegreaterthanfreestock" xml:space="preserve">
    <value>Ja Quantity cannot be greater than Free Stock</value>
  </data>
  <data name="StockTransferNote" xml:space="preserve">
    <value>Stock Transfer Note</value>
    <comment>Label</comment>
  </data>
  <data name="RequestingBranch" xml:space="preserve">
    <value>Requesting Branch</value>
    <comment>Label</comment>
  </data>
  <data name="BranchSearch" xml:space="preserve">
    <value>Ja Branch Search</value>
  </data>
  <data name="Full" xml:space="preserve">
    <value>Ja Full</value>
  </data>
  <data name="Partial" xml:space="preserve">
    <value>Ja Partial</value>
  </data>
  <data name="ReasonforReturn" xml:space="preserve">
    <value>Ja Reason for Return</value>
  </data>
  <data name="ReturnType" xml:space="preserve">
    <value>Ja ReturnType</value>
  </data>
  <data name="SalesInvoiceReturn" xml:space="preserve">
    <value>Ja Sales Invoice Return</value>
  </data>
  <data name="AdjustedValue" xml:space="preserve">
    <value>Ja Adjusted Value</value>
    <comment>Label</comment>
  </data>
  <data name="CurrentStockQty" xml:space="preserve">
    <value>Ja Current Stock Qty</value>
    <comment>Label</comment>
  </data>
  <data name="CurrentValue" xml:space="preserve">
    <value>Ja Current Value</value>
    <comment>Label</comment>
  </data>
  <data name="CurrentWAC" xml:space="preserve">
    <value>Ja Current WAC</value>
    <comment>Label</comment>
  </data>
  <data name="UpdateWeightAverageCost" xml:space="preserve">
    <value>Ja Update Weighted Average Cost</value>
    <comment>Label</comment>
  </data>
  <data name="ValueAdjustment" xml:space="preserve">
    <value>Ja Value Adjustment</value>
    <comment>Label</comment>
  </data>
  <data name="SalesInvoiceReturnDate" xml:space="preserve">
    <value>Ja Sales Invoice Return Date</value>
  </data>
  <data name="InvalidStockTransferNoteNumber" xml:space="preserve">
    <value>Ja Invalid Stock Transfer Note Number</value>
    <comment>Message</comment>
  </data>
  <data name="Quantitycannotbegreaterthanreturnableqty" xml:space="preserve">
    <value>Ja Quantity cannot be greater than returnable quantity</value>
  </data>
  <data name="Pleaseselecttypeofpurchase" xml:space="preserve">
    <value>Ja Please select type of purchase</value>
    <comment>Message</comment>
  </data>
  <data name="TotalPurchaseOrderAmountcannotbezero" xml:space="preserve">
    <value>Ja Total purchase order amount cannot be 0</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectpartsorder" xml:space="preserve">
    <value>Ja Please select parts order</value>
    <comment>Message</comment>
  </data>
  <data name="NetAmount" xml:space="preserve">
    <value>Ja Net Amount</value>
  </data>
  <data name="Pleaseselectpurchaseinvoice" xml:space="preserve">
    <value>Ja Please select purchase invoice</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectpurchaseorder" xml:space="preserve">
    <value>Ja Please select purchase order</value>
    <comment>Message</comment>
  </data>
  <data name="Quantitycannotbegreaterthan" xml:space="preserve">
    <value>Ja Quantity cannot be greater than</value>
    <comment>Message</comment>
  </data>
  <data name="WarehouseisAssociatedtoMultipleOrderClass" xml:space="preserve">
    <value>Ja Warehoues is Associated to multiple Order Class, Select other Warehouse</value>
    <comment>Message</comment>
  </data>
  <data name="StockConfirmation" xml:space="preserve">
    <value />
  </data>
  <data name="ParentpartcannotbechildPart" xml:space="preserve">
    <value>Ja Parent part cannot be child Part</value>
    <comment>Message</comment>
  </data>
  <data name="BinLocationaddedsuccessfully" xml:space="preserve">
    <value>Ja Bin Location added successfully</value>
    <comment>Message</comment>
  </data>
  <data name="BinLocationnotfounddoyouwanttoadd" xml:space="preserve">
    <value>Ja Bin Location not found, do you want to add?</value>
    <comment>Message</comment>
  </data>
  <data name="youdonthavepermissiontoaddrecordstobinlocationmaster" xml:space="preserve">
    <value>Ja You dont have permission to add records to bin location master</value>
    <comment>Message</comment>
  </data>
  <data name="StockAdjustment" xml:space="preserve">
    <value>Ja Stock Adjustment</value>
  </data>
  <data name="KitPartNumbe" xml:space="preserve">
    <value>Ja Kit Part Number</value>
    <comment>Label</comment>
  </data>
  <data name="ExcessAmount" xml:space="preserve">
    <value>Ja Excess Amount</value>
  </data>
  <data name="invalidStockCheckConfirmationNumber" xml:space="preserve">
    <value>Ja Invalid Stock Check Confirmation Number</value>
  </data>
  <data name="ShortAmount" xml:space="preserve">
    <value>Ja Short Amount</value>
  </data>
  <data name="PackingList" xml:space="preserve">
    <value>Packing List</value>
  </data>
  <data name="PartyOrBranch" xml:space="preserve">
    <value>Party/Branch</value>
  </data>
  <data name="Confirmationdate" xml:space="preserve">
    <value>Ja Stock Confirmation Date</value>
  </data>
  <data name="PartsQuotation" xml:space="preserve">
    <value>Ja Parts Quotation</value>
  </data>
  <data name="ServiceQuotation" xml:space="preserve">
    <value>Ja ServiceQuotation</value>
  </data>
  <data name="ServiceCreditLimit" xml:space="preserve">
    <value>Ja Service Credit Limit</value>
    <comment>Label</comment>
  </data>
  <data name="UnBlockaStock" xml:space="preserve">
    <value>Ja Please Unblock a Stock</value>
    <comment>Message</comment>
  </data>
  <data name="QuotationDate" xml:space="preserve">
    <value>Ja Quotation Date</value>
  </data>
  <data name="QuotationValidity" xml:space="preserve">
    <value>Ja Quotation Validity</value>
  </data>
  <data name="OrderClassAlreadyAssociatedtoWarehouse" xml:space="preserve">
    <value>Ja Order Class already associated to Warehouse</value>
    <comment>Message</comment>
  </data>
  <data name="DeliveryNoteDate" xml:space="preserve">
    <value>Delivery Note Date</value>
  </data>
  <data name="DeliveryNoteNumber" xml:space="preserve">
    <value>Delivery Note #</value>
  </data>
  <data name="DeliveryNoteReturnNumber" xml:space="preserve">
    <value>Delivery Note Return #</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanAcceptedQty" xml:space="preserve">
    <value>Ja ReAllocated Quantity cannot be greater than Accepted Quantity</value>
  </data>
  <data name="SuccessfullyReAllocated" xml:space="preserve">
    <value>Ja Successfully Re-Allocated</value>
  </data>
  <data name="IssuedQuantity" xml:space="preserve">
    <value>Issued Quantity</value>
  </data>
  <data name="CreateInvoice" xml:space="preserve">
    <value>Ja Create Invoice</value>
    <comment>Label</comment>
  </data>
  <data name="PickedqtyCannotbegreaterthanAllocatedQty" xml:space="preserve">
    <value>Ja Picked Quantity Cannot be greater than Allocated Quantity</value>
  </data>
  <data name="ChildTaxStructure" xml:space="preserve">
    <value>Ja Child Tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="ParentTaxStructure" xml:space="preserve">
    <value>Ja Parent Tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="TaxStructureMapping" xml:space="preserve">
    <value>Ja Tax Structure Mapping</value>
    <comment>Label</comment>
  </data>
  <data name="TaxStructureisalreadyassociated" xml:space="preserve">
    <value>Ja Tax Structure is already associated</value>
    <comment>Message</comment>
  </data>
  <data name="FollowingPartsareblockedcannotcreateGRN" xml:space="preserve">
    <value>Ja Cannot create GRN as following part(s) are blocked</value>
    <comment>Message</comment>
  </data>
  <data name="CannotsaveGDRasfollowingpartsareblocked" xml:space="preserve">
    <value>Ja Cannot save GDR as following part(s) are blocked</value>
    <comment>Message</comment>
  </data>
  <data name="CreditDaysPassedFor" xml:space="preserve">
    <value>Ja Credit Days Passed For</value>
  </data>
  <data name="Creditlimitispassedfortheparty" xml:space="preserve">
    <value>Ja Credit limit is exceeded for the party</value>
  </data>
  <data name="doyouwanttocontinue" xml:space="preserve">
    <value>Ja do you want to continue</value>
  </data>
  <data name="HigherPriorityOrderexistsdoyouwanttocontinue" xml:space="preserve">
    <value>Ja Higher Priority Order exists do you want to continue?</value>
  </data>
  <data name="PartsareblockedofstockCheckrequestacceptancecannotbedone" xml:space="preserve">
    <value>Ja Parts are blocked for Stock Check Request, Acceptance cannot be done</value>
  </data>
  <data name="Discountcannotbegreaterthan100" xml:space="preserve">
    <value>Ja Discount cannot be greaterthan 100 percentage</value>
    <comment>Message</comment>
  </data>
  <data name="Partdetailsnotpresentfortheselectedwarehouse" xml:space="preserve">
    <value>Ja Part details not present for the selected warehouse</value>
    <comment>Message</comment>
  </data>
  <data name="GrossAmount" xml:space="preserve">
    <value>Ja Gross Amount</value>
  </data>
  <data name="PartsOrderCancellationNumber" xml:space="preserve">
    <value>Ja Parts Order Cancellation #</value>
  </data>
  <data name="DuplicateCheckListDescriptionisnotAllowed" xml:space="preserve">
    <value>Ja Duplicate CheckList Description is not Allowed</value>
  </data>
  <data name="Pleaseselectordertype" xml:space="preserve">
    <value>Ja Please select order type</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectpicklist" xml:space="preserve">
    <value>Ja Please select pick list</value>
    <comment>Message</comment>
  </data>
  <data name="splitquantitycannotbezero" xml:space="preserve">
    <value>Ja split quantity cannot be zero</value>
    <comment>Message</comment>
  </data>
  <data name="sumofsplittedquantitycannotbegreaterthanpickedquantity" xml:space="preserve">
    <value>Ja sum of splitted quantity cannot be greater than picked quantity</value>
    <comment>Message</comment>
  </data>
  <data name="JobcardActivityStatus" xml:space="preserve">
    <value>Ja Job Card Activity Status</value>
  </data>
  <data name="AllocatedQuantity" xml:space="preserve">
    <value>JA Allocated Qty</value>
  </data>
  <data name="DeallocatedQuantity" xml:space="preserve">
    <value>JA Deallocated Qty</value>
  </data>
  <data name="DeAllocationReRunAllocation" xml:space="preserve">
    <value>JA De-Allocation &amp; Re-RunAllocation</value>
  </data>
  <data name="ReallocatedQty" xml:space="preserve">
    <value>JA Reallocated Qty</value>
  </data>
  <data name="ReAllocation" xml:space="preserve">
    <value>JA Re-Allocation</value>
  </data>
  <data name="ReallocationNum" xml:space="preserve">
    <value>JA Reallocation #</value>
  </data>
  <data name="ClosingStock" xml:space="preserve">
    <value>Ja Closing Stock</value>
  </data>
  <data name="Issues" xml:space="preserve">
    <value>Ja Issues</value>
  </data>
  <data name="OpeningStock" xml:space="preserve">
    <value>Ja Opening Stock</value>
  </data>
  <data name="Receipt" xml:space="preserve">
    <value>Ja Receipt</value>
  </data>
  <data name="StockLedger" xml:space="preserve">
    <value>Ja Stock Ledger</value>
  </data>
  <data name="TransactionNumber" xml:space="preserve">
    <value>Ja Transaction Number</value>
  </data>
  <data name="TransactionType" xml:space="preserve">
    <value>Ja Transaction Type</value>
  </data>
  <data name="SelectYear" xml:space="preserve">
    <value>Ja Select Year</value>
  </data>
  <data name="DiscountAmountShouldbeLessThan" xml:space="preserve">
    <value>Ja Discount amount should be less than</value>
  </data>
  <data name="Freestockisnotenoughforthepartnumber" xml:space="preserve">
    <value>Ja Free stock is not enough for the part number</value>
    <comment>Message</comment>
  </data>
  <data name="numberofkitstomakemustbegreaterthanzero" xml:space="preserve">
    <value>Ja Number of kits to make must be greaterthan zero</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidOrderNumber" xml:space="preserve">
    <value>Ja Invalid Purchase Order Number</value>
    <comment>Label</comment>
  </data>
  <data name="OrderorStockTransferRequest" xml:space="preserve">
    <value>Ja Purchase Order/Stock Transfer</value>
    <comment>Label</comment>
  </data>
  <data name="OrderorStockTransferRequestNumber" xml:space="preserve">
    <value>Ja Purchase Order/Stock Transfer Request #</value>
    <comment>Label</comment>
  </data>
  <data name="InvalidDeliveryNoteNumberOrReturnIsAlreadyDone" xml:space="preserve">
    <value>Invalid Delivery Note Number or Return is already done</value>
    <comment>Message</comment>
  </data>
  <data name="SalesNonSales" xml:space="preserve">
    <value>Ja Sales/Non Sales</value>
  </data>
  <data name="NonSales" xml:space="preserve">
    <value>Ja NonSales</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Ja Sales</value>
  </data>
  <data name="AllocatedDetails" xml:space="preserve">
    <value>JA Allocated Details</value>
    <comment>lable</comment>
  </data>
  <data name="BackOrderDetails" xml:space="preserve">
    <value>JA Back Order Details</value>
    <comment>lable</comment>
  </data>
  <data name="PleaseDeallocateReallocatesomequantity" xml:space="preserve">
    <value>JA Please De-allocate &amp; Re-allocate some quantity</value>
    <comment>Message</comment>
  </data>
  <data name="Pleasedeallocatesomequantity" xml:space="preserve">
    <value>JA Please deallocate some quantity</value>
    <comment>Message</comment>
  </data>
  <data name="ReallocateallDeallocatedQuanity" xml:space="preserve">
    <value>JA Re-allocate all De-allocated Quanity</value>
    <comment>Message</comment>
  </data>
  <data name="RecordhasbeenalteredfortheselectedtransactionPleaserequery" xml:space="preserve">
    <value>JA Record has been altered, for the selected transaction. Please re-query</value>
    <comment>Message</comment>
  </data>
  <data name="RerunAllocationDetails" xml:space="preserve">
    <value>JA Rerun - Allocation Details</value>
    <comment>lable</comment>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Ja Home</value>
    <comment>Label</comment>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>Ja Log Out</value>
    <comment>Label</comment>
  </data>
  <data name="PurchaseInvoiceDailyReport" xml:space="preserve">
    <value>Ja Purchase Invoice Daily Report</value>
  </data>
  <data name="PurchaseOrderDailyReport" xml:space="preserve">
    <value>Ja Purchase Order Daily Report</value>
    <comment>Label</comment>
  </data>
  <data name="CaseNumbercannotbezero" xml:space="preserve">
    <value>Ja Case Number cannot be zero</value>
    <comment>Message</comment>
  </data>
  <data name="DiscountedAmountSIR" xml:space="preserve">
    <value>Ja Discounted Amt</value>
  </data>
  <data name="DiscoutAmountSIR" xml:space="preserve">
    <value>Ja Discount Amt</value>
  </data>
  <data name="InvoiceQuantitySIR" xml:space="preserve">
    <value>Ja Invoice Qty</value>
  </data>
  <data name="ReturnedQuantitySIR" xml:space="preserve">
    <value>Ja Returned Qty</value>
  </data>
  <data name="ReturnQuantitySIR" xml:space="preserve">
    <value>Ja Return Qty</value>
  </data>
  <data name="SupplierWiseExcessorShortPartsReceived" xml:space="preserve">
    <value>Ja Supplier Wise Excess or Short Parts Received</value>
    <comment>Label</comment>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>Ja No data found</value>
    <comment>Message</comment>
  </data>
  <data name="AcceptedQuantityPO" xml:space="preserve">
    <value>Ja Accepted Qty</value>
  </data>
  <data name="DiscountedAmountPO" xml:space="preserve">
    <value>Ja Discounted Amt</value>
  </data>
  <data name="DiscoutAmountPO" xml:space="preserve">
    <value>Ja Discount Amt</value>
  </data>
  <data name="EnteredToolalreadyexist" xml:space="preserve">
    <value>Ja Entered Tool already exist</value>
  </data>
  <data name="PurchaseInvoiceMonthlyReport" xml:space="preserve">
    <value>Ja Purchase Invoice Monthly Report</value>
    <comment>Label</comment>
  </data>
  <data name="ExpenseAmount" xml:space="preserve">
    <value>Ja Expense Amount</value>
    <comment>Label</comment>
  </data>
  <data name="FromInvoiceNumber" xml:space="preserve">
    <value>Ja From Invoice Number</value>
    <comment>Label</comment>
  </data>
  <data name="PartsAmount" xml:space="preserve">
    <value>Ja PartsAmount</value>
    <comment>Label</comment>
  </data>
  <data name="ToInvoiceNumber" xml:space="preserve">
    <value>Ja To Invoice Number</value>
    <comment>Label</comment>
  </data>
  <data name="GDRSettlementReport" xml:space="preserve">
    <value>Ja GDR Settlement Report</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateSiteAddressAndLocation" xml:space="preserve">
    <value>Ja Duplicate site address and location</value>
  </data>
  <data name="PreviousServiceAgreementwillbeInactivewouldyouliketoProceed" xml:space="preserve">
    <value>Ja Previous service agreement will be inactive would you like to proceed</value>
  </data>
  <data name="AllocationStatus" xml:space="preserve">
    <value>Ja Allocation Status</value>
  </data>
  <data name="OrderAmount" xml:space="preserve">
    <value>Ja Order Amount</value>
    <comment>Label</comment>
  </data>
  <data name="SupplierWisePurchaseReport" xml:space="preserve">
    <value>Ja Supplierwise Purchase Report</value>
  </data>
  <data name="AlternatePartNumber" xml:space="preserve">
    <value>Ja Alternate Part #</value>
  </data>
  <data name="Doyouwanttoreinvoice" xml:space="preserve">
    <value>Ja Do you want to reinvoice</value>
  </data>
  <data name="ReturnAmountCannotbeZero" xml:space="preserve">
    <value>Return Amount Cannot be Zero</value>
    <comment>Message</comment>
  </data>
  <data name="ThereisNoQuantityForReturn" xml:space="preserve">
    <value>Therei s No Quantity To Return</value>
  </data>
  <data name="PartsOrdercannotbecreatedbecauseofthelocalpartsdoyouwanttosavepurchaseorder" xml:space="preserve">
    <value>Ja Parts order cannot be created because of the local parts do you want to save purchase order?</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidClaimNumber" xml:space="preserve">
    <value>Ja Invalid Claim Number</value>
  </data>
  <data name="Datecannotbegreaterthanserviceagreementtodate" xml:space="preserve">
    <value>Ja Date cannot be greater than service agreement to date</value>
  </data>
  <data name="Datecannotbelessthanserviceagreementfromdate" xml:space="preserve">
    <value>Ja Date cannot be less than service agreement from date</value>
  </data>
  <data name="EmployeeisInActive" xml:space="preserve">
    <value>Ja Employeeis In Active</value>
  </data>
  <data name="InvalidEmployee" xml:space="preserve">
    <value>Ja Invalid employee</value>
  </data>
  <data name="PromisedReturnDateCannotbelessThanCurrentDate" xml:space="preserve">
    <value>Ja Promised return date cannot be less than current date</value>
  </data>
  <data name="ReturnedDateCannotbelessThanCurrentDate" xml:space="preserve">
    <value>Ja Returned date cannot be less than current date</value>
  </data>
  <data name="ReturnedDateCannotbelessThanPromisedReturnDate" xml:space="preserve">
    <value>Ja Returned date cannot be less than promised return date</value>
  </data>
  <data name="ReadingShouldbegreaterthanFailureReading" xml:space="preserve">
    <value>Ja Reading Should be greater than Failure Reading</value>
  </data>
  <data name="ReadingShouldbegreaterthanPreviousReading" xml:space="preserve">
    <value>Ja Reading Should be greater than Previous Reading</value>
  </data>
  <data name="DefectGroupNotActive" xml:space="preserve">
    <value>Ja Defect Code is not active</value>
  </data>
  <data name="InvalidDefectCode" xml:space="preserve">
    <value>Ja Invalid Defect Code </value>
  </data>
  <data name="Partnumberissupersessionwithanotherpart" xml:space="preserve">
    <value>Ja Part number is supersession with another part</value>
    <comment>Message</comment>
  </data>
  <data name="BackOrderStatus" xml:space="preserve">
    <value>Ja Back Order Status</value>
  </data>
  <data name="Dragmebelowtosee" xml:space="preserve">
    <value>Ja Drag me below to see</value>
    <comment>Label</comment>
  </data>
  <data name="FreeStockisupdated" xml:space="preserve">
    <value>Ja Free Stock is updated bu other user</value>
  </data>
  <data name="StockNotFound" xml:space="preserve">
    <value>Ja Stock is not found in bin location</value>
  </data>
  <data name="StockUpdatedByOtherUser" xml:space="preserve">
    <value>Ja Stock is updated by other user. Please re select the transaction</value>
  </data>
  <data name="WarrantyClaimAlreadyDone" xml:space="preserve">
    <value>Ja WarrantyClaimAlreadyDone</value>
  </data>
  <data name="WarehouseOrderClassAssociation" xml:space="preserve">
    <value>Ja Warehouse &amp; OrderClass Association</value>
  </data>
  <data name="AddServiceAgreement" xml:space="preserve">
    <value>Ja Add Service Agreement</value>
  </data>
  <data name="ApprovedAmountCannotbeZero" xml:space="preserve">
    <value>Ja Approved Amount Cannot be Zero</value>
  </data>
  <data name="InternalInvoiceReturnDetails" xml:space="preserve">
    <value>Ja Internal Invoice Return Details</value>
  </data>
  <data name="NoInternalInvoices" xml:space="preserve">
    <value>Ja No Internal Invoices</value>
  </data>
  <data name="NoServiceHistoryRecords" xml:space="preserve">
    <value>Ja No Service History Records</value>
  </data>
  <data name="InactiveModel" xml:space="preserve">
    <value>Ja Inactive Model</value>
    <comment>Message</comment>
  </data>
  <data name="InactiveSupplier" xml:space="preserve">
    <value>Ja Inactive Supplier</value>
    <comment>Message</comment>
  </data>
  <data name="CreditLimitExceededDoyouwanttocontinue" xml:space="preserve">
    <value>Ja Credit Limit Exceeded Do you want to continue</value>
  </data>
  <data name="ExpenseType" xml:space="preserve">
    <value>Ja Expense Type</value>
  </data>
  <data name="PendingForApproval" xml:space="preserve">
    <value>Ja Pending For Approval</value>
  </data>
  <data name="InactiveParty" xml:space="preserve">
    <value>Ja Inactive Party</value>
  </data>
  <data name="Partyislocked" xml:space="preserve">
    <value>Ja Party is locked</value>
  </data>
  <data name="DiscountPercent" xml:space="preserve">
    <value>Ja Discount %</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Ja Invoice Date</value>
  </data>
  <data name="AddCreditNote" xml:space="preserve">
    <value>Ja Add Credit Note</value>
  </data>
  <data name="AddDebitNote" xml:space="preserve">
    <value>Ja Add Debit Note</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Ja Credit Note</value>
  </data>
  <data name="CreditNoteAmount" xml:space="preserve">
    <value>Ja Credit Note Amount</value>
  </data>
  <data name="CreditNoteDetails" xml:space="preserve">
    <value>Ja Credit Note Details</value>
  </data>
  <data name="CreditNoteNumber" xml:space="preserve">
    <value>Ja Credit Note Number</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Ja Debit Note</value>
  </data>
  <data name="DebitNoteAmount" xml:space="preserve">
    <value>Ja Debit Note Amount</value>
  </data>
  <data name="DebitNoteDetails" xml:space="preserve">
    <value>Ja Debit Note Details</value>
  </data>
  <data name="DebitNoteNumber" xml:space="preserve">
    <value>Ja Debit Note Number</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>Ja Document Type</value>
  </data>
  <data name="SelectedInvoiceNumberisAlreadyReturned" xml:space="preserve">
    <value>Ja Internal Invoice is Already Done</value>
  </data>
  <data name="InvalidInternalInvoiceNumber" xml:space="preserve">
    <value>Ja Invalid Internal Invoice Number</value>
  </data>
  <data name="SelectedInvoiceNumberisAlreadyReturned1" xml:space="preserve">
    <value>Ja Internal Invoice is already Returned</value>
  </data>
  <data name="SupplierisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>Ja Supplier is Locked, Do you want to continue</value>
  </data>
  <data name="Suppliernotfound" xml:space="preserve">
    <value>Ja Supplier not found</value>
  </data>
  <data name="IsModelSpecific" xml:space="preserve">
    <value>Ja Is Model Specific ?</value>
  </data>
  <data name="AlreadyApproved" xml:space="preserve">
    <value>Ja Already Approved</value>
  </data>
  <data name="AlreadyClosed" xml:space="preserve">
    <value>Ja Already Closed</value>
  </data>
  <data name="AlreadyRejected" xml:space="preserve">
    <value>Ja Already Rejected</value>
  </data>
  <data name="ViewallRecords" xml:space="preserve">
    <value>Ja View all Records</value>
  </data>
  <data name="NoJobCardsforInternalInvoice" xml:space="preserve">
    <value>Ja No JobCards for Internal Invoice</value>
  </data>
  <data name="NoJobCardsforWarrantyClaim" xml:space="preserve">
    <value>Ja No JobCards for Warranty Claim</value>
  </data>
  <data name="InvalidorinactiveBranch" xml:space="preserve">
    <value>Invalid or Inactive Branch</value>
  </data>
  <data name="DuplicateChecklist" xml:space="preserve">
    <value>Ja DuplicateChecklist</value>
  </data>
  <data name="Pleaseenteronlyintegernumber" xml:space="preserve">
    <value>Ja Please enter only integer number</value>
  </data>
  <data name="Serviceduehourscannotbezero" xml:space="preserve">
    <value>Ja Service due hours cannot be zero</value>
  </data>
  <data name="DatecannotbeGreaterthen" xml:space="preserve">
    <value>Ja Date cannot be Greater then :</value>
    <comment>Message</comment>
  </data>
  <data name="Serviceduedayscannotbezero" xml:space="preserve">
    <value>Ja Service due days cannot be zero</value>
  </data>
  <data name="PartalreadyexistsinParentcompany" xml:space="preserve">
    <value>Ja Part already exists in Parent company</value>
    <comment>Message</comment>
  </data>
  <data name="AddServiceTypeHeader" xml:space="preserve">
    <value>Ja Add Service Type</value>
  </data>
  <data name="EnteredCampaignAlreadyExist" xml:space="preserve">
    <value>Ja Campaign already exist</value>
  </data>
  <data name="EventLocation" xml:space="preserve">
    <value>Ja Event Location</value>
  </data>
  <data name="Blockedquantitycannotbegreaterthanrequestedquantity" xml:space="preserve">
    <value>Ja Blocked Quantity cannot be greater than Requested Quantity</value>
  </data>
  <data name="TransactionQuantity" xml:space="preserve">
    <value>Ja TransactionQuantity</value>
  </data>
  <data name="Inactivebranch" xml:space="preserve">
    <value>Ja Inactive Branch</value>
  </data>
  <data name="CurrentPartDetails" xml:space="preserve">
    <value>Ja CurrentPartDetails</value>
  </data>
  <data name="NewPartDetails" xml:space="preserve">
    <value>Ja New Part Details</value>
  </data>
  <data name="PrecedingPartDetails" xml:space="preserve">
    <value>Ja Preceding Part Details</value>
  </data>
  <data name="supersessionexist" xml:space="preserve">
    <value>Ja Supersession Exist</value>
  </data>
  <data name="PaymentDueDateCannotbelessthanInvoicedate" xml:space="preserve">
    <value>Ja PaymentDueDateCannotbelessthanInvoicedate</value>
  </data>
  <data name="AddServiceInvoiceReturn" xml:space="preserve">
    <value>Ja Add Service Invoice Return</value>
  </data>
  <data name="PickListNumber" xml:space="preserve">
    <value>Ja Pick List #</value>
  </data>
  <data name="InternalInvoiceSearch" xml:space="preserve">
    <value>Ja Internal Invoice Search</value>
  </data>
  <data name="BillSettlementDetails" xml:space="preserve">
    <value>Ja Bill Settlement Details</value>
  </data>
  <data name="InvoiceReturnDetails" xml:space="preserve">
    <value>Ja Invoice Return Details</value>
  </data>
  <data name="ReturnInternalInvoice" xml:space="preserve">
    <value>Ja Return Internal Invoice</value>
  </data>
  <data name="ReturnInvoice" xml:space="preserve">
    <value>Ja Return Invoice</value>
  </data>
  <data name="Noproductisassociatedwithselectedparty" xml:space="preserve">
    <value>Ja No product is associated with selected party</value>
  </data>
  <data name="Totalamountcannotbezeroorblank" xml:space="preserve">
    <value>Ja Total amount cannot be zero or blank</value>
  </data>
  <data name="InvalidInsuranceParty" xml:space="preserve">
    <value>Ja Invalid insurance party</value>
  </data>
  <data name="InsurancePartyisnotactive" xml:space="preserve">
    <value>Ja Insurance party is inactive</value>
  </data>
  <data name="AddServiceInvoice" xml:space="preserve">
    <value>Ja Add Service Invoice</value>
  </data>
  <data name="ReturnQuantitycannotbegreaterthanQuantity." xml:space="preserve">
    <value>Ja Return quantity cannot be greater than quantity.</value>
  </data>
  <data name="ReadingEnteredCannotBeLessThanPreviousReading" xml:space="preserve">
    <value>Ja Reading entered cannot be less than previous reading</value>
  </data>
  <data name="InvalidTool" xml:space="preserve">
    <value>Ja Invalid tool</value>
  </data>
  <data name="DuplicateToolName" xml:space="preserve">
    <value>Ja Duplicate tool name</value>
  </data>
  <data name="movement" xml:space="preserve">
    <value>Ja Movement</value>
  </data>
  <data name="JobCardIsAlreadyDoneforQuotation" xml:space="preserve">
    <value>Ja Job Card is already done for this quotation number</value>
  </data>
  <data name="JobCardisOpenfortheselectedProduct" xml:space="preserve">
    <value>Ja Job card is open for the selected product</value>
  </data>
  <data name="OperationDetailisMandatory" xml:space="preserve">
    <value>Ja Operation detail is mandatory</value>
  </data>
  <data name="QuotationiscreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>Ja Quotation is created for this service request</value>
  </data>
  <data name="ServiceRequestIsClosed" xml:space="preserve">
    <value>Ja Service request is closed</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value />
  </data>
  <data name="SundryAmountcannotexceedCampaignSundryAmount" xml:space="preserve">
    <value>Ja Sundry amount cannot exceed campaign sundry amount</value>
  </data>
  <data name="TravelExpensecannotexceedCampaignExpenseAmount" xml:space="preserve">
    <value>Ja Travel expense cannot exceed campaign expense amount</value>
  </data>
  <data name="UsedQunatityCannotBeGreaterThanIssuedQuantity" xml:space="preserve">
    <value>Ja Used quantity cannot be greater than issued quantity</value>
  </data>
  <data name="Orderqtyshouldalwaysbegreaterthanminordqty" xml:space="preserve">
    <value>Ja Order quantity should always be greater than or equal minimum order quantity</value>
  </data>
  <data name="OrderqtyshouldbeinmultipleofstandardpackingQty" xml:space="preserve">
    <value>Ja Order quantity should be in multiple of standard packing quantity</value>
  </data>
  <data name="LeadTimeTracker" xml:space="preserve">
    <value>Ja LeadTimeTracker</value>
  </data>
  <data name="Actualstartdatecannotbelessthanplannedstartdate" xml:space="preserve">
    <value>Ja Actual start date cannot be less than planned start date</value>
  </data>
  <data name="Actualcompletiondatecannotbelessactualstartdate" xml:space="preserve">
    <value>Ja Actual end date cannot be less than actual start date</value>
  </data>
  <data name="Plannedcompletiondatecannotbelessplannedstartdate" xml:space="preserve">
    <value>Ja Planned completion date cannot be less planned start date</value>
  </data>
  <data name="FailureReadingCannotBeLessThanCurrentReading" xml:space="preserve">
    <value>Ja Failure reading cannot be less than current reading</value>
  </data>
  <data name="Repairreadingcannotbelessthanfailurereading" xml:space="preserve">
    <value>Ja Repair reading cannot be less than failure reading</value>
  </data>
  <data name="LineFullyCompletedDate" xml:space="preserve">
    <value>Ja Line Fully Completed Date</value>
  </data>
  <data name="NumberofShipment" xml:space="preserve">
    <value>Ja Number of Shipment</value>
  </data>
  <data name="POQuantity" xml:space="preserve">
    <value>Ja PO Quantity</value>
  </data>
  <data name="PurchaseInvoiceDate" xml:space="preserve">
    <value>Ja Purchase Invoice Date</value>
  </data>
  <data name="ShippedQuantity" xml:space="preserve">
    <value>Ja Shipped Quantity</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Ja UnitPrice</value>
  </data>
  <data name="QuotationisClosed" xml:space="preserve">
    <value>Ja Quotation is closed</value>
  </data>
  <data name="Quotationisalreadycreated" xml:space="preserve">
    <value>Ja Quotation is already created</value>
  </data>
  <data name="NewReading" xml:space="preserve">
    <value>Ja New Reading</value>
  </data>
  <data name="OldReading" xml:space="preserve">
    <value>Ja Old Reading</value>
  </data>
  <data name="OldReadingCannotBeSameAsNewReading" xml:space="preserve">
    <value>Ja Old reading cannot be same as new reading</value>
  </data>
  <data name="PartsQuotationArchived" xml:space="preserve">
    <value>Ja Parts Quotation Archived</value>
  </data>
  <data name="quotationdatewisesearch" xml:space="preserve">
    <value>Ja Quotation DateWise Search</value>
  </data>
  <data name="ServiceQuotationArchived" xml:space="preserve">
    <value>Ja Service Quotation Archived</value>
  </data>
  <data name="IsDefaultContactRequired" xml:space="preserve">
    <value>Ja Is Default Contact Required</value>
  </data>
  <data name="duplicatewarrantytype" xml:space="preserve">
    <value>Ja duplicate warranty type</value>
  </data>
  <data name="CSANumber" xml:space="preserve">
    <value>Ja CSA #</value>
  </data>
  <data name="DoYouWantToSelectCampaignService" xml:space="preserve">
    <value>Ja Do you want to select campaign service</value>
  </data>
  <data name="AttachmentUpload" xml:space="preserve">
    <value>Ja Attachment upload</value>
  </data>
  <data name="FileSizeShouldNotExceed2MBf" xml:space="preserve">
    <value>Ja File size should no exceed 2MB</value>
  </data>
  <data name="ServiceChargecannotbezero" xml:space="preserve">
    <value>Ja Service Charge cannot be zero</value>
  </data>
  <data name="Servicefrequencycannotbezero" xml:space="preserve">
    <value>Ja Service frequency cannot be zero</value>
  </data>
  <data name="ClaimisMandatory" xml:space="preserve">
    <value>Ja Claim is mandatory</value>
  </data>
  <data name="CreditENote" xml:space="preserve">
    <value>JaCreditENote</value>
  </data>
  <data name="DebitENote" xml:space="preserve">
    <value>JaDebitENote</value>
  </data>
  <data name="DefectGroupNotFound" xml:space="preserve">
    <value>Ja Invalid defect code is entered</value>
  </data>
  <data name="Detailcannotbeblank" xml:space="preserve">
    <value>Ja Detail cannot be blank</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>Ja Hours</value>
  </data>
  <data name="NoAmount" xml:space="preserve">
    <value>Ja No amount</value>
  </data>
  <data name="OperationDetails" xml:space="preserve">
    <value>Ja Operation Details</value>
  </data>
  <data name="PartsInvoiceReturn" xml:space="preserve">
    <value>Ja Parts Invoice Return</value>
  </data>
  <data name="ResourceTimeSheet" xml:space="preserve">
    <value>Ja Resource Time Sheet</value>
  </data>
  <data name="componentdetail" xml:space="preserve">
    <value>Ja Component Detail</value>
  </data>
  <data name="Operationquantitycannotbezero" xml:space="preserve">
    <value>Ja Operation quantity cannot be zero</value>
  </data>
  <data name="Partsquantitycannotbezero" xml:space="preserve">
    <value>Ja Parts quantity cannot be zero</value>
  </data>
  <data name="operationcodedup" xml:space="preserve">
    <value>operation code</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>part</value>
  </data>
  <data name="PartsUpload" xml:space="preserve">
    <value>Ja Parts Upload</value>
  </data>
  <data name="PartyisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>Ja Party is locked, do you want to continue?</value>
  </data>
  <data name="JobCardisOpenfortheselectedProductdoyouwanttocontinue" xml:space="preserve">
    <value>Ja Job card is open for the selected product, Do you want to continue ?</value>
  </data>
  <data name="Notabletoclosethejobcardsincereturneddateisblankingatepass" xml:space="preserve">
    <value>Ja Not able to close the job card, since returned date is blank in gate pass</value>
  </data>
  <data name="EnterTaxableOtherChargesAmount" xml:space="preserve">
    <value>Ja Enter taxable other charges amount</value>
  </data>
  <data name="RecievedAmount" xml:space="preserve">
    <value>Ja Received Amount</value>
  </data>
  <data name="Invoicesearch" xml:space="preserve">
    <value>Ja Service Invoice Search</value>
  </data>
  <data name="Servicechargecodes" xml:space="preserve">
    <value>Ja service charge code</value>
  </data>
  <data name="BOMDetail" xml:space="preserve">
    <value>Ja BOM Detail</value>
  </data>
  <data name="CorePartDetails" xml:space="preserve">
    <value>Ja Core Part Details</value>
  </data>
  <data name="CorePartNumber" xml:space="preserve">
    <value>Ja Core Part #</value>
  </data>
  <data name="CoreType" xml:space="preserve">
    <value>Ja Core Type</value>
  </data>
  <data name="InspectionChecklist" xml:space="preserve">
    <value>Ja Inspection Checklist</value>
  </data>
  <data name="OriginalPartDetails" xml:space="preserve">
    <value>Ja Original Part Details</value>
  </data>
  <data name="OriginalPartNumber" xml:space="preserve">
    <value>Ja Original Part #</value>
  </data>
  <data name="PartisAlreadyUsed" xml:space="preserve">
    <value>Ja Party is already used</value>
  </data>
  <data name="QualityChecklist" xml:space="preserve">
    <value>Ja Quality Checklist</value>
  </data>
  <data name="ReManPartDetails" xml:space="preserve">
    <value>Ja Re-Man Part Details</value>
  </data>
  <data name="ReManPartNumber" xml:space="preserve">
    <value>Ja Re-Man Part #</value>
  </data>
  <data name="DeliveryNoteNum" xml:space="preserve">
    <value>Ja Delivery Note #</value>
  </data>
  <data name="DNActualReturnDate" xml:space="preserve">
    <value>Ja Actual Return Date</value>
  </data>
  <data name="DNExpectedReturnDate" xml:space="preserve">
    <value>Ja Expected Return Date</value>
  </data>
  <data name="DNMachineDetails" xml:space="preserve">
    <value>Ja MachineDetails</value>
  </data>
  <data name="DNMobile" xml:space="preserve">
    <value>Ja Mobile #</value>
  </data>
  <data name="DNShippingAddress" xml:space="preserve">
    <value>Ja Shipping Address</value>
  </data>
  <data name="EngineSerialNum" xml:space="preserve">
    <value>Ja Engine Serial #</value>
  </data>
  <data name="MachineTransferRequest" xml:space="preserve">
    <value>Ja Machine Transfer Request</value>
    <comment>Label</comment>
  </data>
  <data name="ReturnedTo" xml:space="preserve">
    <value>Returned To</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Ja Credits</value>
  </data>
  <data name="DateOfManufacture" xml:space="preserve">
    <value>Ja Date Of Manufacture</value>
  </data>
  <data name="AvailableStock" xml:space="preserve">
    <value>Ja Available Stock</value>
  </data>
  <data name="AveragePurchasePrice" xml:space="preserve">
    <value>Ja Average Purchase Price</value>
  </data>
  <data name="AverageSellingPrice" xml:space="preserve">
    <value>Ja Average Selling Price</value>
  </data>
  <data name="Financier" xml:space="preserve">
    <value>Ja Financier</value>
    <comment>Lable</comment>
  </data>
  <data name="Variant" xml:space="preserve">
    <value>バリアント</value>
  </data>
  <data name="PriceList" xml:space="preserve">
    <value>Ja Selling Price</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Ja Price</value>
  </data>
  <data name="PartyFieldSearch" xml:space="preserve">
    <value>Ja Party Field Search</value>
  </data>
  <data name="EventStartDateAndTime" xml:space="preserve">
    <value>Ja Event Start Date And Time</value>
  </data>
  <data name="EventEndDateAndTime" xml:space="preserve">
    <value>Ja Event End Date And Time</value>
  </data>
  <data name="ProductDetailCannotbeblank" xml:space="preserve">
    <value>マシンの詳細は空白にすることはできません</value>
    <comment>Message</comment>
  </data>
  <data name="TotalProductTransferRequestamountcannotbezero" xml:space="preserve">
    <value>総機械転送要求量はゼロとすることはできません</value>
    <comment>Message</comment>
  </data>
  <data name="CoreDetails" xml:space="preserve">
    <value>Ja Core Details</value>
  </data>
  <data name="CoreReceipt" xml:space="preserve">
    <value>Ja Core Receipt</value>
  </data>
  <data name="CoreValue" xml:space="preserve">
    <value>Ja Core Value</value>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Ja Purchase</value>
  </data>
  <data name="ReBuild" xml:space="preserve">
    <value>Ja Re-Build</value>
  </data>
  <data name="ReceiptType" xml:space="preserve">
    <value>Ja Receipt Type</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>Ja Source</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Ja Stock</value>
  </data>
  <data name="Warranty" xml:space="preserve">
    <value>Ja Warranty</value>
  </data>
  <data name="WareHouseName" xml:space="preserve">
    <value>Ja WareHouse Name</value>
  </data>
  <data name="EngineSerialnumber" xml:space="preserve">
    <value>Ja Engine Serial #</value>
  </data>
  <data name="ServiceCompany" xml:space="preserve">
    <value>Ja Service Company</value>
  </data>
  <data name="InvalidBranch" xml:space="preserve">
    <value>Ja Invalid branch</value>
  </data>
  <data name="Assembling" xml:space="preserve">
    <value>Ja Assembling</value>
  </data>
  <data name="Dismantling" xml:space="preserve">
    <value>Ja Dismantling</value>
  </data>
  <data name="Mandatory" xml:space="preserve">
    <value>Ja Mandatory</value>
  </data>
  <data name="SalvagePartNumber" xml:space="preserve">
    <value>Ja Salvage Part #</value>
  </data>
  <data name="SalvagePartPrefix" xml:space="preserve">
    <value>Ja Salvage Part Prefix</value>
  </data>
  <data name="DuplicateSequence" xml:space="preserve">
    <value>Duplicate sequence</value>
  </data>
  <data name="PartalreadyexistsinDealercompany" xml:space="preserve">
    <value>Ja Part already exists in Dealer company</value>
    <comment>Message</comment>
  </data>
  <data name="CoreGRN" xml:space="preserve">
    <value>Ja Core GRN</value>
  </data>
  <data name="InspectionNumber" xml:space="preserve">
    <value>Ja Inspection #</value>
  </data>
  <data name="ProductTransferNoteNumber" xml:space="preserve">
    <value>マシン転送ノートナンバー</value>
  </data>
  <data name="IsGoodCore" xml:space="preserve">
    <value>Ja Is Good Core ?</value>
  </data>
  <data name="ReceiptDate" xml:space="preserve">
    <value>Ja Receipt Date</value>
  </data>
  <data name="TotalDeduction" xml:space="preserve">
    <value>Ja Total Deduction</value>
  </data>
  <data name="TotalGRNAmount" xml:space="preserve">
    <value>Ja Total GRN Amount</value>
  </data>
  <data name="ProductTransferNoteDate" xml:space="preserve">
    <value>マシン転送注意日</value>
  </data>
  <data name="InspectionFieldSearch" xml:space="preserve">
    <value>Ja Inspection Field Search</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>Ja Inspected By</value>
  </data>
  <data name="InvalidInspectionNumber" xml:space="preserve">
    <value>Ja Invalid inspection number</value>
  </data>
  <data name="GRNisalreadycreatedfortheselectedinspectionnumber" xml:space="preserve">
    <value>Ja GRN is already created for the selected inspection number</value>
  </data>
  <data name="EditProductTransferNote" xml:space="preserve">
    <value>製品転送注意を編集</value>
  </data>
  <data name="DuplicatePart" xml:space="preserve">
    <value>Ja Duplicate part</value>
  </data>
  <data name="SelectPartNumber" xml:space="preserve">
    <value>Ja Select part</value>
  </data>
  <data name="SerialNumberAlreadyExistsForTheSelectedPartNumber" xml:space="preserve">
    <value>Ja Serial number already exists for the selected part #</value>
  </data>
  <data name="returnquantitycannotbegreaterthaninvoicedquantity" xml:space="preserve">
    <value>Ja Return quantity cannot be greater than invoiced quantity</value>
  </data>
  <data name="CoreDetail" xml:space="preserve">
    <value>Ja Core Detail</value>
  </data>
  <data name="GatePassDetail" xml:space="preserve">
    <value>Ja Gate Pass Detail</value>
  </data>
  <data name="Salvage" xml:space="preserve">
    <value>Ja Salvage</value>
  </data>
  <data name="ReMan" xml:space="preserve">
    <value>Ja Re-Man</value>
  </data>
  <data name="ReManInvoice" xml:space="preserve">
    <value>Ja Re-Man Invoice</value>
  </data>
  <data name="ReManInternalInvoice" xml:space="preserve">
    <value>Ja Re-Man Internal Invoice</value>
  </data>
  <data name="ProductTotalAmount" xml:space="preserve">
    <value>Ja Product Total Amount</value>
  </data>
  <data name="QuotationFinalAmount" xml:space="preserve">
    <value>Ja Quotation Final Amount</value>
  </data>
  <data name="SalesQuotation" xml:space="preserve">
    <value>Ja Sales Quotation</value>
  </data>
  <data name="FollowUpDetails" xml:space="preserve">
    <value>Ja Follow Up Details</value>
  </data>
  <data name="Thisisfromcoretypemastercannotbedeleted" xml:space="preserve">
    <value>Ja This is from core type master cannot be deleted</value>
  </data>
  <data name="BOMQuantity" xml:space="preserve">
    <value>Ja BOM Quantity</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Ja Quantity Received</value>
  </data>
  <data name="QuantityReturned" xml:space="preserve">
    <value>Ja Quantity Returned</value>
  </data>
  <data name="Reusable" xml:space="preserve">
    <value>Ja Re-Usable</value>
  </data>
  <data name="Scrap" xml:space="preserve">
    <value>Ja Scrap</value>
  </data>
  <data name="AllSundryshouldbecompleted" xml:space="preserve">
    <value>Ja All sundry should be completed</value>
  </data>
  <data name="planneddate" xml:space="preserve">
    <value>Ja Planned Date</value>
  </data>
  <data name="TaskName" xml:space="preserve">
    <value>Ja Task Name</value>
  </data>
  <data name="MyTasksList" xml:space="preserve">
    <value>My Tasks List</value>
  </data>
  <data name="InvalidEstimationNumber" xml:space="preserve">
    <value>Ja Invalid estimation number</value>
  </data>
  <data name="JobCardIsAlreadyCreatedForTheSelectedEstimationNumber" xml:space="preserve">
    <value>Ja Job card is already created for the selected estimation number</value>
  </data>
  <data name="RequestCoreIsSuccessfull" xml:space="preserve">
    <value>Ja Request core is successfull</value>
  </data>
  <data name="RequestCore" xml:space="preserve">
    <value>Ja Request Core</value>
  </data>
  <data name="BEDTotal" xml:space="preserve">
    <value>Ja Total</value>
  </data>
  <data name="Bonding" xml:space="preserve">
    <value>Ja Bonding</value>
  </data>
  <data name="BondingDate" xml:space="preserve">
    <value>Ja Date</value>
  </data>
  <data name="BondingDocumentType" xml:space="preserve">
    <value>Ja Document Type</value>
  </data>
  <data name="BondingisalreadydoneforthisPINum" xml:space="preserve">
    <value>Ja Bonding is already done for this Purchase Invoice #</value>
  </data>
  <data name="BondingNum" xml:space="preserve">
    <value>Ja Bonding #</value>
  </data>
  <data name="InvalidPurchaseInvoiceNum" xml:space="preserve">
    <value>Ja Invalid Purchase Invoice #</value>
  </data>
  <data name="BillOfEntry" xml:space="preserve">
    <value>Ja Bill Of Entry</value>
  </data>
  <data name="BillOfEntryDate" xml:space="preserve">
    <value>Ja Bill Of Entry Date</value>
  </data>
  <data name="BillOfEntryNum" xml:space="preserve">
    <value>Ja Bill Of Entry #</value>
  </data>
  <data name="ClearingAgentName" xml:space="preserve">
    <value>Ja Clearing Agent Name</value>
  </data>
  <data name="PurchaseInvoiceNum" xml:space="preserve">
    <value>Ja Purchase Invoice #</value>
  </data>
  <data name="AssessableValue" xml:space="preserve">
    <value>Ja Assessable Value</value>
  </data>
  <data name="DutyAmount" xml:space="preserve">
    <value>Ja Duty Amount</value>
  </data>
  <data name="DutyStructure" xml:space="preserve">
    <value>Ja Duty Structure</value>
  </data>
  <data name="ValueinLocalCurrency" xml:space="preserve">
    <value>Ja Value in Local Currency</value>
  </data>
  <data name="DuplicateExpenditureName" xml:space="preserve">
    <value>Ja Duplicate Expenditure Name</value>
  </data>
  <data name="ExpensesTotalAmount" xml:space="preserve">
    <value>Ja Expenses Total Amount</value>
  </data>
  <data name="ExpensesTotalAmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>Ja Expenses Total Amount Is Beyond Acceptable Limit</value>
  </data>
  <data name="MachineTotalAmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>Ja Machine Total Amount Is Beyond Acceptable Limit</value>
  </data>
  <data name="TotalBillOfEntryAmount" xml:space="preserve">
    <value>Ja Total BillOfEntry Amount</value>
  </data>
  <data name="TotalBillOfEntryAmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>Ja Total BillOfEntry Amount Is Beyond Acceptable Limit</value>
  </data>
  <data name="PurchaseOrderStatus" xml:space="preserve">
    <value>Ja Purchase Order Status</value>
  </data>
  <data name="FinanceDetails" xml:space="preserve">
    <value>Ja Finance Details</value>
  </data>
  <data name="FOCDetails" xml:space="preserve">
    <value>Ja FOC Details</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Ja Article</value>
  </data>
  <data name="ProductVariantAndAllocationDetails" xml:space="preserve">
    <value>Ja Product Variant And Allocation Details</value>
  </data>
  <data name="ProductVariantDetails" xml:space="preserve">
    <value>Ja Product Variant Details</value>
  </data>
  <data name="AvailabilityStock" xml:space="preserve">
    <value>Ja Availability Stock</value>
  </data>
  <data name="DeBonding" xml:space="preserve">
    <value>Ja DeBonding #</value>
  </data>
  <data name="DeBondingDate" xml:space="preserve">
    <value>Ja DeBonding Date</value>
  </data>
  <data name="SalesHistory" xml:space="preserve">
    <value>Ja Sales History</value>
  </data>
  <data name="SellingPrice" xml:space="preserve">
    <value>Ja SellingPrice</value>
  </data>
  <data name="ItemNumber" xml:space="preserve">
    <value>Ja Item #</value>
  </data>
  <data name="DuplicateVariant" xml:space="preserve">
    <value>Ja Duplicate Variant</value>
  </data>
  <data name="SellingPriceDetails" xml:space="preserve">
    <value>Ja Selling Price Details</value>
  </data>
  <data name="SupplierPriceDetails" xml:space="preserve">
    <value>Ja Supplier Price Details</value>
  </data>
  <data name="VariantDetails" xml:space="preserve">
    <value>Ja Variant Details</value>
  </data>
  <data name="VariantName" xml:space="preserve">
    <value>Ja Variant Name</value>
  </data>
  <data name="AssessableValuecannotbezero" xml:space="preserve">
    <value>Ja Assessable Value can not be zero</value>
  </data>
  <data name="BillOfentryisalreadydoneforthisPINum" xml:space="preserve">
    <value>Ja Bill of entry is already done for this purchase invoice #</value>
  </data>
  <data name="ExchangeRatecannotbezero" xml:space="preserve">
    <value>Ja Exchange Rate can not be zero</value>
  </data>
  <data name="Valueinlocalcurrencycannotbezero" xml:space="preserve">
    <value>Ja Value in local currency can not be zero</value>
  </data>
  <data name="SelectItemType" xml:space="preserve">
    <value>Ja Select Item Type</value>
  </data>
  <data name="PreffixSuffixDoesntExistsForGDR" xml:space="preserve">
    <value>Ja Preffix suffix doesnot exists for gdr</value>
  </data>
  <data name="PleaseSelectWareHouse" xml:space="preserve">
    <value>Ja Please Select WareHouse</value>
  </data>
  <data name="InactiveClearingAgentName" xml:space="preserve">
    <value>Ja Inactive Clearing Agent Name</value>
  </data>
  <data name="InvalidClearingAgentName" xml:space="preserve">
    <value>Ja Invalid Clearing Agent Name</value>
  </data>
  <data name="DuplicateEnquiryStage" xml:space="preserve">
    <value>Ja Duplicate Enquiry Stage</value>
  </data>
  <data name="EnquiryStageName" xml:space="preserve">
    <value>Ja Enquiry Stage Name</value>
  </data>
  <data name="Probability" xml:space="preserve">
    <value>Ja Probability</value>
  </data>
  <data name="EnquiryQty" xml:space="preserve">
    <value>Ja Enquiry Quantity</value>
  </data>
  <data name="Ratio" xml:space="preserve">
    <value>Ja Ratio %</value>
  </data>
  <data name="SalesSuccessRatio" xml:space="preserve">
    <value>Ja Sales Success Ratio</value>
  </data>
  <data name="WonQty" xml:space="preserve">
    <value>Ja Won Quantity</value>
  </data>
  <data name="CurrentAssigning" xml:space="preserve">
    <value>Ja Assignee</value>
  </data>
  <data name="EnquiryStatus" xml:space="preserve">
    <value>Ja Enquiry Status</value>
  </data>
  <data name="SalesPendingEnquiries" xml:space="preserve">
    <value>Ja Sales Pending Enquiries</value>
  </data>
  <data name="QuantityCanNotBeLessThanAllocatedQuantity" xml:space="preserve">
    <value>Ja Quantity Can Not Be Less Than Allocated Quantity</value>
  </data>
  <data name="QuantityCanNotBeLessThanWonQuantity" xml:space="preserve">
    <value>Ja Quantity Can Not Be Less Than Won Quantity</value>
  </data>
  <data name="SalesQuotationDetailReport" xml:space="preserve">
    <value>Ja Sales Quotation Detail Report</value>
  </data>
  <data name="PendingSalesQuotationsReport" xml:space="preserve">
    <value>Ja Pending Sales Quotations Report</value>
  </data>
  <data name="SalesQuotationVersionDetails" xml:space="preserve">
    <value>Ja Sales Quotation Version Details</value>
  </data>
  <data name="QuotationQty" xml:space="preserve">
    <value>Ja Quotation Qty</value>
  </data>
  <data name="SalesQuotationConversionRatioDetails" xml:space="preserve">
    <value>Ja Sales Quotation Conversion Ratio Details</value>
  </data>
  <data name="PendingPurchaseOrdersForInvoiceReport" xml:space="preserve">
    <value>Ja Pending Purchase Orders For Invoice Report</value>
  </data>
  <data name="PurchaseInvoiceDetailReport" xml:space="preserve">
    <value>Ja Purchase Invoice Detail Report</value>
  </data>
  <data name="BondingDetailReport" xml:space="preserve">
    <value>Ja Bonding Detail Report</value>
  </data>
  <data name="TotalExpenseAmount" xml:space="preserve">
    <value>Ja Total Expense Amount</value>
  </data>
  <data name="BillOfEntryDetailReport" xml:space="preserve">
    <value>Ja Bill Of Entry Detail Report</value>
  </data>
  <data name="PendingPIForBOE" xml:space="preserve">
    <value>Ja Pending Purchase Invoice For Bill Of Entry Report</value>
  </data>
  <data name="ClearingAgentisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>Ja Clearing Agent is Locked Do you want to continue</value>
  </data>
  <data name="TotalBOEAmount" xml:space="preserve">
    <value>Ja Total Bill Of Entry Amount</value>
  </data>
  <data name="ProductTransferRequestDetailReport" xml:space="preserve">
    <value>Ja Product Transfer Request Detail Report</value>
  </data>
  <data name="PendingProductTransferRequestForProductTransferNote" xml:space="preserve">
    <value>Ja Pending Product Transfer Request For Product Transfer Note</value>
  </data>
  <data name="ProductTransferNoteDetailReport" xml:space="preserve">
    <value>Ja Product Transfer Note Detail Report</value>
  </data>
  <data name="RequestingBranchWareHouse" xml:space="preserve">
    <value>Ja Requesting Branch Ware House</value>
  </data>
  <data name="SupplyingBranchWareHouse" xml:space="preserve">
    <value>Ja Supplying Branch Ware House</value>
  </data>
  <data name="PendingProductTransferNoteForProductTransferGRN" xml:space="preserve">
    <value>Ja Pending Product Transfer Note For Product Transfer GRN</value>
  </data>
  <data name="ProductTransferGRNDetailReport" xml:space="preserve">
    <value>Ja Product Transfer GRN Detail Report</value>
  </data>
  <data name="TotalAccessableAmount" xml:space="preserve">
    <value>Ja Total Assessable Amount</value>
  </data>
  <data name="TotalDutyAmount" xml:space="preserve">
    <value>Ja Total Duty Amount</value>
  </data>
  <data name="PendingPIForPGRN" xml:space="preserve">
    <value>Ja Pending Purchase Invoice For Purchase GRN Report</value>
  </data>
  <data name="PurchaseGRNDetailReport" xml:space="preserve">
    <value>Ja Purchase GRN Detail Report</value>
  </data>
  <data name="TotalLandingCost" xml:space="preserve">
    <value>Ja Total Landing Cost</value>
  </data>
  <data name="GoodsReleaseNoteDetailReport" xml:space="preserve">
    <value>Ja Goods Release Note Detail Report</value>
  </data>
  <data name="PendingPIForGoodsReleaseNote" xml:space="preserve">
    <value>Ja PendingPurchase Invoice For Goods Release Note</value>
  </data>
  <data name="ActiveQtyShouldBeLessThanOrEqualToQuantity" xml:space="preserve">
    <value>JA Active quantity should be less than or equal to Quantity</value>
  </data>
  <data name="ActiveQuantityCanNotBeLessThanAllocatedQuantity" xml:space="preserve">
    <value>JA Active quantity can not be less than Allocated quantity</value>
  </data>
  <data name="ActiveQtyCanNotBeBlank" xml:space="preserve">
    <value>JA Active quantity can not be blank</value>
  </data>
  <data name="LCAmountCanNotBeZero" xml:space="preserve">
    <value>JA LC amount can not be zero</value>
  </data>
  <data name="DuplicateRecords" xml:space="preserve">
    <value>Ja Duplicate Records</value>
  </data>
  <data name="VariantDetailIsMandatory" xml:space="preserve">
    <value>Ja Variant detail is mandatory</value>
  </data>
  <data name="ExchangeRatecannotbeblank" xml:space="preserve">
    <value>Ja Exchange rate can not be blank </value>
  </data>
  <data name="Amountcannotbezero" xml:space="preserve">
    <value>Ja Amount can not be zero</value>
  </data>
  <data name="DeliveryNoteNumberFieldSearch" xml:space="preserve">
    <value>Ja Delivery note # field search</value>
  </data>
  <data name="ProductSalesInvoiceNumberFieldSearch" xml:space="preserve">
    <value>Ja Product sales invoice # field search</value>
  </data>
  <data name="ProductTransferNoteNumberFieldSearch" xml:space="preserve">
    <value>Ja Product transfer note # field search</value>
  </data>
  <data name="InactiveTransporterName" xml:space="preserve">
    <value>Ja Inactive transporter name</value>
  </data>
  <data name="InvalidTransporterName" xml:space="preserve">
    <value>Ja Invalid transporter name</value>
  </data>
  <data name="AddBillOfEntry" xml:space="preserve">
    <value>Ja Add Bill Of Entry</value>
  </data>
  <data name="AddBonding" xml:space="preserve">
    <value>Ja Add Bonding</value>
  </data>
  <data name="AddDeliveryNote" xml:space="preserve">
    <value>Ja Add Delivery Note</value>
  </data>
  <data name="editBillOfEntry" xml:space="preserve">
    <value>Ja Edit Bill Of Entry</value>
  </data>
  <data name="editBonding" xml:space="preserve">
    <value>Ja Edit Bonding</value>
  </data>
  <data name="editDeliveryNote" xml:space="preserve">
    <value>Ja Edit Delivery Note</value>
  </data>
  <data name="CommissionDate" xml:space="preserve">
    <value>Ja Commission Date</value>
  </data>
  <data name="WarrantyEndDate" xml:space="preserve">
    <value>Ja Warranty End Date</value>
  </data>
  <data name="SpnGITstock" xml:space="preserve">
    <value>Ja GIT Stock</value>
  </data>
  <data name="SpnOutofWH" xml:space="preserve">
    <value>Ja Out of Warehouse</value>
  </data>
  <data name="SpnUnderClearence" xml:space="preserve">
    <value>Ja Under Clearance</value>
  </data>
  <data name="CancelledBy" xml:space="preserve">
    <value>Ja Cancelled By</value>
  </data>
  <data name="POCDate" xml:space="preserve">
    <value>Ja Purchase Order Cancellation Date</value>
  </data>
  <data name="POCNumber" xml:space="preserve">
    <value>Ja Purchase Order Cancellation #</value>
  </data>
  <data name="PurchaseOrderAmount" xml:space="preserve">
    <value>Ja Purchase Order Amount</value>
  </data>
  <data name="CancellationReason" xml:space="preserve">
    <value>Ja Cancellation Reason</value>
  </data>
  <data name="PurchaseOrderCancellation" xml:space="preserve">
    <value>Ja Purchase Order Cancellation</value>
  </data>
  <data name="TotalPurchaseOrderValue" xml:space="preserve">
    <value>Ja Total Purchase Order Value</value>
  </data>
  <data name="AddConsigneeAddress" xml:space="preserve">
    <value>Ja Add Consignee Address</value>
  </data>
  <data name="FailedtosavenewPartyAddress" xml:space="preserve">
    <value>Ja Failed to save new party address</value>
  </data>
  <data name="InactiveSupplierName" xml:space="preserve">
    <value>Ja Inactive supplier name</value>
  </data>
  <data name="InvalidSupplierName" xml:space="preserve">
    <value>Ja Invalid supplier name</value>
  </data>
  <data name="Servicerequestcannotbeclosedduetopendingfollowupdetails" xml:space="preserve">
    <value>Ja Service request can not be closed due to pending followup details</value>
  </data>
  <data name="Bill" xml:space="preserve">
    <value>Ja Bill</value>
  </data>
  <data name="partnumORnostock" xml:space="preserve">
    <value>Ja part # / not in stock</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>Time Zone</value>
  </data>
  <data name="DeviationAdjusted" xml:space="preserve">
    <value>偏差調整済み</value>
  </data>
  <data name="ListOfParts" xml:space="preserve">
    <value>ja_List of Parts has Split Quantity (Customer Paid or Warranty Claim or Internal Invoice)</value>
  </data>
  <data name="MnualQuantity" xml:space="preserve">
    <value>ja_Part has Split Quantity, Kinldy do Manual Quantity Split Update in Work Order, Used Quantity will be Updated as 0(zero)</value>
  </data>
  <data name="PartsHasSplitQuantity" xml:space="preserve">
    <value>ja_Parts has Split Quanity</value>
  </data>
  <data name="ServiceInvoiceQty" xml:space="preserve">
    <value>ja_Service Invoice Qty</value>
  </data>
  <data name="InternalInvoiceQty" xml:space="preserve">
    <value>ja_Internal Invoice Qty</value>
  </data>
  <data name="WarrantyClaimQty" xml:space="preserve">
    <value>ja_Warranty Claim Qty</value>
  </data>
  <data name="JobCardPartsReturn1" xml:space="preserve">
    <value>ja_WorkOrderPartsReturn</value>
  </data>
  <data name="MnualQty1" xml:space="preserve">
    <value>ja_Kinldy do Manual Quantity Split Update in Work Order, Used Quantity will be Updated as 0(zero)</value>
  </data>
  <data name="WorkOrderDate" xml:space="preserve">
    <value>ja_WorkOrderDate</value>
  </data>
</root>