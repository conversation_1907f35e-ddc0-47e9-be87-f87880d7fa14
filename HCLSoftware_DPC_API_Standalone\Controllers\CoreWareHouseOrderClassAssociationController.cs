﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreWareHouseOrderClassAssociationServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreWareHouseOrderClassAssociationController : ApiController
    {

        #region ::: LoadBranchDropdown Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// to Load Object Dropdowns
        /// </summary>
        /// 
        [Route("api/CoreWareHouseOrderClassAssociation/LoadBranchDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBranchDropdown([FromBody] WareHouseOrderLoadBranchDropdownList WareHouseOrderLoadBranchDropdownobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWareHouseOrderClassAssociationServices.LoadBranchDropdown(connString, WareHouseOrderLoadBranchDropdownobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To select All Log Report Data
        /// </summary>
        ///
        [Route("api/CoreWareHouseOrderClassAssociation/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectWareHouseOrderList SelectWareHouseOrderobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreWareHouseOrderClassAssociationServices.Select(connstring, SelectWareHouseOrderobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Insert and Update WareHouse
        /// </summary>
        /// 
        [Route("api/CoreWareHouseOrderClassAssociation/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertWareHouseOrderList InsertWareHouseOrderobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWareHouseOrderClassAssociationServices.Insert(connString, InsertWareHouseOrderobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Delete WareHouse
        /// </summary>
        /// 
        [Route("api/CoreWareHouseOrderClassAssociation/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteWareHouseOrderList DeleteWareHouseOrderobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWareHouseOrderClassAssociationServices.Delete(connString, DeleteWareHouseOrderobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: WareHouseOrderClassExits Uday Kumar J B 30-09-2024 I think not Used:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>    
        /// 
        [Route("api/CoreWareHouseOrderClassAssociation/WareHouseOrderClassExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WareHouseOrderClassExits([FromBody] WareHouseOrderClassExitsList WareHouseOrderClassExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWareHouseOrderClassAssociationServices.WareHouseOrderClassExits(connString, WareHouseOrderClassExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: CheckWareHouseOrderClassAssocaition Uday Kumar J B 30-09-2024 :::
        /// <summary>
        /// to Check WareHouse OrderClass Assocaition already exists
        /// </summary>
        /// 
        [Route("api/CoreWareHouseOrderClassAssociation/CheckWareHouseOrderClassAssocaition")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckWareHouseOrderClassAssocaition([FromBody] CheckWareHouseOrderClassAssocaitionList CheckWareHouseOrderClassAssocaitionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWareHouseOrderClassAssociationServices.CheckWareHouseOrderClassAssocaition(connString, CheckWareHouseOrderClassAssocaitionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Export Uday Kumar J B 30-09-2024 :::
        /// <summary>
        /// To Export 
        /// </summary>
        [Route("api/CoreWareHouseOrderClassAssociation/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportWareHouseOrderList ExportWareHouseOrderobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportWareHouseOrderobj.sidx;
            string sord = ExportWareHouseOrderobj.sord;
            string filter = ExportWareHouseOrderobj.filter;
            string advnceFilter = ExportWareHouseOrderobj.advanceFilter;

            try
            {


                object Response = await CoreWareHouseOrderClassAssociationServices.Export(ExportWareHouseOrderobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


    }
}