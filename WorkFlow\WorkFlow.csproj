﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{611E356A-C847-47A8-BE7C-49745491783E}</ProjectGuid>
    <ProjectTypeGuids>{E3E379DF-F4C6-4180-9B81-6769533ABE47};{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WorkFlow</RootNamespace>
    <AssemblyName>WorkFlow</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
    <UseGlobalApplicationHostFile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework">
      <HintPath>$(dll_base_path)\open-source\EntityFramework\*******\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp">
      <HintPath>$(dll_base_path)\open-source\itextsharp\*******\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="LogSheetExporter">
      <HintPath>$(dll_base_path)\in-house\LogSheetExporter\1.0.0.0\LogSheetExporter.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>$(dll_base_path)\open-source\Newtonsoft.Json\4.5.0.0\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.4.0.20710.0\lib\net40\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.4.0.20710.0\lib\net40\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.4.0.20710.0\lib\net40\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.4.0.20710.0\lib\net40\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.2.0.20710.0\lib\net40\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_GlobalResources\Resource_en.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource_en.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Resource_ja.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource_ja.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Resource_zh.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource_zh.resx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\CoreWFLoginController.cs" />
    <Compile Include="Controllers\CoreWFLogOutController.cs" />
    <Compile Include="Controllers\CoreWFMainController.cs" />
    <Compile Include="Controllers\CoreWorkFlowController.cs" />
    <Compile Include="Controllers\CoreWorkFlowEscalationController.cs" />
    <Compile Include="Controllers\WFHomeController.cs" />
    <Compile Include="Controllers\WorkFlowAPIController.cs" />
    <Compile Include="Models\CaseProgressObjects.cs" />
    <Compile Include="Models\Common.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\DynamicMenu.cs" />
    <Compile Include="Models\ExtensionMethods.cs" />
    <Compile Include="Models\GNM_WFEscalation.cs">
      <DependentUpon>WorkFlowEscaltionModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_WFEscalationHistory.cs">
      <DependentUpon>WorkFlowEscaltionModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_WorkFlowParent.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WFGeneral.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WFGeneral.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WFGeneral.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WFGeneral.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WFGeneral.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Branch.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Company.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_CompanyEmployee.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Email.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_EmployeeBranch.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Menu.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_MenuLocale.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Module.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_ModuleLocale.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Object.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_PartyBranchAssociation.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_PrefixSuffix.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_RefMasterDetail.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Role.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_RoleObject.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_Sms.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_User.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_UserLocale.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_UserRole.cs">
      <DependentUpon>WFGeneral.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFAction.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFActionLocale.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFCase_Progress.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFChildActions.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFField.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFFieldValue.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFRole.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFRoleLocale.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFRoleUser.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFStepLink.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFSteps.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFStepsLocale.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFStepStatus.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFStepStatusLocale.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WFStepType.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WF_WorkFlow.cs">
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlow.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WorkFlow.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlow.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WorkFlow.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlow.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WorkFlow.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlowEscaltionModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WorkFlowEscaltionModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlowEscaltionModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WorkFlowEscaltionModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlowEscaltionModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>WorkFlowEscaltionModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\WorkFlowObjects.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utilities\Utilities.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Content Include="Content\Cancel.gif" />
    <Content Include="Content\edit.gif" />
    <Content Include="Content\images\ajax-loader.gif" />
    <Content Include="Content\images\attach_user.jpg" />
    <Content Include="Content\images\Background.jpg" />
    <Content Include="Content\images\BlueButton.jpg" />
    <Content Include="Content\images\button_0.jpg" />
    <Content Include="Content\images\Cancel.gif" />
    <Content Include="Content\images\Company.jpg" />
    <Content Include="Content\images\define_workflow.jpg" />
    <Content Include="Content\images\edit.gif" />
    <Content Include="Content\images\file.gif" />
    <Content Include="Content\images\folder-closed.gif" />
    <Content Include="Content\images\folder.gif" />
    <Content Include="Content\images\Home.jpg" />
    <Content Include="Content\images\icon_clock_1.gif" />
    <Content Include="Content\images\icon_clock_2.gif" />
    <Content Include="Content\images\ic_Delete_Grid.gif" />
    <Content Include="Content\images\local.png" />
    <Content Include="Content\images\Logo.jpg" />
    <Content Include="Content\images\Logout.jpg" />
    <Content Include="Content\images\MenuBackground.jpg" />
    <Content Include="Content\images\minus.gif" />
    <Content Include="Content\images\plus.gif" />
    <Content Include="Content\images\Quest Logo.png" />
    <Content Include="Content\images\save.gif" />
    <Content Include="Content\images\SearchLens.jpg" />
    <Content Include="Content\images\subtitlebar.jpg" />
    <Content Include="Content\images\Support.jpg" />
    <Content Include="Content\images\treeview-black-line.gif" />
    <Content Include="Content\images\treeview-black.gif" />
    <Content Include="Content\images\treeview-default-line.gif" />
    <Content Include="Content\images\treeview-default.gif" />
    <Content Include="Content\images\treeview-famfamfam-line.gif" />
    <Content Include="Content\images\treeview-famfamfam.gif" />
    <Content Include="Content\images\treeview-gray-line.gif" />
    <Content Include="Content\images\treeview-gray.gif" />
    <Content Include="Content\images\treeview-red-line.gif" />
    <Content Include="Content\images\treeview-red.gif" />
    <Content Include="Content\local.png" />
    <Content Include="Content\Minus.gif" />
    <Content Include="Content\Plus.gif" />
    <Content Include="Content\SearchLens.jpg" />
    <Content Include="Content\Site.css" />
    <Content Include="Content\Themes\Blue\Blue.css" />
    <Content Include="Content\Themes\Blue\images\aas.png" />
    <Content Include="Content\Themes\Blue\images\bg.png" />
    <Content Include="Content\Themes\Blue\images\Home.png" />
    <Content Include="Content\Themes\Blue\images\Logout.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_diagonals-small_25_c5ddfc_40x40.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_diagonals-thick_90_eeeeee_40x40.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_flat_15_cd0a0a_40x100.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_glass_100_e4f1fb_1x400.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_glass_50_3baae3_1x400.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_glass_80_d7ebf9_1x400.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_highlight-hard_100_f2f5f7_1x100.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_highlight-hard_70_000000_1x100.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_highlight-soft_100_deedf7_1x100.png" />
    <Content Include="Content\Themes\Blue\images\ui-bg_highlight-soft_25_ffef8f_1x100.png" />
    <Content Include="Content\Themes\Blue\images\ui-icons_2694e8_256x240.png" />
    <Content Include="Content\Themes\Blue\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Content\Themes\Blue\images\ui-icons_3d80b3_256x240.png" />
    <Content Include="Content\Themes\Blue\images\ui-icons_72a7cf_256x240.png" />
    <Content Include="Content\Themes\Blue\images\ui-icons_ffffff_256x240.png" />
    <Content Include="Content\ui.jqgrid.css" />
    <Content Include="Global.asax" />
    <Content Include="images\216-h.png" />
    <Content Include="images\350.png" />
    <Content Include="images\400.png" />
    <Content Include="images\accept.png" />
    <Content Include="images\ajax-loader.gif" />
    <Content Include="images\bestbrowser.png" />
    <Content Include="images\bg.png" />
    <Content Include="images\Blue-loginbord.png" />
    <Content Include="images\Blue-thembottonlcenter.png" />
    <Content Include="images\Blue-thembottonleft.png" />
    <Content Include="images\Blue-thembottonlright.png" />
    <Content Include="images\BlueButton.jpg" />
    <Content Include="images\BlueGlass.png" />
    <Content Include="images\blue_full-d.jpg" />
    <Content Include="images\blue_full-d.png" />
    <Content Include="images\border-line.png" />
    <Content Include="images\Bottom--arrow-icon.png" />
    <Content Include="images\Cancel.gif" />
    <Content Include="images\Collapse.jpg" />
    <Content Include="images\Collapse.png" />
    <Content Include="images\Company.jpg" />
    <Content Include="images\Companylogo.png" />
    <Content Include="images\CORE.png" />
    <Content Include="images\cross.png" />
    <Content Include="images\Downloads-images.png" />
    <Content Include="images\edit.gif" />
    <Content Include="images\Error.jpg" />
    <Content Include="images\Expand.jpg" />
    <Content Include="images\Expand.png" />
    <Content Include="images\favicon.ico" />
    <Content Include="images\file.gif" />
    <Content Include="images\folder-closed.gif" />
    <Content Include="images\folder.gif" />
    <Content Include="images\FSM.png" />
    <Content Include="images\home.ico" />
    <Content Include="images\home.png" />
    <Content Include="images\homered.png" />
    <Content Include="images\ic_Calendar.gif" />
    <Content Include="images\Ic_Collapse--left.png" />
    <Content Include="images\Ic_Expand-right.png" />
    <Content Include="images\Ic_First.gif" />
    <Content Include="images\Ic_First.png" />
    <Content Include="images\Ic_Last.gif" />
    <Content Include="images\Ic_Last.png" />
    <Content Include="images\Ic_Next.gif" />
    <Content Include="images\Ic_Next.png" />
    <Content Include="images\Ic_Prev.gif" />
    <Content Include="images\Ic_Prev.png" />
    <Content Include="images\Keyman.png" />
    <Content Include="images\left-line.png" />
    <Content Include="images\localize.jpg" />
    <Content Include="images\login-box-backg.png" />
    <Content Include="images\login-btn.png" />
    <Content Include="images\login-icon.png" />
    <Content Include="images\login-sprite.png" />
    <Content Include="images\LoginBack.jpg" />
    <Content Include="images\Logo-4.png" />
    <Content Include="images\Logo.jpg" />
    <Content Include="images\Logo.png" />
    <Content Include="images\Logout.jpg" />
    <Content Include="images\Logout.png" />
    <Content Include="images\Logoutred.png" />
    <Content Include="images\max.png" />
    <Content Include="images\MenuBackground.jpg" />
    <Content Include="images\min.png" />
    <Content Include="images\minus.gif" />
    <Content Include="images\New-color-blue-big-login-1.png" />
    <Content Include="images\New-login-2.png" />
    <Content Include="images\New-login.png" />
    <Content Include="images\New-loginbutton.png" />
    <Content Include="images\Parts.jpg" />
    <Content Include="images\phone.png" />
    <Content Include="images\phonered.png" />
    <Content Include="images\pic_chrome.png" />
    <Content Include="images\pic_firefox.png" />
    <Content Include="images\pic_ie.png" />
    <Content Include="images\pic_safari.png" />
    <Content Include="images\plus.gif" />
    <Content Include="images\Printer.png" />
    <Content Include="images\productlogo.png" />
    <Content Include="images\Quest Logo.png" />
    <Content Include="images\Quest-logo-185-67-png.png" />
    <Content Include="images\RedButton.jpg" />
    <Content Include="images\right-line.png" />
    <Content Include="images\SearchLens.jpg" />
    <Content Include="images\SessionExpired.png" />
    <Content Include="images\sub.png" />
    <Content Include="images\SubtitleBacjGround.jpg" />
    <Content Include="images\subtitlebar.jpg" />
    <Content Include="images\Support.jpg" />
    <Content Include="images\Top--arrow-icon.png" />
    <Content Include="images\treeview-black-line.gif" />
    <Content Include="images\treeview-black.gif" />
    <Content Include="images\treeview-default-line.gif" />
    <Content Include="images\treeview-default.gif" />
    <Content Include="images\treeview-famfamfam-line.gif" />
    <Content Include="images\treeview-famfamfam.gif" />
    <Content Include="images\treeview-gray-line.gif" />
    <Content Include="images\treeview-gray.gif" />
    <Content Include="images\treeview-red-line.gif" />
    <Content Include="images\treeview-red.gif" />
    <Content Include="images\ui-bg_glass_80_d7ebf9_1x400.png" />
    <Content Include="images\white.png" />
    <Content Include="Scripts\Common\Common.js" />
    <Content Include="Scripts\Common\FieldSearch.js" />
    <Content Include="Scripts\JQGrid\grid.locale-en.js" />
    <Content Include="Scripts\JQGrid\jquery.jqGrid.min.js" />
    <Content Include="Scripts\JQuery\DateTimePicker.js" />
    <Content Include="Scripts\JQuery\fullcalendar.js" />
    <Content Include="Scripts\JQuery\jquery-1.8.2.js" />
    <Content Include="Scripts\JQuery\jquery-ui-1.9.0.custom.min.js" />
    <Content Include="Scripts\JQuery\jquery.nestedAccordion.js" />
    <Content Include="Scripts\JQuery\jquery.treeview.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Models\WorkFlow.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>WorkFlow.Designer.cs</LastGenOutput>
    </EntityDeploy>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Models\WorkFlow.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>WorkFlow.edmx</DependentUpon>
      <LastGenOutput>WorkFlow.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Models\WorkFlow.edmx.diagram">
      <DependentUpon>WorkFlow.edmx</DependentUpon>
    </Content>
    <Content Include="Models\WorkFlow.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>WorkFlow.edmx</DependentUpon>
      <LastGenOutput>WorkFlow.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Core\CoreWorkFlow.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\Resource_en.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Resource_en.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\Resource_ja.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Resource_ja.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\Resource_zh.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Resource_zh.designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\_Layout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Core\CoreLoginView.cshtml" />
    <Content Include="Views\Core\CoreMainView.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Core\LogOutView.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\HomePage.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\SessionExpire.cshtml" />
    <Content Include="Views\Shared\SessionRunning.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Models\WFGeneral.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>WFGeneral.Designer.cs</LastGenOutput>
    </EntityDeploy>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Models\WFGeneral.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>WFGeneral.edmx</DependentUpon>
      <LastGenOutput>WFGeneral.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Models\WFGeneral.edmx.diagram">
      <DependentUpon>WFGeneral.edmx</DependentUpon>
    </Content>
    <Content Include="Models\WFGeneral.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>WFGeneral.edmx</DependentUpon>
      <LastGenOutput>WFGeneral.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Models\WorkFlowEscaltionModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>WorkFlowEscaltionModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Models\WorkFlowEscaltionModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>WorkFlowEscaltionModel.edmx</DependentUpon>
      <LastGenOutput>WorkFlowEscaltionModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Models\WorkFlowEscaltionModel.edmx.diagram">
      <DependentUpon>WorkFlowEscaltionModel.edmx</DependentUpon>
    </Content>
    <Content Include="Models\WorkFlowEscaltionModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>WorkFlowEscaltionModel.edmx</DependentUpon>
      <LastGenOutput>WorkFlowEscaltionModel.cs</LastGenOutput>
    </Content>
    <None Include="Properties\PublishProfiles\WF.pubxml" />
    <None Include="Properties\PublishProfiles\Workflow.pubxml" />
    <Content Include="Views\Core\CoreWorkFlowEscalationView.cshtml" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:59743/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>