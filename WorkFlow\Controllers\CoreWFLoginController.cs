﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json.Linq;
using WorkFlow.Models;
using System.Configuration;
using System.Net;
using LS = LogSheetExporter;
using WorkFlow.Utilities;


namespace Demo.Controllers
{
    public class CoreWFLoginController : Controller
    {
        //
        // GET: /Login/
        WorkFlowEntity wrkFlowEnt = null;
        GenEntities genEnt = null;
      
        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
        string DbName = ConfigurationManager.AppSettings.Get("DbName");
        public ActionResult CoreLoginView()
        {
            return View("~/Views/Core/CoreLoginView.cshtml");
        }

        public ActionResult LoginAuthenticate()
        {

            wrkFlowEnt = new WorkFlowEntity(DbName);
            genEnt = new GenEntities(DbName);

            Utilities uti = new Utilities();
            WF_User gu = null;
            JTokenReader jtr = null;
            string userid = string.Empty;
            string password = string.Empty;
            string Branch_ID = string.Empty;
            int CompanyThemeID = 0;
            JObject jobj = JObject.Parse(Request.Params["LoginDetails"]);
            dynamic r = null;
            var Response = r;
            try
            {
                jtr = new JTokenReader(jobj["UserId"]);
                jtr.Read();
                userid = jtr.Value.ToString();

                jtr = new JTokenReader(jobj["Password"]);
                jtr.Read();

                password = jtr.Value.ToString();
                password = uti.CalculateMD5Hash(password);

                jtr = new JTokenReader(jobj["Branch"]);
                jtr.Read();

                Branch_ID = jtr.Value.ToString();

                gu = genEnt.WF_User.Where(uid => uid.User_LoginID.ToLower() == userid.ToLower() && uid.User_Password == password).FirstOrDefault();
                jobj.RemoveAll();
                if (gu == null)
                {
                    Response = new { Result = "Fail" };
                }
                else
                {
                    Response = new
                    {
                        Result = "Success",
                        UserID = gu.User_ID,
                        IsLock = gu.User_Locked,
                        IsActive = gu.User_IsActive,
                        UName = gu.User_Name

                    };
                    Session["IsNewSession"] = true;
                    Session["User_ID"] = gu.User_ID;
                    Session["User_Name"] = gu.User_Name;
                    Session["UserDetails"] = gu;
                    Session["LanguageID"] = gu.Language_ID;
                    Session["Company_ID"] = gu.Company_ID;
                    Session["Branch"] = Branch_ID;

                    Session["DefaultGridSize"] = genEnt.WF_Company.Where(a => a.Company_ID == gu.Company_ID).FirstOrDefault().DefaultGridSize;

                    CompanyThemeID = Convert.ToInt32(genEnt.WF_Company.Where(a => a.Company_ID == gu.Company_ID).FirstOrDefault().CompanyTheme_ID);
                    Session["CompanyTheme"] = genEnt.WF_RefMasterDetail.Where(a => a.RefMasterDetail_ID == CompanyThemeID).Select(a => a.RefMasterDetail_Name).FirstOrDefault();
                    int LanguageID = gu.Language_ID;
                    int BranchID = Convert.ToInt32(Branch_ID);

                    int ModuleID = genEnt.WF_Module.OrderBy(a => a.Module_ID).FirstOrDefault().Module_ID;
                    WFCommon.BuildACLObject();
                    WFCommon.BuildMenu(ModuleID);
                }


            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                
                return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                   
                }
                return RedirectToAction("Error");
            }

            return Json(Response, JsonRequestBehavior.AllowGet);
        }

        public ActionResult BindBranchDropDown(string UserName)
        {
            genEnt = new GenEntities(DbName);
            IEnumerable<WF_Branch> gBranch = null;
            IEnumerable<WF_EmployeeBranch> gEmployeeBranch = null;
            IEnumerable<WF_PartyBranchAssociation> PartyBranch = null;

            WF_User gUser = null; 
            dynamic s = null; ;
            var BranchArray = s;
            try{
                gUser = genEnt.WF_User.Where(u => u.User_LoginID.ToLower() == UserName.ToLower() && u.User_IsActive == true).FirstOrDefault();
                if (gUser != null)
                {
                    gBranch = genEnt.WF_Branch.Where(b => b.Branch_Active == true);
                    if (gUser.User_Type_ID == 1)
                    {
                        gEmployeeBranch = genEnt.WF_EmployeeBranch.Where(e => e.CompanyEmployee_ID == gUser.Employee_ID);
                        BranchArray = from a in gEmployeeBranch
                                      join b in gBranch on a.Branch_ID equals b.Branch_ID
                                      select new
                                      {
                                          b.Branch_ID,
                                          b.Branch_Name
                                      };
                    }
                    else
                    {
                        PartyBranch = genEnt.WF_PartyBranchAssociation.Where(p => p.Party_ID == gUser.Partner_ID);

                        BranchArray = from a in PartyBranch
                                      join b in gBranch on a.Branch_ID equals b.Branch_ID
                                      select new
                                      {
                                          b.Branch_ID,
                                          b.Branch_Name
                                      };
                    }


                }

           
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                   // 
                }
                return RedirectToAction("Error");
            }
            return Json(BranchArray, JsonRequestBehavior.AllowGet);
        }
    }
}
