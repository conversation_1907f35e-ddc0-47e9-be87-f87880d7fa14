﻿//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class WorkFlowEscalationEntities : DbContext
    {
        public WorkFlowEscalationEntities(string Dbname)
            : base(WFCommon.GetConnection(Dbname, "WorkFlowEscaltionModel").ConnectionString)
        {
            this.Configuration.LazyLoadingEnabled = true;
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public DbSet<GNM_WFEscalation> GNM_WFEscalation { get; set; }
        public DbSet<GNM_WFEscalationHistory> GNM_WFEscalationHistory { get; set; }
    }
}
