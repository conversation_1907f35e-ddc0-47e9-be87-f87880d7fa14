//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_Branch
    {
        public WF_Branch()
        {
            this.GNM_PartyBranchAssociation = new HashSet<WF_PartyBranchAssociation>();
            this.GNM_PrefixSuffix = new HashSet<WF_PrefixSuffix>();
            this.GNM_EmployeeBranch = new HashSet<WF_EmployeeBranch>();
        }
    
        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_ShortName { get; set; }
        public string Branch_ZipCode { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Branch_Phone { get; set; }
        public string Branch_Fax { get; set; }
        public bool Branch_HeadOffice { get; set; }
        public bool Branch_Active { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_Email { get; set; }
        public string Branch_Mobile { get; set; }
        public Nullable<bool> Branch_External { get; set; }
        public Nullable<int> TimeZoneID { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public Nullable<byte> IsOverTimeDWM { get; set; }
        public Nullable<decimal> Yearly_Sales_Target { get; set; }
        public Nullable<decimal> Rework_Target { get; set; }
        public Nullable<decimal> Cust_Satisfaction_Target { get; set; }
        public Nullable<decimal> RO_with_Rework_Target { get; set; }
        public Nullable<decimal> RO_with_Cust_Satisfaction_Target { get; set; }
        public Nullable<int> DueDays { get; set; }
    
        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual ICollection<WF_PartyBranchAssociation> GNM_PartyBranchAssociation { get; set; }
        public virtual ICollection<WF_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<WF_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail1 { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail2 { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail21 { get; set; }
    }
}
