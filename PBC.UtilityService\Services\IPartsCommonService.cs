using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Services
{
    public interface IPartsCommonService
    {
        /// <summary>
        /// Save BL Parts Invoice
        /// </summary>
        /// <param name="obj">Save BL Parts Invoice List object</param>
        /// <param name="connString">Connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <param name="jsonObj">JSON object list</param>
        /// <returns>Action result with operation status</returns>
        Task<IActionResult> SaveBLAsync(Save_BLPartsInvoiceList obj, string connString, int logException, List<dynamic> jsonObj);
    }
}
