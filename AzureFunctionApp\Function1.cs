using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SharedAPIClassLibrary_DC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;


namespace AzureFunctionApp
{
    public static class Function1
    {
        

    [FunctionName("JWTGenOnLogin_AFIMP")]
    public static async Task<IActionResult> Run(
    [HttpTrigger(AuthorizationLevel.Function, "post", Route = null)] Stream req,
    ILogger log)
        {
            try
            {
                using (StreamReader reader = new StreamReader(req))
                {
                    string json = await reader.ReadToEndAsync();
                    var model = JsonConvert.DeserializeObject<UserLoginModel>(json);


                    //var connectionString = configuration["DCAPIKEY"];
                    // Use JwtService to generate the token

                    // Initialize JwtService with the secret key
                    var jwtService = new JwtService("CwF5uSoYuSGhpVz9xKQovBZWjJorr");

                    // Use JwtService to generate the token
                    var token = jwtService.GenerateJwtToken();
                    return new OkObjectResult(token);
                }
            }
            catch (Exception ex)
            {
                log.LogError($"Error: {ex.Message}");
                return new BadRequestObjectResult(ex.Message);
            }
        }


    }

    
}
