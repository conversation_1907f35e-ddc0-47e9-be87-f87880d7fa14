﻿using WorkFlow.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using WorkFlow.Models;

namespace WorkFlow.Controllers
{
    public class WFHomeController : Controller
    {
        //
        // GET: /Default/
        //Coded By Muralidhara H M :-P
        public ActionResult HomePage()
        {
            if (Session["IsNewSession"] != null)
            {
                WFCommon.appPath=  HttpContext.Request.ApplicationPath == "/" ? "" : HttpContext.Request.ApplicationPath;
                return View("~/Views/Shared/HomePage.cshtml");
            }
            else
            {
                if (Session.IsNewSession)
                {
                    return View("~/Views/Shared/SessionRunning.cshtml");
                }
                else
                {
                    Session.Abandon();
                    Session.Clear();
                    Session.RemoveAll();
                    return View("~/Views/Shared/SessionExpire.cshtml");
                }
            }
                
        }

    }
}
