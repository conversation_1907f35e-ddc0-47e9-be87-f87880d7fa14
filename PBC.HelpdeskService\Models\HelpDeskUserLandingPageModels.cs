using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace PBC.HelpdeskService.Models
{
    // Request DTOs
    public class SelectDealerNameList
    {
        public string value { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
    }

    public partial class HelpDeskServiceRequestEntities
    {

        public HD_ServiceRequestNotesDetail HD_ServiceRequestNotesDetail { get; set; }
        public HD_SRFollowUpDetails HD_SRFollowUpDetails { get; set; }
        public HD_SRFollowUpInviteDetails HD_SRFollowUpInviteDetails { get; set; }
        public HD_SRPRODUCTDETAILSALLOCATION HD_SRPRODUCTDETAILSALLOCATION { get; set; }
        public HD_ServiceRequest HD_ServiceRequest { get; set; }
        public HD_CRERCRepository HD_CRERCRepository { get; set; }
        public HD_ServiceRequestAttachmentInfo HD_ServiceRequestAttachmentInfo { get; set; }
        public HD_SR_SURVEY_ANSWERS HD_SR_SURVEY_ANSWERS { get; set; }
        public HD_SR_SURVEY_QUESTIONS HD_SR_SURVEY_QUESTIONS { get; set; }
        public HD_ServiceRequestPartsList HD_ServiceRequestPartsList { get; set; }
        public HD_KnowledgeBase HD_KnowledgeBase { get; set; }
        public HD_SRProductDetails HD_SRProductDetails { get; set; }
        public TICKET_CUSTOMERHISTORY TICKET_CUSTOMERHISTORY { get; set; }
        public HD_CustomerFeedbackQuestion HD_CustomerFeedbackQuestion { get; set; }
        public HD_SRCustomerQuestionFeedBack HD_SRCustomerQuestionFeedBack { get; set; }

        // public virtual ObjectResult<SP_CustomerFeedbackReport_Result> SP_CustomerFeedbackReport(string branchId, Nullable<int> languageID, string rating, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<bool> isNegFeedback)
        // {
        //     var branchIdParameter = branchId != null ?
        //         new ObjectParameter("BranchId", branchId) :
        //         new ObjectParameter("BranchId", typeof(string));

        //     var languageIDParameter = languageID.HasValue ?
        //         new ObjectParameter("LanguageID", languageID) :
        //         new ObjectParameter("LanguageID", typeof(int));

        //     var ratingParameter = rating != null ?
        //         new ObjectParameter("Rating", rating) :
        //         new ObjectParameter("Rating", typeof(string));

        //     var fromDateParameter = fromDate.HasValue ?
        //         new ObjectParameter("FromDate", fromDate) :
        //         new ObjectParameter("FromDate", typeof(System.DateTime));

        //     var toDateParameter = toDate.HasValue ?
        //         new ObjectParameter("ToDate", toDate) :
        //         new ObjectParameter("ToDate", typeof(System.DateTime));

        //     var isNegFeedbackParameter = isNegFeedback.HasValue ?
        //         new ObjectParameter("IsNegFeedback", isNegFeedback) :
        //         new ObjectParameter("IsNegFeedback", typeof(bool));

        //     return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SP_CustomerFeedbackReport_Result>("SP_CustomerFeedbackReport", branchIdParameter, languageIDParameter, ratingParameter, fromDateParameter, toDateParameter, isNegFeedbackParameter);
        // }
    }
    public partial class TICKET_CUSTOMERHISTORY
    {
        public int Party_ID { get; set; }
        public string Ticket_ { get; set; }
        public System.DateTime TicketDate { get; set; }
        public string Brand { get; set; }
        public string ProductType_Name { get; set; }
        public string Model_Name { get; set; }
        public string License { get; set; }
        public string VIN { get; set; }
        public Nullable<int> ProductReading { get; set; }
        public string PartyContactPerson_Name { get; set; }
        public string PartyContactPerson_Mobile { get; set; }
        public string TicketStatus { get; set; }
        public string QuotationNumber { get; set; }
        public Nullable<System.DateTime> QuotationDate { get; set; }
        public Nullable<byte> QuotationVersion { get; set; }
        public string QuotationStatus { get; set; }
        public string WO_JOBNUM { get; set; }
        public Nullable<System.DateTime> JobCardDate { get; set; }
        public string WorkorderStatus { get; set; }
    }
    public partial class HD_KnowledgeBase
    {
        public int KnowledgeBase_ID { get; set; }
        public string FileName { get; set; }
        public int Parent_ID { get; set; }
        public string FileType { get; set; }
        public string Content { get; set; }
        public Nullable<int> IssueArea_ID { get; set; }
        public Nullable<int> IssueSubArea_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
    }
    public partial class HD_SR_SURVEY_QUESTIONS
    {
        public HD_SR_SURVEY_QUESTIONS()
        {
            this.HD_SR_SURVEY_ANSWERS = new HashSet<HD_SR_SURVEY_ANSWERS>();
        }

        public int QUESTION_ID { get; set; }
        public string QUESTION_DESCRIPTION { get; set; }
        public string OPTION_A { get; set; }
        public string OPTION_B { get; set; }
        public string OPTION_C { get; set; }
        public string OPTION_D { get; set; }

        public virtual ICollection<HD_SR_SURVEY_ANSWERS> HD_SR_SURVEY_ANSWERS { get; set; }
    }

    public partial class HD_SR_SURVEY_ANSWERS
    {
        public int ANSWER_ID { get; set; }
        public Nullable<int> CONTACTPERSON_ID { get; set; }
        public Nullable<int> QUESTION_ID { get; set; }
        public Nullable<byte> ANSWER_OPTION { get; set; }

        public virtual HD_SR_SURVEY_QUESTIONS HD_SR_SURVEY_QUESTIONS { get; set; }
    }

    public class InitialSetupList
    {
        public int ObjectId { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; }
        public int Company_ID { get; set; }
        public bool NeedToChangepassword { get; set; }
    }

    // Entity Models (from original SharedAPIClassLibrary_DC)
    public class GNM_Branch
    {
        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_ShortName { get; set; }
        public string Branch_ZipCode { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Branch_Phone { get; set; }
        public string Branch_Fax { get; set; }
        public bool Branch_HeadOffice { get; set; }
        public bool Branch_Active { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_Email { get; set; }
        public string Branch_Mobile { get; set; }
        public bool? Branch_External { get; set; }
        public int? TimeZoneID { get; set; }
        public int? Region_ID { get; set; }
        public string Branch_Code { get; set; }
        public string Branch_TaxNumber { get; set; }
        public string Branch_RegistrationNumber { get; set; }
        public DateTime? Branch_EstablishedDate { get; set; }
        public string Branch_Description { get; set; }
        public string Branch_Currency { get; set; }
        public string Branch_Language { get; set; }
        public DateTime? Created_Date { get; set; }
        public int? Created_By { get; set; }
        public DateTime? Modified_Date { get; set; }
        public int? Modified_By { get; set; }
    }
    public partial class GNM_PartyProductAssociation
    {
        public int PartyProduct_ID { get; set; }
        public int Party_ID { get; set; }
        public int Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartySkillset
    {
        public int Party_Skillset_ID { get; set; }
        public int Party_ID { get; set; }
        public int Party_Skillset_Rating { get; set; }
        public int Skillset_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyBranchAssociation
    {
        public int PartyBranch_ID { get; set; }
        public int Party_ID { get; set; }
        public int Branch_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_ServiceSchedule
    {
        public int PartyServiceSchedule_ID { get; set; }
        public int Party_ID { get; set; }
        public int ServiceType_ID { get; set; }
        public System.DateTime ServiceDate { get; set; }
        public Nullable<byte> Status { get; set; }
        public string Closure_reason { get; set; }
        public Nullable<int> JobCard_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_PartySegmentDetails
    {
        public int PartySegment_ID { get; set; }
        public int Party_ID { get; set; }
        public int PrimarySegment_ID { get; set; }
        public Nullable<int> SecondarySegment_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_PARTYCONTRACTDETAILS
    {
        public int PartyContract_ID { get; set; }
        public int Party_ID { get; set; }
        public string AgreementNumber { get; set; }
        public System.DateTime FromDate { get; set; }
        public System.DateTime ToDate { get; set; }
        public decimal ContractValue { get; set; }
        public string Unit { get; set; }
        public int Currency { get; set; }
        public string Remarks { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_PartyTaxStructure
    {
        public int PartyTaxStructure_ID { get; set; }
        public int Party_ID { get; set; }
        public int TaxStructure_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyDiscount
    {
        public int PartyDiscount_ID { get; set; }
        public int Party_ID { get; set; }
        public Nullable<decimal> Parts_Discount { get; set; }
        public Nullable<decimal> Service_Discount { get; set; }
        public Nullable<System.DateTime> Effective_Date { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyPartsRatecontract
    {
        public int PartyPartsRateContract_ID { get; set; }
        public int Party_ID { get; set; }
        public int Part_ID { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> Quantity { get; set; }
        public System.DateTime Effective_FromDate { get; set; }
        public Nullable<System.DateTime> Effective_ToDate { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> UploadedBy { get; set; }
        public Nullable<System.DateTime> UploadedDate { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_PartyCreditLimitLog
    {
        public int PartyCreditLimitLog_ID { get; set; }
        public System.DateTime UpdatedDateTime { get; set; }
        public int Currency_ID { get; set; }
        public decimal CreditLimit { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> Party_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_PartyAmount
    {
        public int PartyAmount_ID { get; set; }
        public int Party_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<decimal> PartsOutStandingCredit { get; set; }
        public Nullable<decimal> ServiceOutStandingCredit { get; set; }
        public Nullable<decimal> RemanOutStandingCredit { get; set; }
        public Nullable<decimal> SalesOutStandingCredit { get; set; }
        public Nullable<decimal> PartyAdvanceAmount { get; set; }
        public Nullable<int> Currency_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_CreditDetails
    {
        public int CreditDetails_ID { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<decimal> Parts_Credit_Limit { get; set; }
        public Nullable<decimal> Service_Credit_Limit { get; set; }
        public Nullable<decimal> Sales_Credit_Limit { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public partial class GNM_Party
    {
        public GNM_Party()
        {
            this.GNM_PartyBranchAssociation = new HashSet<GNM_PartyBranchAssociation>();
            this.GNM_PartyProductAssociation = new HashSet<GNM_PartyProductAssociation>();
            this.GNM_PartySkillset = new HashSet<GNM_PartySkillset>();
            this.GNM_ServiceSchedule = new HashSet<GNM_ServiceSchedule>();
            this.GNM_PartyLocale = new HashSet<GNM_PartyLocale>();
            this.GNM_PartySegmentDetails = new HashSet<GNM_PartySegmentDetails>();
            this.GNM_PartyTaxDetails = new HashSet<GNM_PartyTaxDetails>();
            this.GNM_PartyContactPersonDetails = new HashSet<GNM_PartyContactPersonDetails>();
            this.GNM_PartyAddress = new HashSet<GNM_PartyAddress>();
            this.GNM_PARTYCONTRACTDETAILS = new HashSet<GNM_PARTYCONTRACTDETAILS>();
            this.GNM_PartyTaxStructure = new HashSet<GNM_PartyTaxStructure>();
            this.GNM_PartyDiscount = new HashSet<GNM_PartyDiscount>();
            this.GNM_PartyPartsRatecontract = new HashSet<GNM_PartyPartsRatecontract>();
            this.GNM_PartyCreditLimitLog = new HashSet<GNM_PartyCreditLimitLog>();
            this.GNM_PartyAmount = new HashSet<GNM_PartyAmount>();
            this.GNM_CreditDetails = new HashSet<GNM_CreditDetails>();
        }

        public int Party_ID { get; set; }
        public bool Party_IsActive { get; set; }
        public bool Party_IsLocked { get; set; }
        public string Party_Name { get; set; }
        public string Party_Location { get; set; }
        public string Party_Email { get; set; }
        public string Party_Phone { get; set; }
        public string Party_Fax { get; set; }
        public string Party_PaymentTerms { get; set; }
        public byte PartyType { get; set; }
        public string Party_Mobile { get; set; }
        public int ModifiedBY { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> Country_ID { get; set; }
        public Nullable<int> State_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<bool> IsOEM { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<int> Relationship_Branch_ID { get; set; }
        public Nullable<int> Relationship_Company_ID { get; set; }
        public Nullable<decimal> PartsCreditLimit { get; set; }
        public Nullable<decimal> SalesCreditLimit { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public bool IsImportExport { get; set; }
        public Nullable<decimal> ServiceCreditLimit { get; set; }
        public Nullable<int> PaymentDueDays { get; set; }
        public Nullable<decimal> PartsOutStandingCredit { get; set; }
        public Nullable<decimal> ServiceOutStandingCredit { get; set; }
        public Nullable<decimal> PartyAdvanceAmount { get; set; }
        public Nullable<decimal> RemanOutStandingCredit { get; set; }
        public Nullable<decimal> SalesOutStandingCredit { get; set; }
        public Nullable<int> CustomerType_ID { get; set; }
        public Nullable<bool> IsKeyCustomer { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string Party_Code { get; set; }
        public Nullable<bool> SupplierHasInterface { get; set; }
        public Nullable<byte> CustomerType { get; set; }
        public Nullable<int> CustomerLanguageID { get; set; }
        public Nullable<decimal> ServiceCreditLimitinUSD { get; set; }
        public Nullable<decimal> ServiceOutStandingCreditinUSD { get; set; }
        public Nullable<bool> IsPONumberMandatory { get; set; }
        public Nullable<decimal> CAD_ExchangeRate { get; set; }
        public Nullable<decimal> US_ExchangeRate { get; set; }
        public Nullable<bool> CreditExceededMailSent { get; set; }
        public Nullable<bool> IsInternalCustomer { get; set; }
        public Nullable<decimal> Variance_Percentage { get; set; }
        public Nullable<decimal> Variance_Value { get; set; }

        public virtual ICollection<GNM_PartyBranchAssociation> GNM_PartyBranchAssociation { get; set; }
        public virtual ICollection<GNM_PartyProductAssociation> GNM_PartyProductAssociation { get; set; }
        public virtual ICollection<GNM_PartySkillset> GNM_PartySkillset { get; set; }
        public virtual ICollection<GNM_ServiceSchedule> GNM_ServiceSchedule { get; set; }
        public virtual ICollection<GNM_PartyLocale> GNM_PartyLocale { get; set; }
        public virtual ICollection<GNM_PartySegmentDetails> GNM_PartySegmentDetails { get; set; }
        public virtual ICollection<GNM_PartyTaxDetails> GNM_PartyTaxDetails { get; set; }
        public virtual ICollection<GNM_PartyContactPersonDetails> GNM_PartyContactPersonDetails { get; set; }
        public virtual ICollection<GNM_PartyAddress> GNM_PartyAddress { get; set; }
        public virtual ICollection<GNM_PARTYCONTRACTDETAILS> GNM_PARTYCONTRACTDETAILS { get; set; }
        public virtual ICollection<GNM_PartyTaxStructure> GNM_PartyTaxStructure { get; set; }
        public virtual ICollection<GNM_PartyDiscount> GNM_PartyDiscount { get; set; }
        public virtual ICollection<GNM_PartyPartsRatecontract> GNM_PartyPartsRatecontract { get; set; }
        public virtual ICollection<GNM_PartyCreditLimitLog> GNM_PartyCreditLimitLog { get; set; }
        public virtual ICollection<GNM_PartyAmount> GNM_PartyAmount { get; set; }
        public virtual ICollection<GNM_CreditDetails> GNM_CreditDetails { get; set; }

    }

    public class NameID
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }

    public class TabPosition
    {
        public int ID { get; set; }
        public bool Visibility { get; set; }
        public int Position { get; set; }
    }

    // Utility Request Models for HTTP calls
    public class DecryptStringRequest
    {
        public string EncryptedString { get; set; }
    }

    public class GetResourceRequest
    {
        public string CultureValue { get; set; }
        public string ResourceKey { get; set; }
    }

    public class InitialSetupRequest
    {
        public int ObjectId { get; set; }
        public int UserId { get; set; }
        public string ConnectionString { get; set; }
        public int LogException { get; set; }
    }

    public class GetObjectIdRequest
    {
        public string Name { get; set; }
        public string ConnectionString { get; set; }
        public int LogException { get; set; }
    }

    public class CheckPermissionsRequest
    {
        public string Name { get; set; }
        public string WFName { get; set; }
        public string HelpDesk { get; set; }
        public int CompanyId { get; set; }
        public int LogException { get; set; }
        public int UserId { get; set; }
        public string ConnectionString { get; set; }
    }

    public class GetValueFromDbRequest
    {
        public string Query { get; set; }
        public Dictionary<string, object> Parameters { get; set; }
        public string ConnectionString { get; set; }
    }

    public class LockRecordRequest
    {
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public int QuotationID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public string WorkFlowName { get; set; } = string.Empty;
        public string DBName { get; set; } = string.Empty;
        public int Branch_ID { get; set; } = 0;
    }

    public class UnLockRecordRequest
    {
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public int JobcardID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public string WorkFlowName { get; set; } = string.Empty;
        public string DBName { get; set; } = string.Empty;
        public int Branch_ID { get; set; } = 0;
    }

    public class LocalTimeBasedOnBranchRequest
    {
        public int BranchID { get; set; }
        public DateTime ServerTime { get; set; }
        public string ConnString { get; set; } = string.Empty;
    }

    public class GetEndStepStatusNameRequest
    {
        public int WorkflowID { get; set; }
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; } = 1;
    }

    public class OrderByFieldRequest
    {
        public string SortField { get; set; } = string.Empty;
        public string SortDirection { get; set; } = "asc";
        public object[] Data { get; set; } = Array.Empty<object>();
    }

    public class FilterSearchRequest
    {
        public Filters Filters { get; set; } = new Filters();
        public object[] Data { get; set; } = Array.Empty<object>();
    }

    public class ExtensionMethodsResponse<T>
    {
        public bool Success { get; set; }
        public T Data { get; set; }
        public int TotalCount { get; set; }
        public string? Message { get; set; }
    }

    // Batch 2 DTOs - Permission and Validation Methods
    public class validateCalldateandPCDList
    {
        public DateTime PCD { get; set; }
        public DateTime Calldate { get; set; }
    }

    public class CheckBayWorkshopAvailabilityList
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsWIPBay { get; set; }
        public int BookingMinutes { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
    }

    public class CheckForWorkshopBlockOverlapList
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsFromQuote { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
        public int VIN { get; set; }
        public int ServiceRequest_ID { get; set; }
    }

    // Additional utility request models for batch 2
    public class GetWorkFlowIDRequest
    {
        public string WorkFlowName { get; set; }
        public string DBName { get; set; }
        public string ConnString { get; set; }
        public int LogException { get; set; }
    }

    public class CheckAddRecordsRequest
    {
        public int ObjectID { get; set; }
        public int WorkFlowID { get; set; }
        public int CompanyID { get; set; }
        public int UserID { get; set; }
        public string ConnString { get; set; }
        public int LogException { get; set; }
    }

    // Batch 3 DTOs - Data Retrieval and Search Methods
    public class SelectFieldSearchParty2List
    {
        public string value { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int LanguageID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
    }

    public class GetDataUserLindingList
    {
        public string starts_with { get; set; }
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class GetDealerDataList
    {
        public string starts_with { get; set; }
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class GetProductDetailsUserLandingList
    {
        public int ProductID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }

    public class checkDuplicateContactPersonList
    {
        public string CPName { get; set; }
        public int Party_ID { get; set; }
    }

    public class GetOpenCampaignDetailsList
    {
        public int Product_ID { get; set; }
        public int Company_ID { get; set; }
    }

    // Entity models used in batch 3 methods (from original code)
    public class PartyContactDetails
    {
        public int Party_ID { get; set; }
        public string Party_Name { get; set; }
        public string PartyContactPerson_Mobile { get; set; }
        public string PartyContactPerson_Email { get; set; }
        public string Party_Location { get; set; }
        public int? PartyContactPerson_ID { get; set; }
        public string Party_Code { get; set; }
    }

    public class PartyDetailsAuto
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Mobile { get; set; }
        public string Email { get; set; }
        public string Location { get; set; }
        public string Address { get; set; }
        public string Party_Code { get; set; }
    }

    public class GNM_Product
    {
        public int Product_ID { get; set; }
        public string Product_SerialNumber { get; set; }
        public string Product_UniqueNo { get; set; }
        public int Model_ID { get; set; }
        public int Brand_ID { get; set; }
        public int ProductType_ID { get; set; }
        public int? PrimarySegment_ID { get; set; }
        public int? SecondarySegment_ID { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public bool? IsComponent { get; set; }
        public int? Party_ID { get; set; }
        public int? NextServiceType_ID { get; set; }
        public DateTime? NextServiceDate { get; set; }
        public int? MachineStatus_ID { get; set; }
        public int? AverageReadingPerDay { get; set; }
        public DateTime? CommissioningDate { get; set; }
        public bool? IsActive { get; set; }
        public int? Warehouse_ID { get; set; }
        public int? Parts_ID { get; set; }
        public int? SerialStatus { get; set; }
        public DateTime? DateOfManufacture { get; set; }
        public string Product_EngineSerialNumber { get; set; }
        public int? ServiceCompany { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public decimal? LandingCost { get; set; }
        public DateTime? WarrantyEndDate { get; set; }
        public DateTime? DateOfSale { get; set; }
        public byte? AttachmentCount { get; set; }
        public int? WholeSaleDealerid { get; set; }
        public bool? IsWholeSaleUser { get; set; }
        public int? ServiceEngineer_ID { get; set; }
        public string Reading_Unit { get; set; }
        public int? LastServiceBranch { get; set; }
        public bool? IsPCPApplicable { get; set; }
        public byte? PCPFrequency { get; set; }
        public byte? PCPUsedCount { get; set; }
        public bool? IsClaasMachine { get; set; }
        public string Series { get; set; }
        public bool? isSeriesAttachmentAdded { get; set; }
    }

    public partial class GNM_RefMasterDetailLocale
    {
        public int RefMasterDetailLocale_ID { get; set; }
        public int RefMasterDetail_ID { get; set; }
        public int RefMaster_ID { get; set; }
        public string RefMasterDetail_Short_Name { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public int Language_ID { get; set; }
        public bool RefMasterDetail_IsActive { get; set; }

        public virtual GNM_RefMaster GNM_RefMaster { get; set; }
        public virtual GNM_RefMasterDetail GNM_RefMasterDetail { get; set; }
    }

    public partial class GNM_CompParam
    {
        public int CompanyParam_ID { get; set; }
        public int Company_ID { get; set; }
        public string Param_Name { get; set; }
        public string Param_value { get; set; }
    }

    public partial class GNM_RefMaster
    {
        public GNM_RefMaster()
        {
            this.GNM_RefMasterDetail = new HashSet<GNM_RefMasterDetail>();
            this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
        }

        public int RefMaster_ID { get; set; }
        public string RefMaster_Name { get; set; }
        public Nullable<bool> IsCompanySpecific { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public bool IsSystemMaster { get; set; }

        public virtual ICollection<GNM_RefMasterDetail> GNM_RefMasterDetail { get; set; }
        public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
    }
    public partial class GNM_RefMasterDetail
    {
        public GNM_RefMasterDetail()
        {
            this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
        }

        public int RefMasterDetail_ID { get; set; }
        public bool RefMasterDetail_IsActive { get; set; }
        public string RefMasterDetail_Short_Name { get; set; }
        public bool IsCompanySpecific { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int RefMaster_ID { get; set; }
        public bool RefMasterDetail_IsDefault { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string SystemCondition { get; set; }

        public virtual GNM_RefMaster GNM_RefMaster { get; set; }
        public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
    }
    public class GNM_PartyLocale
    {
        public int Party_Locale_ID { get; set; }
        public int Party_ID { get; set; }
        public string Party_Name { get; set; }
        public string Party_Location { get; set; }
        public string Party_PaymentTerms { get; set; }
        public int Language_ID { get; set; }
        public string Party_Address { get; set; }
        public string Party_Code { get; set; }
    }

    public class GetCallHistoryList
    {
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int userLanguageID { get; set; }
        public int generalLanguageID { get; set; }
        public bool IsDealer { get; set; }
        public string PartyID { get; set; }
        public string UserCulture { get; set; }
    }

    public partial class GNM_PartyAddress
    {
        public GNM_PartyAddress()
        {
            this.GNM_PartyAddressLocale = new HashSet<GNM_PartyAddressLocale>();
        }

        public int PartyAddress_ID { get; set; }
        public int Party_ID { get; set; }
        public string PartyAddress_Location { get; set; }
        public string PartyAddress_Address { get; set; }
        public int PartyAddress_LeadTimeInDays { get; set; }
        public Nullable<int> PartyAddress_CountryID { get; set; }
        public Nullable<int> PartyAddress_StateID { get; set; }
        public bool PartyAddress_Active { get; set; }
        public string PartyAddress_ZIP { get; set; }
        public bool IsDefault { get; set; }
        public Nullable<int> EquivalentConsignee_ID { get; set; }
        public Nullable<int> Region_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
        public virtual ICollection<GNM_PartyAddressLocale> GNM_PartyAddressLocale { get; set; }
    }

    public class GNM_PartyAddressLocale
    {
        public int PartyAddressLocale_ID { get; set; }
        public int PartyAddress_ID { get; set; }
        public string PartyAddressLocale_Address { get; set; }
        public int Language_ID { get; set; }
    }

    public class GNM_ProductCustomer
    {
        public int ProductCustomer_ID { get; set; }
        public int Product_ID { get; set; }
        public int Party_ID { get; set; }
        public DateTime? ProductCustomer_FromDate { get; set; }
        public DateTime? ProductCustomer_ToDate { get; set; }
    }

    public class Filters
    {
        public string groupOp { get; set; }
        public List<FilterRule> rules { get; set; }
    }

    public class FilterRule
    {
        public string field { get; set; }
        public string op { get; set; }
        public string data { get; set; }
    }

    public class AdvanceFilter
    {
        public ICollection<Rules> rules { get; set; }
    }

    public class Rules
    {
        public string Field { get; set; }
        public string Data { get; set; }
        public string Operator { get; set; }
        public string Condition { get; set; }
    }

    // Additional models for campaign functionality

    public class ParentCompanyObject
    {
        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public int? Company_Parent_ID { get; set; }
    }

    public class CampaignClass
    {
        public int Campaign_ID { get; set; }
        public string CampaignName { get; set; }
        public string CampaignCode { get; set; }
        public int Company_ID { get; set; }
    }

    public class GNM_PartyContactPersonDetails
    {
        public int PartyContactPerson_ID { get; set; }
        public int Party_ID { get; set; }
        public string PartyContactPerson_Name { get; set; }
        public string PartyContactPerson_Email { get; set; }
        public string? PartyContactPerson_Department { get; set; }
        public string PartyContactPerson_Mobile { get; set; }
        public string? PartyContactPerson_Phone { get; set; }
        public bool Party_IsDefaultContact { get; set; }
        public bool PartyContactPerson_IsActive { get; set; }
        public string? PartyContactPerson_Remarks { get; set; }
        public int Language_ID { get; set; }
        public DateTime? PartyContactPerson_DOB { get; set; }
    }

    public class FieldSearch
    {
        public int? ID { get; set; }
        public int? Party_ID { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public string MobileNumber { get; set; }
        public string Email { get; set; }
        public string Party_Code { get; set; }
        public string PartyAddress_Address { get; set; }
    }

    public class SaveNotesDetailsList
    {
        public int User_ID { get; set; }
        public int SRIDGlobal { get; set; }
        public string data { get; set; }
        public JObject jObj { get; set; }
    }
    public class ForAutoAllocationList
    {
        public int CompanyID { get; set; }
        public int WorkFlowID { get; set; }
        public int CurrentStepID { get; set; }
        public int ActionID { get; set; }
        public int TransactionNumber { get; set; }
        public int RoleID { get; set; }
        public int UserID { get; set; }
        public int BranchID { get; set; }
        public string UserCulture { get; set; }
    }

    // Request DTOs for Batch 3 HTTP endpoints
    public class SelectFieldSearchPartyRequest
    {
        public SelectFieldSearchParty2List Obj { get; set; }
        public string Sidx { get; set; }
        public string Sord { get; set; }
        public int Page { get; set; }
        public int Rows { get; set; }
        public bool Search { get; set; }
        public string Filters { get; set; }
    }

    public class GetCustomerDataRequest
    {
        public GetDataUserLindingList Obj { get; set; }
    }

    public class GetDealerDataRequest
    {
        public GetDealerDataList Obj { get; set; }
    }

    public class GetProductDetailsRequest
    {
        public GetProductDetailsUserLandingList Obj { get; set; }
    }

    public class CheckDuplicateContactPersonRequest
    {
        public checkDuplicateContactPersonList Obj { get; set; }
    }

    public class GetOpenCampaignDetailsRequest
    {
        public GetOpenCampaignDetailsList Obj { get; set; }
        public string Sidx { get; set; }
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; }
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; }
        public bool Advnce { get; set; }
        public string Query { get; set; }
    }
    public class ExportUserLandingList
    {
        public int Mode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string Legendfilter { get; set; }
        public string UserCulture { get; set; }
        public string IsDealerToDealerSupply { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int LanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int exprtType { get; set; }
    }

    public partial class ServiceRequest
    {
        public long row_number
        {
            get;
            set;
        }

        public string IsDropin
        {
            get;
            set;
        }
        public string IsEscaltedString { get; set; }
        public bool? IsEscalted { get; set; }
        public int? EscalatedLevel { get; set; }
        public string SubGroupName
        {
            get;
            set;
        }
        public string IsDropinString
        {
            get;
            set;
        }
        public bool? IsDealer
        {
            get;
            set;
        }
        public string SiteAddress
        {
            get;
            set;
        }
        public string Brand_Name
        {
            get;
            set;
        }
        public string JobCardStatus
        {
            get;
            set;
        }
        public string JobCardActivity
        {
            get;
            set;
        }
        public string QuotationNumber
        {
            get;
            set;
        }

        public string JobNumber
        {
            get;
            set;
        }

        public string JobCardDate
        {
            get;
            set;
        }
        public string FinancialYear
        {
            get;
            set;
        }

        public int ServiceRequest_ID
        {
            get;
            set;
        }
        public string RequestNumber
        {
            get;
            set;
        }
        public DateTime ServiceRequestDate
        {
            get;
            set;
        }
        public string ServiceRequestDateSTR
        {
            get;
            set;
        }
        public string Date
        {
            get;
            set;
        }
        public string Model
        {
            get;
            set;
        }
        public string SerialNumber
        {
            get;
            set;
        }
        public string IssueArea
        {
            get;
            set;
        }
        public string IssueSubArea
        {
            get;
            set;
        }
        public string Status
        {
            get;
            set;
        }
        public string Party_Code
        {
            get;
            set;
        }
        public string PartyName
        {
            get;
            set;
        }
        public string Assign
        {
            get;
            set;
        }
        public Boolean? Locked_Ind
        {
            get;
            set;
        }
        public string Lock
        {
            get;
            set;
        }
        public int Addresse_ID
        {
            get;
            set;
        }
        public byte Addresse_Flag
        {
            get;
            set;
        }
        public int? WFNextStep_ID
        {
            get;
            set;
        }
        public int IndicatorType
        {
            get;
            set;
        }
        public int? Brand_ID
        {
            get;
            set;
        }
        public string Brand
        {
            get;
            set;
        }
        public string Product_Unique_Number
        {
            get;
            set;
        }
        public int? ProductType_ID
        {
            get;
            set;
        }
        public string ProductType
        {
            get;
            set;
        }
        public int PartyContactPerson_ID
        {
            get;
            set;
        }
        public string ContactPerson
        {
            get;
            set;
        }
        public int? ProductReading
        {
            get;
            set;
        }
        public string Email
        {
            get;
            set;
        }
        public string Phone
        {
            get;
            set;
        }
        public string CallModeName
        {
            get;
            set;
        }
        public string CallPriorityName
        {
            get;
            set;
        }
        public string CallComplexityName
        {
            get;
            set;
        }
        public int CallMode_ID
        {
            get;
            set;
        }
        public int CallPriority_ID
        {
            get;
            set;
        }
        public int CallComplexity_ID
        {
            get;
            set;
        }
        public DateTime CallDateAndTime
        {
            get;
            set;
        }
        public DateTime? PromisedCompletionDate
        {
            get;
            set;
        }
        public Boolean IsUnderWarranty
        {
            get;
            set;
        }
        public Boolean IsUnderBreakDown
        {
            get;
            set;
        }
        public string UnderWarrenty
        {
            get;
            set;
        }
        public string UnderBreakdown
        {
            get;
            set;
        }
        public string CallDescription
        {
            get;
            set;
        }
        public DateTime? CallClosureDateAndTime
        {
            get;
            set;
        }
        public int? CustomerRating
        {
            get;
            set;
        }
        public string CustomerRatingExport
        {
            get;
            set;
        }
        public int? CallOwner_ID
        {
            get;
            set;
        }
        public string CallOwner
        {
            get;
            set;
        }
        public int? FunctionGroup_ID
        {
            get;
            set;
        }
        public string FunctionGroupName
        {
            get;
            set;
        }
        public int? ClosureType_ID
        {
            get;
            set;
        }
        public string ClosureTypeName
        {
            get;
            set;
        }
        public string InformationCollected
        {
            get;
            set;
        }
        public int CaseType_ID
        {
            get;
            set;
        }
        public string EnquiryType
        {
            get;
            set;
        }
        public int? Region_ID
        {
            get;
            set;
        }
        public string RegionName
        {
            get;
            set;
        }
        public string ClosingDescription
        {
            get;
            set;
        }
        public string PartyType
        {
            get;
            set;
        }
        public int Party_ID
        {
            get;
            set;
        }
        public string Attachmentcount
        {
            get;
            set;
        }
        public byte? Attachmentco
        {
            get;
            set;
        }
        public string calldatestring
        {
            get;
            set;
        }
        public int Branch_ID
        {
            get;
            set;
        }
        public string BranchName
        {
            get;
            set;
        }
        public int? ChildTicket_Sequence_ID
        {
            get;
            set;
        }
        public string PartyContactPerson_Email
        {
            get;
            set;
        }
        public string PartyContactPerson_Mobile
        {
            get;
            set;
        }
        public string PartyContactPerson_Name
        {
            get;
            set;
        }

        public string AssignedTo
        {
            get;
            set;
        }
        public string StepStatusCode
        {
            get;
            set;
        }
        public int? JobCard_ID
        {
            get;
            set;
        }
        public DateTime? WorkOrderDate
        {
            get;
            set;
        }
        public DateTime? QuoteDate
        {
            get;
            set;
        }
        public string Reading_Unit
        {
            get;
            set;
        }
    }

    public class ReportExportList
    {
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public System.Data.DataTable? Options { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public System.Data.DataTable? Alignment { get; set; }
        public string? FileName { get; set; }
        public string? Header { get; set; }
        public int exprtType { get; set; }
        public string? UserCulture { get; set; }
    }






    #region HelpDeskUserLandingPageMasterClasses
    public class WorkFlowSummary
    {
        public int StatusID { get; set; }

        public string StatusName { get; set; }

        public int Count { get; set; }

        public int MaxValue { get; set; }

        public int Mode { get; set; }

        public int Company_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int WFCaseProgress_ID { get; set; }
    }

    public class SLA
    {
        public int WFCaseProgress_ID
        {
            get;
            set;
        }
        public int CallPriority_ID
        {
            get;
            set;
        }
        public int CallComplexity_ID
        {
            get;
            set;
        }
        public int SRPartyID
        {
            get;
            set;
        }
        public int? AGPartyID
        {
            get;
            set;
        }
        public decimal ServiceLevelAgreement_Hours
        {
            get;
            set;
        }
        public DateTime CallDateAndTime
        {
            get;
            set;
        }
    }

    public class CallHistory
    {
        public int ServiceRequestID
        {
            get;
            set;
        }
        public string PartyName
        {
            get;
            set;
        }
        public string RequestNumber
        {
            get;
            set;
        }
        public DateTime Date
        {
            get;
            set;
        }
        public string Model
        {
            get;
            set;
        }
        public string SerialNumber
        {
            get;
            set;
        }
        public string IssueArea
        {
            get;
            set;
        }
        public string IssueSubArea
        {
            get;
            set;
        }
        public string ContactPerson
        {
            get;
            set;
        }
        public string ContactPersonMobile
        {
            get;
            set;
        }
        public string Status
        {
            get;
            set;
        }

    }

    public class UnLockRecordList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int ServiceRequestID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
    }

    public partial class SRM_CampaignCustomerDetails
    {
        public int CampaignCustomerDetails_ID { get; set; }
        public Nullable<int> Campaign_ID { get; set; }
        public Nullable<int> Customer_ID { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<int> State_ID { get; set; }
        public string Location { get; set; }
        public Nullable<int> JobCard_ID { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }
    public class LockRecordList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int ServiceRequestID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
    }
    public partial class SRM_CampaignServiceChargeDetails
    {
        public int CampaignServiceChargeDetails_ID { get; set; }
        public Nullable<int> Campaign_ID { get; set; }
        public int ServiceCharge_ID { get; set; }
        public Nullable<decimal> Rate { get; set; }
        public Nullable<decimal> Quantity { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public Nullable<decimal> Customerdiscount { get; set; }
        public Nullable<decimal> OEMdiscount { get; set; }
        public bool IsMandatory { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }
    public partial class SRM_CampaignLocale
    {
        public int CampaignLocale_ID { get; set; }
        public int Campaign_ID { get; set; }
        public string CampaingName { get; set; }
        public int Language_ID { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }
    public partial class SRM_CampaignMachineDetails
    {
        public int CampaignMachineDetail_ID { get; set; }
        public int Campaing_ID { get; set; }
        public string SerialNumber { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<int> JobCard_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }

    public partial class SRM_CampaignOperationDetails
    {
        public int CampaignOperationDetail_ID { get; set; }
        public int Campaing_ID { get; set; }
        public int Operation_ID { get; set; }
        public Nullable<decimal> Quantity { get; set; }
        public bool IsMandatory { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }
    public partial class SRM_CampaignPartsDetails
    {
        public int CampaignPartsDetail_ID { get; set; }
        public int Campaign_ID { get; set; }
        public Nullable<int> Parts_ID { get; set; }
        public Nullable<decimal> Quantity { get; set; }
        public Nullable<decimal> Customerdiscount { get; set; }
        public Nullable<decimal> OEMdiscount { get; set; }
        public Nullable<bool> IsMandatory { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }
    public partial class SRM_CampaignCausingPartsDetails
    {
        public int CampaignCausingPartsDetails_ID { get; set; }
        public int Campaign_ID { get; set; }
        public int CausingPart_ID { get; set; }

        public virtual SRM_Campaign SRM_Campaign { get; set; }
    }
    public partial class SRM_Campaign
    {
        public SRM_Campaign()
        {
            this.SRM_CampaignCustomerDetails = new HashSet<SRM_CampaignCustomerDetails>();
            this.SRM_CampaignServiceChargeDetails = new HashSet<SRM_CampaignServiceChargeDetails>();
            this.SRM_CampaignLocale = new HashSet<SRM_CampaignLocale>();
            this.SRM_CampaignMachineDetails = new HashSet<SRM_CampaignMachineDetails>();
            this.SRM_CampaignOperationDetails = new HashSet<SRM_CampaignOperationDetails>();
            this.SRM_CampaignPartsDetails = new HashSet<SRM_CampaignPartsDetails>();
            this.SRM_CampaignCausingPartsDetails = new HashSet<SRM_CampaignCausingPartsDetails>();
        }

        public int Campaign_ID { get; set; }
        public string CampaignCode { get; set; }
        public string CampaignName { get; set; }
        public bool IsActive { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<decimal> MaximumSundryValue { get; set; }
        public Nullable<decimal> MaximumTravelValue { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public string Remarks { get; set; }
        public Nullable<bool> IsOEM_Campaign { get; set; }
        public Nullable<bool> IsModelSpecific { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string SelectionCriteria { get; set; }
        public string FEGRP { get; set; }
        public string FECOD { get; set; }

        public virtual ICollection<SRM_CampaignCustomerDetails> SRM_CampaignCustomerDetails { get; set; }
        public virtual ICollection<SRM_CampaignServiceChargeDetails> SRM_CampaignServiceChargeDetails { get; set; }
        public virtual ICollection<SRM_CampaignLocale> SRM_CampaignLocale { get; set; }
        public virtual ICollection<SRM_CampaignMachineDetails> SRM_CampaignMachineDetails { get; set; }
        public virtual ICollection<SRM_CampaignOperationDetails> SRM_CampaignOperationDetails { get; set; }
        public virtual ICollection<SRM_CampaignPartsDetails> SRM_CampaignPartsDetails { get; set; }
        public virtual ICollection<SRM_CampaignCausingPartsDetails> SRM_CampaignCausingPartsDetails { get; set; }
    }

    public class WF_WFStepStatus
    {
        public int WFStepStatus_ID { get; set; }

        public string WFStepStatus_Nm { get; set; }

        public string StepStatusCode { get; set; }
    }

    public partial class HD_ServiceLevelAgreement
    {
        public int ServiceLevelAgreement_ID { get; set; }
        public int CallComplexity_ID { get; set; }
        public int CallPriority_ID { get; set; }
        public decimal ServiceLevelAgreement_Hours { get; set; }
        public int Company_ID { get; set; }
        public bool ServiceLevelAgreementHours_IsActive { get; set; }
        public Nullable<int> Party_ID { get; set; }

        public virtual HD_ServiceLevelAgreement HD_ServiceLevelAgreement1 { get; set; }
        public virtual HD_ServiceLevelAgreement HD_ServiceLevelAgreement2 { get; set; }
    }

    public class GetWorkFlowSummaryList
    {
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int User_ID { get; set; }
        //SelWorkFlowSummary
        public int Branch { get; set; }
        public string UserCulture { get; set; }

    }

    public partial class GNM_PartyTaxDetails
    {
        public int PartyTax_ID { get; set; }
        public int Party_ID { get; set; }
        public string PartyTax_TaxCode { get; set; }
        public string PartyTax_TaxCodeDescription { get; set; }
        public bool PartyTaxDetails_IsActive { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }

    public class SaveCustomerList
    {
        public int Branch { get; set; }
        public string key { get; set; }
        public string PartyAddress { get; set; }
        public string PartyTaxDetails { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
    }

    public class TableDescription
    {
        public string Label
        {
            get;
            set;
        }
        public string DataType
        {
            get;
            set;
        }
        public string ColumnName
        {
            get;
            set;
        }
        public string ControlID
        {
            get;
            set;
        }
    }
    public class PackingListObj
    {
        public int PackingType_ID { get; set; }
        public string PackingType_Name { get; set; }
    }
    #endregion

    public class Indicator
    {
        public int TransactionID { get; set; }
        public int IndicatorType { get; set; }
        public bool IsLock { get; set; }
    }

    public class GroupQueueAdvancedRequest
    {
        public int CompanyID { get; set; }
        public required string ConnString { get; set; }
        public int WorkFlowID { get; set; }
        public int UserID { get; set; }
    }

    public class AllQueueRequest
    {
        public int WorkFlowID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public required string ConnString { get; set; }
        public int LogException { get; set; }
        public int StatusID { get; set; }
        public int BranchID { get; set; }
    }

    public partial class HD_ServiceRequest
    {
        public HD_ServiceRequest()
        {
            this.HD_ServiceRequestNotesDetail = new HashSet<HD_ServiceRequestNotesDetail>();
            this.HD_SRFollowUpDetails = new HashSet<HD_SRFollowUpDetails>();
            this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
            this.HD_CRERCRepository = new HashSet<HD_CRERCRepository>();
            this.HD_ServiceRequestAttachmentInfo = new HashSet<HD_ServiceRequestAttachmentInfo>();
            this.HD_ServiceRequestPartsList = new HashSet<HD_ServiceRequestPartsList>();
            this.HD_SRProductDetails = new HashSet<HD_SRProductDetails>();
            this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
        }

        public int ServiceRequest_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
        public System.DateTime ServiceRequestDate { get; set; }
        public Nullable<int> Quotation_ID { get; set; }
        public string QuotationNumber { get; set; }
        public Nullable<int> JobCard_ID { get; set; }
        public string JobCardNumber { get; set; }
        public int CallStatus_ID { get; set; }
        public Nullable<int> ParentIssue_ID { get; set; }
        public string Product_Unique_Number { get; set; }
        public int Party_ID { get; set; }
        public int PartyContactPerson_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public string SerialNumber { get; set; }
        public Nullable<int> ProductReading { get; set; }
        public bool IsUnderWarranty { get; set; }
        public int CallMode_ID { get; set; }
        public Nullable<int> CallPriority_ID { get; set; }
        public int CallComplexity_ID { get; set; }
        public System.DateTime CallDateAndTime { get; set; }
        public Nullable<System.DateTime> PromisedCompletionDate { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string CallDescription { get; set; }
        public Nullable<int> IssueArea_ID { get; set; }
        public Nullable<int> IssueSubArea_ID { get; set; }
        public Nullable<int> FunctionGroup_ID { get; set; }
        public bool IsUnderBreakDown { get; set; }
        public Nullable<int> QuestionaryLevel1_ID { get; set; }
        public Nullable<int> QuestionaryLevel2_ID { get; set; }
        public Nullable<int> QuestionaryLevel3_ID { get; set; }
        public Nullable<int> DefectGroup_ID { get; set; }
        public Nullable<int> DefectName_ID { get; set; }
        public string RootCause { get; set; }
        public string InformationCollected { get; set; }
        public Nullable<System.DateTime> CallClosureDateAndTime { get; set; }
        public Nullable<int> ClosureType_ID { get; set; }
        public string ClosingDescription { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<int> ModifiedBy_ID { get; set; }
        public Nullable<int> Document_no { get; set; }
        public int CaseType_ID { get; set; }
        public string ActionRemarks { get; set; }
        public Nullable<int> Product_ID { get; set; }
        public Nullable<int> CustomerRating { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<bool> IsCallBlocked { get; set; }
        public Nullable<int> StockBlocking_ID { get; set; }
        public Nullable<int> EnquiryStage_ID { get; set; }
        public Nullable<int> SalesQuotation_ID { get; set; }
        public string SalesQuotationNumber { get; set; }
        public Nullable<int> SalesOrder_ID { get; set; }
        public string SalesOrderNumber { get; set; }
        public Nullable<System.DateTime> SalesOrderDate { get; set; }
        public Nullable<int> CallOwner_ID { get; set; }
        public string Flexi1 { get; set; }
        public string Flexi2 { get; set; }
        public string ResolutionTime { get; set; }
        public string ResponseTime { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<System.DateTime> CustomerFeedbackDate { get; set; }
        public Nullable<bool> IsNegetiveFeedback { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<byte> IsDealerList { get; set; }
        public Nullable<byte> ProductRateType { get; set; }
        public Nullable<int> ChildTicket_Sequence_ID { get; set; }
        public string ResponseTime24 { get; set; }
        public string ResolutionTime24 { get; set; }
        public Nullable<int> Current_AssignTo { get; set; }
        public Nullable<int> ContractorID { get; set; }
        public Nullable<int> ContractorContactPerson_ID { get; set; }
        public Nullable<int> ScheduledType_ID { get; set; }
        public Nullable<System.DateTime> ExpectedArrivalDateTime { get; set; }
        public Nullable<System.DateTime> ActualArrivalDateTime { get; set; }
        public Nullable<System.DateTime> ExpectedDepartureDateTime { get; set; }
        public Nullable<System.DateTime> ActualDepartureDateTime { get; set; }
        public Nullable<int> NoofTechs { get; set; }
        public Nullable<int> ShiftHours { get; set; }
        public Nullable<bool> IsWIPBay { get; set; }

        public virtual ICollection<HD_ServiceRequestNotesDetail> HD_ServiceRequestNotesDetail { get; set; }
        public virtual ICollection<HD_SRFollowUpDetails> HD_SRFollowUpDetails { get; set; }
        public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
        public virtual ICollection<HD_CRERCRepository> HD_CRERCRepository { get; set; }
        public virtual ICollection<HD_ServiceRequestAttachmentInfo> HD_ServiceRequestAttachmentInfo { get; set; }
        public virtual ICollection<HD_ServiceRequestPartsList> HD_ServiceRequestPartsList { get; set; }
        public virtual ICollection<HD_SRProductDetails> HD_SRProductDetails { get; set; }
        public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
    }

    public partial class HD_CustomerFeedbackQuestion
    {
        public HD_CustomerFeedbackQuestion()
        {
            this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
        }

        public int Question_ID { get; set; }
        public string Question { get; set; }
        public string CaseType_IDs { get; set; }
        public string IssueArea_IDs { get; set; }
        public string IssueSubArea_IDs { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsRating { get; set; }
        public bool IsFeedback { get; set; }
        public bool IsActive { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> QuestionCode { get; set; }

        public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
    }

    public partial class HD_SRCustomerQuestionFeedBack
    {
        public int QuestionFeedBack_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Question_ID { get; set; }
        public System.DateTime FeedBackDate { get; set; }
        public string FeedBack { get; set; }
        public Nullable<byte> Rating { get; set; }

        public virtual HD_CustomerFeedbackQuestion HD_CustomerFeedbackQuestion { get; set; }
        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_SRFollowUpInviteDetails
    {
        public int SRFollowUpInviteDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int SRFollowUpDetails_ID { get; set; }
        public bool Invitee_Type { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public bool IsAttended { get; set; }

        public virtual HD_SRFollowUpDetails HD_SRFollowUpDetails { get; set; }
        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_SRFollowUpDetails
    {
        public HD_SRFollowUpDetails()
        {
            this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
        }

        public int SRFollowUpDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string FollowUpDescription { get; set; }
        public System.DateTime StartDateandTime { get; set; }
        public Nullable<System.DateTime> EndDateandTime { get; set; }
        public int FollowUpMode_ID { get; set; }
        public int FollowUpStatus_ID { get; set; }
        public string Remarks { get; set; }

        public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_SRProductDetails
    {
        public HD_SRProductDetails()
        {
            this.HD_SRPRODUCTDETAILSALLOCATION = new HashSet<HD_SRPRODUCTDETAILSALLOCATION>();
        }

        public int SRProductDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Model_ID { get; set; }
        public int Brand_ID { get; set; }
        public int ProductType_ID { get; set; }
        public decimal Quantity { get; set; }
        public Nullable<decimal> ActiveQuantity { get; set; }
        public Nullable<decimal> WonQuantity { get; set; }
        public string LostSaleReasons { get; set; }
        public Nullable<int> Competitor_ID { get; set; }
        public Nullable<int> CompetitorModel_ID { get; set; }
        public Nullable<int> CompetitorBrand_ID { get; set; }
        public Nullable<int> CompetitorProductType_ID { get; set; }
        public Nullable<decimal> CompetitorPrice { get; set; }
        public Nullable<int> PrimarySegment_ID { get; set; }
        public Nullable<int> SecondarySegment_ID { get; set; }
        public string Remarks { get; set; }
        public Nullable<decimal> Rate { get; set; }
        public Nullable<int> PACKINGTYPE_ID { get; set; }
        public string ReferenceDetails { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        public virtual ICollection<HD_SRPRODUCTDETAILSALLOCATION> HD_SRPRODUCTDETAILSALLOCATION { get; set; }
    }
    public partial class HD_SRPRODUCTDETAILSALLOCATION
    {
        public int SRPRODUCTDETAILSALLOCATION_ID { get; set; }
        public int SRPRODUCTDETAILS_ID { get; set; }
        public int PRODUCT_ID { get; set; }

        public virtual HD_SRProductDetails HD_SRProductDetails { get; set; }
    }
    public partial class HD_ServiceRequestPartsList
    {
        public int ServiceRequestPartsList_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Parts_ID { get; set; }
        public int UnitOfMeasurement { get; set; }
        public decimal Quantity { get; set; }
        public string Remarks { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> MRP { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_ServiceRequestAttachmentInfo
    {
        public int ServiceRequestAttachmentInfo_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string FileName { get; set; }
        public int FileSize { get; set; }
        public string FileDescription { get; set; }
        public System.DateTime FileUploadDate { get; set; }
        public int FileUploadedByEmployee_ID { get; set; }
        public bool IsMailAttachment { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_CRERCRepository
    {
        public int CRERCRepository_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string RequestPartNumber { get; set; }
        public string RequestPartDescription { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Parts_ID { get; set; }
        public bool IsEPC { get; set; }
        public bool IsPricedInSAP { get; set; }
        public Nullable<decimal> Rate { get; set; }
        public bool IsNLS { get; set; }
        public bool IsIPN { get; set; }
        public Nullable<System.DateTime> LRDDate { get; set; }
        public bool IsMailAttachment { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_ServiceRequestNotesDetail
    {
        public int ServiceRequestNotes_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int CreatedBy { get; set; }
        public System.DateTime CreatedDateAndTime { get; set; }
        public string NotesDescription { get; set; }
        public string Department { get; set; }
        public string Name { get; set; }
        public Nullable<bool> IsFollowUp { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }

    public class GetInitialDataList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
    }

    public class WF_WFCase_Progress
    {
        public int WFCaseProgress_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int Transaction_ID { get; set; }

        public int WFSteps_ID { get; set; }

        public int? Addresse_ID { get; set; }

        public byte Addresse_Flag { get; set; }

        public DateTime Received_Time { get; set; }

        public int? Actioned_By { get; set; }

        public DateTime? Action_Time { get; set; }

        public int? Action_Chosen { get; set; }

        public string Action_Remarks { get; set; }

        public bool? Locked_Ind { get; set; }

        public int? WFNextStep_ID { get; set; }
    }


    public class SelectServiceRequestList
    {
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string CompanyIDs { get; set; }
        public string BranchIDs { get; set; }
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
        public int mode { get; set; }
        public int FromManager { get; set; }

        public int StatusID { get; set; }
        public string Legendfilter { get; set; } = "All";

    }

    public class SelectServiceRequestRequest
    {
        public SelectServiceRequestList Obj { get; set; }
        public string Sidx { get; set; }
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; }
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; }
        public bool Advnce { get; set; }
        public string Query { get; set; }
    }

    public class UpdateIsEditTicketList
    {
        public int ServiceRequest_ID { get; set; }
        public int Type { get; set; } // 1 - editing, 2 - saved
        public int User_ID { get; set; }
    }


}
