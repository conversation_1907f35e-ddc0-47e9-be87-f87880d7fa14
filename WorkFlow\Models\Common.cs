﻿using System;
using System.Collections.Generic;
using System.Data.EntityClient;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json.Linq;
using WorkFlow.Models;
using LS = LogSheetExporter;
using System.Configuration;

namespace WorkFlow.Models
{
    public static class WFCommon
    {
        
        
        public static string appPath
        {
            get;
            set;
        }

        #region:::InitialSetup:::
        public static JsonResult InitialSetup(int ObjID)
        {
            string query = string.Empty;
            List<ACLProperties> ACLobjList = null;
            GenEntities genEnt = new GenEntities(ConfigurationManager.AppSettings.Get("DbName"));
            int UserID = 0;
            JsonResult ACL = new JsonResult();
          
           WF_Object gobj = null;
            try
            {
                UserID = Convert.ToInt32(HttpContext.Current.Session["User_ID"]);
                ACLobjList = (List<ACLProperties>)HttpContext.Current.Session["ACL"];
                gobj = genEnt.WF_Object.Where(oid => oid.Object_ID == ObjID).FirstOrDefault();

                ACL.Data = new
                {
                    IsRead = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Read > 0) ? true : false,
                    IsAdd = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Create > 0) ? true : false,
                    IsEdit = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Update > 0) ? true : false,
                    IsDelete = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Delete > 0) ? true : false,
                    IsPrint = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Print > 0) ? true : false,
                    IsExport = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Export > 0) ? true : false,
                    IsImport = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Import > 0) ? true : false,

                    SelectAction = "/" + gobj.Object_Name + "/" + gobj.Read_Action,
                    AddAction = "/" + gobj.Object_Name + "/" + gobj.Create_Action,
                    EditAction = "/" + gobj.Object_Name + "/" + gobj.Update_Action,
                    DeleteAction = "/" + gobj.Object_Name + "/" + gobj.Delete_Action,
                    PrintAction = "/" + gobj.Object_Name + "/" + gobj.Print_Action,
                    ExportAction = "/" + gobj.Object_Name + "/" + gobj.Export_Action,
                    ImportAction = "/" + gobj.Object_Name + "/" + gobj.Import_Action

                    //IsRead = true,
                    //IsAdd = true,
                    //IsEdit = true,
                    //IsDelete = true,
                    //IsPrint = true,
                    //IsExport = true,
                    //IsImport = true,
                };
            }
            catch (Exception ex)
            {

            }

            return ACL;
        }
        #endregion

        #region :::BuildACLObject:::
        public static void BuildACLObject()
        {
            GenEntities genEnt = new GenEntities(ConfigurationManager.AppSettings.Get("DbName"));
            List<ACLProperties> ACLobjList = null;
            string query = string.Empty;
            try
            {
                WF_User User = (WF_User)HttpContext.Current.Session["UserDetails"];
                int UserID = User.User_ID;
                query = "select object_id,SUM(cast(RoleObject_Create as int)) RoleObject_Create ,SUM(cast(RoleObject_Read as int)) RoleObject_Read,sum(cast(roleobject_update as int)) RoleObject_Update, SUM(cast(roleobject_delete as int)) RoleObject_Delete, SUM(cast(roleobject_print as int)) RoleObject_Print, SUM(cast(roleobject_export as int)) RoleObject_Export ,SUM(cast(roleobject_import as int)) RoleObject_Import from GNM_roleobject where Role_ID in (select role_id from gnm_userrole where USER_ID=" + UserID + ") group by OBJECT_ID";

                ACLobjList = genEnt.Database.SqlQuery(typeof(ACLProperties), query).Cast<ACLProperties>().ToList();
                HttpContext.Current.Session["ACL"] = ACLobjList;
            }
            catch (Exception ex)
            {

            }
        }
        #endregion

        #region:::BuildMenu:::
        public static void BuildMenu(int ModuleID)
        {
            GenEntities genEnt = new GenEntities(ConfigurationManager.AppSettings.Get("DbName"));
            string query = string.Empty;
            try
            {
                WF_User User = (WF_User)HttpContext.Current.Session["UserDetails"];
                int UserID = User.User_ID;
               
                if (HttpContext.Current.Session["ACLMenu"] == null)
                {
                    List<LoginProperties> ACLobjList = null;
                    
                    query = ";WITH n as (SELECT Menu_ID,Module_ID,Object_ID,Parentmenu_ID,Menu_Path,Menu_Description,Menu_SortOrder FROM dbo.GNM_Menu WHERE Menu_ID IN (select Distinct Menu_ID from GNM_Menu left outer join GNM_Object on GNM_Object.Object_ID = GNM_Menu.Object_ID left outer join GNM_RoleObject on GNM_Object.Object_ID=GNM_RoleObject.Object_ID inner join GNM_Module modl  on GNM_Menu.Module_ID =modl.Module_ID where GNM_RoleObject.Role_ID in(select role_id from gnm_userrole where USER_ID=" + UserID + ")  and GNM_RoleObject.RoleObject_Read=1 and Menu_IsActive=1 and modl.Module_IsActive=1 ) UNION ALL SELECT child.Menu_ID,child.Module_ID,child.Object_ID,child.Parentmenu_ID,child.Menu_Path,child.Menu_Description,child.Menu_SortOrder FROM dbo.GNM_Menu AS child JOIN n ON child.Menu_ID = n.Parentmenu_ID) Select distinct Menu_ID,Module_ID,Object_ID,Parentmenu_ID,Menu_Path,Menu_Description,Menu_SortOrder from n";
                    
                    ACLobjList = genEnt.Database.SqlQuery(typeof(LoginProperties), query).Cast<LoginProperties>().ToList();
                    HttpContext.Current.Session["ACLMenu"] = ACLobjList;
                }
            }
            catch (Exception ex)
            {

            }

        }
        #endregion


        public static EntityConnection GetConnection(string dbname,string modelname)
        {

            EntityConnectionStringBuilder entityBuilder = new EntityConnectionStringBuilder();

            entityBuilder.ProviderConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings[dbname].ConnectionString;
            entityBuilder.Provider = "System.Data.SqlClient";
            entityBuilder.Metadata = @"res://*/Models."+modelname+".csdl|res://*/Models."+modelname+".ssdl|res://*/Models."+modelname+".msl";

            return new EntityConnection(entityBuilder.ToString());
        }
        public static string DecryptString(string str)
        {
            str = HttpContext.Current.Server.UrlDecode(Uri.UnescapeDataString(str)).Replace("%lthash%", "&#");
            return str;
        }
    }

    public class ACLProperties
    {
        public int Object_ID { get; set; }
        public int RoleObject_Create { get; set; }
        public int RoleObject_Read { get; set; }
        public int RoleObject_Update { get; set; }
        public int RoleObject_Delete { get; set; }
        public int RoleObject_Print { get; set; }
        public int RoleObject_Export { get; set; }
        public int RoleObject_Import { get; set; }
    }

    public class LoginProperties
    {
        public int Module_ID { get; set; }
        public int Parentmenu_ID { get; set; }
        public int Object_ID { get; set; }
        public int Menu_ID { get; set; }
        public string Menu_Description { get; set; }
        public string Menu_Path { get; set; }
        public byte Menu_SortOrder { get; set; }
        public bool Menu_IsActive { get; set; }
    }
}

