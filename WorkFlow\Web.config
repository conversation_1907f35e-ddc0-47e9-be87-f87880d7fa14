﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="PreserveLoginUrl" value="true"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="LogError" value="1"/>
    <add key="GeneralLngID" value="278"/>
    <add key="SmtpServerIP" value=""/>
    <add key="SmtpServerUserID" value=""/>
    <add key="SmtpServerPassword" value=""/>
    <add key="LogError" value="1"/>
    <add key="VersionNumber" value="1.0.0"/>
    <add key="VersionDate" value="04-Jan-2013"/>
    <add key="*elpLineNumber" value="+91 80 6691 4531"/>
    <add key="*elpLineEmail" value="<EMAIL>"/>
    <add key="DbName" value="FSMGOLD"/>
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <httpRuntime targetFramework="4.5"/>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
      </assemblies>
    </compilation>
    <pages>
      <namespaces>
        <add namespace="System.Web.*elpers"/>
        <add namespace="System.Web.Mvc"/>
        <add namespace="System.Web.Mvc.Ajax"/>
        <add namespace="System.Web.Mvc.*tml"/>
        <add namespace="System.Web.Routing"/>
        <add namespace="System.Web.WebPages"/>
      </namespaces>
    </pages>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false"/>
    <handlers>
      <remove name="ExtensionlessUrl*andler-ISAPI-4.0_32bit"/>
      <remove name="ExtensionlessUrl*andler-ISAPI-4.0_64bit"/>
      <remove name="ExtensionlessUrl*andler-Integrated-4.0"/>
      <add name="ExtensionlessUrl*andler-ISAPI-4.0_32bit" path="*." verb="GET,*EAD,POST,DEBUG,PUT,DELETE,PATC*,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0"/>
      <add name="ExtensionlessUrl*andler-ISAPI-4.0_64bit" path="*." verb="GET,*EAD,POST,DEBUG,PUT,DELETE,PATC*,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0"/>
      <add name="ExtensionlessUrl*andler-Integrated-4.0" path="*." verb="GET,*EAD,POST,DEBUG,PUT,DELETE,PATC*,OPTIONS" type="System.Web.*andlers.TransferRequest*andler" preCondition="integratedMode,runtimeVersionv4.0"/>
    </handlers>
  </system.webServer>
  <connectionStrings>
    <!--<add name="AfterMarketERP" connectionString="Data Source=QITBLRPRDSERVER\PRODUCTS_testing; Initial Catalog=FSMGOLD; User ID=sa;password=$testing;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <!--<add name="FSMGOLD" connectionString="Data Source=QITBLRPRDSERVER\PRODUCTS_TESTING; Initial Catalog=VECV_*elpdesk; User ID=hosting;password=*$osting;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <!--<add name="FSMGOLD" connectionString="Data Source=QITBLRcsserver\wip; Initial Catalog=*****_trial; User ID=*****;password=*****;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <!--<add name="FSMGOLD" connectionString="Data Source=QITBLRPRDSRV\PRODUCTS_STAGING; Initial Catalog=*****_Trial; User ID=sa;password=****$13;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <!--<add name="DBConnection" connectionString="Data Source=QITBLRPRDSERVER\SQL2012; Initial Catalog=CallCenter; User ID=*****;password=*****;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <!--<add name="FSMGOLD" connectionString="Data Source=QITBLRSDSERVER\SQL2014; Initial Catalog=ERP_Prevost_GUI; User ID=sa;password=*********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <add name="FSMGOLD" connectionString="Data Source=quest-demo-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=QIPL_ERP_Demo; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient"/>
    <!--<add name="VECV" connectionString="Data Source=QITBLRPRDSERVER\PRODUCTS_TESTING; Initial Catalog=VECV_TT; User ID=*osting;password=*$osting;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
    <!--<add name="Import" connectionString="Data Source=QITBLRPRDSERVER\WIP_SQL2012; Initial Catalog=VECV_STMS; User ID=devuser;password=*****" providerName="System.Data.SqlClient" />-->
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework"/>
  </entityFramework>
</configuration>