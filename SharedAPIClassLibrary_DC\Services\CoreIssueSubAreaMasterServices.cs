﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreIssueSubAreaMasterServices
    {

        #region ::: Select Reference Master /Mithun:::
        /// <summary>
        /// To Select Reference Master
        /// </summary>
        /// <param name="connstring"></param>
        /// <param name="SelectReferenceMasterObj"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectReferenceMaster(string connstring, SelectReferenceMasterList SelectReferenceMasterObj, int LogException)
        {
            var Masterdata = new object();
            List<dynamic> referenceMasterData = new List<dynamic>();
            try
            {
                int CompanyID = Convert.ToInt32(SelectReferenceMasterObj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);
                string userLanguageCode = SelectReferenceMasterObj.UserLanguageCode.ToString();
                string generalLanguageCode = SelectReferenceMasterObj.GeneralLanguageCode.ToString();


                using (SqlConnection connection = new SqlConnection(connstring))
                {
                    connection.Open();

                    // Retrieve Reference Master Detail
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetReferenceMasterDetail", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@IsCompanySpecific", userLanguageCode == generalLanguageCode ? 0 : 1);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                referenceMasterData.Add(new
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }

                    // Retrieve Reference Master Detail Locale if different language
                    if (userLanguageCode != generalLanguageCode)
                    {
                        referenceMasterData.Clear(); // Clear the previous list as we're fetching localized data

                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetReferenceMasterDetailLocale", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", Language_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    referenceMasterData.Add(new
                                    {
                                        ID = reader.GetInt32(0),
                                        Name = reader.GetString(1)
                                    });
                                }
                            }
                        }
                    }
                }

                Masterdata = new
                {
                    ReferenceMasterData = referenceMasterData.OrderBy(x => x.Name)
                };

                //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                Masterdata = new
                {
                    ReferenceMasterData = "Error"
                };
                return new JsonResult(Masterdata);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                Masterdata = new
                {
                    ReferenceMasterData = "Error"
                };
            }
            return new JsonResult(referenceMasterData);
        }
        #endregion

        #region ::: CheckIssueSubArea /Mithun:::
        /// <summary>
        /// To Check IssueSubArea_Description already exists 
        /// </summary>
        public static IActionResult CheckIssueSubArea(CheckIssueSubAreaList CheckIssueSubAreaObj, string constring, int LogException)
        {
            int Count = 0;
            string Val = CheckIssueSubAreaObj.Val.Trim();
            int CompanyId = 0;
            CompanyId = Convert.ToInt32(CheckIssueSubAreaObj.Company_ID);
            try
            {
                Val = Uri.UnescapeDataString(Val);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_AMERP_CheckIssueSubArea", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Val", Val);
                        cmd.Parameters.AddWithValue("@IssueAreaID", CheckIssueSubAreaObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@ColumnName", CheckIssueSubAreaObj.ColumnName);
                        cmd.Parameters.AddWithValue("@IssueSubAreaID", CheckIssueSubAreaObj.IssueSubAreaID);
                        cmd.Parameters.AddWithValue("@EnquiryType_ID", CheckIssueSubAreaObj.EnquiryType_ID);
                        cmd.Parameters.AddWithValue("@CompanyId", CompanyId);

                        SqlParameter outputParam = new SqlParameter("@Count", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputParam);

                        cmd.ExecuteNonQuery();
                        Count = (int)outputParam.Value;
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            return new JsonResult(Count);
        }

        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To Select IssueSubArea for a IssueArea
        /// </summary>
        public static IActionResult Select(SelectList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            string AppPath = string.Empty;
            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectObj.Company_ID);
                string YesE = CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "no").ToString();

                List<IssueSubAreaMaster> issueSubAreaMasters = new List<IssueSubAreaMaster>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetIssueSubAreas", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IssueAreaID", SelectObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectObj.LanguageID);
                        cmd.Parameters.AddWithValue("@EnquiryType_ID", SelectObj.EnquiryType_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var issueSubArea = new IssueSubAreaMaster
                                {
                                    IssueSubArea_ID = reader.GetInt32(reader.GetOrdinal("IssueSubArea_ID")),
                                    IssueSubArea_ShortName = SelectObj.LanguageID == Convert.ToInt32(SelectObj.GeneralLanguageID) ? reader.GetString(reader.GetOrdinal("IssueSubArea_ShortName")) : reader.IsDBNull(reader.GetOrdinal("Locale_ShortName")) ? reader.GetString(reader.GetOrdinal("IssueSubArea_ShortName")) : reader.GetString(reader.GetOrdinal("Locale_ShortName")),
                                    IssueSubArea_Description = SelectObj.LanguageID == Convert.ToInt32(SelectObj.GeneralLanguageID) ? reader.GetString(reader.GetOrdinal("IssueSubArea_Description")) : reader.IsDBNull(reader.GetOrdinal("Locale_Description")) ? reader.GetString(reader.GetOrdinal("IssueSubArea_Description")) : reader.GetString(reader.GetOrdinal("Locale_Description")),
                                    IssueSubArea_IsActive = reader.GetBoolean(reader.GetOrdinal("IssueSubArea_IsActive")) ? (SelectObj.LanguageID == Convert.ToInt32(SelectObj.GeneralLanguageID) ? YesE : YesL) : (SelectObj.LanguageID == Convert.ToInt32(SelectObj.GeneralLanguageID) ? NoE : NoL)
                                };
                                issueSubAreaMasters.Add(issueSubArea);
                            }
                        }
                    }
                }

                IQueryable<IssueSubAreaMaster> queryableList = issueSubAreaMasters.AsQueryable();

                if (_search)
                {
                    // Perform double decryption on the 'filters' string
                    string decryptedFilters = Common.DecryptString(Common.DecryptString(filters));

                    // Parse the decrypted string to Filters object
                    Filters filtersObj = JObject.Parse(decryptedFilters).ToObject<Filters>();

                    // Apply filtering if there are rules present
                    if (filtersObj.rules.Count() > 0)
                    {
                        queryableList = queryableList.FilterSearch<IssueSubAreaMaster>(filtersObj);
                    }
                }
                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse((Query)).ToObject<AdvanceFilter>();
                    queryableList = queryableList.AdvanceSearch<IssueSubAreaMaster>(advnfilter);
                }

                queryableList = queryableList.OrderByField<IssueSubAreaMaster>(sidx, sord);

                Count = queryableList.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                var jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in queryableList.AsEnumerable()
                            select new
                            {
                                ID = a.IssueSubArea_ID,
                                edit = "<a title='Edit' href='#' id='" + a.IssueSubArea_ID + "' key='" + a.IssueSubArea_ID + "' class='ISubAreaEdit font-icon-class' editmode='false'  ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.IssueSubArea_ID + "' defaultchecked=''  id='chk" + a.IssueSubArea_ID + "' class='ISubAreaDelete'/>",
                                IssueSubArea_ShortName = (a.IssueSubArea_ShortName),
                                IssueSubArea_Description = (a.IssueSubArea_Description),
                                IssueSubArea_IsActive = a.IssueSubArea_IsActive,
                                Locale = "<a key='" + a.IssueSubArea_ID + "' src='" + AppPath + "/Content/local.png' class='IssueSubAreaLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>",
                                View = "<img id='" + a.IssueSubArea_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.IssueSubArea_ID + "' class='ViewIssueSubAreaLocale'/>",
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    // filter = Request.Params["filters"],
                    //advanceFilter = Request.Params["Query"]
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }



        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Issue Sub Area
        /// </summary>
        public static IActionResult Delete(DeleteLista DeleteObj, string constring, int LogException)
        {
            string ErrorMsg = string.Empty;
            try
            {
                int Company_ID = Convert.ToInt32(DeleteObj.Company_ID);
                JObject jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //GNM_User User = DeleteObj.UserDeatails.FirstOrDefault();
                int ID = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        ID = (int)jObj["rows"].ElementAt(i)["id"];

                        string deleteIssueSubAreaQuery = "DELETE FROM HD_IssueSubArea WHERE IssueSubArea_ID = @ID AND Company_ID = @Company_ID";
                        string deleteIssueSubAreaLocaleQuery = "DELETE FROM HD_IssueSubAreaLocale WHERE IssueSubArea_ID = @ID";

                        using (SqlCommand cmd = new SqlCommand(deleteIssueSubAreaQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@ID", ID);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            cmd.ExecuteNonQuery();
                        }

                        using (SqlCommand cmd = new SqlCommand(deleteIssueSubAreaLocaleQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@ID", ID);
                            cmd.ExecuteNonQuery();
                        }

                        // Assuming you have a method to insert GPS details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(DeleteObj.Company_ID.ToString()),
                        //    Convert.ToInt32(DeleteObj.Branch),
                        //    User.User_ID,
                        //    Common.GetObjectID("CoreIssueSubAreaMaster",constring),
                        //    ID,
                        //    0,
                        //    0,
                        //    "Deleted Issue SubArea",
                        //    false,
                        //    Convert.ToInt32(DeleteObj.MenuID),
                        //    Convert.ToDateTime(DeleteObj.LoggedINDateTime)
                        //);
                    }
                }

                ErrorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ErrorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            catch (Exception ex)
            {
                // Handle other potential exceptions
                ErrorMsg += ex.Message;
            }
            //return ErrorMsg;
            return new JsonResult(ErrorMsg);
        }
        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        public static IActionResult Save(SaveLista SaveObj, string constring, int LogException)
        {
            string ErrorMsg = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(SaveObj.data);
                int Count = jObj["rows"].Count();
                int CompanyID = Convert.ToInt32(SaveObj.Company_ID);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SaveObj.UserDeatails.FirstOrDefault();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        int IssueSubArea_ID = Convert.ToInt32((string)jObj["rows"].ElementAt(i)["IssueSubArea_ID"] ?? "0");
                        int IssueArea_ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["IssueArea_ID"]);
                        int EnquiryType_ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["EnquiryType_ID"]);
                        string IssueSubArea_ShortName = Common.DecryptString((string)jObj["rows"].ElementAt(i)["IssueSubArea_ShortName"]);
                        string IssueSubArea_Description = Common.DecryptString((string)jObj["rows"].ElementAt(i)["IssueSubArea_Description"]);
                        bool IssueSubArea_IsActive = Convert.ToBoolean(jObj["rows"].ElementAt(i)["IssueSubArea_IsActive"]);

                        string issueSubAreaXML = $@"
                    <IssueSubArea>
                        <IssueSubArea_ID>{IssueSubArea_ID}</IssueSubArea_ID>
                        <Company_ID>{CompanyID}</Company_ID>
                        <EnquiryType_ID>{EnquiryType_ID}</EnquiryType_ID>
                        <IssueSubArea_ShortName>{IssueSubArea_ShortName}</IssueSubArea_ShortName>
                        <IssueSubArea_Description>{IssueSubArea_Description}</IssueSubArea_Description>
                        <IssueSubArea_IsActive>{IssueSubArea_IsActive}</IssueSubArea_IsActive>
                        <IssueArea_ID>{IssueArea_ID}</IssueArea_ID>
                    </IssueSubArea>";

                        string selectQuery = "SELECT * FROM HD_IssueSubArea WHERE IssueSubArea_ID = @IssueSubArea_ID AND Company_ID = @CompanyID";
                        using (SqlCommand selectCmd = new SqlCommand(selectQuery, conn))
                        {
                            selectCmd.Parameters.AddWithValue("@IssueSubArea_ID", IssueSubArea_ID);
                            selectCmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                            SqlDataReader reader = selectCmd.ExecuteReader();
                            if (reader.HasRows)
                            {
                                // Update existing record
                                reader.Close();
                                using (SqlCommand updateCmd = new SqlCommand("UP_UPD_AMERP_UpdateIssueSubAreaXML", conn))
                                {
                                    updateCmd.CommandType = CommandType.StoredProcedure;
                                    updateCmd.Parameters.AddWithValue("@IssueSubAreaXML", issueSubAreaXML);

                                    updateCmd.ExecuteNonQuery();
                                }

                                // gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), User.User_ID, Common.GetObjectID("CoreIssueSubAreaMaster",constring), IssueSubArea_ID, 0, 0, "Updated Issue SubArea- " + IssueSubArea_Description, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));
                            }
                            else
                            {
                                // Insert new record
                                reader.Close();
                                using (SqlCommand insertCmd = new SqlCommand("UP_INS_AMEERP_InsertIssueSubAreaXML", conn))
                                {
                                    insertCmd.CommandType = CommandType.StoredProcedure;
                                    insertCmd.Parameters.AddWithValue("@IssueSubAreaXML", issueSubAreaXML);

                                    SqlParameter newIdParam = new SqlParameter("@NewIssueSubAreaID", SqlDbType.Int)
                                    {
                                        Direction = ParameterDirection.Output
                                    };
                                    insertCmd.Parameters.Add(newIdParam);

                                    insertCmd.ExecuteNonQuery();
                                    int newIssueSubAreaID = Convert.ToInt32(newIdParam.Value);

                                    // gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), User.User_ID, Common.GetObjectID("CoreIssueSubAreaMaster",constring), newIssueSubAreaID, 0, 0, "Inserted Issue SubArea- " + IssueSubArea_Description, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));
                                }
                            }
                        }
                    }
                }

                ErrorMsg = "Saved";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                ErrorMsg = string.Empty;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                ErrorMsg = string.Empty;
            }
            //return ErrorMsg;
            return new JsonResult(ErrorMsg);

        }


        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// To update local
        /// </summary>
        /// <param name="UpdateLocaleObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult UpdateLocale(UpdateLocaleList UpdateLocaleObj, string constring, int LogException)
        {
            int IssueSubAreaLocale_ID = 0;
            var x = new { IssueSubAreaLocale_ID = IssueSubAreaLocale_ID };

            try
            {

                // Parse JSON data from request
                JObject jObj = JObject.Parse(UpdateLocaleObj.data);
                HD_IssueSubAreaLocale ISARow = jObj.ToObject<HD_IssueSubAreaLocale>();

                // Decrypt fields
                ISARow.IssueSubArea_ShortName = Common.DecryptString(ISARow.IssueSubArea_ShortName);
                ISARow.IssueSubArea_Description = Common.DecryptString(ISARow.IssueSubArea_Description);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = connection;

                    if (ISARow.IssueSubAreaLocale_ID != 0)
                    {
                        // Update existing record using stored procedure
                        cmd.CommandText = "UP_UPD_AMERP_UpdateIssueSubAreaLocale";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IssueSubAreaLocale_ID", ISARow.IssueSubAreaLocale_ID);
                        cmd.Parameters.AddWithValue("@IssueSubArea_ShortName", ISARow.IssueSubArea_ShortName);
                        cmd.Parameters.AddWithValue("@IssueSubArea_Description", ISARow.IssueSubArea_Description);
                        cmd.ExecuteNonQuery();

                        IssueSubAreaLocale_ID = ISARow.IssueSubAreaLocale_ID;
                    }
                    else
                    {
                        // Insert new record using stored procedure
                        cmd.CommandText = "UP_INS_AMERP_InsertIssueSubAreaLocale";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IssueSubArea_ShortName", ISARow.IssueSubArea_ShortName);
                        cmd.Parameters.AddWithValue("@IssueSubArea_Description", ISARow.IssueSubArea_Description);

                        SqlParameter outputParam = new SqlParameter("@IssueSubAreaLocale_ID", SqlDbType.Int);
                        outputParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(outputParam);

                        cmd.ExecuteNonQuery();

                        IssueSubAreaLocale_ID = Convert.ToInt32(outputParam.Value);
                    }

                    // Optionally log details here using ADO.NET or another logging mechanism

                    x = new { IssueSubAreaLocale_ID = IssueSubAreaLocale_ID };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log exception to text file or other log repository
                    // Example: LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }

        #endregion

        #region ::: CheckDefectIssueSubAreaLocale /Mithun:::
        /// <summary>
        /// To Check Defect Issue SubArea Locale
        /// </summary>
        /// <param name="IssueSubArea_Description"></param>
        /// <param name="IssueSubArea_ShortName"></param>
        /// <param name="IssueArea_ID"></param>
        /// <param name="IssueSubAreaLocale_ID"></param>
        /// <returns></returns>
        public static IActionResult CheckDefectIssueSubAreaLocale(CheckDefectIssueSubAreaLocaleList CheckDefectIssueSubAreaLocaleObj, string constring, int LogException)
        {
            int Count = 0;

            try
            {
                string IssueAreaDescription = Common.DecryptString(CheckDefectIssueSubAreaLocaleObj.IssueSubArea_Description);
                string IssueAreaShortName = Common.DecryptString(CheckDefectIssueSubAreaLocaleObj.IssueSubArea_ShortName);
                int Language_ID = Convert.ToInt32(CheckDefectIssueSubAreaLocaleObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_CheckIssueSubAreaLocale", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.Add(new SqlParameter("@IssueArea_ID", SqlDbType.Int) { Value = CheckDefectIssueSubAreaLocaleObj.IssueArea_ID });
                        cmd.Parameters.Add(new SqlParameter("@IssueSubArea_ShortName", SqlDbType.NVarChar, 100) { Value = IssueAreaShortName });
                        cmd.Parameters.Add(new SqlParameter("@IssueSubArea_Description", SqlDbType.NVarChar, 200) { Value = IssueAreaDescription });
                        cmd.Parameters.Add(new SqlParameter("@IssueSubAreaLocale_ID", SqlDbType.Int) { Value = CheckDefectIssueSubAreaLocaleObj.IssueSubAreaLocale_ID });
                        cmd.Parameters.Add(new SqlParameter("@Language_ID", SqlDbType.Int) { Value = Language_ID });

                        SqlParameter resultParam = new SqlParameter("@Result", SqlDbType.Int);
                        resultParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(resultParam);

                        cmd.ExecuteNonQuery();

                        Count = (int)resultParam.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle other exceptions or rethrow as needed
            }

            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region ::: SelectParticularIssueSubArea :::
        /// <summary>
        /// To Select Particular IssueSubArea
        /// </summary>
        /// <param name="IssueSubAreaID"></param>
        /// <returns></returns>
        public static IActionResult SelectParticularIssueSubArea(SelectParticularIssueSubAreaList SelectParticularIssueSubAreaObj, string constring, int LogException)
        {
            var x = new object();
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularIssueSubAreaObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch IssueSubAreaList
                    string queryIssueSubArea = "SELECT * FROM HD_IssueSubArea WHERE IssueSubArea_ID = @IssueSubAreaID";
                    SqlCommand commandIssueSubArea = new SqlCommand(queryIssueSubArea, connection);
                    commandIssueSubArea.Parameters.AddWithValue("@IssueSubAreaID", SelectParticularIssueSubAreaObj.IssueSubAreaID);
                    SqlDataReader readerIssueSubArea = commandIssueSubArea.ExecuteReader();
                    HD_IssueSubArea IssueSubAreaList = null;
                    if (readerIssueSubArea.Read())
                    {
                        IssueSubAreaList = new HD_IssueSubArea
                        {
                            IssueSubArea_ID = Convert.ToInt32(readerIssueSubArea["IssueSubArea_ID"]),
                            IssueSubArea_ShortName = readerIssueSubArea["IssueSubArea_ShortName"].ToString(),
                            IssueSubArea_Description = readerIssueSubArea["IssueSubArea_Description"].ToString(),
                            IssueSubArea_IsActive = Convert.ToBoolean(readerIssueSubArea["IssueSubArea_IsActive"]),
                            IssueArea_ID = Convert.ToInt32(readerIssueSubArea["IssueArea_ID"])
                        };
                    }
                    readerIssueSubArea.Close();

                    // Fetch IssueSubAreaLocaleList
                    string queryIssueSubAreaLocale = "SELECT * FROM HD_IssueSubAreaLocale WHERE IssueSubArea_ID = @IssueSubAreaID AND Language_ID = @LanguageID";
                    SqlCommand commandIssueSubAreaLocale = new SqlCommand(queryIssueSubAreaLocale, connection);
                    commandIssueSubAreaLocale.Parameters.AddWithValue("@IssueSubAreaID", SelectParticularIssueSubAreaObj.IssueSubAreaID);
                    commandIssueSubAreaLocale.Parameters.AddWithValue("@LanguageID", Language_ID);
                    SqlDataReader readerIssueSubAreaLocale = commandIssueSubAreaLocale.ExecuteReader();
                    HD_IssueSubAreaLocale IssueSubAreaLocaleList = null;
                    if (readerIssueSubAreaLocale.Read())
                    {
                        IssueSubAreaLocaleList = new HD_IssueSubAreaLocale
                        {
                            IssueSubAreaLocale_ID = Convert.ToInt32(readerIssueSubAreaLocale["IssueSubAreaLocale_ID"]),
                            IssueSubArea_Description = readerIssueSubAreaLocale["IssueSubArea_Description"].ToString(),
                            IssueSubArea_ShortName = readerIssueSubAreaLocale["IssueSubArea_ShortName"].ToString()
                        };
                    }
                    readerIssueSubAreaLocale.Close();

                    // Create anonymous object x
                    x = new
                    {
                        IssueSubArea_ID = IssueSubAreaList?.IssueSubArea_ID,
                        IssueSubArea_ShortName = IssueSubAreaList?.IssueSubArea_ShortName,
                        IssueSubArea_Description = IssueSubAreaList?.IssueSubArea_Description,
                        IssueSubArea_IsActive = IssueSubAreaList?.IssueSubArea_IsActive,
                        IssueArea_ID = IssueSubAreaList?.IssueArea_ID,
                        IssueSubAreaLocaleID = IssueSubAreaLocaleList?.IssueSubAreaLocale_ID.ToString() ?? "",
                        IssueSubAreaNameLocale = IssueSubAreaLocaleList?.IssueSubArea_Description ?? "",
                        IssueSubAreaShortNameLocale = IssueSubAreaLocaleList?.IssueSubArea_ShortName ?? ""
                    };
                }

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        #endregion


        #region:::Data For Export /:::

        public static IQueryable<IssueSubAreaMaster> GetIssueSubAreaMasterList(SelectList ExportObj, string constring)
        {
            List<IssueSubAreaMaster> issueSubAreaMasters = new List<IssueSubAreaMaster>();


            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetIssueSubAreas", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@IssueAreaID", ExportObj.IssueAreaID);
                    cmd.Parameters.AddWithValue("@CompanyID", ExportObj.Company_ID);
                    cmd.Parameters.AddWithValue("@LanguageID", ExportObj.LanguageID);
                    cmd.Parameters.AddWithValue("@EnquiryType_ID", ExportObj.EnquiryType_ID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var issueSubArea = new IssueSubAreaMaster
                            {
                                IssueSubArea_ID = reader.GetInt32(reader.GetOrdinal("IssueSubArea_ID")),
                                IssueSubArea_ShortName = ExportObj.LanguageID == ExportObj.GeneralLanguageID
                                    ? reader.GetString(reader.GetOrdinal("IssueSubArea_ShortName"))
                                    : reader.IsDBNull(reader.GetOrdinal("Locale_ShortName"))
                                        ? reader.GetString(reader.GetOrdinal("IssueSubArea_ShortName"))
                                        : reader.GetString(reader.GetOrdinal("Locale_ShortName")),
                                IssueSubArea_Description = ExportObj.LanguageID == ExportObj.GeneralLanguageID
                                    ? reader.GetString(reader.GetOrdinal("IssueSubArea_Description"))
                                    : reader.IsDBNull(reader.GetOrdinal("Locale_Description"))
                                        ? reader.GetString(reader.GetOrdinal("IssueSubArea_Description"))
                                        : reader.GetString(reader.GetOrdinal("Locale_Description")),
                                IssueSubArea_IsActive = reader.GetBoolean(reader.GetOrdinal("IssueSubArea_IsActive"))
                                    ? (ExportObj.LanguageID == ExportObj.GeneralLanguageID ? "Yes" : "Oui")  // Replace Yes/No with your localized strings
                                    : (ExportObj.LanguageID == ExportObj.GeneralLanguageID ? "No" : "Non")
                            };
                            issueSubAreaMasters.Add(issueSubArea);
                        }
                    }
                }
            }

            // Return the list as IQueryable for further use
            return issueSubAreaMasters.AsQueryable();
        }

        #endregion



        #region::: Export /Mithun:::

        public static async Task<object> Export(SelectList ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            DataTable Dt = new DataTable();
            int Count = 0;

            try
            {
                // Fetch the queryable list using the new method
                IQueryable<IssueSubAreaMaster> queryableList = GetIssueSubAreaMasterList(ExportObj, constring);

                // Apply filters
                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    string decryptedFilters = Common.DecryptString(Common.DecryptString(filters));

                    // Parse the decrypted string to Filters object
                    Filters filtersObj = JObject.Parse(decryptedFilters).ToObject<Filters>();

                    // Apply filtering if there are rules present
                    if (filtersObj.rules.Count() > 0)
                    {
                        queryableList = queryableList.FilterSearch<IssueSubAreaMaster>(filtersObj);
                    }
                }

                // Apply advanced search filters if provided
                if (!string.IsNullOrEmpty(Query) && Query != "null" && Query != "undefined")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    queryableList = queryableList.AdvanceSearch<IssueSubAreaMaster>(advnfilter);
                }

                // Apply sorting
                queryableList = queryableList.OrderByField<IssueSubAreaMaster>(sidx, sord);

                // Define the DataTable columns once
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "code"));
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "IssueSubArea"));
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active"));

                // Prepare data for export
                var issueSubAreaArray = queryableList.Select(a => new
                {
                    a.IssueSubArea_ShortName,
                    a.IssueSubArea_Description,
                    a.IssueSubArea_IsActive
                }).ToList();

                Count = issueSubAreaArray.Count;

                // If data is found, populate the DataTable
                if (Count > 0)
                {
                    foreach (var issue in issueSubAreaArray)
                    {
                        Dt.Rows.Add(issue.IssueSubArea_ShortName, issue.IssueSubArea_Description, issue.IssueSubArea_IsActive);
                    }

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "code").ToString());
                    DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "IssueSubArea").ToString());
                    DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                    DtAlignment.Rows.Add(0, 0, 0);

                    // Create additional DataTable for report
                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "EnquiryType"));
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea"));

                    Dt1.Rows.Add(Common.DecryptString(ExportObj.EnquiryTypeName), Common.DecryptString(ExportObj.IssueAreaName));

                    // Create report export object
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID,
                        Branch = ExportObj.Branch.ToString(),
                        GeneralLanguageID = ExportObj.GeneralLanguageID,
                        UserLanguageID = ExportObj.LanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "IssueArea",
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea"),
                        exprtType = ExportObj.exprtType,
                        UserCulture = ExportObj.UserCulture
                    };

                    // Export the report and return the result
                    var res = await ReportExport.Export(reportExportList, constring, LogException);
                    return res.Value;
                }

                // Return a default message if no data was exported
                return "No data available for export.";
            }
            catch (Exception ex)
            {
                // Log the exception if necessary
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Return null to indicate failure
                return null;
            }
        }



        #endregion
    }
    public class SelectParticularIssueSubAreaList
    {
        public int IssueSubAreaID { get; set; }
        public int UserLanguageID { get; set; }
    }
    public class CheckDefectIssueSubAreaLocaleList
    {
        public string IssueSubArea_Description { get; set; }
        public string IssueSubArea_ShortName { get; set; }
        public int IssueArea_ID { get; set; }
        public int IssueSubAreaLocale_ID { get; set; }
        public int UserLanguageID { get; set; }
    }

    public class UpdateLocaleLista
    {
        public string data { get; set; }
    }
    public class SaveLista
    {
        public List<GNM_User> UserDeatails { get; set; }
        public string data { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }

    public class DeleteLista
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int LanguageID { get; set; }
        public int MenuID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
        public string key { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public List<GNM_User> UserDeatails { get; set; }
    }
    public class SelectList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public int exprtType { get; set; }
        public int IssueAreaID { get; set; }
        public int EnquiryType_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int LanguageID { get; set; }
        public string EnquiryTypeName { get; set; }
        public string IssueAreaName { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string filters { get; set; }
        public string Query { get; set; }
        public bool _search { get; set; }
        public bool advnce { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
    }
    public class CheckIssueSubAreaList
    {
        public int Company_ID { get; set; }
        public int IssueAreaID { get; set; }
        public int IssueSubAreaID { get; set; }
        public int EnquiryType_ID { get; set; }
        public string Val { get; set; }
        public string ColumnName { get; set; }
    }

    public class IssueSubAreaMaster
    {
        public int IssueSubArea_ID
        {
            get;
            set;
        }

        public string IssueSubArea_ShortName
        {
            get;
            set;
        }

        public string IssueSubArea_Description
        {
            get;
            set;
        }

        public string IssueSubArea_IsActive
        {
            get;
            set;
        }

        public int Company_ID
        {
            get;
            set;
        }
    }

    public class SelectReferenceMasterList
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
    }
    public partial class HD_IssueSubAreaLocale
    {
        public int IssueSubAreaLocale_ID { get; set; }
        public int IssueSubArea_ID { get; set; }
        public string IssueSubArea_ShortName { get; set; }
        public string IssueSubArea_Description { get; set; }
        public int Language_ID { get; set; }
        public bool IssueSubArea_IsActive { get; set; }
        public Nullable<int> EnquiryType_ID { get; set; }

        public virtual HD_IssueSubArea HD_IssueSubArea { get; set; }
    }
    public partial class HD_IssueSubArea
    {
        public HD_IssueSubArea()
        {
            this.HD_IssueSubAreaLocale = new HashSet<HD_IssueSubAreaLocale>();
        }

        public int IssueSubArea_ID { get; set; }
        public int IssueArea_ID { get; set; }
        public string IssueSubArea_ShortName { get; set; }
        public string IssueSubArea_Description { get; set; }
        public bool IssueSubArea_IsActive { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> EnquiryType_ID { get; set; }

        public virtual ICollection<HD_IssueSubAreaLocale> HD_IssueSubAreaLocale { get; set; }
    }
}
