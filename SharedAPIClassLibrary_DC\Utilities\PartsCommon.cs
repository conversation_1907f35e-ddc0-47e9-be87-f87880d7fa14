﻿using AMMSCore.Models;
using DocumentFormat.OpenXml.Office.Word;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Services;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class PartsCommon
    {
        #region Vinay N
        /// <summary>
        /// Save_BL
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="JsonObj"></param>
        /// <returns></returns>
        public static IActionResult Save_BL(Save_BLPartsInvoiceList Obj, string connString, int LogException, List<dynamic> JsonObj)
        {

            var jsonData = default(dynamic);
            using (System.Transactions.TransactionScope scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required))
            {
                try
                {

                    
                    int companyID = Obj.Company_ID;
                    int userid = Obj.User_ID;
                    int branchID = Convert.ToInt32(JsonObj.ElementAt(1));

                    List<StockLessPartDetails> Mainstockchangesparts = new List<StockLessPartDetails>();

                    int StockChanged = 0;
                    JObject jobj = JObject.Parse(JsonObj.ElementAt(3));
                    PRT_PurchaseInvoice headerRow = jobj.ToObject<PRT_PurchaseInvoice>();
                    headerRow.PurchaseInvoiceNumber = Common.DecryptString(headerRow.PurchaseInvoiceNumber);
                    headerRow.SupplierInvoiceNumber = Common.DecryptString(headerRow.SupplierInvoiceNumber);
                    headerRow.PurchaseInvoiceDate = DateTime.Now;
                    headerRow.Remarks = Common.DecryptString(headerRow.Remarks == null ? "" : headerRow.Remarks);
                    headerRow.Company_ID = companyID;
                    headerRow.Branch_ID = branchID;
                    headerRow.Updated_By = userid;
                    headerRow.Updated_Date = DateTime.Now;
                    headerRow.SupplierInvoiceDate = headerRow.SupplierInvoiceDate == default(DateTime) ? null : headerRow.SupplierInvoiceDate;
                    headerRow.TaxableOtherCharges = Common.DecryptString(headerRow.TaxableOtherCharges);
                    headerRow.NonTaxableOtherCharges = Common.DecryptString(headerRow.NonTaxableOtherCharges);
                    headerRow.IsDealer = headerRow.IsDealer;
                    headerRow.Roundoff = headerRow.Roundoff;
                    headerRow.TotalPIAmount = headerRow.TotalPIAmount;
                    int wareHouseCounter = 0;
                    for (int i = 0; i < headerRow.PRT_PurchaseInvoicePartsDetails.Count; i++)
                    {
                        int ID = 0;
                        int purchaseOrderID = headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(i).PurchaseOrder_ID != null ? headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(i).PurchaseOrder_ID.Value : 0;
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = @"
                                SELECT TOP 1 WareHouse_ID 
                                FROM PRT_PurchaseOrder 
                                WHERE PurchaseOrder_ID = @PurchaseOrderID";


                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.Text;
                                    command.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);

                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    object result = command.ExecuteScalar();
                                    ID = result != null && result != DBNull.Value ? Convert.ToInt32(result) : 0;
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                
                            }
                        }
                        int wareHouseID = purchaseOrderID != 0 ? ID : headerRow.WareHouse_ID.HasValue ? headerRow.WareHouse_ID.Value : 0;
                        headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(i).WareHouse_ID = wareHouseID;

                        if (purchaseOrderID > 0)
                        {
                            wareHouseCounter++;
                        }
                    }
                    if (wareHouseCounter > 0)
                    {
                        headerRow.WareHouse_ID = null;
                        headerRow.PurchaseOrderClass_ID = null;
                    }

                    for (int y = 0; y < headerRow.PRT_PurchaseInvoicePartsDetails.Count(); y++)
                    {
                        decimal? Freestock = 0.0M;
                        int parts_ID = headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(y).Parts_ID;
                        int Warehouse_ID = headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(y).WareHouse_ID;
                        int PurchaseOrder_ID = Convert.ToInt32(headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(y).PurchaseOrder_ID);
                        if (PurchaseOrder_ID > 0)
                        {
                            decimal? PendingPPO = 0;
                            GNM_Parts Partsdetails = new GNM_Parts();
                            PRT_PurchaseOrderPartsDetail orderdetail = new PRT_PurchaseOrderPartsDetail();
                            using (SqlConnection conn2 = new SqlConnection(connString))
                            {
                                string query = @"
                                    SELECT TOP 1 
                                        PurchaseOrderPartsDetail_ID, 
                                        PurchaseOrder_ID, 
                                        Parts_ID, 
                                        SupplierPrice, 
                                        RequestedQuantity, 
                                        ApprovedQuantity, 
                                        InvoicedQuantity, 
                                        BackOrderCancelledQuantity, 
                                        DiscountPercentage, 
                                        DiscountAmount, 
                                        TaxStructure_ID, 
                                        TaxAmount, 
                                        Amount, 
                                        PartsOrder_ID, 
                                        DiscountedAmount, 
                                        MRP 
                                    FROM PRT_PurchaseOrderPartsDetail 
                                    WHERE PurchaseOrder_ID = @PurchaseOrder_ID 
                                      AND Parts_ID = @Parts_ID";


                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn2))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.Parameters.AddWithValue("@PurchaseOrder_ID", PurchaseOrder_ID);
                                        command.Parameters.AddWithValue("@Parts_ID", parts_ID);

                                        if (conn2.State == ConnectionState.Closed || conn2.State == ConnectionState.Broken)
                                        {
                                            conn2.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            // Check if a record exists
                                            if (reader.Read())
                                            {
                                                orderdetail = new PRT_PurchaseOrderPartsDetail
                                                {
                                                    PurchaseOrderPartsDetail_ID = Convert.ToInt32(reader["PurchaseOrderPartsDetail_ID"]),
                                                    PurchaseOrder_ID = Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                                    Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                    SupplierPrice = Convert.ToDecimal(reader["SupplierPrice"]),
                                                    RequestedQuantity = reader["RequestedQuantity"] as decimal?,
                                                    ApprovedQuantity = reader["ApprovedQuantity"] as decimal?,
                                                    InvoicedQuantity = reader["InvoicedQuantity"] as decimal?,
                                                    BackOrderCancelledQuantity = reader["BackOrderCancelledQuantity"] as decimal?,
                                                    DiscountPercentage = reader["DiscountPercentage"] as decimal?,
                                                    DiscountAmount = reader["DiscountAmount"] as decimal?,
                                                    TaxStructure_ID = reader["TaxStructure_ID"] as int?,
                                                    TaxAmount = reader["TaxAmount"] as decimal?,
                                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                                    PartsOrder_ID = reader["PartsOrder_ID"] as int?,
                                                    DiscountedAmount = reader["DiscountedAmount"] as decimal?,
                                                    MRP = reader["MRP"] as decimal?
                                                };
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                
                                }
                            }

                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = @"
                                    SELECT 
                                        Parts_ID, 
                                     
                                        Parts_PartPrefix, 
                                        Parts_PartsNumber, 
                                        Parts_PartsDescription, 
                                        Parts_AliasPartPrefix, 
                                        Parts_AliasPartNumber, 
                                        SuperceededPart_ID, 
                                        SuperceedingPart_ID, 
                                        MovementType_ID, 
                                        Parts_Weight, 
                                        Parts_Dimensions, 
                                        PartsCategory_ID, 
                                        PartsFunctionGroup_ID, 
                                        PartsCustomsCode_ID, 
                                        UnitOfMeasurement_ID, 
                                        SupersessionType_ID, 
                                        Parts_IsActive, 
                                        Parts_IsHazardousGood, 
                                        ModifiedBy, 
                                        ModifiedDate, 
                                        Company_ID, 
                                        Parts_IsLocal, 
                                        Parts_IsComponent, 
                                        IsKitPart, 
                                        ExciseDuty_ID, 
                                        SalvagePart_ID, 
                                        PartType, 
                                        AttachmentCount, 
                                        PartsDisposal_ID, 
                                        IntroductionDate, 
                                        LatestSupersession_ID, 
                                        CustomerWarrantyInDays, 
                                        ReadingLimit 
                                    FROM GNM_Parts 
                                    WHERE Parts_ID = @Parts_ID";


                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.Parameters.AddWithValue("@Parts_ID", parts_ID);

                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            // Check if a record exists
                                            if (reader.Read())
                                            {
                                                Partsdetails = new GNM_Parts
                                                {
                                                    Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                    
                                                    Parts_PartPrefix = reader["Parts_PartPrefix"] as string,
                                                    Parts_PartsNumber = reader["Parts_PartsNumber"] as string,
                                                    Parts_PartsDescription = reader["Parts_PartsDescription"] as string,
                                                    Parts_AliasPartPrefix = reader["Parts_AliasPartPrefix"] as string,
                                                    Parts_AliasPartNumber = reader["Parts_AliasPartNumber"] as string,
                                                    SuperceededPart_ID = reader["SuperceededPart_ID"] as int?,
                                                    SuperceedingPart_ID = reader["SuperceedingPart_ID"] as int?,
                                                    MovementType_ID = Convert.ToInt32(reader["MovementType_ID"]),
                                                    Parts_Weight = reader["Parts_Weight"] as decimal?,
                                                    Parts_Dimensions = reader["Parts_Dimensions"] as string,
                                                    PartsCategory_ID = reader["PartsCategory_ID"] as int?,
                                                    PartsFunctionGroup_ID = reader["PartsFunctionGroup_ID"] as int?,
                                                    PartsCustomsCode_ID = reader["PartsCustomsCode_ID"] as int?,
                                                    UnitOfMeasurement_ID = Convert.ToInt32(reader["UnitOfMeasurement_ID"]),
                                                    SupersessionType_ID = reader["SupersessionType_ID"] as int?,
                                                    Parts_IsActive = Convert.ToBoolean(reader["Parts_IsActive"]),
                                                    Parts_IsHazardousGood = Convert.ToBoolean(reader["Parts_IsHazardousGood"]),
                                                    ModifiedBy = Convert.ToInt32(reader["ModifiedBy"]),
                                                    ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                                    Parts_IsLocal = Convert.ToBoolean(reader["Parts_IsLocal"]),
                                                    Parts_IsComponent = Convert.ToBoolean(reader["Parts_IsComponent"]),
                                                    IsKitPart = Convert.ToBoolean(reader["IsKitPart"]),
                                                    ExciseDuty_ID = reader["ExciseDuty_ID"] as int?,
                                                    SalvagePart_ID = reader["SalvagePart_ID"] as int?,
                                                    PartType = reader["PartType"] as int?,
                                                    AttachmentCount = reader["AttachmentCount"] as byte?,
                                                    PartsDisposal_ID = reader["PartsDisposal_ID"] as int?,
                                                    IntroductionDate = reader["IntroductionDate"] as DateTime?,
                                                    LatestSupersession_ID = reader["LatestSupersession_ID"] as int?,
                                                    CustomerWarrantyInDays = reader["CustomerWarrantyInDays"] as int?,
                                                    ReadingLimit = reader["ReadingLimit"] as int?
                                                };
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                   
                                }
                            }

                            if (orderdetail != null)
                            {
                                PendingPPO = Convert.ToDecimal(orderdetail.ApprovedQuantity) - Convert.ToDecimal(orderdetail.InvoicedQuantity);
                            }


                            if (PendingPPO < Convert.ToDecimal(headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(y).Quantity))
                            {
                                StockLessPartDetails stockchangesparts = new StockLessPartDetails();
                                StockChanged = StockChanged + 1;
                                stockchangesparts.Parts_ID = parts_ID;
                                stockchangesparts.PartsPrefix = Partsdetails.Parts_PartPrefix;
                                stockchangesparts.PartNumber = Partsdetails.Parts_PartsNumber;
                                stockchangesparts.Parts = Partsdetails.Parts_PartsDescription;
                                stockchangesparts.CurrentStock = Convert.ToDecimal(PendingPPO);
                                stockchangesparts.Quantity = headerRow.PRT_PurchaseInvoicePartsDetails.ElementAt(y).Quantity;
                                Mainstockchangesparts.Add(stockchangesparts);
                            }

                        }
                    }
                    if (StockChanged == 0)
                    {


                        if (headerRow.PurchaseInvoice_ID == 0)
                        {

                            if (Common.CheckPreffixSuffix(companyID, Convert.ToInt32(JsonObj.ElementAt(1)), "PRT_PurchaseInvoice", connString, LogException))
                            {
                                
                                PRT_PurchaseInvoice dummy = new PRT_PurchaseInvoice();
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string insertInvoiceQuery = @"
                                        INSERT INTO PRT_PurchaseInvoice 
                                        (
                                            PurchaseInvoiceNumber,
                                            PurchaseInvoiceDate,
                                            Supplier_ID,
                                            Currency_ID,
                                            ExchangeRate,
                                            SupplierInvoiceNumber,
                                            SupplierInvoiceDate,
                                            IsImport,
                                            ModeOfShipment_ID,
                                            TotalInvoiceAmountInLocalCurrency,
                                            LandingCostFactor,
                                            Remarks,
                                            TotalAmount,
                                            DiscountPercentage,
                                            DiscountAmount,
                                            DiscountedAmount,
                                            TaxStructure_ID,
                                            TaxableOtherCharges,
                                            TaxablePercentage,
                                            TaxableOtherChargesAmount,
                                            TaxOnTaxableOtherCharges,
                                            TotalTaxableAmount,
                                            TaxAmount,
                                            NonTaxableOtherCharges,
                                            NonTaxablePercentage,
                                            NonTaxableOtherChargesAmount,
                                            TotalInvoiceAmount,
                                            DocumentNumber,
                                            FinancialYear,
                                            Company_ID,
                                            Branch_ID,
                                            Updated_By,
                                            Updated_Date,
                                            PurchaseGRN_ID,
                                            WareHouse_ID,
                                            PurchaseOrderClass_ID,
                                            ItemLevelTaxStructure_ID,
                                            IsDealer,
                                            Roundoff,
                                            TotalPIAmount
                                        )
                                        VALUES
                                        (
                                            @PurchaseInvoiceNumber,
                                            @PurchaseInvoiceDate,
                                            @Supplier_ID,
                                            @Currency_ID,
                                            @ExchangeRate,
                                            @SupplierInvoiceNumber,
                                            @SupplierInvoiceDate,
                                            @IsImport,
                                            @ModeOfShipment_ID,
                                            @TotalInvoiceAmountInLocalCurrency,
                                            @LandingCostFactor,
                                            @Remarks,
                                            @TotalAmount,
                                            @DiscountPercentage,
                                            @DiscountAmount,
                                            @DiscountedAmount,
                                            @TaxStructure_ID,
                                            @TaxableOtherCharges,
                                            @TaxablePercentage,
                                            @TaxableOtherChargesAmount,
                                            @TaxOnTaxableOtherCharges,
                                            @TotalTaxableAmount,
                                            @TaxAmount,
                                            @NonTaxableOtherCharges,
                                            @NonTaxablePercentage,
                                            @NonTaxableOtherChargesAmount,
                                            @TotalInvoiceAmount,
                                            @DocumentNumber,
                                            @FinancialYear,
                                            @Company_ID,
                                            @Branch_ID,
                                            @Updated_By,
                                            @Updated_Date,
                                            @PurchaseGRN_ID,
                                            @WareHouse_ID,
                                            @PurchaseOrderClass_ID,
                                            @ItemLevelTaxStructure_ID,
                                            @IsDealer,
                                            @Roundoff,
                                            @TotalPIAmount
                                        );
                                        SELECT SCOPE_IDENTITY();";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(insertInvoiceQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@PurchaseInvoiceNumber", headerRow.PurchaseInvoiceNumber ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@PurchaseInvoiceDate", headerRow.PurchaseInvoiceDate);
                                            cmd.Parameters.AddWithValue("@Supplier_ID", headerRow.Supplier_ID);
                                            cmd.Parameters.AddWithValue("@Currency_ID", headerRow.Currency_ID);
                                            cmd.Parameters.AddWithValue("@ExchangeRate", headerRow.ExchangeRate );
                                            cmd.Parameters.AddWithValue("@SupplierInvoiceNumber", headerRow.SupplierInvoiceNumber ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@SupplierInvoiceDate", headerRow.SupplierInvoiceDate ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@IsImport", headerRow.IsImport );
                                            cmd.Parameters.AddWithValue("@ModeOfShipment_ID", headerRow.ModeOfShipment_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TotalInvoiceAmountInLocalCurrency", headerRow.TotalInvoiceAmountInLocalCurrency );
                                            cmd.Parameters.AddWithValue("@LandingCostFactor", headerRow.LandingCostFactor );
                                            cmd.Parameters.AddWithValue("@Remarks", headerRow.Remarks ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TotalAmount", headerRow.TotalAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@DiscountPercentage", headerRow.DiscountPercentage ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@DiscountAmount", headerRow.DiscountAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@DiscountedAmount", headerRow.DiscountedAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxStructure_ID", headerRow.TaxStructure_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxableOtherCharges", headerRow.TaxableOtherCharges ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxablePercentage", headerRow.TaxablePercentage ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxableOtherChargesAmount", headerRow.TaxableOtherChargesAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxOnTaxableOtherCharges", headerRow.TaxOnTaxableOtherCharges ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TotalTaxableAmount", headerRow.TotalTaxableAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxAmount", headerRow.TaxAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@NonTaxableOtherCharges", headerRow.NonTaxableOtherCharges ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@NonTaxablePercentage", headerRow.NonTaxablePercentage ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@NonTaxableOtherChargesAmount", headerRow.NonTaxableOtherChargesAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TotalInvoiceAmount", headerRow.TotalInvoiceAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@DocumentNumber", headerRow.DocumentNumber ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@FinancialYear", headerRow.FinancialYear );
                                            cmd.Parameters.AddWithValue("@Company_ID", headerRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@Branch_ID", headerRow.Branch_ID);
                                            cmd.Parameters.AddWithValue("@Updated_By", headerRow.Updated_By);
                                            cmd.Parameters.AddWithValue("@Updated_Date", headerRow.Updated_Date);
                                            cmd.Parameters.AddWithValue("@PurchaseGRN_ID", headerRow.PurchaseGRN_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerRow.PurchaseOrderClass_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@ItemLevelTaxStructure_ID", headerRow.ItemLevelTaxStructure_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@IsDealer", headerRow.IsDealer ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Roundoff", headerRow.Roundoff ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TotalPIAmount", headerRow.TotalPIAmount);
                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            headerRow.PurchaseInvoice_ID = Convert.ToInt32(cmd.ExecuteScalar());
                                          
                                        }
                                        if (headerRow.PRT_PurchaseInvoicePartsDetails != null && headerRow.PRT_PurchaseInvoicePartsDetails.Count > 0)
                                        {
                                            string insertDetQuery = @"
                                                INSERT INTO PRT_PurchaseInvoicePartsDetails 
                                                (
                                                    PurchaseInvoice_ID,
                                                    PurchaseOrder_ID,
                                                    Parts_ID,
                                                    PartsOrder_ID,
                                                    Rate,
                                                    Quantity,
                                                    DiscountPercentage,
                                                    DiscountAmount,
                                                    TaxStructure_ID,
                                                    TaxAmount,
                                                    Amount,
                                                    LandingCostInLocalCurrency,
                                                    VarianceinRate,
                                                    DiscountedAmount,
                                                    WareHouse_ID,
                                                    MRP
                                                ) 
                                                VALUES 
                                                (
                                                    @PurchaseInvoice_ID,
                                                    @PurchaseOrder_ID,
                                                    @Parts_ID,
                                                    @PartsOrder_ID,
                                                    @Rate,
                                                    @Quantity,
                                                    @DiscountPercentage,
                                                    @DiscountAmount,
                                                    @TaxStructure_ID,
                                                    @TaxAmount,
                                                    @Amount,
                                                    @LandingCostInLocalCurrency,
                                                    @VarianceinRate,
                                                    @DiscountedAmount,
                                                    @WareHouse_ID,
                                                    @MRP
                                                )";
                                            using (cmd = new SqlCommand(insertDetQuery, conn))
                                            {
                                                foreach (var partDetail in headerRow.PRT_PurchaseInvoicePartsDetails)
                                                {
                                                    cmd.Parameters.Clear(); // Clear previous parameters to avoid issues during the next iteration

                                                    // Add parameters for the current partDetail
                                                    cmd.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);
                                                    cmd.Parameters.AddWithValue("@PurchaseOrder_ID", partDetail.PurchaseOrder_ID ?? (object)DBNull.Value); // Handle nullable fields
                                                    cmd.Parameters.AddWithValue("@Parts_ID", partDetail.Parts_ID);
                                                    cmd.Parameters.AddWithValue("@PartsOrder_ID", partDetail.PartsOrder_ID ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@Rate", partDetail.Rate);
                                                    cmd.Parameters.AddWithValue("@Quantity", partDetail.Quantity);
                                                    cmd.Parameters.AddWithValue("@DiscountPercentage", partDetail.DiscountPercentage ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@DiscountAmount", partDetail.DiscountAmount ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@TaxStructure_ID", partDetail.TaxStructure_ID ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@TaxAmount", partDetail.TaxAmount ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@Amount", partDetail.Amount);
                                                    cmd.Parameters.AddWithValue("@LandingCostInLocalCurrency", partDetail.LandingCostInLocalCurrency ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@VarianceinRate", partDetail.VarianceinRate ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@DiscountedAmount", partDetail.DiscountedAmount ?? (object)DBNull.Value);
                                                    cmd.Parameters.AddWithValue("@WareHouse_ID", partDetail.WareHouse_ID);
                                                    cmd.Parameters.AddWithValue("@MRP", partDetail.MRP ?? (object)DBNull.Value);

                                                
                                                    int rowsAffected = cmd.ExecuteNonQuery();
                                                }

                                                }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                      
                                    }
                                }

                                // gbl.InsertGPSDetails(companyID, branchID, userid, Convert.ToInt32(Common.GetObjectID("PRT_PurchaseInvoice")), headerRow.PurchaseInvoice_ID, 0, 0, "Insert", false, Convert.ToInt32(JsonObj.ElementAt(5)), Convert.ToDateTime(JsonObj.ElementAt(4)));
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string query = @"
                                        SELECT TOP 1 * 
                                        FROM PRT_PurchaseInvoice
                                        WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";


                                    SqlCommand command = null;

                                    try
                                    {
                                        using (command = new SqlCommand(query, conn))
                                        {
                                            command.CommandType = CommandType.Text;
                                            command.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);

                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = command.ExecuteReader())
                                            {
                                                // Check if a record exists
                                                if (reader.Read())
                                                {
                                                    dummy = new PRT_PurchaseInvoice
                                                    {
                                                        PurchaseInvoice_ID = Convert.ToInt32(reader["PurchaseInvoice_ID"]),
                                                        PurchaseInvoiceNumber = reader["PurchaseInvoiceNumber"].ToString(),
                                                        PurchaseInvoiceDate = Convert.ToDateTime(reader["PurchaseInvoiceDate"]),
                                                        Supplier_ID = Convert.ToInt32(reader["Supplier_ID"]),
                                                        Currency_ID = Convert.ToInt32(reader["Currency_ID"]),
                                                        ExchangeRate = Convert.ToDecimal(reader["ExchangeRate"]),
                                                        SupplierInvoiceNumber = reader["SupplierInvoiceNumber"].ToString(),
                                                        SupplierInvoiceDate = reader["SupplierInvoiceDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["SupplierInvoiceDate"]),
                                                        IsImport = Convert.ToBoolean(reader["IsImport"]),
                                                        ModeOfShipment_ID = reader["ModeOfShipment_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ModeOfShipment_ID"]),
                                                        TotalInvoiceAmountInLocalCurrency = Convert.ToDecimal(reader["TotalInvoiceAmountInLocalCurrency"]),
                                                        LandingCostFactor = Convert.ToDecimal(reader["LandingCostFactor"]),
                                                        Remarks = reader["Remarks"].ToString(),
                                                        TotalAmount = reader["TotalAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TotalAmount"]),
                                                        DiscountPercentage = reader["DiscountPercentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountPercentage"]),
                                                        DiscountAmount = reader["DiscountAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountAmount"]),
                                                        DiscountedAmount = reader["DiscountedAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountedAmount"]),
                                                        TaxStructure_ID = reader["TaxStructure_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["TaxStructure_ID"]),
                                                        TaxableOtherCharges = reader["TaxableOtherCharges"].ToString(),
                                                        TaxablePercentage = reader["TaxablePercentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TaxablePercentage"]),
                                                        TaxableOtherChargesAmount = reader["TaxableOtherChargesAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TaxableOtherChargesAmount"]),
                                                        TaxOnTaxableOtherCharges = reader["TaxOnTaxableOtherCharges"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TaxOnTaxableOtherCharges"]),
                                                        TotalTaxableAmount = reader["TotalTaxableAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TotalTaxableAmount"]),
                                                        TaxAmount = reader["TaxAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TaxAmount"]),
                                                        NonTaxableOtherCharges = reader["NonTaxableOtherCharges"].ToString(),
                                                        NonTaxablePercentage = reader["NonTaxablePercentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["NonTaxablePercentage"]),
                                                        NonTaxableOtherChargesAmount = reader["NonTaxableOtherChargesAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["NonTaxableOtherChargesAmount"]),
                                                        TotalInvoiceAmount = reader["TotalInvoiceAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TotalInvoiceAmount"]),
                                                        DocumentNumber = reader["DocumentNumber"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DocumentNumber"]),
                                                        FinancialYear = Convert.ToInt32(reader["FinancialYear"]),
                                                        Company_ID = reader["Company_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Company_ID"]),
                                                        Branch_ID = reader["Branch_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Branch_ID"]),
                                                        Updated_By = Convert.ToInt32(reader["Updated_By"]),
                                                        Updated_Date = Convert.ToDateTime(reader["Updated_Date"]),
                                                        PurchaseGRN_ID = reader["PurchaseGRN_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PurchaseGRN_ID"]),
                                                        WareHouse_ID = reader["WareHouse_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["WareHouse_ID"]),
                                                        PurchaseOrderClass_ID = reader["PurchaseOrderClass_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PurchaseOrderClass_ID"]),
                                                        ItemLevelTaxStructure_ID = reader["ItemLevelTaxStructure_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ItemLevelTaxStructure_ID"]),
                                                        IsDealer = reader["IsDealer"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsDealer"]),
                                                        Roundoff = reader["Roundoff"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Roundoff"]),
                                                        TotalPIAmount = reader["TotalPIAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TotalPIAmount"])
                                                    };
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                       
                                    }
                                }

                                List<PRT_PurchaseInvoicePartsDetails> purchaseInvoicePartsAfterInsert = new List<PRT_PurchaseInvoicePartsDetails>();
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string query = @"
                                        SELECT * 
                                        FROM PRT_PurchaseInvoicePartsDetails
                                        WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";


                                    SqlCommand command = null;

                                    try
                                    {
                                        using (command = new SqlCommand(query, conn))
                                        {
                                            command.CommandType = CommandType.Text;
                                            command.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);

                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = command.ExecuteReader())
                                            {
                                                // Check if a record exists
                                                while (reader.Read())
                                                {
                                                    var partDetail = new PRT_PurchaseInvoicePartsDetails
                                                    {
                                                        PurchaseInvoicePartsDetailID = Convert.ToInt32(reader["PurchaseInvoicePartsDetailID"]),
                                                        PurchaseInvoice_ID = Convert.ToInt32(reader["PurchaseInvoice_ID"]),
                                                        PurchaseOrder_ID = reader["PurchaseOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                                        Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                        PartsOrder_ID = reader["PartsOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PartsOrder_ID"]),
                                                        Rate = Convert.ToDecimal(reader["Rate"]),
                                                        Quantity = Convert.ToDecimal(reader["Quantity"]),
                                                        DiscountPercentage = reader["DiscountPercentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountPercentage"]),
                                                        DiscountAmount = reader["DiscountAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountAmount"]),
                                                        TaxStructure_ID = reader["TaxStructure_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["TaxStructure_ID"]),
                                                        TaxAmount = reader["TaxAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TaxAmount"]),
                                                        Amount = Convert.ToDecimal(reader["Amount"]),
                                                        LandingCostInLocalCurrency = reader["LandingCostInLocalCurrency"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["LandingCostInLocalCurrency"]),
                                                        VarianceinRate = reader["VarianceinRate"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["VarianceinRate"]),
                                                        DiscountedAmount = reader["DiscountedAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountedAmount"]),
                                                        WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                                        MRP = reader["MRP"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["MRP"])
                                                    };
                                                    purchaseInvoicePartsAfterInsert.Add(partDetail);
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                      
                                    }
                                }

                                for (int i = 0; i < purchaseInvoicePartsAfterInsert.Count; i++)
                                {
                                    int purchaseOrderID = purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0;
                                    //int wareHouseID = purchaseOrderID != 0 ? purchaseOrderClient.PRT_PurchaseOrder.Where(a => a.PurchaseOrder_ID == purchaseOrderID).Select(a => a.WareHouse_ID.Value).FirstOrDefault() : wareHouseClient.GNM_WareHouse.Where(a => a.Company_ID == companyID && a.Branch_ID == branchID && a.IsDefault == true).Select(a => a.WareHouse_ID).FirstOrDefault();

                                    AllocationLogicServices.UpdatePartsStock(purchaseInvoicePartsAfterInsert[i].Parts_ID, purchaseInvoicePartsAfterInsert[i].Quantity, (purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0), purchaseInvoicePartsAfterInsert[i].WareHouse_ID, headerRow.PurchaseInvoiceDate, (purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency != null ? purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency.Value : 0), headerRow.Supplier_ID, Obj.Company_ID, Obj.Branch, connString, LogException);
                                    int PartId = purchaseInvoicePartsAfterInsert[i].Parts_ID;
                                    //var InsertPurchaseInvoice_GST = salesinvClient.INSERT_GSTPARTDETAILSREPORT("[dbo].[PRT_PurchaseInvoice]", companyID, branchID, headerRow.PurchaseInvoice_ID, dummy.PurchaseInvoiceNumber.ToString(), PartId, headerRow.PurchaseInvoiceDate, headerRow.Supplier_ID, headerRow.WareHouse_ID);
                                }
                                string allocationEnabled = string.Empty;
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string query = @"
                                        SELECT Param_value 
                                        FROM GNM_CompParam 
                                        WHERE Company_ID = @Company_ID 
                                          AND UPPER(Param_Name) = @Param_Name";


                                    SqlCommand command = null;

                                    try
                                    {
                                        using (command = new SqlCommand(query, conn))
                                        {
                                            command.CommandType = CommandType.Text;
                                            command.Parameters.AddWithValue("@Company_ID", companyID);
                                            command.Parameters.AddWithValue("@Param_Name", "CREATE_AUTO_GRN");

                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            object result = command.ExecuteScalar();
                                            allocationEnabled = result != null && result != DBNull.Value ? result.ToString() : string.Empty;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                     
                                    }
                                }

                                if (allocationEnabled == "T")
                                {
                                    CreateAutoGRNList objList = new CreateAutoGRNList()
                                    {
                                        Company_ID = Obj.Company_ID,
                                        User_ID = Obj.User_ID,
                                        Branch = Obj.Branch,
                                        purchaseInvoiceID = headerRow.PurchaseInvoice_ID
                                    };




                                    PRT_PurchaseInvoiceServices.CreateAutoGRN(objList, connString, LogException);
                                }

                                jsonData = new
                                {
                                    IsPSExists = true,
                                    IsSuccess = true,
                                    PurchaseInvoice_ID = headerRow.PurchaseInvoice_ID,
                                    PurchaseInvoiceNumber = dummy.PurchaseInvoiceNumber,
                                    StockChanged,
                                    Mainstockchangesparts
                                };
                                // gbl.InsertGPSDetails(companyID, branchID, userid, Common.GetObjectID("PRT_PurchaseInvoice"), jsonData.PurchaseInvoice_ID, 0, 0, "Inserted " + jsonData.PurchaseInvoiceNumber + "", false, Convert.ToInt32(JsonObj.ElementAt(5)), Convert.ToDateTime(JsonObj.ElementAt(4)), null);
                            }
                            else
                            {
                                jsonData = new
                                {
                                    IsPSExists = false,
                                    IsSuccess = true,
                                    PurchaseInvoice_ID = headerRow.PurchaseInvoice_ID,
                                    PurchaseInvoiceNumber = "",
                                    StockChanged,
                                    Mainstockchangesparts
                                };
                            }
                        }
                        else
                        {
                            List<PRT_PurchaseInvoicePartsDetails> purchaseInvoicePartsBeforeUpdate = new List<PRT_PurchaseInvoicePartsDetails>();
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = @"
                                    SELECT 
                                        PurchaseInvoicePartsDetailID,
                                        PurchaseInvoice_ID,
                                        PurchaseOrder_ID,
                                        Parts_ID,
                                        PartsOrder_ID,
                                        Rate,
                                        Quantity,
                                        DiscountPercentage,
                                        DiscountAmount,
                                        TaxStructure_ID,
                                        TaxAmount,
                                        Amount,
                                        LandingCostInLocalCurrency,
                                        VarianceinRate,
                                        DiscountedAmount,
                                        WareHouse_ID,
                                        MRP
                                    FROM PRT_PurchaseInvoicePartsDetails
                                    WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";


                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);

                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            // Check if a record exists
                                            while (reader.Read())
                                            {
                                                PRT_PurchaseInvoicePartsDetails partDetail = new PRT_PurchaseInvoicePartsDetails
                                                {
                                                    PurchaseInvoicePartsDetailID = Convert.ToInt32(reader["PurchaseInvoicePartsDetailID"]),
                                                    PurchaseInvoice_ID = Convert.ToInt32(reader["PurchaseInvoice_ID"]),
                                                    PurchaseOrder_ID = reader["PurchaseOrder_ID"] as int?,
                                                    Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                    PartsOrder_ID = reader["PartsOrder_ID"] as int?,
                                                    Rate = Convert.ToDecimal(reader["Rate"]),
                                                    Quantity = Convert.ToDecimal(reader["Quantity"]),
                                                    DiscountPercentage = reader["DiscountPercentage"] as decimal?,
                                                    DiscountAmount = reader["DiscountAmount"] as decimal?,
                                                    TaxStructure_ID = reader["TaxStructure_ID"] as int?,
                                                    TaxAmount = reader["TaxAmount"] as decimal?,
                                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                                    LandingCostInLocalCurrency = reader["LandingCostInLocalCurrency"] as decimal?,
                                                    VarianceinRate = reader["VarianceinRate"] as decimal?,
                                                    DiscountedAmount = reader["DiscountedAmount"] as decimal?,
                                                    WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                                    MRP = reader["MRP"] as decimal?
                                                };

                                                purchaseInvoicePartsBeforeUpdate.Add(partDetail);
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                 
                                }
                            }
                            for (int i = 0; i < purchaseInvoicePartsBeforeUpdate.Count; i++)
                            {
                                int purchaseOrderID = purchaseInvoicePartsBeforeUpdate[i].PurchaseOrder_ID != null ? purchaseInvoicePartsBeforeUpdate[i].PurchaseOrder_ID.Value : 0;
                                //int wareHouseID = purchaseOrderID != 0 ? purchaseOrderClient.PRT_PurchaseOrder.Where(a => a.PurchaseOrder_ID == purchaseOrderID).Select(a => a.WareHouse_ID.Value).FirstOrDefault() : wareHouseClient.GNM_WareHouse.Where(a => a.Company_ID == companyID && a.Branch_ID == branchID && a.IsDefault == true).Select(a => a.WareHouse_ID).FirstOrDefault();
                                AllocationLogicServices.UpdatePartsStockReturn(connString,LogException,Obj.Company_ID,Obj.Branch,purchaseInvoicePartsBeforeUpdate[i].Parts_ID, purchaseInvoicePartsBeforeUpdate[i].Quantity, (purchaseInvoicePartsBeforeUpdate[i].PurchaseOrder_ID != null ? purchaseInvoicePartsBeforeUpdate[i].PurchaseOrder_ID.Value : 0), purchaseInvoicePartsBeforeUpdate[i].WareHouse_ID);
                            }

                            PRT_PurchaseInvoice invoiceEditRow = new PRT_PurchaseInvoice();
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = @"
                                    SELECT 
                                        PurchaseInvoice_ID,
                                        PurchaseInvoiceNumber,
                                        PurchaseInvoiceDate,
                                        Supplier_ID,
                                        Currency_ID,
                                        ExchangeRate,
                                        SupplierInvoiceNumber,
                                        SupplierInvoiceDate,
                                        IsImport,
                                        ModeOfShipment_ID,
                                        TotalInvoiceAmountInLocalCurrency,
                                        LandingCostFactor,
                                        Remarks,
                                        TotalAmount,
                                        DiscountPercentage,
                                        DiscountAmount,
                                        DiscountedAmount,
                                        TaxStructure_ID,
                                        TaxableOtherCharges,
                                        TaxablePercentage,
                                        TaxableOtherChargesAmount,
                                        TaxOnTaxableOtherCharges,
                                        TotalTaxableAmount,
                                        TaxAmount,
                                        NonTaxableOtherCharges,
                                        NonTaxablePercentage,
                                        NonTaxableOtherChargesAmount,
                                        TotalInvoiceAmount,
                                        DocumentNumber,
                                        FinancialYear,
                                        Company_ID,
                                        Branch_ID,
                                        Updated_By,
                                        Updated_Date,
                                        PurchaseGRN_ID,
                                        WareHouse_ID,
                                        PurchaseOrderClass_ID,
                                        ItemLevelTaxStructure_ID,
                                        IsDealer,
                                        Roundoff,
                                        TotalPIAmount
                                    FROM PRT_PurchaseInvoice
                                    WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";


                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);

                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            // Check if a record exists
                                            if (reader.Read()) // We expect only one result
                                            {
                                                invoiceEditRow = new PRT_PurchaseInvoice
                                                {
                                                    PurchaseInvoice_ID = Convert.ToInt32(reader["PurchaseInvoice_ID"]),
                                                    PurchaseInvoiceNumber = reader["PurchaseInvoiceNumber"] as string,
                                                    PurchaseInvoiceDate = Convert.ToDateTime(reader["PurchaseInvoiceDate"]),
                                                    Supplier_ID = Convert.ToInt32(reader["Supplier_ID"]),
                                                    Currency_ID = Convert.ToInt32(reader["Currency_ID"]),
                                                    ExchangeRate = Convert.ToDecimal(reader["ExchangeRate"]),
                                                    SupplierInvoiceNumber = reader["SupplierInvoiceNumber"] as string,
                                                    SupplierInvoiceDate = reader["SupplierInvoiceDate"] as DateTime?,
                                                    IsImport = Convert.ToBoolean(reader["IsImport"]),
                                                    ModeOfShipment_ID = reader["ModeOfShipment_ID"] as int?,
                                                    TotalInvoiceAmountInLocalCurrency = Convert.ToDecimal(reader["TotalInvoiceAmountInLocalCurrency"]),
                                                    LandingCostFactor = Convert.ToDecimal(reader["LandingCostFactor"]),
                                                    Remarks = reader["Remarks"] as string,
                                                    TotalAmount = reader["TotalAmount"] as decimal?,
                                                    DiscountPercentage = reader["DiscountPercentage"] as decimal?,
                                                    DiscountAmount = reader["DiscountAmount"] as decimal?,
                                                    DiscountedAmount = reader["DiscountedAmount"] as decimal?,
                                                    TaxStructure_ID = reader["TaxStructure_ID"] as int?,
                                                    TaxableOtherCharges = reader["TaxableOtherCharges"] as string,
                                                    TaxablePercentage = reader["TaxablePercentage"] as decimal?,
                                                    TaxableOtherChargesAmount = reader["TaxableOtherChargesAmount"] as decimal?,
                                                    TaxOnTaxableOtherCharges = reader["TaxOnTaxableOtherCharges"] as decimal?,
                                                    TotalTaxableAmount = reader["TotalTaxableAmount"] as decimal?,
                                                    TaxAmount = reader["TaxAmount"] as decimal?,
                                                    NonTaxableOtherCharges = reader["NonTaxableOtherCharges"] as string,
                                                    NonTaxablePercentage = reader["NonTaxablePercentage"] as decimal?,
                                                    NonTaxableOtherChargesAmount = reader["NonTaxableOtherChargesAmount"] as decimal?,
                                                    TotalInvoiceAmount = reader["TotalInvoiceAmount"] as decimal?,
                                                    DocumentNumber = reader["DocumentNumber"] as int?,
                                                    FinancialYear = Convert.ToInt32(reader["FinancialYear"]),
                                                    Company_ID = reader["Company_ID"] as int?,
                                                    Branch_ID = reader["Branch_ID"] as int?,
                                                    Updated_By = Convert.ToInt32(reader["Updated_By"]),
                                                    Updated_Date = Convert.ToDateTime(reader["Updated_Date"]),
                                                    PurchaseGRN_ID = reader["PurchaseGRN_ID"] as int?,
                                                    WareHouse_ID = reader["WareHouse_ID"] as int?,
                                                    PurchaseOrderClass_ID = reader["PurchaseOrderClass_ID"] as int?,
                                                    ItemLevelTaxStructure_ID = reader["ItemLevelTaxStructure_ID"] as int?,
                                                    IsDealer = reader["IsDealer"] as bool?,
                                                    Roundoff = reader["Roundoff"] as decimal?,
                                                    TotalPIAmount = reader["TotalPIAmount"] as decimal?
                                                };
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                   
                                }
                            }
                            invoiceEditRow.SupplierInvoiceDate = headerRow.SupplierInvoiceDate;
                            invoiceEditRow.SupplierInvoiceNumber = headerRow.SupplierInvoiceNumber;
                            invoiceEditRow.ModeOfShipment_ID = headerRow.ModeOfShipment_ID;
                            invoiceEditRow.DiscountAmount = headerRow.DiscountAmount;
                            invoiceEditRow.DiscountedAmount = headerRow.DiscountedAmount;
                            invoiceEditRow.DiscountPercentage = headerRow.DiscountPercentage;
                            invoiceEditRow.ExchangeRate = headerRow.ExchangeRate;
                            invoiceEditRow.LandingCostFactor = headerRow.LandingCostFactor;
                            invoiceEditRow.NonTaxableOtherCharges = headerRow.NonTaxableOtherCharges;
                            invoiceEditRow.NonTaxableOtherChargesAmount = headerRow.NonTaxableOtherChargesAmount;
                            invoiceEditRow.NonTaxablePercentage = headerRow.NonTaxablePercentage;
                            invoiceEditRow.TaxableOtherCharges = headerRow.TaxableOtherCharges;
                            invoiceEditRow.TaxableOtherChargesAmount = headerRow.TaxableOtherChargesAmount;
                            invoiceEditRow.TaxablePercentage = headerRow.TaxablePercentage;
                            invoiceEditRow.TaxAmount = headerRow.TaxAmount;
                            invoiceEditRow.TotalInvoiceAmount = headerRow.TotalInvoiceAmount;
                            invoiceEditRow.TotalInvoiceAmountInLocalCurrency = headerRow.TotalInvoiceAmountInLocalCurrency;
                            invoiceEditRow.TotalTaxableAmount = headerRow.TotalTaxableAmount;
                            invoiceEditRow.TaxStructure_ID = headerRow.TaxStructure_ID;
                            invoiceEditRow.Updated_By = headerRow.Updated_By;
                            invoiceEditRow.Updated_Date = headerRow.Updated_Date;
                            invoiceEditRow.TaxOnTaxableOtherCharges = headerRow.TaxOnTaxableOtherCharges;
                            invoiceEditRow.Remarks = headerRow.Remarks;
                            invoiceEditRow.Roundoff = headerRow.Roundoff;
                            invoiceEditRow.TotalPIAmount = headerRow.TotalPIAmount;


                            using (SqlConnection conn = new SqlConnection(connString))
                            {


                                foreach (var partDetail in headerRow.PRT_PurchaseInvoicePartsDetails)
                                {
                                    int pk = partDetail.PurchaseInvoicePartsDetailID;
                                    if (pk == 0)
                                    {
                                        string query = @"
                                        INSERT INTO PRT_PurchaseInvoicePartsDetails (
                                            PurchaseInvoice_ID,
                                            PurchaseOrder_ID,
                                            Parts_ID,
                                            PartsOrder_ID,
                                            Rate,
                                            Quantity,
                                            DiscountPercentage,
                                            DiscountAmount,
                                            TaxStructure_ID,
                                            TaxAmount,
                                            Amount,
                                            LandingCostInLocalCurrency,
                                            VarianceinRate,
                                            DiscountedAmount,
                                            WareHouse_ID,
                                            MRP
                                        )
                                        VALUES (
                                            @PurchaseInvoice_ID,
                                            @PurchaseOrder_ID,
                                            @Parts_ID,
                                            @PartsOrder_ID,
                                            @Rate,
                                            @Quantity,
                                            @DiscountPercentage,
                                            @DiscountAmount,
                                            @TaxStructure_ID,
                                            @TaxAmount,
                                            @Amount,
                                            @LandingCostInLocalCurrency,
                                            @VarianceinRate,
                                            @DiscountedAmount,
                                            @WareHouse_ID,
                                            @MRP
                                        )";
                                        using (SqlCommand cmd = new SqlCommand(query, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;

                                            // Add parameters
                                            cmd.Parameters.AddWithValue("@PurchaseInvoice_ID", partDetail.PurchaseInvoice_ID);
                                            cmd.Parameters.AddWithValue("@PurchaseOrder_ID", partDetail.PurchaseOrder_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Parts_ID", partDetail.Parts_ID);
                                            cmd.Parameters.AddWithValue("@PartsOrder_ID", partDetail.PartsOrder_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Rate", partDetail.Rate);
                                            cmd.Parameters.AddWithValue("@Quantity", partDetail.Quantity);
                                            cmd.Parameters.AddWithValue("@DiscountPercentage", partDetail.DiscountPercentage ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@DiscountAmount", partDetail.DiscountAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxStructure_ID", partDetail.TaxStructure_ID ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@TaxAmount", partDetail.TaxAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Amount", partDetail.Amount);
                                            cmd.Parameters.AddWithValue("@LandingCostInLocalCurrency", partDetail.LandingCostInLocalCurrency ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@VarianceinRate", partDetail.VarianceinRate ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@DiscountedAmount", partDetail.DiscountedAmount ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@WareHouse_ID", partDetail.WareHouse_ID);
                                            cmd.Parameters.AddWithValue("@MRP", partDetail.MRP ?? (object)DBNull.Value);

                                            try
                                            {
                                                if (conn.State == ConnectionState.Closed)
                                                {
                                                    conn.Open();
                                                }

                                                // Execute the insert command
                                                cmd.ExecuteNonQuery();
                                            }
                                            catch (Exception ex)
                                            {
                                                // Log exception
                                                if (LogException == 1)
                                                {
                                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        string query = "SELECT * FROM PRT_PurchaseInvoicePartsDetails WHERE PurchaseInvoicePartsDetailID = @PurchaseInvoicePartsDetailID";
                                        PRT_PurchaseInvoicePartsDetails partsEditRow = new PRT_PurchaseInvoicePartsDetails();
                                        using (SqlCommand cmd = new SqlCommand(query, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;

                                            // Add parameters
                                            cmd.Parameters.AddWithValue("@PurchaseInvoicePartsDetailID", pk);

                                            try
                                            {
                                                if (conn.State == ConnectionState.Closed)
                                                {
                                                    conn.Open();
                                                }

                                                // Execute the insert command
                                                using (SqlDataReader reader = cmd.ExecuteReader())
                                                {
                                                    if (reader.Read())
                                                    {
                                                        partsEditRow= new PRT_PurchaseInvoicePartsDetails
                                                        {
                                                            PurchaseInvoicePartsDetailID = reader.GetInt32(reader.GetOrdinal("PurchaseInvoicePartsDetailID")),
                                                            PurchaseInvoice_ID = reader.GetInt32(reader.GetOrdinal("PurchaseInvoice_ID")),
                                                            PurchaseOrder_ID = reader["PurchaseOrder_ID"] as int?,
                                                            Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                                            PartsOrder_ID = reader["PartsOrder_ID"] as int?,
                                                            Rate = reader.GetDecimal(reader.GetOrdinal("Rate")),
                                                            Quantity = reader.GetDecimal(reader.GetOrdinal("Quantity")),
                                                            DiscountPercentage = reader["DiscountPercentage"] as decimal?,
                                                            DiscountAmount = reader["DiscountAmount"] as decimal?,
                                                            TaxStructure_ID = reader["TaxStructure_ID"] as int?,
                                                            TaxAmount = reader["TaxAmount"] as decimal?,
                                                            Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                                            LandingCostInLocalCurrency = reader["LandingCostInLocalCurrency"] as decimal?,
                                                            VarianceinRate = reader["VarianceinRate"] as decimal?,
                                                            DiscountedAmount = reader["DiscountedAmount"] as decimal?,
                                                            WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                                            MRP = reader["MRP"] as decimal?
                                                        };
                                                    }
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                // Log exception
                                                if (LogException == 1)
                                                {
                                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                                }
                                            }
                                        }
                                        partsEditRow.Amount = partDetail.Amount;
                                        partsEditRow.DiscountAmount = partDetail.DiscountAmount;
                                        partsEditRow.DiscountedAmount = partDetail.DiscountedAmount;
                                        partsEditRow.DiscountPercentage = partDetail.DiscountPercentage;
                                        partsEditRow.LandingCostInLocalCurrency = partDetail.LandingCostInLocalCurrency;
                                        partsEditRow.Quantity = partDetail.Quantity;
                                        partsEditRow.Rate = partDetail.Rate;
                                        partsEditRow.TaxAmount = partDetail.TaxAmount;
                                        partsEditRow.TaxStructure_ID = partDetail.TaxStructure_ID;
                                        partsEditRow.VarianceinRate = partDetail.VarianceinRate;
                                    }

                                }
                            }
                            int DelCount = 0;
                            int ID = 0;
                            JTokenReader DelReader = null;
                            JObject DelJobj = null;

                            //Parts Delete
                            DelJobj = JObject.Parse(JsonObj.ElementAt(2));
                            DelCount = DelJobj["partrows"].Count();
                            PRT_PurchaseInvoicePartsDetails PurchaseInvoicePartsRow = null;
                            ID = 0;
                            for (int i = 0; i < DelCount; i++)
                            {
                                DelReader = new JTokenReader(DelJobj["partrows"].ElementAt(i).ToObject<JObject>()["id"]);
                                DelReader.Read();
                                ID = Convert.ToInt32(DelReader.Value);
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string selectQuery = "SELECT * FROM PRT_PurchaseInvoicePartsDetails WHERE PurchaseInvoicePartsDetailID = @PurchaseInvoicePartsDetailID";


                                    SqlCommand command = null;

                                    try
                                    {
                                        using (command = new SqlCommand(selectQuery, conn))
                                        {
                                            command.CommandType = CommandType.Text;
                                            command.Parameters.AddWithValue("@PurchaseInvoicePartsDetailID", ID);

                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = command.ExecuteReader())
                                            {
                                                if (reader.Read())
                                                {
                                                    string deleteQuery = "DELETE FROM PRT_PurchaseInvoicePartsDetails WHERE PurchaseInvoicePartsDetailID = @PurchaseInvoicePartsDetailID";
                                                    using (SqlCommand deleteCmd = new SqlCommand(deleteQuery, conn))
                                                    {
                                                        deleteCmd.Parameters.AddWithValue("@PurchaseInvoicePartsDetailID", ID);


                                                        deleteCmd.ExecuteNonQuery();
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                       
                                    }
                                }


                            }




                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                int purchaseInvoiceID = headerRow.PurchaseInvoice_ID;  // Assuming headerRow.PurchaseInvoice_ID is available
                                decimal landingFactor = headerRow.LandingCostFactor; // Assuming landingFactor is available
                                decimal exchangeRate = headerRow.ExchangeRate; // Assuming headerRow.ExchangeRate is available

                                // Open the connection if it is closed
                                if (conn.State == ConnectionState.Closed)
                                {
                                    conn.Open();
                                }

                                // Query to retrieve purchase invoice parts details for the given PurchaseInvoice_ID
                                string selectQuery = @"
                                SELECT 
                                    PurchaseInvoicePartsDetailID, 
                                    PurchaseInvoice_ID, 
                                    Amount, 
                                    Quantity, 
                                    Rate, 
                                    DiscountAmount,
                                    LandingCostInLocalCurrency
                                FROM 
                                    PRT_PurchaseInvoicePartsDetails
                                WHERE 
                                    PurchaseInvoice_ID = @PurchaseInvoice_ID";

                                // Create the SqlCommand for selecting the records
                                using (SqlCommand selectCmd = new SqlCommand(selectQuery, conn))
                                {
                                    selectCmd.Parameters.AddWithValue("@PurchaseInvoice_ID", purchaseInvoiceID);

                                    // Execute the query and use SqlDataReader to fetch the records
                                    using (SqlDataReader reader = selectCmd.ExecuteReader())
                                    {
                                        // Loop through each record and update LandingCostInLocalCurrency
                                        while (reader.Read())
                                        {
                                            int pk = Convert.ToInt32(reader["PurchaseInvoicePartsDetailID"]);
                                            decimal amount = Convert.ToDecimal(reader["Amount"]);
                                            decimal quantity = Convert.ToDecimal(reader["Quantity"]);

                                            // Calculate the total landing cost in local currency
                                            decimal totalLandingCostInLocalCurrency = (((landingFactor * amount) + amount) * exchangeRate) / quantity;

                                            // Update the record in the database
                                            string updateQuery = @"
                                            UPDATE PRT_PurchaseInvoicePartsDetails
                                            SET LandingCostInLocalCurrency = @LandingCostInLocalCurrency
                                            WHERE PurchaseInvoicePartsDetailID = @PurchaseInvoicePartsDetailID";

                                            using (SqlCommand updateCmd = new SqlCommand(updateQuery, conn))
                                            {
                                                updateCmd.Parameters.AddWithValue("@LandingCostInLocalCurrency", totalLandingCostInLocalCurrency);
                                                updateCmd.Parameters.AddWithValue("@PurchaseInvoicePartsDetailID", pk);

                                                // Execute the update command
                                                updateCmd.ExecuteNonQuery();
                                            }
                                        }
                                    }
                                }
                            }

                            // gbl.InsertGPSDetails(companyID, branchID, userid, Convert.ToInt32(Common.GetObjectID("PRT_PurchaseInvoice")), headerRow.PurchaseInvoice_ID, 0, 0, "Insert", false);


                            List<PRT_PurchaseInvoicePartsDetails> purchaseInvoicePartsAfterInsert = new List<PRT_PurchaseInvoicePartsDetails>();
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = @"
                                    SELECT 
                                        PurchaseInvoicePartsDetailID,
                                        PurchaseInvoice_ID,
                                        PurchaseOrder_ID,
                                        Parts_ID,
                                        PartsOrder_ID,
                                        Rate,
                                        Quantity,
                                        DiscountPercentage,
                                        DiscountAmount,
                                        TaxStructure_ID,
                                        TaxAmount,
                                        Amount,
                                        LandingCostInLocalCurrency,
                                        VarianceinRate,
                                        DiscountedAmount,
                                        WareHouse_ID,
                                        MRP
                                    FROM 
                                        PRT_PurchaseInvoicePartsDetails
                                    WHERE 
                                        PurchaseInvoice_ID = @PurchaseInvoice_ID";
                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);
                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                PRT_PurchaseInvoicePartsDetails partDetail = new PRT_PurchaseInvoicePartsDetails
                                                {
                                                    PurchaseInvoicePartsDetailID = Convert.ToInt32(reader["PurchaseInvoicePartsDetailID"]),
                                                    PurchaseInvoice_ID = Convert.ToInt32(reader["PurchaseInvoice_ID"]),
                                                    PurchaseOrder_ID = reader["PurchaseOrder_ID"] != DBNull.Value ? (int?)reader["PurchaseOrder_ID"] : null,
                                                    Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                    PartsOrder_ID = reader["PartsOrder_ID"] != DBNull.Value ? (int?)reader["PartsOrder_ID"] : null,
                                                    Rate = Convert.ToDecimal(reader["Rate"]),
                                                    Quantity = Convert.ToDecimal(reader["Quantity"]),
                                                    DiscountPercentage = reader["DiscountPercentage"] != DBNull.Value ? (decimal?)reader["DiscountPercentage"] : null,
                                                    DiscountAmount = reader["DiscountAmount"] != DBNull.Value ? (decimal?)reader["DiscountAmount"] : null,
                                                    TaxStructure_ID = reader["TaxStructure_ID"] != DBNull.Value ? (int?)reader["TaxStructure_ID"] : null,
                                                    TaxAmount = reader["TaxAmount"] != DBNull.Value ? (decimal?)reader["TaxAmount"] : null,
                                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                                    LandingCostInLocalCurrency = reader["LandingCostInLocalCurrency"] != DBNull.Value ? (decimal?)reader["LandingCostInLocalCurrency"] : null,
                                                    VarianceinRate = reader["VarianceinRate"] != DBNull.Value ? (decimal?)reader["VarianceinRate"] : null,
                                                    DiscountedAmount = reader["DiscountedAmount"] != DBNull.Value ? (decimal?)reader["DiscountedAmount"] : null,
                                                    WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                                    MRP = reader["MRP"] != DBNull.Value ? (decimal?)reader["MRP"] : null
                                                };

                                                purchaseInvoicePartsAfterInsert.Add(partDetail);
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                 
                                }
                            }


                            for (int i = 0; i < purchaseInvoicePartsAfterInsert.Count; i++)
                            {
                                int purchaseOrderID = purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0;
                                //int wareHouseID = purchaseOrderID != 0 ? purchaseOrderClient.PRT_PurchaseOrder.Where(a => a.PurchaseOrder_ID == purchaseOrderID).Select(a => a.WareHouse_ID.Value).FirstOrDefault() : wareHouseClient.GNM_WareHouse.Where(a => a.Company_ID == companyID && a.Branch_ID == branchID && a.IsDefault == true).Select(a => a.WareHouse_ID).FirstOrDefault();
                                AllocationLogicServices.UpdatePartsStock(purchaseInvoicePartsAfterInsert[i].Parts_ID, purchaseInvoicePartsAfterInsert[i].Quantity, (purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0), purchaseInvoicePartsAfterInsert[i].WareHouse_ID, headerRow.PurchaseInvoiceDate, (purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency != null ? purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency.Value : 0), headerRow.Supplier_ID, Obj.Company_ID, Obj.Branch, connString, LogException);
                            }


                            jsonData = new
                            {
                                IsPSExists = true,
                                IsSuccess = true,
                                PurchaseInvoice_ID = headerRow.PurchaseInvoice_ID,
                                PurchaseInvoiceNumber = headerRow.PurchaseInvoiceNumber,
                                StockChanged,
                                Mainstockchangesparts
                            };
                            //gbl.InsertGPSDetails(companyID, branchID, userid, Common.GetObjectID("PRT_PurchaseInvoice"), jsonData.PurchaseInvoice_ID, 0, 0, "Updated " + jsonData.PurchaseInvoiceNumber + "", false, Convert.ToInt32(JsonObj.ElementAt(5)), Convert.ToDateTime(JsonObj.ElementAt(4)), null);
                        }
                    }
                    else
                    {

                        jsonData = new
                        {
                            IsPSExists = true,
                            IsSuccess = false,
                            StockChanged,
                            Mainstockchangesparts
                        };
                    }
                    scope.Complete();
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    jsonData = new
                    {
                        IsPSExists = true,
                        IsSuccess = false,
                        PurchaseInvoice_ID = 0,
                        PurchaseInvoiceNumber = ""
                    };
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion
    }
    public class Save_BLPartsInvoiceList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
    }
    public class StockLessPartDetails
    {
        public int Parts_ID { get; set; }
        public decimal FreeStock { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal RequestedQty { get; set; }
        public decimal Quantity { get; set; }
        public string PartsPrefix { get; set; }
        public string PartNumber { get; set; }
        public string Parts { get; set; }
    }
    public partial class PRT_PurchaseInvoicePartsDetails
    {
        public int PurchaseInvoicePartsDetailID { get; set; }
        public int PurchaseInvoice_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public Nullable<int> PartsOrder_ID { get; set; }
        public decimal Rate { get; set; }
        public decimal Quantity { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public decimal Amount { get; set; }
        public Nullable<decimal> LandingCostInLocalCurrency { get; set; }
        public Nullable<decimal> VarianceinRate { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public int WareHouse_ID { get; set; }
        public Nullable<decimal> MRP { get; set; }
        public string PurchaseOrder_Number { get; set; }

        public virtual PRT_PurchaseInvoice PRT_PurchaseInvoice { get; set; }
    }
    public partial class PRT_PurchaseInvoice
    {
        public PRT_PurchaseInvoice()
        {
            this.PRT_PurchaseInvoicePartsDetails = new HashSet<PRT_PurchaseInvoicePartsDetails>();
        }

        public int PurchaseInvoice_ID { get; set; }
        public string PurchaseInvoiceNumber { get; set; }
        public System.DateTime PurchaseInvoiceDate { get; set; }
        public int Supplier_ID { get; set; }
        public int Currency_ID { get; set; }
        public decimal ExchangeRate { get; set; }
        public string SupplierInvoiceNumber { get; set; }
        public Nullable<System.DateTime> SupplierInvoiceDate { get; set; }
        public bool IsImport { get; set; }
        public Nullable<int> ModeOfShipment_ID { get; set; }
        public decimal TotalInvoiceAmountInLocalCurrency { get; set; }
        public decimal LandingCostFactor { get; set; }
        public string Remarks { get; set; }
        public Nullable<decimal> TotalAmount { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public string TaxableOtherCharges { get; set; }
        public Nullable<decimal> TaxablePercentage { get; set; }
        public Nullable<decimal> TaxableOtherChargesAmount { get; set; }
        public Nullable<decimal> TaxOnTaxableOtherCharges { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public string NonTaxableOtherCharges { get; set; }
        public Nullable<decimal> NonTaxablePercentage { get; set; }
        public Nullable<decimal> NonTaxableOtherChargesAmount { get; set; }
        public Nullable<decimal> TotalInvoiceAmount { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public int FinancialYear { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public int Updated_By { get; set; }
        public System.DateTime Updated_Date { get; set; }
        public Nullable<int> PurchaseGRN_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public Nullable<int> PurchaseOrderClass_ID { get; set; }
        public Nullable<int> ItemLevelTaxStructure_ID { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<decimal> Roundoff { get; set; }
        public Nullable<decimal> TotalPIAmount { get; set; }
        public string SAP_OrderNumber {  get; set; }
        public int InvoiceAddressID { get; set; }
        public int ConsigneeAddressID { get; set; }

        public virtual ICollection<PRT_PurchaseInvoicePartsDetails> PRT_PurchaseInvoicePartsDetails { get; set; }
    }
    public partial class PRT_PurchaseOrderPartsDetail
    {
        public int PurchaseOrderPartsDetail_ID { get; set; }
        public int PurchaseOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal SupplierPrice { get; set; }
        public Nullable<decimal> RequestedQuantity { get; set; }
        public Nullable<decimal> ApprovedQuantity { get; set; }
        public Nullable<decimal> InvoicedQuantity { get; set; }
        public Nullable<decimal> BackOrderCancelledQuantity { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public decimal Amount { get; set; }
        public Nullable<int> PartsOrder_ID { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<decimal> MRP { get; set; }

        public virtual PRT_PurchaseOrder PRT_PurchaseOrder { get; set; }
    }
    public partial class PRT_PurchaseOrderAttachmentDetail
    {
        public int POAttachmentDetails_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public string FileName { get; set; }
        public Nullable<System.DateTime> UpdatedDate { get; set; }
        public Nullable<int> UploadedBy_ID { get; set; }
        public string FileDescription { get; set; }

        public virtual PRT_PurchaseOrder PRT_PurchaseOrder { get; set; }
    }
    public partial class PRT_PurchaseOrder
    {
        public PRT_PurchaseOrder()
        {
            this.PRT_PurchaseOrderAttachmentDetail = new HashSet<PRT_PurchaseOrderAttachmentDetail>();
            this.PRT_PurchaseOrderPartsDetail = new HashSet<PRT_PurchaseOrderPartsDetail>();
        }
        public string IncoTerms { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public int PurchaseOrder_ID { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public byte PurchaseOrderVersion { get; set; }
        public System.DateTime PurchaseOrderDate { get; set; }
        public int TypeofPurchase_ID { get; set; }
        public int Supplier_ID { get; set; }
        public Nullable<int> PurchaseOrderClass_ID { get; set; }
        public int InvoiceAddress_ID { get; set; }
        public int ConsigneeAddress_ID { get; set; }
        public string PaymentTerms { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public string SerialNumber { get; set; }
        public decimal TotalOrderAmount { get; set; }
        public string Remarks { get; set; }
        public bool EnableVersion { get; set; }
        public bool IsArchived { get; set; }
        public int FinancialYear { get; set; }
        public int Updated_by { get; set; }
        public System.DateTime Updated_Date { get; set; }
        public Nullable<System.DateTime> Expected_Delivery_Date { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public int PurchaseOrderStatus_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public int POStatus_ID { get; set; }
        public Nullable<int> ModeOfShipment_ID { get; set; }
        public Nullable<int> SUPPLIERQUOTATION_ID { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<bool> IsUnderBreakDown { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<bool> TallyGeneratedDate { get; set; }
        public Nullable<bool> TallySentStatus { get; set; }
        public decimal? NonTaxableOtherChargesAmount { get; set; }
        public decimal? TaxableOtherChargesAmount { get; set; }
        public string TaxableOtherCharges { get; set; }
        public string NonTaxableOtherCharges { get; set; }

        public virtual ICollection<PRT_PurchaseOrderAttachmentDetail> PRT_PurchaseOrderAttachmentDetail { get; set; }
        public virtual ICollection<PRT_PurchaseOrderPartsDetail> PRT_PurchaseOrderPartsDetail { get; set; }
    }
}
