//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_Menu
    {
        public WF_Menu()
        {
            this.GNM_MenuLocale = new HashSet<WF_MenuLocale>();
        }
    
        public int Menu_ID { get; set; }
        public int Module_ID { get; set; }
        public string Menu_Description { get; set; }
        public Nullable<int> Parentmenu_ID { get; set; }
        public Nullable<int> Object_ID { get; set; }
        public string Menu_Path { get; set; }
        public byte Menu_SortOrder { get; set; }
        public bool Menu_IsActive { get; set; }
        public string Menu_IconName { get; set; }
    
        public virtual WF_Module GNM_Module { get; set; }
        public virtual ICollection<WF_MenuLocale> GNM_MenuLocale { get; set; }
    }
}
