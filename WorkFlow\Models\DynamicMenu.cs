﻿using WorkFlow.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using System.IO;
using System.Configuration;
using Org.BouncyCastle.Asn1.Ocsp;
using WorkFlow.Models;


namespace WorkFlow.Models
{
    public static class HtmlHelperSiteMenu
    {
        public static string AppPath;
       
        public static MvcHtmlString HorizontalMenu(this HtmlHelper helper)
        {
            GenEntities GEClient = new GenEntities(ConfigurationManager.AppSettings.Get("DbName"));           
            int GenLangID = Convert.ToInt32(HttpContext.Current.Session["GeneralLanguageID"]);
            IEnumerable<WF_ModuleLocale> sitelinksLocale = null;
            IEnumerable<WF_Module> sitelinks = null;
            StringBuilder sb = new StringBuilder();
            List<LoginProperties> Modules = (List<LoginProperties>)HttpContext.Current.Session["ACLMenu"];
            AppPath = HttpContext.Current.Request.ApplicationPath == "/" ? "" : HttpContext.Current.Request.ApplicationPath;
            try
            {
                WF_User User = (WF_User)HttpContext.Current.Session["UserDetails"];
                int langid = User.Language_ID;

                sitelinks = GEClient.WF_Module.OrderBy(m => m.Module_SortOrder).Where(m => m.Module_IsActive == true);
                foreach (var mod in sitelinks)
                {
                    if (Modules.Where(m => m.Module_ID == mod.Module_ID).Count() > 0)
                    {
                        sb.Append("<Span class='SpanSelected'><a href='#' onclick=ImgClick(" + mod.Module_ID + "," + mod.Module_ID + ")  class='selected' id='" + mod.Module_ID + "' )><img src='" + AppPath + "/Images/" + mod.Module_Description.Trim() + ".png' alt='ModuleImg'/>&nbsp;" + mod.Module_Description.Trim() + "</a></Span>&nbsp;");
                    }
                }

            }
            catch (Exception ex)
            {
            }
            return MvcHtmlString.Create(sb.ToString());
        }

        public static MvcHtmlString DefaultMenuSidebar(this HtmlHelper helper)
        {
            GenEntities GEClient = new GenEntities(ConfigurationManager.AppSettings.Get("DbName"));   
            int moduleid = 0;
            List<LoginProperties> sitelinks = (List<LoginProperties>)HttpContext.Current.Session["ACLMenu"];
            try
            {
                //moduleid = GEClient.WF_Module.OrderBy(m => m.Module_SortOrder).FirstOrDefault().Module_ID ;
                moduleid = GEClient.WF_Module.Where(m => m.Module_Description.ToUpper()== "CORE").FirstOrDefault().Module_ID;
                if (HttpContext.Current.Session["MenuID"] == null)
                {
                    return MvcHtmlString.Create(MenuBuilding(moduleid));
                }
                else
                {
                    //int x = Convert.ToInt32(HttpContext.Current.Session["MenuID"]);
                    int defaultModuleID = sitelinks.Where(i => i.Menu_ID == 34).Select(i => i.Module_ID).FirstOrDefault();
                    return MvcHtmlString.Create(MenuBuilding(defaultModuleID));
                }
               // moduleid = sitelinks.OrderBy(i => i.).Select(i => i.Module_ID).First();
            }
            catch (Exception ex)
            {
                return MvcHtmlString.Empty;
            }           
        }

        public static string MenuBuilding(int ModuleId)
        {
            try
            {
                List<LoginProperties> ACLobjList = (List<LoginProperties>)HttpContext.Current.Session["ACLMenu"];

                if (ACLobjList == null || ACLobjList.Count() == 0)
                    return string.Empty;
                var topLevelParentId = Convert.ToInt32(ACLobjList.OrderBy(i => i.Parentmenu_ID).Where(i => i.Module_ID == ModuleId).Select(i => i.Parentmenu_ID).FirstOrDefault());
                return buildMenuItemsBasedonModule(ACLobjList.OrderBy(i => i.Menu_SortOrder).Where(i => i.Module_ID == ModuleId).ToList(), topLevelParentId, ModuleId).ToString();
            }
            catch (Exception ex)
            {
                return string.Empty;
            } 
        }

        public static string buildMenuItemsBasedonModule(List<LoginProperties> siteLinks, int parentId, int moduleID)
        {
            try
            {
                var parentTag = new TagBuilder("ul");
                parentTag.MergeAttribute("class", "accordion");
                var childSiteLinks = GetChildSiteLinks(siteLinks, parentId);

                foreach (var siteLink in childSiteLinks)
                {
                    if ((SiteLinkHasChildren(siteLinks, siteLink.Menu_ID) && siteLink.Object_ID == 0) || (!(SiteLinkHasChildren(siteLinks, siteLink.Menu_ID)) && siteLink.Object_ID != 0))
                    {
                        var itemTag = new TagBuilder("li");
                        var anchorTag = new TagBuilder("a");
            
                        anchorTag.InnerHtml = "<nobr>" + siteLink.Menu_Description + "</nobr>";
                        anchorTag.MergeAttribute("href", AppPath + siteLink.Menu_Path);
                        anchorTag.MergeAttribute("onclick", "MenuClickAction(" + siteLink.Menu_ID + ")");
                        itemTag.InnerHtml = anchorTag.ToString();
                        if (SiteLinkHasChildren(siteLinks, siteLink.Menu_ID))
                        {
                            itemTag.InnerHtml += buildMenuItemsBasedonModule(siteLinks, siteLink.Menu_ID, moduleID);
                        }
                        parentTag.InnerHtml += itemTag;
                    }
                }
                return parentTag.ToString();
            }
            catch (Exception ex)
            {
                return string.Empty;
            } 
        }

        public static bool SiteLinkHasChildren(List<LoginProperties> siteLinks, int id)
        {            
            return siteLinks.Any(i => i.Parentmenu_ID == id);
        }

        public static List<LoginProperties> GetChildSiteLinks(List<LoginProperties> siteLinks, int parentIdForChildren)
        {
            return siteLinks.Where(i => i.Parentmenu_ID == parentIdForChildren).OrderBy(i => i.Menu_SortOrder).ToList();
        }       
    }   
}