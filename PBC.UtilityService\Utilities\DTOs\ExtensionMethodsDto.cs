using System.ComponentModel.DataAnnotations;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Utilities.DTOs
{
    /// <summary>
    /// Request DTO for OrderByField operation
    /// </summary>
    public class OrderByFieldRequest
    {
        /// <summary>
        /// Gets or sets the field name to sort by
        /// </summary>
        [Required]
        [StringLength(100)]
        public string SortField { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the sort direction ("asc" or "desc")
        /// </summary>
        [Required]
        [RegularExpression("^(asc|desc)$", ErrorMessage = "Sort direction must be 'asc' or 'desc'")]
        public string SortDirection { get; set; } = "asc";

        /// <summary>
        /// Gets or sets the data to be sorted (as JSON array)
        /// </summary>
        [Required]
        public object[] Data { get; set; } = Array.Empty<object>();
    }

    /// <summary>
    /// Request DTO for FilterSearch operation
    /// </summary>
    public class FilterSearchRequest
    {
        /// <summary>
        /// Gets or sets the filter criteria
        /// </summary>
        [Required]
        public Filters Filters { get; set; } = new Filters();

        /// <summary>
        /// Gets or sets the data to be filtered (as JSON array)
        /// </summary>
        [Required]
        public object[] Data { get; set; } = Array.Empty<object>();
    }

    /// <summary>
    /// Request DTO for AdvanceSearch operation
    /// </summary>
    public class AdvanceSearchRequest
    {
        /// <summary>
        /// Gets or sets the advanced filter criteria
        /// </summary>
        [Required]
        public AdvanceFilter AdvanceFilter { get; set; } = new AdvanceFilter();

        /// <summary>
        /// Gets or sets the data to be filtered (as JSON array)
        /// </summary>
        [Required]
        public object[] Data { get; set; } = Array.Empty<object>();
    }

    /// <summary>
    /// Request DTO for Paginate operation
    /// </summary>
    public class PaginateRequest<T>
    {
        /// <summary>
        /// Gets or sets the data to be paginated
        /// </summary>
        [Required]
        public List<T> Data { get; set; } = new List<T>();

        /// <summary>
        /// Gets or sets the page number (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page must be greater than 0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Gets or sets the number of rows per page
        /// </summary>
        [Range(1, 1000, ErrorMessage = "Rows must be between 1 and 1000")]
        public int Rows { get; set; } = 10;
    }

    /// <summary>
    /// Generic request DTO for Paginate operation
    /// </summary>
    public class PaginateRequest
    {
        /// <summary>
        /// Gets or sets the data to be paginated (as JSON array)
        /// </summary>
        [Required]
        public object[] Data { get; set; } = Array.Empty<object>();

        /// <summary>
        /// Gets or sets the page number (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page must be greater than 0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Gets or sets the number of rows per page
        /// </summary>
        [Range(1, 1000, ErrorMessage = "Rows must be between 1 and 1000")]
        public int Rows { get; set; } = 10;
    }

    /// <summary>
    /// Request DTO for ConvertToType operation
    /// </summary>
    public class ConvertToTypeRequest
    {
        /// <summary>
        /// Gets or sets the object to convert
        /// </summary>
        [Required]
        public object Value { get; set; } = new object();

        /// <summary>
        /// Gets or sets the target type name
        /// </summary>
        [Required]
        [StringLength(100)]
        public string TargetType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for DecryptString operation
    /// </summary>
    public class DecryptStringRequest
    {
        /// <summary>
        /// Gets or sets the string to decrypt
        /// </summary>
        [Required]
        [StringLength(1000)]
        public string EncryptedString { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response DTO for ExtensionMethods operations
    /// </summary>
    public class ExtensionMethodsResponse<T>
    {
        /// <summary>
        /// Gets or sets whether the operation was successful
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// Gets or sets the result data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Gets or sets any error message
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the total count (for pagination)
        /// </summary>
        public int? TotalCount { get; set; }
    }

    /// <summary>
    /// Response DTO for pagination operations
    /// </summary>
    public class PaginationResponse<T>
    {
        /// <summary>
        /// Gets or sets the paginated data
        /// </summary>
        public List<T> Data { get; set; } = new List<T>();

        /// <summary>
        /// Gets or sets the current page number
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Gets or sets the total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Gets or sets the total number of records
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Gets or sets the number of rows per page
        /// </summary>
        public int RowsPerPage { get; set; }

        /// <summary>
        /// Gets or sets whether there is a next page
        /// </summary>
        public bool HasNextPage { get; set; }

        /// <summary>
        /// Gets or sets whether there is a previous page
        /// </summary>
        public bool HasPreviousPage { get; set; }
    }
}
