﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreWareHouseOrderClassAssociationServices
    {

        #region ::: LoadBranchDropdown Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// to Load Object Dropdowns
        /// </summary>
        /// 
        public static IActionResult LoadBranchDropdown(string connString, WareHouseOrderLoadBranchDropdownList WareHouseOrderLoadBranchDropdownobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int UserLang = Convert.ToInt32(WareHouseOrderLoadBranchDropdownobj.UserLanguageID);
                int GenLang = Convert.ToInt32(WareHouseOrderLoadBranchDropdownobj.GeneralLanguageID);
                int branchID = Convert.ToInt32(WareHouseOrderLoadBranchDropdownobj.Branch);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    if (UserLang == GenLang)
                    {
                        // Use stored procedure GetBranchesByCompany
                        cmd.CommandText = "SP_WareHouseOrderGetBranchesByCompany";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", WareHouseOrderLoadBranchDropdownobj.Company_ID);
                    }
                    else
                    {
                        // Use stored procedure GetBranchesByLocale
                        cmd.CommandText = "SP_WareHouseOrderGetBranchesByLocale";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", WareHouseOrderLoadBranchDropdownobj.Company_ID);
                    }

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        List<object> BranchArr = new List<object>();

                        while (reader.Read())
                        {
                            BranchArr.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }

                        jsonData = new
                        {
                            BranchArr,
                            branchID
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }

        #endregion


        #region ::: Select Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Select WareHouse for a Branch
        /// </summary>
        /// 

        public static IActionResult Select(string connstring, SelectWareHouseOrderList SelectWareHouseOrderobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            string AppPath = string.Empty;
            int Count = 0;
            int Total = 0;
            List<WareHouseMaster> warehouseList = new List<WareHouseMaster>();
            IQueryable<WareHouseMaster> WareHouseMasterQueryable = warehouseList.AsQueryable(); // Changed to IQueryable
            string WareHouses = "-1:--------Select--------;";
            string OrderClass = "-1:--------Select--------;";

            try
            {
                int Language_ID = Convert.ToInt32(SelectWareHouseOrderobj.LanguageID);

                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    conn.Open();

                    // Check language and execute appropriate stored procedure
                    if (SelectWareHouseOrderobj.GeneralLanguageCode == SelectWareHouseOrderobj.UserLanguageCode)
                    {
                        // Fetch warehouses
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetWarehousesByBranch", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", SelectWareHouseOrderobj.Branch_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    WareHouses += reader["WareHouse_ID"].ToString() + ":" + reader["WareHouseName"].ToString() + ";";
                                }
                            }
                        }

                        // Fetch order classes
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetOrderClassesByCompany", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Company_ID", SelectWareHouseOrderobj.Company_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    OrderClass += reader["OrderClass_ID"].ToString() + ":" + reader["OrderClass_Description"].ToString() + ";";
                                }
                            }
                        }
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetWarehouseMaster", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", SelectWareHouseOrderobj.Branch_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    warehouseList.Add(new WareHouseMaster
                                    {
                                        ID = Convert.ToInt32(reader["ID"]),
                                        WareHouse = reader["WareHouse"].ToString(),
                                        orderClass = reader["orderClass"].ToString(),
                                        orderClassID = reader["orderClassID"].ToString(),
                                        WareHouseID = reader["WareHouseID"].ToString()
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        // Fetch localized warehouses
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetWareHouseLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", SelectWareHouseOrderobj.Branch_ID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    WareHouses += reader["WareHouse_ID"].ToString() + ":" + reader["WareHouseName"].ToString() + ";";
                                }
                            }
                        }

                        // Fetch localized order classes
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetOrderClassLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Company_ID", SelectWareHouseOrderobj.Company_ID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    OrderClass += reader["OrderClass_ID"].ToString() + ":" + reader["OrderClass_Description"].ToString() + ";";
                                }
                            }
                        }
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetWareHouseMasterLocal", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    warehouseList.Add(new WareHouseMaster
                                    {
                                        ID = Convert.ToInt32(reader["WareHouseOrderClass_ID"]),
                                        WareHouse = reader["WareHouseName"].ToString(),
                                        orderClass = reader["OrderClass_Description"].ToString(),
                                        orderClassID = reader["OrderClass_Description"].ToString(),
                                        WareHouseID = reader["WareHouseName"].ToString()
                                    });
                                }
                            }
                        }

                    }

                    WareHouses = WareHouses.TrimEnd(';');
                    OrderClass = OrderClass.TrimEnd(';');

                    // Apply filters if search is active
                    if (_search)
                    {
                        Filters filterObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        WareHouseMasterQueryable = WareHouseMasterQueryable.FilterSearch<WareHouseMaster>(filterObj); // Changed to IQueryable
                    }

                    // Apply advanced filters if applicable
                    if (advnce)
                    {
                        AdvanceFilter advnFilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                        WareHouseMasterQueryable = WareHouseMasterQueryable.AdvanceSearch<WareHouseMaster>(advnFilter); // Changed to IQueryable
                    }

                    // Sort the results
                    if (!string.IsNullOrEmpty(sidx))
                    {
                        WareHouseMasterQueryable = WareHouseMasterQueryable.OrderByField<WareHouseMaster>(sidx, sord); // Changed to IQueryable
                    }

                    // Pagination
                    Count = WareHouseMasterQueryable.Count();
                    Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                    var paginatedData = WareHouseMasterQueryable.Skip((page - 1) * rows).Take(rows).ToList(); // Convert to List for JSON result

                    jsonData = new
                    {
                        total = Total,
                        page = page,
                        rows = paginatedData.Select(a => new
                        {
                            ID = a.ID,
                            edit = $"<img id='{a.ID}' src='{AppPath}/Content/edit.gif' key='{a.ID}' class='WareHouseEdit' editmode='false'/>",
                            delete = $"<input type='checkbox' key='{a.ID}' defaultchecked='' id='chk{a.ID}' class='WareHouseDelete'/>",
                            WareHouseID = a.WareHouseID,
                            orderClassID = a.orderClassID,
                            View = $"<img id='{a.ID}' src='{AppPath}/Content/plus.gif' key='{a.ID}' class='ViewWareHouseLocale' WareHouse='{a.WareHouse}'/>"
                        }).ToList(),
                        records = Count,
                        WareHouses,
                        OrderClass
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion



        #region ::: Insert Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Insert and Update WareHouse
        /// </summary>
        /// 
        public static IActionResult Insert(string connString, InsertWareHouseOrderList InsertWareHouseOrderobj)
        {
            int result = 0;
            int count = 0;
            JObject jObj;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                jObj = JObject.Parse(InsertWareHouseOrderobj.data);
                count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    for (int i = 0; i < count; i++)
                    {
                        GNM_WareHouseOrderClass sRow = jObj["rows"].ElementAt(i).ToObject<GNM_WareHouseOrderClass>();

                        using (SqlCommand cmd = new SqlCommand())
                        {
                            cmd.Connection = conn;
                            cmd.CommandType = CommandType.StoredProcedure;

                            if (sRow.WareHouseOrderClass_ID != 0) // Update case
                            {
                                cmd.CommandText = "SP_WareHouseOrderUpdateWareHouseOrderClass"; // Update stored procedure
                                cmd.Parameters.AddWithValue("@WareHouseOrderClass_ID", sRow.WareHouseOrderClass_ID);
                            }
                            else // Insert case
                            {
                                cmd.CommandText = "SP_WareHouseOrderInsertWareHouseOrderClass"; // Insert stored procedure
                            }

                            cmd.Parameters.AddWithValue("@WareHouse_ID", sRow.WareHouse_ID);
                            cmd.Parameters.AddWithValue("@OrderClass_ID", sRow.OrderClass_ID);

                            SqlParameter outputIdParam = new SqlParameter("@Result", SqlDbType.Int) { Direction = ParameterDirection.Output };
                            cmd.Parameters.Add(outputIdParam);

                            cmd.ExecuteNonQuery();
                            result = (int)outputIdParam.Value;

                            // Log GPS details
                            //gbl.InsertGPSDetails(Convert.ToInt32(InsertWareHouseOrderobj.Company_ID),
                            //                     Convert.ToInt32(InsertWareHouseOrderobj.Branch),
                            //                     Convert.ToInt32(InsertWareHouseOrderobj.User_ID),
                            //                     Convert.ToInt32(Common.GetObjectID("CoreWareHouseOrderClassAssociation")),
                            //                     result,
                            //                     0, 0,
                            //                     sRow.WareHouseOrderClass_ID != 0 ? "Update" : "Insert",
                            //                     false,
                            //                     Convert.ToInt32(InsertWareHouseOrderobj.MenuID));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }

        #endregion



        #region ::: Delete Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Delete WareHouse
        /// </summary>
        /// 

        public static IActionResult Delete(string connString, DeleteWareHouseOrderList DeleteWareHouseOrderobj)
        {
            string msg = string.Empty;
            int id = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(DeleteWareHouseOrderobj.key);
                int count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < count; i++)
                    {
                        JTokenReader jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                        jTR.Read();
                        id = Convert.ToInt32(jTR.Value);

                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderDeleteWareHouseOrderClass", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@WareHouseOrderClass_ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                                {
                                    msg += CommonFunctionalities.GetResourceString(DeleteWareHouseOrderobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    return new JsonResult(msg);
                                }
                                throw;
                            }
                        }
                    }

                    // Log GPS details
                    //gbl.InsertGPSDetails(Convert.ToInt32(DeleteWareHouseOrderobj.Company_ID),
                    //                     Convert.ToInt32(DeleteWareHouseOrderobj.Branch),
                    //                     Convert.ToInt32(DeleteWareHouseOrderobj.User_ID),
                    //                     Convert.ToInt32(Common.GetObjectID("CoreWareHouseOrderClassAssociation")),
                    //                     id, 0, 0, "Delete", false, Convert.ToInt32(DeleteWareHouseOrderobj.MenuID));

                    msg += CommonFunctionalities.GetResourceString(DeleteWareHouseOrderobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(msg);
        }
        #endregion



        #region ::: Export Uday Kumar J B 30-09-2024 :::
        /// <summary>
        /// To Export 
        /// </summary>
        public static async Task<object> Export(ExportWareHouseOrderList ExportWareHouseOrderobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string msg = " ";
            try
            {
                DataTable DtData = new DataTable();
                IQueryable<WareHouseMaster> IQAssociation = SelectExport(connString, ExportWareHouseOrderobj.LanguageID, ExportWareHouseOrderobj.GeneralLanguageCode, ExportWareHouseOrderobj.UserLanguageCode, ExportWareHouseOrderobj.Branch, ExportWareHouseOrderobj.Company_ID, sidx, sord, filters, advnceFilters);

                if (IQAssociation == null || !IQAssociation.Any())
                {
                    msg = "No Data is Found";
                    return msg; // Return 404 if no data is found
                }

                var WareHouseArray = from a in IQAssociation.AsEnumerable()
                                     select new
                                     {
                                         a.WareHouse,
                                         a.orderClass
                                     };

                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportWareHouseOrderobj.GeneralCulture.ToString(), "Warehouse").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportWareHouseOrderobj.GeneralCulture.ToString(), "orderClass").ToString());

                int Count = WareHouseArray.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(WareHouseArray.ElementAt(i).WareHouse, WareHouseArray.ElementAt(i).orderClass);
                    }

                    DataTable DtCriteria = new DataTable();

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("Warehouse");
                    DtAlignment.Columns.Add("orderClass");
                    DtAlignment.Rows.Add(0, 0);

                    DtCriteria.Columns.Add(CommonFunctionalities.GetGlobalResourceObject(ExportWareHouseOrderobj.GeneralCulture.ToString(), "Branch").ToString());
                    DtCriteria.Rows.Add(Common.DecryptString(ExportWareHouseOrderobj.BranchName));
                    ExportList reportExportList = new ExportList
                    {
                        Company_ID = ExportWareHouseOrderobj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportWareHouseOrderobj.Branch,
                        dt1 = DtAlignment,
                        DtCriteria = DtCriteria,

                        dt = DtData,

                        FileName = "WarehouseOrderClassAssociation", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportWareHouseOrderobj.UserCulture.ToString(), "WarehouseOrderClassAssociation").ToString(), // Set a default or dynamic header
                        exprtType = ExportWareHouseOrderobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportWareHouseOrderobj.UserCulture
                    };

                    var result = await DocumentExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //return DocumentExport.Export(reportExportList, connString, LogException);
                    // ReportExport.Export(ExportWareHouseOrderobj.exprtType, DtData, DtCriteria, DtAlignment, "WarehouseOrderClassAssociation", CommonFunctionalities.GetGlobalResourceObject(ExportWareHouseOrderobj.GeneralCulture.ToString(), "WarehouseOrderClassAssociation").ToString());
                    // gbl.InsertGPSDetails(Convert.ToInt32(ExportWareHouseOrderobj.Company_ID), Convert.ToInt32(ExportWareHouseOrderobj.Branch), Convert.ToInt32(ExportWareHouseOrderobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreWareHouseOrderClassAssociation")), 0, 0, 0, "WareHouse Order Class Association-Export", false, Convert.ToInt32(ExportWareHouseOrderobj.MenuID));
                }
                msg = "No Records to Export";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return msg;
        }


        public static IQueryable<WareHouseMaster> SelectExport(string connstring, int LanguageID, string GeneralLanguageCode, string UserLanguageCode, int Branch_ID, int Company_ID, string sidx, string sord, string filters, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            List<WareHouseMaster> WareHouseMasterList = new List<WareHouseMaster>();
            IQueryable<WareHouseMaster> WareHouseMasterQueryable = WareHouseMasterList.AsQueryable(); // Changed to IQueryable

            try
            {
                int Language_ID = Convert.ToInt32(LanguageID);

                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    conn.Open();

                    // Check language and execute appropriate stored procedure
                    if (GeneralLanguageCode == UserLanguageCode)
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderGetWarehouseMaster", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    WareHouseMasterList.Add(new WareHouseMaster
                                    {
                                        ID = Convert.ToInt32(reader["ID"]),
                                        WareHouse = reader["WareHouse"].ToString(),
                                        orderClass = reader["orderClass"].ToString(),
                                        orderClassID = reader["orderClassID"].ToString(),
                                        WareHouseID = reader["WareHouseID"].ToString()
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("GetWarehouseMasterLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    WareHouseMasterList.Add(new WareHouseMaster
                                    {
                                        ID = Convert.ToInt32(reader["ID"]),
                                        WareHouse = reader["WareHouse"].ToString(),
                                        orderClass = reader["orderClass"].ToString(),
                                        orderClassID = reader["orderClassID"].ToString(),
                                        WareHouseID = reader["WareHouseID"].ToString()
                                    });
                                }
                            }
                        }

                    }

                    // Apply filters if search is active
                    if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        if (filtersObj.rules.Count > 0)
                        {
                            WareHouseMasterQueryable = WareHouseMasterQueryable.FilterSearch(filtersObj); // Custom filter logic
                        }
                    }

                    // Apply advanced filters if present
                    if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                        WareHouseMasterQueryable = WareHouseMasterQueryable.AdvanceSearch(advnfilter); // Custom advanced filter logic
                    }

                    // Sort the results
                    if (!string.IsNullOrEmpty(sidx))
                    {
                        WareHouseMasterQueryable = WareHouseMasterQueryable.OrderByField(sidx, sord); // Changed to IQueryable
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return WareHouseMasterQueryable;
        }
        #endregion




        #region ::: WareHouseOrderClassExits Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>    
        /// 
        public static IActionResult WareHouseOrderClassExits(string connString, WareHouseOrderClassExitsList WareHouseOrderClassExitsobj)
        {
            JTokenReader jr = null;
            var jsonResult = default(dynamic);
            int? j = null;
            List<int> rowIndex = new List<int>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(WareHouseOrderClassExitsobj.Company_ID);
                int Branch_ID = Convert.ToInt32(WareHouseOrderClassExitsobj.Branch);
                JObject jObj = JObject.Parse(WareHouseOrderClassExitsobj.key);
                int BranchID = 0;
                int ContPersrowcount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < ContPersrowcount; i++)
                    {
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WareHouseOrderClass_ID"]);
                        jr.Read();
                        int ID = Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WareHouse_ID"]);
                        jr.Read();
                        int WareHouse_ID = Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["OrderClass_ID"]);
                        jr.Read();
                        int OrderClass_ID = Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Branch_ID"]);
                        jr.Read();
                        BranchID = Convert.ToInt32(jr.Value.ToString());

                        // Check for existing OrderClass_ID and Branch_ID
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderCheckOrderClassExists", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@OrderClass_ID", OrderClass_ID);
                            cmd.Parameters.AddWithValue("@Branch_ID", BranchID);

                            int count = (int)cmd.ExecuteScalar();
                            if (count > 0)
                            {
                                rowIndex.Add(i);
                                j = rowIndex.ElementAt(i);
                            }
                        }

                        // Check for existing WareHouse_ID, OrderClass_ID, and different WareHouseOrderClass_ID
                        using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderCheckWareHouseOrderClassExists", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@WareHouse_ID", WareHouse_ID);
                            cmd.Parameters.AddWithValue("@OrderClass_ID", OrderClass_ID);
                            cmd.Parameters.AddWithValue("@WareHouseOrderClass_ID", ID);
                            cmd.Parameters.AddWithValue("@Branch_ID", BranchID);

                            int count = (int)cmd.ExecuteScalar();
                            if (count > 0)
                            {
                                rowIndex.Add(i);
                                j = null;
                            }
                        }
                    }
                }

                if (rowIndex.Count != 0 && j == null)
                {
                    jsonResult = new
                    {
                        Exists = true,
                        rowsIndex = rowIndex.ToArray()
                    };
                }
                else if (j != null)
                {
                    jsonResult = new
                    {
                        Exists = true,
                        rowsIndex = rowIndex.ToArray(),
                        IsOCAssoc = true
                    };
                }
                else
                {
                    jsonResult = new
                    {
                        Exists = false,
                        rowsIndex = rowIndex.ToArray()
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion



        #region ::: CheckWareHouseOrderClassAssocaition Uday Kumar J B 30-09-2024 :::
        /// <summary>
        /// to Check WareHouse OrderClass Assocaition already exists
        /// </summary>
        /// 
        public static IActionResult CheckWareHouseOrderClassAssocaition(string connString, CheckWareHouseOrderClassAssocaitionList CheckWareHouseOrderClassAssocaitionobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("SP_WareHouseOrderCheckWareHouseOrderClassAssociation", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@WareHouse_ID", CheckWareHouseOrderClassAssocaitionobj.WareHouseID);
                        cmd.Parameters.AddWithValue("@OrderClass_ID", CheckWareHouseOrderClassAssocaitionobj.OrderClassID);
                        cmd.Parameters.AddWithValue("@WareHouseOrderClass_ID", CheckWareHouseOrderClassAssocaitionobj.WareHouseOrderClass_ID);
                        cmd.Parameters.AddWithValue("@Branch_ID", CheckWareHouseOrderClassAssocaitionobj.BranchID);

                        // Execute the stored procedure and get the count
                        status = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(status);
        }
        #endregion


        public class ExportWareHouseOrderList
        {

            public int LanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string BranchName { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

        }


        public class CheckWareHouseOrderClassAssocaitionList
        {
            public int WareHouseID { get; set; }
            public int OrderClassID { get; set; }
            public int WareHouseOrderClass_ID { get; set; }
            public int BranchID { get; set; }
        }

        public class WareHouseOrderClassExitsList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
        }


        public class DeleteWareHouseOrderList
        {

            public string key { get; set; }
            public string GeneralCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

        }

        public class InsertWareHouseOrderList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }


        public class SelectWareHouseOrderList
        {
            public int LanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
        }
        public class WareHouseOrderLoadBranchDropdownList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int Company_ID { get; set; }

        }




        #region ::: WareHouseMaster classes Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// WareHouseMaster
        /// </summary>
        public class WareHouseMaster
        {
            public int ID
            {
                get;
                set;
            }

            public string WareHouse
            {
                get;
                set;
            }
            public string WareHouseID
            {
                get;
                set;
            }
            public string orderClass
            {
                get;
                set;
            }
            public string orderClassID
            {
                get;
                set;
            }
        }

        public partial class GNM_WareHouseOrderClass
        {
            public int WareHouseOrderClass_ID { get; set; }
            public int WareHouse_ID { get; set; }
            public int OrderClass_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }

            public virtual OrderClass PRM_OrderClass { get; set; }
            public virtual GNM_WareHouse GNM_WareHouse { get; set; }
        }


        public partial class OrderClass
        {
            public OrderClass()
            {
                this.GNM_WareHouseOrderClass = new HashSet<GNM_WareHouseOrderClass>();
                this.PRM_OrderClassLocale = new HashSet<OrderClassLocale>();
            }

            public int OrderClass_ID { get; set; }
            public int Company_ID { get; set; }
            public int PartOrderType_ID { get; set; }
            public string OrderClass_Description { get; set; }
            public Nullable<int> OrderClass_LeadTime { get; set; }
            public Nullable<decimal> OrderClass_Discount { get; set; }
            public Nullable<bool> OrderClass_IsConsiderForDemand { get; set; }
            public Nullable<bool> OrderClass_IsProductValidate { get; set; }
            public bool OrderClass_Type { get; set; }
            public bool OrderClass_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_WareHouseOrderClass> GNM_WareHouseOrderClass { get; set; }
            public virtual ICollection<OrderClassLocale> PRM_OrderClassLocale { get; set; }
        }

        public partial class OrderClassLocale
        {
            public int OrderClassLocale_ID { get; set; }
            public int OrderClass_ID { get; set; }
            public string OrderClass_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual OrderClass PRM_OrderClass { get; set; }
        }

        #endregion


    }
}
