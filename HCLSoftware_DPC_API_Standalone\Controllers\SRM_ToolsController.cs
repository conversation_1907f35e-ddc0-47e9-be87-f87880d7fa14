﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.SRM_ToolsServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class SRM_ToolsController : ApiController
    {

        #region ::: Select Tools Landing Grid Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To select Tools Landing Grid
        /// </summary>
        /// 
        [Route("api/SRM_Tools/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectToolsMasterList selectToolsMasterObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_ToolsServices.Select(connstring, selectToolsMasterObj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Particular Tools from the Grid Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To Select Particular Tools from the Grid
        /// </summary>
        /// 
        [Route("api/SRM_Tools/SelectParticularTools")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularTools([FromBody] SelectParticularToolsList SelectParticularToolsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.SelectParticularTools(connString, SelectParticularToolsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: To Insert Tools Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Insert Tools
        /// </summary>
        /// 
        [Route("api/SRM_Tools/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertToolsList InsertToolsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.Insert(connString, InsertToolsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: To Update Tools Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Update Tools
        /// </summary>
        /// 
        [Route("api/SRM_Tools/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateList Updateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.Update(connString, Updateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: To Delete Tools Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Delete Tools
        /// </summary>
        /// 
        [Route("api/SRM_Tools/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteListb Deleteobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.Delete(connString, Deleteobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: To Export Tools Uday Kumar J B 12-07-2024  pending :::
        /// <summary>
        /// To Export Tools
        /// </summary>
        /// 
        //[Route("api/SRM_Tools/Export")]
        //[HttpPost]
        //[JwtTokenValidationFilter]
        //public IHttpActionResult Export([FromBody] DeleteList Deleteobj)
        //{
        //    var Response = default(dynamic);
        //    int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
        //    try
        //    {
        //        String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
        //        Response = SRM_ToolsServices.Export(connString, Deleteobj);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //    }

        //    return Ok(Response.Value);
        //}
        #endregion


        #region :::To Load Grid Locale Uday Kumar J B 12-07-2024  :::
        /// <summary>
        ///To Load Grid Locale
        /// </summary>
        ///
        [Route("api/SRM_Tools/LoadGridLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadGridLocale([FromBody] LoadGridLocaleToolMasterList LoadGridLocaleToolMasterobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_ToolsServices.LoadGridLocale(connString, LoadGridLocaleToolMasterobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Single Tools Globe image in the Grid Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To select the Single Tools when click on Globe image in the Grid
        /// </summary>  
        /// 
        [Route("api/SRM_Tools/SelectSingleTools")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSingleTools([FromBody] SelectSingleToolsList SelectSingleToolsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.SelectSingleTools(connString, SelectSingleToolsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Tools Locale  Uday Kumar J B 12-07-2024  :::
        /// <summary>
        ///  Method to Insert Tools Locale
        /// </summary> 
        /// 
        [Route("api/SRM_Tools/InsertToolsLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertToolsLocale([FromBody] InsertToolsLocaleList InsertToolsLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.InsertToolsLocale(connString, InsertToolsLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select ReferenceMaster  Uday Kumar J B 12-07-2024  :::
        /// <summary>
        ///  To SelectReferenceMaster
        /// </summary> 
        /// 
        [Route("api/SRM_Tools/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterListb SelectReferenceMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.SelectReferenceMaster(connString, SelectReferenceMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Check Duplicate Tools Name And Serial Number  Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// to check duplicate Tools Name and Serial number
        /// </summary>
        /// 
        [Route("api/SRM_Tools/CheckToolsNameAndSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckToolsNameAndSerialNumber([FromBody] CheckToolsNameAndSerialNumberList CheckToolsNameAndSerialNumberobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.CheckToolsNameAndSerialNumber(connString, CheckToolsNameAndSerialNumberobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Display Events For User when click on calender image  Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To display Events For User when click on calender image
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/SRM_Tools/SelEventsForUser")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEventsForUser([FromBody] SelEventsForUserList SelEventsForUserobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_ToolsServices.SelEventsForUser(connString, SelEventsForUserobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}