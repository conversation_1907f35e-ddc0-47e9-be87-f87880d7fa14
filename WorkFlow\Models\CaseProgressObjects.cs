﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace WorkFlow.Models
{
    public class CaseProgressObjects
    {
        public int RoleID
        {
            get;
            set;
        }

       public int CompanyID
       {
           get;
           set;
       }

       public int transactionNumber
       {
           get;
           set;
       }

       public int workFlowID
       {
           get;
           set;
       }

        public int currentStepID
        {
            get;
            set;
        }

       public DateTime receivedTime
       {
           get;
           set;
       }

       public int actionID
       {
           get;
           set;
       }

        public int actionBy
        {
            get;
            set;
        }

        public int AssignTo
        {
            get;
            set;
        }

        public string actionRemarks
        {
            get;
            set;
        }

        public byte addresseType
        {
            get;
            set;
        }

        public DateTime actionTime
        {
            get;
            set;
        }

        public string smsTextAddressee
        {
            get;
            set;
        }

       public string smsTextCustomer
       {
           get;
           set;
       }

       public string customerMobileNumber
       {
           get;
           set;
       }

       public string customerEmailID
       {
           get;
           set;
       }

       public string emailSubAddressee
       {
           get;
           set;
       }

       public string emailBodyAddress
       {
           get;
           set;
       }

       public string emailBodyCustomer
       {
           get;
           set;
       }

       public string emailSubCustomer
       {
           get;
           set;
       }
        
       public int NextStepID
       {
           get;
           set;
       }
        //added by <PERSON><PERSON><PERSON> -start
       public string CustomerBcc
       {
           get;
           set;
       }     
       public string customerCC
       {
           get;
           set;
       }
       public string AddresseBcc
       {
           get;
           set;
       }
       public string AddresseCC
       {
           get;
           set;
       }
        //added by <PERSON><PERSON><PERSON> -end

    }
}