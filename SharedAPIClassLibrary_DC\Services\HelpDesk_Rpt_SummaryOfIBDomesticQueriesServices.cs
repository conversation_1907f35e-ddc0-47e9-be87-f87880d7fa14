﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices
    {


        #region  ::: Generate SummaryOfBusinessCalls Report Uday Kumar J B 14-11-2024 :::
        public static IActionResult SummaryOfBusinessCallsReport(SummaryOfBusinessCallsReportList SummaryOfBusinessCallsReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var JsonResult = default(dynamic);
            try
            {
                string CompanyIDs = SummaryOfBusinessCallsReportobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = SummaryOfBusinessCallsReportobj.BranchIDs.TrimEnd(new char[] { ',' });
                double total = 0.00;
                int count = 0;
                List<SummaryOfBusinessCalls> ReportList = new List<SummaryOfBusinessCalls>();

                // Set up ADO.NET connection and command to call the stored procedure
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_SummaryOfBusinessCalls", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Adding parameters
                        command.Parameters.AddWithValue("@Current_Date", SummaryOfBusinessCallsReportobj.Date.Date);
                        command.Parameters.AddWithValue("@IsInternational", SummaryOfBusinessCallsReportobj.Businessarea);
                        command.Parameters.AddWithValue("@Company_ID", CompanyIDs);
                        command.Parameters.AddWithValue("@Branch_ID", BranchIDs);

                        // Open the connection
                        connection.Open();

                        // Execute the stored procedure
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ReportList.Add(new SummaryOfBusinessCalls
                                {
                                    Calls_pending_since = reader["Calls_pending_since"].ToString(),
                                    Responsbility = reader["Responsbility"].ToString(),
                                    Calls = Convert.ToInt32(reader["Calls"])
                                });
                            }
                        }
                    }
                }

                count = ReportList.Count();
                total = Math.Ceiling(Convert.ToDouble(count) / rows);
                var data = ReportList.Select(a => new SummaryOfBusinessCalls
                {
                    Calls_pending_since = a.Calls_pending_since,
                    Responsbility = a.Responsbility,
                    Calls = a.Calls,
                    Callst = a.Calls_pending_since == "Total" ? "<span style='color:blue;text-decoration:underline;cursor:pointer;'>" + a.Calls + "</span>" : a.Calls.ToString()
                }).ToArray();

                JsonResult = new
                {
                    page = page,
                    records = count,
                    total = total,
                    rows = data
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonResult);
        }
        #endregion



        #region ::: Generate TodaysCallSummary Report Uday Kumar J B 14-11-2024 :::
        public static IActionResult TodaysCallSummaryReport(TodaysCallSummaryReportList TodaysCallSummaryReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var JsonResult = default(dynamic);
            try
            {
                string CompanyIDs = TodaysCallSummaryReportobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = TodaysCallSummaryReportobj.BranchIDs.TrimEnd(new char[] { ',' });
                double total = 0.00;
                List<TodaysCallSummary> ReportList = new List<TodaysCallSummary>();

                // Set up ADO.NET connection and command to call the stored procedure
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_TodaysCallSummary", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Adding parameters
                        command.Parameters.AddWithValue("@Current_Date", TodaysCallSummaryReportobj.Date.Date);
                        command.Parameters.AddWithValue("@IsInternational", TodaysCallSummaryReportobj.Businessarea);
                        command.Parameters.AddWithValue("@Company_ID", CompanyIDs);
                        command.Parameters.AddWithValue("@Branch_ID", BranchIDs);

                        // Open the connection
                        connection.Open();

                        // Execute the stored procedure
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ReportList.Add(new TodaysCallSummary
                                {
                                    Market = Names(TodaysCallSummaryReportobj) + " on " + TodaysCallSummaryReportobj.Date.ToString("dd-MMM-yyyy"),
                                    COUNT = Convert.ToInt32(reader["COUNT"])
                                });
                            }
                        }
                    }
                }

                int count = ReportList.Count();
                total = Math.Ceiling(Convert.ToDouble(count) / rows);
                var data = ReportList.Select(a => new TodaysCallSummary
                {
                    Market = a.Market,
                    COUNT = a.COUNT
                }).ToArray();

                JsonResult = new
                {
                    page = page,
                    records = count,
                    total = total,
                    rows = data
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonResult);
        }
        public static string Names(TodaysCallSummaryReportList TodaysCallSummaryReportobj)
        {
            string returnvalue = "";
            int i = 1;
            switch (i)
            {
                case 1:
                    returnvalue = CommonFunctionalities.GetResourceString(TodaysCallSummaryReportobj.UserCulture.ToString(), "OpeningBalance").ToString(); //"Opening Balance"
                    break;
                case 2:
                    returnvalue = CommonFunctionalities.GetResourceString(TodaysCallSummaryReportobj.UserCulture.ToString(), "CallsReceived").ToString(); //"Queries Received ";
                    break;
                case 3:
                    returnvalue = CommonFunctionalities.GetResourceString(TodaysCallSummaryReportobj.UserCulture.ToString(), "TotalCompleted").ToString(); //"Total Completed";
                    break;
                case 4:
                    returnvalue = CommonFunctionalities.GetResourceString(TodaysCallSummaryReportobj.UserCulture.ToString(), "TotalPending").ToString(); //"Total Pending";
                    break;
            }
            i++;
            return returnvalue;
        }
        #endregion



        #region ::: Generate Dailyopencalls Report Uday Kumar J B 14-11-2024:::
        public static IActionResult DailyopencallsReport(DailyopencallsReportList DailyopencallsReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var JsonResult = default(dynamic);
            try
            {
                int UserLang = Convert.ToInt32(DailyopencallsReportobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(DailyopencallsReportobj.GeneralLanguageID);
                double total = 0.00;
                List<Dailyopencalls> ReportList = new List<Dailyopencalls>();
                string BranchName = string.Empty;
                string BusinessareaName = DailyopencallsReportobj.Businessarea ? "International" : "Domestic";

                // Set up ADO.NET connection and command to call the stored procedure
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_Daily_OpenCalls", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Adding parameters
                        command.Parameters.AddWithValue("@Current_Date", DailyopencallsReportobj.Date.Date);
                        command.Parameters.AddWithValue("@IsInternational", DailyopencallsReportobj.Businessarea);
                        command.Parameters.AddWithValue("@Company_ID", DailyopencallsReportobj.CompanyIDs.TrimEnd(','));
                        command.Parameters.AddWithValue("@Branch_ID", DailyopencallsReportobj.BranchIDs.TrimEnd(','));

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int companyId = Convert.ToInt32(reader["Company_ID"]);
                                int branchId = Convert.ToInt32(reader["Branch_ID"]);

                                // Fetch CompanyName using a separate query
                                string companyName = string.Empty;
                                using (SqlCommand companyCommand = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyNameById", connection))
                                {
                                    companyCommand.CommandType = CommandType.StoredProcedure;
                                    companyCommand.Parameters.AddWithValue("@CompanyId", companyId);
                                    var companyResult = companyCommand.ExecuteScalar();
                                    if (companyResult != null)
                                    {
                                        companyName = companyResult.ToString();
                                    }
                                }

                                // Fetch BranchName using a separate query
                                string branchName = string.Empty;
                                using (SqlCommand branchCommand = new SqlCommand("SP_AMERP_HelpDesk_GetBranchNameById", connection))
                                {
                                    branchCommand.CommandType = CommandType.StoredProcedure;
                                    branchCommand.Parameters.AddWithValue("@BranchId", branchId);
                                    var branchResult = branchCommand.ExecuteScalar();
                                    if (branchResult != null)
                                    {
                                        branchName = branchResult.ToString();
                                    }
                                }

                                // Add the item to the ReportList
                                ReportList.Add(new Dailyopencalls
                                {
                                    Date = reader["Date"] != DBNull.Value ? Convert.ToDateTime(reader["Date"]) : DateTime.MinValue,
                                    Datest = reader["Date"] != DBNull.Value ? Convert.ToDateTime(reader["Date"]).ToString("dd-MMM-yyyy") : string.Empty,
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] != DBNull.Value ? reader["ServiceRequestNumber"].ToString() : string.Empty,
                                    BusinessArea = reader["BusinessArea"] != DBNull.Value ? reader["BusinessArea"].ToString() : string.Empty,
                                    Model_Name = reader["Model_Name"] != DBNull.Value ? reader["Model_Name"].ToString() : string.Empty,
                                    VCNumber = reader["VCNumber"] != DBNull.Value ? reader["VCNumber"].ToString() : string.Empty,
                                    SerialNumber = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : string.Empty,
                                    Party_Name = reader["Party_Name"] != DBNull.Value ? reader["Party_Name"].ToString() : string.Empty,
                                    CallDescription = reader["CallDescription"] != DBNull.Value ? reader["CallDescription"].ToString() : string.Empty,
                                    AssignedTo = reader["AssignedTo"] != DBNull.Value ? reader["AssignedTo"].ToString() : string.Empty,
                                    Action_Remarks = reader["Action_Remarks"] != DBNull.Value ? reader["Action_Remarks"].ToString() : string.Empty,
                                    Pendingwith = reader["Pendingwith"] != DBNull.Value ? reader["Pendingwith"].ToString() : string.Empty,
                                    AgeingfromRegsitered = reader["AgeingfromRegsitered"] != DBNull.Value ? Convert.ToInt32(reader["AgeingfromRegsitered"]) : 0,
                                    AgeingfromEscalated = reader["AgeingfromEscalated"] != DBNull.Value ? Convert.ToInt32(reader["AgeingfromEscalated"]) : 0,
                                    CompanyName = companyName,
                                    BranchName = branchName,
                                    Region = Common.getRegionName(connString, LogException, UserLang, GeneralLang, branchId)
                                });
                            }
                        }
                    }
                }

                // Convert ReportList to an IQueryable and apply sorting
                var iArray = ReportList.AsQueryable();

                // Filter search (if enabled)
                if (_search)
                {
                    Filters filterobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filterobj.rules.Any())
                        iArray = iArray.FilterSearch<Dailyopencalls>(filterobj);
                }

                // Apply sorting
                iArray = iArray.OrderByField(sidx, sord);

                // Paginate and calculate total pages
                int count = iArray.Count();
                total = Math.Ceiling(Convert.ToDouble(count) / rows);
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                // Build JSON result
                JsonResult = new
                {
                    page = page,
                    records = count,
                    total = total,
                    rows = iArray.ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonResult);
        }
        #endregion



        #region ::: Generate Daily open calls Report Export Uday Kumar J B 14-11-2024:::
        public static async Task<object> Export(DailyopencallsReportList DailyopencallsReportobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            try
            {
                int UserLang = Convert.ToInt32(DailyopencallsReportobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(DailyopencallsReportobj.GeneralLanguageID);
                string BranchNameex = DailyopencallsReportobj.SummaryBranchName.ToString();
                string Date = DailyopencallsReportobj.SummaryDate.ToString();
                string BusinessArea = DailyopencallsReportobj.SummaryBusinessArea.ToString();
                int Company_ID = Convert.ToInt32(DailyopencallsReportobj.Company_ID);
                double total = 0.00;
                List<Dailyopencalls> ReportList = new List<Dailyopencalls>();
                string BranchName = string.Empty;
                string BusinessareaName = DailyopencallsReportobj.Businessarea ? "International" : "Domestic";

                // Set up ADO.NET connection and command to call the stored procedure
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_Daily_OpenCalls", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Adding parameters
                        command.Parameters.AddWithValue("@Current_Date", DailyopencallsReportobj.Date.Date);
                        command.Parameters.AddWithValue("@IsInternational", DailyopencallsReportobj.Businessarea);
                        command.Parameters.AddWithValue("@Company_ID", DailyopencallsReportobj.CompanyIDs.TrimEnd(','));
                        command.Parameters.AddWithValue("@Branch_ID", DailyopencallsReportobj.BranchIDs.TrimEnd(','));

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int companyId = Convert.ToInt32(reader["Company_ID"]);
                                int branchId = Convert.ToInt32(reader["Branch_ID"]);

                                // Fetch CompanyName using a separate query
                                string companyName = string.Empty;
                                using (SqlCommand companyCommand = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyNameById", connection))
                                {
                                    companyCommand.CommandType = CommandType.StoredProcedure;
                                    companyCommand.Parameters.AddWithValue("@CompanyId", companyId);
                                    var companyResult = companyCommand.ExecuteScalar();
                                    if (companyResult != null)
                                    {
                                        companyName = companyResult.ToString();
                                    }
                                }

                                // Fetch BranchName using a separate query
                                string branchName = string.Empty;
                                using (SqlCommand branchCommand = new SqlCommand("SP_AMERP_HelpDesk_GetBranchNameById", connection))
                                {
                                    branchCommand.CommandType = CommandType.StoredProcedure;
                                    branchCommand.Parameters.AddWithValue("@BranchId", branchId);
                                    var branchResult = branchCommand.ExecuteScalar();
                                    if (branchResult != null)
                                    {
                                        branchName = branchResult.ToString();
                                    }
                                }

                                // Add the item to the ReportList
                                ReportList.Add(new Dailyopencalls
                                {
                                    Date = reader["Date"] != DBNull.Value ? Convert.ToDateTime(reader["Date"]) : DateTime.MinValue,
                                    Datest = reader["Date"] != DBNull.Value ? Convert.ToDateTime(reader["Date"]).ToString("dd-MMM-yyyy") : string.Empty,
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] != DBNull.Value ? reader["ServiceRequestNumber"].ToString() : string.Empty,
                                    BusinessArea = reader["BusinessArea"] != DBNull.Value ? reader["BusinessArea"].ToString() : string.Empty,
                                    Model_Name = reader["Model_Name"] != DBNull.Value ? reader["Model_Name"].ToString() : string.Empty,
                                    VCNumber = reader["VCNumber"] != DBNull.Value ? reader["VCNumber"].ToString() : string.Empty,
                                    SerialNumber = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : string.Empty,
                                    Party_Name = reader["Party_Name"] != DBNull.Value ? reader["Party_Name"].ToString() : string.Empty,
                                    CallDescription = reader["CallDescription"] != DBNull.Value ? reader["CallDescription"].ToString() : string.Empty,
                                    AssignedTo = reader["AssignedTo"] != DBNull.Value ? reader["AssignedTo"].ToString() : string.Empty,
                                    Action_Remarks = reader["Action_Remarks"] != DBNull.Value ? reader["Action_Remarks"].ToString() : string.Empty,
                                    Pendingwith = reader["Pendingwith"] != DBNull.Value ? reader["Pendingwith"].ToString() : string.Empty,
                                    AgeingfromRegsitered = reader["AgeingfromRegsitered"] != DBNull.Value ? Convert.ToInt32(reader["AgeingfromRegsitered"]) : 0,
                                    AgeingfromEscalated = reader["AgeingfromEscalated"] != DBNull.Value ? Convert.ToInt32(reader["AgeingfromEscalated"]) : 0,
                                    CompanyName = companyName,
                                    BranchName = branchName,
                                    Region = Common.getRegionName(connString, LogException, UserLang, GeneralLang, branchId)
                                });
                            }
                        }
                    }
                }

                // Convert ReportList to an IQueryable and apply sorting
                var iArray = ReportList.AsQueryable();

                // Filter search (if enabled)
                if (!string.IsNullOrEmpty(filter) && filter != "null" && filter != "undefined")
                {
                    Filters filterobj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filter))).ToObject<Filters>();
                    if (filterobj.rules.Any())
                    {
                        iArray = iArray.FilterSearch<Dailyopencalls>(filterobj);
                    }
                }

                // Apply sorting
                iArray = iArray.OrderByField(sidx, sord);

                // Paginate and calculate total pages
                int count = iArray.Count();

                List<Dailyopencalls> SummaryList = iArray.ToList();
                DataTable dt = new DataTable();
                bool CHANGESERIALNUMTOVCNUM = false;

                // ADO.NET to check if 'CHANGESERIALNUMTOVCNUM' is set to TRUE
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_AMERP_HelpDesk_GetBranchNameById", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                    cmd.Parameters.AddWithValue("@Param_Name", "CHANGESERIALNUMTOVCNUM");

                    conn.Open();
                    var results = cmd.ExecuteScalar();
                    CHANGESERIALNUMTOVCNUM = results != null && results.ToString().ToUpper() == "TRUE";
                }

                // Define columns for the main DataTable (dt)
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "CompanyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "BranchName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "caseregistrationnumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "BusinessArea").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Model").ToString());

                if (CHANGESERIALNUMTOVCNUM)
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "VCNumber").ToString());
                }
                else
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "SerialNumber").ToString());
                }
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Party").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "CallDescription").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "AssignedTo").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "ActionRemarks").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "PendingWith").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "AgeingfromRegistered").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "AgeingfromEscalated").ToString());

                // Create rows for each entry in SummaryList
                foreach (var item in SummaryList)
                {
                    DataRow row = dt.NewRow();
                    row[0] = item.Region;
                    row[1] = item.CompanyName;
                    row[2] = item.BranchName;
                    row[3] = item.Date;
                    row[4] = item.ServiceRequestNumber;
                    row[5] = item.BusinessArea;
                    row[6] = item.Model_Name;
                    if (CHANGESERIALNUMTOVCNUM)
                    {
                        row[7] = item.VCNumber;
                        row[8] = item.SerialNumber;
                    }
                    else
                    {
                        row[7] = item.SerialNumber;
                    }
                    row[9] = item.Party_Name;
                    row[10] = item.CallDescription;
                    row[11] = item.AssignedTo;
                    row[12] = item.Action_Remarks;
                    row[13] = item.Pendingwith;
                    row[14] = item.AgeingfromRegsitered;
                    row[15] = item.AgeingfromEscalated;
                    dt.Rows.Add(row);
                }

                // Set up DataTable for DateRange and dtAlignment (if necessary)
                DataTable DateRange = new DataTable();
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Branch").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Date").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "BusinessArea").ToString());
                DateRange.Rows.Add(BranchNameex, Date, BusinessArea);

                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "Region").ToString());
                // Populate dtAlignment as required

                ReportExportList reportExportList = new ReportExportList
                {
                    Company_ID = DailyopencallsReportobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = DailyopencallsReportobj.Branch_ID.ToString(),
                    GeneralLanguageID = DailyopencallsReportobj.GeneralLanguageID,
                    UserLanguageID = DailyopencallsReportobj.LanguageID,
                    Options = DateRange,
                    dt = dt,
                    Alignment = dtAlignment,
                    FileName = "Summary of Business calls", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "DailyOpencalls").ToString(), // Set a default or dynamic header
                    exprtType = DailyopencallsReportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = DailyopencallsReportobj.UserCulture
                };

                var result = await ReportExport.Export(reportExportList, connString, LogException);
                return result.Value;

                // Call ReportExport function to export
                // ReportExport.Export(exprtType, dt, DateRange, dtAlignment, "Summary of Business calls", CommonFunctionalities.GetResourceString(DailyopencallsReportobj.UserCulture.ToString(), "DailyOpencalls").ToString());
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return false;
        }
        #endregion



        #region ::: HelpDesk_Rpt_SummaryOfIBDomesticQueries List and obj classes Uday Kumar J B 14-11-2024:::
        public class DailyopencallsReportList
        {
            public DateTime Date { get; set; }
            public bool Businessarea { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string SummaryBranchName { get; set; }
            public string SummaryDate { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
            public string SummaryBusinessArea { get; set; }
            public int exprtType { get; set; }
            public int Branch_ID { get; set; }
            public int LanguageID { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
        }

        public class TodaysCallSummaryReportList
        {
            public DateTime Date { get; set; }
            public bool Businessarea { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public string UserCulture { get; set; }
        }

        public class SummaryOfBusinessCallsReportList
        {
            public DateTime Date { get; set; }
            public bool Businessarea { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
        }
        #endregion



        #region ::: HelpDesk_Rpt_SummaryOfIBDomesticQueries  classes Uday Kumar J B 14-11-2024:::
        public class Dailyopencalls
        {
            public DateTime Date { get; set; }
            public string Datest { get; set; }
            public string ServiceRequestNumber { get; set; }
            public string BusinessArea { get; set; }
            public string Model_Name { get; set; }
            public string VCNumber { get; set; }
            public string SerialNumber { get; set; }
            public string Party_Name { get; set; }
            public string CallDescription { get; set; }
            public string AssignedTo { get; set; }
            public string Action_Remarks { get; set; }
            public string Pendingwith { get; set; }
            public int AgeingfromRegsitered { get; set; }
            public int AgeingfromEscalated { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public string Region { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
        }
        public class SummaryOfBusinessCalls
        {
            public string Calls_pending_since { get; set; }
            public string Responsbility { get; set; }
            public int Calls { get; set; }
            public string Callst { get; set; }
        }
        public class TodaysCallSummary
        {
            public string Market { get; set; }
            public int COUNT { get; set; }
        }
        #endregion


    }
}
