using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PartsCommonController : ControllerBase
    {
        private readonly ILogger<PartsCommonController> _logger;
        private readonly IPartsCommonService _partsCommonService;

        public PartsCommonController(ILogger<PartsCommonController> logger, IPartsCommonService partsCommonService)
        {
            _logger = logger;
            _partsCommonService = partsCommonService;
        }

        /// <summary>
        /// Save BL Parts Invoice
        /// </summary>
        /// <param name="request">Save BL request containing all necessary data</param>
        /// <returns>Result of the save operation</returns>
        [HttpPost("save-bl")]
        public async Task<IActionResult> SaveBL([FromBody] SaveBLRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Request cannot be null");
                }

                if (request.Obj == null)
                {
                    return BadRequest("Save_BLPartsInvoiceList object cannot be null");
                }

                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                if (request.JsonObj == null || !request.JsonObj.Any())
                {
                    return BadRequest("JSON object list cannot be null or empty");
                }

                _logger.LogInformation("Received Save BL request for Company_ID: {CompanyId}, User_ID: {UserId}", 
                    request.Obj.Company_ID, request.Obj.User_ID);

                var result = await _partsCommonService.SaveBLAsync(request.Obj, request.ConnString, request.LogException, request.JsonObj);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Save BL request");
                return StatusCode(500, "Internal server error");
            }
        }
    }

    /// <summary>
    /// Request model for Save BL operation
    /// </summary>
    public class SaveBLRequest
    {
        /// <summary>
        /// Save BL Parts Invoice List object
        /// </summary>
        public Save_BLPartsInvoiceList Obj { get; set; } = new Save_BLPartsInvoiceList();

        /// <summary>
        /// Database connection string
        /// </summary>
        public string ConnString { get; set; } = string.Empty;

        /// <summary>
        /// Log exception flag (1 to log, 0 to not log)
        /// </summary>
        public int LogException { get; set; } = 1;

        /// <summary>
        /// JSON object list containing the data to process
        /// </summary>
        public List<dynamic> JsonObj { get; set; } = new List<dynamic>();
    }
}
