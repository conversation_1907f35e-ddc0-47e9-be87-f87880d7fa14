{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HelpdeskSharedAPI;Trusted_Connection=true;MultipleActiveResultSets=true"}, "ServiceUrls": {"CoreService": "http://localhost:5001", "UtilitiesService": "http://localhost:5003", "AggregatorService": "http://localhost:5004", "WorkflowService": "http://localhost:5005"}, "ServicePorts": {"CoreService": "5001", "HelpdeskService": "5002", "UtilitiesService": "5003", "AggregatorService": "5004", "WorkflowService": "5005"}}