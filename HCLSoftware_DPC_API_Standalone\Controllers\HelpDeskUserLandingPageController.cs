﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskUserLandingPageController : ApiController
    {
        #region SelectDealerName vinay n 27/11/24
        /// <summary>
        /// SelectDealerName
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/SelectDealerName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDealerName([FromBody] SelectDealerNameList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.SelectDealerName(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region InitialSetup vinay n 27/11/24
        /// <summary>
        /// InitialSetup
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/InitialSetup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InitialSetup([FromBody] InitialSetupList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.InitialSetup(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelWorkFlowSummary vinay n 27/11/24
        /// <summary>
        /// SelWorkFlowSummary
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/SelWorkFlowSummary")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelWorkFlowSummary([FromBody] GetWorkFlowSummaryList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.SelWorkFlowSummary(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region validateCalldateandPCD vinay n 27/11/24
        /// <summary>
        /// validateCalldateandPCD
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/validateCalldateandPCD")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult validateCalldateandPCD([FromBody] validateCalldateandPCDList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.validateCalldateandPCD(Obj);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region CheckBayWorkshopAvailability vinay n 27/11/24
        /// <summary>
        /// CheckBayWorkshopAvailability
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/CheckBayWorkshopAvailability")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckBayWorkshopAvailability([FromBody] CheckBayWorkshopAvailabilityList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.CheckBayWorkshopAvailability(Obj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region CheckForWorkshopBlockOverlap vinay n 27/11/24
        /// <summary>
        /// CheckForWorkshopBlockOverlap
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/CheckForWorkshopBlockOverlap")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckForWorkshopBlockOverlap([FromBody] CheckForWorkshopBlockOverlapList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.CheckForWorkshopBlockOverlap(Obj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region SelectFieldSearchParty vinay n 27/11/24  
        /// <summary>
        /// SelectFieldSearchParty
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>   
        [Route("api/HelpDeskUserLandingPage/SelectFieldSearchParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchParty(SelectFieldSearchParty2List Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];
            string value = HttpContext.Current.Request.Params["value"];
            Obj.value = string.IsNullOrEmpty(value) ? "" : value;



            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskUserLandingPageServices.SelectFieldSearchParty(Obj, connstring, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

        #region SelectServiceRequest vinay n 27/11/24  
        /// <summary>
        /// SelectServiceRequest
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>   
        [Route("api/HelpDeskUserLandingPage/SelectServiceRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceRequest(SelectServiceRequestList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskUserLandingPageServices.SelectServiceRequest(Obj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters, connstring, LogException);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

        #region LockRecord vinay n 27/11/24
        /// <summary>
        /// LockRecord
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/LockRecord")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LockRecord([FromBody] LockRecordList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.LockRecord(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region UnLockRecord vinay n 27/11/24
        /// <summary>
        /// UnLockRecord
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/UnLockRecord")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UnLockRecord([FromBody] UnLockRecordList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.UnLockRecord(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetData vinay n 27/11/24
        /// <summary>
        /// GetData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/GetData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetData([FromBody] GetDataUserLindingList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.GetData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetDealerData vinay n 27/11/24
        /// <summary>
        /// GetDealerData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/GetDealerData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetDealerData([FromBody] GetDealerDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.GetDealerData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SaveCustomer vinay n 27/11/24
        /// <summary>
        /// SaveCustomer
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/SaveCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveCustomer([FromBody] SaveCustomerList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.SaveCustomer(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetCallHistory vinay n 27/11/24
        /// <summary>
        /// GetCallHistory
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/GetCallHistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallHistory([FromBody] GetCallHistoryList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {

                Response = HelpDeskUserLandingPageServices.GetCallHistory(Obj, connstring, LogException, sidx, sord, page, rows, advnce, advnceFilters, _search, filters);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Export vinay n 27/11/24
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportUserLandingList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));


            try
            {

                Response = await HelpDeskUserLandingPageServices.Export(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response);
        }
        #endregion
        #region GetProductDetails vinay n 27/11/24
        /// <summary>
        /// GetProductDetails
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/GetProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductDetails([FromBody] GetProductDetailsUserLandingList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUserLandingPageServices.GetProductDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetOpenCampaignDetails vinay n 27/11/24
        /// <summary>
        /// GetOpenCampaignDetails
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/GetOpenCampaignDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetOpenCampaignDetails([FromBody] GetOpenCampaignDetailsList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {

                Response = HelpDeskUserLandingPageServices.GetOpenCampaignDetails(Obj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetInitialData vinay n 27/11/24
        /// <summary>
        /// GetInitialData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/GetInitialData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetInitialData([FromBody] GetInitialDataList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            try
            {

                Response = HelpDeskUserLandingPageServices.GetInitialData(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region checkDuplicateContactPerson vinay n 27/11/24
        /// <summary>
        /// checkDuplicateContactPerson
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUserLandingPage/checkDuplicateContactPerson")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult checkDuplicateContactPerson([FromBody] checkDuplicateContactPersonList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            try
            {

                Response = HelpDeskUserLandingPageServices.checkDuplicateContactPerson(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}