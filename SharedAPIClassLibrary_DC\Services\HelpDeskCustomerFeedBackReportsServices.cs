﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskCustomerFeedBackReportsServices
    {
        public static IQueryable<CustomerFeedBackReport> CustomerFeedbackReport = null;
        #region SelectCustomerFeedbackReport vinay 19/11/24
        /// <summary>
        /// SelectCustomerFeedbackReport
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="_search"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <returns></returns>
        public static IActionResult SelectCustomerFeedbackReport(SelectCustomerFeedbackReportList Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query)
        {
            var jsonResult = default(dynamic);
            var data = default(dynamic);
            //var _Data =default(dynamic);
            IQueryable<CustomerFeedBackReport> result = null;
            try
            {

                string Sear = "Norm";
                string decodedValueReg = Uri.UnescapeDataString(Obj.RegionObj);
                string decodedValueReg2 = Uri.UnescapeDataString(decodedValueReg);
                Region Regions = JObject.Parse(Common.DecryptString(decodedValueReg2)).ToObject<Region>();
                string decodedValueComp = Uri.UnescapeDataString(Obj.CompanyObj);
                string decodedValueComp2 = Uri.UnescapeDataString(decodedValueComp);

                Company companys = JObject.Parse(Common.DecryptString(decodedValueComp2)).ToObject<Company>();
                string decodedValueBranch = Uri.UnescapeDataString(Obj.BranchObj);
                string decodedValueBranch2 = Uri.UnescapeDataString(decodedValueBranch);

                Branch branchs = JObject.Parse(Common.DecryptString(decodedValueBranch2)).ToObject<Branch>();
                string decodedValueState = Uri.UnescapeDataString(Obj.StateObj);
                string decodedValueState2 = Uri.UnescapeDataString(decodedValueState);

                State1 States = JObject.Parse(Common.DecryptString(decodedValueState2)).ToObject<State1>();
                int LanguageID = Convert.ToInt32(Obj.UserLanguageID.ToString());
                int GeneralLanguageID = Convert.ToInt32(Obj.GeneralLanguageID.ToString());

                if (LanguageID == GeneralLanguageID)
                {
                    LanguageID = 0;
                }
                else
                {
                    LanguageID = LanguageID;
                }
                string decodedValueRatingObj = Uri.UnescapeDataString(Obj.RatingObj);
                string decodedValueRatingObj2 = Uri.UnescapeDataString(decodedValueRatingObj);
                string Rating = Common.DecryptString(decodedValueRatingObj2);
                Rating = Rating.TrimEnd(new char[] { ',' });
                bool IsNegFeedback = false;
                if (Rating.Split(',')[Rating.Split(',').Length - 1] == "N")
                {
                    IsNegFeedback = true;
                    Rating = Rating.Replace(",N", "");
                    Rating = Rating.Replace("N", "");
                }
                string FromDate = Common.DecryptString(Obj.FromDate);
                string ToDate = Common.DecryptString(Obj.ToDate);
                //Session["startDate"] = FromDate;
                //Session["endDate"] = ToDate;
                DateTime Fromdate = Convert.ToDateTime(FromDate).Date;
                DateTime Todate = Convert.ToDateTime(ToDate).Date;
                string RegionIds = "";
                string CompanyIds = "";
                string BranchIds = "";
                string StateIds = "";
                for (int i = 0; i < Regions.Regions.Count; i++)
                {
                    RegionIds += Regions.Regions[i].ID.ToString();
                    if (Regions.Regions.Count != i + 1)
                        RegionIds += ",";
                }

                for (int i = 0; i < States.States.Count; i++)
                {
                    StateIds += States.States[i].ID.ToString();
                    if (States.States.Count != i + 1)
                        StateIds += ",";
                }
                for (int i = 0; i < companys.Companys.Count; i++)
                {
                    CompanyIds += companys.Companys[i].ID.ToString();
                    if (companys.Companys.Count != i + 1)
                        CompanyIds += ",";
                }
                for (int i = 0; i < branchs.Branchs.Count; i++)
                {
                    BranchIds += branchs.Branchs[i].ID.ToString();
                    if (branchs.Branchs.Count != i + 1)
                        BranchIds += ",";
                }


                List<dynamic> dlist = new List<dynamic>();
                String _Query = "EXEC SP_CustomerFeedbackReport '" + BranchIds + "','" + LanguageID + "','" + Rating + "','" + Fromdate + "','" + Todate + "','" + (IsNegFeedback == true ? 1 : 0) + "'";
                var _Data = new List<CustomerFeedBackReport>();

                _Data = GetValueFromDB<List<CustomerFeedBackReport>>(_Query, null, connString);





                result = _Data.AsQueryable<CustomerFeedBackReport>();

                //FilterToolBar Search
                if (_search)
                {
                    string decodedValuefilters = Uri.UnescapeDataString(filters);

                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValuefilters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        result = result.FilterSearch<CustomerFeedBackReport>(filtersObj);
                    Sear = "Filter";
                }
                //Advance Search
                else if (advnce)
                {
                    string decodedValueQuery = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValueQuery).ToObject<AdvanceFilter>();

                    result = result.AdvanceSearch<CustomerFeedBackReport>(advnfilter);
                }
                //Sorting 
                result = result.OrderByField<CustomerFeedBackReport>(sidx, sord);

                CustomerFeedbackReport = result;

                int count = result.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                data = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = result.ToList().Paginate(page, rows),
                    Sear
                };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return new JsonResult(data);
        }

        #endregion
        #region ExportCustomerFeedbackReport vinay 20/11/24
        public static async Task<object> ExportCustomerFeedbackReport(SelectCustomerFeedbackReportList Obj, string connString, int LogException)
        {
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            IQueryable<CustomerFeedBackReport> iFeedbackArray = (IQueryable<CustomerFeedBackReport>)CustomerFeedbackReport;

            try
            {


                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "customername").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "State").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Brand").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Producttype").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "model").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Serialnum").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "UniqueIdentifier").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "caseregistrationnumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "TicketDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QuotationNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QuotationDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "workordernumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "WorkOrderDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ServiceType").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerRating").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerFeedbackDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "FeedBack").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsNegetiveFeedback").ToString());



                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Region").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "customername").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "State").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Brand").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Producttype").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "model").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Serialnum").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "UniqueIdentifier").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "caseregistrationnumber").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "TicketDate").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QuotationNumber").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QuotationDate").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "workordernumber").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "WorkOrderDate").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ServiceType").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerRating").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerFeedbackDate").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "FeedBack").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsNegetiveFeedback").ToString());
                dt1.Rows.Add(0, 0, 0, 1);


                int cnt = iFeedbackArray.AsEnumerable().Count();
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(iFeedbackArray.ElementAt(i).Region,
                        iFeedbackArray.ElementAt(i).Branch,
                        iFeedbackArray.ElementAt(i).CustomerName,
                        iFeedbackArray.ElementAt(i).State,
                        iFeedbackArray.ElementAt(i).Brand,
                        iFeedbackArray.ElementAt(i).ProductType,
                        iFeedbackArray.ElementAt(i).Model,
                        iFeedbackArray.ElementAt(i).VIN,
                        iFeedbackArray.ElementAt(i).UNIT, iFeedbackArray.ElementAt(i).TicketNo, iFeedbackArray.ElementAt(i).TicketDate,
                        iFeedbackArray.ElementAt(i).QuoteNo, iFeedbackArray.ElementAt(i).QuoteDate, iFeedbackArray.ElementAt(i).WorkOrderNo, iFeedbackArray.ElementAt(i).WorkOrderDate,
                        iFeedbackArray.ElementAt(i).ServiceType, iFeedbackArray.ElementAt(i).CustomerRating, iFeedbackArray.ElementAt(i).CustomerFeedbackDate, iFeedbackArray.ElementAt(i).Feedback, iFeedbackArray.ElementAt(i).IsNegativeFeedback);
                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = Obj.Company_ID,
                    Branch = Obj.Branch,
                    dt1 = dt1,
                    dt = dt,
                    FileName = "CustomerFeedbackReport",
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerFeedback").ToString(),
                    exprtType = Obj.exprtType,
                    UserCulture = Obj.UserCulture
                };
                var res = await DocumentExport.Export(reportExportList, connString, LogException);
                return res.Value;

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion
        #region GetValueFromDB vinay n 4/12/24
        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // If the result type is a collection (e.g., List<T>)
                        if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                        {

                            var list = Activator.CreateInstance<T>();
                            var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();
                            while (reader.Read())
                            {
                                var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }

                              ((IList)list).Add(item);
                            }
                            result = (T)list;
                        }
                        // If the result is a scalar type (like string, bool, int)
                        else if (typeof(T) == typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        // Handle cases for custom classes/complex objects
                        else if (typeof(T).IsClass && typeof(T) != typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = Activator.CreateInstance<T>(); // Create an instance of the class
                                var columnPropertyMapping = typeof(T)
                                  .GetProperties()
                                  .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                               .Select(reader.GetName)
                                                               .Contains(property.Name))
                                  .Select(property => new
                                  {
                                      Property = property,
                                      Ordinal = reader.GetOrdinal(property.Name),
                                      TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                  })
                                  .ToList();

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }
        #endregion

    }
    #region HelpDeskCustomerFeedBackReportsListsAndObjs
    public class SelectCustomerFeedbackReportList
    {
        public string RegionObj { get; set; }
        public string CompanyObj { get; set; }
        public string BranchObj { get; set; }
        public string StateObj { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string RatingObj { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        //ExportCustomerFeedbackReport
        public string UserCulture { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int exprtType { get; set; }


    }
    #endregion
    #region HelpDeskCustomerFeedBackReportsMasterClasses
    public class CustomerFeedBackReport
    {
        public string Region { get; set; }
        public string State { get; set; }
        public string CustomerName { get; set; }
        public string Brand { get; set; }
        public string ProductType { get; set; }
        public string Model { get; set; }
        public string VIN { get; set; }
        public string UNIT { get; set; }
        public string Branch { get; set; }
        public string TicketNo { get; set; }
        public string TicketDate { get; set; }

        //public string TickerDateSort { get; set; }
        public string QuoteNo { get; set; }
        public string WorkOrderNo { get; set; }
        public string WorkOrderDate { get; set; }
        //public string WorkOrderDateSort { get; set; }
        public string ServiceType { get; set; }
        public int? CustomerRating { get; set; }
        public string CustomerFeedbackDate { get; set; }
        //public string CustomerFeedbackDateSort { get; set; }
        public string Feedback { get; set; }
        public string IsNegativeFeedback { get; set; }
        public string QuoteDate { get; set; }
        //public string QuoteDateSort { get; set; }
    }
    public class Branch
    {
        public List<Branchs> Branchs
        {
            get;
            set;
        }
    }
    public class States1
    {
        public int ID { get; set; }
        public string NAME { get; set; }
    }
    public class State1
    {
        public List<States1> States
        {
            get;
            set;
        }
    }
    public class Regions
    {

        public int ID
        {
            get;
            set;
        }
        public string Name
        {
            get;
            set;
        }
    }
    public class Region
    {
        public List<Regions> Regions
        {
            get;
            set;
        }
    }
    public class Company
    {
        public List<Companys> Companys
        {
            get;
            set;
        }
    }
    public class Companys
    {

        public int ID
        {
            get;
            set;
        }
        public string Name
        {
            get;
            set;
        }
    }
    #endregion
}
