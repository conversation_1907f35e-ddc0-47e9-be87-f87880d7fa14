﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreWorkFlowEscalation1Services
    {

        #region ::: SelectCompany Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Select Company
        /// </summary> 
        /// 

        public static IActionResult SelectCompany(string connString, SelectCompanyList SelectCompanyobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var Masterdata = default(dynamic);
                int Language_ID = Convert.ToInt32(SelectCompanyobj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (SelectCompanyobj.UserLanguageCode.ToString() == SelectCompanyobj.GeneralLanguageCode.ToString())
                    {
                        // Call the stored procedure for default language
                        using (SqlCommand cmd = new SqlCommand("sp_SelectActiveCompanies", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var companies = new List<object>();
                                while (reader.Read())
                                {
                                    companies.Add(new
                                    {
                                        ID = reader["ID"],
                                        Name = reader["Name"]
                                    });
                                }

                                Masterdata = new { Data = companies };
                            }
                        }
                    }
                    else
                    {
                        // Call the stored procedure for specific locale
                        using (SqlCommand cmd = new SqlCommand("sp_SelectActiveCompaniesLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", Language_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var companies = new List<object>();
                                while (reader.Read())
                                {
                                    companies.Add(new
                                    {
                                        ID = reader["ID"],
                                        Name = reader["Name"]
                                    });
                                }

                                Masterdata = new { Data = companies };
                            }
                        }
                    }
                }

                return new JsonResult(Masterdata);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SelectWorkFlow uday Kumar J B 30-09-2024 :::

        public static IActionResult SelectWorkFlow(string connString)
        {
            var Masterdata = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("sp_SelectWorkFlows", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var workFlows = new List<object>();
                            while (reader.Read())
                            {
                                workFlows.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            Masterdata = new { Data = workFlows };
                        }
                    }
                }

                return new JsonResult(Masterdata);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region ::: Select Uday Kumar J B 30-09-2024:::

        public static IActionResult Select(string connstring, SelectWorkFlowList SelectWorkFlowobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            string AppPath = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var Masterdata = default(dynamic);
                string WFRoleNames = "-1:-------------------Select-------------------;";
                string EmployeeNames = "-1:---------Select---------;";
                int Count = 0;
                int Total = 0;

                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    conn.Open();

                    // Fetch Workflow Roles
                    List<WF_WFRole> gnmRoles = new List<WF_WFRole>();
                    using (SqlCommand cmd = new SqlCommand("sp_SelectWFRole", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@WorkFlow_ID", SelectWorkFlowobj.WorkFlow_ID);
                        cmd.Parameters.AddWithValue("@Company_ID", SelectWorkFlowobj.Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFRole role = new WF_WFRole
                                {
                                    WFRole_ID = (int)reader["WFRole_ID"],
                                    WFRole_Name = reader["WFRole_Name"].ToString()
                                };
                                gnmRoles.Add(role);
                                WFRoleNames += role.WFRole_ID + ":" + role.WFRole_Name + ";";
                            }
                        }
                    }
                    WFRoleNames = WFRoleNames.TrimEnd(';');

                    // Fetch Company Employees
                    List<GNM_CompanyEmployee> IEmployee = new List<GNM_CompanyEmployee>();
                    using (SqlCommand cmd = new SqlCommand("sp_SelectCompanyEmployees", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", SelectWorkFlowobj.Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_CompanyEmployee emp = new GNM_CompanyEmployee
                                {
                                    Company_Employee_ID = (int)reader["Company_Employee_ID"],
                                    Company_Employee_Name = reader["Company_Employee_Name"].ToString()
                                };
                                IEmployee.Add(emp);
                                EmployeeNames += emp.Company_Employee_ID + ":" + emp.Company_Employee_Name + ";";
                            }
                        }
                    }
                    EmployeeNames = EmployeeNames.TrimEnd(';');

                    // Fetch Workflow Escalations
                    List<WFEscalation> IEWFEscalation = new List<WFEscalation>();
                    using (SqlCommand cmd = new SqlCommand("sp_SelectWFEscalations", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", SelectWorkFlowobj.Company_ID);
                        cmd.Parameters.AddWithValue("@WorkFlow_ID", SelectWorkFlowobj.WorkFlow_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                IEWFEscalation.Add(new WFEscalation
                                {
                                    Escalation_ID = (int)reader["Escalation_ID"],
                                    Company_ID = SelectWorkFlowobj.Company_ID,
                                    WorkFlow_ID = SelectWorkFlowobj.WorkFlow_ID,
                                    RoleName = gnmRoles.FirstOrDefault(r => r.WFRole_ID == (int)reader["WFRole_ID"])?.WFRole_Name ?? "",
                                    Escalation_Hours = reader["Escalation_Hours"].ToString(),
                                    EmployeeName = IEmployee.FirstOrDefault(e => e.Company_Employee_ID == (int)reader["Escalate_To_EmployeeID"])?.Company_Employee_Name ?? "",
                                    Escalate_IsEmail = (bool)reader["Escalate_IsEmail"] ? "Yes" : "No",
                                    Escalate_IsMobile = (bool)reader["Escalate_IsMobile"] ? "Yes" : "No"
                                });
                            }
                        }
                    }

                    // Apply pagination and sorting
                    var IQWFEscalation = IEWFEscalation.AsQueryable();
                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {

                            IQWFEscalation = IQWFEscalation.FilterSearch<WFEscalation>(filtersObj);
                        }
                    }
                    if (advnce)
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        IQWFEscalation = IQWFEscalation.AdvanceSearch<WFEscalation>(advnfilter);
                    }
                    IQWFEscalation = IQWFEscalation.OrderByField(sidx, sord);

                    Count = IQWFEscalation.Count();
                    Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                    if (Count < (rows * page) && Count != 0)
                    {
                        page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                    }

                    var jsonData = new
                    {
                        TotalPages = Total,
                        PageNo = page,
                        RecordCount = Count,
                        rows = IQWFEscalation.Skip((page - 1) * rows).Take(rows).Select(a => new
                        {
                            ID = a.Escalation_ID,
                            edit = "<img id='" + a.Escalation_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.Escalation_ID + "' class='EscalationEdit' editmode='false'/>",
                            delete = "<input type='checkbox' key='" + a.Escalation_ID + "' defaultchecked='' id='chk" + a.Escalation_ID + "' class='EscalationDelete'/>",
                            a.Escalation_Hours,
                            a.Escalate_IsEmail,
                            a.Escalate_IsMobile,
                            a.RoleName,
                            UserName = a.EmployeeName
                        }).ToList(),
                        WFRoleNames,
                        HasRows = (IQWFEscalation.Count() > 0),
                        EmployeeNames
                    };

                    return new JsonResult(jsonData);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: Escalation AlreadyExits  Uday Kumar J B 30-09-2024:::

        public static IActionResult EscAlreadyExits(string connString, EscAlreadyExitsList EscAlreadyExitsobj)
        {
            bool repeate = false; // Default is false (not repeated)
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("sp_CheckEscalationExists", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EscalationID", EscAlreadyExitsobj.EscalationID);
                        cmd.Parameters.AddWithValue("@WFRoleValue", EscAlreadyExitsobj.WFRoleValue);
                        cmd.Parameters.AddWithValue("@WFEscalationHours", EscAlreadyExitsobj.WFEscalationHours);
                        cmd.Parameters.AddWithValue("@WFEscalateToEmployee", EscAlreadyExitsobj.WFEscalateToEmployee);

                        var result = cmd.ExecuteScalar();

                        repeate = (result != null && Convert.ToInt32(result) == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(repeate);
        }
        #endregion


        #region ::: Save Uday Kumar J B 30-09-2024:::

        public static IActionResult Save(string connString, SaveWorkFlowlist SaveWorkFlowobj)
        {
            string Msg = string.Empty;
            int Count = 0;
            JObject jObj;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                jObj = JObject.Parse(SaveWorkFlowobj.data);
                Count = jObj["rows"].Count();

                for (int i = 0; i < Count; i++)
                {
                    int Escalation_ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["Escalation_ID"]?.ToString() ?? "0");
                    int WFRole_ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["WFRole_ID"]?.ToString());
                    int Escalate_To_EmployeeID = Convert.ToInt32(jObj["rows"].ElementAt(i)["Escalate_To_EmployeeID"]?.ToString());
                    decimal Escalation_Hours = Convert.ToDecimal(jObj["rows"].ElementAt(i)["Escalation_Hours"]?.ToString());
                    bool Escalate_IsEmail = Convert.ToBoolean(jObj["rows"].ElementAt(i)["Escalate_IsEmail"]?.ToString());
                    bool Escalate_IsMobile = Convert.ToBoolean(jObj["rows"].ElementAt(i)["Escalate_IsMobile"]?.ToString());

                    // Check if Escalation exists in the database
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();

                        if (Escalation_ID > 0)
                        {
                            // If exists, update the existing escalation record
                            using (SqlCommand cmd = new SqlCommand("sp_UpdateWFEscalation", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Escalation_ID", Escalation_ID);
                                cmd.Parameters.AddWithValue("@Company_ID", SaveWorkFlowobj.Company_ID);
                                cmd.Parameters.AddWithValue("@WorkFlow_ID", SaveWorkFlowobj.WorkFlow_ID);
                                cmd.Parameters.AddWithValue("@WFRole_ID", WFRole_ID);
                                cmd.Parameters.AddWithValue("@Escalation_Hours", Escalation_Hours);
                                cmd.Parameters.AddWithValue("@Escalate_To_EmployeeID", Escalate_To_EmployeeID);
                                cmd.Parameters.AddWithValue("@Escalate_IsEmail", Escalate_IsEmail);
                                cmd.Parameters.AddWithValue("@Escalate_IsMobile", Escalate_IsMobile);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // If not exists, insert a new escalation record
                            using (SqlCommand cmd = new SqlCommand("sp_InsertWFEscalation", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Company_ID", SaveWorkFlowobj.Company_ID);
                                cmd.Parameters.AddWithValue("@WorkFlow_ID", SaveWorkFlowobj.WorkFlow_ID);
                                cmd.Parameters.AddWithValue("@WFRole_ID", WFRole_ID);
                                cmd.Parameters.AddWithValue("@Escalation_Hours", Escalation_Hours);
                                cmd.Parameters.AddWithValue("@Escalate_To_EmployeeID", Escalate_To_EmployeeID);
                                cmd.Parameters.AddWithValue("@Escalate_IsEmail", Escalate_IsEmail);
                                cmd.Parameters.AddWithValue("@Escalate_IsMobile", Escalate_IsMobile);
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 30-09-2024:::

        public static IActionResult Delete(string connString, DeleteWorkFlowList DeleteWorkFlowobj)
        {
            string Msg = string.Empty;
            JObject jObj;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                jObj = JObject.Parse(DeleteWorkFlowobj.key);
                int Count = jObj["rows"].Count();
                int ID = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        // Get the Escalation_ID from the JSON data
                        ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                        // Call stored procedure to delete the row with the corresponding Escalation_ID
                        using (SqlCommand cmd = new SqlCommand("sp_DeleteWFEscalation", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Escalation_ID", ID);

                            cmd.ExecuteNonQuery();
                        }
                    }
                }

                // Return the success message from the resource file
                Msg += CommonFunctionalities.GetResourceString(DeleteWorkFlowobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    // Return dependency error message
                    Msg += CommonFunctionalities.GetResourceString(DeleteWorkFlowobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }

            return new JsonResult(Msg);
        }
        #endregion


        #region ::: Export Uday kumar J B 30-09-2024:::
        public static async Task<object> Export(ExportWorkFlowlist ExportWorkFlowobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            DataTable Dt = new DataTable();

            try
            {
                // Retrieve data from the SelectExport method
                var EscMasterList = SelectExport(connString, ExportWorkFlowobj.CompanyID, ExportWorkFlowobj.WorkFlow_ID);

                var IQEscMaster = EscMasterList.AsQueryable().OrderByField(sidx, sord);
                // Apply standard filters if present
                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        IQEscMaster = IQEscMaster.FilterSearch(filtersObj);
                }

                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    IQEscMaster = IQEscMaster.AdvanceSearch(advnfilter);
                }

                var EscMasterArray = from a in IQEscMaster
                                     select new
                                     {
                                         a.RoleName,
                                         a.Escalation_Hours,
                                         a.EmployeeName,
                                         a.Escalate_IsEmail,
                                         a.Escalate_IsMobile
                                     };

                // Define the columns
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "role").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "escalationhours").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "escalateto").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "isemail").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "issms").ToString());

                // Alignment DataTable
                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Role");
                DtAlignment.Columns.Add("Escalation Hours");
                DtAlignment.Columns.Add("Escalate To");
                DtAlignment.Columns.Add("Is Email?");
                DtAlignment.Columns.Add("Is SMS?");
                DtAlignment.Rows.Add(0, 2, 0, 0, 0);

                // Add rows from EscMasterArray
                int Count = EscMasterArray.Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        Dt.Rows.Add(EscMasterArray.ElementAt(i).RoleName,
                                    EscMasterArray.ElementAt(i).Escalation_Hours,
                                    EscMasterArray.ElementAt(i).EmployeeName,
                                    EscMasterArray.ElementAt(i).Escalate_IsEmail,
                                    EscMasterArray.ElementAt(i).Escalate_IsMobile);
                    }

                    // Additional DataTable for company and workflow details
                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "company").ToString());
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "workflow").ToString());
                    Dt1.Rows.Add(Common.DecryptString(ExportWorkFlowobj.CompanyName), Common.DecryptString(ExportWorkFlowobj.WorkFlowName));

                    ExportList reportExportList = new ExportList
                    {
                        Company_ID = ExportWorkFlowobj.Company_ID,
                        Branch = ExportWorkFlowobj.Branch,
                        dt1 = DtAlignment,
                        dt = Dt,
                        FileName = "WorkFlowEscalation",
                        Header = CommonFunctionalities.GetResourceString(ExportWorkFlowobj.UserCulture.ToString(), "WorkFlowEscalation").ToString(),
                        exprtType = ExportWorkFlowobj.exprtType,
                        UserCulture = ExportWorkFlowobj.UserCulture
                    };

                    var result = await DocumentExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //return DocumentExport.Export(reportExportList, connString, LogException);
                    // Export the data (uncomment and implement the actual export method)
                    // ReportExport.Export(ExportWorkFlowobj.exprtType, Dt, Dt1, DtAlignment, "WorkFlowEscalation", CommonFunctionalities.GetGlobalResourceObject(ExportWorkFlowobj.UserCulture.ToString(), "WorkFlowEscalation").ToString());
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return null;
        }


        public static List<WFEscalationDTO> SelectExport(string connString, int Company_ID, int WorkFlow_ID)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Placeholder for role and employee names
                string WFRoleNames = "-1:-------------------Select-------------------;";
                string EmployeeNames = "-1:---------Select---------;";

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch Workflow Roles
                    List<WF_WFRole> gnmRoles = new List<WF_WFRole>();
                    using (SqlCommand cmd = new SqlCommand("sp_SelectWFRole", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@WorkFlow_ID", WorkFlow_ID);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFRole role = new WF_WFRole
                                {
                                    WFRole_ID = (int)reader["WFRole_ID"],
                                    WFRole_Name = reader["WFRole_Name"].ToString()
                                };
                                gnmRoles.Add(role);
                                WFRoleNames += role.WFRole_ID + ":" + role.WFRole_Name + ";";
                            }
                        }
                    }
                    WFRoleNames = WFRoleNames.TrimEnd(';');

                    // Fetch Company Employees
                    List<GNM_CompanyEmployee> IEmployee = new List<GNM_CompanyEmployee>();
                    using (SqlCommand cmd = new SqlCommand("sp_SelectCompanyEmployees", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_CompanyEmployee emp = new GNM_CompanyEmployee
                                {
                                    Company_Employee_ID = (int)reader["Company_Employee_ID"],
                                    Company_Employee_Name = reader["Company_Employee_Name"].ToString()
                                };
                                IEmployee.Add(emp);
                                EmployeeNames += emp.Company_Employee_ID + ":" + emp.Company_Employee_Name + ";";
                            }
                        }
                    }
                    EmployeeNames = EmployeeNames.TrimEnd(';');

                    // Fetch Workflow Escalations
                    List<WFEscalationDTO> IEWFEscalationDTO = new List<WFEscalationDTO>();
                    using (SqlCommand cmd = new SqlCommand("sp_SelectWFEscalations", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@WorkFlow_ID", WorkFlow_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                IEWFEscalationDTO.Add(new WFEscalationDTO
                                {
                                    RoleName = gnmRoles.FirstOrDefault(r => r.WFRole_ID == (int)reader["WFRole_ID"])?.WFRole_Name ?? "",
                                    Escalation_Hours = reader["Escalation_Hours"].ToString(),
                                    EmployeeName = IEmployee.FirstOrDefault(e => e.Company_Employee_ID == (int)reader["Escalate_To_EmployeeID"])?.Company_Employee_Name ?? "",
                                    Escalate_IsEmail = (bool)reader["Escalate_IsEmail"] ? "Yes" : "No",
                                    Escalate_IsMobile = (bool)reader["Escalate_IsMobile"] ? "Yes" : "No"
                                });
                            }
                        }
                    }

                    // Return the list of WFEscalationDTO objects
                    return IEWFEscalationDTO;
                }
            }
            catch (Exception ex)
            {
                // Optionally log the exception if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Return an empty list in case of an exception
                return new List<WFEscalationDTO>();
            }
        }


        #endregion



        #region ::: Work Folw List and obj Classes uday Kumar J B 30-09-2024 :::
        public class SelectCompanyList
        {
            public int UserLanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }
        public class SelectWorkFlowList
        {
            public int WorkFlow_ID { get; set; }
            public int Company_ID { get; set; }

        }
        public class EscAlreadyExitsList
        {
            public int EscalationID { get; set; }
            public int WFRoleValue { get; set; }
            public decimal WFEscalationHours { get; set; }
            public int WFEscalateToEmployee { get; set; }
        }

        public class SaveWorkFlowlist
        {
            public int WorkFlow_ID { get; set; }
            public int Company_ID { get; set; }
            public string data { get; set; }
        }
        public class DeleteWorkFlowList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }

        }
        public class ExportWorkFlowlist
        {
            public int exprtType { get; set; }
            public string CompanyName { get; set; }
            public string WorkFlowName { get; set; }
            public string UserCulture { get; set; }
            public int WorkFlow_ID { get; set; }
            public int Company_ID { get; set; }
            public int CompanyID { get; set; }
            public int Branch { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
        }

        public class WFEscalationDTO
        {
            public string RoleName { get; set; }
            public string Escalation_Hours { get; set; }
            public string EmployeeName { get; set; }
            public string Escalate_IsEmail { get; set; }
            public string Escalate_IsMobile { get; set; }
        }

        #endregion


        #region ::: WorkFlow Classes Uday Kumar J B 30-09-2024 :::
        class WFEscalation
        {
            public int Escalation_ID { get; set; }
            public int Company_ID { get; set; }
            public int WorkFlow_ID { get; set; }
            public string RoleName { get; set; }
            public string Escalation_Hours { get; set; }
            public string EmployeeName { get; set; }
            public string Escalate_IsEmail { get; set; }
            public string Escalate_IsMobile { get; set; }
        }
        public class WF_WFRole
        {
            public int WFRole_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public string WFRole_Name { get; set; }

            public bool WfRole_IsAdmin { get; set; }

            public bool WfRole_AutoAllocationAllowed { get; set; }

            public bool? WFRole_IsRoleExternal { get; set; }

            public int? WFRole_ExternalCompany_ID { get; set; }

            public virtual ICollection<WF_WFRoleUser> GNM_WFRoleUser { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

            public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

            public virtual ICollection<WF_WFRoleLocale> GNM_WFRoleLocale { get; set; }

            public WF_WFRole()
            {
                GNM_WFRoleUser = new HashSet<WF_WFRoleUser>();
                GNM_WFStepLink = new HashSet<WF_WFStepLink>();
                GNM_WFRoleLocale = new HashSet<WF_WFRoleLocale>();
            }
        }
        public class WF_WFStepLink
        {
            public int WFStepLink_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public int Company_ID { get; set; }

            public int FrmWFSteps_ID { get; set; }

            public int WFAction_ID { get; set; }

            public int ToWFSteps_ID { get; set; }

            public int? Addresse_WFRole_ID { get; set; }

            public byte Addresse_Flag { get; set; }

            public bool IsSMSSentToCustomer { get; set; }

            public bool IsEmailSentToCustomer { get; set; }

            public bool IsSMSSentToAddressee { get; set; }

            public bool IsEmailSentToAddresse { get; set; }

            public bool AutoAllocationAllowed { get; set; }

            public bool IsVersionEnabled { get; set; }

            public int? InvokeParentWF_ID { get; set; }

            public int? InvokeParentWFLink_ID { get; set; }

            public int? InvokeChildObject_ID { get; set; }

            public int? InvokeChildObjectAction { get; set; }

            public int? WFField_ID { get; set; }

            public string AutoCondition { get; set; }

            public virtual WF_WFAction GNM_WFAction { get; set; }

            public virtual WF_WFRole GNM_WFRole { get; set; }

            public virtual WF_WFSteps GNM_WFSteps { get; set; }

            public virtual WF_WFSteps GNM_WFSteps1 { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        }
        public class WF_WorkFlow
        {
            public int WorkFlow_ID { get; set; }

            public string WorkFlow_Name { get; set; }

            public bool? AllQueue_Filter_IsBranch { get; set; }

            public virtual ICollection<WF_WFAction> GNM_WFAction { get; set; }

            public virtual ICollection<WF_WFField> GNM_WFField { get; set; }

            public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

            public virtual ICollection<WF_WFRole> GNM_WFRole { get; set; }

            public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }

            public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

            public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }

            public WF_WorkFlow()
            {
                GNM_WFAction = new HashSet<WF_WFAction>();
                GNM_WFField = new HashSet<WF_WFField>();
                GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
                GNM_WFRole = new HashSet<WF_WFRole>();
                GNM_WFSteps = new HashSet<WF_WFSteps>();
                GNM_WFStepLink = new HashSet<WF_WFStepLink>();
                GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
            }
        }

        public class WF_WFActionLocale
        {
            public int WFActionLocale_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public int WFAction_ID { get; set; }

            public int Language_ID { get; set; }

            public string WFAction_Name { get; set; }

            public string ActionCode { get; set; }

            public virtual WF_WFAction GNM_WFAction { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        }
        public class WF_WFFieldValue
        {
            public int WFFieldValue_ID { get; set; }

            public int WFField_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public int Company_ID { get; set; }

            public int Transaction_ID { get; set; }

            public string WorkFlowFieldValue { get; set; }

            public virtual WF_WFField GNM_WFField { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        }

        public class WF_WFField
        {
            public int WFField_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public string WorkFlowFieldName { get; set; }

            public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

            public WF_WFField()
            {
                GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
            }
        }
        public class WF_WFSteps
        {
            public int WFSteps_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public string WFStep_Name { get; set; }

            public int WFStepType_ID { get; set; }

            public int WFStepStatus_ID { get; set; }

            public bool WFStep_IsActive { get; set; }

            public string BranchCode { get; set; }

            public virtual WF_WFStepType GNM_WFStepType { get; set; }

            public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

            public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

            public virtual ICollection<WF_WFStepLink> GNM_WFStepLink1 { get; set; }

            public virtual ICollection<WF_WFStepsLocale> GNM_WFStepsLocale { get; set; }

            public WF_WFSteps()
            {
                GNM_WFStepLink = new HashSet<WF_WFStepLink>();
                GNM_WFStepLink1 = new HashSet<WF_WFStepLink>();
                GNM_WFStepsLocale = new HashSet<WF_WFStepsLocale>();
            }
        }
        public class WF_WFStepType
        {
            public int WFStepType_ID { get; set; }

            public string WFStepType_Nm { get; set; }

            public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }

            public WF_WFStepType()
            {
                GNM_WFSteps = new HashSet<WF_WFSteps>();
            }
        }
        public class WF_WFStepsLocale
        {
            public int WFStepsLocale_ID { get; set; }

            public int WFSteps_ID { get; set; }

            public string WFStep_Name { get; set; }

            public int Language_ID { get; set; }

            public virtual WF_WFSteps GNM_WFSteps { get; set; }
        }
        public class WF_WFAction
        {
            public int WFAction_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public string WFAction_Name { get; set; }

            public string ActionCode { get; set; }

            public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

            public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

            public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }

            public WF_WFAction()
            {
                GNM_WFStepLink = new HashSet<WF_WFStepLink>();
                GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
            }
        }

        public class WF_WFRoleLocale
        {
            public int WFRoleLocale_ID { get; set; }

            public int WFRole_ID { get; set; }

            public string WFRole_Name { get; set; }

            public int Language_ID { get; set; }

            public virtual WF_WFRole GNM_WFRole { get; set; }
        }

        public class WF_WFRoleUser
        {
            public int WFRoleUser_ID { get; set; }

            public int WFRole_ID { get; set; }

            public int UserID { get; set; }

            public int ApprovalLimit { get; set; }

            public virtual WF_WFRole GNM_WFRole { get; set; }
        }


        public partial class GNM_CompanyEmployee
        {
            public GNM_CompanyEmployee()
            {
                this.GNM_EmployeeTrainingDetails = new HashSet<GNM_EmployeeTrainingDetails>();
                this.GNM_CompanyEmployeeLocale = new HashSet<GNM_CompanyEmployeeLocale>();
                this.GNM_CompanyEmployeeSkillset = new HashSet<GNM_CompanyEmployeeSkillset>();
                this.GNM_EmployeeBranch = new HashSet<GNM_EmployeeBranch>();
                this.GNM_EmployeeETODetails = new HashSet<GNM_EmployeeETODetails>();
                this.GNM_EmployeeETOLogDetails = new HashSet<GNM_EmployeeETOLogDetails>();
                this.GNM_EmployeedownLines = new HashSet<GNM_EmployeedownLines>();
                this.GNM_EmployeedownLines1 = new HashSet<GNM_EmployeedownLines>();
                this.GNM_CompanyEmployeeQUALIFICATION = new HashSet<GNM_CompanyEmployeeQUALIFICATION>();
                this.GNM_CompanyEmployeeEXPERIENCE = new HashSet<GNM_CompanyEmployeeEXPERIENCE>();
            }

            public int Company_Employee_ID { get; set; }
            public string Employee_ID { get; set; }
            public string Company_Employee_Name { get; set; }
            public int Company_ID { get; set; }
            public int Country_ID { get; set; }
            public int State_ID { get; set; }
            public string Company_Employee_MobileNumber { get; set; }
            public string Company_Employee_Landline_Number { get; set; }
            public string Company_Employee_ZipCode { get; set; }
            public Nullable<System.DateTime> Company_Employee_ActiveFrom { get; set; }
            public Nullable<System.DateTime> Company_Employee_ValidateUpTo { get; set; }
            public bool Company_Employee_Active { get; set; }
            public Nullable<int> Company_Employee_Manager_ID { get; set; }
            public string Company_Employee_Address { get; set; }
            public string Company_Employee_Location { get; set; }
            public string Company_Employee_Email { get; set; }
            public int Company_Employee_Department_ID { get; set; }
            public int Company_Employee_Designation_ID { get; set; }
            public int ModifiedBy { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }
            public Nullable<decimal> HourlyRate { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public Nullable<bool> IsEligibleForOT { get; set; }
            public Nullable<byte> ExemptionHours { get; set; }
            public Nullable<bool> IsOnJob { get; set; }
            public Nullable<int> JobID { get; set; }
            public Nullable<bool> IsEligibleForIncentive { get; set; }
            public Nullable<bool> IsUnderProductiveMonitoring { get; set; }
            public Nullable<bool> IsConsideredForPayroll { get; set; }
            public Nullable<int> Bay_ID { get; set; }
            public string PHOTONAME { get; set; }
            public string EmployeeImagePath { get; set; }
            public Nullable<System.DateTime> ClockedOnJobStartTime { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
            public virtual ICollection<GNM_EmployeeTrainingDetails> GNM_EmployeeTrainingDetails { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeLocale> GNM_CompanyEmployeeLocale { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeSkillset> GNM_CompanyEmployeeSkillset { get; set; }
            public virtual ICollection<GNM_EmployeeBranch> GNM_EmployeeBranch { get; set; }
            public virtual ICollection<GNM_EmployeeETODetails> GNM_EmployeeETODetails { get; set; }
            public virtual ICollection<GNM_EmployeeETOLogDetails> GNM_EmployeeETOLogDetails { get; set; }
            public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines { get; set; }
            public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines1 { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeQUALIFICATION> GNM_CompanyEmployeeQUALIFICATION { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeEXPERIENCE> GNM_CompanyEmployeeEXPERIENCE { get; set; }
        }

        #endregion

    }
}
