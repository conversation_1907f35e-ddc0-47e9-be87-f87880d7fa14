﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Tr_CaseRegistrationCountServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Tr_CaseRegistrationCountController : ApiController
    {


        #region ::: Get Party Detail Grid Uday kumar J B 18-11-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/SelectPartyDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyDetailGrid([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridList HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseRegistrationCountServices.SelectPartyDetailGrid(HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Model Detail Grid Uday Kumar J B 18-11-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        ///
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/SelectModelDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModelDetailGrid([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridList HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseRegistrationCountServices.SelectModelDetailGrid(HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: load Reference Master data Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/loadMasters")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult loadMasters([FromBody] HelpDesk_Tr_CaseRegistrationCountloadMastersList HelpDesk_Tr_CaseRegistrationCountloadMastersobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseRegistrationCountServices.loadMasters(HelpDesk_Tr_CaseRegistrationCountloadMastersobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: load Product Type Uday kumar J B 18-11-2024:::
        /// <summary>
        /// To load Product Type
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/ProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ProductType([FromBody] HelpDesk_Tr_CaseRegistrationCountProductTypeList HelpDesk_Tr_CaseRegistrationCountProductTypesobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseRegistrationCountServices.ProductType(HelpDesk_Tr_CaseRegistrationCountProductTypesobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Party Dtails Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/GetPartyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetails([FromBody] HelpDesk_Tr_CaseRegistrationCountGetPartyDetailsList HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseRegistrationCountServices.GetPartyDetails(HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: Get Model Dtails Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/GetModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetModel([FromBody] HelpDesk_Tr_CaseRegistrationCountGetModelList HelpDesk_Tr_CaseRegistrationCountGetModelsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseRegistrationCountServices.GetModel(HelpDesk_Tr_CaseRegistrationCountGetModelsobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: Load Brand Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Brand
        /// </summary>
        /// <returns>...</returns>
        ///   
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/GetModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBrand([FromBody] HelpDesk_Tr_CaseRegistrationCountLoadBrandList HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseRegistrationCountServices.LoadBrand(HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: Select Service Request Count Year Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/SelectYearReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectYearReport([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseRegistrationCountServices.SelectYearReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: Select Service Request Count Month wise Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start
        //
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/SelectMonthReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMonthReport([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseRegistrationCountServices.SelectMonthReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion





        #region ::: Select Service Request Count Day wise Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/SelectDayReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDayReport([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseRegistrationCountServices.SelectDayReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion




        #region ::: Select Service Request Count Type Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/SelectTypeReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectTypeReport([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseRegistrationCountServices.SelectTypeReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: To Export Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDesk_Tr_CaseRegistrationCount/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.sidx;
            string sord = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.sord;
            string filter = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.filter;
            string advnceFilter = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Tr_CaseRegistrationCountServices.Export(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion




    }
}