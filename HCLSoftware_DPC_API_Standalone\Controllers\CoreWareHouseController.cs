﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreWareHouseController : ApiController
    {
        #region LoadBranchDropdown vinay n 21/8/24
        /// <summary>
        /// LoadBranchDropdown
        /// </summary>
        /// <param name="LoadBranchDropdownWareHouseListObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/LoadBranchDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBranchDropdown([FromBody] LoadBranchDropdownWareHouseList LoadBranchDropdownWareHouseListObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.LoadBranchDropdown(LoadBranchDropdownWareHouseListObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: Select vinay n 21/8/24
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="getLandingGridDataObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] getLandingGridDataList getLandingGridDataObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreWareHouseServices.Select(getLandingGridDataObj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters, connstring, LogException);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region :::Save vinay n 21/8/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="SaveWareHouseListObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveWareHouseList SaveWareHouseListObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.Save(SaveWareHouseListObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: CheckWareHouse vinay n 21/8/24
        /// <summary>
        /// CheckWareHouse
        /// </summary>
        /// <param name="CheckWareHouseObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/CheckWareHouse")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckWareHouse([FromBody] CheckWareHouse2List CheckWareHouseObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.CheckWareHouse(CheckWareHouseObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region :::SelectParticularWareHouse  vinay n 21/8/24
        /// <summary>
        /// SelectParticularWareHouse
        /// </summary>
        /// <param name="SelectParticularWareHouseObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/SelectParticularWareHouse")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularWareHouse([FromBody] SelectParticularWareHouseList SelectParticularWareHouseObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.SelectParticularWareHouse(SelectParticularWareHouseObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Delete vinay n 21/8/24
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DeleteWhereHouseObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteWhereHouseList DeleteWhereHouseObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.Delete(DeleteWhereHouseObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region UpdateLocale vinay n 21/8/24
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// <param name="UpdateLocaleWhereHouseObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleWhereHouseList UpdateLocaleWhereHouseObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.UpdateLocale(UpdateLocaleWhereHouseObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region CheckIfDefaultWarehouseExists vinay n 21/8/24
        /// <summary>
        /// CheckIfDefaultWarehouseExists
        /// </summary>
        /// <param name="CheckIfDefaultWarehouseExistsObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/CheckIfDefaultWarehouseExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckIfDefaultWarehouseExists([FromBody] CheckIfDefaultWarehouseExistsList CheckIfDefaultWarehouseExistsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.CheckIfDefaultWarehouseExists(CheckIfDefaultWarehouseExistsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region CheckWareHouseLocale vinay n 21/8/24
        /// <summary>
        /// CheckWareHouseLocale
        /// </summary>
        /// <param name="CheckWareHouseLocaleObj"></param>
        /// <returns></returns>
        [Route("api/CoreWareHouse/CheckWareHouseLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckWareHouseLocale([FromBody] CheckWareHouseLocaleList CheckWareHouseLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreWareHouseServices.CheckWareHouseLocale(CheckWareHouseLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Export
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreWareHouse/Export")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] getLandingGridDataList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





            try
            {


                object Response = await CoreWareHouseServices.Export(ExportObj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion
    }


}