//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_RefMasterDetail
    {
        public WF_RefMasterDetail()
        {
            this.GNM_Branch = new HashSet<WF_Branch>();
            this.GNM_Company = new HashSet<WF_Company>();
            this.GNM_Company1 = new HashSet<WF_Company>();
            this.GNM_CompanyEmployee = new HashSet<WF_CompanyEmployee>();
            this.GNM_CompanyEmployee1 = new HashSet<WF_CompanyEmployee>();
            this.GNM_CompanyEmployee2 = new HashSet<WF_CompanyEmployee>();
            this.GNM_MenuLocale = new HashSet<WF_MenuLocale>();
            this.GNM_ModuleLocale = new HashSet<WF_ModuleLocale>();
            this.GNM_User = new HashSet<WF_User>();
            this.GNM_Branch1 = new HashSet<WF_Branch>();
            this.GNM_Branch2 = new HashSet<WF_Branch>();
            this.GNM_Branch21 = new HashSet<WF_Branch>();
            this.GNM_CompanyEmployee3 = new HashSet<WF_CompanyEmployee>();
            this.GNM_UserLocale = new HashSet<WF_UserLocale>();
        }
    
        public int RefMasterDetail_ID { get; set; }
        public bool RefMasterDetail_IsActive { get; set; }
        public string RefMasterDetail_Short_Name { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int RefMaster_ID { get; set; }
        public bool RefMasterDetail_IsDefault { get; set; }
    
        public virtual ICollection<WF_Branch> GNM_Branch { get; set; }
        public virtual ICollection<WF_Company> GNM_Company { get; set; }
        public virtual ICollection<WF_Company> GNM_Company1 { get; set; }
        public virtual WF_Company GNM_Company2 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee1 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee2 { get; set; }
        public virtual ICollection<WF_MenuLocale> GNM_MenuLocale { get; set; }
        public virtual ICollection<WF_ModuleLocale> GNM_ModuleLocale { get; set; }
        public virtual ICollection<WF_User> GNM_User { get; set; }
        public virtual ICollection<WF_Branch> GNM_Branch1 { get; set; }
        public virtual ICollection<WF_Branch> GNM_Branch2 { get; set; }
        public virtual ICollection<WF_Branch> GNM_Branch21 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee3 { get; set; }
        public virtual ICollection<WF_UserLocale> GNM_UserLocale { get; set; }
    }
}
