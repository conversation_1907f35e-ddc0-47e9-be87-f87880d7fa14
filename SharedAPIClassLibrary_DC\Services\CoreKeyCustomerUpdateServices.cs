﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreKeyCustomerUpdateServices
    {
        static string AppPath = string.Empty;

        #region ::: Get Party Master /Mithun :::
        /// <summary>
        /// To Insert and Update Hourly Rate
        /// </summary>

        public static IQueryable<GNM_Party> GetPartyMaster(SelectPartyMasterList GetPartyMasterObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query, int partyType)
        {
            string query = "";
            string wherecondition = "";
            string queryCount = "";
            List<GNM_Party> partyList = new List<GNM_Party>();
            IQueryable<GNM_Party> result = null;
            int companyID = Convert.ToInt32(GetPartyMasterObj.Company_ID);
            bool filterPartyBasedOnCompany = false;


            try
            {
                // Get filter condition based on company
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string filterQuery = "SELECT Param_value FROM GNM_CompParam WHERE Company_ID = @CompanyID AND UPPER(Param_Name) = 'FILTERPARTYBASEDONCOMPANY'";
                    using (SqlCommand cmd = new SqlCommand(filterQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        filterPartyBasedOnCompany = cmd.ExecuteScalar()?.ToString().ToUpper() == "TRUE";
                    }

                    // Set where condition based on the filter result
                    if (filterPartyBasedOnCompany)
                    {
                        wherecondition = "WHERE Company_ID = @CompanyID AND Party_IsActive = 1";
                    }
                    else
                    {
                        wherecondition = "WHERE PartyType = @PartyType AND Party_IsActive = 1";
                    }

                    // Handle search filters
                    if (_search)
                    {
                        string decodedSearchString = Common.DecryptString(filters);

                        // Perform the second decryption
                        decodedSearchString = Common.DecryptString(decodedSearchString);

                        // Now parse the double-decoded string into the Filters object
                        Filters filtersObj = JObject.Parse(decodedSearchString).ToObject<Filters>();

                        if (filtersObj.rules.Count() > 0)
                        {
                            foreach (var rule in filtersObj.rules)
                            {
                                // Handle the Party_IsActive case
                                if (rule.field == "Party_IsActive")
                                {
                                    rule.data = rule.data.ToUpper() == "YES" ? "1" : "0";
                                }

                                // Build the where condition and prevent SQL injection
                                wherecondition += $" AND {rule.field} LIKE '%{rule.data.Replace("'", "''")}%'"; // Safeguard against SQL injection
                            }
                        }
                    }

                    // Handle advanced search filters
                    if (advnce)
                    {
                        string op = "";
                        AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(Query)).ToObject<AdvanceFilter>();

                        if (advnfilter.rules.Count() > 0)
                        {
                            // Start building the WHERE clause
                            List<SqlParameter> sqlParameters = new List<SqlParameter>(); // List to store SQL parameters
                            StringBuilder whereconditionBuilder = new StringBuilder(wherecondition); // Use StringBuilder to efficiently build the WHERE clause

                            for (int i = 0; i < advnfilter.rules.Count(); i++)
                            {
                                var rule = advnfilter.rules.ElementAt(i);

                                // Special case for "IsKeyCustomer"
                                if (rule.Field == "IsKeyCustomer")
                                {
                                    rule.Data = rule.Data.ToUpper() == "YES" ? "1" : "0"; // Convert "YES" or "NO" to "1" or "0"
                                }

                                // Prevent SQL injection by escaping single quotes
                                rule.Data = rule.Data.Replace("'", "''");

                                op = Common.getoperator(rule.Operator);

                                string parameterName = "@param" + i;  // Unique parameter for each filter rule
                                sqlParameters.Add(new SqlParameter(parameterName, rule.Data));

                                if (i == 0)
                                {
                                    // First rule, just add to the where condition
                                    if (op == "like")
                                    {
                                        whereconditionBuilder.Append(" AND " + rule.Field + " " + op + " '%' + " + parameterName + " + '%'");
                                    }
                                    else
                                    {
                                        whereconditionBuilder.Append(" AND " + rule.Field + " " + op + " " + parameterName);
                                    }
                                }
                                else
                                {
                                    // Subsequent rules, use the "AND/OR" condition
                                    if (op == "like")
                                    {
                                        whereconditionBuilder.Append(" " + rule.Condition + " " + rule.Field + " " + op + " '%' + " + parameterName + " + '%'");
                                    }
                                    else
                                    {
                                        whereconditionBuilder.Append(" " + rule.Condition + " " + rule.Field + " " + op + " " + parameterName);
                                    }
                                    wherecondition = whereconditionBuilder.ToString();
                                    wherecondition = wherecondition.Replace("= 'is null'", "");
                                }
                            }
                        }
                        // Final WHERE clause
                    }
                    // Query for count
                    queryCount = $"SELECT COUNT(Party_ID) FROM GNM_Party {wherecondition} AND PartyType = @PartyType";
                    using (SqlCommand countCmd = new SqlCommand(queryCount, conn))
                    {
                        countCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        countCmd.Parameters.AddWithValue("@PartyType", partyType);
                        int totalCount = (int)countCmd.ExecuteScalar();
                    }

                    // Query for data
                    query = $@"(SELECT *, ROW_NUMBER() OVER(ORDER BY {sidx} {sord}) AS row_number 
                       FROM GNM_Party {wherecondition} AND PartyType = @PartyType)";

                    using (SqlCommand dataCmd = new SqlCommand(query, conn))
                    {
                        dataCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        dataCmd.Parameters.AddWithValue("@PartyType", partyType);

                        using (SqlDataReader reader = dataCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_Party party = new GNM_Party
                                {
                                    Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                    Party_Name = reader["Party_Name"].ToString(),
                                    Party_Location = reader["Party_Location"].ToString(),
                                    Party_Phone = reader["Party_Phone"].ToString(),
                                    Party_Mobile = reader["Party_Mobile"].ToString(),
                                    IsKeyCustomer = Convert.ToBoolean(reader["IsKeyCustomer"]),
                                };
                                partyList.Add(party);
                            }
                        }
                    }

                    // Convert list to IQueryable
                    return partyList.AsQueryable();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle error
            }
            return partyList.AsQueryable();
        }

        #endregion

        #region ::: SelectPartyMaster /Mithun :::
        /// <summary>
        /// To SelectPartyMaster
        /// </summary>
        public static IActionResult SelectPartyMaster(SelectPartyMasterList SelectPartyMasterObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            int count = 0;
            int total = 0;

            IQueryable<GNM_Party> IQParty = null;
            var jsonData = default(dynamic);
            int partyType = Convert.ToInt32(SelectPartyMasterObj.partyType);
            GetPartyMasterList GetPartyMasterObj = new GetPartyMasterList();
            try
            {

                IQParty = GetPartyMaster(SelectPartyMasterObj, constring, LogException, sidx, sord, page, rows, _search, filters, advnce, Query, partyType);
                count = IQParty.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in IQParty
                            select new
                            {
                                //edit = "<img  key='" + q.Party_ID + "' alt='Edit' party='" + q.Party_Name + "' src='" + AppPath + "/Content/edit.gif' class='editPartyMaster' editmode='false'/>",
                                edit = "<a title='Edit' href='#' key='" + q.Party_ID + "' alt='Edit' party='" + q.Party_Name + "' class='editPartyMaster font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + q.Party_ID + "' type='checkbox' defaultchecked='' class='DelPartyMaster' />",
                                q.Party_ID,
                                Party_Name = (q.Party_Name),
                                q.Party_Phone,
                                Party_Location = (q.Party_Location),
                                q.Party_Mobile,
                                Party_IsActive = q.Party_IsActive == true ? "Yes" : "No",
                                Local = "<img id='" + q.Party_ID + "' src='" + AppPath + "/Content/local.png' class='PartyLocale' width='20' height='20' alt='Localize' title='Localize'/>",
                                IsKeyCustomer = q.IsKeyCustomer == true ? "Yes" : "No",
                            }
                    ).ToList().Paginate(page, rows),
                    //filter = Request.Params["filters"],
                    //advanceFilter = Request.Params["Query"],
                    //_search = Request.Params["_search"],
                    //advnce = Request.Params["advnce"],
                    partyType = partyType,
                    sidx = sidx,
                    sord = sord,
                    NRows = rows
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region::: UpdateKeyCustomer /Mithun :::
        /// <summary>
        /// To UpdateKeyCustomer
        /// </summary>

        public static IActionResult UpdateKeyCustomer(UpdateKeyCustomerList UpdateKeyCustomerObj, string constring, int LogException)
        {
            string status = string.Empty;

            try
            {
                int BranchID = Convert.ToInt32(UpdateKeyCustomerObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];

                GNM_User User = UpdateKeyCustomerObj.UserDetails.FirstOrDefault();
                JObject jObj = JObject.Parse(UpdateKeyCustomerObj.data);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        int Party_ID = jObj["rows"][i]["Party_ID"].ToObject<int>();
                        bool KeyCustomer = jObj["rows"][i]["KeyCustomer"].ToObject<bool>();

                        using (SqlCommand cmd = new SqlCommand("Up_Upd_Am_Erp_UpdateKeyCustomer", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.Add(new SqlParameter("@Party_ID", SqlDbType.Int) { Value = Party_ID });
                            cmd.Parameters.Add(new SqlParameter("@KeyCustomer", SqlDbType.Bit) { Value = KeyCustomer });
                            cmd.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Convert.ToInt32(UpdateKeyCustomerObj.Company_ID) });
                            cmd.Parameters.Add(new SqlParameter("@Branch_ID", SqlDbType.Int) { Value = BranchID });
                            cmd.Parameters.Add(new SqlParameter("@User_ID", SqlDbType.Int) { Value = User.User_ID });
                            cmd.Parameters.Add(new SqlParameter("@Menu_ID", SqlDbType.Int) { Value = Convert.ToInt32(UpdateKeyCustomerObj.MenuID) });
                            cmd.Parameters.Add(new SqlParameter("@LoggedINDateTime", SqlDbType.DateTime) { Value = Convert.ToDateTime(UpdateKeyCustomerObj.LoggedINDateTime) });

                            cmd.ExecuteNonQuery();
                            //   gbl.InsertGPSDetails(Convert.ToInt32(UpdateKeyCustomerObj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("KeyCustomerUpdate",constring), Party_ID, 0, 0, "Updated Key Customer For Party- " + Party_ID + "", false, Convert.ToInt32(UpdateKeyCustomerObj.MenuID), Convert.ToDateTime(UpdateKeyCustomerObj.LoggedINDateTime));

                        }
                    }
                }

                status = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return status;
            return new JsonResult(status);
        }


        #endregion

        #region::: Export/Mithun :::

        //public void PartyExport(SelectPartyMasterList ExportObj, string constring, int LogException)
        //{
        //    try
        //    {
        //        int BranchID = Convert.ToInt32(ExportObj.Branch);
        //         GNM_User User = (GNM_User)Session["UserDetails"];

        //        List<GNM_Party> wf = new List<GNM_Party>();
        //        IQueryable<GNM_Party> Result = GetPartyMaster(ExportObj, constring, LogException, ExportObj.sidx, ExportObj.sord, ExportObj.page, ExportObj.rows, ExportObj._search, ExportObj.filters, ExportObj.advnce, ExportObj.Query, ExportObj.partyType);

        //        if (wf.Count > 0)
        //        {
        //            DataTable dt = new DataTable();
        //            dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Party").ToString());
        //            dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Location").ToString());
        //            dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Phone").ToString());
        //            dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Mobile").ToString());
        //            dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IsKeyCustomer").ToString());

        //            DataTable dt1 = new DataTable();
        //            dt1.Columns.Add("Party");
        //            dt1.Columns.Add("Location");
        //            dt1.Columns.Add("Phone");
        //            dt1.Columns.Add("Mobile");
        //            dt1.Columns.Add("IsKeyCustomer");
        //            dt1.Rows.Add(0, 0, 2, 2, 1);

        //            for (int i = 0; i < wf.Count; i++)
        //            {
        //                dt.Rows.Add(wf[i].Party_Name, wf[i].Party_Location, wf[i].Party_Phone, wf[i].Party_Mobile, (wf[i].IsKeyCustomer == true ? "Yes" : "No"));
        //            }

        //            DataTable DtCriteria = new DataTable();
        //            DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "PartyType").ToString());
        //            DtCriteria.Rows.Add(Common.DecryptString(ExportObj.partyType));

        //            ReportExport.Export(exprtType, dt, DtCriteria, dt1, "Party", "Key Customer");

        //            gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), BranchID, User.User_ID, Common.GetObjectID("KeyCustomerUpdate"), 0, 0, 0, "Key Customer-Export", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 0)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //         RedirectToAction("Error");
        //    }
        //}

        #endregion





        public class SelectPartyMasterList
        {
            public int partyType { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
            public bool _search { get; set; }
            public bool advnce { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public int rows { get; set; }
            public int page { get; set; }
        }
        public class GetPartyMasterList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
            public bool _search { get; set; }
            public bool advnce { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string partyType { get; set; }
            public int rows { get; set; }
            public int page { get; set; }
        }
        public class UpdateKeyCustomerList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
    }
}
