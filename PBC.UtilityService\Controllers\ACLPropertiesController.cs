using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for ACL Properties operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class ACLPropertiesController : ControllerBase
    {
        private readonly IACLPropertiesService _aclPropertiesService;
        private readonly ILogger<ACLPropertiesController> _logger;

        public ACLPropertiesController(
            IACLPropertiesService aclPropertiesService,
            ILogger<ACLPropertiesController> logger)
        {
            _aclPropertiesService = aclPropertiesService;
            _logger = logger;
        }

        /// <summary>
        /// Gets all ACL Properties
        /// </summary>
        /// <returns>Collection of ACL Properties</returns>
        /// <response code="200">Returns the list of ACL Properties</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<ACLPropertiesDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<ACLPropertiesDto>>> GetAll()
        {
            try
            {
                _logger.LogInformation("GET /api/aclproperties - Getting all ACL Properties");
                var result = await _aclPropertiesService.GetAllAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all ACL Properties");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving ACL Properties");
            }
        }

        /// <summary>
        /// Gets ACL Properties by Object ID
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <returns>ACL Properties if found</returns>
        /// <response code="200">Returns the ACL Properties</response>
        /// <response code="404">ACL Properties not found</response>
        /// <response code="400">Invalid Object ID</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("{objectId:int}")]
        [ProducesResponseType(typeof(ACLPropertiesDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ACLPropertiesDto>> GetByObjectId([Range(1, int.MaxValue)] int objectId)
        {
            try
            {
                _logger.LogInformation("GET /api/aclproperties/{ObjectId} - Getting ACL Properties", objectId);
                
                var result = await _aclPropertiesService.GetByObjectIdAsync(objectId);
                if (result == null)
                {
                    _logger.LogWarning("ACL Properties not found for Object ID: {ObjectId}", objectId);
                    return NotFound($"ACL Properties not found for Object ID: {objectId}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ACL Properties for Object ID: {ObjectId}", objectId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving ACL Properties");
            }
        }

        /// <summary>
        /// Creates new ACL Properties
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Created ACL Properties</returns>
        /// <response code="201">ACL Properties created successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="409">ACL Properties already exists for the Object ID</response>
        /// <response code="500">Internal server error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ACLPropertiesDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ACLPropertiesDto>> Create([FromBody] CreateACLPropertiesRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/aclproperties - Creating ACL Properties for Object ID: {ObjectId}", request.Object_ID);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _aclPropertiesService.CreateAsync(request);
                return CreatedAtAction(nameof(GetByObjectId), new { objectId = result.Object_ID }, result);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Conflict creating ACL Properties for Object ID: {ObjectId}", request.Object_ID);
                return Conflict(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating ACL Properties for Object ID: {ObjectId}", request.Object_ID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while creating ACL Properties");
            }
        }

        /// <summary>
        /// Updates existing ACL Properties
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <param name="request">Update request</param>
        /// <returns>Updated ACL Properties</returns>
        /// <response code="200">ACL Properties updated successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="404">ACL Properties not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPut("{objectId:int}")]
        [ProducesResponseType(typeof(ACLPropertiesDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ACLPropertiesDto>> Update([Range(1, int.MaxValue)] int objectId, [FromBody] UpdateACLPropertiesRequest request)
        {
            try
            {
                _logger.LogInformation("PUT /api/aclproperties/{ObjectId} - Updating ACL Properties", objectId);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _aclPropertiesService.UpdateAsync(objectId, request);
                if (result == null)
                {
                    return NotFound($"ACL Properties not found for Object ID: {objectId}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ACL Properties for Object ID: {ObjectId}", objectId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating ACL Properties");
            }
        }

        /// <summary>
        /// Deletes ACL Properties by Object ID
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">ACL Properties deleted successfully</response>
        /// <response code="400">Invalid Object ID</response>
        /// <response code="404">ACL Properties not found</response>
        /// <response code="500">Internal server error</response>
        [HttpDelete("{objectId:int}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> Delete([Range(1, int.MaxValue)] int objectId)
        {
            try
            {
                _logger.LogInformation("DELETE /api/aclproperties/{ObjectId} - Deleting ACL Properties", objectId);
                
                var result = await _aclPropertiesService.DeleteAsync(objectId);
                if (!result)
                {
                    return NotFound($"ACL Properties not found for Object ID: {objectId}");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ACL Properties for Object ID: {ObjectId}", objectId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting ACL Properties");
            }
        }

        /// <summary>
        /// Checks if ACL Properties exists for the given Object ID
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <returns>Boolean indicating existence</returns>
        /// <response code="200">Returns existence status</response>
        /// <response code="400">Invalid Object ID</response>
        /// <response code="500">Internal server error</response>
        [HttpHead("{objectId:int}")]
        [HttpGet("{objectId:int}/exists")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> Exists([Range(1, int.MaxValue)] int objectId)
        {
            try
            {
                _logger.LogInformation("HEAD/GET /api/aclproperties/{ObjectId}/exists - Checking ACL Properties existence", objectId);
                
                var result = await _aclPropertiesService.ExistsAsync(objectId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking ACL Properties existence for Object ID: {ObjectId}", objectId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking ACL Properties existence");
            }
        }
    }
}
