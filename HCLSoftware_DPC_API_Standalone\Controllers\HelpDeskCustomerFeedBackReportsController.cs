﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskCustomerFeedBackReportsController : ApiController
    {
        #region ::: SelectCustomerFeedbackReport vinay n 22/11/24 :::
        /// <summary>
        /// SelectCustomerFeedbackReport
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/HelpDeskCustomerFeedBackReports/SelectCustomerFeedbackReport")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCustomerFeedbackReport([FromBody] SelectCustomerFeedbackReportList Obj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskCustomerFeedBackReportsServices.SelectCustomerFeedbackReport(Obj, connstring, LogException, sidx, sord, page, rows, _search, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region Export vinay n 3/12/24
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/HelpDeskCustomerFeedBackReports/ExportCustomerFeedbackReport")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ExportCustomerFeedbackReport([FromBody] SelectCustomerFeedbackReportList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));


            try
            {

                Response = await HelpDeskCustomerFeedBackReportsServices.ExportCustomerFeedbackReport(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response);
        }
        #endregion
    }
}