﻿#region ::: Namespaces Used :::
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Configuration;
using LS = LogSheetExporter;
using System.Data;
using WorkFlow.Controllers;
using WorkFlow.Models;
using WorkFlow.Controllers.WorkFlow.Controllers;

#endregion
namespace AMMSCore.Controllers
{
    public class CoreWorkFlowEscalationController : Controller
    {
        #region ::: Variables Used :::
        dynamic empty = null;
        dynamic Dummy = null;
        GenEntities CompanyClient = new GenEntities(ConfigurationManager.AppSettings.Get("DbName"));
        WorkFlowEntity wrkFlowEnt = new WorkFlowEntity(ConfigurationManager.AppSettings.Get("DbName"));
        WorkFlowEscalationEntities WFEscalationClient = new WorkFlowEscalationEntities(ConfigurationManager.AppSettings.Get("DbName"));        
        WorkFlowAPIController API = new WorkFlowAPIController(ConfigurationManager.AppSettings.Get("DbName"));
        JObject jObj;
        JTokenReader jTR;
        static string AppPath = string.Empty;
        string exMsg = string.Empty;
        DataTable Dt;
        int Count = 0;
        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
        #endregion
        #region ::: CoreWorkFlowEscalationView :::
        public ActionResult CoreWorkFlowEscalationView()
        {
            try
            {
                int objectid = Convert.ToInt32(Request.Params[0]);
                Session["ObjectID"] = objectid;
                AppPath = System.Web.HttpContext.Current.Request.ApplicationPath == "/" ? "" : System.Web.HttpContext.Current.Request.ApplicationPath;
                return View("~/Views/Core/CoreWorkFlowEscalationView.cshtml");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                return RedirectToAction("Error");
            }
        }
        #endregion
        #region ::: InitialSetup :::
        /// <summary>
        /// Initial Setup
        /// </summary>    
        public ActionResult InitialSetup()
        {
            try
            {
                int ObjectID = Convert.ToInt32(Session["ObjectID"]);
                AppPath = Request.ApplicationPath == "/" ? "" : Request.ApplicationPath;
                JsonResult jr = WFCommon.InitialSetup(ObjectID);
                return Json(jr, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RedirectToAction("Error");
            }
        }
        #endregion
        #region ::: SelectCompany :::
        /// <summary>
        /// To Select Company
        /// </summary> 
        public ActionResult SelectCompany()
        {
            try
            {                
                var Masterdata = Dummy;
                int Language_ID = Convert.ToInt32(Session["UserLanguageID"]);

                //if (Session["UserLanguageCode"].ToString() == Session["GeneralLanguageCode"].ToString())
                //{
                    IEnumerable<WF_Company> Company = CompanyClient.WF_Company.Where(i => i.Company_Active == true);
                    Masterdata = new
                    {
                        Data = from C in Company
                               orderby C.Company_Name
                               select new
                               {
                                   ID = C.Company_ID,
                                   Name = C.Company_Name
                               }
                    };
                //}               

                return Json(Masterdata, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RedirectToAction("Error");
            }

        }
        #endregion
        #region ::: SelectWorkFlow :::
        public ActionResult SelectWorkFlow()
        {
            var Masterdata = Dummy;
            try
            {
                IEnumerable<WF_WorkFlow> workFlow = wrkFlowEnt.WF_WorkFlow;
                Masterdata = new
                {
                    Data = from C in workFlow
                           orderby C.WorkFlow_Name
                           select new
                           {
                               ID = C.WorkFlow_ID,
                               Name = C.WorkFlow_Name
                           }
                };
                return Json(Masterdata, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RedirectToAction("Error");
            }
        }
        #endregion
        #region ::: LogClientException :::
        /// <summary>
        /// To Log Client Exception
        /// </summary>
        public void LogClientException()
        {
            try
            {
                if (Request.QueryString.Count > 0)
                {

                    string MethodName = Request.QueryString["MethodName"];
                    string Description = Request.QueryString["Description"];
                    string ErrorType = Request.QueryString["ErrorType"];
                    int Number = Convert.ToInt32(Request.QueryString["Number"]);

                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(Number, "JavaScript Error: CoreCompanyCalenderMasterView/" + MethodName, Description, ErrorType);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }
        #endregion
        #region ::: Select :::
        public ActionResult Select(int Company_ID, int WorkFlow_ID, string sidx, string sord, int page, int rows)
        {
            try
            {
                int Count = 0;
                int Total = 0;
                IEnumerable<WF_WFRole> gnmRoles = wrkFlowEnt.WF_WFRole.Where(a => a.WorkFlow_ID == WorkFlow_ID);
                IEnumerable<WF_CompanyEmployee> IEmployee = CompanyClient.WF_CompanyEmployee.Where(a => a.Company_ID == Company_ID && a.Company_Employee_Active == true);
                IEnumerable<GNM_WFEscalation> IEWFEscalationList = WFEscalationClient.GNM_WFEscalation.Where(i => i.Company_ID == Company_ID && i.WorkFlow_ID == WorkFlow_ID);
                IQueryable<WFEscalation> IQWFEscalation = null;
                IEnumerable<WFEscalation> IEWFEscalation = null;
                var jsonData = Dummy;
                IEWFEscalation = from a in IEWFEscalationList
                                 join b in gnmRoles on a.WFRole_ID equals b.WFRole_ID into RoleList
                                 from Rolefinal in RoleList.DefaultIfEmpty(new WF_WFRole { WFRole_Name = "" })
                                 join c in IEmployee on a.Escalate_To_EmployeeID equals c.Company_Employee_ID into UserList
                                 from Userfinal in UserList.DefaultIfEmpty(new WF_CompanyEmployee { Company_Employee_Name = "" })
                                 select new WFEscalation
                                 {
                                     Escalation_ID = a.Escalation_ID,
                                     Company_ID = a.Company_ID,
                                     WorkFlow_ID = a.WorkFlow_ID,
                                     RoleName = Rolefinal.WFRole_Name,
                                     Escalation_Hours = a.Escalation_Hours.ToString(),
                                     EmployeeName = Userfinal.Company_Employee_Name,
                                     Escalate_IsEmail = a.Escalate_IsEmail == true ? "Yes" : "No",
                                     Escalate_IsMobile = a.Escalate_IsMobile == true ? "Yes" : "No",
                                     CCToAssignee = a.CCToAssignee==true?"Yes":"No"
                                 };

                string WFRoleNames = "-1:-------------------Select-------------------;";
                for (int i = 0; i < gnmRoles.Count(); i++)
                {
                    WFRoleNames = WFRoleNames + gnmRoles.ElementAt(i).WFRole_ID + ":" + gnmRoles.ElementAt(i).WFRole_Name + ";";
                }
                WFRoleNames = WFRoleNames.TrimEnd(new char[] { ';' });
                string EmployeeNames = "-1:---------Select---------;";
                for (int i = 0; i < IEmployee.Count(); i++)
                {
                    EmployeeNames = EmployeeNames + IEmployee.ElementAt(i).Company_Employee_ID + ":" + IEmployee.ElementAt(i).Company_Employee_Name + ";";
                }
                EmployeeNames = EmployeeNames.TrimEnd(new char[] { ';' });

                WFRoleNames = WFRoleNames.TrimEnd(new char[] { ';' });
                IQWFEscalation = IEWFEscalation.AsQueryable<WFEscalation>();
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(WFCommon.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                    if (filters.rules.Count() > 0)
                        IQWFEscalation = IQWFEscalation.FilterSearch<WFEscalation>(filters);

                }
                if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();
                    IQWFEscalation = IQWFEscalation.AdvanceSearch<WFEscalation>(advnfilter);
                }
                IQWFEscalation = IQWFEscalation.OrderByField<WFEscalation>(sidx, sord);

                Session["IQWFEscalation"] = IQWFEscalation;
                Count = IQWFEscalation.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                jsonData = new
                {
                    TotalPages = Total,
                    PageNo = page,
                    RecordCount = Count,
                    rows = (from a in IQWFEscalation.AsEnumerable()
                            select new
                            {
                                ID = a.Escalation_ID,
                                edit = "<img id='" + a.Escalation_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.Escalation_ID + "' class='EscalationEdit' editmode='false'/>",
                                delete = "<input type='checkbox' key='" + a.Escalation_ID + "' defaultchecked=''  id='chk" + a.Escalation_ID + "' class='EscalationDelete'/>",
                                a.Escalation_Hours,
                                a.Escalate_IsEmail,
                                a.Escalate_IsMobile,
                                a.CCToAssignee,
                                a.RoleName,
                                UserName = a.EmployeeName
                            }).ToList().Paginate(page, rows),
                    WFRoleNames,
                    HasRows = (IQWFEscalation.Count() == 0) ? false : true,
                    EmployeeNames
                };
                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RedirectToAction("Error");
            }
        }
        #endregion
        #region ::: Escalation AlreadyExits :::
        public bool EscAlreadyExits(int prim, int sec, int ActualRID, int ActualUID)
        {
            bool repeate = true;
            try
            {
                if (prim == ActualRID && sec == ActualUID)
                {
                    repeate = false;
                }
                else if (prim != ActualRID && sec != ActualUID)
                {
                    repeate = WFEscalationClient.GNM_WFEscalation.Where(a => a.WFRole_ID == prim && a.Escalate_To_EmployeeID == sec && a.WFRole_ID != ActualRID && a.Escalate_To_EmployeeID != ActualUID).Count() > 0 ? true : false;
                }
                else if (prim != ActualRID && sec == ActualUID)
                {
                    repeate = WFEscalationClient.GNM_WFEscalation.Where(a => a.WFRole_ID == prim && a.Escalate_To_EmployeeID == sec && a.WFRole_ID != ActualRID).Count() > 0 ? true : false;
                }
                else if (prim == ActualRID && sec != ActualUID)
                {
                    repeate = WFEscalationClient.GNM_WFEscalation.Where(a => a.WFRole_ID == prim && a.Escalate_To_EmployeeID == sec && a.Escalate_To_EmployeeID != ActualUID).Count() > 0 ? true : false;
                }
            }
            catch (Exception e) { }
            return repeate;
        }
        #endregion
        #region ::: Save :::
        public string Save(int Company_ID, int WorkFlow_ID)
        {
            string Msg = string.Empty;
            int Count = 0;
            try
            {
                WF_User UserDetails = (WF_User)Session["WFEscalationDetails"];
                jObj = JObject.Parse(Request.Params["data"]);
                Count = jObj["rows"].Count();
                for (int i = 0; i < Count; i++)
                {

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Escalation_ID"]);
                    jTR.Read();
                    int Escalation_ID = Convert.ToInt32(jTR.Value.ToString() == "" ? "0" : jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFRole_ID"]);
                    jTR.Read();
                    int WFRole_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Escalate_To_EmployeeID"]);
                    jTR.Read();
                    int Escalate_To_EmployeeID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Escalation_Hours"]);
                    jTR.Read();
                    decimal Escalation_Hours = Convert.ToDecimal(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Escalate_IsEmail"]);
                    jTR.Read();
                    bool Escalate_IsEmail = Convert.ToBoolean(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Escalate_IsMobile"]);
                    jTR.Read();
                    bool Escalate_IsMobile = Convert.ToBoolean(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CCToAssignee"]);
                    jTR.Read();
                    bool CCToAssignee = Convert.ToBoolean(jTR.Value.ToString());

                    GNM_WFEscalation UpdateWFEsacalation = Dummy;
                    UpdateWFEsacalation = WFEscalationClient.GNM_WFEscalation.Where(a => a.Escalation_ID == Escalation_ID && a.WorkFlow_ID == WorkFlow_ID && a.Company_ID == Company_ID).FirstOrDefault();
                    if (UpdateWFEsacalation != null)
                    {
                        UpdateWFEsacalation.Escalate_To_EmployeeID = Escalate_To_EmployeeID;
                        UpdateWFEsacalation.Escalation_Hours = Escalation_Hours;
                        UpdateWFEsacalation.Company_ID = Company_ID;
                        UpdateWFEsacalation.WFRole_ID = WFRole_ID;
                        UpdateWFEsacalation.WorkFlow_ID = WorkFlow_ID;
                        UpdateWFEsacalation.Escalate_IsEmail = Escalate_IsEmail;
                        UpdateWFEsacalation.Escalate_IsMobile = Escalate_IsMobile;
                        UpdateWFEsacalation.CCToAssignee = CCToAssignee;
                        WFEscalationClient.SaveChanges();
                    }
                    else
                    {
                        GNM_WFEscalation NewRow = new GNM_WFEscalation();
                        NewRow.Company_ID = Company_ID;
                        NewRow.WorkFlow_ID = WorkFlow_ID;
                        NewRow.WFRole_ID = WFRole_ID;
                        NewRow.Escalation_Hours = Escalation_Hours;
                        NewRow.Escalate_To_EmployeeID = Escalate_To_EmployeeID;
                        NewRow.Escalate_IsEmail = Escalate_IsEmail;
                        NewRow.Escalate_IsMobile = Escalate_IsMobile;
                        NewRow.CCToAssignee = CCToAssignee;
                        WFEscalationClient.GNM_WFEscalation.Add(NewRow);
                        WFEscalationClient.SaveChanges();
                    }
                }
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                Msg = string.Empty;
            }
            return Msg;
        }
        #endregion
        #region ::: Delete :::
        public string Delete()
        {
            string Msg = string.Empty;
            try
            {
                jObj = JObject.Parse(Request.Params["key"]);
                Count = jObj["rows"].Count();
                int ID = 0;
                GNM_WFEscalation deleteRow = null;
                for (int i = 0; i < Count; i++)
                {
                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);
                    deleteRow = WFEscalationClient.GNM_WFEscalation.Where(a => a.Escalation_ID == ID).FirstOrDefault();
                    WFEscalationClient.GNM_WFEscalation.Remove(deleteRow);
                }
                WFEscalationClient.SaveChanges();
                Msg += HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return Msg;
        }
        #endregion
        #region ::: Export :::
        //public void Export(int exprtType, string CompanyName, string WorkFlowName)
        //{
        //    IQueryable<WFEscalation> IQEscMaster = null;
        //    Dt = new DataTable();
        //    try
        //    {
        //        IQEscMaster = ((IQueryable<WFEscalation>)Session["IQWFEscalation"]);
        //        var EscMasterArray = from a in IQEscMaster.AsEnumerable()
        //                             select new
        //                             {
        //                                 a.RoleName,
        //                                 a.Escalation_Hours,
        //                                 a.EmployeeName,
        //                                 a.Escalate_IsEmail,
        //                                 a.Escalate_IsMobile
        //                             };

        //        Dt.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "role").ToString());
        //        Dt.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "escalationhours").ToString());
        //        Dt.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "escalateto").ToString());
        //        Dt.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "isemail").ToString());
        //        Dt.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "issms").ToString());

        //        DataTable DtAlignment = new DataTable();
        //        DtAlignment.Columns.Add("Role");
        //        DtAlignment.Columns.Add("Escalation Hours");
        //        DtAlignment.Columns.Add("Escalate To");
        //        DtAlignment.Columns.Add("Is Email?");
        //        DtAlignment.Columns.Add("Is SMS?");
        //        DtAlignment.Rows.Add(0, 2, 0, 0, 0);

        //        Count = EscMasterArray.AsEnumerable().Count();
        //        if (Count > 0)
        //        {
        //            for (int i = 0; i < Count; i++)
        //            {
        //                Dt.Rows.Add(EscMasterArray.ElementAt(i).RoleName, EscMasterArray.ElementAt(i).Escalation_Hours, EscMasterArray.ElementAt(i).EmployeeName, EscMasterArray.ElementAt(i).Escalate_IsEmail, EscMasterArray.ElementAt(i).Escalate_IsMobile);
        //            }

        //            DataTable Dt1 = new DataTable();
        //            Dt1.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "company").ToString());
        //            Dt1.Columns.Add(@HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "workflow").ToString());
        //            Dt1.Rows.Add(WFCommon.DecryptString(CompanyName), WFCommon.DecryptString(WorkFlowName));

        //            ReportExport.Export(exprtType, Dt, Dt1, DtAlignment, "WorkFlowEscalation", @HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "WorkFlowEscalation").ToString());
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //        RedirectToAction("Error");
        //    }
        //}
        #endregion
    }
    #region ::: Classes Created :::
    class WFEscalation
    {
        public int Escalation_ID { get; set; }
        public int Company_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string RoleName { get; set; }
        public string Escalation_Hours { get; set; }
        public string EmployeeName { get; set; }
        public string Escalate_IsEmail { get; set; }
        public string Escalate_IsMobile { get; set; }
        public string CCToAssignee { get; set; }
    }
    #endregion
}
