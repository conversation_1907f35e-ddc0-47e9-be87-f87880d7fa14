﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreServiceChargesServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreServiceChargesMasterController : ApiController
    {

        #region ::: SelectServiceCharges Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// Method to Load English Grid 
        /// </summary> 
        /// 

        [Route("api/CoreServiceChargesMaster/SelectServiceCharges")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceCharges([FromBody] SelectServiceChargesList SelectServiceChargesobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string rowsParam = HttpContext.Current.Request.Params["rows"];

            // Decode the URL-encoded string
            rowsParam = HttpUtility.UrlDecode(rowsParam);

            // Remove non-numeric characters
            rowsParam = rowsParam.Trim('[', ']');

            int rows;
            if (int.TryParse(rowsParam, out rows))
            {
                rows = Convert.ToInt32(rowsParam);
            }
            else
            {
                // Handle the case where rowsParam is null, empty, or not a valid integer
                rows = 10; // or any default value you prefer
            }

            string sidx = HttpContext.Current.Request.Params["sidx"];

            string pageParam = HttpContext.Current.Request.Params["page"];
            int page = 1; // Default value

            if (!string.IsNullOrEmpty(pageParam) && int.TryParse(pageParam, out int parsedPage))
            {
                page = parsedPage;
            }
            else
            {

                page = 1;
            }

            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceChargesServices.SelectServiceCharges(connString, SelectServiceChargesobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region :::To Load Grid Locale Uday Kumar J B 15-07-2024 :::
        /// <summary>
        ///To Load Grid Locale
        /// </summary>
        ///
        [Route("api/CoreServiceChargesMaster/LoadGridLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadGridLocale([FromBody] LoadGridLocaleCoreServiceChargesList LoadGridLocaleCoreServiceChargesobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceChargesServices.LoadGridLocale(connString, LoadGridLocaleCoreServiceChargesobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertServiceChargeHeader 15-07-2024 :::
        /// <summary>
        /// Method to Insert Service Charges 
        /// </summary>  
        /// 
        [Route("api/CoreServiceChargesMaster/InsertServiceChargeHeader")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertServiceChargeHeader([FromBody] InsertServiceChargeHeaderList InsertServiceChargeHeaderobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.InsertServiceChargeHeader(connString, InsertServiceChargeHeaderobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertServiceChargeHeaderLocale  Uday Kumar J B 15-07-2024 :::
        /// <summary>
        ///  Method to Insert Locale Service Charges
        /// </summary>  
        /// 
        [Route("api/CoreServiceChargesMaster/InsertServiceChargeHeaderLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertServiceChargeHeaderLocale([FromBody] InsertServiceChargeHeaderLocaleList InsertServiceChargeHeaderLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.InsertServiceChargeHeaderLocale(connString, InsertServiceChargeHeaderLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSingleServiceCharge Uday Kumar J B 15-07-2024 :::
        /// <summary>
        /// To select the Single Service Charge
        /// </summary>  
        ///
        [Route("api/CoreServiceChargesMaster/SelectSingleServiceCharge")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSingleServiceCharge([FromBody] SelectSingleServiceChargeList SelectSingleServiceChargeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.SelectSingleServiceCharge(connString, SelectSingleServiceChargeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSingleServiceChargeLocale Uday Kumar J B 16-07-2024 :::
        /// <summary>
        ///  To select the Single Locale Service Charge
        /// </summary> 
        /// 
        [Route("api/CoreServiceChargesMaster/SelectSingleServiceChargeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSingleServiceChargeLocale([FromBody] SelectSingleServiceChargeLocaleList SelectSingleServiceChargeLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.SelectSingleServiceChargeLocale(connString, SelectSingleServiceChargeLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectServiceChargesDetail Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To Select the Service Charge Details
        /// </summary>   
        /// 
        [Route("api/CoreServiceChargesMaster/SelectServiceChargesDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceChargesDetail([FromBody] SelectServiceChargesDetailList SelectServiceChargesDetailobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"] ?? "0";
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"] ?? "asc";
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceChargesServices.SelectServiceChargesDetail(connString, SelectServiceChargesDetailobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: ServiceChargeExport Uday Kumar J B 16-07-2024 Pending :::
        /// <summary>
        /// Exporting Service Charges Grid
        /// </summary>
        /// 
        [Route("api/CoreServiceChargesMaster/ServiceChargeExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ServiceChargeExport([FromBody] ServiceChargeExportList ServiceChargeExportobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ServiceChargeExportobj.sidx;
            string sord = ServiceChargeExportobj.sord;
            string filter = ServiceChargeExportobj.filter;
            string advnceFilter = ServiceChargeExportobj.advanceFilter;

            try
            {


                object Response = await CoreServiceChargesServices.ServiceChargeExport(ServiceChargeExportobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: SelectServiceChargesDetailLocale Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To Select the Service Charge Details
        /// </summary>   
        /// 
        [Route("api/CoreServiceChargesMaster/SelectServiceChargesDetailLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceChargesDetailLocale([FromBody] SelectServiceChargesDetailLocaleList SelectServiceChargesDetailLocaleobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceChargesServices.SelectServiceChargesDetailLocale(connString, SelectServiceChargesDetailLocaleobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: LoadBrandDropdown Uday Kumar J B 16-07-2024:::
        /// <summary>
        ////to Select Brand Details Based on the login Company
        /// </summary> 
        /// 
        [Route("api/CoreServiceChargesMaster/LoadBrandDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBrandDropdown([FromBody] LoadBrandDropdownList LoadBrandDropdownobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.LoadBrandDropdown(connString, LoadBrandDropdownobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: LoadMachineTypeDropdown Uday Kumar J B 16-07-2024:::
        /// <summary>
        ////to Select ProductType Based on the BrandID 
        /// </summary> 
        /// 
        [Route("api/CoreServiceChargesMaster/LoadMachineTypeDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadMachineTypeDropdown([FromBody] LoadMachineTypeDropdownList LoadMachineTypeDropdownobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.LoadMachineTypeDropdown(connString, LoadMachineTypeDropdownobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: LoadModelDropdown Uday Kumar J B 16-07-2024:::
        /// <summary>
        ////to Select Model BAsed on the product Type
        /// </summary> 
        ///
        [Route("api/CoreServiceChargesMaster/LoadModelDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadModelDropdown([FromBody] LoadModelDropdownList LoadModelDropdownobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.LoadModelDropdown(connString, LoadModelDropdownobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertServiceChargeDetails Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// Method to insert Service Charge Details
        /// </summary> 
        /// 
        [Route("api/CoreServiceChargesMaster/InsertServiceChargeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertServiceChargeDetails([FromBody] InsertServiceChargeDetailsList InsertServiceChargeDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.InsertServiceChargeDetails(connString, InsertServiceChargeDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteServiceCharge Uday Kumar J B 16-07-2024 :::
        /// <summary>
        /// to Delete the Service Charge
        /// </summary>
        /// 
        [Route("api/CoreServiceChargesMaster/DeleteServiceCharge")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteServiceCharge([FromBody] DeleteServiceChargeList DeleteServiceChargeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.DeleteServiceCharge(connString, DeleteServiceChargeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DelServiceChargesDetails  Uday Kumar J B 16-07-2024 :::
        /// <summary>
        /// to Delete the Service Charges Details
        /// </summary>
        /// 
        [Route("api/CoreServiceChargesMaster/DelServiceChargesDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DelServiceChargesDetails([FromBody] DelServiceChargesDetailsList DelServiceChargesDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.DelServiceChargesDetails(connString, DelServiceChargesDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ValidateServiceCode Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To Validate PartNumber for duplicate 
        /// </summary>
        ///
        [Route("api/CoreServiceChargesMaster/ValidateServiceCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateServiceCode([FromBody] ValidateServiceCodeList ValidateServiceCodeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.ValidateServiceCode(connString, ValidateServiceCodeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ServiceChargeProdExits Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>
        /// 
        [Route("api/CoreServiceChargesMaster/ServiceChargeProdExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ServiceChargeProdExits([FromBody] ServiceChargeProdExitsList ServiceChargeProdExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.ServiceChargeProdExits(connString, ServiceChargeProdExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductType Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To select ProductType
        /// </summary>   
        /// 
        [Route("api/CoreServiceChargesMaster/SelectProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductType([FromBody] SelectProductTypeLista SelectProductTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.SelectProductType(connString, SelectProductTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectModel Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To select Model
        /// </summary>
        /// 
        [Route("api/CoreServiceChargesMaster/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] SelectModelList SelectModelobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreServiceChargesServices.SelectModel(connString, SelectModelobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}