﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Resources;
using System.Security.Cryptography;
using System.Text;

namespace AMMSCore.Utilities
{
    public class Utilities
    {
        #region
        /// <summary>
        /// Calculate Total Pages
        /// </summary>
        /// <param name="numberOfRecords"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static int CalculateTotalPages(long numberOfRecords, Int32 pageSize)
        {
            long result;
            int totalPages;

            Math.DivRem(numberOfRecords, pageSize, out result);

            if (result > 0)
                totalPages = (int)((numberOfRecords / pageSize)) + 1;
            else
                totalPages = (int)(numberOfRecords / pageSize);

            return totalPages;
        }
        #endregion

        #region
        /// <summary>
        /// Check if date is a valid format
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        /// 
        public static Boolean IsDate(string date)
        {
            DateTime dateTime;
            return DateTime.TryParse(date, out dateTime);
        }
        #endregion

        #region
        /// <summary>
        /// IsNumeric
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static Boolean IsNumeric(object entity)
        {
            if (entity == null) return false;

            int result;
            return int.TryParse(entity.ToString(), out result);
        }
        #endregion

        #region
        /// <summary>
        /// IsDouble
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static Boolean IsDouble(object entity)
        {
            if (entity == null) return false;

            string e = entity.ToString();

            // Loop through all instances of the string 'text'.
            int count = 0;
            int i = 0;
            while ((i = e.IndexOf(".", i)) != -1)
            {
                i += ".".Length;
                count++;
            }
            if (count > 1) return false;

            e = e.Replace(".", "");

            int result;
            return int.TryParse(e, out result);
        }
        #endregion

        #region
        public static List<String> Message(string message)
        {
            List<String> returnMessage = new List<String>();
            returnMessage.Add(message);
            return returnMessage;
        }
        #endregion

        #region 

        //        private void SetPassword(string user, string userPassword)
        //{
        //   string pwdToHash = userPassword + "^Y8~JJ"; // ^Y8~JJ is my hard-coded salt
        //   string hashToStoreInDatabase = BCrypt.HashPassword(pwdToHash, BCrypt.GenerateSalt());
        //   using (SqlConnection sqlConn = new System.Data.SqlClient.SqlConnection(...)
        //   {
        //     sqlConn.Open();
        //     SqlCommand cmSql = sqlConn.CreateCommand();
        //     cmSql.CommandText = "UPDATE LOGINS SET PASSWORD=@parm1 WHERE USERNAME=@parm2";
        //     cmSql.Parameters.Add("@parm1", SqlDbType.Char);
        //     cmSql.Parameters.Add("@parm2", SqlDbType.VarChar);
        //     cmSql.Parameters["@parm1"].Value = hashToStoreInDatabase;
        //     cmSql.Parameters["@parm2"].Value = user;
        //     cmSql.ExecuteNonQuery();
        //   }
        // }

        //        private bool DoesPasswordMatch(string hashedPwdFromDatabase, string userEnteredPassword)
        //        {
        //            return BCrypt.CheckPassword(userEnteredPassword + "^Y8~JJ", hashedPwdFromDatabase);
        //        }


        public string CalculateMD5Hash(string input)
        {
            // step 1, calculate MD5 hash from input
            MD5 md5 = System.Security.Cryptography.MD5.Create();
            byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
            byte[] hash = md5.ComputeHash(inputBytes);

            // step 2, convert byte array to hex string
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < hash.Length; i++)
            {
                sb.Append(hash[i].ToString("x2"));
            }
            return sb.ToString();
        }

        public string GenerateDMSPassword(string userPassword)
        {
            string hashToStoreInDatabase = "";
            //string Connstring = System.Configuration.ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            //string pwdToHash = userPassword + "^ZD46~RD"; //  is our DMS hard-coded salt
            //string hashToStoreInDatabase = BCrypt.HashPassword(pwdToHash, BCrypt.GenerateSalt());
            return hashToStoreInDatabase;
        }

        #endregion

        #region::: GetGlobalResourceObject :::
        public static IActionResult GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            var jsonData = default(dynamic);

            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                //return resourceValue;
                return new JsonResult(resourceValue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                // return string.Empty;
                return new JsonResult(jsonData);
            }
        }
        #endregion

        #region ::: GetMonthName:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(int ID, string Culture)
        {
            string MonthName = string.Empty;
            try
            {
                switch (ID)
                {
                    case 1:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "January").ToString();
                        break;
                    case 2:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "February").ToString();
                        break;
                    case 3:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "March").ToString();
                        break;
                    case 4:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "April").ToString();
                        break;
                    case 5:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "May").ToString();
                        break;
                    case 6:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "June").ToString();
                        break;
                    case 7:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "July").ToString();
                        break;
                    case 8:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "August").ToString();
                        break;
                    case 9:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "September").ToString();
                        break;
                    case 10:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "October").ToString();
                        break;
                    case 11:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "November").ToString();
                        break;
                    case 12:
                        MonthName = GetGlobalResourceObject(Culture.ToString(), "December").ToString();
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {

            }
            return MonthName;
        }
        #endregion

        #region ::: GetPariority:::
        /// <summary>
        /// To Select Pariority based on Culture
        /// </summary>
        public static string GetPariority(byte ID, string Culture)
        {
            string Priority = string.Empty;

            try
            {
                switch (ID)
                {
                    case 1:
                        Priority = GetGlobalResourceObject(Culture.ToString(), "low").ToString();
                        break;
                    case 2:
                        Priority = GetGlobalResourceObject(Culture.ToString(), "medium").ToString();
                        break;
                    case 3:
                        Priority = GetGlobalResourceObject(Culture.ToString(), "high").ToString();
                        break;
                    case 4:
                        Priority = GetGlobalResourceObject(Culture.ToString(), "critical").ToString();
                        break;
                    default:
                        Priority = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {

            }
            return Priority;
        }
        #endregion

    }
}