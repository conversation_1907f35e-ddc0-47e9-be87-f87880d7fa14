using PBC.UtilityService.Utilities.DTOs;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Interface for ExtensionMethods service operations
    /// </summary>
    public interface IExtensionMethodsService
    {
        /// <summary>
        /// Orders data by a specified field and sort direction
        /// </summary>
        /// <param name="request">The order by field request</param>
        /// <returns>Ordered data response</returns>
        Task<ExtensionMethodsResponse<object[]>> OrderByFieldAsync(OrderByFieldRequest request);

        /// <summary>
        /// Filters data using simple filter rules
        /// </summary>
        /// <param name="request">The filter search request</param>
        /// <returns>Filtered data response</returns>
        Task<ExtensionMethodsResponse<object[]>> FilterSearchAsync(FilterSearchRequest request);

        /// <summary>
        /// Filters data using advanced filter rules with operators and conditions
        /// </summary>
        /// <param name="request">The advance search request</param>
        /// <returns>Filtered data response</returns>
        Task<ExtensionMethodsResponse<object[]>> AdvanceSearchAsync(AdvanceSearchRequest request);

        /// <summary>
        /// Paginates data by returning a specific page of results
        /// </summary>
        /// <param name="request">The paginate request</param>
        /// <returns>Paginated data response</returns>
        Task<PaginationResponse<object>> PaginateAsync(PaginateRequest request);

        /// <summary>
        /// Converts an object to a specified type with custom conversion logic
        /// </summary>
        /// <param name="request">The convert to type request</param>
        /// <returns>Converted object response</returns>
        Task<ExtensionMethodsResponse<object>> ConvertToTypeAsync(ConvertToTypeRequest request);

        /// <summary>
        /// Decrypts and unescapes a URL-encoded string
        /// </summary>
        /// <param name="request">The decrypt string request</param>
        /// <returns>Decrypted string response</returns>
        Task<ExtensionMethodsResponse<string>> DecryptStringAsync(DecryptStringRequest request);

        /// <summary>
        /// Performs a case-insensitive "like" comparison for strings
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if field contains value</returns>
        Task<ExtensionMethodsResponse<bool>> LikeAsync(string field, string value);

        /// <summary>
        /// Performs a "like" comparison for integers by converting to string
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if field contains value</returns>
        Task<ExtensionMethodsResponse<bool>> LikeIntegerAsync(int field, int value);

        /// <summary>
        /// Performs a "like" comparison for decimals by converting to string
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if field contains value</returns>
        Task<ExtensionMethodsResponse<bool>> LikeDecimalAsync(decimal field, decimal value);

        /// <summary>
        /// Performs a "like" comparison for DateTime values by formatting and comparing strings
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if formatted date contains value</returns>
        Task<ExtensionMethodsResponse<bool>> LikeDateAsync(DateTime field, string value);

        /// <summary>
        /// Performs equality comparison for nullable boolean values
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to compare</param>
        /// <returns>True if values are equal</returns>
        Task<ExtensionMethodsResponse<bool>> LikeNullableBoolAsync(bool? field, bool value);

        /// <summary>
        /// Performs equality comparison for boolean values
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to compare</param>
        /// <returns>True if values are equal</returns>
        Task<ExtensionMethodsResponse<bool>> LikeBoolAsync(bool field, bool value);
    }
}
