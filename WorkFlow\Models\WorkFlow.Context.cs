﻿//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class WorkFlowEntity : DbContext
    {
        public WorkFlowEntity(string Dbname)
            : base(WFCommon.GetConnection(Dbname, "WorkFlow").ConnectionString)
        {
            this.Configuration.LazyLoadingEnabled = true;
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public DbSet<WF_Sms> WF_Sms { get; set; }
        public DbSet<WF_WFAction> WF_WFAction { get; set; }
        public DbSet<WF_WFCase_Progress> WF_WFCase_Progress { get; set; }
        public DbSet<WF_WFField> WF_WFField { get; set; }
        public DbSet<WF_WFFieldValue> WF_WFFieldValue { get; set; }
        public DbSet<WF_WFRole> WF_WFRole { get; set; }
        public DbSet<WF_WFRoleUser> WF_WFRoleUser { get; set; }
        public DbSet<WF_WFSteps> WF_WFSteps { get; set; }
        public DbSet<WF_WFStepStatus> WF_WFStepStatus { get; set; }
        public DbSet<WF_WFStepType> WF_WFStepType { get; set; }
        public DbSet<WF_WorkFlow> WF_WorkFlow { get; set; }
        public DbSet<GNM_WorkFlowParent> GNM_WorkFlowParent { get; set; }
        public DbSet<WF_WFChildActions> WF_WFChildActions { get; set; }
        public DbSet<WF_WFStepLink> WF_WFStepLink { get; set; }
        public DbSet<WF_Email> WF_Email { get; set; }
        public DbSet<WF_WFActionLocale> WF_WFActionLocale { get; set; }
        public DbSet<WF_WFStepsLocale> WF_WFStepsLocale { get; set; }
        public DbSet<WF_WFStepStatusLocale> WF_WFStepStatusLocale { get; set; }
        public DbSet<WF_WFRoleLocale> WF_WFRoleLocale { get; set; }
    }
}
