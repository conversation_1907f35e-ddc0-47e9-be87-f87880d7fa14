using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Text;
using PBC.AggregatorService.DTOs;
using Microsoft.AspNetCore.Authorization;

namespace PBC.AggregatorService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CoreController : ControllerBase
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CoreController> _logger;
        private readonly IConfiguration _configuration;

        public CoreController(HttpClient httpClient, ILogger<CoreController> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
        }

        #region ::: CheckOldPassword :::
        /// <summary>
        /// Check if old password matches the stored password
        /// </summary>
        /// <param name="request">CheckOldPasswordList object</param>
        /// <returns>JsonResult with count (1 if password matches, 0 if not)</returns>
        [HttpPost("CheckOldPassword")]
        [Authorize]
        public async Task<IActionResult> CheckOldPassword([FromBody] CheckOldPasswordList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/CheckOldPassword");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new CheckOldPasswordRequestWithConfig
                {
                    User_ID = request.User_ID,
                    OldPassword = request.OldPassword,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/corechangepassword/check-old-password";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckOldPassword");
                return StatusCode(500, "An error occurred while checking old password");
            }
        }
        #endregion

        #region ::: ChangePassword :::
        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="request">ChangePasswordList object with encrypted password data</param>
        /// <returns>Void - matches original implementation</returns>
        [HttpPost("ChangePassword")]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/ChangePassword");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new ChangePasswordRequestWithConfig
                {
                    User_ID = request.User_ID,
                    data = request.data,
                    Company_ID = request.Company_ID,
                    Branch = request.Branch,
                    MenuID = request.MenuID,
                    LoggedINDateTime = request.LoggedINDateTime,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/corechangepassword/change-password";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    return Ok();
                }
                else
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ChangePassword");
                return StatusCode(500, "An error occurred while changing password");
            }
        }
        #endregion

        #region ::: CoreConsignee - LoadBranchDropdown :::
        /// <summary>
        /// Load branch dropdown data
        /// </summary>
        /// <param name="request">LoadBranchDropdownList object</param>
        /// <returns>Branch dropdown data</returns>
        [HttpPost("LoadBranchDropdown")]
        [Authorize]
        public async Task<IActionResult> LoadBranchDropdown([FromBody] LoadBranchDropdownList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/LoadBranchDropdown");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    Company_ID = request.Company_ID,
                    Branch = request.Branch,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/load-branch-dropdown";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadBranchDropdown");
                return StatusCode(500, "An error occurred while loading branch dropdown");
            }
        }
        #endregion

        #region ::: CoreConsignee - Select :::
        /// <summary>
        /// Select consignee data with pagination and filtering
        /// </summary>
        /// <param name="request">SelectConsigneeRequest object</param>
        /// <returns>Paginated consignee data</returns>
        [HttpPost("Select")]
        [Authorize]
        public async Task<IActionResult> Select([FromBody] SelectConsigneeRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/Select");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    SelectConsigneeObj = request.SelectConsigneeObj,
                    Sidx = request.Sidx,
                    Rows = request.Rows,
                    Page = request.Page,
                    Sord = request.Sord,
                    Search = request.Search,
                    Nd = request.Nd,
                    Filters = request.Filters,
                    Advnce = request.Advnce,
                    AdvanceFilter = request.AdvanceFilter,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/select";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Select");
                return StatusCode(500, "An error occurred while selecting consignee data");
            }
        }
        #endregion

        #region ::: CoreConsignee - Save :::
        /// <summary>
        /// Save consignee data
        /// </summary>
        /// <param name="request">SaveList object</param>
        /// <returns>Save result</returns>
        [HttpPost("Save")]
        [Authorize]
        public async Task<IActionResult> Save([FromBody] SaveList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/Save");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    SaveObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/save";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Save");
                return StatusCode(500, "An error occurred while saving consignee data");
            }
        }
        #endregion

        #region ::: CoreConsignee - CheckConsignee :::
        /// <summary>
        /// Check consignee existence
        /// </summary>
        /// <param name="request">CheckConsigneeList object</param>
        /// <returns>Check result</returns>
        [HttpPost("CheckConsignee")]
        [Authorize]
        public async Task<IActionResult> CheckConsignee([FromBody] CheckConsigneeList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/CheckConsignee");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    CheckConsigneeObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/check-consignee";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckConsignee");
                return StatusCode(500, "An error occurred while checking consignee");
            }
        }
        #endregion

        #region ::: CoreConsignee - CheckConsigneeAddress :::
        /// <summary>
        /// Check consignee address existence
        /// </summary>
        /// <param name="request">CheckConsigneeAddressList object</param>
        /// <returns>Check result</returns>
        [HttpPost("CheckConsigneeAddress")]
        [Authorize]
        public async Task<IActionResult> CheckConsigneeAddress([FromBody] CheckConsigneeAddressList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/CheckConsigneeAddress");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    CheckConsigneeAddressObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/check-consignee-address";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckConsigneeAddress");
                return StatusCode(500, "An error occurred while checking consignee address");
            }
        }
        #endregion

        #region ::: CoreConsignee - SelectParticularConsignee :::
        /// <summary>
        /// Select particular consignee details
        /// </summary>
        /// <param name="request">SelectParticularConsigneeList object</param>
        /// <returns>Consignee details</returns>
        [HttpPost("SelectParticularConsignee")]
        [Authorize]
        public async Task<IActionResult> SelectParticularConsignee([FromBody] SelectParticularConsigneeList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/SelectParticularConsignee");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    SelectParticularConsigneeObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/select-particular-consignee";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectParticularConsignee");
                return StatusCode(500, "An error occurred while selecting particular consignee");
            }
        }
        #endregion

        #region ::: CoreConsignee - Delete :::
        /// <summary>
        /// Delete consignee data
        /// </summary>
        /// <param name="request">DeleteList object</param>
        /// <returns>Delete result</returns>
        [HttpPost("Delete")]
        [Authorize]
        public async Task<IActionResult> Delete([FromBody] DeleteList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/Delete");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    DeleteObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/delete";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Delete");
                return StatusCode(500, "An error occurred while deleting consignee");
            }
        }
        #endregion

        #region ::: CoreConsignee - UpdateLocale :::
        /// <summary>
        /// Update consignee locale data
        /// </summary>
        /// <param name="request">UpdateLocaleList object</param>
        /// <returns>Update result</returns>
        [HttpPost("UpdateLocale")]
        [Authorize]
        public async Task<IActionResult> UpdateLocale([FromBody] UpdateLocaleList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/UpdateLocale");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    UpdateLocaleObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/update-locale";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateLocale");
                return StatusCode(500, "An error occurred while updating locale");
            }
        }
        #endregion

        #region ::: CoreConsignee - CheckConsigneeLocale :::
        /// <summary>
        /// Check consignee locale existence
        /// </summary>
        /// <param name="request">CheckConsigneeLocaleList object</param>
        /// <returns>Check result</returns>
        [HttpPost("CheckConsigneeLocale")]
        [Authorize]
        public async Task<IActionResult> CheckConsigneeLocale([FromBody] CheckConsigneeLocaleList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/CheckConsigneeLocale");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    CheckConsigneeLocaleObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/check-consignee-locale";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckConsigneeLocale");
                return StatusCode(500, "An error occurred while checking consignee locale");
            }
        }
        #endregion

        #region ::: CoreConsignee - CheckConsigneeAddressLocale :::
        /// <summary>
        /// Check consignee address locale existence
        /// </summary>
        /// <param name="request">CheckConsigneeAddressLocaleList object</param>
        /// <returns>Check result</returns>
        [HttpPost("CheckConsigneeAddressLocale")]
        public async Task<IActionResult> CheckConsigneeAddressLocale([FromBody] CheckConsigneeAddressLocaleList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/CheckConsigneeAddressLocale");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    CheckConsigneeAddressLocaleObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/check-consignee-address-locale";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckConsigneeAddressLocale");
                return StatusCode(500, "An error occurred while checking consignee address locale");
            }
        }
        #endregion

        #region ::: CoreConsignee - CheckWareHouse :::
        /// <summary>
        /// Check warehouse existence
        /// </summary>
        /// <param name="request">CheckWareHouseList object</param>
        /// <returns>Check result</returns>
        [HttpPost("CheckWareHouse")]
        [Authorize]
        public async Task<IActionResult> CheckWareHouse([FromBody] CheckWareHouseList request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/CheckWareHouse");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    CheckWareHouseObj = request,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/check-warehouse";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckWareHouse");
                return StatusCode(500, "An error occurred while checking warehouse");
            }
        }
        #endregion

        #region ::: CoreConsignee - Export :::
        /// <summary>
        /// Export consignee data
        /// </summary>
        /// <param name="request">ExportConsigneeRequest object</param>
        /// <returns>Export result</returns>
        [HttpPost("Export")]
        [Authorize]
        public async Task<IActionResult> Export([FromBody] ExportConsigneeRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Core/Export");

                // Get connection string and log exception setting from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Create request with configuration
                var requestWithConfig = new
                {
                    ExportObj = request.ExportObj,
                    Filter = request.Filter,
                    AdvanceFilter = request.AdvanceFilter,
                    Sidx = request.Sidx,
                    Sord = request.Sord,
                    ConnString = connString,
                    LogException = logException
                };

                // Call PBC.CoreService
                var coreServiceUrl = _configuration["ServiceUrls:CoreService"];
                var endpoint = $"{coreServiceUrl}/api/coreconsignee/export";

                var jsonContent = JsonConvert.SerializeObject(requestWithConfig);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    return Ok(result);
                }
                else
                {
                    _logger.LogError($"Error calling CoreService: {response.StatusCode}, {responseContent}");
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Export");
                return StatusCode(500, "An error occurred while exporting consignee data");
            }
        }
        #endregion
    }
}
