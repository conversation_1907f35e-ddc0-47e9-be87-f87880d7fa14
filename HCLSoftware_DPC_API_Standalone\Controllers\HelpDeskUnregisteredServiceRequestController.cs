﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskUnregisteredServiceRequestController : ApiController
    {
        #region GetPermissions vinay n 25/11/24
        /// <summary>
        /// GetPermissions
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/GetPermissions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPermissions([FromBody] GetPermissionsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.GetPermissions(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectUnRegisteredSeviceRequest vinay n 25/11/24  
        /// <summary>
        /// SelectUnRegisteredSeviceRequest
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>   
        [Route("api/HelpDeskUnregisteredServiceRequest/SelectUnRegisteredSeviceRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUnRegisteredSeviceRequest(SelectUnRegisteredSeviceRequestList Obj)
        {
            var Response = default(dynamic);
            var rawBody = Request.Content.ReadAsStringAsync().Result;
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["FromDate"] != null && HttpContext.Current.Request.Params["FromDate"] != "")
            {
                Obj.FromDate = HttpContext.Current.Request.Params["FromDate"];
            }
            if (HttpContext.Current.Request.Params["ToDate"] != null && HttpContext.Current.Request.Params["FromDate"] != "")
            {
                Obj.ToDate = HttpContext.Current.Request.Params["ToDate"];
            }

            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskUnregisteredServiceRequestServices.SelectUnRegisteredSeviceRequest(Obj, connstring, LogException, sidx, sord, page, rows, _search, advnce, filters, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

        #region GetPartyDetailsByProductUniqueNo vinay n 25/11/24
        /// <summary>
        /// GetPartyDetailsByProductUniqueNo
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/GetPartyDetailsByProductUniqueNo")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetailsByProductUniqueNo([FromBody] GetPartyDetailsByProductUniqueNoList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.GetPartyDetailsByProductUniqueNo(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region getCustomerDetailsByPhone vinay n 25/11/24
        /// <summary>
        /// getCustomerDetailsByPhone
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/getCustomerDetailsByPhone")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getCustomerDetailsByPhone([FromBody] getCustomerDetailsByPhoneList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.getCustomerDetailsByPhone(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region getCustomerDetailsByEmail vinay n 25/11/24
        /// <summary>
        /// getCustomerDetailsByEmail
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/getCustomerDetailsByEmail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getCustomerDetailsByEmail([FromBody] getCustomerDetailsByEmailList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.getCustomerDetailsByEmail(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region getCustomerDetailsByName vinay n 25/11/24
        /// <summary>
        /// getCustomerDetailsByName
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/getCustomerDetailsByName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getCustomerDetailsByName([FromBody] getCustomerDetailsByNameList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.getCustomerDetailsByName(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region InsertUnregisteredServiceRequest vinay n 25/11/24 //not used
        /// <summary>
        /// InsertUnregisteredServiceRequest
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/InsertUnregisteredServiceRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertUnregisteredServiceRequest([FromBody] InsertUnregisteredServiceRequestList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.InsertUnregisteredServiceRequest(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectSingleUnRegisteredSeviceRequest vinay n 25/11/24
        /// <summary>
        /// SelectSingleUnRegisteredSeviceRequest
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/SelectSingleUnRegisteredSeviceRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSingleUnRegisteredSeviceRequest([FromBody] SelectSingleUnRegisteredSeviceRequestList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.SelectSingleUnRegisteredSeviceRequest(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetObjectID vinay n 25/11/24
        /// <summary>
        /// GetObjectID
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/GetObjectID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetObjectID([FromBody] GetObjectIDList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.GetObjectID(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response);
        }
        #endregion
        #region getBrandProductType vinay n 25/11/24
        /// <summary>
        /// GetObjectID
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/getBrandProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getBrandProductType([FromBody] getBrandProductTypeList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.getBrandProductType(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region UpdateUnregisteredServiceRequest vinay n 25/11/24
        /// <summary>
        /// UpdateUnregisteredServiceRequest
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/UpdateUnregisteredServiceRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateUnregisteredServiceRequest([FromBody] UpdateUnregisteredServiceRequestList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.UpdateUnregisteredServiceRequest(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region UnRegisteredServiceExport vinay n 25/11/24
        /// <summary>
        /// UnRegisteredServiceExport
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/UnRegisteredServiceExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> UnRegisteredServiceExport([FromBody] SelectUnRegisteredSeviceRequestList Obj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = Obj.sidx;
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = Obj.sord;
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = Obj.advanceFilter;
            if (HttpContext.Current.Request.Params["FromDate"] != null && HttpContext.Current.Request.Params["FromDate"] != "")
            {
                Obj.FromDate = HttpContext.Current.Request.Params["FromDate"];
            }
            if (HttpContext.Current.Request.Params["ToDate"] != null && HttpContext.Current.Request.Params["FromDate"] != "")
            {
                Obj.ToDate = HttpContext.Current.Request.Params["ToDate"];
            }

            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = await HelpDeskUnregisteredServiceRequestServices.UnRegisteredServiceExport(Obj, connstring, LogException, sidx, sord, page, rows, _search, advnce, filters, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response);
        }
        #endregion

        #region ProductMasterSave vinay n 25/11/24
        /// <summary>
        /// ProductMasterSave
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/ProductMasterSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ProductMasterSave([FromBody] ProductMasterSaveObj Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.ProductMasterSave(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ProductMasterSaveConfirm vinay n 25/11/24
        /// <summary>
        /// ProductMasterSaveConfirm
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/ProductMasterSaveConfirm")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ProductMasterSaveConfirm([FromBody] ProductMasterSaveConfirmList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskUnregisteredServiceRequestServices.ProductMasterSaveConfirm(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectPartyDetailGrid vinay n 25/11/24
        /// <summary>
        /// SelectPartyDetailGrid
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/SelectPartyDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyDetailGrid([FromBody] SelectPartyDetailGridList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

                string sidx = HttpContext.Current.Request.Params["sidx"];
                int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
                int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
                string sord = HttpContext.Current.Request.Params["sord"];
                bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
                long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
                string filters = "";
                bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

                string advnceFilters = HttpContext.Current.Request.Params["Query"];


                if (HttpContext.Current.Request.Params["filters"] == null)
                {
                    filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
                }
                else
                {
                    filters = HttpContext.Current.Request.Params["filters"];
                }
                Response = HelpDeskUnregisteredServiceRequestServices.SelectPartyDetailGrid(Obj, connstring, LogException, sidx, sord, page, rows, _search, advnce, filters, advnceFilters);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetPartyDetailsbyID vinay n 25/11/24
        /// <summary>
        /// GetPartyDetailsbyID
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/GetPartyDetailsbyID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetailsbyID([FromBody] GetPartyDetailsbyIDList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





                Response = HelpDeskUnregisteredServiceRequestServices.GetPartyDetailsbyID(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetProductDetails vinay n 25/11/24
        /// <summary>
        /// GetProductDetails
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/GetProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductDetails([FromBody] GetProductDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





                Response = HelpDeskUnregisteredServiceRequestServices.GetProductDetails(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region getSerialNumberForModel vinay n 25/11/24
        /// <summary>
        /// getSerialNumberForModel
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/getSerialNumberForModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getSerialNumberForModel([FromBody] getSerialNumberForModelList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





                Response = HelpDeskUnregisteredServiceRequestServices.getSerialNumberForModel(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region getProductUniqueNumber vinay n 25/11/24
        /// <summary>
        /// getProductUniqueNumber
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskUnregisteredServiceRequest/getProductUniqueNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getProductUniqueNumber([FromBody] getProductUniqueNumberList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





                Response = HelpDeskUnregisteredServiceRequestServices.getProductUniqueNumber(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion





    }
}