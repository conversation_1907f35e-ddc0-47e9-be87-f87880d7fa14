// using Microsoft.AspNetCore.Mvc;
// using Newtonsoft.Json;
// using Newtonsoft.Json.Linq;
// using System;
// using System.Collections.Generic;
// using System.Configuration;
// using System.Data;
// using System.Data.SqlClient;
// using System.Linq;
// using System.Text;
// using WorkFlow.Models;
// using LS = SharedAPIClassLibrary_AMERP.Utilities;
// namespace SharedAPIClassLibrary_AMERP
// {
//     public class CoreEmailTemplateService
//     {


//         #region ::: SelectEmailTemplateLand Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Get Email Template Detail for Landing Grid  
//         /// </summary>  

//         public async IActionResult SelectEmailTemplateLand(string connString, SelectEmailTemplateLandList SelectEmailTemplateLandobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
//         {
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             var jsonData = default(dynamic);
//             try
//             {
//                 int Count = 0;
//                 int Total = 0;
//                 List<EmailTemplate> emailTemplates = new List<EmailTemplate>();

//                 // Define the connection string and command for the stored procedure
//                 using (SqlConnection conn = new SqlConnection(connString))
//                 {
//                     conn.Open();

//                     using (SqlCommand cmd = new SqlCommand("usp_GetEmailTemplates", conn))
//                     {
//                         cmd.CommandType = CommandType.StoredProcedure;

//                         // Use SqlDataReader to read the results directly
//                         using (SqlDataReader reader = cmd.ExecuteReader())
//                         {
//                             while (reader.Read())
//                             {
//                                 emailTemplates.Add(new EmailTemplate
//                                 {
//                                     TEMPLATEID = (int)reader["TEMPLATEID"],
//                                     TEMPLATECODE = reader["TEMPLATECODE"].ToString(),
//                                     TEMPLATENAME = reader["TEMPLATENAME"].ToString(),
//                                     P1 = reader["P1"].ToString(),
//                                     P2 = reader["P2"].ToString(),
//                                     P3 = reader["P3"].ToString(),
//                                     P4 = reader["P4"].ToString(),
//                                     P5 = reader["P5"].ToString(),
//                                     P6 = reader["P6"].ToString(),
//                                     P7 = reader["P7"].ToString(),
//                                     P8 = reader["P8"].ToString(),
//                                     P9 = reader["P9"].ToString(),
//                                     P10 = reader["P10"].ToString(),
//                                     P11 = reader["P11"].ToString(),
//                                     P12 = reader["P12"].ToString(),
//                                     P13 = reader["P13"].ToString(),
//                                     P14 = reader["P14"].ToString(),
//                                     P15 = reader["P15"].ToString(),
//                                     P16 = reader["P16"].ToString(),
//                                     P17 = reader["P17"].ToString(),
//                                     P18 = reader["P18"].ToString(),
//                                     P19 = reader["P19"].ToString(),
//                                     P20 = reader["P20"].ToString()
//                                 });
//                             }
//                         }
//                     }
//                 }

//                 // Convert the list to a queryable collection
//                 var IEEmailTemplate = emailTemplates.AsQueryable();

//                 // Filter and sorting logic
//                 if (_search && !string.IsNullOrEmpty(filters))
//                 {
//                     Filters filtersObj = JObject.Parse(await _dDecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
//                     IEEmailTemplate = IEEmailTemplate.FilterSearch(filtersObj);
//                 }
//                 else if (advnce && !string.IsNullOrEmpty(advnceFilters))
//                 {
//                     AdvanceFilter advnFilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
//                     IEEmailTemplate = IEEmailTemplate.AdvanceSearch(advnFilter);
//                     page = 1; // Reset to first page on advanced search
//                 }

//                 // Ordering logic
//                 IEEmailTemplate = IEEmailTemplate.OrderByField(sidx, sord);

//                 // Pagination logic
//                 Count = IEEmailTemplate.Count();
//                 Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
//                 string Title = CommonFunctionalities.GetGlobalResourceObject(SelectEmailTemplateLandobj.UserCulture.ToString(), "view").ToString();

//                 // Prepare the JSON data for the response
//                 jsonData = new
//                 {
//                     total = Total,
//                     page = page,
//                     records = Count,
//                     rows = IEEmailTemplate.Select(et => new
//                     {
//                         edit = "<a title='" + Title + "' href='#' style='font-size: 13px;' id='" + et.TEMPLATEID + "' key='" + et.TEMPLATEID + "' class='editEmailTemplate' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
//                         delete = "<input type='checkbox' key='" + et.TEMPLATEID + "' defaultchecked='' id='chk" + et.TEMPLATEID + "' class='chkEmailTemplateDelete'/>",
//                         et.TEMPLATECODE,
//                         et.TEMPLATENAME,
//                         et.P1,
//                         et.P2,
//                         et.P3,
//                         et.P4,
//                         et.P5,
//                         et.P6,
//                         et.P7,
//                         et.P8,
//                         et.P9,
//                         et.P10,
//                         et.P11,
//                         et.P12,
//                         et.P13,
//                         et.P14,
//                         et.P15,
//                         et.P16,
//                         et.P17,
//                         et.P18,
//                         et.P19,
//                         et.P20
//                     }).ToList(),
//                     Lbl_Refresh = CommonFunctionalities.GetGlobalResourceObject(SelectEmailTemplateLandobj.UserCulture.ToString(), "refresh").ToString(),
//                     Lbl_AdvanceSearch = CommonFunctionalities.GetGlobalResourceObject(SelectEmailTemplateLandobj.UserCulture.ToString(), "advancesearch").ToString()
//                 };
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     // Log the exception
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}: {ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//             }

//             return new JsonResult(jsonData);
//         }

//         #endregion


//         #region ::: SelectEmailTemplateDetailer Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Get Email Template Detail for Landing Grid 
//         /// </summary>  
//         ///
//         public static IActionResult SelectEmailTemplateDetailer(string connString, SelectEmailTemplateDetailerList SelectEmailTemplateDetailerobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filterParams, bool advnce, string advnceFilters)
//         {
//             var jsonData = default(dynamic);
//             string AppPath = string.Empty;
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             try
//             {
//                 int userLanguageID = Convert.ToInt32(SelectEmailTemplateDetailerobj.UserLanguageID);
//                 int generalLanguageID = Convert.ToInt32(SelectEmailTemplateDetailerobj.GeneralLanguageID);

//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectEmailTemplateDetailer", connection))
//                     {
//                         command.CommandType = CommandType.StoredProcedure;
//                         command.Parameters.AddWithValue("@TemplateID", SelectEmailTemplateDetailerobj.TemplatID);
//                         command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
//                         command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
//                         command.Parameters.AddWithValue("@Page", page);
//                         command.Parameters.AddWithValue("@RowsPerPage", rows);

//                         using (var reader = command.ExecuteReader())
//                         {
//                             if (reader.HasRows)
//                             {
//                                 List<EmailTemplateDetailerClass> rowsList = new List<EmailTemplateDetailerClass>();

//                                 while (reader.Read())
//                                 {
//                                     var row = new EmailTemplateDetailerClass
//                                     {
//                                         TEMPLATEDETAILID = Convert.ToInt32(reader["TEMPLATEDETAILID"]),
//                                         COMPANYNAME = reader["COMPANYNAME"].ToString(),
//                                         SUBJECT = reader["SUBJECT"].ToString(),
//                                         BODY = reader["BODY"].ToString(),
//                                         SMS = reader["SMS"].ToString(),
//                                         CC = reader["CC"].ToString(),
//                                         BCC = reader["BCC"].ToString(),
//                                         edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectEmailTemplateDetailerobj.UserCulture.ToString(), "edit").ToString() + " href='#' style='font-size: 13px;'  id='" + reader["TEMPLATEDETAILID"] + "' key='" + reader["TEMPLATEDETAILID"] + "' class='editEmailTemplateDetailer' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
//                                         delete = "<input type='checkbox' key='" + reader["TEMPLATEDETAILID"] + "' defaultchecked=''  id='chk" + reader["TEMPLATEDETAILID"] + "' class='chkEmailTemplateDetailerDelete'/>",
//                                         Locale = (userLanguageID != generalLanguageID) ? "<a key='" + reader["TEMPLATEDETAILID"] + "' src='" + AppPath + "/Content/Images/local.png' class='editEmailTemplateDetailerLocalePopUp' editmode='false' alt='Localize' width='15' height='15'  title='Localize'><i class='fa fa-globe'></i></a>" : ""
//                                     };

//                                     rowsList.Add(row);
//                                 }

//                                 IQueryable<EmailTemplateDetailerClass> IQEmailTemplateDetail = rowsList.AsQueryable();

//                                 if (_search)
//                                 {
//                                     Filters filter = JObject.Parse(Common.DecryptString(filterParams)).ToObject<Filters>();
//                                     IQEmailTemplateDetail = IQEmailTemplateDetail.FilterSearch<EmailTemplateDetailerClass>(filter);
//                                 }

//                                 int rowCount = rowsList.Count;

//                                 jsonData = new
//                                 {
//                                     total = rowCount,
//                                     page = page,
//                                     records = rowCount,
//                                     rows = IQEmailTemplateDetail,
//                                     Lbl_Refresh = CommonFunctionalities.GetGlobalResourceObject(SelectEmailTemplateDetailerobj.UserCulture.ToString(), "refresh").ToString()
//                                 };
//                             }
//                         }
//                         if (jsonData == null)
//                         {
//                             jsonData = new { error = "No Records to Display" };
//                         }
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }

//                 jsonData = new { error = "An error occurred while fetching data." };
//             }

//             return new JsonResult(jsonData);
//         }
//         #endregion


//         #region ::: SaveEmailTemplateHeader Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         ///  Save record of Email Template Header  
//         /// </summary>  
//         ///

//         public static IActionResult SaveEmailTemplateHeader(string connString, SaveEmailTemplateHeaderList SaveEmailTemplateHeaderobj)
//         {

//             try
//             {
//                 var reqData = SaveEmailTemplateHeaderobj.EmailTempData;
//                 var etList = JsonConvert.DeserializeObject<List<GNM_EMAILTEMPLATES>>(reqData);

//                 using (SqlConnection conn = new SqlConnection(connString))
//                 {
//                     conn.Open();

//                     foreach (var item in etList)
//                     {
//                         try
//                         {
//                             using (SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveEmailTemplateHeader", conn))
//                             {
//                                 cmd.CommandType = CommandType.StoredProcedure;

//                                 cmd.Parameters.AddWithValue("@TEMPLATEID", item.TEMPLATEID);
//                                 cmd.Parameters.AddWithValue("@TEMPLATECODE", item.TEMPLATECODE ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@TEMPLATENAME", item.TEMPLATENAME ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P1", item.P1 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P2", item.P2 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P3", item.P3 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P4", item.P4 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P5", item.P5 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P6", item.P6 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P7", item.P7 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P8", item.P8 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P9", item.P9 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P10", item.P10 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P11", item.P11 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P12", item.P12 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P13", item.P13 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P14", item.P14 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P15", item.P15 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@REMARKS", item.REMARKS ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@ISACTIVE", item.ISACTIVE);
//                                 cmd.Parameters.AddWithValue("@P16", item.P16 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P17", item.P17 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P18", item.P18 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P19", item.P19 ?? (object)DBNull.Value);
//                                 cmd.Parameters.AddWithValue("@P20", item.P20 ?? (object)DBNull.Value);

//                                 cmd.ExecuteNonQuery();

//                                 // Insert GPS Details
//                                 //int companyId = Convert.ToInt32(SaveEmailTemplateHeaderobj.Company_ID);
//                                 //int branchId = Convert.ToInt32(SaveEmailTemplateHeaderobj.Branch);
//                                 //int userId = Convert.ToInt32(SaveEmailTemplateHeaderobj.User_ID);
//                                 //int menuId = Convert.ToInt32(SaveEmailTemplateHeaderobj.MenuID);
//                                 //DateTime loggedInDateTime = Convert.ToDateTime(SaveEmailTemplateHeaderobj.LoggedINDateTime);
//                                 //int objectID = Convert.ToInt32(Common.GetObjectID("CoreEmailTemplate"));
//                                 //string action = item.TEMPLATEID <= 0 ? "Inserted" : "Updated";

//                                 //gbl.InsertGPSDetails(companyId, branchId, userId, objectID, item.TEMPLATEID, 0, 0, action, false, menuId, loggedInDateTime);
//                             }
//                         }
//                         catch (SqlException ex)
//                         {
//                             // Check for UNIQUE KEY violation
//                             if (ex.Number == 2627 || ex.Number == 2601) // Error numbers for UNIQUE KEY constraint violation
//                             {
//                                 // Handle the duplicate key error
//                                 return new JsonResult("Duplicate key error: The TEMPLATECODE '" + item.TEMPLATECODE + "' already exists.");
//                             }

//                         }
//                     }
//                 }

//                 return new JsonResult("1");
//             }
//             catch (Exception ex)
//             {
//                 return new JsonResult("0");
//             }
//         }
//         #endregion


//         #region ::: SaveEmailTemplateDetailer Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Save record of Email Template Detailer 
//         /// </summary>  
//         ///
//         public static IActionResult SaveEmailTemplateDetailer(string connString, SaveEmailTemplateDetailerList SaveEmailTemplateDetailerobj)
//         {

//             try
//             {
//                 // Decrypt the input string
//                 string etDet = Common.DecryptString(SaveEmailTemplateDetailerobj.etDet);

//                 // Decode URL-encoded string
//                 string decodedEtDet = Uri.UnescapeDataString(etDet);

//                 // Split the decoded string into array of details
//                 string[] strArr = decodedEtDet.Split('$');

//                 // Establish the SQL connection
//                 using (SqlConnection conn = new SqlConnection(connString))
//                 {
//                     conn.Open();

//                     foreach (string strItem in strArr)
//                     {
//                         string[] str = strItem.Split('*');
//                         if (str[0] != "")
//                         {
//                             int templateDetailID = Convert.ToInt32(str[0]);
//                             int templateID = Convert.ToInt32(str[1]);

//                             if (templateID <= 0)
//                             {
//                                 // Retrieve the latest template ID if it's not provided
//                                 using (SqlCommand cmd = new SqlCommand("GetLatestTemplateID", conn))
//                                 {
//                                     object result = cmd.ExecuteScalar();
//                                     templateID = result != null ? Convert.ToInt32(result) : 0;
//                                 }
//                             }

//                             // Use stored procedure to save or update the template detail
//                             using (SqlCommand cmd = new SqlCommand("Up_Save_Am_Erp_SaveEmailTemplateDetail", conn))
//                             {
//                                 cmd.CommandType = CommandType.StoredProcedure;
//                                 cmd.Parameters.AddWithValue("@TEMPLATEDETAILID", templateDetailID);
//                                 cmd.Parameters.AddWithValue("@TEMPLATEID", templateID);
//                                 cmd.Parameters.AddWithValue("@COMPANYID", Convert.ToInt32(str[2]));
//                                 cmd.Parameters.AddWithValue("@SUBJECT", str[3]);
//                                 cmd.Parameters.AddWithValue("@BODY", str[4]);
//                                 cmd.Parameters.AddWithValue("@SMS", str[5]);
//                                 cmd.Parameters.AddWithValue("@CC", str[6]);
//                                 cmd.Parameters.AddWithValue("@BCC", str[7]);
//                                 string action = templateDetailID <= 0 ? "INSERT" : "UPDATE";
//                                 cmd.Parameters.AddWithValue("@ACTION", action);
//                                 cmd.ExecuteNonQuery();

//                                 // Log GPS details
//                                 //int companyID = Convert.ToInt32(SaveEmailTemplateDetailerobj.Company_ID);
//                                 //int branchID = Convert.ToInt32(SaveEmailTemplateDetailerobj.Branch);
//                                 //int userID = Convert.ToInt32(SaveEmailTemplateDetailerobj.User_ID);
//                                 //int menuID = Convert.ToInt32(SaveEmailTemplateDetailerobj.MenuID);
//                                 //DateTime loggedInDateTime = Convert.ToDateTime(SaveEmailTemplateDetailerobj.LoggedINDateTime);
//                                 //int objectID = Convert.ToInt32(Common.GetObjectID("CoreEmailTemplate"));

//                                 //gbl.InsertGPSDetails(companyID, branchID, userID, objectID, templateID, 0, 0, "Update", false, menuID, loggedInDateTime);
//                             }
//                         }
//                     }
//                 }
//                 return new JsonResult("1");
//             }
//             catch (Exception ex)
//             {
//                 return new JsonResult("0");
//             }
//         }
//         #endregion


//         #region ::: deleteEmailTemplate Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Delete record of Email Template 
//         /// </summary>  
//         ///

//         public static IActionResult deleteEmailTemplate(string connString, deleteEmailTemplateList deleteEmailTemplateobj)
//         {
//             int EmailTemplateID = 0;
//             try
//             {
//                 string[] arrNotIC = deleteEmailTemplateobj.TemplateID.Split(',');

//                 using (SqlConnection conn = new SqlConnection(connString))
//                 {
//                     conn.Open();

//                     foreach (string idStr in arrNotIC)
//                     {
//                         if (!string.IsNullOrEmpty(idStr))
//                         {
//                             EmailTemplateID = Convert.ToInt32(idStr);

//                             using (SqlCommand cmd = new SqlCommand("DeleteEmailTemplate", conn))
//                             {
//                                 cmd.CommandType = CommandType.StoredProcedure;
//                                 cmd.Parameters.AddWithValue("@TemplateID", EmailTemplateID);
//                                 cmd.ExecuteNonQuery();
//                             }

//                             // Log GPS details
//                             //int companyID = Convert.ToInt32(deleteEmailTemplateobj.Company_ID);
//                             //int branchID = Convert.ToInt32(deleteEmailTemplateobj.Branch);
//                             //int userID = Convert.ToInt32(deleteEmailTemplateobj.User_ID);
//                             //int menuID = Convert.ToInt32(deleteEmailTemplateobj.MenuID);
//                             //DateTime loggedInDateTime = Convert.ToDateTime(deleteEmailTemplateobj.LoggedINDateTime);
//                             //int objectID = Convert.ToInt32(Common.GetObjectID("CoreEmailTemplate"));

//                             //gbl.InsertGPSDetails(companyID, branchID, userID, objectID, EmailTemplateID, 0, 0, "Delete", false, menuID, loggedInDateTime);
//                         }
//                     }
//                 }

//                 return new JsonResult("1");
//             }
//             catch (Exception ex)
//             {
//                 return new JsonResult("0");
//             }
//         }
//         #endregion


//         #region ::: deleteEmailTemplateDetail Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Delete record of Email Template Detail 
//         /// </summary>  
//         ///
//         public static IActionResult deleteEmailTemplateDetail(string connString, deleteEmailTemplateDetailList deleteEmailTemplateDetailobj)
//         {
//             string Msg = string.Empty;
//             JObject jObj = null;
//             JTokenReader jTR = null;
//             try
//             {
//                 jObj = JObject.Parse(deleteEmailTemplateDetailobj.key);
//                 int Count = jObj["rows"].Count();
//                 int ID = 0;

//                 using (SqlConnection conn = new SqlConnection(connString))
//                 {
//                     conn.Open();
//                     for (int i = 0; i < Count; i++)
//                     {
//                         jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
//                         jTR.Read();
//                         ID = Convert.ToInt32(jTR.Value);

//                         using (SqlCommand cmd = new SqlCommand("DeleteEmailTemplateDetail", conn))
//                         {
//                             cmd.CommandType = CommandType.StoredProcedure;
//                             cmd.Parameters.AddWithValue("@TemplateDetailID", ID);
//                             cmd.ExecuteNonQuery();
//                         }
//                     }
//                 }

//                 //int companyID = Convert.ToInt32(deleteEmailTemplateDetailobj.Company_ID);
//                 //int branchID = Convert.ToInt32(deleteEmailTemplateDetailobj.Branch);
//                 //int userID = Convert.ToInt32(deleteEmailTemplateDetailobj.User_ID);
//                 //int menuID = Convert.ToInt32(deleteEmailTemplateDetailobj.MenuID);
//                 //DateTime loggedInDateTime = Convert.ToDateTime(deleteEmailTemplateDetailobj.LoggedINDateTime);
//                 //int objectID = Convert.ToInt32(Common.GetObjectID("CoreEmailTemplate"));

//                 //gbl.InsertGPSDetails(companyID, branchID, userID, objectID, ID, 0, 0, "Deleted", false, menuID, loggedInDateTime);
//                 Msg += CommonFunctionalities.GetResourceString(deleteEmailTemplateDetailobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
//             }
//             catch (SqlException ex)
//             {
//                 if (ex.Number == 547)
//                 {
//                     Msg += CommonFunctionalities.GetResourceString(deleteEmailTemplateDetailobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
//                 }
//             }
//             catch (Exception ex)
//             {
//                 Msg += CommonFunctionalities.GetResourceString(deleteEmailTemplateDetailobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
//             }
//             return new JsonResult(Msg);
//         }
//         #endregion


//         #region ::: CheckDuplicateEmailTemplate Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Check Duplicate value of EmailTemplate 
//         /// </summary>  
//         ///

//         public static IActionResult CheckDuplicateEmailTemplate(string connString, CheckDuplicateEmailTemplateList CheckDuplicateEmailTemplateobj)
//         {
//             string result = "0$";
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             try
//             {
//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckDuplicateEmailTemplate", connection);
//                     command.CommandType = CommandType.StoredProcedure;

//                     command.Parameters.AddWithValue("@TemplateID", CheckDuplicateEmailTemplateobj.templateID);
//                     command.Parameters.AddWithValue("@Code", CheckDuplicateEmailTemplateobj.code);
//                     command.Parameters.AddWithValue("@Name", CheckDuplicateEmailTemplateobj.name);

//                     SqlParameter resultParameter = new SqlParameter("@Result", SqlDbType.NVarChar, -1);
//                     resultParameter.Direction = ParameterDirection.Output;
//                     command.Parameters.Add(resultParameter);

//                     command.ExecuteNonQuery();

//                     result = resultParameter.Value.ToString();
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//             }

//             return new JsonResult(result);
//         }
//         #endregion


//         #region ::: CheckDuplicateCompanyName Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Check Duplicate value of Company Name 
//         /// </summary>  
//         ///
//         public static IActionResult CheckDuplicateCompanyName(string connString, CheckDuplicateCompanyNameList CheckDuplicateCompanyNameobj)
//         {
//             int tempID = Convert.ToInt32(CheckDuplicateCompanyNameobj.TemplateID);
//             int compID = Convert.ToInt32(CheckDuplicateCompanyNameobj.CompanyID);
//             int tempDetID = 0;
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             if (CheckDuplicateCompanyNameobj.TEMPLATEDETAILIDValue != "false")
//                 tempDetID = Convert.ToInt32(CheckDuplicateCompanyNameobj.TEMPLATEDETAILIDValue);

//             string result = "0";

//             try
//             {
//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     using (var command = new SqlCommand("Up_Chk_Am_Erp_CheckDuplicateCompanyName", connection))
//                     {
//                         command.CommandType = CommandType.StoredProcedure;

//                         command.Parameters.AddWithValue("@TemplateID", tempID);
//                         command.Parameters.AddWithValue("@CompanyID", compID);
//                         command.Parameters.AddWithValue("@TemplateDetailID", tempDetID);

//                         SqlParameter isDuplicateParameter = new SqlParameter("@IsDuplicate", SqlDbType.Bit);
//                         isDuplicateParameter.Direction = ParameterDirection.Output;
//                         command.Parameters.Add(isDuplicateParameter);

//                         command.ExecuteNonQuery();

//                         bool isDuplicate = (bool)isDuplicateParameter.Value;
//                         result = isDuplicate ? "1" : "0";
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//             }

//             return new JsonResult(result);
//         }
//         #endregion


//         #region ::: SelectParticularEmailTemplate Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Select Particular Email Template 
//         /// </summary>  
//         ///
//         public static IActionResult SelectParticularEmailTemplate(string connString, SelectParticularEmailTemplateList SelectParticularEmailTemplateobj)
//         {
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             dynamic jsonData = null;
//             try
//             {
//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectParticularEmailTemplate", connection))
//                     {
//                         command.CommandType = CommandType.StoredProcedure;
//                         command.Parameters.AddWithValue("@TemplateID", SelectParticularEmailTemplateobj.templateID);

//                         using (var reader = command.ExecuteReader())
//                         {
//                             if (reader.Read())
//                             {
//                                 jsonData = new
//                                 {
//                                     TEMPLATEID = reader["TEMPLATEID"],
//                                     TEMPLATECODE = reader["TEMPLATECODE"],
//                                     TEMPLATENAME = reader["TEMPLATENAME"],
//                                     P1 = reader["P1"],
//                                     P2 = reader["P2"],
//                                     P3 = reader["P3"],
//                                     P4 = reader["P4"],
//                                     P5 = reader["P5"],
//                                     P6 = reader["P6"],
//                                     P7 = reader["P7"],
//                                     P8 = reader["P8"],
//                                     P9 = reader["P9"],
//                                     P10 = reader["P10"],
//                                     P11 = reader["P11"],
//                                     P12 = reader["P12"],
//                                     P13 = reader["P13"],
//                                     P14 = reader["P14"],
//                                     P15 = reader["P15"],
//                                     P16 = reader["P16"],
//                                     P17 = reader["P17"],
//                                     P18 = reader["P18"],
//                                     P19 = reader["P19"],
//                                     P20 = reader["P20"],
//                                 };
//                             }
//                         }
//                     }
//                 }

//                 // GNM_User user = SelectParticularEmailTemplateobj.UserDetails.FirstOrDefault();
//                 //gbl.InsertGPSDetails(
//                 //    Convert.ToInt32(SelectParticularEmailTemplateobj.Company_ID),
//                 //    Convert.ToInt32(SelectParticularEmailTemplateobj.Branch),
//                 //    SelectParticularEmailTemplateobj.User_ID,
//                 //    Common.GetObjectID("CoreEmailTemplate"),
//                 //    SelectParticularEmailTemplateobj.templateID,
//                 //    0,
//                 //    0,
//                 //    "Viewed",
//                 //    false,
//                 //    Convert.ToInt32(SelectParticularEmailTemplateobj.MenuID),
//                 //    Convert.ToDateTime(SelectParticularEmailTemplateobj.LoggedINDateTime)
//                 //);
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//             }

//             return new JsonResult(jsonData);
//         }
//         #endregion


//         #region ::: SelectCompanyNames Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Select Company Names 
//         /// </summary>  
//         ///
//         public static IActionResult SelectCompanyNames(string connString)
//         {
//             dynamic jsonData = null;
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             try
//             {
//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectActiveCompanyNames", connection))
//                     {
//                         command.CommandType = CommandType.StoredProcedure;

//                         using (var reader = command.ExecuteReader())
//                         {
//                             var companies = new List<object>();

//                             while (reader.Read())
//                             {
//                                 companies.Add(new
//                                 {
//                                     Company_ID = reader["Company_ID"],
//                                     Company_Name = reader["Company_Name"]
//                                 });
//                             }

//                             jsonData = companies;
//                         }
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//             }

//             return new JsonResult(jsonData);
//         }
//         #endregion


//         #region ::: selectparticularEmailTemplateDetailLocale Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// select particular Email Template Detail Locale
//         /// </summary>  
//         ///

//         public static IActionResult selectparticularEmailTemplateDetailLocale(string connString, selectparticularEmailTemplateDetailLocaleList selectparticularEmailTemplateDetailLocaleobj)
//         {
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             dynamic JsonData = null;
//             try
//             {
//                 List<EmailTemplateDetailerLocaleClass> listEtDet = new List<EmailTemplateDetailerLocaleClass>();

//                 using (SqlConnection conn = new SqlConnection(connString))
//                 {
//                     // Get Email Template Detail
//                     EmailTemplateDetailerLocaleClass etDetEnglish = null;
//                     using (SqlCommand cmd = new SqlCommand("sp_GetEmailTemplateDetailByID", conn))
//                     {
//                         cmd.CommandType = CommandType.StoredProcedure;
//                         cmd.Parameters.AddWithValue("@EmailTemplateDetailID", selectparticularEmailTemplateDetailLocaleobj.EmailTemplateDetailID);

//                         conn.Open();
//                         using (SqlDataReader reader = cmd.ExecuteReader())
//                         {
//                             if (reader.Read())
//                             {
//                                 etDetEnglish = new EmailTemplateDetailerLocaleClass
//                                 {
//                                     SUBJECT = reader["SUBJECT"].ToString(),
//                                     BODY = reader["BODY"].ToString(),
//                                     SMS = reader["SMS"].ToString()
//                                 };
//                             }
//                         }
//                         conn.Close();
//                     }

//                     if (etDetEnglish != null)
//                     {
//                         // Get Email Template Detail Locale
//                         using (SqlCommand cmd = new SqlCommand("sp_GetEmailTemplateDetailLocaleByID", conn))
//                         {
//                             cmd.CommandType = CommandType.StoredProcedure;
//                             cmd.Parameters.AddWithValue("@EmailTemplateDetailID", selectparticularEmailTemplateDetailLocaleobj.EmailTemplateDetailID);

//                             conn.Open();
//                             using (SqlDataReader reader = cmd.ExecuteReader())
//                             {
//                                 if (reader.Read())
//                                 {
//                                     listEtDet.Add(new EmailTemplateDetailerLocaleClass
//                                     {
//                                         SUBJECT = etDetEnglish.SUBJECT,
//                                         BODY = etDetEnglish.BODY,
//                                         SMS = etDetEnglish.SMS,
//                                         ETDLocaleID = Convert.ToInt32(reader["TEMPLATEDETAILLOCALEID"]),
//                                         SUBJECT_Locale = reader["SUBJECT_LOCALE"].ToString(),
//                                         BODY_Locale = reader["BODY_LOCALE"].ToString(),
//                                         SMS_Locale = reader["SMS_LOCALE"].ToString()
//                                     });
//                                 }
//                                 else
//                                 {
//                                     listEtDet.Add(etDetEnglish);
//                                 }
//                             }
//                             conn.Close();
//                         }
//                         JsonData = listEtDet;
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//             }

//             return new JsonResult(JsonData);
//         }
//         #endregion


//         #region ::: SaveEmailTemplateDetailLocale Uday Kumar J B 13-08-2024 :::
//         /// <summary>
//         /// Save Email Template Detail Locale
//         /// </summary>  
//         ///
//         public static IActionResult SaveEmailTemplateDetailLocale(string connString, SaveEmailTemplateDetailLocaleList SaveEmailTemplateDetailLocaleobj)
//         {
//             try
//             {
//                 string etDetLocale = Common.DecryptString(SaveEmailTemplateDetailLocaleobj.etDetLocale);
//                 string decodedEtDetLocale = Uri.UnescapeDataString(etDetLocale);

//                 string[] etDLocaleArr = decodedEtDetLocale.Split('$');

//                 int templateDetailLocaleID = Convert.ToInt32(etDLocaleArr[0]);
//                 int templateDetailID = Convert.ToInt32(etDLocaleArr[1]);
//                 string languageCode = SaveEmailTemplateDetailLocaleobj.UserLanguageCode.ToString();
//                 string subjectLocale = etDLocaleArr[2];
//                 string bodyLocale = etDLocaleArr[3];
//                 string smsLocale = etDLocaleArr[4];

//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     using (var command = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveEmailTemplateDetailLocale", connection))
//                     {
//                         command.CommandType = CommandType.StoredProcedure;

//                         command.Parameters.AddWithValue("@TemplateDetailLocaleID", templateDetailLocaleID);
//                         command.Parameters.AddWithValue("@TemplateDetailID", templateDetailID);
//                         command.Parameters.AddWithValue("@LanguageCode", languageCode);
//                         command.Parameters.AddWithValue("@SubjectLocale", subjectLocale);
//                         command.Parameters.AddWithValue("@BodyLocale", bodyLocale);
//                         command.Parameters.AddWithValue("@SmsLocale", smsLocale);

//                         command.ExecuteNonQuery();
//                     }

//                     int templateId;
//                     using (var command = new SqlCommand("usp_SaveEmailTemplateDetailLocale", connection))
//                     {
//                         command.Parameters.AddWithValue("@TemplateDetailID", templateDetailID);
//                         templateId = (int)command.ExecuteScalar();
//                     }

//                     //  gbl.InsertGPSDetails(Convert.ToInt32(SaveEmailTemplateDetailLocaleobj.Company_ID), Convert.ToInt32(SaveEmailTemplateDetailLocaleobj.Branch), Convert.ToInt32(SaveEmailTemplateDetailLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreEmailTemplate")), templateId, 0, 0, "Update", false, Convert.ToInt32(SaveEmailTemplateDetailLocaleobj.MenuID));
//                 }

//                 return new JsonResult("1");
//             }
//             catch (Exception exp)
//             {
//                 return new JsonResult("0");
//             }
//         }
//         #endregion


//         #region :::  CommonMethodForEmailandSMS Uday Kumar J B 13-08-2024 not Used I think but check once:::
//         /// <summary>
//         /// To fetch Email Subject, Boday & SMS
//         /// </summary> 
//         /// 
//         public static StringBuilder[] CommonMethodForEmailandSMS(string connString, CommonMethodForEmailandSMSList CommonMethodForEmailandSMSobj)
//         {
//             int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
//             int BranchId = CommonMethodForEmailandSMSobj?.BranchId ?? 0;

//             string p1 = CommonMethodForEmailandSMSobj?.p1 ?? "";
//             string p2 = CommonMethodForEmailandSMSobj?.p2 ?? "";
//             string p3 = CommonMethodForEmailandSMSobj?.p3 ?? "";
//             string p4 = CommonMethodForEmailandSMSobj?.p4 ?? "";
//             string p5 = CommonMethodForEmailandSMSobj?.p5 ?? "";
//             string p6 = CommonMethodForEmailandSMSobj?.p6 ?? "";
//             string p7 = CommonMethodForEmailandSMSobj?.p7 ?? "";
//             string p8 = CommonMethodForEmailandSMSobj?.p8 ?? "";
//             string p9 = CommonMethodForEmailandSMSobj?.p9 ?? "";
//             string p10 = CommonMethodForEmailandSMSobj?.p10 ?? "";
//             string p11 = CommonMethodForEmailandSMSobj?.p11 ?? "";
//             string p12 = CommonMethodForEmailandSMSobj?.p12 ?? "";
//             string p13 = CommonMethodForEmailandSMSobj?.p13 ?? "";
//             string p14 = CommonMethodForEmailandSMSobj?.p14 ?? "";
//             string p15 = CommonMethodForEmailandSMSobj?.p15 ?? "";
//             string p16 = CommonMethodForEmailandSMSobj?.p16 ?? "";
//             string p17 = CommonMethodForEmailandSMSobj?.p17 ?? "";
//             string p18 = CommonMethodForEmailandSMSobj?.p18 ?? "";
//             string p19 = CommonMethodForEmailandSMSobj?.p19 ?? "";
//             string p20 = CommonMethodForEmailandSMSobj?.p20 ?? "";

//             StringBuilder[] Result = new StringBuilder[5];

//             try
//             {
//                 using (var connection = new SqlConnection(connString))
//                 {
//                     connection.Open();

//                     using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectEmailTemplateGmail", connection))
//                     {
//                         command.CommandType = CommandType.StoredProcedure;
//                         command.Parameters.AddWithValue("@TemplateCode", CommonMethodForEmailandSMSobj.TemplateCode);
//                         command.Parameters.AddWithValue("@CompanyId", CommonMethodForEmailandSMSobj.CompanyId);
//                         command.Parameters.AddWithValue("@LanguageCode", CommonMethodForEmailandSMSobj.LanguageCode);
//                         command.Parameters.AddWithValue("@BranchId", BranchId);
//                         command.Parameters.AddWithValue("@Param1", p1);
//                         command.Parameters.AddWithValue("@Param2", p2);
//                         command.Parameters.AddWithValue("@Param3", p3);
//                         command.Parameters.AddWithValue("@Param4", p4);
//                         command.Parameters.AddWithValue("@Param5", p5);
//                         command.Parameters.AddWithValue("@Param6", p6);
//                         command.Parameters.AddWithValue("@Param7", p7);
//                         command.Parameters.AddWithValue("@Param8", p8);
//                         command.Parameters.AddWithValue("@Param9", p9);
//                         command.Parameters.AddWithValue("@Param10", p10);
//                         command.Parameters.AddWithValue("@Param11", p11);
//                         command.Parameters.AddWithValue("@Param12", p12);
//                         command.Parameters.AddWithValue("@Param13", p13);
//                         command.Parameters.AddWithValue("@Param14", p14);
//                         command.Parameters.AddWithValue("@Param15", p15);
//                         command.Parameters.AddWithValue("@Param16", p16);
//                         command.Parameters.AddWithValue("@Param17", p17);
//                         command.Parameters.AddWithValue("@Param18", p18);
//                         command.Parameters.AddWithValue("@Param19", p19);
//                         command.Parameters.AddWithValue("@Param20", p20);


//                         using (var reader = command.ExecuteReader())
//                         {
//                             if (reader.Read())
//                             {
//                                 Result[0] = new StringBuilder(reader["Subject"].ToString());
//                                 Result[1] = new StringBuilder(reader["Body"].ToString());
//                                 Result[2] = new StringBuilder(reader["SMS"].ToString());
//                                 Result[3] = new StringBuilder(reader["BCC"].ToString());
//                                 Result[4] = new StringBuilder(reader["CC"].ToString());
//                             }
//                         }
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (LogException == 1)
//                 {
//                     LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
//                 }
//                 throw ex;
//             }

//             return (Result);
//         }
//         #endregion


//         #region :::  EmailTemplate List and obj classes Uday Kumar J B 13-08-2024  :::
//         /// <summary>
//         /// EmailTemplate Classes
//         /// </summary> 
//         /// 

//         public class CommonMethodForEmailandSMSList
//         {
//             public string TemplateCode { get; set; }
//             public int CompanyId { get; set; }
//             public string LanguageCode { get; set; }
//             public int BranchId { get; set; }
//             public string p1 { get; set; }
//             public string p2 { get; set; }
//             public string p3 { get; set; }
//             public string p4 { get; set; }
//             public string p5 { get; set; }
//             public string p6 { get; set; }
//             public string p7 { get; set; }
//             public string p8 { get; set; }
//             public string p9 { get; set; }
//             public string p10 { get; set; }
//             public string p11 { get; set; }
//             public string p12 { get; set; }
//             public string p13 { get; set; }
//             public string p14 { get; set; }
//             public string p15 { get; set; }
//             public string p16 { get; set; }
//             public string p17 { get; set; }
//             public string p18 { get; set; }
//             public string p19 { get; set; }
//             public string p20 { get; set; }
//         }
//         public class SaveEmailTemplateDetailLocaleList
//         {
//             public string etDetLocale { get; set; }
//             public string UserLanguageCode { get; set; }

//             public int Company_ID { get; set; }
//             public int Branch { get; set; }
//             public int User_ID { get; set; }
//             public int MenuID { get; set; }
//             public DateTime LoggedINDateTime { get; set; }
//         }
//         public class selectparticularEmailTemplateDetailLocaleList
//         {
//             public int EmailTemplateDetailID { get; set; }
//         }
//         public class SelectParticularEmailTemplateList
//         {
//             public int templateID { get; set; }
//             public int Company_ID { get; set; }

//             public List<GNM_User> UserDetails { get; set; }
//             public int Branch { get; set; }
//             public int User_ID { get; set; }
//             public int MenuID { get; set; }
//             public DateTime LoggedINDateTime { get; set; }
//         }
//         public class CheckDuplicateCompanyNameList
//         {
//             public int TemplateID { get; set; }
//             public int CompanyID { get; set; }
//             public string TEMPLATEDETAILIDValue { get; set; }
//         }
//         public class CheckDuplicateEmailTemplateList
//         {
//             public int templateID { get; set; }
//             public string code { get; set; }
//             public string name { get; set; }
//         }
//         public class deleteEmailTemplateDetailList
//         {
//             public string key { get; set; }
//             public int Company_ID { get; set; }
//             public int Branch { get; set; }
//             public int User_ID { get; set; }
//             public int MenuID { get; set; }
//             public DateTime LoggedINDateTime { get; set; }
//             public string GeneralCulture { get; set; }
//         }
//         public class deleteEmailTemplateList
//         {
//             public string TemplateID { get; set; }
//             public int Company_ID { get; set; }
//             public int Branch { get; set; }
//             public int User_ID { get; set; }
//             public int MenuID { get; set; }
//             public DateTime LoggedINDateTime { get; set; }
//         }
//         public class SaveEmailTemplateDetailerList
//         {
//             public string etDet { get; set; }
//             public int Company_ID { get; set; }
//             public int Branch { get; set; }
//             public int User_ID { get; set; }
//             public int MenuID { get; set; }
//             public DateTime LoggedINDateTime { get; set; }
//         }
//         public class SaveEmailTemplateHeaderList
//         {
//             public string EmailTempData { get; set; }
//             public int Company_ID { get; set; }
//             public int Branch { get; set; }
//             public int User_ID { get; set; }
//             public int MenuID { get; set; }
//             public DateTime LoggedINDateTime { get; set; }
//         }
//         public class SelectEmailTemplateDetailerList
//         {
//             public int UserLanguageID { get; set; }
//             public int GeneralLanguageID { get; set; }
//             public int TemplatID { get; set; }
//             public string UserCulture { get; set; }
//         }
//         public class SelectEmailTemplateLandList
//         {
//             public string UserCulture { get; set; }
//         }

//         #endregion


//         #region :::  EmailTemplate Classes Uday Kumar J B 13-08-2024:::
//         /// <summary>
//         /// EmailTemplate Classes
//         /// </summary> 
//         /// 
//         public class EmailTemplate
//         {
//             public int TEMPLATEID { get; set; }
//             public string TEMPLATECODE { get; set; }
//             public string TEMPLATENAME { get; set; }
//             public string P1 { get; set; }
//             public string P2 { get; set; }
//             public string P3 { get; set; }
//             public string P4 { get; set; }
//             public string P5 { get; set; }
//             public string P6 { get; set; }
//             public string P7 { get; set; }
//             public string P8 { get; set; }
//             public string P9 { get; set; }
//             public string P10 { get; set; }
//             public string P11 { get; set; }
//             public string P12 { get; set; }
//             public string P13 { get; set; }
//             public string P14 { get; set; }
//             public string P15 { get; set; }
//             public string P16 { get; set; }
//             public string P17 { get; set; }
//             public string P18 { get; set; }
//             public string P19 { get; set; }
//             public string P20 { get; set; }
//         }

//         public class EmailTemplateDetailerClass
//         {
//             public int TEMPLATEDETAILID { get; set; }
//             public int TEMPLATEID { get; set; }
//             public string COMPANYNAME { get; set; }
//             public string SUBJECT { get; set; }
//             public string edit { get; set; }
//             public string delete { get; set; }
//             public string BODY { get; set; }
//             public string SMS { get; set; }
//             public string BCC { get; set; }
//             public string CC { get; set; }
//             public string Locale { get; set; }
//         }

//         public class GNM_EMAILTEMPLATES
//         {
//             public int TEMPLATEID { get; set; }
//             public string TEMPLATECODE { get; set; }
//             public string TEMPLATENAME { get; set; }
//             public string P1 { get; set; }
//             public string P2 { get; set; }
//             public string P3 { get; set; }
//             public string P4 { get; set; }
//             public string P5 { get; set; }
//             public string P6 { get; set; }
//             public string P7 { get; set; }
//             public string P8 { get; set; }
//             public string P9 { get; set; }
//             public string P10 { get; set; }
//             public string P11 { get; set; }
//             public string P12 { get; set; }
//             public string P13 { get; set; }
//             public string P14 { get; set; }
//             public string P15 { get; set; }
//             public string REMARKS { get; set; }
//             public bool ISACTIVE { get; set; }
//             public string P16 { get; set; }
//             public string P17 { get; set; }
//             public string P18 { get; set; }
//             public string P19 { get; set; }
//             public string P20 { get; set; }
//         }

//         public class EmailTemplateDetailerLocaleClass
//         {
//             public string SUBJECT { get; set; }
//             public string BODY { get; set; }
//             public string SMS { get; set; }
//             public int ETDLocaleID { get; set; }
//             public string SUBJECT_Locale { get; set; }
//             public string BODY_Locale { get; set; }
//             public string SMS_Locale { get; set; }
//         }

//         #endregion


//     }
// }
