﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CorePrefixSuffixServices
    {

        #region ::: SelectPrefixSuffix Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// SelectPrefixSuffix
        /// </summary>   
        ///

        public static IActionResult SelectPrefixSuffix(string connString, SelectPrefixSuffixList SelectPrefixSuffixobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            var x = default(dynamic);
            int? BranchId = 0;
            string Sear = "Norm";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            List<GNMPrefixSuffix> prefixList = new List<GNMPrefixSuffix>();

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetching PrefixSuffix data
                    string prefixQuery = "Up_Select_Am_Erp_SelectPrefixSuffix";
                    using (SqlCommand command = new SqlCommand(prefixQuery, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Objectid", SelectPrefixSuffixobj.Objectid);
                        command.Parameters.AddWithValue("@CompanyID", SelectPrefixSuffixobj.Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNMPrefixSuffix prefixSuffix = new GNMPrefixSuffix
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("PrefixSuffix_ID")),
                                    Start_Number = reader.GetInt32(reader.GetOrdinal("Start_Number")),
                                    Prefix = reader.GetString(reader.GetOrdinal("Prefix")),
                                    Suffix = reader.GetString(reader.GetOrdinal("Suffix")),
                                    FinancialYear = reader.GetInt32(reader.GetOrdinal("FinancialYear")),
                                    FromDate = reader.GetDateTime(reader.GetOrdinal("FromDate")).ToString("dd-MMM-yyyy"),
                                    ToDate = reader.GetDateTime(reader.GetOrdinal("ToDate")).ToString("dd-MMM-yyyy")
                                };
                                prefixList.Add(prefixSuffix);
                            }
                        }
                    }

                    // Fetching Company Financial Years
                    string financialYearQuery = "Up_Select_Am_Erp_SelectPrefixSuffixFinancialYear";
                    List<GNM_CompanyFinancialYear> CompFinYears = new List<GNM_CompanyFinancialYear>();
                    using (SqlCommand command = new SqlCommand(financialYearQuery, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", SelectPrefixSuffixobj.Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_CompanyFinancialYear finYear = new GNM_CompanyFinancialYear
                                {
                                    Company_FinancialYear_ID = reader.GetInt32(reader.GetOrdinal("Company_FinancialYear_ID")),
                                    Company_FinancialYear = reader.GetInt32(reader.GetOrdinal("Company_FinancialYear"))
                                };
                                CompFinYears.Add(finYear);
                            }
                        }
                    }

                    // Constructing FinancialYear dropdown
                    string FinancialYear = "-1:---Select---;";
                    foreach (var finYear in CompFinYears)
                    {
                        FinancialYear += $"{finYear.Company_FinancialYear_ID}:{finYear.Company_FinancialYear};";
                    }
                    FinancialYear = FinancialYear.TrimEnd(';');

                    if (prefixList.Count > 0)
                    {
                        BranchId = prefixList.FirstOrDefault(a => a.Object_ID == SelectPrefixSuffixobj.Objectid)?.Branch_ID ?? 0;
                    }

                    // Filtering data
                    var filteredList = prefixList.AsQueryable().OrderByField(sidx, sord);
                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {
                            filteredList = filteredList.FilterSearch(filtersObj);
                            Sear = "Filter";
                        }
                    }
                    else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        if (advnfilter != null && advnfilter.rules.Count > 0)
                        {
                            filteredList = filteredList.AdvanceSearch(advnfilter);
                            page = 1;
                        }
                    }

                    // Pagination logic
                    count = filteredList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }
                    var arr = filteredList.Select(a => new
                    {
                        edit = $"<a title='Edit' href='#' style='font-size: 13px;' id='{a.ID}' class='EditPrefixSuffix' key='{a.ID}' class='EditPrefixSuffix' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = $"<input type='checkbox' key='{a.ID}' defaultchecked='' id='chk{a.ID}' class='PrefixSuffixDelete'/>",
                        ID = a.ID,
                        a.Start_Number,
                        Prefix = a.Prefix,
                        Suffix = a.Suffix,
                        a.FinancialYear,
                        FromDate = a.FromDate,
                        ToDate = a.ToDate
                    });

                    x = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = arr.ToList().Paginate(page, rows),
                        BranchId,
                        FinancialYear,
                        Sear
                    };

                    return new JsonResult(x);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(false);
            }
        }

        #endregion


        #region ::: SelectPrefixSuffixByBranch Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// SelectPrefixSuffix
        /// </summary>   
        public static IActionResult SelectPrefixSuffixByBranch(string connString, SelectPrefixSuffixByBranchList SelectPrefixSuffixByBranchobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filterString, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            int? BranchId = 0;
            object x = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User UserDetails = SelectPrefixSuffixByBranchobj.UserDetails.FirstOrDefault();
                BranchId = SelectPrefixSuffixByBranchobj.BranchID;

                // Get Prefix and Suffix data
                List<GNM_PrefixSuffix> prefixSuffixList = new List<GNM_PrefixSuffix>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("sp_GetPrefixSuffixByBranch", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ObjectID", SelectPrefixSuffixByBranchobj.Objectid);
                        cmd.Parameters.AddWithValue("@CompanyID", SelectPrefixSuffixByBranchobj.Company_ID);
                        cmd.Parameters.AddWithValue("@BranchID", SelectPrefixSuffixByBranchobj.BranchID);
                        cmd.Parameters.AddWithValue("@Sidx", sidx);
                        cmd.Parameters.AddWithValue("@Sord", sord);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                prefixSuffixList.Add(new GNM_PrefixSuffix
                                {
                                    PrefixSuffix_ID = Convert.ToInt32(reader["PrefixSuffix_ID"]),
                                    Start_Number = Convert.ToInt32(reader["Start_Number"]),
                                    Prefix = reader["Prefix"].ToString(),
                                    Suffix = reader["Suffix"].ToString(),
                                    FinancialYear = Convert.ToInt32(reader["FinancialYear"]),
                                    FromDate = Convert.ToDateTime(reader["FromDate"]),
                                    ToDate = Convert.ToDateTime(reader["ToDate"])
                                });
                            }
                        }
                    }
                }

                // Get Financial Years
                List<string> financialYears = new List<string>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("sp_GetCompanyFinancialYears", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", SelectPrefixSuffixByBranchobj.Company_ID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                financialYears.Add(reader["Company_FinancialYear_ID"] + ":" + reader["Company_FinancialYear"]);
                            }
                        }
                    }
                }

                string FinancialYear = "-1:---Select---;" + string.Join(";", financialYears);

                count = prefixSuffixList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                var arr = prefixSuffixList
                    .Where(a => a.Company_ID == SelectPrefixSuffixByBranchobj.Company_ID && a.Branch_ID == (SelectPrefixSuffixByBranchobj.BranchID == 0 ? null : BranchId))
                    .Select(a => new
                    {
                        edit = $"<a title='Edit' href='#' style='font-size: 13px;' id='{a.PrefixSuffix_ID}' class='EditPrefixSuffix' key='{a.PrefixSuffix_ID}' class='EditPrefixSuffix' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = $"<input type='checkbox' key='{a.PrefixSuffix_ID}' defaultchecked=''  id='chk{a.PrefixSuffix_ID}' class='PrefixSuffixDelete'/>",
                        ID = a.PrefixSuffix_ID,
                        a.Start_Number,
                        a.Prefix,
                        a.Suffix,
                        a.FinancialYear,
                        FromDate = a.FromDate.ToString("dd-MMM-yyyy"),
                        ToDate = a.ToDate.ToString("dd-MMM-yyyy"),
                    });

                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = arr.Skip((page - 1) * rows).Take(rows).ToList(),
                    BranchId,
                    FinancialYear
                };

                return new JsonResult(x);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(false);
            }
        }
        #endregion


        #region ::: InsertPrefixSuffix Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// InsertPrefixSuffix
        /// </summary> 
        /// 

        public static IActionResult InsertPrefixSuffix(string connString, InsertPrefixSuffixList InsertPrefixSuffixobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = InsertPrefixSuffixobj.UserDetails.FirstOrDefault();
                int userID = InsertPrefixSuffixobj.User_ID;
                int CompanyID = InsertPrefixSuffixobj.Company_ID;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    JObject jObj = JObject.Parse(InsertPrefixSuffixobj.data);
                    foreach (var row in jObj["rows"])
                    {
                        string ID = row["ID"].ToString().Trim();
                        int SelectedObject = Convert.ToInt32(row["SelectedObject"]);
                        int SelectedBranch = Convert.ToInt32(row["SelectedBranch"]);
                        int startNum = Convert.ToInt32(row["startNum"]);
                        string prefix = Common.DecryptString(row["prefix"].ToString());
                        string suffix = Common.DecryptString(row["suffix"].ToString());
                        DateTime FDate = Convert.ToDateTime(row["FDate"]);
                        DateTime TDate = Convert.ToDateTime(row["TDate"] + " 23:59:59.000");
                        int FinYear = Convert.ToInt32(row["Finyear"]);
                        int FinYearID = Convert.ToInt32(row["FinYearID"]);

                        // Determine which stored procedure to call
                        string query;
                        if (string.IsNullOrWhiteSpace(ID))
                        {
                            query = "UP_INS_AM_ERP_InsertPrefixSuffix";
                        }
                        else
                        {
                            query = "UP_Upd_AM_ERP_UpdatePrefixSuffix";
                        }

                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@BranchID", SelectedBranch == 0 ? (object)DBNull.Value : SelectedBranch);
                            command.Parameters.AddWithValue("@ObjectID", SelectedObject);
                            command.Parameters.AddWithValue("@StartNum", startNum);
                            command.Parameters.AddWithValue("@Prefix", prefix);
                            command.Parameters.AddWithValue("@Suffix", suffix);
                            command.Parameters.AddWithValue("@FDate", FDate);
                            command.Parameters.AddWithValue("@TDate", TDate);
                            command.Parameters.AddWithValue("@FinancialYear", FinYear);
                            command.Parameters.AddWithValue("@CompanyFinancialYearID", FinYearID);
                            command.Parameters.AddWithValue("@ModifiedBY", userID);
                            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                            if (!string.IsNullOrWhiteSpace(ID))
                            {
                                command.Parameters.AddWithValue("@PrefixSuffixID", Convert.ToInt32(ID));
                            }

                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: DeletePreficSuffix Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        /// 

        public static IActionResult DeletePreficSuffix(string connString, DeletePrefixSuffixList DeletePrefixSuffixobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeletePrefixSuffixobj.key);
                int rowCount = jobj["rows"].Count();

                for (int i = 0; i < rowCount; i++)
                {
                    int id = (int)jobj["rows"][i]["id"];

                    // Delete the PrefixSuffix record
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("DeletePrefixSuffix", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PrefixSuffix_ID", id);
                            cmd.ExecuteNonQuery();
                        }
                    }
                    // Log deletion
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(DeletePrefixSuffixobj.Company_ID),
                    //    Convert.ToInt32(DeletePrefixSuffixobj.Branch),
                    //    Convert.ToInt32(DeletePrefixSuffixobj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CorePrefixSuffix")),
                    //    id,
                    //    0,
                    //    0,
                    //    "Deleted",
                    //    false,
                    //    Convert.ToInt32(DeletePrefixSuffixobj.MenuID),
                    //    Convert.ToDateTime(DeletePrefixSuffixobj.LoggedINDateTime)
                    //);


                }

                errorMsg = CommonFunctionalities.GetResourceString(DeletePrefixSuffixobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg = CommonFunctionalities.GetResourceString(DeletePrefixSuffixobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: LoadObjectDropdown Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to Load Object Dropdowns
        /// </summary>
        /// 

        public static IActionResult LoadObjectDropdown(string connString, LoadObjectDropdownList LoadObjectDropdownobj)
        {
            var jsonData = new { ObjArr = new List<object>(), BranchArr = new List<object>() };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User UserDetails = LoadObjectDropdownobj.UserDetails.FirstOrDefault();
                int UserLang = Convert.ToInt32(LoadObjectDropdownobj.UserLanguageID);
                int GenLang = Convert.ToInt32(LoadObjectDropdownobj.GeneralLanguageID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch PrefixObject data
                    List<object> objArr = new List<object>();
                    string queryPrefixObject = "UP_GET_AM_ERP_PrefixObject";
                    using (SqlCommand commandPrefixObject = new SqlCommand(queryPrefixObject, connection))
                    {
                        commandPrefixObject.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader readerPrefixObject = commandPrefixObject.ExecuteReader())
                        {
                            while (readerPrefixObject.Read())
                            {
                                objArr.Add(new
                                {
                                    ID = readerPrefixObject["Object_ID"],
                                    Name = readerPrefixObject["Object_Description"]
                                });
                            }
                        }
                    }

                    // Fetch BranchObject data
                    List<object> branchArr = new List<object>();
                    string queryBranchObject = "UP_GET_AM_ERP_BranchObject";
                    using (SqlCommand commandBranchObject = new SqlCommand(queryBranchObject, connection))
                    {
                        commandBranchObject.CommandType = CommandType.StoredProcedure;
                        commandBranchObject.Parameters.AddWithValue("@CompanyID", LoadObjectDropdownobj.Company_ID);
                        using (SqlDataReader readerBranchObject = commandBranchObject.ExecuteReader())
                        {
                            while (readerBranchObject.Read())
                            {
                                branchArr.Add(new
                                {
                                    ID = readerBranchObject["Branch_ID"],
                                    Name = readerBranchObject["Branch_Name"]
                                });
                            }
                        }
                    }

                    jsonData = new
                    {
                        ObjArr = objArr,
                        BranchArr = branchArr
                    };
                }

                if (UserLang != GenLang)
                {
                    // Additional logic if UserLang is different from GenLang
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: ValidateFinYear Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to log Exception to text file
        /// </summary>    
        /// 

        public static IActionResult ValidateFinYear(string connString, ValidateFinYearList ValidateFinYearobj)
        {
            bool IsValid = true;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User UserDetails = ValidateFinYearobj.UserDetails.FirstOrDefault();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Determine which stored procedure to call
                    string spName = "UP_SEL_AM_ERP_ValidatePrefixSuffixFinYear";
                    SqlCommand command = new SqlCommand(spName, connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@FinYear", ValidateFinYearobj.FinYear);
                    command.Parameters.AddWithValue("@ObjectID", ValidateFinYearobj.Objectid);
                    command.Parameters.AddWithValue("@CompanyID", ValidateFinYearobj.Company_ID);

                    if (!string.IsNullOrWhiteSpace(ValidateFinYearobj.Key))
                    {
                        command.Parameters.AddWithValue("@Key", Convert.ToInt32(ValidateFinYearobj.Key));
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@Key", DBNull.Value);
                    }

                    if (ValidateFinYearobj.BranchID != 0)
                    {
                        command.Parameters.AddWithValue("@BranchID", ValidateFinYearobj.BranchID);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@BranchID", DBNull.Value);
                    }

                    // Execute the stored procedure
                    SqlParameter returnParam = command.Parameters.Add("@IsValid", SqlDbType.Bit);
                    returnParam.Direction = ParameterDirection.Output;
                    command.ExecuteNonQuery();

                    // Get the output parameter value
                    // IsValid = (bool)returnParam.Value;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
            return new JsonResult(IsValid);
        }
        #endregion


        #region ::: SelectDates Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to select the Dates for Comapny financial Year
        /// </summary>  
        /// 

        public static IActionResult SelectDates(string connString, SelectDatesList SelectDatesobj)
        {
            var Jsondata = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User UserDetails = SelectDatesobj.UserDetails.FirstOrDefault();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Determine which stored procedure to call
                    string spName = "UP_GET_AM_ERP_GetSelectDates";
                    SqlCommand command = new SqlCommand(spName, connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@CompanyID", SelectDatesobj.Company_ID);
                    command.Parameters.AddWithValue("@Finyear", SelectDatesobj.Finyear);

                    // Execute the stored procedure
                    SqlDataReader reader = command.ExecuteReader();

                    if (reader.HasRows)
                    {
                        reader.Read();

                        // Extract FromDate and ToDate from the reader
                        DateTime fromDate = reader.GetDateTime(reader.GetOrdinal("Company_FinancialYear_FromDate"));
                        DateTime toDate = reader.GetDateTime(reader.GetOrdinal("Company_FinancialYear_ToDate"));

                        // Format dates as required
                        string formattedFromDate = fromDate.ToString("dd-MMM-yyyy");
                        string formattedToDate = toDate.ToString("dd-MMM-yyyy");

                        // Prepare Jsondata
                        Jsondata = new
                        {
                            FromDate = formattedFromDate,
                            ToDate = formattedToDate
                        };
                    }

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Jsondata);
        }
        #endregion


        #region ::: CorePrefixSuffix list and obj classes Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// Prefix Suffix list and obj classes
        /// </summary>  
        /// 
        public class SelectDatesList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Finyear { get; set; }
            public int Company_ID { get; set; }
        }

        public class ValidateFinYearList
        {
            public int FinYear { get; set; }
            public int Objectid { get; set; }
            public string Key { get; set; }
            public int BranchID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
        }

        public class LoadObjectDropdownList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }
        public class DeletePrefixSuffixList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }

            public DateTime LoggedINDateTime { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public string UserCulture { get; set; }
        }
        public class InsertPrefixSuffixList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string data { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
        }

        public class SelectPrefixSuffixByBranchList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int BranchID { get; set; }

            public int Objectid { get; set; }
            public int Company_ID { get; set; }
        }
        public class SelectPrefixSuffixList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Objectid { get; set; }
        }


        #endregion


        #region :::CorePrefixSuffix Classes :::
        public class GNMPrefixSuffix
        {
            public int ID { get; set; }
            public int Start_Number { get; set; }
            public string Prefix { get; set; }
            public string Suffix { get; set; }
            public string FromDate { get; set; }
            public int Object_ID { get; set; }
            public int Branch_ID { get; set; }
            public string ToDate { get; set; }
            public int FinancialYear { get; set; }
        }
        #endregion


    }
}
