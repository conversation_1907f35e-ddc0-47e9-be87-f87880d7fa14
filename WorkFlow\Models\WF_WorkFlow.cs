//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_WorkFlow
    {
        public WF_WorkFlow()
        {
            this.GNM_WFAction = new HashSet<WF_WFAction>();
            this.GNM_WFField = new HashSet<WF_WFField>();
            this.GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
            this.GNM_WFRole = new HashSet<WF_WFRole>();
            this.GNM_WFSteps = new HashSet<WF_WFSteps>();
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }
    
        public int WorkFlow_ID { get; set; }
        public string WorkFlow_Name { get; set; }
        public Nullable<bool> AllQueue_Filter_IsBranch { get; set; }
    
        public virtual ICollection<WF_WFAction> GNM_WFAction { get; set; }
        public virtual ICollection<WF_WFField> GNM_WFField { get; set; }
        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }
        public virtual ICollection<WF_WFRole> GNM_WFRole { get; set; }
        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }
    }
}
