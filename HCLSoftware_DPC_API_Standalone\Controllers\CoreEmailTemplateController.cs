﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreEmailTemplateService;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreEmailTemplateController : ApiController
    {

        #region ::: SelectEmailTemplateLand Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Get Email Template Detail for Landing Grid  
        /// </summary>  
        [Route("api/CoreEmailTemplate/SelectEmailTemplateLand")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectEmailTemplateLand([FromBody] SelectEmailTemplateLandList SelectEmailTemplateLandobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmailTemplateService.SelectEmailTemplateLand(connString, SelectEmailTemplateLandobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectEmailTemplateDetailer Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Get Email Template Detail for Landing Grid 
        /// </summary>  
        ///  
        [Route("api/CoreEmailTemplate/SelectEmailTemplateDetailer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectEmailTemplateDetailer([FromBody] SelectEmailTemplateDetailerList SelectEmailTemplateDetailerobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmailTemplateService.SelectEmailTemplateDetailer(connString, SelectEmailTemplateDetailerobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmailTemplateHeader Uday Kumar J B 13-08-2024 :::
        /// <summary>
        ///  Save record of Email Template Header  
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/SaveEmailTemplateHeader")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmailTemplateHeader([FromBody] SaveEmailTemplateHeaderList SaveEmailTemplateHeaderobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.SaveEmailTemplateHeader(connString, SaveEmailTemplateHeaderobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmailTemplateDetailer Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Save record of Email Template Detailer 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/SaveEmailTemplateDetailer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmailTemplateDetailer([FromBody] SaveEmailTemplateDetailerList SaveEmailTemplateDetailerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.SaveEmailTemplateDetailer(connString, SaveEmailTemplateDetailerobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: deleteEmailTemplate Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Delete record of Email Template 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/deleteEmailTemplate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult deleteEmailTemplate([FromBody] deleteEmailTemplateList deleteEmailTemplateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.deleteEmailTemplate(connString, deleteEmailTemplateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: deleteEmailTemplateDetail Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Delete record of Email Template Detail 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/deleteEmailTemplateDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult deleteEmailTemplateDetail([FromBody] deleteEmailTemplateDetailList deleteEmailTemplateDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.deleteEmailTemplateDetail(connString, deleteEmailTemplateDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckDuplicateEmailTemplate Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Check Duplicate value of EmailTemplate 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/CheckDuplicateEmailTemplate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateEmailTemplate([FromBody] CheckDuplicateEmailTemplateList CheckDuplicateEmailTemplateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.CheckDuplicateEmailTemplate(connString, CheckDuplicateEmailTemplateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckDuplicateCompanyName Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Check Duplicate value of Company Name 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/CheckDuplicateCompanyName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateCompanyName([FromBody] CheckDuplicateCompanyNameList CheckDuplicateCompanyNameobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.CheckDuplicateCompanyName(connString, CheckDuplicateCompanyNameobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularEmailTemplate Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Select Particular Email Template 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/SelectParticularEmailTemplate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularEmailTemplate([FromBody] SelectParticularEmailTemplateList SelectParticularEmailTemplateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.SelectParticularEmailTemplate(connString, SelectParticularEmailTemplateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCompanyNames Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Select Company Names 
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/SelectCompanyNames")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompanyNames()
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.SelectCompanyNames(connString);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: selectparticularEmailTemplateDetailLocale not used Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// select particular Email Template Detail Locale
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/selectparticularEmailTemplateDetailLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult selectparticularEmailTemplateDetailLocale([FromBody] selectparticularEmailTemplateDetailLocaleList selectparticularEmailTemplateDetailLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.selectparticularEmailTemplateDetailLocale(connString, selectparticularEmailTemplateDetailLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmailTemplateDetailLocale Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// Save Email Template Detail Locale
        /// </summary>  
        ///
        [Route("api/CoreEmailTemplate/SaveEmailTemplateDetailLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmailTemplateDetailLocale([FromBody] SaveEmailTemplateDetailLocaleList SaveEmailTemplateDetailLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.SaveEmailTemplateDetailLocale(connString, SaveEmailTemplateDetailLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  CommonMethodForEmailandSMS Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// To fetch Email Subject, Boday & SMS
        /// </summary> 
        /// 
        [Route("api/CoreEmailTemplate/CommonMethodForEmailandSMS")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CommonMethodForEmailandSMS([FromBody] CommonMethodForEmailandSMSList CommonMethodForEmailandSMSobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmailTemplateService.CommonMethodForEmailandSMS(connString, CommonMethodForEmailandSMSobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}