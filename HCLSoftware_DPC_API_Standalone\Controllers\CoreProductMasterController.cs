﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreProductMasterController : ApiController
    {

        #region ::: Select Uday Kumar J B 26-07-2024 :::
        /// <summary>
        /// To select All product
        /// </summary>
        ///
        [Route("api/CoreProductMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreMasterList SelectCoreMasterobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.Select(connstring, SelectCoreMasterobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Uday Kumar J B  23-07-2024:::
        /// <summary>
        /// To Insert Product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertCoreProductMasterList InsertCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.Insert(connString, InsertCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: To Export  Product Details Uday Kumar J B 30-07-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        ///
        [Route("api/CoreProductMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportCoreProductMasterList ExportCoreProductMasterobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportCoreProductMasterobj.sidx;
            string sord = ExportCoreProductMasterobj.sord;
            string filter = ExportCoreProductMasterobj.filter;
            string advnceFilter = ExportCoreProductMasterobj.advanceFilter;

            try
            {


                object Response = await CoreProductMasterServices.Export(ExportCoreProductMasterobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: Update Product Uday Kumar J B 26-07-2024:::
        /// <summary>
        /// To Update Product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateCoreProductMasterList UpdateCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.Update(connString, UpdateCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Delete Product 
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreProductMasterList DeleteCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.Delete(connString, DeleteCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterLsit SelectReferenceMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectReferenceMaster(connString, SelectReferenceMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductType Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Select ProductType 
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductType([FromBody] SelectProductTypeCoreProductMasterList SelectProductTypeCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectProductType(connString, SelectProductTypeCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectModel Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Select Model
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] SelectModelCoreProductMasterList SelectModelCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectModel(connString, SelectModelCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Service Company Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Service Company
        /// </summary>
        /// <param name="LanguageID"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/SelectServiceCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceCompany([FromBody] SelectServiceCompanyCoreProductMasterList SelectServiceCompanyCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectServiceCompany(connString, SelectServiceCompanyCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularProduct Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectParticularProduct")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularProduct([FromBody] SelectParticularProductCoreProductMasterList SelectParticularProductCoreProductMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectParticularProduct(connString, SelectParticularProductCoreProductMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularProductCSA Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Particular Product Component
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectParticularProductCSA")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularProductCSA([FromBody] SelectParticularProductCSAList SelectParticularProductCSAobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectParticularProductCSA(connString, SelectParticularProductCSAobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetCurrentDate Uday Kumar J B 23--7-2024 :::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/AddInformation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AddInformation([FromBody] AddInformationList AddInformationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.AddInformation(connString, AddInformationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularProductWarranty Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Particular Product Warranty
        /// </summary>
        ///
        [Route("api/CoreProductMaster/SelectParticularProductWarranty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularProductWarranty([FromBody] SelectParticularProductWarrantyList SelectParticularProductWarrantyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectParticularProductWarranty(connString, SelectParticularProductWarrantyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularComponentWarranty Uday Kumar J B 24-06-2024:::
        /// <summary>
        /// To Select Particular Component Warranty
        /// </summary>
        ///
        [Route("api/CoreProductMaster/SelectParticularComponentWarranty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularComponentWarranty([FromBody] SelectParticularComponentWarrantyList SelectParticularComponentWarrantyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectParticularComponentWarranty(connString, SelectParticularComponentWarrantyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductWarrantyDetails Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Select Warranty Details For a product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductWarrantyDetails([FromBody] SelectProductWarrantyDetailsList SelectProductWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductWarrantyDetails(connstring, SelectProductWarrantyDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectComponentWarrantyDetails Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Warranty Details For a Component
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectComponentWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectComponentWarrantyDetails([FromBody] SelectComponentWarrantyDetailsList SelectComponentWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectComponentWarrantyDetails(connstring, SelectComponentWarrantyDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularComponentWarrantyLocale Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Warranty Details For a Particular Locale
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectParticularComponentWarrantyLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularComponentWarrantyLocale([FromBody] SelectParticularComponentWarrantyLocaleList SelectParticularComponentWarrantyLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectParticularComponentWarrantyLocale(connString, SelectParticularComponentWarrantyLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveCompWarrantyLocaledetails Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To Save the Component warranty Details Locale 
        /// </summary>
        ///
        [Route("api/CoreProductMaster/SaveCompWarrantyLocaledetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveCompWarrantyLocaledetails([FromBody] SaveCompWarrantyLocaledetailsList SaveCompWarrantyLocaledetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveCompWarrantyLocaledetails(connString, SaveCompWarrantyLocaledetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularWarrantyLocale Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To Select Warranty Details For a Particular Product Locale
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectParticularProductWarrantyLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularProductWarrantyLocale([FromBody] SelectParticularProductWarrantyLocaleList SelectParticularProductWarrantyLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectParticularProductWarrantyLocale(connString, SelectParticularProductWarrantyLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductWarrantyLocaledetails Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To Save Warranty Details For a Particular Product Locale
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductWarrantyLocaledetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductWarrantyLocaledetails([FromBody] SaveProductWarrantyLocaledetailsList SaveProductWarrantyLocaledetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductWarrantyLocaledetails(connString, SaveProductWarrantyLocaledetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductCustomerDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Select Customer Details For a product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductCustomerDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductCustomerDetails([FromBody] SelectProductCustomerDetailsList SelectProductCustomerDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductCustomerDetails(connstring, SelectProductCustomerDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductContactorDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Select Customer Details For a product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductContactorDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductContactorDetails([FromBody] SelectProductContactorDetailsList SelectProductContactorDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductContactorDetails(connstring, SelectProductContactorDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductCustomerDetailsForIsParty Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Customer Details For a product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductCustomerDetailsForIsParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductCustomerDetailsForIsParty([FromBody] SelectProductCustomerDetailsForIsPartyList SelectProductCustomerDetailsForIsPartyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectProductCustomerDetailsForIsParty(connString, SelectProductCustomerDetailsForIsPartyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllPartyAddress Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// to select all the Party Address
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelAllPartyAddressForDefaultParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllPartyAddressForDefaultParty([FromBody] SelAllPartyAddressForDefaultPartyList SelAllPartyAddressForDefaultPartyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelAllPartyAddressForDefaultParty(connString, SelAllPartyAddressForDefaultPartyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllPartyAddressForDefaultPartyForGrid Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// to select all the Party Address
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelAllPartyAddressForDefaultPartyForGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllPartyAddressForDefaultPartyForGrid([FromBody] SelAllPartyAddressForDefaultPartyForGridList SelAllPartyAddressForDefaultPartyForGridobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelAllPartyAddressForDefaultPartyForGrid(connstring, SelAllPartyAddressForDefaultPartyForGridobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductSiteAddressDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Select Site Address Details For a product
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductSiteAddressDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductSiteAddressDetails([FromBody] SelectProductSiteAddressDetailsList SelectProductSiteAddressDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductSiteAddressDetails(connstring, SelectProductSiteAddressDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductServiceHistoryDetails Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Product Service History Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductServiceHistoryDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductServiceHistoryDetails([FromBody] SelectProductServiceHistoryDetailsList SelectProductServiceHistoryDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductServiceHistoryDetails(connstring, SelectProductServiceHistoryDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCSAProductServiceHistoryDetails Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Select Product Service History Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectCSAProductServiceHistoryDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCSAProductServiceHistoryDetails([FromBody] SelectCSAProductServiceHistoryDetailsList SelectCSAProductServiceHistoryDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectCSAProductServiceHistoryDetails(connstring, SelectCSAProductServiceHistoryDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductServiceSchduleDetails Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Select Product Service Schdule Details 
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductServiceSchduleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductServiceSchduleDetails([FromBody] SelectProductServiceSchduleDetailsList SelectProductServiceSchduleDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductServiceSchduleDetails(connstring, SelectProductServiceSchduleDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductComponentDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Select Product Component Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductComponentDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductComponentDetails([FromBody] SelectProductComponentDetailsList SelectProductComponentDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductComponentDetails(connstring, SelectProductComponentDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectComponentReadingDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Select Product Component Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectComponentReadingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectComponentReadingDetails([FromBody] SelectComponentReadingDetailsList SelectComponentReadingDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectComponentReadingDetails(connstring, SelectComponentReadingDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Product Status History Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To Select Product Component Details  
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductStatusHistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductStatusHistory([FromBody] SelectProductStatusHistoryList SelectProductStatusHistoryobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductStatusHistory(connstring, SelectProductStatusHistoryobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertReadingDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Insert Component Details
        /// </summary>
        ///  
        [Route("api/CoreProductMaster/InsertReadingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertReadingDetails([FromBody] InsertReadingDetailsList InsertReadingDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.InsertReadingDetails(connString, InsertReadingDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductWarrantyDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Save Product Warranty Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductWarrantyDetails([FromBody] SaveProductWarrantyDetailsList SaveProductWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductWarrantyDetails(connString, SaveProductWarrantyDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductCustomerDetails Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Save Product Customer Details
        /// </summary>
        ///
        [Route("api/CoreProductMaster/SaveProductCustomerDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductCustomerDetails([FromBody] SaveProductCustomerDetailsList SaveProductCustomerDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductCustomerDetails(connString, SaveProductCustomerDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductContractorDetails Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Save Product Customer Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductContractorDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductContractorDetails([FromBody] SaveProductContractorDetailsList SaveProductContractorDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductContractorDetails(connString, SaveProductContractorDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductSiteAddressDetails Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Save Product SiteAddress Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductSiteAddressDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductSiteAddressDetails([FromBody] SaveProductSiteAddressDetailsList SaveProductSiteAddressDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductSiteAddressDetails(connString, SaveProductSiteAddressDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductSalesHistory  Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To Save Product Sales History Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductSalesHistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductSalesHistory([FromBody] SaveProductSalesHistoryList SaveProductSalesHistoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductSalesHistory(connString, SaveProductSalesHistoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save  Warranty Details Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// Save CSA Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveComponentWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveComponentWarrantyDetails([FromBody] SaveComponentWarrantyDetailsList SaveComponentWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveComponentWarrantyDetails(connString, SaveComponentWarrantyDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Validate Sales InvoiceNumber Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To  Validate Sales InvoiceNumber
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/ValidateSalesInvoiceNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateSalesInvoiceNumber([FromBody] ValidateSalesInvoiceNumberList ValidateSalesInvoiceNumberobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.ValidateSalesInvoiceNumber(connString, ValidateSalesInvoiceNumberobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SetProductSiteAddressInActive Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To Set Product SiteAddress as Inactive
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SetProductSiteAddressInActive")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SetProductSiteAddressInActive([FromBody] SetProductSiteAddressInActiveList SetProductSiteAddressInActiveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SetProductSiteAddressInActive(connString, SetProductSiteAddressInActiveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductServiceScheduleDetails Uday Kumar J B 23-07-2024:::
        /// <summary>
        /// To Save Product Service Schedule Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductServiceScheduleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductServiceScheduleDetails([FromBody] SaveProductServiceScheduleDetailsList SaveProductServiceScheduleDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductServiceScheduleDetails(connString, SaveProductServiceScheduleDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteProductWarrantyDetails Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// To Delete Product Warranty Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/DeleteProductWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProductWarrantyDetails([FromBody] DeleteProductWarrantyDetailsList DeleteProductWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.DeleteProductWarrantyDetails(connString, DeleteProductWarrantyDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteProductCompWarrantyDetails Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To Delete Product Warranty Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/DeleteProductCompWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProductCompWarrantyDetails([FromBody] DeleteProductCompWarrantyDetailsList DeleteProductCompWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.DeleteProductCompWarrantyDetails(connString, DeleteProductCompWarrantyDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteProductServiceSchduleDetails Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Delete Product ServiceSchdule Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/DeleteProductServiceSchduleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProductServiceSchduleDetails([FromBody] DeleteProductServiceSchduleDetailsList DeleteProductServiceSchduleDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.DeleteProductServiceSchduleDetails(connString, DeleteProductServiceSchduleDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteProductComponentDetails Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Delete Product Component Details
        /// </summary>
        ///  
        [Route("api/CoreProductMaster/DeleteProductComponentDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProductComponentDetails([FromBody] DeleteProductComponentDetailsList DeleteProductComponentDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.DeleteProductComponentDetails(connString, DeleteProductComponentDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartModel Uday Kumar J B 19-07-2024 :::
        /// <summary>
        /// To Select Part Model
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectPartModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartModel([FromBody] SelectPartModelList SelectPartModelobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectPartModel(connString, SelectPartModelobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertComponentDetails Uday Kumar J B 19-07-2024:::
        /// <summary>
        /// To Insert Component Details
        /// </summary>
        ///
        [Route("api/CoreProductMaster/InsertComponentDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertComponentDetails([FromBody] InsertComponentDetailsList InsertComponentDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.InsertComponentDetails(connString, InsertComponentDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveCSADetails Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// Save CSA Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveCSADetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveCSADetails([FromBody] SaveCSADetailsList SaveCSADetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveCSADetails(connString, SaveCSADetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save  Warranty Details Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// Save CSA Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveWarrantyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveWarrantyDetails([FromBody] SaveWarrantyDetailsList SaveWarrantyDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveWarrantyDetails(connString, SaveWarrantyDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCSADetails Uday Kumar J B 22-07-2024::
        /// <summary>
        /// To Select CSA Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectCSADetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCSADetails([FromBody] SelectCSADetailsList SelectCSADetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectCSADetails(connstring, SelectCSADetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Reading Details Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Select Product Component Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectProductReadingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductReadingDetails([FromBody] SelectProductReadingDetailsList SelectProductReadingDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectProductReadingDetails(connstring, SelectProductReadingDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateComponentDetails Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To Update Product ComponentDetails  
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/UpdateComponentDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateComponentDetails([FromBody] UpdateComponentDetailsList UpdateComponentDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.UpdateComponentDetails(connString, UpdateComponentDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductModelAndSerialNumber Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Check if Model name & Serial Number Already exists
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckProductModelAndSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductModelAndSerialNumber([FromBody] CheckProductModelAndSerialNumberList CheckProductModelAndSerialNumberobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckProductModelAndSerialNumber(connString, CheckProductModelAndSerialNumberobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductComponentModelAndSerialNumber Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Check for Product and Model, Serial Number Already exists
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckProductComponentModelAndSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductComponentModelAndSerialNumber([FromBody] CheckProductComponentModelAndSerialNumberList CheckProductComponentModelAndSerialNumberobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckProductComponentModelAndSerialNumber(connString, CheckProductComponentModelAndSerialNumberobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductUniqueNo Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check if Product Unique Identifier Already Exits
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckProductUniqueNo")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductUniqueNo([FromBody] CheckProductUniqueNoList CheckProductUniqueNoobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckProductUniqueNo(connString, CheckProductUniqueNoobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductCSAUniqueNumber Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check if Product CSA Number is Uniqe 
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckProductCSAUniqueNo")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductCSAUniqueNo([FromBody] CheckProductCSAUniqueNoList CheckProductCSAUniqueNoobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckProductCSAUniqueNo(connString, CheckProductCSAUniqueNoobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckWarrantyType Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Check if Service Type Already Exits
        /// </summary>
        ///  
        [Route("api/CoreProductMaster/CheckWarrantyType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckWarrantyType([FromBody] CheckWarrantyTypeList CheckWarrantyTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckWarrantyType(connString, CheckWarrantyTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckWarrantyType Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Check if Service Type Already Exits
        /// </summary>
        ///  
        [Route("api/CoreProductMaster/CheckWarrantyToDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckWarrantyToDate([FromBody] CheckWarrantyToDateList CheckWarrantyToDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckWarrantyToDate(connString, CheckWarrantyToDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckServiceHistory Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Check if Service History is Done
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckServiceHistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckServiceHistory([FromBody] CheckServiceHistoryList CheckServiceHistoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckServiceHistory(connString, CheckServiceHistoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckServiceHistory Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check if Service History is Done
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckReadingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckReadingDetails([FromBody] CheckReadingDetailsList CheckReadingDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckReadingDetails(connString, CheckReadingDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetCustomer Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Select Customer
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/GetCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCustomer([FromBody] GetCustomerList GetCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetCustomer(connString, GetCustomerobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetContractor Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To Select Contractor
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/GetContractor")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetContractor([FromBody] GetContractorList GetContractorobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetContractor(connString, GetContractorobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetCustomerDetails Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To select All Customers Matching
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/GetCustomerDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCustomerDetails([FromBody] GetCustomerDetailsList GetCustomerDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];



            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.GetCustomerDetails(connstring, GetCustomerDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetContractorDetails Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To select All Customers Matching
        /// </summary>
        ///
        [Route("api/CoreProductMaster/GetContractorDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetContractorDetails([FromBody] GetContractorDetailsList GetContractorDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.GetContractorDetails(connstring, GetContractorDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductCSAFromDateToDate Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check if Service Type Already Exits
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckProductCSAFromAndToDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductCSAFromAndToDate([FromBody] CheckProductCSAFromAndToDateList CheckProductCSAFromAndToDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckProductCSAFromAndToDate(connString, CheckProductCSAFromAndToDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductCSAFromDateToDate Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check if Service Type Already Exits
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckProductWarrantyFromAndToDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductWarrantyFromAndToDate([FromBody] CheckProductWarrantyFromAndToDateList CheckProductWarrantyFromAndToDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckProductWarrantyFromAndToDate(connString, CheckProductWarrantyFromAndToDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckCustomerFromDate Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check Customer From Date Validations
        /// </summary>
        ///
        [Route("api/CoreProductMaster/CheckCustomerFromDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCustomerFromDate([FromBody] CheckCustomerFromDateList CheckCustomerFromDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckCustomerFromDate(connString, CheckCustomerFromDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckContractorFromDate Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Check Customer From Date Validations
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckContractorFromDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckContractorFromDate([FromBody] CheckContractorFromDateList CheckContractorFromDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckContractorFromDate(connString, CheckContractorFromDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCustomer Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Select Customer
        /// </summary>
        ///
        [Route("api/CoreProductMaster/SelectCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCustomer([FromBody] SelectCustomerList SelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectCustomer(connString, SelectCustomerobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckModelName Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check Model Name
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckModelName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckModelName([FromBody] CheckModelNameList CheckModelNameobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckModelName(connString, CheckModelNameobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetPartByID Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Validate PartByID 
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/GetPartByID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartByID([FromBody] GetPartByIDList GetPartByIDobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetPartByID(connString, GetPartByIDobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckSiteAddress Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To Check SiteAddress
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckSiteAddress")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckSiteAddress([FromBody] CheckSiteAddressList CheckSiteAddressobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckSiteAddress(connString, CheckSiteAddressobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckCustomerToDate Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Check Customer From Date Validations
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/CheckCustomerToDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCustomerToDate([FromBody] CheckCustomerToDateList CheckCustomerToDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckCustomerToDate(connString, CheckCustomerToDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: checkstatushistory Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To check statushistory
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/checkstatushistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult checkstatushistory([FromBody] CheckstatushistoryList checkstatushistoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.checkstatushistory(connString, checkstatushistoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: checkSecondRetailExist Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// To check SecondRetailExist
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/checkSecondRetailExist")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult checkSecondRetailExist([FromBody] CheckSecondRetailExistList checkSecondRetailExistobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.checkSecondRetailExist(connString, checkSecondRetailExistobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelPurchaseDetails Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Select All purchase Invoice details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/SelPurchaseDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelPurchaseDetails([FromBody] SelPurchaseDetailsList SelPurchaseDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelPurchaseDetails(connstring, SelPurchaseDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Sales History Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Select All Sales History details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/SelSalesHistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelSalesHistory([FromBody] SelSalesHistoryList SelSalesHistoryobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelSalesHistory(connstring, SelSalesHistoryobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectFieldSearchParts Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// Select Field Search Part
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/SelectFieldSearchParts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchParts([FromBody] SelectFieldSearchPartsList SelectFieldSearchPartsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectFieldSearchParts(connstring, SelectFieldSearchPartsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select OperationCode change Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To select OperationCode when enter operation code and press tab
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/ChangeOperationCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ChangeOperationCode([FromBody] ChangeOperationCodeList ChangeOperationCodeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.ChangeOperationCode(connString, ChangeOperationCodeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Field Search OperationCode Uday Kumar J B 24-07-2024:::
        /// <summary>
        /// To Select Field Search for Operation code
        /// </summary> 
        /// 
        [Route("api/CoreProductMaster/SelectFieldSearchOperationCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchOperationCode([FromBody] SelectFieldSearchOperationCodeList SelectFieldSearchOperationCodeobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectFieldSearchOperationCode(connstring, SelectFieldSearchOperationCodeobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert and Update Product Operation Details Uday Kumar J B 24-07-2024 :::
        /// <summary>
        /// To Insert and Update Product Operation details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SaveProductOperationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductOperationDetails([FromBody] SaveProductOperationDetailsList SaveProductOperationDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveProductOperationDetails(connString, SaveProductOperationDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Product Operation Details Uday Kumar J B 25-07-2024:::
        /// <summary>
        /// To Select Product Operation Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelAllProductOperationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllProductOperationDetails([FromBody] SelAllProductOperationDetailsList SelAllProductOperationDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelAllProductOperationDetails(connstring, SelAllProductOperationDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Check CSA From Date Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// To Check CSA From Date  
        /// </summary>
        ///
        [Route("api/CoreProductMaster/CheckCSAFromDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCSAFromDate([FromBody] CheckCSAFromDateList CheckCSAFromDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckCSAFromDate(connString, CheckCSAFromDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteProductCSAOperationDetails Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// To Delete Product Operation Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/DeleteProductCSAOperationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProductCSAOperationDetails([FromBody] DeleteProductCSAOperationDetailsList DeleteProductCSAOperationDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.DeleteProductCSAOperationDetails(connString, DeleteProductCSAOperationDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartNumber Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// Select Part Number
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectPartNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartNumber([FromBody] SelectPartNumberList SelectPartNumberobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectPartNumber(connString, SelectPartNumberobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectComponentFieldSearchParts Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// Select Field Search Part
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/SelectComponentFieldSearchParts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectComponentFieldSearchParts([FromBody] SelectComponentFieldSearchPartsList SelectComponentFieldSearchPartsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectComponentFieldSearchParts(connstring, SelectComponentFieldSearchPartsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectComponentPartNumber Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// Select Component Part Number
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/SelectComponentPartNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectComponentPartNumber([FromBody] SelectComponentPartNumberList SelectComponentPartNumberobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectComponentPartNumber(connString, SelectComponentPartNumberobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Check Valid Date Format Uday Kumar J B 22-07-2024 :::
        [Route("api/CoreProductMaster/CheckValidDateFormat")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckValidDateFormat([FromBody] CheckValidDateFormatList CheckValidDateFormatobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckValidDateFormat(CheckValidDateFormatobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: GetServiceRequestDetail Uday Kumar J B 22-07-2024 :::
        /// <summary>
        /// GetServiceRequestDetail
        /// </summary>
        /// <param name="ServiceEngineerName"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/GetServiceRequestDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetServiceRequestDetail([FromBody] GetServiceRequestDetailList GetServiceRequestDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetServiceRequestDetail(connString, GetServiceRequestDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectFieldSearchDealerBranches Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// SelectFieldSearchDealerBranches
        /// </summary>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/SelectFieldSearchDealerBranches")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchDealerBranches([FromBody] SelectFieldSearchDealerBranchesList SelectFieldSearchDealerBranchesobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreProductMasterServices.SelectFieldSearchDealerBranches(connstring, SelectFieldSearchDealerBranchesobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetBranch Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// To Select Customer
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/GetBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetBranch([FromBody] GetBranchList GetBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetBranch(connString, GetBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  SelectWareHouseByBranch Uday Kumar J B 22-0-2024:::
        /// <summary>
        /// SelectWareHouseByBranch
        /// </summary>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/SelectWareHouseByBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectWareHouseByBranch([FromBody] SelectWareHouseByBranchLista SelectWareHouseByBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SelectWareHouseByBranch(connString, SelectWareHouseByBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckDuplicatetDueDate Uday Kumar J B 23-07-2024 :::
        /// <summary>
        /// CheckDuplicatetDueDate
        /// </summary>
        /// <param name="ProductID"></param>
        /// <param name="DueDate"></param>
        /// <param name="ProductCSAServiceSchedule_ID"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/CheckDuplicatetDueDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicatetDueDate([FromBody] CheckDuplicatetDueDateList CheckDuplicatetDueDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckDuplicatetDueDate(connString, CheckDuplicatetDueDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ValidateWarrantyToDate Uday Kumar J B 25-07-2024:::
        /// <summary>
        /// ValidateWarrantyToDate
        /// </summary>
        /// <param name="WarrantyFromToDate"></param>
        /// <param name="ProductID"></param>
        /// <param name="ProductWarrantyID"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        ///  
        [Route("api/CoreProductMaster/ValidateWarrantyToDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateWarrantyToDate([FromBody] ValidateWarrantyToDateList ValidateWarrantyToDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.ValidateWarrantyToDate(connString, ValidateWarrantyToDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  ValidateSaledatelesstahncurrentdate Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// ValidateSaledatelesstahncurrentdate
        /// </summary>
        /// <returns></returns>
        ///
        [Route("api/CoreProductMaster/ValidateSaledatelesstahncurrentdate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateSaledatelesstahncurrentdate([FromBody] ValidateSaledatelesstahncurrentdateList ValidateSaledatelesstahncurrentdateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.ValidateSaledatelesstahncurrentdate(ValidateSaledatelesstahncurrentdateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ValidateCompWarrantyToDate Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// ValidateCompWarrantyToDate
        /// </summary>
        /// <param name="CompWarrantyFromToDate"></param>
        /// <param name="ProductID"></param>
        /// <param name="ComponentWarrantyID"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/ValidateCompWarrantyToDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateCompWarrantyToDate([FromBody] ValidateCompWarrantyToDateList ValidateCompWarrantyToDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.ValidateCompWarrantyToDate(connString, ValidateCompWarrantyToDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetWarrantyOrAgreement Uday Kumar J B 30-07-2024:::
        /// <summary>
        /// To Get Warranty Or Agreement Details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/GetWarrantyOrAgreement")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetWarrantyOrAgreement([FromBody] GetWarrantyOrAgreementList GetWarrantyOrAgreementobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetWarrantyOrAgreement(connString, GetWarrantyOrAgreementobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetCountryRegion Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// Get Country Region for Site Address
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/GetCountryRegion")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCountryRegion([FromBody] GetCountryRegionList GetCountryRegionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetCountryRegion(connString, GetCountryRegionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteProductCustomerDetails Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// To Delete Product Customer Details
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/DeleteProductCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProductCustomer([FromBody] DeleteProductCustomerList DeleteProductCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.DeleteProductCustomer(connString, DeleteProductCustomerobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ImportPartsTemplate need to Do Pending Uday Kumar J B 25-07-2024 :::
        /// <summary>
        /// ImportPartsTemplate
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/ImportPartsTemplate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ImportPartsTemplate([FromBody] ImportPartsTemplateList Obj)
        {
            var Response = default(dynamic);
            try
            {
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreProductMasterServices.ImportPartsTemplate(Obj, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UploadFile Uday Kumar J B  need to do Pending 25-07-2024 :::
        /// <summary>
        /// UploadFile
        /// </summary>
        /// 
        [Route("api/CoreProductMaster/UploadFile")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> UploadFile(UploadFileList UploadFileobj)
        {
            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                string branchID = null;
                string companyID = null;
                string productID = null;
                string userID = null;
                string menuID = null;
                string User_LanguageID = null;
                string General_LanguageID = null;
                string ObjectID = null;
                string loggedInDateTime = null;
                string userCulture = null;
                IFormFile componentWarrantyFile = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "branchID":
                                branchID = value;
                                break;
                            case "companyID":
                                companyID = value;
                                break;
                            case "userID":
                                userID = value;
                                break;
                            case "productID":
                                productID = value;
                                break;
                            case "loggedInDateTime":
                                loggedInDateTime = value;
                                break;
                            case "menuID":
                                menuID = value;
                                break;
                            case "User_LanguageID":
                                User_LanguageID = value;
                                break;
                            case "General_LanguageID":
                                General_LanguageID = value;
                                break;
                            case "userCulture":
                                userCulture = value;
                                break;
                            case "ObjectID":
                                ObjectID = value;
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            componentWarrantyFile = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = CoreProductMasterServices.UploadFile(componentWarrantyFile, connString, UploadFileobj);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }
        #endregion


        #region ::: GetPartyCode 25-07-2024:::
        /// <summary>
        /// GetPartyCode
        /// </summary>
        /// <param name="Party_ID"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/GetPartyCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyCode([FromBody] GetPartyCodeList GetPartyCodeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.GetPartyCode(connString, GetPartyCodeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckIFPartExists Uday Kumar J B 26-07-2024:::
        /// <summary>
        /// CheckIFPartExists
        /// </summary>
        /// <param name="PartNumber"></param>
        /// <param name="Company_ID"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreProductMaster/CheckIFPartExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckIFPartExists([FromBody] CheckIFPartExistsList CheckIFPartExistsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.CheckIFPartExists(connString, CheckIFPartExistsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SaveAttachment Uday Kumar J B 26-07-2024:::
        /// <summary>
        /// Save Sales history Attachment
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/SaveAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveAttachment([FromBody] SaveAttachmentProductList SaveAttachmentProductobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveAttachment(connString, SaveAttachmentProductobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveAttachmentComponentWarranty Uday Kumar J B 26-07-2024:::
        /// <summary>
        /// Save Sales history Attachment
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreProductMaster/SaveAttachmentComponentWarranty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveAttachmentComponentWarranty([FromBody] SaveAttachmentComponentWarrantyList SaveAttachmentComponentWarrantyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreProductMasterServices.SaveAttachmentComponentWarranty(connString, SaveAttachmentComponentWarrantyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion
    }
}