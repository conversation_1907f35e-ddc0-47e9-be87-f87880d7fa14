﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using WorkFlow.Models;
using System.Text;
using iTextSharp.text.pdf;
using iTextSharp.text;
using System.Net;
using System.Configuration;
using LS = LogSheetExporter;
using WorkFlow.Models;
using WorkFlow.Utilities;
namespace WorkFlow.Controllers
{
    public class WorkFlowController : Controller
    {

        dynamic empty = null;
        WorkFlowEntity wrkFlowEnt = null;
        GenEntities genEnt = null;
        JObject jObj;
        JTokenReader jTR;
        static string AppPath = string.Empty;
        string exMsg = string.Empty;
        string DbName = ConfigurationManager.AppSettings.Get("DbName");
        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
        //Initial Mode
        public ActionResult CoreWorkFlow()
        {
            try
            {
                Session["RoleAccess"] = Uri.EscapeDataString(Newtonsoft.Json.JsonConvert.SerializeObject(WFCommon.InitialSetup(Convert.ToInt32(Request.Params["ObjectID"])).Data));
               // Session["RoleAccess"] = Uri.EscapeDataString(Newtonsoft.Json.JsonConvert.SerializeObject(Common.InitialSetup(1).Data));

                WFCommon.appPath = HttpContext.Request.ApplicationPath == "/" ? "" : HttpContext.Request.ApplicationPath;
                AppPath = System.Web.HttpContext.Current.Request.ApplicationPath == "/" ? "" : System.Web.HttpContext.Current.Request.ApplicationPath;
                return View("~/Views/Core/CoreWorkFlow.cshtml");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                return RedirectToAction("Error");
            }
        }
        //WorkFlow Landing Grid
        public JsonResult SelectWorkFlow(string sidx, string sord, int page, int rows)
        {
            var jsonData = empty;
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                int count = 0;
                int total = 0;
                IQueryable<WorkFlowObjects> wrkFlow = (from mstr in wrkFlowEnt.WF_WorkFlow
                                                       select new WorkFlowObjects()
                                                             {
                                                                 WorkFlow_ID = mstr.WorkFlow_ID,
                                                                 WorkFlow_Name = mstr.WorkFlow_Name,
                                                                 IsAllQueBranchFilter = (mstr.AllQueue_Filter_IsBranch == true ? "Yes" : "No")

                                                             });


                //FilterToolBar Search
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(WFCommon.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                    if(filters.rules.Count()>0)
                    wrkFlow = wrkFlow.FilterSearch<WorkFlowObjects>(filters);

                }//Advance Search
                else if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();
                    wrkFlow = wrkFlow.AdvanceSearch<WorkFlowObjects>(advnfilter);
                }
                //Sorting 
                wrkFlow = wrkFlow.OrderByField<WorkFlowObjects>(sidx, sord);

                count = wrkFlow.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in wrkFlow.AsEnumerable()
                            select new
                            {
                                q.WorkFlow_ID,
                                q.WorkFlow_Name,
                                q.IsAllQueBranchFilter,
                                WorkFlow = "<img key='" + q.WorkFlow_ID + "' WFName='" + q.WorkFlow_Name + "' src='" + AppPath + "/Content/Images/define_workflow.jpg'  class='DefineWFImg' />",
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);


        }

        //WF Step Grid
        public JsonResult WFStepsData(string sidx, string sord, int page, int rows, int id)
        {
            var jsonData = empty;
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                int count = 0;
                int total = 0;
                IEnumerable<WF_WFStepType> gnmStepType = wrkFlowEnt.WF_WFStepType;
                IEnumerable<WF_WFStepStatus> gnmStepStatus = wrkFlowEnt.WF_WFStepStatus;
                IEnumerable<WF_WFRole> gnmRoles = wrkFlowEnt.WF_WFRole.Where(a => a.WorkFlow_ID == id);
                IQueryable<WorkFlowSteps> wrkFlow = (from mstr in
                                                         wrkFlowEnt.WF_WFSteps.AsEnumerable()
                                                     join stepstatus in gnmStepStatus on mstr.WFStepStatus_ID equals stepstatus.WFStepStatus_ID
                                                     join steptype in gnmStepType on mstr.WFStepType_ID equals steptype.WFStepType_ID
                                                     where mstr.WorkFlow_ID==id
                                                     select new WorkFlowSteps()
                                                         {
                                                             WFSteps_ID = mstr.WFSteps_ID,
                                                             WFStep_Name = mstr.WFStep_Name,
                                                             WFStepType_Nm = steptype.WFStepType_Nm,
                                                             WFStepStatus_Nm = stepstatus.WFStepStatus_Nm,
                                                             WFStep_IsActive = (mstr.WFStep_IsActive == true ? "Yes" : "No")

                                                         }).AsQueryable();
                string WFStepType = "-1:--Select--;";
                for (int i = 0; i < gnmStepType.Count(); i++)
                {
                    WFStepType = WFStepType + gnmStepType.ElementAt(i).WFStepType_ID + ":" + gnmStepType.ElementAt(i).WFStepType_Nm + ";";
                }
                WFStepType = WFStepType.TrimEnd(new char[] { ';' });


                string WFStepStatus = "-1:--Select--;";
                for (int i = 0; i < gnmStepStatus.Count(); i++)
                {
                    WFStepStatus = WFStepStatus + gnmStepStatus.ElementAt(i).WFStepStatus_ID + ":" + gnmStepStatus.ElementAt(i).WFStepStatus_Nm + ";";
                }
                WFStepStatus = WFStepStatus.TrimEnd(new char[] { ';' });

                //FilterToolBar Search
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(WFCommon.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                    if(filters.rules.Count()>0)
                    wrkFlow = wrkFlow.FilterSearch<WorkFlowSteps>(filters);
                }//Advance Search
                else if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();

                    wrkFlow = wrkFlow.AdvanceSearch<WorkFlowSteps>(advnfilter);
                }
                //Sorting 
                wrkFlow = wrkFlow.OrderByField<WorkFlowSteps>(sidx, sord);

                count = wrkFlow.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    WFStepType,
                    WFStepStatus,
                    rows = (from WF in wrkFlow.AsEnumerable()
                            select new
                            {
                                edit = "<img key='" + WF.WFSteps_ID + "'  src='" + AppPath + "/Content/Images/edit.gif'  class='WFStepsEdtImg' editmode='false'/>",
                                delete = "<input key='" + WF.WFSteps_ID + "' type='checkbox' defaultchecked='' class='WFStepsDelChkImg' />",
                                WF.WFSteps_ID,
                                WF.WFStep_Name,
                                WF.WFStepType_Nm,
                                WF.WFStepStatus_Nm,
                                WF.WFStep_IsActive
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);
        }
        //Step Save
        public void WorkFlowStepsSave()
        {
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                WF_WorkFlow WF = null;
                WF_WFSteps WFSteps = null;
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int WFStepsID = 0;
                jr = new JTokenReader(jObj["WorkFlowID"]);
                jr.Read();

                int WFID = Convert.ToInt32(jr.Value);
                WF = wrkFlowEnt.WF_WorkFlow.Where(a => a.WorkFlow_ID == WFID).First();

                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepID"]);
                    jr.Read();
                    if (jr.Value.ToString() == "")
                    {
                        WFSteps = new WF_WFSteps();
                        WFSteps.WorkFlow_ID = WFID;
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepName"]);
                        jr.Read();
                        WFSteps.WFStep_Name = WFCommon.DecryptString(jr.Value.ToString());
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepType"]);
                        jr.Read();
                        WFSteps.WFStepType_ID = Convert.ToInt32(jr.Value);
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepStatus"]);
                        jr.Read();
                        WFSteps.WFStepStatus_ID = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepIsActive"]);
                        jr.Read();
                        WFSteps.WFStep_IsActive = Convert.ToBoolean(jr.Value);
                        wrkFlowEnt.WF_WFSteps.Add(WFSteps);
                    }
                    else
                    {
                        WFStepsID = Convert.ToInt32(jr.Value);
                        WFSteps = wrkFlowEnt.WF_WFSteps.Where(a => a.WFSteps_ID == WFStepsID).First();
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepName"]);
                        jr.Read();
                        WFSteps.WFStep_Name =WFCommon.DecryptString(jr.Value.ToString());
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepType"]);
                        jr.Read();
                        WFSteps.WFStepType_ID = Convert.ToInt32(jr.Value);
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepStatus"]);
                        jr.Read();
                        WFSteps.WFStepStatus_ID = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepIsActive"]);
                        jr.Read();
                        WFSteps.WFStep_IsActive = Convert.ToBoolean(jr.Value);
                    }
                }

                wrkFlowEnt.SaveChanges();
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                RedirectToAction("Error");
            }
        }

        //Delete Steps
        public string DeleteWorkFlowSteps()
        {
            try
            {
                WF_WFSteps gnmSteps = null;
                JTokenReader jr = null;
                wrkFlowEnt = new WorkFlowEntity(DbName);
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    gnmSteps = wrkFlowEnt.WF_WFSteps.Where(a => a.WFSteps_ID == id).First();
                    wrkFlowEnt.WF_WFSteps.Remove(gnmSteps);
                    wrkFlowEnt.SaveChanges();
                }
                exMsg = "Deleted Successfully";
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    exMsg += "Dependency found cannot delete the records";//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                }
            }
            return exMsg;
        }

        //---To get Step link data to set Parent Link
        public JsonResult GetStepLinkData(int WorkFlowid, int CompID)
        {
            var jsonData = empty;
            wrkFlowEnt = new WorkFlowEntity(DbName);
            IEnumerable<WF_WFStepLink> ParentWFStepLink = wrkFlowEnt.WF_WFStepLink.Where(st => st.Company_ID == CompID && st.WorkFlow_ID == WorkFlowid);
            IEnumerable<WF_WFSteps> steps = wrkFlowEnt.WF_WFSteps;
            IEnumerable<WF_WFAction> actions = wrkFlowEnt.WF_WFAction;

            jsonData = (from stlink in ParentWFStepLink
                            join frmStep in steps on stlink.FrmWFSteps_ID equals frmStep.WFSteps_ID
                            join toStep in steps on stlink.ToWFSteps_ID equals toStep.WFSteps_ID
                            join act in actions on stlink.WFAction_ID equals act.WFAction_ID
                            select new
                            {
                                ID = stlink.WFStepLink_ID,
                                Name = frmStep.WFStep_Name + "--" + act.WFAction_Name + "--" + toStep.WFStep_Name
                            });

            return Json(jsonData, JsonRequestBehavior.AllowGet);
        }

        //SteoLink Grid
        public JsonResult WFStepLinkData(string sidx, string sord, int page, int rows, int id, int companyID)
        {
            var jsonData = empty;
            try
            {               
                wrkFlowEnt = new WorkFlowEntity(DbName);
                genEnt = new GenEntities(DbName);
               // int CompanyID = Convert.ToInt32(Session["Company_ID"]);
                int ParentCompID = Convert.ToInt32(genEnt.WF_Company.Where(c => c.Company_ID == companyID).Select(c => c.Company_Parent_ID).FirstOrDefault());

                IEnumerable<WF_WFSteps> gnmWFSteps = wrkFlowEnt.WF_WFSteps.Where(a => a.WorkFlow_ID == id && a.WFStep_IsActive==true);
                //IEnumerable<WF_Company> gnmCompany = ce.WF_Company.Where(c=>c.Company_Active==true);
                IEnumerable<WF_Company> gnmCompany = LoadCompany("");
                IEnumerable<WF_WFRole> gnmRoles = wrkFlowEnt.WF_WFRole.Where(a => a.WorkFlow_ID == id && (a.WFRole_ExternalCompany_ID == companyID || a.WFRole_ExternalCompany_ID == ParentCompID));
                IEnumerable<WF_WFAction> gnmAction = wrkFlowEnt.WF_WFAction.Where(a => a.WorkFlow_ID == id); ;
                IEnumerable<WF_WorkFlow> workFlow = wrkFlowEnt.WF_WorkFlow;
                IEnumerable<WF_Object> Object = genEnt.WF_Object.Where(o => o.Object_Type == "T");

                IEnumerable<WF_Object> childObject = (from a in Object
                                                     join b in wrkFlowEnt.WF_WFChildActions.AsEnumerable() on a.Object_ID equals b.Object_ID
                                                     select a).Distinct();

                IEnumerable<WF_WFStepLink> ParentWFStepLink = wrkFlowEnt.WF_WFStepLink.Where(st => st.Company_ID == companyID);
                IEnumerable<WF_WFSteps> steps = wrkFlowEnt.WF_WFSteps;
                IEnumerable<WF_WFAction> actions = wrkFlowEnt.WF_WFAction;
                IEnumerable<WF_WFField> gnmField = wrkFlowEnt.WF_WFField.Where(a => a.WorkFlow_ID == id);

                int count = 0;
                int total = 0;
                IEnumerable<WF_WFStepType> gnmStepType = wrkFlowEnt.WF_WFStepType;
                IEnumerable<WF_WFStepStatus> gnmStepStatus = wrkFlowEnt.WF_WFStepStatus;
                IEnumerable<WF_WFStepLink> Steplink = wrkFlowEnt.WF_WFStepLink.Where(w => w.WorkFlow_ID == id && w.Company_ID == companyID);

                IQueryable<WorkFlowStepLinks> wrkFlow = (from mstr in Steplink
                                                         join Frmstep in gnmWFSteps on mstr.FrmWFSteps_ID equals Frmstep.WFSteps_ID
                                                         join Tostep in gnmWFSteps on mstr.ToWFSteps_ID equals Tostep.WFSteps_ID
                                                         join Roles in gnmRoles on mstr.Addresse_WFRole_ID equals Roles.WFRole_ID
                                                         join Actions in gnmAction on mstr.WFAction_ID equals Actions.WFAction_ID
                                                         join wfName in workFlow on mstr.InvokeParentWF_ID equals wfName.WorkFlow_ID into wfgrp
                                                         from wfFinal in wfgrp.DefaultIfEmpty(new WF_WorkFlow { WorkFlow_Name = "" })
                                                         join pst in ParentWFStepLink on mstr.InvokeParentWFLink_ID equals pst.WFStepLink_ID into stlinkgrp
                                                         from stlinkFinal in stlinkgrp.DefaultIfEmpty(new WF_WFStepLink { FrmWFSteps_ID = 0, WFAction_ID = 0, ToWFSteps_ID = 0 })
                                                         join frmStep in steps on stlinkFinal.FrmWFSteps_ID equals frmStep.WFSteps_ID into frmstgrp
                                                         from fromstepfinal in frmstgrp.DefaultIfEmpty(new WF_WFSteps { WFStep_Name = "" })
                                                         join toStep in steps on stlinkFinal.ToWFSteps_ID equals toStep.WFSteps_ID into tostgrp
                                                         from tostepfinal in tostgrp.DefaultIfEmpty(new WF_WFSteps { WFStep_Name = "" })
                                                         join act in actions on stlinkFinal.WFAction_ID equals act.WFAction_ID into actgrp
                                                         from actFinal in actgrp.DefaultIfEmpty(new WF_WFAction { WFAction_Name = "" })
                                                         join obj in childObject on mstr.InvokeChildObject_ID equals obj.Object_ID into objgrp
                                                         from objFinal in objgrp.DefaultIfEmpty(new WF_Object { Object_Description = "" })
                                                         join Field in gnmField on mstr.WFField_ID equals Field.WFField_ID into temptable
                                                         from temp in temptable.DefaultIfEmpty(new WF_WFField() { WFField_ID = 0, WorkFlowFieldName = String.Empty })
                                                         join chAction in wrkFlowEnt.WF_WFChildActions.AsEnumerable() on mstr.InvokeChildObjectAction equals chAction.ChildActions_ID into grpchAct
                                                         from finalchAct in grpchAct.DefaultIfEmpty(new WF_WFChildActions { ChildActions_ID=0, Actions_Name="" })
                                                         // where mstr.WorkFlow_ID == id && mstr.Company_ID == companyID
                                                         select new WorkFlowStepLinks()
                                                         {
                                                             WFStepLink_ID = mstr.WFStepLink_ID,
                                                             FrmStepNm = Frmstep.WFStep_Name,
                                                             ToStepNm = Tostep.WFStep_Name,
                                                             WFAction_Name = Actions.WFAction_Name,
                                                             WFRole_Name = Roles.WFRole_Name,
                                                             Addresse_Flag = mstr.Addresse_Flag == 0 ? "Role" : (mstr.Addresse_Flag == 1 ? "Individual" : "Auto"),
                                                             IsSMSSentToCustomer = mstr.IsSMSSentToCustomer == true ? "Yes" : "No",
                                                             IsEmailSentToCustomer = mstr.IsEmailSentToCustomer == true ? "Yes" : "No",
                                                             IsSMSSentToAddressee = mstr.IsSMSSentToAddressee == true ? "Yes" : "No",
                                                             IsEmailSentToAddresse = mstr.IsEmailSentToAddresse == true ? "Yes" : "No",
                                                             AutoAllocationAllowed = mstr.AutoAllocationAllowed == true ? "Yes" : "No",
                                                             IsVersionEnabled = mstr.IsVersionEnabled == true ? "Yes" : "No",
                                                             ChildObjectAction = (finalchAct == null) ? "" : finalchAct.Actions_Name,
                                                             ChildObjectActionID = (finalchAct == null) ? 0 : finalchAct.ChildActions_ID,
                                                             ParentWFID = (mstr.InvokeParentWF_ID == null) ? 0 : Convert.ToInt32(mstr.InvokeParentWF_ID),
                                                             ParentWFStepLinkID = (mstr.InvokeParentWFLink_ID == null) ? 0 : Convert.ToInt32(mstr.InvokeParentWFLink_ID),
                                                             ParentWorkFlow = wfFinal.WorkFlow_Name,
                                                             ParentWorkFlowStepLink = (mstr.InvokeParentWFLink_ID == null) ? "" : fromstepfinal.WFStep_Name + "--" + actFinal.WFAction_Name + "--" + tostepfinal.WFStep_Name,
                                                             WFChildObjectID = (mstr.InvokeChildObject_ID == null) ? 0 : Convert.ToInt32(mstr.InvokeChildObject_ID),
                                                             WFChildObject = objFinal.Object_Description,
                                                             WorkFlowField = temp.WorkFlowFieldName,
                                                             AutoCondition = mstr.AutoCondition

                                                         }).AsQueryable();

                string WorkFlowDdl = "-1:--Select--;";
                for (int i = 0; i < workFlow.Count(); i++)
                {
                    WorkFlowDdl = WorkFlowDdl + workFlow.ElementAt(i).WorkFlow_ID + ":" + workFlow.ElementAt(i).WorkFlow_Name + ";";
                }
                WorkFlowDdl = WorkFlowDdl.TrimEnd(new char[] { ';' });

                string WorkFlowSteplink = "-1:--Select--";
               

                string ChildObjectDdl = "-1:--Select--;";
                for (int i = 0; i < childObject.Count(); i++)
                {
                    ChildObjectDdl = ChildObjectDdl + childObject.ElementAt(i).Object_ID+ ":" + childObject.ElementAt(i).Object_Description + ";";
                }
                ChildObjectDdl = ChildObjectDdl.TrimEnd(new char[] { ';' });


                string ChildObjectActionDdl = "-1:--Select--";


                string WFFields = "-1:--Select--;";
                for (int i = 0; i < gnmField.Count(); i++)
                {
                    WFFields = WFFields + gnmField.ElementAt(i).WFField_ID + ":" + gnmField.ElementAt(i).WorkFlowFieldName + ";";
                }
                WFFields = WFFields.TrimEnd(new char[] { ';' });


                string WFSteps = "-1:--Select--;";
                for (int i = 0; i < gnmWFSteps.Count(); i++)
                {
                    WFSteps = WFSteps + gnmWFSteps.ElementAt(i).WFSteps_ID + ":" + gnmWFSteps.ElementAt(i).WFStep_Name + ";";
                }
                WFSteps = WFSteps.TrimEnd(new char[] { ';' });

                List<string> Company = new List<string>();
                Company.Add("<option value='-1'>--Select--</option>");
                for (int i = 0; i < gnmCompany.Count(); i++)
                {
                    Company.Add("<option value='" + gnmCompany.ElementAt(i).Company_ID + "'>" + gnmCompany.ElementAt(i).Company_Name + "</option>");
                }

                string WFRoleNames = "-1:--Select--;";
                for (int i = 0; i < gnmRoles.Count(); i++)
                {
                    WFRoleNames = WFRoleNames + gnmRoles.ElementAt(i).WFRole_ID + ":" + gnmRoles.ElementAt(i).WFRole_Name + ";";
                }
                WFRoleNames = WFRoleNames.TrimEnd(new char[] { ';' });

                string WFActions = "-1:--Select--;";
                for (int i = 0; i < gnmAction.Count(); i++)
                {
                    WFActions = WFActions + gnmAction.ElementAt(i).WFAction_ID + ":" + gnmAction.ElementAt(i).WFAction_Name + ";";
                }
                WFActions = WFActions.TrimEnd(new char[] { ';' });


                //FilterToolBar Search
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(Request.Params["filters"]).ToObject<Filters>();
                    wrkFlow = wrkFlow.FilterSearch<WorkFlowStepLinks>(filters);

                }//Advance Search
                else if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();

                    wrkFlow = wrkFlow.AdvanceSearch<WorkFlowStepLinks>(advnfilter);
                }
                //Sorting 
                wrkFlow = wrkFlow.OrderByField<WorkFlowStepLinks>(sidx, sord);

                count = wrkFlow.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;


                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    DefaultCompanyID = (count > 0) ? wrkFlowEnt.WF_WFStepLink.Where(a => a.WorkFlow_ID == id).FirstOrDefault().Company_ID.ToString() : "-1",
                    Company,
                    WFSteps,
                    WFRoleNames,
                    WFActions,
                    WorkFlowDdl,
                    WFFields,
                    WorkFlowSteplink,
                    ChildObjectDdl,
                    ChildObjectActionDdl,
                    rows = (from WF in wrkFlow.AsEnumerable()
                            select new
                            {

                                edit = "<img key='" + WF.WFStepLink_ID + "'  src='" + AppPath + "/Content/Images/edit.gif'  class='WFStepLinkEdtImg' editmode='false'/>",
                                delete = "<input key='" + WF.WFStepLink_ID + "' type='checkbox' defaultchecked='' class='WFStepLinkDelChkImg' />",
                                WF.WFStepLink_ID,
                                WF.FrmStepNm,
                                WF.ToStepNm,
                                WF.WFAction_Name,
                                WF.WFRole_Name,
                                WF.Addresse_Flag,
                                WF.IsSMSSentToCustomer,
                                WF.IsEmailSentToCustomer,
                                WF.IsSMSSentToAddressee,
                                WF.IsEmailSentToAddresse,
                                WF.AutoAllocationAllowed,
                                WF.IsVersionEnabled,
                                WF.WFChildObject,
                                WF.WFChildObjectID,
                                WF.ParentWorkFlowStepLink,
                                WF.ParentWorkFlow,
                                WF.ParentWFStepLinkID,
                                WF.ParentWFID,
                                WF.ChildObjectAction,
                                WF.WorkFlowField,
                               WF.AutoCondition,
                               WF.ChildObjectActionID
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);
        
        }
        
        //Get Company
        public JsonResult GetDefaultCompanyID(int workFlowID)
        {
            var jsonData = empty;
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                //CompanyEntities ce = new CompanyEntities();              
                
                IEnumerable<WF_Company> gnmCompany = LoadCompany("");
                List<string> Company = new List<string>();
                Company.Add("<option value='-1'>--Select--</option>");
                for (int i = 0; i < gnmCompany.Count(); i++)
                {
                    Company.Add("<option value='" + gnmCompany.ElementAt(i).Company_ID + "'>" + gnmCompany.ElementAt(i).Company_Name + "</option>");
                }
                jsonData = new
                {
                    companyID = "-1",
                    Company
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                RedirectToAction("Error");
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);
        }
        
        //StepLink Save
        public void WFStepLinkSave()
        {
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                WF_WorkFlow WF = null;
                WF_WFStepLink WFStepLink = null;
                WF_WFStepLink WFStepLinkUpdate = null;
                JTokenReader jr = null;
                int WFStepsID = 0;

                JObject jObj = JObject.Parse(Request.Params["key"]);                
                jr = new JTokenReader(jObj["WorkFlowID"]);
                jr.Read();
                int WFID = Convert.ToInt32(jr.Value);

                WF = wrkFlowEnt.WF_WorkFlow.Where(a => a.WorkFlow_ID == WFID).First();
                
                jr = new JTokenReader(jObj["CompanyID"]);
                jr.Read();
                int CompanyID = Convert.ToInt32(jr.Value);
                
                int rowcount = jObj["rows"].Count();
                string Isnew = string.Empty;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFStepLinkID"]);
                    jr.Read();
                    Isnew = jr.Value.ToString();
                    if (Isnew == "")
                    {
                        WFStepLink = new WF_WFStepLink();
                        WFStepLink = jObj["rows"].ElementAt(i).ToObject<WF_WFStepLink>();
                        WFStepLink.Company_ID = CompanyID;
                        WFStepLink.WorkFlow_ID = WFID;
                        WFStepLink.InvokeChildObject_ID = (WFStepLink.InvokeChildObject_ID == 0) ? null : WFStepLink.InvokeChildObject_ID;
                        WFStepLink.InvokeParentWF_ID = (WFStepLink.InvokeParentWF_ID == 0) ? null : WFStepLink.InvokeParentWF_ID;
                        WFStepLink.InvokeParentWFLink_ID = (WFStepLink.InvokeParentWFLink_ID == 0) ? null : WFStepLink.InvokeParentWFLink_ID;
                        WFStepLink.WFField_ID = (WFStepLink.WFField_ID == 0) ? null : WFStepLink.WFField_ID;
                        wrkFlowEnt.WF_WFStepLink.Add(WFStepLink);

                    }
                    else
                    {
                        WFStepsID = Convert.ToInt32(jr.Value);
                        WFStepLink = wrkFlowEnt.WF_WFStepLink.Where(a => a.WFStepLink_ID == WFStepsID).First();
                        WFStepLinkUpdate = jObj["rows"].ElementAt(i).ToObject<WF_WFStepLink>();
                        WFStepLink.InvokeChildObject_ID = (WFStepLinkUpdate.InvokeChildObject_ID == 0) ? null : WFStepLinkUpdate.InvokeChildObject_ID;
                        WFStepLink.InvokeParentWF_ID = (WFStepLinkUpdate.InvokeParentWF_ID == 0) ? null : WFStepLinkUpdate.InvokeParentWF_ID;
                        WFStepLink.InvokeParentWFLink_ID = (WFStepLinkUpdate.InvokeParentWFLink_ID == 0) ? null : WFStepLinkUpdate.InvokeParentWFLink_ID;
                        WFStepLink.Addresse_Flag = WFStepLinkUpdate.Addresse_Flag;

                        WFStepLink.Addresse_WFRole_ID = WFStepLinkUpdate.Addresse_WFRole_ID;
                        WFStepLink.AutoAllocationAllowed = WFStepLinkUpdate.AutoAllocationAllowed;
                        WFStepLink.AutoCondition = WFCommon.DecryptString(WFStepLinkUpdate.AutoCondition);
                        WFStepLink.FrmWFSteps_ID = WFStepLinkUpdate.FrmWFSteps_ID;
                        WFStepLink.InvokeChildObjectAction = WFStepLinkUpdate.InvokeChildObjectAction;
                        WFStepLink.IsEmailSentToAddresse = WFStepLinkUpdate.IsEmailSentToAddresse;
                        WFStepLink.IsEmailSentToCustomer = WFStepLinkUpdate.IsEmailSentToCustomer;
                        WFStepLink.IsSMSSentToAddressee = WFStepLinkUpdate.IsSMSSentToAddressee;
                        WFStepLink.IsSMSSentToCustomer = WFStepLinkUpdate.IsSMSSentToCustomer;
                        WFStepLink.IsVersionEnabled = WFStepLinkUpdate.IsVersionEnabled;
                        WFStepLink.ToWFSteps_ID = WFStepLinkUpdate.ToWFSteps_ID;
                        WFStepLink.WFAction_ID = WFStepLinkUpdate.WFAction_ID;
                        WFStepLink.WFField_ID = (WFStepLinkUpdate.WFField_ID == 0) ? null : WFStepLinkUpdate.WFField_ID;
                    }
                }

                wrkFlowEnt.SaveChanges();
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                RedirectToAction("Error");
            }
        }
        
        //StepLink Delete
        public string WFStepLinkDelete()
        {
            try
            {
                WF_WFStepLink gnmStepLink = null;
                JTokenReader jr = null;
                wrkFlowEnt = new WorkFlowEntity(DbName);
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    gnmStepLink = wrkFlowEnt.WF_WFStepLink.Where(a => a.WFStepLink_ID == id).First();
                    wrkFlowEnt.WF_WFStepLink.Remove(gnmStepLink);
                }
                wrkFlowEnt.SaveChanges();
                exMsg = "Deleted Successfully";
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    exMsg += "Dependency found cannot delete the records";//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                }
            }
            return exMsg;
        }
        
        //Actions Grid
        public JsonResult WFActions(string sidx, string sord, int page, int rows, int id)
        {
            var jsonData = empty;
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                int count = 0;
                int total = 0;
                IQueryable<WF_WFAction> wrkFlow = wrkFlowEnt.WF_WFAction.Where(a => a.WorkFlow_ID == id).AsQueryable();


                //FilterToolBar Search
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(Request.Params["filters"]).ToObject<Filters>();
                    wrkFlow = wrkFlow.FilterSearch<WF_WFAction>(filters);

                }//Advance Search
                else if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();

                    wrkFlow = wrkFlow.AdvanceSearch<WF_WFAction>(advnfilter);
                }
                //Sorting 
                wrkFlow = wrkFlow.OrderByField<WF_WFAction>(sidx, sord);

                count = wrkFlow.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;


                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from WF in wrkFlow.AsEnumerable()
                            select new
                            {
                                edit = "<img key='" + WF.WFAction_ID + "'  src='" +AppPath + "/Content/Images/edit.gif'  class='WFActionEdtImg' editmode='false'/>",
                                delete = "<input key='" + WF.WFAction_ID + "' type='checkbox' defaultchecked='' class='WFActionDelChkImg' />",
                                WF.WFAction_ID,
                                WF.WFAction_Name,
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);

        }
        //Actions Save
        public void WFActionsSave()
        {
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                WF_WorkFlow WF = null;
                WF_WFAction WFAction = null;
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int WFActionID = 0;
                jr = new JTokenReader(jObj["WorkFlowID"]);
                jr.Read();

                int WFID = Convert.ToInt32(jr.Value);
                WF = wrkFlowEnt.WF_WorkFlow.Where(a => a.WorkFlow_ID == WFID).First();

                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFActionID"]);
                    jr.Read();
                    if (jr.Value.ToString() == "")
                    {
                        WFAction = new WF_WFAction();
                        WFAction.WorkFlow_ID = WFID;
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFActionNm"]);
                        jr.Read();
                        WFAction.WFAction_Name =WFCommon.DecryptString(jr.Value.ToString());
                        wrkFlowEnt.WF_WFAction.Add(WFAction);

                    }
                    else
                    {
                        WFActionID = Convert.ToInt32(jr.Value);
                        WFAction = wrkFlowEnt.WF_WFAction.Where(a => a.WFAction_ID == WFActionID).First();
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["WFActionNm"]);
                        jr.Read();
                        WFAction.WFAction_Name = WFCommon.DecryptString(jr.Value.ToString());
                    }
                }

                wrkFlowEnt.SaveChanges();
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                RedirectToAction("Error");
            }
        }
        //Actions Delete
        public string WFActionsDelete()
        {
            try
            {
                WF_WFAction gnmAction = null;
                JTokenReader jr = null;
                wrkFlowEnt = new WorkFlowEntity(DbName);
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    gnmAction = wrkFlowEnt.WF_WFAction.Where(a => a.WFAction_ID == id).First();
                    wrkFlowEnt.WF_WFAction.Remove(gnmAction);
                }
                wrkFlowEnt.SaveChanges();
                exMsg = "Deleted Successfully";
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    exMsg += "Dependency found cannot delete the records";//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                }
            }
            return exMsg;
        }
        
        //Roles Grid
        public JsonResult SelectWFRole(string sidx, string sord, int page, int rows, int id)
        {
            var jsonData = empty;
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                genEnt = new GenEntities(DbName);
                int count = 0;
                int total = 0;

                int CompanyID = Convert.ToInt32(Session["Company_ID"]);
                int ParentCompID =Convert.ToInt32(genEnt.WF_Company.Where(c => c.Company_ID == CompanyID).Select(c => c.Company_Parent_ID).FirstOrDefault());

                IEnumerable<WF_WFRole> WorkFlowRole = wrkFlowEnt.WF_WFRole.Where(i => i.WorkFlow_ID == id && (i.WFRole_ExternalCompany_ID == CompanyID || i.WFRole_ExternalCompany_ID == ParentCompID));
                IEnumerable<WF_Company> Company = genEnt.WF_Company;
                //IEnumerable<WF_Company> gnmCompany = LoadCompany("");
                IEnumerable<WF_Company> gnmCompany = genEnt.WF_Company.Where(c => c.Company_ID == ParentCompID);

                string CompanyDDl = "-1:--Select--;";
                for (int i = 0; i < gnmCompany.Count(); i++)
                {
                    CompanyDDl = CompanyDDl + gnmCompany.ElementAt(i).Company_ID + ":" + gnmCompany.ElementAt(i).Company_Name + ";";
                }

                CompanyDDl = CompanyDDl.TrimEnd(new char[] { ';' });

                IQueryable<WorkFlowRole> wrkFlow = (from mstr in WorkFlowRole
                                                    join comp in Company on mstr.WFRole_ExternalCompany_ID equals comp.Company_ID into CompData
                                                    from final in CompData.DefaultIfEmpty(new WF_Company{ Company_ID=0, Company_Name=""})                                                         
                                                    select new WorkFlowRole()
                                                       {
                                                           WFRole_ID=mstr.WFRole_ID,
                                                            WFRole_Name=mstr.WFRole_Name,
                                                             WfRole_IsAdmin=(mstr.WfRole_IsAdmin == true ? "Yes" : "No"),
                                                           WfRole_AutoAllocationAllowed = (mstr.WfRole_AutoAllocationAllowed == true ? "Yes" : "No"),
                                                           WFRole_IsExternal = (mstr.WFRole_IsRoleExternal == true ? "Yes" : "No"),
                                                           WFRole_ExternalCompany_ID = (mstr.WFRole_ExternalCompany_ID == null) ? 0 : Convert.ToInt32(mstr.WFRole_ExternalCompany_ID),
                                                           WFRole_ExternalCompanyName=final.Company_Name
                                                       }).AsQueryable();


                //FilterToolBar Search
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(Request.Params["filters"]).ToObject<Filters>();
                    wrkFlow = wrkFlow.FilterSearch<WorkFlowRole>(filters);

                }//Advance Search
                else if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();

                    wrkFlow = wrkFlow.AdvanceSearch<WorkFlowRole>(advnfilter);
                }
                //Sorting 
                wrkFlow = wrkFlow.OrderByField<WorkFlowRole>(sidx, sord);

                count = wrkFlow.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;


                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    CompanyDDl,
                    rows = (from WF in wrkFlow.AsEnumerable()
                            select new
                            {

                                edit = "<img key='" + WF.WFRole_ID + "'  src='" + AppPath + "/Content/Images/edit.gif'  class='WFRolesEdtImg' editmode='false'/>",
                                delete = "<input key='" + WF.WFRole_ID + "' type='checkbox' defaultchecked='' class='WFRolesDelChkImg' />",
                                WF.WFRole_ID,
                                WF.WFRole_Name,
                                WF.WfRole_IsAdmin,
                                WF.WfRole_AutoAllocationAllowed ,
                                WFRole_IsExternal=WF.WFRole_IsExternal,
                                WFRole_ExternalCompany_ID = WF.WFRole_ExternalCompany_ID,
                                WFRole_ExternalCompanyName = WF.WFRole_ExternalCompanyName,
                                ExtCompID=WF.WFRole_ExternalCompany_ID,
                                RoleUser = "<img key='" + WF.WFRole_ID + "' RoleName='" + WF.WFRole_Name + "'  src='" + AppPath + "/Content/Images/attach_user.jpg'  class='RoleUserImg' />",
             
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);
        }
        //Roles Save
        public void SaveWFRoles()
        {
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                WF_WFRole WFRole = null;
                WF_WFRole WFRoleUpd = null;
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int rowcount = jObj["rows"].Count();
                int compid = Convert.ToInt32(Session["Company_ID"].ToString());
                for (int i = 0; i < rowcount; i++)
                {

                    WFRole = jObj["rows"].ElementAt(i).ToObject<WF_WFRole>();
                    WFRole.WFRole_Name = WFCommon.DecryptString(WFRole.WFRole_Name);
                    WFRole.WFRole_ExternalCompany_ID = (WFRole.WFRole_ExternalCompany_ID == 0) ? compid : WFRole.WFRole_ExternalCompany_ID;
                    if (WFRole.WFRole_ID == 0)
                    {
                        wrkFlowEnt.WF_WFRole.Add(WFRole);
                    }
                    else
                    {
                        WFRoleUpd = wrkFlowEnt.WF_WFRole.Where(a => a.WFRole_ID == WFRole.WFRole_ID).FirstOrDefault();
                        WFRoleUpd.WFRole_Name = WFRole.WFRole_Name;
                        WFRoleUpd.WfRole_IsAdmin = WFRole.WfRole_IsAdmin;
                        WFRoleUpd.WfRole_AutoAllocationAllowed = WFRole.WfRole_AutoAllocationAllowed;
                        WFRoleUpd.WFRole_IsRoleExternal = WFRole.WFRole_IsRoleExternal;
                        WFRoleUpd.WFRole_ExternalCompany_ID = WFRole.WFRole_ExternalCompany_ID;
                    }

                }

                wrkFlowEnt.SaveChanges();
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                RedirectToAction("Error");
            }
        }
        //Role Delete
        public string WFRolesDelete()
        {
            try
            {
                WF_WFRole gnmRole = null;
                JTokenReader jr = null;
                wrkFlowEnt = new WorkFlowEntity(DbName);
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    gnmRole = wrkFlowEnt.WF_WFRole.Where(a => a.WFRole_ID == id).First();
                    wrkFlowEnt.WF_WFRole.Remove(gnmRole);
                }
                wrkFlowEnt.SaveChanges();
                exMsg = "Deleted Successfully";
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    exMsg += "Dependency found cannot delete the records";//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                }
            }
            return exMsg;
        }
        //User Roles Grid
        public JsonResult WFUserRoles(string sidx, string sord, int page, int rows, int id, int CompID)
        {
            var jsonData = empty;
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                genEnt = new GenEntities(DbName);

                int count = 0;
                int total = 0;
                //int CompID = 41;// Convert.ToInt32(Session["Company_ID"]);
                IEnumerable<WF_User> gnmUser = genEnt.WF_User.Where(u => u.Company_ID == CompID && u.User_IsActive == true);
                IQueryable<WorkFlowRoleUsers> wrkFlow = (from mstr in wrkFlowEnt.WF_WFRoleUser.AsEnumerable()
                                                      join user in gnmUser on mstr.UserID equals user.User_ID
                                                      where mstr.WFRole_ID == id
                                                         select new WorkFlowRoleUsers
                                                      {
                                                          WFRoleUser_ID = mstr.WFRoleUser_ID,
                                                          User_Name = user.User_Name,
                                                          ApprovalLimit = mstr.ApprovalLimit
                                                      }).AsQueryable();
                 string WFUsers = "-1:--Select--;";
                for (int i = 0; i < gnmUser.Count(); i++)
                {
                    WFUsers = WFUsers + gnmUser.ElementAt(i).User_ID + ":" + gnmUser.ElementAt(i).User_Name + ";";
                }
                WFUsers = WFUsers.TrimEnd(new char[] { ';' });
                                     


                //FilterToolBar Search
                if (Request.Params["_search"] == "true")
                {
                    Filters filters = JObject.Parse(Request.Params["filters"]).ToObject<Filters>();
                    wrkFlow = wrkFlow.FilterSearch<WorkFlowRoleUsers>(filters);

                }//Advance Search
                else if (Request.Params["advnce"] == "true")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();

                    wrkFlow = wrkFlow.AdvanceSearch<WorkFlowRoleUsers>(advnfilter);
                }
                //Sorting 
                wrkFlow = wrkFlow.OrderByField<WorkFlowRoleUsers>(sidx, sord);

                count = wrkFlow.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;


                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    WFUsers,
                    rows = (from WF in wrkFlow.AsEnumerable()
                            select new
                            {
                                edit = "<img key='" + WF.WFRoleUser_ID + "'  src='" + AppPath + "/Content/Images/edit.gif'  class='WFUserRoleEdtImg' editmode='false'/>",
                                delete = "<input key='" + WF.WFRoleUser_ID + "' type='checkbox' defaultchecked='' class='WFUserRoleDelChkImg' />",
                                WF.WFRoleUser_ID,
                                WF.User_Name,
                                WF.ApprovalLimit,
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet); 
        }
        //User Role Save
        public void WFUserRoleSave()
        {
            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                WF_WFRoleUser WFRoleUser = null;
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int RoleUserID = 0;
                string IsNew = string.Empty;
                jr = new JTokenReader(jObj["RoleID"]);
                jr.Read();

                int RoleID = Convert.ToInt32(jr.Value);

                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["RoleUserID"]);
                    jr.Read();
                    IsNew = jr.Value.ToString();
                    if (IsNew == "")
                    {
                        WFRoleUser = new WF_WFRoleUser();
                    }
                    else
                    {
                        RoleUserID = Convert.ToInt32(jr.Value);
                        WFRoleUser = wrkFlowEnt.WF_WFRoleUser.Where(a => a.WFRoleUser_ID == RoleUserID).First();
                    }
                    WFRoleUser.WFRole_ID = RoleID;
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["UserID"]);
                    jr.Read();
                    WFRoleUser.UserID = Convert.ToInt32(jr.Value);
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["AprvlLmt"]);
                    jr.Read();
                    WFRoleUser.ApprovalLimit = Convert.ToInt32(jr.Value);
                    if (IsNew == "")
                    {
                        wrkFlowEnt.WF_WFRoleUser.Add(WFRoleUser);

                    }
                }

                wrkFlowEnt.SaveChanges();
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                RedirectToAction("Error");
            }
        }
        //User Role Delete
        public string WFUserRoleDelete()
        {
            try
            {
                WF_WFRoleUser gnmRole = null;
                JTokenReader jr = null;
                wrkFlowEnt = new WorkFlowEntity(DbName);
                JObject jObj = JObject.Parse(Request.Params["key"]);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    gnmRole = wrkFlowEnt.WF_WFRoleUser.Where(a => a.WFRoleUser_ID == id).First();
                    wrkFlowEnt.WF_WFRoleUser.Remove(gnmRole);
                }
                wrkFlowEnt.SaveChanges();
                exMsg = "Deleted Successfully";
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    exMsg += "Dependency found cannot delete the records";//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                }
            }
            return exMsg;
        }

        //TO Check If Description Exists
        public string CheckRoleNameExists(int id, string name)
        {
            string data = string.Empty;

            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                int Company_ID = Convert.ToInt32(Session["Company_ID"]);
                int primID = Convert.ToInt32(Request.Params["primID"]);
                data = wrkFlowEnt.WF_WFRole.Where(a => a.WorkFlow_ID == id && a.WFRole_Name.ToLower() == name.ToLower() && a.WFRole_ExternalCompany_ID == Company_ID && a.WFRole_ID != primID).Count() > 0 ? "true" : "false";
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return data;
        }
        //TO Check If Description Exists
        public string CheckActionExists(int id, string name)
        {
            string data = string.Empty;

            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                int Company_ID = Convert.ToInt32(Session["Company_ID"]);
                int primID = Convert.ToInt32(Request.Params["primID"]);
                data = wrkFlowEnt.WF_WFAction.Where(a => a.WorkFlow_ID == id && a.WFAction_Name.ToLower() == name.ToLower() && a.WFAction_ID != primID).Count() > 0 ? "true" : "false";
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return data;
        }
        //TO Check If Description Exists
        public string CheckStepExists(int id, string name)
        {
            string data = string.Empty;

            try
            {
                wrkFlowEnt = new WorkFlowEntity(DbName);
                int Company_ID = Convert.ToInt32(Session["Company_ID"]);
                int primID = Convert.ToInt32(Request.Params["primID"]);
                data = wrkFlowEnt.WF_WFSteps.Where(a => a.WorkFlow_ID == id && a.WFStep_Name.ToLower() == name.ToLower() && a.WFSteps_ID != primID).Count() > 0 ? "true" : "false";
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return data;
        }

        #region:::  To validate selected roles   :::
        /// <summary>
        ///To validate selected roles 
        /// </summary>
        /// <returns>...</returns>
        public bool validateUserRole(int UserID, int RoleID)
        {
            bool isduplicate = false;
            try
            {
                isduplicate = (wrkFlowEnt.WF_WFRoleUser.Where(urid => urid.WFRole_ID == RoleID && urid.UserID == UserID).Count()) > 0 ? true : false;
            }
            catch (Exception ex)
            {
                isduplicate = false;
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return isduplicate;
        }
        #endregion

        public IEnumerable<WF_Company> LoadCompany(string CompanyType)
        {
            wrkFlowEnt = new WorkFlowEntity(DbName);
            genEnt = new GenEntities(DbName);

            IEnumerable<WF_Company> Company = null;
            try
            {
                Company = genEnt.WF_Company.Where(i => i.Company_Active == true && i.Company_Type.ToLower() != CompanyType.ToLower()).OrderBy(i => i.Company_Name);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return Company;
        }


        public JsonResult getChildActions(int objectID) {
            var jsonData = empty;
            wrkFlowEnt = new WorkFlowEntity(DbName);
            try {
                jsonData = (from a in wrkFlowEnt.WF_WFChildActions.AsEnumerable()
                            where a.Object_ID == objectID
                            select new
                            {
                                ID = a.ChildActions_ID,
                                Name = a.Actions_Name
                            });
                                                      
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return Json(jsonData, JsonRequestBehavior.AllowGet);
        }
    }
}
