﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AzureFunctionsVersion>v3</AzureFunctionsVersion>
	<_FunctionsSkipCleanOutput>true</_FunctionsSkipCleanOutput>
	<SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>
  <ItemGroup>
	<PackageReference Include="AWSSDK.Core" Version="3.7.302.3" />
	<PackageReference Include="AWSSDK.S3" Version="3.7.305.19" />
	<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.Management.Fluent" Version="1.38.1" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="3.0.11" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HCLSoftware_DPC_API_Standalone\HCLSoftware_AMERP_API_Standalone.csproj" />
    <ProjectReference Include="..\SharedAPIClassLibrary_DC\SharedAPIClassLibrary_AMERP.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
</Project>
