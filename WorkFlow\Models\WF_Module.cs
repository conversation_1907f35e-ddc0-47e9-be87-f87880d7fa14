//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_Module
    {
        public WF_Module()
        {
            this.GNM_Menu = new HashSet<WF_Menu>();
            this.GNM_ModuleLocale = new HashSet<WF_ModuleLocale>();
        }
    
        public int Module_ID { get; set; }
        public string Module_Description { get; set; }
        public bool Module_IsActive { get; set; }
        public byte Module_SortOrder { get; set; }
        public string Module_IconName { get; set; }
    
        public virtual ICollection<WF_Menu> GNM_Menu { get; set; }
        public virtual ICollection<WF_ModuleLocale> GNM_ModuleLocale { get; set; }
    }
}
