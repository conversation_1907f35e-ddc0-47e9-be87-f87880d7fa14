using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class BCryptController : ControllerBase
    {
        private readonly IBCryptService _bcryptService;
        private readonly ILogger<BCryptController> _logger;

        public BCryptController(IBCryptService bcryptService, ILogger<BCryptController> logger)
        {
            _bcryptService = bcryptService;
            _logger = logger;
        }

        /// <summary>
        /// Generates a DMS password hash using BCrypt with AMP_SP salt
        /// </summary>
        /// <param name="request">The password generation request</param>
        /// <returns>BCrypt hashed password</returns>
        /// <response code="200">Returns the hashed password</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("generate-dms-password")]
        [ProducesResponseType(typeof(BCryptResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<BCryptResponse<string>>> GenerateDMSPassword([FromBody] GenerateDMSPasswordRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/bcrypt/generate-dms-password");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _bcryptService.GenerateDMSPasswordAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating DMS password hash");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while generating DMS password hash");
            }
        }

        /// <summary>
        /// Checks if a password matches the stored hash
        /// </summary>
        /// <param name="request">The password check request</param>
        /// <returns>True if password matches</returns>
        /// <response code="200">Returns whether password matches</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-password")]
        [ProducesResponseType(typeof(BCryptResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<BCryptResponse<bool>>> CheckPassword([FromBody] CheckPasswordRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/bcrypt/check-password");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _bcryptService.CheckPasswordWithDBAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking password");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking password");
            }
        }
    }
}
