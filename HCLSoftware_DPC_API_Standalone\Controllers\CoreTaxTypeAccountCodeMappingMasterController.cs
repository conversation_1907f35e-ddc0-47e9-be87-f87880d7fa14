﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreTaxTypeAccountCodeMappingMasterController : ApiController
    {
        #region SelAllTaxMapping vinay n 27/9/24
        /// <summary>
        /// SelAllTaxMapping
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxTypeAccountCodeMappingMaster/SelAllTaxMapping")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllTaxMapping([FromBody] SelAllTaxTypeAccountCodeMappingList obj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreTaxTypeAccountCodeMappingMasterServices.SelAllTaxMapping(obj, connstring, LogException, _search, filters, advnceFilters, advnce, sidx, sord, page, rows);


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region Save vinay n 27/9/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxTypeAccountCodeMappingMaster/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public void Save([FromBody] SaveTaxTypeAccountCodeMappingList obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                CoreTaxTypeAccountCodeMappingMasterServices.Save(obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }


        }
        #endregion
        #region Delete vinay n 27/9/24
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxTypeAccountCodeMappingMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteTaxTypeAccountCodeMappingList obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTaxTypeAccountCodeMappingMasterServices.Delete(obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion
        #region CheckMapping vinay n 27/9/24
        /// <summary>
        /// CheckMapping
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxTypeAccountCodeMappingMaster/CheckMapping")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckMapping([FromBody] CheckMappingTaxTypeAccountCodeMappingList obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTaxTypeAccountCodeMappingMasterServices.CheckMapping(obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Export
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreTaxTypeAccountCodeMappingMaster/Export")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelAllTaxTypeAccountCodeMappingList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





            try
            {


                object Response = await CoreTaxTypeAccountCodeMappingMasterServices.Export(ExportObj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion
    }
}