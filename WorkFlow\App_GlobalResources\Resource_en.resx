﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AbandonDelayReason" xml:space="preserve">
    <value>Abandon &amp; Delay Reason</value>
    <comment>Label</comment>
  </data>
  <data name="Accept" xml:space="preserve">
    <value>Accept</value>
    <comment>Label</comment>
  </data>
  <data name="AccountNumber" xml:space="preserve">
    <value>Account Number</value>
    <comment>Label</comment>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
    <comment>Label</comment>
  </data>
  <data name="ActionBy" xml:space="preserve">
    <value>Action By</value>
    <comment>Label</comment>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>Action Date</value>
    <comment>Label</comment>
  </data>
  <data name="ActionForNextService" xml:space="preserve">
    <value>Action For Next Service</value>
    <comment>Label</comment>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>Action Name</value>
    <comment>Label</comment>
  </data>
  <data name="ActionRemarks" xml:space="preserve">
    <value>Action Remarks</value>
    <comment>Label</comment>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
    <comment>Label</comment>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Is Active?</value>
    <comment>Label</comment>
  </data>
  <data name="ActiveFrom" xml:space="preserve">
    <value>Active From</value>
    <comment>Label</comment>
  </data>
  <data name="ActiveFromdatecannotbegreaterthanActiveTodate" xml:space="preserve">
    <value>Active from date cannot be greater than active to date</value>
    <comment>Message</comment>
  </data>
  <data name="ActiveTo" xml:space="preserve">
    <value>Active To</value>
    <comment>Label</comment>
  </data>
  <data name="ActualHours" xml:space="preserve">
    <value>Actual Hours</value>
    <comment>Label</comment>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
    <comment>Label</comment>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>Add Action</value>
    <comment>Label</comment>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>Add Branch</value>
    <comment>Label</comment>
  </data>
  <data name="AddBranchTaxDetails" xml:space="preserve">
    <value>Add Branch Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddBrands" xml:space="preserve">
    <value>Add Brands</value>
    <comment>Label</comment>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>Add Company</value>
    <comment>Label</comment>
  </data>
  <data name="AddCompanyRelation" xml:space="preserve">
    <value>Add Company Relation</value>
    <comment>Label</comment>
  </data>
  <data name="AddCompanyTaxDetails" xml:space="preserve">
    <value>Add Company Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddCompanyTerms" xml:space="preserve">
    <value>Add Company Terms</value>
    <comment>Label</comment>
  </data>
  <data name="addcomponentdetails" xml:space="preserve">
    <value>Add Component Details</value>
    <comment>Label</comment>
  </data>
  <data name="addcustomer" xml:space="preserve">
    <value>Add Customer</value>
    <comment>Label</comment>
  </data>
  <data name="AddCustomerQuotation" xml:space="preserve">
    <value>Add Customer Quotation</value>
    <comment>Label</comment>
  </data>
  <data name="AddEmployee" xml:space="preserve">
    <value>Add Employee</value>
    <comment>Label</comment>
  </data>
  <data name="AddFilter" xml:space="preserve">
    <value>Add Filter</value>
    <comment>Label</comment>
  </data>
  <data name="addfreestock" xml:space="preserve">
    <value>Add Parts Free Stock</value>
    <comment>Label</comment>
  </data>
  <data name="AddFunctionGroup" xml:space="preserve">
    <value>Add Function Group</value>
    <comment>Label</comment>
  </data>
  <data name="AddJobCard" xml:space="preserve">
    <value>Add Job Card</value>
    <comment>Label</comment>
  </data>
  <data name="AddMaster" xml:space="preserve">
    <value>Add Master</value>
    <comment>Label</comment>
  </data>
  <data name="addmodel" xml:space="preserve">
    <value>Add Model</value>
    <comment>Label</comment>
  </data>
  <data name="addnewpart" xml:space="preserve">
    <value>Add New Part</value>
    <comment>Label</comment>
  </data>
  <data name="addoperation" xml:space="preserve">
    <value>Add Operation Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddOperationEmployeeDetails" xml:space="preserve">
    <value>Add Operation Employee Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddPart" xml:space="preserve">
    <value>Add Part</value>
    <comment>Label</comment>
  </data>
  <data name="addpartprice" xml:space="preserve">
    <value>Add Part Price</value>
    <comment>Label</comment>
  </data>
  <data name="AddParty" xml:space="preserve">
    <value>Add Party</value>
    <comment>Label</comment>
  </data>
  <data name="AddPrefixSuffix" xml:space="preserve">
    <value>Add Prefix Suffix</value>
    <comment>Label</comment>
  </data>
  <data name="addproduct" xml:space="preserve">
    <value>Add Product</value>
    <comment>Label</comment>
  </data>
  <data name="addproductdetail" xml:space="preserve">
    <value>Add Product Detail</value>
    <comment>Label</comment>
  </data>
  <data name="addproducttype" xml:space="preserve">
    <value>Add Product Type</value>
    <comment>Label</comment>
  </data>
  <data name="addproducttypedetails" xml:space="preserve">
    <value>Add Part Product Type Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddRequest" xml:space="preserve">
    <value>Add Request</value>
    <comment>Label</comment>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
    <comment>Label</comment>
  </data>
  <data name="Address1" xml:space="preserve">
    <value>Address Line 1</value>
    <comment>Label</comment>
  </data>
  <data name="Address2" xml:space="preserve">
    <value>Address Line 2</value>
    <comment>Label</comment>
  </data>
  <data name="Address3" xml:space="preserve">
    <value>Address Line 3</value>
    <comment>Label</comment>
  </data>
  <data name="AddresseFlag" xml:space="preserve">
    <value>Addresse Flag</value>
    <comment>Label</comment>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>Add Role</value>
    <comment>Label</comment>
  </data>
  <data name="addServiceCharge" xml:space="preserve">
    <value>Add Service Charge</value>
    <comment>Label</comment>
  </data>
  <data name="addServiceChargeDetails" xml:space="preserve">
    <value>Add Service Charge Details</value>
    <comment>Label</comment>
  </data>
  <data name="addservicecharges" xml:space="preserve">
    <value>Add Service Charges</value>
    <comment>Label</comment>
  </data>
  <data name="AddServiceType" xml:space="preserve">
    <value>Add Service Type</value>
    <comment>Label</comment>
  </data>
  <data name="addsiteaddress" xml:space="preserve">
    <value>Add Site Address</value>
    <comment>Label</comment>
  </data>
  <data name="AddSkills" xml:space="preserve">
    <value>Add Skills</value>
    <comment>Label</comment>
  </data>
  <data name="AddSpecialization" xml:space="preserve">
    <value>Add Specialization</value>
    <comment>Label</comment>
  </data>
  <data name="AddStep" xml:space="preserve">
    <value>Add Step</value>
    <comment>Label</comment>
  </data>
  <data name="AddStepLink" xml:space="preserve">
    <value>Add Step Link</value>
    <comment>Label</comment>
  </data>
  <data name="AddSundry" xml:space="preserve">
    <value>Add Sundry</value>
    <comment>Label</comment>
  </data>
  <data name="AddTaxCode" xml:space="preserve">
    <value>Add Tax Code</value>
    <comment>Label</comment>
  </data>
  <data name="addtaxstructure" xml:space="preserve">
    <value>Add tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="addtaxtstructuredetails" xml:space="preserve">
    <value>Add Tax Structure Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>Add User</value>
    <comment>Label</comment>
  </data>
  <data name="addwarrantydetails" xml:space="preserve">
    <value>Add Warranty Details</value>
    <comment>Label</comment>
  </data>
  <data name="AddWorkDetails" xml:space="preserve">
    <value>Add Work Details</value>
    <comment>Label</comment>
  </data>
  <data name="advancesearch" xml:space="preserve">
    <value>Advance Search</value>
    <comment>Label</comment>
  </data>
  <data name="all" xml:space="preserve">
    <value>All</value>
    <comment>Label</comment>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>Allocate</value>
    <comment>Label</comment>
  </data>
  <data name="AllocatedHours" xml:space="preserve">
    <value>Allocated Hours</value>
    <comment>Label</comment>
  </data>
  <data name="AlloctedSuccessfully" xml:space="preserve">
    <value>Allocated Successfully</value>
    <comment>Message</comment>
  </data>
  <data name="AllQueue" xml:space="preserve">
    <value>All Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
    <comment>Label</comment>
  </data>
  <data name="AmountBlank" xml:space="preserve">
    <value>Amount is  blank</value>
    <comment>Message</comment>
  </data>
  <data name="ApprovalLimit" xml:space="preserve">
    <value>Approval Limit</value>
    <comment>Label</comment>
  </data>
  <data name="April" xml:space="preserve">
    <value>Apr</value>
    <comment>Label</comment>
  </data>
  <data name="Areyousurewanttocancel" xml:space="preserve">
    <value>Are you sure you want to cancel?</value>
    <comment>Message</comment>
  </data>
  <data name="Areyousurewanttodelete" xml:space="preserve">
    <value>Are you sure you want to delete?</value>
    <comment>Message</comment>
  </data>
  <data name="AreyousureyouwanttoAbandontheRequest" xml:space="preserve">
    <value>Are you sure you want to abandon the request</value>
    <comment>Message</comment>
  </data>
  <data name="areyousureyouwanttomovetheevent" xml:space="preserve">
    <value>Are you sure you want to move the event?</value>
    <comment>Message</comment>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>Assigned To</value>
    <comment>Label</comment>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>Assign To</value>
    <comment>Label</comment>
  </data>
  <data name="August" xml:space="preserve">
    <value>Aug</value>
    <comment>Label</comment>
  </data>
  <data name="autoallocate" xml:space="preserve">
    <value>Auto Allocate</value>
    <comment>Label</comment>
  </data>
  <data name="AutoAllocationAllowed" xml:space="preserve">
    <value>Auto Allocation Allowed</value>
    <comment>Label</comment>
  </data>
  <data name="AverageResolutionTime" xml:space="preserve">
    <value>Average Resolution Time</value>
    <comment>Label</comment>
  </data>
  <data name="averageresolutiontimeyearwise" xml:space="preserve">
    <value>Average Resolution Time - Year Wise</value>
    <comment>Label</comment>
  </data>
  <data name="AverageTime" xml:space="preserve">
    <value>Average Time</value>
    <comment>Label</comment>
  </data>
  <data name="BankName" xml:space="preserve">
    <value>Bank Name</value>
    <comment>Label</comment>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Branch</value>
    <comment>Label</comment>
  </data>
  <data name="BranchAssociation" xml:space="preserve">
    <value>Branch Association</value>
    <comment>Label</comment>
  </data>
  <data name="branchdetail" xml:space="preserve">
    <value>Branch Detail</value>
    <comment>Label</comment>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>Branch Name</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateBranchName" xml:space="preserve">
    <value>Branch name is already present</value>
    <comment>Message</comment>
  </data>
  <data name="BranchTaxCode" xml:space="preserve">
    <value>Branch Tax Code</value>
    <comment>Label</comment>
  </data>
  <data name="BranchTaxDetails" xml:space="preserve">
    <value>Branch Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
    <comment>Label</comment>
  </data>
  <data name="BrandsAssociation" xml:space="preserve">
    <value>Brands Association</value>
    <comment>Label</comment>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>Browse</value>
    <comment>Label</comment>
  </data>
  <data name="calculateformula" xml:space="preserve">
    <value>Calculate Formula</value>
    <comment>Label</comment>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>Calendar</value>
    <comment>Label</comment>
  </data>
  <data name="CallBackDate" xml:space="preserve">
    <value>Call Back Date</value>
    <comment>Label</comment>
  </data>
  <data name="CallBackTime" xml:space="preserve">
    <value>Call Back Time</value>
    <comment>Label</comment>
  </data>
  <data name="CallClosureDate" xml:space="preserve">
    <value>Call Closure Date</value>
    <comment>Label</comment>
  </data>
  <data name="CallClosureTime" xml:space="preserve">
    <value>Call Closure Time</value>
    <comment>Label</comment>
  </data>
  <data name="CallDate" xml:space="preserve">
    <value>Call Date</value>
    <comment>Label</comment>
  </data>
  <data name="CallDescription" xml:space="preserve">
    <value>Call Description</value>
    <comment>Label</comment>
  </data>
  <data name="CallDetails" xml:space="preserve">
    <value>Call Details</value>
    <comment>Label</comment>
  </data>
  <data name="CallMode" xml:space="preserve">
    <value>Call Mode</value>
    <comment>Label</comment>
  </data>
  <data name="CallNature" xml:space="preserve">
    <value>Call Nature</value>
    <comment>Label</comment>
  </data>
  <data name="CallStatus" xml:space="preserve">
    <value>Call Status</value>
    <comment>Label</comment>
  </data>
  <data name="CallTime" xml:space="preserve">
    <value>Call Time</value>
    <comment>Label</comment>
  </data>
  <data name="CallType" xml:space="preserve">
    <value>Call Type</value>
    <comment>Label</comment>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
    <comment>Label</comment>
  </data>
  <data name="CaseProgress" xml:space="preserve">
    <value>Case Progress</value>
    <comment>Label</comment>
  </data>
  <data name="CaseProgressHistory" xml:space="preserve">
    <value>Case Progress History</value>
    <comment>Label</comment>
  </data>
  <data name="CauseofFailure" xml:space="preserve">
    <value>Cause of Failure</value>
    <comment>Label</comment>
  </data>
  <data name="ChooseColumnNames" xml:space="preserve">
    <value>Choose column names</value>
    <comment>Message</comment>
  </data>
  <data name="clearformula" xml:space="preserve">
    <value>Clear Formula</value>
    <comment>Label</comment>
  </data>
  <data name="closurereason" xml:space="preserve">
    <value>Closure Reason</value>
    <comment>Label</comment>
  </data>
  <data name="ClosureType" xml:space="preserve">
    <value>Closure Type</value>
    <comment>Label</comment>
  </data>
  <data name="code" xml:space="preserve">
    <value>Code</value>
    <comment>Label</comment>
  </data>
  <data name="ColumnNames" xml:space="preserve">
    <value>Column Names</value>
    <comment>Label</comment>
  </data>
  <data name="commissioningdate" xml:space="preserve">
    <value>Commissioning Date</value>
    <comment>Label</comment>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyBrands" xml:space="preserve">
    <value>Company Brands</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyHeader" xml:space="preserve">
    <value>Header</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyMaster" xml:space="preserve">
    <value>Company Master</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Company Name</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateCompanyName" xml:space="preserve">
    <value>Duplicate Company Name</value>
    <comment>Message</comment>
  </data>
  <data name="CompanyRelation" xml:space="preserve">
    <value>Company-Company Relation</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyRelationships" xml:space="preserve">
    <value>Company Relationships</value>
    <comment>Label</comment>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand" xml:space="preserve">
    <value>Company header saved success fully, Please associate atleast one Brand, Please Create One Branch, Employee and associate Employeee to Branch</value>
    <comment>Message</comment>
  </data>
  <data name="CompanyTaxCode" xml:space="preserve">
    <value>Company Tax Code</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyTaxDetails" xml:space="preserve">
    <value>Company Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyTerms" xml:space="preserve">
    <value>Company Terms</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyTheme" xml:space="preserve">
    <value>Company Theme</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Company Type</value>
    <comment>Label</comment>
  </data>
  <data name="CompleteEnteringDetail" xml:space="preserve">
    <value>Complete entering detail</value>
    <comment>Message</comment>
  </data>
  <data name="CompleteEnteringDetailParts" xml:space="preserve">
    <value>Complete entering parts detail</value>
    <comment>Message</comment>
  </data>
  <data name="CompleteEnteringDetailService" xml:space="preserve">
    <value>Complete entering service detail</value>
    <comment>Message</comment>
  </data>
  <data name="CompleteEnteringDetailSundry" xml:space="preserve">
    <value>Complete entering sundry detail</value>
    <comment>Message</comment>
  </data>
  <data name="componentdetails" xml:space="preserve">
    <value>Component Details</value>
    <comment>Label</comment>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
    <comment>Label</comment>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>Contact Person</value>
    <comment>Label</comment>
  </data>
  <data name="ContactPersons" xml:space="preserve">
    <value>Contact Persons</value>
    <comment>Label</comment>
  </data>
  <data name="CopyRoleFrom" xml:space="preserve">
    <value>Copy Role From</value>
    <comment>Label</comment>
  </data>
  <data name="CorrectiveAction" xml:space="preserve">
    <value>Corrective Action</value>
    <comment>Label</comment>
  </data>
  <data name="Count" xml:space="preserve">
    <value>Count</value>
    <comment>Label</comment>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
    <comment>Label</comment>
  </data>
  <data name="CreateJobCard" xml:space="preserve">
    <value>Create JobCard</value>
    <comment>Label</comment>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Create New</value>
    <comment>Label</comment>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>Create Quotation</value>
    <comment>Label</comment>
  </data>
  <data name="CreateServiceRequest" xml:space="preserve">
    <value>Create Service Request</value>
    <comment>Label</comment>
  </data>
  <data name="crictical" xml:space="preserve">
    <value>Critical</value>
    <comment>Label</comment>
  </data>
  <data name="Critical" xml:space="preserve">
    <value>Critical</value>
    <comment>Label</comment>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
    <comment>Label</comment>
  </data>
  <data name="CurrentStep" xml:space="preserve">
    <value>Current Step</value>
    <comment>Label</comment>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerComplaint" xml:space="preserve">
    <value>Customer Complaint</value>
    <comment>Label</comment>
  </data>
  <data name="customerdetails" xml:space="preserve">
    <value>Customer Details</value>
    <comment>Label</comment>
  </data>
  <data name="customerdueservices" xml:space="preserve">
    <value>Customer Due for Service</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>Customer is locked, do you want to continue?</value>
    <comment>Message</comment>
  </data>
  <data name="Customerisnotactive" xml:space="preserve">
    <value>Customer is not active</value>
    <comment>Message</comment>
  </data>
  <data name="customername" xml:space="preserve">
    <value>Customer Name</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerNotFound" xml:space="preserve">
    <value>Customer not found</value>
    <comment>Message</comment>
  </data>
  <data name="CustomerQuotation" xml:space="preserve">
    <value>Customer Quotation</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerQuotationDate" xml:space="preserve">
    <value>Quotation Date</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerQuotationNumber" xml:space="preserve">
    <value>Quotation Number</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>Customer Rating</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerServiceHistory" xml:space="preserve">
    <value>Customer Service History</value>
    <comment>Label</comment>
  </data>
  <data name="DataSavedSuccessfully" xml:space="preserve">
    <value>Data saved successfully</value>
    <comment>Message</comment>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
    <comment>Label</comment>
  </data>
  <data name="DateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>Effective from date cannot be less than current date</value>
    <comment>Message</comment>
  </data>
  <data name="Datecannotbelessthenpreviousdate" xml:space="preserve">
    <value>Date cannot be less then or equal to the date of previous records</value>
    <comment>Message</comment>
  </data>
  <data name="dateselectedmustbegreaterthenpreviouscustomer" xml:space="preserve">
    <value>Date Selected must be greater than previous Customer</value>
    <comment>Message</comment>
  </data>
  <data name="DaysLeft" xml:space="preserve">
    <value>Days Left</value>
    <comment>Label</comment>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>Dealer</value>
    <comment>Label</comment>
  </data>
  <data name="DealersorCompany" xml:space="preserve">
    <value>Dealers/Company</value>
    <comment>Label</comment>
  </data>
  <data name="December" xml:space="preserve">
    <value>Dec</value>
    <comment>Label</comment>
  </data>
  <data name="DefaultGridSize" xml:space="preserve">
    <value>Default Grid Size</value>
    <comment>Label</comment>
  </data>
  <data name="DefaultgridSizeshouldbebetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>Default grid size should be between 0 and 255</value>
    <comment>Message</comment>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteAction" xml:space="preserve">
    <value>Delete Action</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteBranch" xml:space="preserve">
    <value>Delete Branch</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteBranchTaxDetails" xml:space="preserve">
    <value>Delete Branch Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteBrands" xml:space="preserve">
    <value>Delete Brands</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteCompany" xml:space="preserve">
    <value>Delete Company</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteCompanyRelation" xml:space="preserve">
    <value>Delete Company Relation</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteCompanyTerms" xml:space="preserve">
    <value>Delete Company Terms</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteEmployee" xml:space="preserve">
    <value>Delete Employee</value>
    <comment>Label</comment>
  </data>
  <data name="deletefreestock" xml:space="preserve">
    <value>Delete Part Free Stock</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteFunctionGroup" xml:space="preserve">
    <value>Delete Function Group</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteJobCard" xml:space="preserve">
    <value>Delete Job Card</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteMaster" xml:space="preserve">
    <value>Delete Master</value>
    <comment>Label</comment>
  </data>
  <data name="deletemodel" xml:space="preserve">
    <value>Delete Model</value>
    <comment>Label</comment>
  </data>
  <data name="deleteoperation" xml:space="preserve">
    <value>Delete Operation</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteOperationEmployeeDetails" xml:space="preserve">
    <value>Delete Operation Employee Details</value>
    <comment>Label</comment>
  </data>
  <data name="deletepart" xml:space="preserve">
    <value>Delete Part</value>
    <comment>Label</comment>
  </data>
  <data name="deletepartprice" xml:space="preserve">
    <value>Delete Part Price</value>
    <comment>Label</comment>
  </data>
  <data name="deletepartproducttype" xml:space="preserve">
    <value>Delete Part product Type</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteParts" xml:space="preserve">
    <value>Delete Parts</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteParty" xml:space="preserve">
    <value>Delete Party</value>
    <comment>Label</comment>
  </data>
  <data name="DeletePrefixSuffix" xml:space="preserve">
    <value>Delete Prefix Suffix</value>
    <comment>Label</comment>
  </data>
  <data name="deleteproduct" xml:space="preserve">
    <value>Delete Product</value>
    <comment>Label</comment>
  </data>
  <data name="deleteproductdetail" xml:space="preserve">
    <value>Delete Product Detail</value>
    <comment>Label</comment>
  </data>
  <data name="deleteproducttype" xml:space="preserve">
    <value>Delete Product Type</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteQuotation" xml:space="preserve">
    <value>Delete Quotation</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteRequest" xml:space="preserve">
    <value>Delete Request</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteRole" xml:space="preserve">
    <value>Delete Role</value>
    <comment>Label</comment>
  </data>
  <data name="deleteServiceCharge" xml:space="preserve">
    <value>Delete Service Charge</value>
    <comment>Label</comment>
  </data>
  <data name="deleteServiceChargeDetails" xml:space="preserve">
    <value>Delete Service Charge Details</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteServiceType" xml:space="preserve">
    <value>Delete Service Type</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteSkills" xml:space="preserve">
    <value>Delete Skills</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteSpecialization" xml:space="preserve">
    <value>Delete Specialization</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteStep" xml:space="preserve">
    <value>Delete Step</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteStepLink" xml:space="preserve">
    <value>Delete Step Link</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteSundry" xml:space="preserve">
    <value>Delete Sundry</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteTaxCode" xml:space="preserve">
    <value>Delete Tax Code</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteTaxDetails" xml:space="preserve">
    <value>Delete Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="deletetaxstructure" xml:space="preserve">
    <value>Delete Tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="deletetaxtstructuredetails" xml:space="preserve">
    <value>Delete Tax Structure Details</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteUser" xml:space="preserve">
    <value>Delete User</value>
    <comment>Label</comment>
  </data>
  <data name="deletewarrantydetails" xml:space="preserve">
    <value>Delete Warranty Details</value>
    <comment>Label</comment>
  </data>
  <data name="DeleteWorkDetails" xml:space="preserve">
    <value>Delete Work Details</value>
    <comment>Label</comment>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
    <comment>Label</comment>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Department</value>
    <comment>Label</comment>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>Dependency found cannot delete the records</value>
    <comment>Message</comment>
  </data>
  <data name="description" xml:space="preserve">
    <value>Description</value>
    <comment>Label</comment>
  </data>
  <data name="Designation" xml:space="preserve">
    <value>Designation</value>
    <comment>Label</comment>
  </data>
  <data name="DestinationColumns" xml:space="preserve">
    <value>Destination Columns</value>
    <comment>Label</comment>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
    <comment>Label</comment>
  </data>
  <data name="DeviationHours" xml:space="preserve">
    <value>Deviation Hours</value>
    <comment>Label</comment>
  </data>
  <data name="DeviationPercentage" xml:space="preserve">
    <value>Deviation%</value>
    <comment>Label</comment>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Discount</value>
    <comment>Label</comment>
  </data>
  <data name="Discountamount" xml:space="preserve">
    <value>Discount Amount</value>
    <comment>Label</comment>
  </data>
  <data name="DiscountedAmount" xml:space="preserve">
    <value>Discounted Amount</value>
    <comment>Label</comment>
  </data>
  <data name="DiscountPercentage" xml:space="preserve">
    <value>Discount Percentage</value>
    <comment>Label</comment>
  </data>
  <data name="DivideByZeroException" xml:space="preserve">
    <value>Application error occured</value>
    <comment>Message</comment>
  </data>
  <data name="DonotEnterSpace" xml:space="preserve">
    <value>Do not enter space</value>
    <comment>Message</comment>
  </data>
  <data name="DoyouwanttoaddthisSerialNumber" xml:space="preserve">
    <value>Do you want to add this serial number</value>
    <comment>Message</comment>
  </data>
  <data name="Doyouwanttochangetheassociation" xml:space="preserve">
    <value>Do you want to change the association?</value>
    <comment>Message</comment>
  </data>
  <data name="Doyouwanttocreatenewversion" xml:space="preserve">
    <value>Do you want to create New Version?</value>
    <comment>Message</comment>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due Date</value>
    <comment>Label</comment>
  </data>
  <data name="DueDatecannotbeBlank" xml:space="preserve">
    <value>Due date cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="DueRange" xml:space="preserve">
    <value>Due Range</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateDescription" xml:space="preserve">
    <value>Duplicate description</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateEmailsFound" xml:space="preserve">
    <value>Duplicate emails found</value>
    <comment>Message</comment>
  </data>
  <data name="duplicatemodel" xml:space="preserve">
    <value>Duplicate model</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicatePhoneNumbersFound" xml:space="preserve">
    <value>Duplicate phone numbers found</value>
    <comment>Message</comment>
  </data>
  <data name="duplicateproducttype" xml:space="preserve">
    <value>Duplicate product type</value>
    <comment>Message</comment>
  </data>
  <data name="duplicatesecondarysegment" xml:space="preserve">
    <value>Duplicate secondary segment</value>
    <comment>Message</comment>
  </data>
  <data name="duplicateservicedate" xml:space="preserve">
    <value>Duplicate service date</value>
    <comment>Message</comment>
  </data>
  <data name="duplicateservicetype" xml:space="preserve">
    <value>Duplicate service type</value>
    <comment>Message</comment>
  </data>
  <data name="duplicatestate" xml:space="preserve">
    <value>Duplicate state</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateSundryDescriptionisnotAllowed" xml:space="preserve">
    <value>Duplicate sundry description is not allowed</value>
    <comment>Message</comment>
  </data>
  <data name="edit" xml:space="preserve">
    <value>Edit</value>
    <comment>Label</comment>
  </data>
  <data name="EditAction" xml:space="preserve">
    <value>Edit Action</value>
    <comment>Label</comment>
  </data>
  <data name="editcomponentdetails" xml:space="preserve">
    <value>Edit Component Details</value>
    <comment>Label</comment>
  </data>
  <data name="EditJobCard" xml:space="preserve">
    <value>Edit Job Card</value>
    <comment>Label</comment>
  </data>
  <data name="editmodel" xml:space="preserve">
    <value>Edit Model</value>
    <comment>Label</comment>
  </data>
  <data name="editoperation" xml:space="preserve">
    <value>Edit Operation</value>
    <comment>Label</comment>
  </data>
  <data name="editproduct" xml:space="preserve">
    <value>Edit Product</value>
    <comment>Label</comment>
  </data>
  <data name="editproducttype" xml:space="preserve">
    <value>Edit Product Type</value>
    <comment>Label</comment>
  </data>
  <data name="editsecondarysegment" xml:space="preserve">
    <value>Edit Secondary Segment</value>
    <comment>Label</comment>
  </data>
  <data name="editsevicecharges" xml:space="preserve">
    <value>Edit Service Charges</value>
    <comment>Label</comment>
  </data>
  <data name="editsiteaddress" xml:space="preserve">
    <value>Edit Site Address</value>
    <comment>Label</comment>
  </data>
  <data name="edittaxstructure" xml:space="preserve">
    <value>Edit Tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="editwarrantydeatils" xml:space="preserve">
    <value>Edit Warranty Deatils</value>
    <comment>Label</comment>
  </data>
  <data name="effectivefrom" xml:space="preserve">
    <value>Effective From</value>
    <comment>Label</comment>
  </data>
  <data name="EightHour" xml:space="preserve">
    <value>Less Than 8 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="EightToSixteenHours" xml:space="preserve">
    <value>8 To 16 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
    <comment>Label</comment>
  </data>
  <data name="EmailToAddresse" xml:space="preserve">
    <value>Email To Addresse</value>
    <comment>Label</comment>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>Email To Customer</value>
    <comment>Label</comment>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Employee</value>
    <comment>Label</comment>
  </data>
  <data name="EmployeeBranch" xml:space="preserve">
    <value>Employee - Branch</value>
    <comment>Label</comment>
  </data>
  <data name="EmployeeDetails" xml:space="preserve">
    <value>Employee Details</value>
    <comment>Label</comment>
  </data>
  <data name="EmployeeID" xml:space="preserve">
    <value>Employee Code</value>
    <comment>Label</comment>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeBranch" xml:space="preserve">
    <value>Employee is already associated with the branch</value>
    <comment>Message</comment>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeSpecialization" xml:space="preserve">
    <value>Employee is already associated with the specialization</value>
    <comment>Message</comment>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Employee</value>
    <comment>Label</comment>
  </data>
  <data name="EmployeeNotFound" xml:space="preserve">
    <value>Employee not found</value>
    <comment>Message</comment>
  </data>
  <data name="EmployeeSkills" xml:space="preserve">
    <value>Skills</value>
    <comment>Label</comment>
  </data>
  <data name="EmployeeSpecialization" xml:space="preserve">
    <value>Specialization</value>
    <comment>Label</comment>
  </data>
  <data name="EndDatecannotbelessthanStartDate" xml:space="preserve">
    <value>End date cannot be less than start date</value>
    <comment>Message</comment>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
    <comment>Label</comment>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>Enter Code</value>
    <comment>Label</comment>
  </data>
  <data name="EnterDescription" xml:space="preserve">
    <value>Enter Description</value>
    <comment>Label</comment>
  </data>
  <data name="EnteredNumberdoesnotbelongstocustomerorprospect" xml:space="preserve">
    <value>Entered number doesnot belongs to customer or prospect</value>
    <comment>Message</comment>
  </data>
  <data name="EnterMasterName" xml:space="preserve">
    <value>Enter master name</value>
    <comment>Message</comment>
  </data>
  <data name="enterNonTaxableothercharges1" xml:space="preserve">
    <value>Enter non taxable other charges 1</value>
    <comment>Message</comment>
  </data>
  <data name="enterNonTaxableothercharges2" xml:space="preserve">
    <value>Enter non taxable other charges 2</value>
    <comment>Message</comment>
  </data>
  <data name="enterTaxableothercharges1" xml:space="preserve">
    <value>Enter taxable other charges 1</value>
    <comment>Message</comment>
  </data>
  <data name="enterTaxableothercharges2" xml:space="preserve">
    <value>Enter taxable other charges 2</value>
    <comment>Message</comment>
  </data>
  <data name="EntryTaxPercentage" xml:space="preserve">
    <value>Entry Tax Percentage</value>
    <comment>Label</comment>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
    <comment>Label</comment>
  </data>
  <data name="ErrorSaving" xml:space="preserve">
    <value>Error Saving</value>
    <comment>Label</comment>
  </data>
  <data name="EventName" xml:space="preserve">
    <value>Event Name</value>
    <comment>Label</comment>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Export</value>
    <comment>Label</comment>
  </data>
  <data name="ExportAction" xml:space="preserve">
    <value>Export Action</value>
    <comment>Label</comment>
  </data>
  <data name="ExporttoDocument" xml:space="preserve">
    <value>Export to Document</value>
    <comment>Label</comment>
  </data>
  <data name="FailedtosavenewContactPerson" xml:space="preserve">
    <value>Failed to save new contact person</value>
    <comment>Message</comment>
  </data>
  <data name="FailedtosavenewParty" xml:space="preserve">
    <value>Failed to save new party</value>
    <comment>Message</comment>
  </data>
  <data name="FailedtosavenewSerialNumber" xml:space="preserve">
    <value>Failed to save new serial number</value>
    <comment>Message</comment>
  </data>
  <data name="FailedToSaveSerial" xml:space="preserve">
    <value>Failed to save serial</value>
    <comment>Message</comment>
  </data>
  <data name="FAX" xml:space="preserve">
    <value>FAX</value>
    <comment>Label</comment>
  </data>
  <data name="February" xml:space="preserve">
    <value>Feb</value>
    <comment>Label</comment>
  </data>
  <data name="Fieldshighlightedaremandatory" xml:space="preserve">
    <value>Fields highlighted are mandatory</value>
    <comment>Message</comment>
  </data>
  <data name="FieldsmarkedwithStararemandatory" xml:space="preserve">
    <value>Fields marked with * are mandatory</value>
    <comment>Message</comment>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
    <comment>Label</comment>
  </data>
  <data name="FilterCriteria" xml:space="preserve">
    <value>Filter Criteria</value>
    <comment>Label</comment>
  </data>
  <data name="forgotpassword" xml:space="preserve">
    <value>Forgot Password?</value>
    <comment>Label</comment>
  </data>
  <data name="formula" xml:space="preserve">
    <value>Formula</value>
    <comment>Label</comment>
  </data>
  <data name="formulasummary" xml:space="preserve">
    <value>Formula Summary</value>
    <comment>Label</comment>
  </data>
  <data name="FourtyEightToNintyHours" xml:space="preserve">
    <value>48 To 90 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="freestock" xml:space="preserve">
    <value>Free Stock</value>
    <comment>Label</comment>
  </data>
  <data name="fromdate" xml:space="preserve">
    <value>From Date</value>
    <comment>Label</comment>
  </data>
  <data name="FromDateandTodatecannotbeEmpty" xml:space="preserve">
    <value>From Date and To Date cannot be empty</value>
    <comment>Message</comment>
  </data>
  <data name="fromdatecannotbegreatorthantodate" xml:space="preserve">
    <value>From Date cannot be greater than To Date</value>
    <comment>Message</comment>
  </data>
  <data name="fromdatecannotbelessthencurrentdate" xml:space="preserve">
    <value>From Date cannot be less than current date</value>
    <comment>Message</comment>
  </data>
  <data name="fromdatemustbegreaterthanorequaltoissuedate" xml:space="preserve">
    <value>From Date must be greater than or equal to Issue date</value>
    <comment>Message</comment>
  </data>
  <data name="fromdatemustbelesserthentodate" xml:space="preserve">
    <value>From Date must be lesser than To Date</value>
    <comment>Message</comment>
  </data>
  <data name="FromStep" xml:space="preserve">
    <value>From Step</value>
    <comment>Label</comment>
  </data>
  <data name="FunctionGroup" xml:space="preserve">
    <value>Function Group</value>
    <comment>Label</comment>
  </data>
  <data name="FunctionGroupHeader" xml:space="preserve">
    <value>Function Group Header</value>
    <comment>Label</comment>
  </data>
  <data name="FunctionGroupID" xml:space="preserve">
    <value>Function Group ID</value>
    <comment>Label</comment>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>Function Group Name</value>
    <comment>Label</comment>
  </data>
  <data name="FunctionGroupNative" xml:space="preserve">
    <value>Function Group Native</value>
    <comment>Label</comment>
  </data>
  <data name="FunctionGroupOperations" xml:space="preserve">
    <value>Function Group Operations</value>
    <comment>Label</comment>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>Generate Report</value>
    <comment>Label</comment>
  </data>
  <data name="GroupQueue" xml:space="preserve">
    <value>Group Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Header" xml:space="preserve">
    <value>Header</value>
    <comment>Label</comment>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Heading</value>
    <comment>Label</comment>
  </data>
  <data name="high" xml:space="preserve">
    <value>High</value>
    <comment>Label</comment>
  </data>
  <data name="HighlightedFieldsareMandatory" xml:space="preserve">
    <value>Highlighted fields are mandatory</value>
    <comment>Message</comment>
  </data>
  <data name="HMR" xml:space="preserve">
    <value>HMR</value>
    <comment>Label</comment>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Import</value>
    <comment>Label</comment>
  </data>
  <data name="ImportAction" xml:space="preserve">
    <value>Import Action</value>
    <comment>Label</comment>
  </data>
  <data name="ImportedSuccessfully" xml:space="preserve">
    <value>Imported successfully</value>
    <comment>Message</comment>
  </data>
  <data name="ImportIntoDatabase" xml:space="preserve">
    <value>Import Into Database</value>
    <comment>Label</comment>
  </data>
  <data name="ImportParts" xml:space="preserve">
    <value>Import Parts</value>
    <comment>Label</comment>
  </data>
  <data name="IndexOutOfRangeException" xml:space="preserve">
    <value>Application error occured</value>
    <comment>Message</comment>
  </data>
  <data name="InsertedSuccessfully" xml:space="preserve">
    <value>Inserted successfully</value>
    <comment>Message</comment>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>Internal</value>
    <comment>Label</comment>
  </data>
  <data name="InvalidCastException" xml:space="preserve">
    <value>Application error occured</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>Invalid date</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>Invalid email</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidOperationException" xml:space="preserve">
    <value>Database error occured</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidPhoneNo" xml:space="preserve">
    <value>Invalid Mobile No</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidProduct" xml:space="preserve">
    <value>Invalid product</value>
    <comment>Message</comment>
  </data>
  <data name="invalidselection" xml:space="preserve">
    <value>Invalid selection</value>
    <comment>Message</comment>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>Is Active?</value>
    <comment>Label</comment>
  </data>
  <data name="IsAdmin" xml:space="preserve">
    <value>Is Admin</value>
    <comment>Label</comment>
  </data>
  <data name="isbaseamountincluded" xml:space="preserve">
    <value>Is Base Amount Included?</value>
    <comment>Label</comment>
  </data>
  <data name="IsCompanySpecific" xml:space="preserve">
    <value>Is Company Specific</value>
    <comment>Label</comment>
  </data>
  <data name="iscomponent" xml:space="preserve">
    <value>Is Component</value>
    <comment>Label</comment>
  </data>
  <data name="IsCustomer" xml:space="preserve">
    <value>IsCustomer</value>
    <comment>Label</comment>
  </data>
  <data name="IsDefaultContact" xml:space="preserve">
    <value>Is Default Contact ?</value>
    <comment>Label</comment>
  </data>
  <data name="IsExternal" xml:space="preserve">
    <value>Is External</value>
    <comment>Label</comment>
  </data>
  <data name="isHazardous" xml:space="preserve">
    <value>Is Hazardous</value>
    <comment>Label</comment>
  </data>
  <data name="IsHeadOffice" xml:space="preserve">
    <value>Is Head Office</value>
    <comment>Label</comment>
  </data>
  <data name="IsOperationCompleted" xml:space="preserve">
    <value>Is Operation Completed</value>
    <comment>Label</comment>
  </data>
  <data name="issueddate" xml:space="preserve">
    <value>Issued Date</value>
    <comment>Label</comment>
  </data>
  <data name="issueddatesholudbelessthanorequaltocurrentdate" xml:space="preserve">
    <value>Issued date sholud be less than or equal to current date</value>
    <comment>Message</comment>
  </data>
  <data name="IsUnderBreakDown" xml:space="preserve">
    <value>Is Under Break Down?</value>
    <comment>Label</comment>
  </data>
  <data name="isunderwarranty" xml:space="preserve">
    <value>Is Under Warranty?</value>
    <comment>Label</comment>
  </data>
  <data name="isversionallowed" xml:space="preserve">
    <value>Is Version Allowed?</value>
    <comment>Label</comment>
  </data>
  <data name="January" xml:space="preserve">
    <value>Jan</value>
    <comment>Label</comment>
  </data>
  <data name="JobCard" xml:space="preserve">
    <value>Job Card</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardAbandonReason" xml:space="preserve">
    <value>Job Card Abandon Reason</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardClosureDate" xml:space="preserve">
    <value>Job Card Closure Date</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardCushionHours" xml:space="preserve">
    <value>Job Card Cushion Hours</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardDate" xml:space="preserve">
    <value>Job Card Date</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardDelayReason" xml:space="preserve">
    <value>Job Card Delay Reason</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardisalreadycreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>Job card is already created for this service request number</value>
    <comment>Message</comment>
  </data>
  <data name="JobCardNumber" xml:space="preserve">
    <value>Job Card Number</value>
    <comment>Label</comment>
  </data>
  <data name="JobcardNumbernotfound" xml:space="preserve">
    <value>Job card number not found</value>
    <comment>Message</comment>
  </data>
  <data name="JobCardPendingCount" xml:space="preserve">
    <value>Job Card Pending Count</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardStatus" xml:space="preserve">
    <value>Job Card Status</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardVersion" xml:space="preserve">
    <value>Version</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardWIPCount" xml:space="preserve">
    <value>Job Card WIP Count</value>
    <comment>Label</comment>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>Job Description</value>
    <comment>Label</comment>
  </data>
  <data name="JobEndDate" xml:space="preserve">
    <value>Job End Date</value>
    <comment>Label</comment>
  </data>
  <data name="JobPriority" xml:space="preserve">
    <value>Job Priority</value>
    <comment>Label</comment>
  </data>
  <data name="JobSiteAddress" xml:space="preserve">
    <value>Job Site Address</value>
    <comment>Label</comment>
  </data>
  <data name="JobStartDate" xml:space="preserve">
    <value>Job Start Date</value>
    <comment>Label</comment>
  </data>
  <data name="JoinedTables" xml:space="preserve">
    <value>Joined Tables</value>
    <comment>Label</comment>
  </data>
  <data name="JoinWith" xml:space="preserve">
    <value> Join With</value>
    <comment>Label</comment>
  </data>
  <data name="July" xml:space="preserve">
    <value>Jul</value>
    <comment>Label</comment>
  </data>
  <data name="June" xml:space="preserve">
    <value>Jun</value>
    <comment>Label</comment>
  </data>
  <data name="Landline" xml:space="preserve">
    <value>Landline</value>
    <comment>Label</comment>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
    <comment>Label</comment>
  </data>
  <data name="LanguageName" xml:space="preserve">
    <value>Language</value>
    <comment>Label</comment>
  </data>
  <data name="Local" xml:space="preserve">
    <value>Local</value>
    <comment>Label</comment>
  </data>
  <data name="Locale" xml:space="preserve">
    <value>Locale</value>
    <comment>Label</comment>
  </data>
  <data name="LocaleDetails" xml:space="preserve">
    <value>Locale Details</value>
    <comment>Label</comment>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
    <comment>Label</comment>
  </data>
  <data name="lockedby" xml:space="preserve">
    <value>Locked By</value>
    <comment>Label</comment>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
    <comment>Label</comment>
  </data>
  <data name="LoginID" xml:space="preserve">
    <value>Login ID</value>
    <comment>Label</comment>
  </data>
  <data name="LogoName" xml:space="preserve">
    <value>Logo Name</value>
    <comment>Label</comment>
  </data>
  <data name="low" xml:space="preserve">
    <value>Low</value>
    <comment>Label</comment>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Manager</value>
    <comment>Label</comment>
  </data>
  <data name="mandatoryservices" xml:space="preserve">
    <value>Mandatory Services</value>
    <comment>Label</comment>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
    <comment>Label</comment>
  </data>
  <data name="MapColumns" xml:space="preserve">
    <value>Map Columns</value>
    <comment>Label</comment>
  </data>
  <data name="MappedColumns" xml:space="preserve">
    <value>Mapped Columns</value>
    <comment>Label</comment>
  </data>
  <data name="March" xml:space="preserve">
    <value>Mar</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateMaster" xml:space="preserve">
    <value>Duplicate Master</value>
    <comment>Message</comment>
  </data>
  <data name="MasterID" xml:space="preserve">
    <value>Master ID</value>
    <comment>Label</comment>
  </data>
  <data name="MasterName" xml:space="preserve">
    <value>Master Name</value>
    <comment>Label</comment>
  </data>
  <data name="May" xml:space="preserve">
    <value>May</value>
    <comment>Label</comment>
  </data>
  <data name="medium" xml:space="preserve">
    <value>Medium</value>
    <comment>Label</comment>
  </data>
  <data name="MenuDetail" xml:space="preserve">
    <value>Menu Details</value>
    <comment>Label</comment>
  </data>
  <data name="MenuDetails" xml:space="preserve">
    <value>Menu Details</value>
    <comment>Label</comment>
  </data>
  <data name="MenuName" xml:space="preserve">
    <value>Menu Name</value>
    <comment>Label</comment>
  </data>
  <data name="MenuNamecannotbeblank" xml:space="preserve">
    <value>Menu name can not be blank</value>
    <comment>Message</comment>
  </data>
  <data name="MenuPath" xml:space="preserve">
    <value>Menu Path</value>
    <comment>Label</comment>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
    <comment>Label</comment>
  </data>
  <data name="model" xml:space="preserve">
    <value>Model</value>
    <comment>Label</comment>
  </data>
  <data name="modelenglish" xml:space="preserve">
    <value>Model English</value>
    <comment>Label</comment>
  </data>
  <data name="modellocale" xml:space="preserve">
    <value>Model Locale</value>
    <comment>Label</comment>
  </data>
  <data name="modelmaster" xml:space="preserve">
    <value>Model Master</value>
    <comment>Label</comment>
  </data>
  <data name="modelname" xml:space="preserve">
    <value>Model Name</value>
    <comment>Label</comment>
  </data>
  <data name="Module" xml:space="preserve">
    <value>Module</value>
    <comment>Label</comment>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>Module Name</value>
    <comment>Label</comment>
  </data>
  <data name="ModuleNameCannotbeblank" xml:space="preserve">
    <value>Module name cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="month" xml:space="preserve">
    <value>Month</value>
    <comment>Label</comment>
  </data>
  <data name="MyQueue" xml:space="preserve">
    <value>My Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
    <comment>Label</comment>
  </data>
  <data name="NintyHour" xml:space="preserve">
    <value>More Than 90 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="NoChangesMade" xml:space="preserve">
    <value>No changes made</value>
    <comment>Message</comment>
  </data>
  <data name="NochangesmadetoSave" xml:space="preserve">
    <value>No changes made to save</value>
    <comment>Message</comment>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd" xml:space="preserve">
    <value>Party is InActive or No matching record found, Do you want to add this party</value>
    <comment>Message</comment>
  </data>
  <data name="NonTaxable" xml:space="preserve">
    <value>Non Taxable</value>
    <comment>Label</comment>
  </data>
  <data name="NonTaxableothercharges1" xml:space="preserve">
    <value>Non Taxable other charges 1</value>
    <comment>Label</comment>
  </data>
  <data name="NonTaxableothercharges2" xml:space="preserve">
    <value>Non Taxable other charges 2</value>
    <comment>Label</comment>
  </data>
  <data name="Noproductisassociatedwithselectedcustomer" xml:space="preserve">
    <value>No product is associated with selected customer</value>
    <comment>Message</comment>
  </data>
  <data name="November" xml:space="preserve">
    <value>Nov</value>
    <comment>Label</comment>
  </data>
  <data name="NullReferenceException" xml:space="preserve">
    <value>Application error occured</value>
    <comment>Message</comment>
  </data>
  <data name="number" xml:space="preserve">
    <value>Number</value>
    <comment>Label</comment>
  </data>
  <data name="ObjectDescription" xml:space="preserve">
    <value>Object Description</value>
    <comment>Label</comment>
  </data>
  <data name="ObjectDescriptioncannotbeblank" xml:space="preserve">
    <value>Object description cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="ObjectMaster" xml:space="preserve">
    <value>Object</value>
    <comment>Label</comment>
  </data>
  <data name="ObjectName" xml:space="preserve">
    <value>Object Name</value>
    <comment>Label</comment>
  </data>
  <data name="ObjectNamecannotbeblank" xml:space="preserve">
    <value>Object name cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="ObjectssavedSuccessfully" xml:space="preserve">
    <value>Objects saved successfully</value>
    <comment>Message</comment>
  </data>
  <data name="October" xml:space="preserve">
    <value>Oct</value>
    <comment>Label</comment>
  </data>
  <data name="onlyactivecustomerdeatilscanbeedited" xml:space="preserve">
    <value>Only active customer deatils can be edited</value>
    <comment>Message</comment>
  </data>
  <data name="onlyactivewarrantycanbeedited" xml:space="preserve">
    <value>Only active warranty can be edited</value>
    <comment>Message</comment>
  </data>
  <data name="OpenReport" xml:space="preserve">
    <value>Open Report</value>
    <comment>Label</comment>
  </data>
  <data name="operation" xml:space="preserve">
    <value>Operation</value>
    <comment>Label</comment>
  </data>
  <data name="OperationCode" xml:space="preserve">
    <value>Operation Code</value>
    <comment>Label</comment>
  </data>
  <data name="OperationCodeNotFound" xml:space="preserve">
    <value>Operation code not found</value>
    <comment>Message</comment>
  </data>
  <data name="OperationDescription" xml:space="preserve">
    <value>Operation Description</value>
    <comment>Label</comment>
  </data>
  <data name="OperationDetails" xml:space="preserve">
    <value>Operation Details</value>
    <comment>Label</comment>
  </data>
  <data name="OperationDeviationReport" xml:space="preserve">
    <value>Operation Deviation Report</value>
    <comment>Label</comment>
  </data>
  <data name="OperationEmployeeDetails" xml:space="preserve">
    <value>Operation Employee Details</value>
    <comment>Label</comment>
  </data>
  <data name="OperationEndDate" xml:space="preserve">
    <value>Operation End Date</value>
    <comment>Label</comment>
  </data>
  <data name="OperationEndDateCannotbelessthanoperationStartDate" xml:space="preserve">
    <value>Operation end date cannot be less than operation start date</value>
    <comment>Message</comment>
  </data>
  <data name="operationenglish" xml:space="preserve">
    <value>Operation English</value>
    <comment>Label</comment>
  </data>
  <data name="operationheader" xml:space="preserve">
    <value>Operation Header</value>
    <comment>Label</comment>
  </data>
  <data name="OperationHours" xml:space="preserve">
    <value>Operation Hours</value>
    <comment>Label</comment>
  </data>
  <data name="operationlocale" xml:space="preserve">
    <value>Operation Locale</value>
    <comment>Label</comment>
  </data>
  <data name="operationmaster" xml:space="preserve">
    <value>Operation Master</value>
    <comment>Label</comment>
  </data>
  <data name="OperationStartDate" xml:space="preserve">
    <value>Operation Start Date</value>
    <comment>Label</comment>
  </data>
  <data name="OtherDetail" xml:space="preserve">
    <value>Other Detail</value>
    <comment>Label</comment>
  </data>
  <data name="OtherDetails" xml:space="preserve">
    <value>Other Details</value>
    <comment>Label</comment>
  </data>
  <data name="OutOfMemoryException" xml:space="preserve">
    <value>Application error occured</value>
    <comment>Message</comment>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>Parent Company</value>
    <comment>Label</comment>
  </data>
  <data name="parentcompanyoperationcannotbedeleted" xml:space="preserve">
    <value>Parent company operation cannot be deleted</value>
    <comment>Message</comment>
  </data>
  <data name="parentcompanyoperationcannotbeedited" xml:space="preserve">
    <value>Parent company operation cannot be edited</value>
    <comment>Message</comment>
  </data>
  <data name="ParentMenu" xml:space="preserve">
    <value>Parent Menu</value>
    <comment>Label</comment>
  </data>
  <data name="partcategory" xml:space="preserve">
    <value>Part Category</value>
    <comment>Label</comment>
  </data>
  <data name="partdescription" xml:space="preserve">
    <value>Part Description</value>
    <comment>Label</comment>
  </data>
  <data name="partfunctiongroup" xml:space="preserve">
    <value>Part Function Group</value>
    <comment>Label</comment>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>Partner</value>
    <comment>Label</comment>
  </data>
  <data name="PartnerName" xml:space="preserve">
    <value>Partner</value>
    <comment>Label</comment>
  </data>
  <data name="partnumber" xml:space="preserve">
    <value>Part Number</value>
    <comment>Label</comment>
  </data>
  <data name="Duplicatepartnumber" xml:space="preserve">
    <value>Duplicate part number</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumbernotfound" xml:space="preserve">
    <value>Part number not found</value>
    <comment>Message</comment>
  </data>
  <data name="partprice" xml:space="preserve">
    <value>Part Price</value>
    <comment>Label</comment>
  </data>
  <data name="partpricepdetails" xml:space="preserve">
    <value>Part Price Details</value>
    <comment>Label</comment>
  </data>
  <data name="partproducttypedetails" xml:space="preserve">
    <value>Parts Product Type Details</value>
    <comment>Label</comment>
  </data>
  <data name="PartsDetail" xml:space="preserve">
    <value>Parts Detail</value>
    <comment>Label</comment>
  </data>
  <data name="partsenglish" xml:space="preserve">
    <value>Parts </value>
    <comment>Label</comment>
  </data>
  <data name="partsfreestockdetails" xml:space="preserve">
    <value>Parts Free Stock details</value>
    <comment>Label</comment>
  </data>
  <data name="partslocale" xml:space="preserve">
    <value>Parts </value>
    <comment>Label</comment>
  </data>
  <data name="partsmaster" xml:space="preserve">
    <value>Parts Master</value>
    <comment>Label</comment>
  </data>
  <data name="partsmasterlocale" xml:space="preserve">
    <value>Parts Master </value>
    <comment>Label</comment>
  </data>
  <data name="partspmasterheader" xml:space="preserve">
    <value>Parts Master </value>
    <comment>Label</comment>
  </data>
  <data name="partspricedetails" xml:space="preserve">
    <value>Parts Price Details</value>
    <comment>Label</comment>
  </data>
  <data name="partsproducttypelocale" xml:space="preserve">
    <value>Parts Product Type </value>
    <comment>Label</comment>
  </data>
  <data name="PartsTemplate" xml:space="preserve">
    <value>Parts Template</value>
    <comment>Label</comment>
  </data>
  <data name="PartsTotalAmount" xml:space="preserve">
    <value>Parts Total Amount</value>
    <comment>Label</comment>
  </data>
  <data name="Party" xml:space="preserve">
    <value>Party</value>
    <comment>Label</comment>
  </data>
  <data name="PartyDetails" xml:space="preserve">
    <value>Party Details</value>
    <comment>Label</comment>
  </data>
  <data name="PartyLocation" xml:space="preserve">
    <value>Party Location</value>
    <comment>Label</comment>
  </data>
  <data name="PartyMobile" xml:space="preserve">
    <value>Party Mobile</value>
    <comment>Label</comment>
  </data>
  <data name="PartyName" xml:space="preserve">
    <value>Party Name</value>
    <comment>Label</comment>
  </data>
  <data name="PartyNotFound" xml:space="preserve">
    <value>Party Not Found</value>
    <comment>Label</comment>
  </data>
  <data name="PartyPhone" xml:space="preserve">
    <value>Party Phone</value>
    <comment>Label</comment>
  </data>
  <data name="PartyType" xml:space="preserve">
    <value>Party Type</value>
    <comment>Label</comment>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
    <comment>Label</comment>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch" xml:space="preserve">
    <value>Confirm password does not match with password given</value>
    <comment>Message</comment>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>Payment Terms</value>
    <comment>Label</comment>
  </data>
  <data name="PercentageDeviation" xml:space="preserve">
    <value>Percentage Deviation</value>
    <comment>Label</comment>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
    <comment>Label</comment>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>Phone</value>
    <comment>Label</comment>
  </data>
  <data name="PlannedCompletionDate" xml:space="preserve">
    <value>Planned Completion Date</value>
    <comment>Label</comment>
  </data>
  <data name="PlannedCompletionDatecannotbelessthanStartDate" xml:space="preserve">
    <value>Planned completion date cannot be less than start date</value>
    <comment>Message</comment>
  </data>
  <data name="PlannedStartDate" xml:space="preserve">
    <value>Planned Start Date</value>
    <comment>Label</comment>
  </data>
  <data name="pleasecompleteenteringdetails" xml:space="preserve">
    <value>Please complete entering details</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseenterPartyName" xml:space="preserve">
    <value>Please enter party name</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseenterReportHeader" xml:space="preserve">
    <value>Please enter report header</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseenterReportName" xml:space="preserve">
    <value>Please enter report name</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseenteruserdetails" xml:space="preserve">
    <value>Please enter user details</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseentervalue" xml:space="preserve">
    <value>Please enter value</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseentervalue1" xml:space="preserve">
    <value>Please enter value1</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseprovideMenuName" xml:space="preserve">
    <value>Please provide menu name</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseprovideModuleName" xml:space="preserve">
    <value>Please provide module name</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseprovidepassword" xml:space="preserve">
    <value>Please provide password</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectafiletoupload" xml:space="preserve">
    <value>Please select a file to upload</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseselectBrand" xml:space="preserve">
    <value>Please select brand</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectcompany" xml:space="preserve">
    <value>Please select company</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectcondition" xml:space="preserve">
    <value>Please select condition</value>
    <comment>Message</comment>
  </data>
  <data name="pleaseselectModel" xml:space="preserve">
    <value>Please select model</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseselectmodelandSerialNumber" xml:space="preserve">
    <value>Pleases select model and serial number</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectoperator" xml:space="preserve">
    <value>Please select operator</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectrecordstodelete" xml:space="preserve">
    <value>Please select records to delete</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseselecttheColumnName" xml:space="preserve">
    <value>Please select the column name</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselectthecolumnstomap" xml:space="preserve">
    <value>Please select the columns to map</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseselecttheoperator" xml:space="preserve">
    <value>Please select the operator</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseselecttheTableName" xml:space="preserve">
    <value>Please select the table name</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseselectUserstosave" xml:space="preserve">
    <value>Please select users to save</value>
    <comment>Message</comment>
  </data>
  <data name="prefix" xml:space="preserve">
    <value>Prefix</value>
    <comment>Label</comment>
  </data>
  <data name="prefixsuffix" xml:space="preserve">
    <value>Prefix Suffix</value>
    <comment>Label</comment>
  </data>
  <data name="PreviousdateCannotbeempty" xml:space="preserve">
    <value>Previous date cannot be empty</value>
    <comment>Message</comment>
  </data>
  <data name="PricecannotbeBlankorZero" xml:space="preserve">
    <value>Price cannot be blank or zero</value>
    <comment>Message</comment>
  </data>
  <data name="PrimarySegment" xml:space="preserve">
    <value>Primary Segment</value>
    <comment>Label</comment>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
    <comment>Label</comment>
  </data>
  <data name="PrintAction" xml:space="preserve">
    <value>Print Action</value>
    <comment>Label</comment>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
    <comment>Label</comment>
  </data>
  <data name="product" xml:space="preserve">
    <value>Product</value>
    <comment>Label</comment>
  </data>
  <data name="ProductAssociation" xml:space="preserve">
    <value>Product Association</value>
    <comment>Label</comment>
  </data>
  <data name="productdetail" xml:space="preserve">
    <value>Product Detail</value>
    <comment>Label</comment>
  </data>
  <data name="productdetails" xml:space="preserve">
    <value>Product Details</value>
    <comment>Label</comment>
  </data>
  <data name="productid" xml:space="preserve">
    <value>Product ID</value>
    <comment>Label</comment>
  </data>
  <data name="Productisnotasscociatedwithanycustomer" xml:space="preserve">
    <value>Product is currently not asscociated with any customer</value>
    <comment>Message</comment>
  </data>
  <data name="ProductReading" xml:space="preserve">
    <value>Product Reading</value>
    <comment>Label</comment>
  </data>
  <data name="ProductServiceHistory" xml:space="preserve">
    <value>Product Service History</value>
    <comment>Label</comment>
  </data>
  <data name="Producttype" xml:space="preserve">
    <value>Product Type</value>
    <comment>Label</comment>
  </data>
  <data name="producttypemaster" xml:space="preserve">
    <value>Product Type Master</value>
    <comment>Label</comment>
  </data>
  <data name="producttypename" xml:space="preserve">
    <value>Product Type Name</value>
    <comment>Label</comment>
  </data>
  <data name="ProductUniqueNo" xml:space="preserve">
    <value>Unique Identifier</value>
    <comment>Label</comment>
  </data>
  <data name="Prospect" xml:space="preserve">
    <value>Prospect</value>
    <comment>Label</comment>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
    <comment>Label</comment>
  </data>
  <data name="QuotationAmount" xml:space="preserve">
    <value>Quotation Amount</value>
    <comment>Label</comment>
  </data>
  <data name="QuotationDetail" xml:space="preserve">
    <value>Quotation Detail</value>
    <comment>Label</comment>
  </data>
  <data name="QuotationNumber" xml:space="preserve">
    <value>Quotation Number</value>
    <comment>Label</comment>
  </data>
  <data name="QuotationPriority" xml:space="preserve">
    <value>Quotation Priority</value>
    <comment>Label</comment>
  </data>
  <data name="RangecannotbeBlank" xml:space="preserve">
    <value>Range cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
    <comment>Label</comment>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Rating</value>
    <comment>Label</comment>
  </data>
  <data name="Ratingshouldbebetween1and10" xml:space="preserve">
    <value>Rating should be between 1 and 10</value>
    <comment>Message</comment>
  </data>
  <data name="Read" xml:space="preserve">
    <value>Read</value>
    <comment>Label</comment>
  </data>
  <data name="ReadAction" xml:space="preserve">
    <value>Read Action</value>
    <comment>Label</comment>
  </data>
  <data name="reading" xml:space="preserve">
    <value>Reading</value>
    <comment>Label</comment>
  </data>
  <data name="readinglimit" xml:space="preserve">
    <value>Reading Limit</value>
    <comment>Label</comment>
  </data>
  <data name="readinglog" xml:space="preserve">
    <value>Reading Log</value>
    <comment>Label</comment>
  </data>
  <data name="reasonforinactive" xml:space="preserve">
    <value>Reason for Inactive</value>
    <comment>Label</comment>
  </data>
  <data name="RecentActivityLinks" xml:space="preserve">
    <value>Recent Activity Links</value>
    <comment>Label</comment>
  </data>
  <data name="RecievedTime" xml:space="preserve">
    <value>Received Time</value>
    <comment>Label</comment>
  </data>
  <data name="ReferenceDetail" xml:space="preserve">
    <value>Reference Detail</value>
    <comment>Label</comment>
  </data>
  <data name="ReferenceMasters" xml:space="preserve">
    <value>Reference Masters</value>
    <comment>Label</comment>
  </data>
  <data name="ReferenceTables" xml:space="preserve">
    <value>Reference Tables</value>
    <comment>Label</comment>
  </data>
  <data name="refresh" xml:space="preserve">
    <value>Refresh</value>
    <comment>Label</comment>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
    <comment>Label</comment>
  </data>
  <data name="RegisteredMobile" xml:space="preserve">
    <value>Registered Mobile</value>
    <comment>Label</comment>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>Remarks</value>
    <comment>Label</comment>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>Remove Filter</value>
    <comment>Label</comment>
  </data>
  <data name="ReportWizard" xml:space="preserve">
    <value>Report Wizard</value>
    <comment>Label</comment>
  </data>
  <data name="ReqDate" xml:space="preserve">
    <value>Req Date</value>
    <comment>Label</comment>
  </data>
  <data name="ReqNumber" xml:space="preserve">
    <value>Req Number</value>
    <comment>Label</comment>
  </data>
  <data name="RequestDescription" xml:space="preserve">
    <value>Request Description</value>
    <comment>Label</comment>
  </data>
  <data name="resolutiontime" xml:space="preserve">
    <value>Resolution Time</value>
    <comment>Label</comment>
  </data>
  <data name="Resolutiontimewithtimeslots" xml:space="preserve">
    <value>Resolution Time With Time Slots</value>
    <comment>Label</comment>
  </data>
  <data name="RoleDefinition" xml:space="preserve">
    <value>Role Definition</value>
    <comment>Label</comment>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Role Name</value>
    <comment>Label</comment>
  </data>
  <data name="RoleNameCannotbeblank" xml:space="preserve">
    <value>Role name cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="RoleObject" xml:space="preserve">
    <value>Role Object</value>
    <comment>Label</comment>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Roles</value>
    <comment>Label</comment>
  </data>
  <data name="RoundOff" xml:space="preserve">
    <value>Round Off</value>
    <comment>Label</comment>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
    <comment>Label</comment>
  </data>
  <data name="SaveAction" xml:space="preserve">
    <value>Save Action</value>
    <comment>Label</comment>
  </data>
  <data name="SaveFormatandGenerateReport" xml:space="preserve">
    <value>Save Format and Generate Report</value>
    <comment>Label</comment>
  </data>
  <data name="savefreestockdetails" xml:space="preserve">
    <value>Save Parts Free Stock Details</value>
    <comment>Label</comment>
  </data>
  <data name="saveheader" xml:space="preserve">
    <value>Save Header</value>
    <comment>Label</comment>
  </data>
  <data name="savepartprice" xml:space="preserve">
    <value>Save Part Price Details</value>
    <comment>Label</comment>
  </data>
  <data name="SavePrefixSuffix" xml:space="preserve">
    <value>Save Prefix Suffix</value>
    <comment>Label</comment>
  </data>
  <data name="saveproductdetail" xml:space="preserve">
    <value>Save Product Detail</value>
    <comment>Label</comment>
  </data>
  <data name="saveproducttype" xml:space="preserve">
    <value>Save Part Product Type</value>
    <comment>Label</comment>
  </data>
  <data name="SaveRole" xml:space="preserve">
    <value>Save Role</value>
    <comment>Label</comment>
  </data>
  <data name="SaveStep" xml:space="preserve">
    <value>Save Step</value>
    <comment>Label</comment>
  </data>
  <data name="SaveStepLink" xml:space="preserve">
    <value>Save Step Link</value>
    <comment>Label</comment>
  </data>
  <data name="SavedSuccessfully" xml:space="preserve">
    <value>Saved Successfully</value>
    <comment>Message</comment>
  </data>
  <data name="SaveSuccessfull" xml:space="preserve">
    <value>Saved Successfully</value>
    <comment>Message</comment>
  </data>
  <data name="savetaxstructure" xml:space="preserve">
    <value>Save Tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="SaveUser" xml:space="preserve">
    <value>Save User</value>
    <comment>Label</comment>
  </data>
  <data name="SecondarySegment" xml:space="preserve">
    <value>Secondary Segment</value>
    <comment>Label</comment>
  </data>
  <data name="SecondarySegmentdescription" xml:space="preserve">
    <value>Secondary Segment Description</value>
    <comment>Label</comment>
  </data>
  <data name="secondarysegmentenglish" xml:space="preserve">
    <value>Secondary Segment English</value>
    <comment>Label</comment>
  </data>
  <data name="secondarysegmentlocale" xml:space="preserve">
    <value>Secondary Segment Locale</value>
    <comment>Label</comment>
  </data>
  <data name="SegmentDetail" xml:space="preserve">
    <value>Segment Detail</value>
    <comment>Label</comment>
  </data>
  <data name="select" xml:space="preserve">
    <value>Select</value>
    <comment>Label</comment>
  </data>
  <data name="SelectDDl" xml:space="preserve">
    <value>---------Select---------</value>
    <comment>Label</comment>
  </data>
  <data name="SelectedFileisnotanExcelFile" xml:space="preserve">
    <value>Selected file is not an excel file</value>
    <comment>Message</comment>
  </data>
  <data name="SelectionCriteria" xml:space="preserve">
    <value>Selection Criteria</value>
    <comment>Label</comment>
  </data>
  <data name="SelectModel" xml:space="preserve">
    <value>Select Model</value>
    <comment>Label</comment>
  </data>
  <data name="SelectModelandRequest" xml:space="preserve">
    <value>Select Model and Request</value>
    <comment>Label</comment>
  </data>
  <data name="SelectPartyType" xml:space="preserve">
    <value>Select Party Type</value>
    <comment>Label</comment>
  </data>
  <data name="SelectReportFromPreviouslyStoredFormats" xml:space="preserve">
    <value>Select Report From Previously Stored Formats</value>
    <comment>Label</comment>
  </data>
  <data name="SelectReqNumber" xml:space="preserve">
    <value>Select Request Number</value>
    <comment>Label</comment>
  </data>
  <data name="SelectTableName" xml:space="preserve">
    <value>Select Table Name</value>
    <comment>Label</comment>
  </data>
  <data name="September" xml:space="preserve">
    <value>Sep</value>
    <comment>Label</comment>
  </data>
  <data name="sequenceno" xml:space="preserve">
    <value>Sequence No</value>
    <comment>Label</comment>
  </data>
  <data name="Serial" xml:space="preserve">
    <value>Serial</value>
    <comment>Label</comment>
  </data>
  <data name="serialnumber" xml:space="preserve">
    <value>Serial Number</value>
    <comment>Label</comment>
  </data>
  <data name="serialnumberalreadyexistsforthismodel" xml:space="preserve">
    <value>Serial number already exists for this model</value>
    <comment>Message</comment>
  </data>
  <data name="SerialNumbernotfoundfortheselectedmodel" xml:space="preserve">
    <value>Serial number not found for the selected model</value>
    <comment>Message</comment>
  </data>
  <data name="ServiceChargeCode" xml:space="preserve">
    <value>Service Charge Code</value>
    <comment>Label</comment>
  </data>
  <data name="servicechargecodenotfound" xml:space="preserve">
    <value>Service charge code not found</value>
    <comment>Message</comment>
  </data>
  <data name="ServiceChargeDetail" xml:space="preserve">
    <value>Service Charge Detail</value>
    <comment>Label</comment>
  </data>
  <data name="servicecharges" xml:space="preserve">
    <value>Service Charges</value>
    <comment>Label</comment>
  </data>
  <data name="servicechargesdetail" xml:space="preserve">
    <value>Service Charges Detail</value>
    <comment>Label</comment>
  </data>
  <data name="servicechargesenglish" xml:space="preserve">
    <value>Service Charges</value>
    <comment>Label</comment>
  </data>
  <data name="servicechargesheader" xml:space="preserve">
    <value>Service Charges </value>
    <comment>Label</comment>
  </data>
  <data name="servicechargeslocale" xml:space="preserve">
    <value>Service Charges </value>
    <comment>Label</comment>
  </data>
  <data name="servicechargesmaster" xml:space="preserve">
    <value>Service Charges Master</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceChargesTotalAmount" xml:space="preserve">
    <value>Service Charges Total Amount</value>
    <comment>Label</comment>
  </data>
  <data name="Duplicateservicecode" xml:space="preserve">
    <value>Duplicate Service code</value>
    <comment>Message</comment>
  </data>
  <data name="servicedate" xml:space="preserve">
    <value>Service Date</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceDetails" xml:space="preserve">
    <value>Service Details</value>
    <comment>Label</comment>
  </data>
  <data name="servicehistory" xml:space="preserve">
    <value>Service History</value>
    <comment>Label</comment>
  </data>
  <data name="ServicePriority" xml:space="preserve">
    <value>Service Priority</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceQuotationNumber" xml:space="preserve">
    <value>Quotation Number</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequest" xml:space="preserve">
    <value>Service Request</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestAbandoned" xml:space="preserve">
    <value>Service Request Abandoned</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestCount" xml:space="preserve">
    <value>Service Request Count</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestDate" xml:space="preserve">
    <value>Service Request Date</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestNumber" xml:space="preserve">
    <value>Service Request Number</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestNumbernotfound" xml:space="preserve">
    <value>Service request number not found</value>
    <comment>Message</comment>
  </data>
  <data name="ServiceSchedule" xml:space="preserve">
    <value>Service Schedule</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceType" xml:space="preserve">
    <value>Service Type</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceTypeName" xml:space="preserve">
    <value>Service Type Name</value>
    <comment>Label</comment>
  </data>
  <data name="ShortName" xml:space="preserve">
    <value>Short Name</value>
    <comment>Label</comment>
  </data>
  <data name="siteaddress" xml:space="preserve">
    <value>Site Address</value>
    <comment>Label</comment>
  </data>
  <data name="siteaddressdetails" xml:space="preserve">
    <value>Site Address Details</value>
    <comment>Label</comment>
  </data>
  <data name="SixteenToTwentyFourHours" xml:space="preserve">
    <value>16 To 24 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="skill" xml:space="preserve">
    <value>Skill</value>
    <comment>Label</comment>
  </data>
  <data name="Skillisalreadyassociatedwiththeemployee" xml:space="preserve">
    <value>Skill is already associated with the employee</value>
    <comment>Message</comment>
  </data>
  <data name="skilllevel" xml:space="preserve">
    <value>Skill Level</value>
    <comment>Label</comment>
  </data>
  <data name="skilllevelshouldbebetween1to10" xml:space="preserve">
    <value>Skill level should be between 1 to 10</value>
    <comment>Message</comment>
  </data>
  <data name="Skillset" xml:space="preserve">
    <value>Skill Set</value>
    <comment>Label</comment>
  </data>
  <data name="slno" xml:space="preserve">
    <value>Sl No</value>
    <comment>Label</comment>
  </data>
  <data name="SMSToAddressee" xml:space="preserve">
    <value>SMS To Addressee</value>
    <comment>Label</comment>
  </data>
  <data name="SMSToCustomer" xml:space="preserve">
    <value>SMS To Customer</value>
    <comment>Label</comment>
  </data>
  <data name="SMTPMailBox" xml:space="preserve">
    <value>SMTP Mail Box</value>
    <comment>Label</comment>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>SMTP Password</value>
    <comment>Label</comment>
  </data>
  <data name="SMTPServerName" xml:space="preserve">
    <value>SMTP Server Name</value>
    <comment>Label</comment>
  </data>
  <data name="SMTPUserName" xml:space="preserve">
    <value>SMTP User Name</value>
    <comment>Label</comment>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>Sort Order</value>
    <comment>Label</comment>
  </data>
  <data name="SortOrdercannotbeblank" xml:space="preserve">
    <value>Sort order can not be blank</value>
    <comment>Message</comment>
  </data>
  <data name="sortordercannotbeblankforMenu" xml:space="preserve">
    <value>Sort order can not be blank for menu</value>
    <comment>Message</comment>
  </data>
  <data name="SourceColumns" xml:space="preserve">
    <value>Source Columns</value>
    <comment>Label</comment>
  </data>
  <data name="Specialization" xml:space="preserve">
    <value>Specialization</value>
    <comment>Label</comment>
  </data>
  <data name="SpecializationMaster" xml:space="preserve">
    <value>Specialization Master</value>
    <comment>Label</comment>
  </data>
  <data name="SqlException" xml:space="preserve">
    <value>Database error occured</value>
    <comment>Message</comment>
  </data>
  <data name="StandardHours" xml:space="preserve">
    <value>Standard Hours</value>
    <comment>Label</comment>
  </data>
  <data name="standardtime" xml:space="preserve">
    <value>Standard Time</value>
    <comment>Label</comment>
  </data>
  <data name="startnumber" xml:space="preserve">
    <value>Start Number</value>
    <comment>Label</comment>
  </data>
  <data name="startnumbercannotbenullorzero" xml:space="preserve">
    <value>Start number cannot be null or zero</value>
    <comment>Message</comment>
  </data>
  <data name="State" xml:space="preserve">
    <value>State</value>
    <comment>Label</comment>
  </data>
  <data name="stateenglish" xml:space="preserve">
    <value>State English</value>
    <comment>Label</comment>
  </data>
  <data name="statelocale" xml:space="preserve">
    <value>State Locale</value>
    <comment>Label</comment>
  </data>
  <data name="status" xml:space="preserve">
    <value>Status</value>
    <comment>Label</comment>
  </data>
  <data name="StepLink" xml:space="preserve">
    <value>Step Link</value>
    <comment>Label</comment>
  </data>
  <data name="StepName" xml:space="preserve">
    <value>Step Name</value>
    <comment>Label</comment>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>Steps</value>
    <comment>Label</comment>
  </data>
  <data name="StepStatus" xml:space="preserve">
    <value>Step Status</value>
    <comment>Label</comment>
  </data>
  <data name="StepType" xml:space="preserve">
    <value>Step Type</value>
    <comment>Label</comment>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
    <comment>Message</comment>
  </data>
  <data name="suffix" xml:space="preserve">
    <value>Suffix</value>
    <comment>Label</comment>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
    <comment>Label</comment>
  </data>
  <data name="SundryDetail" xml:space="preserve">
    <value>Sundry Detail</value>
    <comment>Label</comment>
  </data>
  <data name="SundryJobDescription" xml:space="preserve">
    <value>Sundry Job Description</value>
    <comment>Label</comment>
  </data>
  <data name="SundryTotalAmount" xml:space="preserve">
    <value>Sundry Total Amount</value>
    <comment>Label</comment>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>Table Name</value>
    <comment>Label</comment>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
    <comment>Label</comment>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>Taxable</value>
    <comment>Label</comment>
  </data>
  <data name="Taxableothercharges1" xml:space="preserve">
    <value>Taxable Other Charges 1</value>
    <comment>Label</comment>
  </data>
  <data name="Taxableothercharges2" xml:space="preserve">
    <value>Taxable Other Charges 2</value>
    <comment>Label</comment>
  </data>
  <data name="Taxamount" xml:space="preserve">
    <value>Tax Amount</value>
    <comment>Label</comment>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>Tax Code</value>
    <comment>Label</comment>
  </data>
  <data name="TaxCodeName" xml:space="preserve">
    <value>Tax Code Name</value>
    <comment>Label</comment>
  </data>
  <data name="TaxDetails" xml:space="preserve">
    <value>Tax Details</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateTaxname" xml:space="preserve">
    <value>Duplicate tax name</value>
    <comment>Message</comment>
  </data>
  <data name="taxpercentage" xml:space="preserve">
    <value>Tax percentage</value>
    <comment>Label</comment>
  </data>
  <data name="TaxStructure" xml:space="preserve">
    <value>Tax Structure</value>
    <comment>Label</comment>
  </data>
  <data name="taxstructuredetail" xml:space="preserve">
    <value>Tax Structure Detail</value>
    <comment>Label</comment>
  </data>
  <data name="taxstructuredetails" xml:space="preserve">
    <value>Tax Structure Details</value>
    <comment>Label</comment>
  </data>
  <data name="taxstructureenglish" xml:space="preserve">
    <value>Tax Structure </value>
    <comment>Label</comment>
  </data>
  <data name="taxstructureheader" xml:space="preserve">
    <value>Tax Structure Header</value>
    <comment>Label</comment>
  </data>
  <data name="taxstructurelocale" xml:space="preserve">
    <value>Tax Structure </value>
    <comment>Label</comment>
  </data>
  <data name="taxstructurename" xml:space="preserve">
    <value>Tax Structure Name</value>
    <comment>Label</comment>
  </data>
  <data name="taxtype" xml:space="preserve">
    <value>Tax Type</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateTaxType" xml:space="preserve">
    <value>Duplicate tax type</value>
    <comment>Message</comment>
  </data>
  <data name="taxtypeisreferencedinformulacannotdelete" xml:space="preserve">
    <value>Tax type is referenced in formula cannot delete</value>
    <comment>Message</comment>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
    <comment>Label</comment>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>Terms And Conditions</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateBrand" xml:space="preserve">
    <value>Duplicate Brand</value>
    <comment>Message</comment>
  </data>
  <data name="TheCompanyDealerhasalreadybeenassociated" xml:space="preserve">
    <value>The company/dealer has already been associated</value>
    <comment>Message</comment>
  </data>
  <data name="ThetaxStructurehasalreadybeenselected" xml:space="preserve">
    <value>The tax structure has already been selected </value>
    <comment>Message</comment>
  </data>
  <data name="ThirtySixToFourtyEightHours" xml:space="preserve">
    <value>36 To 48 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateRole" xml:space="preserve">
    <value>Duplicate Role</value>
    <comment>Message</comment>
  </data>
  <data name="Thisroleisalreadyselectedfortheuser" xml:space="preserve">
    <value>This role is already selected for the user</value>
    <comment>Message</comment>
  </data>
  <data name="ThisSerialNumberisalreadyassociatedwiththecustomer" xml:space="preserve">
    <value>This serial number is already associated with the customer</value>
    <comment>Message</comment>
  </data>
  <data name="todate" xml:space="preserve">
    <value>To Date</value>
    <comment>Label</comment>
  </data>
  <data name="todatecannotbelessthenfromdatedate" xml:space="preserve">
    <value>To date cannot be less than from date</value>
    <comment>Message</comment>
  </data>
  <data name="ToStep" xml:space="preserve">
    <value>To Step</value>
    <comment>Label</comment>
  </data>
  <data name="TotalAmount" xml:space="preserve">
    <value>Total Amount</value>
    <comment>Label</comment>
  </data>
  <data name="TotalOn" xml:space="preserve">
    <value>Total On</value>
    <comment>Label</comment>
  </data>
  <data name="TotalQuotationAmount" xml:space="preserve">
    <value>Total Quotation Amount</value>
    <comment>Label</comment>
  </data>
  <data name="TotalTaxableAmount" xml:space="preserve">
    <value>Total Taxable Amount</value>
    <comment>Label</comment>
  </data>
  <data name="TwentyFourToThirtySixHours" xml:space="preserve">
    <value>24 To 36 Hours</value>
    <comment>Label</comment>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateUniqueidentifier" xml:space="preserve">
    <value>Duplicate Unique identifier</value>
    <comment>Message</comment>
  </data>
  <data name="unitofmeasurement" xml:space="preserve">
    <value>Unit Of Measurement</value>
    <comment>Label</comment>
  </data>
  <data name="uom" xml:space="preserve">
    <value>UOM</value>
    <comment>Label</comment>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>Upload File</value>
    <comment>Label</comment>
  </data>
  <data name="UploadParts" xml:space="preserve">
    <value>Upload Parts</value>
    <comment>Label</comment>
  </data>
  <data name="usageenvironment" xml:space="preserve">
    <value>Usage Environment</value>
    <comment>Label</comment>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
    <comment>Label</comment>
  </data>
  <data name="UserDataSavedSuccessfully" xml:space="preserve">
    <value>User data saved successfully</value>
    <comment>Message</comment>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>User Detail</value>
    <comment>Label</comment>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>User Details</value>
    <comment>Label</comment>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>User Name</value>
    <comment>Label</comment>
  </data>
  <data name="UserRoleDetails" xml:space="preserve">
    <value>User-Role Details</value>
    <comment>Label</comment>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>User Roles</value>
    <comment>Label</comment>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>User Type</value>
    <comment>Label</comment>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
    <comment>Label</comment>
  </data>
  <data name="Ver" xml:space="preserve">
    <value>Ver</value>
    <comment>Label</comment>
  </data>
  <data name="VerificationQueue" xml:space="preserve">
    <value>Verification Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version</value>
    <comment>Label</comment>
  </data>
  <data name="view" xml:space="preserve">
    <value>View</value>
    <comment>Label</comment>
  </data>
  <data name="ViewJobCard" xml:space="preserve">
    <value>View Job Card</value>
    <comment>Label</comment>
  </data>
  <data name="WarrantyDate" xml:space="preserve">
    <value>Warranty Date</value>
    <comment>Label</comment>
  </data>
  <data name="warrantydetails" xml:space="preserve">
    <value>Products Under Warranty</value>
    <comment>Label</comment>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Website</value>
    <comment>Label</comment>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome :</value>
    <comment>Label</comment>
  </data>
  <data name="WorkFlow" xml:space="preserve">
    <value>Work Flow</value>
    <comment>Label</comment>
  </data>
  <data name="WorkFlowID" xml:space="preserve">
    <value>Work Flow ID</value>
    <comment>Label</comment>
  </data>
  <data name="WorkFlowName" xml:space="preserve">
    <value>Work Flow Name</value>
    <comment>Label</comment>
  </data>
  <data name="WorkFlowSteps" xml:space="preserve">
    <value>Work Flow Steps</value>
    <comment>Label</comment>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Year</value>
    <comment>Label</comment>
  </data>
  <data name="ZipCode" xml:space="preserve">
    <value>Zip Code</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateOperationCode" xml:space="preserve">
    <value>Duplicate Operation Code</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateModule" xml:space="preserve">
    <value>Duplicate Module</value>
    <comment>Message</comment>
  </data>
  <data name="SortOrderCannotbegreaterthan" xml:space="preserve">
    <value>Sort order cannot be greater than 255</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateLoginId" xml:space="preserve">
    <value>Duplicate Login Id</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidRegisteredMobileNumber" xml:space="preserve">
    <value>Invalid registered mobile number</value>
    <comment>Message</comment>
  </data>
  <data name="InValidUniqueIdentificationNumber" xml:space="preserve">
    <value>Invalid unique identification number</value>
    <comment>Message</comment>
  </data>
  <data name="PriorityShouldBeBetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>Priority should be between 0 and 255</value>
    <comment>Message</comment>
  </data>
  <data name="ClosureDetails" xml:space="preserve">
    <value>Closure Details</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateServiceTypeisnotallowed" xml:space="preserve">
    <value>Duplicate service type is not allowed</value>
    <comment>Message</comment>
  </data>
  <data name="Taxableothercharges1Amount" xml:space="preserve">
    <value>Taxable Other Charges 1 Amount</value>
    <comment>Label</comment>
  </data>
  <data name="Taxableothercharges2Amount" xml:space="preserve">
    <value>Taxable Other Charges 2 Amount</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerContact" xml:space="preserve">
    <value>Contact Person</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerContactPhone" xml:space="preserve">
    <value>Contact Person Mobile</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerLocation" xml:space="preserve">
    <value>Customer Location</value>
    <comment>Label</comment>
  </data>
  <data name="ProductLocation" xml:space="preserve">
    <value>Product Location</value>
    <comment>Label</comment>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Select All</value>
    <comment>Label</comment>
  </data>
  <data name="AllocationNotPossible" xml:space="preserve">
    <value>Allocation Not Possible</value>
    <comment>Message</comment>
  </data>
  <data name="AlloctionFailed" xml:space="preserve">
    <value>Allocation failed</value>
    <comment>Message</comment>
  </data>
  <data name="ErrorOccuredwhileLocking" xml:space="preserve">
    <value>Error occured while locking</value>
    <comment>Message</comment>
  </data>
  <data name="TransactionisalreadybeenLocked" xml:space="preserve">
    <value>Transaction has already been locked</value>
    <comment>Message</comment>
  </data>
  <data name="TransactionisalreadybeenUnLocked" xml:space="preserve">
    <value>Transaction has already been unlocked</value>
    <comment>Message</comment>
  </data>
  <data name="TransactionLockedSuccessfully" xml:space="preserve">
    <value>Transaction Locked Successfully</value>
    <comment>Message</comment>
  </data>
  <data name="TransactionUnLockedSuccessfully" xml:space="preserve">
    <value>Transaction Unlocked Successfully</value>
    <comment>Message</comment>
  </data>
  <data name="no" xml:space="preserve">
    <value>No</value>
    <comment>Label</comment>
  </data>
  <data name="yes" xml:space="preserve">
    <value>Yes</value>
    <comment>Label</comment>
  </data>
  <data name="EditCompany" xml:space="preserve">
    <value>Edit Company</value>
    <comment>Label</comment>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
    <comment>Label</comment>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
    <comment>Label</comment>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Created</value>
    <comment>Label</comment>
  </data>
  <data name="CustomerQuotationSummary" xml:space="preserve">
    <value>Customer Quotation Summary</value>
    <comment>Label</comment>
  </data>
  <data name="Hold" xml:space="preserve">
    <value>Hold</value>
    <comment>Label</comment>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>In Progress</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardSummary" xml:space="preserve">
    <value>Job Card Summary</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestSummary" xml:space="preserve">
    <value>Service Request Summary</value>
    <comment>Label</comment>
  </data>
  <data name="UnRegisteredServiceRequest" xml:space="preserve">
    <value>Verification Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Abandoned" xml:space="preserve">
    <value>Abandoned</value>
    <comment>Label</comment>
  </data>
  <data name="AddParts" xml:space="preserve">
    <value>Add Parts</value>
    <comment>Label</comment>
  </data>
  <data name="BrandName" xml:space="preserve">
    <value>Brand Name</value>
    <comment>Label</comment>
  </data>
  <data name="EditPartsMaster" xml:space="preserve">
    <value>Edit Parts Master</value>
    <comment>Label</comment>
  </data>
  <data name="LocaledetailsarenotavaliableforBrand" xml:space="preserve">
    <value>Locale details are not avaliable for brand</value>
    <comment>Message</comment>
  </data>
  <data name="LocaledetailsarenotavaliableforProductModel" xml:space="preserve">
    <value>Locale details are not avaliable for product model</value>
    <comment>Message</comment>
  </data>
  <data name="LocaledetailsarenotavaliableforProductType" xml:space="preserve">
    <value>Locale details are not avaliable for product type</value>
    <comment>Message</comment>
  </data>
  <data name="locked" xml:space="preserve">
    <value>Locked</value>
    <comment>Label</comment>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>Mobile Number</value>
    <comment>Label</comment>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
    <comment>Label</comment>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>Registered</value>
    <comment>Label</comment>
  </data>
  <data name="ViewPartsMaster" xml:space="preserve">
    <value>View Parts Master</value>
    <comment>Label</comment>
  </data>
  <data name="ErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>Error in uploaded parts. Please open excel</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidFile" xml:space="preserve">
    <value>Invalid file</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseselectFile" xml:space="preserve">
    <value>Please select file</value>
    <comment>Message</comment>
  </data>
  <data name="Aresurewanttodelete" xml:space="preserve">
    <value>Are sure want to delete</value>
    <comment>Message</comment>
  </data>
  <data name="CannotDeleteinEditMode" xml:space="preserve">
    <value>Cannot delete in edit mode</value>
    <comment>Message</comment>
  </data>
  <data name="SelectRecordstoDelete" xml:space="preserve">
    <value>Select Records to Delete</value>
    <comment>Label</comment>
  </data>
  <data name="SRNotFound" xml:space="preserve">
    <value>Service request not found</value>
    <comment>Message</comment>
  </data>
  <data name="TotalTaxableAmountBlank" xml:space="preserve">
    <value>Total taxable amount is blank</value>
    <comment>Message</comment>
  </data>
  <data name="SelectServiceRequest" xml:space="preserve">
    <value>Select Service Request</value>
    <comment>Label</comment>
  </data>
  <data name="selectbrand" xml:space="preserve">
    <value>Select Brand</value>
    <comment>Label</comment>
  </data>
  <data name="selectproducttype" xml:space="preserve">
    <value>Select Product Type</value>
    <comment>Label</comment>
  </data>
  <data name="pleaseentervalidmodel" xml:space="preserve">
    <value>Please enter valid model</value>
    <comment>Message</comment>
  </data>
  <data name="ActionRemarksMaxlimitexceeded" xml:space="preserve">
    <value>Action remarks max limit exceeded</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidProductUniqueNumber" xml:space="preserve">
    <value>Invalid Product Unique Identifier or Product is not active</value>
    <comment>Message</comment>
  </data>
  <data name="invalidmodelormodelisinactive" xml:space="preserve">
    <value>Invalid model or model is inactive</value>
    <comment>Message</comment>
  </data>
  <data name="EditEvents" xml:space="preserve">
    <value>Edit Events</value>
    <comment>Label</comment>
  </data>
  <data name="AddEvents" xml:space="preserve">
    <value>Add Events</value>
    <comment>Label</comment>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Events</value>
    <comment>Label</comment>
  </data>
  <data name="AmountShouldbeLessThan" xml:space="preserve">
    <value>Amount should be less than</value>
    <comment>Message</comment>
  </data>
  <data name="DiscountShouldbeLessThan" xml:space="preserve">
    <value>Discount should be less than</value>
    <comment>Message</comment>
  </data>
  <data name="invalidpartnumberorpartnumberisinactive" xml:space="preserve">
    <value>Invalid part number or part number is inactive</value>
    <comment>Message</comment>
  </data>
  <data name="IsVersionEnabled" xml:space="preserve">
    <value>Is Version Enabled ?</value>
    <comment>Label</comment>
  </data>
  <data name="To" xml:space="preserve">
    <value>To</value>
    <comment>Label</comment>
  </data>
  <data name="AuthorizedSignatory" xml:space="preserve">
    <value>Authorized Signatory</value>
    <comment>Label</comment>
  </data>
  <data name="For" xml:space="preserve">
    <value>For</value>
    <comment>Label</comment>
  </data>
  <data name="Quotation" xml:space="preserve">
    <value>Quotation</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceChargesDetails" xml:space="preserve">
    <value>Service Charges Details</value>
    <comment>Label</comment>
  </data>
  <data name="TermsConditions" xml:space="preserve">
    <value>Terms &amp; Conditions</value>
    <comment>Label</comment>
  </data>
  <data name="EnquiryDate" xml:space="preserve">
    <value>Enquiry Date</value>
    <comment>Label</comment>
  </data>
  <data name="EnquiryNumber" xml:space="preserve">
    <value>Enquiry Number</value>
    <comment>Label</comment>
  </data>
  <data name="PartsDetails" xml:space="preserve">
    <value>Parts Details</value>
    <comment>Label</comment>
  </data>
  <data name="SundryDetails" xml:space="preserve">
    <value>Sundry Details</value>
    <comment>Label</comment>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
    <comment>Label</comment>
  </data>
  <data name="Sn" xml:space="preserve">
    <value>Sn</value>
    <comment>Label</comment>
  </data>
  <data name="pleasesavetheOperationdata" xml:space="preserve">
    <value>Please save the operation details</value>
    <comment>Message</comment>
  </data>
  <data name="pleasesavethepartsdata" xml:space="preserve">
    <value>Please save the parts details</value>
    <comment>Message</comment>
  </data>
  <data name="pleasesavetheServicedata" xml:space="preserve">
    <value>Please save the service details</value>
    <comment>Message</comment>
  </data>
  <data name="pleasesavethesundrydata" xml:space="preserve">
    <value>Please save the sundry details</value>
    <comment>Message</comment>
  </data>
  <data name="Atleastselectonedetail" xml:space="preserve">
    <value>Atleast select one detail</value>
    <comment>Message</comment>
  </data>
  <data name="OperationandEmployeeisalreadyassociated" xml:space="preserve">
    <value>Operation and employee is already associated</value>
    <comment>Message</comment>
  </data>
  <data name="Operationisalreadyassociatedwithanotheremployee" xml:space="preserve">
    <value>Operation is already associated with another employee</value>
    <comment>Message</comment>
  </data>
  <data name="NonTaxableothercharges1Amount" xml:space="preserve">
    <value>Non Taxable other charges 1 Amount</value>
    <comment>Label</comment>
  </data>
  <data name="NonTaxableothercharges2Amount" xml:space="preserve">
    <value>Non Taxable other charges 2 Amount</value>
    <comment>Label</comment>
  </data>
  <data name="InvalidName" xml:space="preserve">
    <value>Invalid party name</value>
    <comment>Message</comment>
  </data>
  <data name="averageresponsetime" xml:space="preserve">
    <value>Average Response Time</value>
    <comment>Label</comment>
  </data>
  <data name="averageresponsetimeyearwise" xml:space="preserve">
    <value>Average Response Time -Year Wise</value>
    <comment>Label</comment>
  </data>
  <data name="responsetime" xml:space="preserve">
    <value>Response Time</value>
    <comment>Label</comment>
  </data>
  <data name="InvalidModel" xml:space="preserve">
    <value>Invalid model</value>
    <comment>Message</comment>
  </data>
  <data name="FromDatecannotbelessthanToDate" xml:space="preserve">
    <value>From Date cannot be greater than To Date</value>
    <comment>Message</comment>
  </data>
  <data name="realizationreport" xml:space="preserve">
    <value>Realization Report</value>
    <comment>Label</comment>
  </data>
  <data name="RevenueGenerated" xml:space="preserve">
    <value>Revenue Generated</value>
    <comment>Label</comment>
  </data>
  <data name="revenuemorethen" xml:space="preserve">
    <value>Revenue More Then</value>
    <comment>Label</comment>
  </data>
  <data name="RevenuecannotbeBlankorzero" xml:space="preserve">
    <value>Revenue cannot be blank</value>
    <comment>Message</comment>
  </data>
  <data name="ClosedCount" xml:space="preserve">
    <value>Closed Count</value>
    <comment>Label</comment>
  </data>
  <data name="CompletedCount" xml:space="preserve">
    <value>Completed Count</value>
    <comment>Label</comment>
  </data>
  <data name="InProgressCount" xml:space="preserve">
    <value>In Progress Count</value>
    <comment>Label</comment>
  </data>
  <data name="OnHoldCount" xml:space="preserve">
    <value>OnHold Count</value>
    <comment>Label</comment>
  </data>
  <data name="AddTaxStructureDetails" xml:space="preserve">
    <value>Add Tax Structure Details</value>
    <comment>Label</comment>
  </data>
  <data name="modelisinactive" xml:space="preserve">
    <value>Model is inactive</value>
    <comment>Message</comment>
  </data>
  <data name="modelnotfound" xml:space="preserve">
    <value>Model not found</value>
    <comment>Message</comment>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
    <comment>Label</comment>
  </data>
  <data name="JobCardFieldSearch" xml:space="preserve">
    <value>Job Card Field Search</value>
    <comment>Label</comment>
  </data>
  <data name="OperationFieldSearch" xml:space="preserve">
    <value>Operation Field Search</value>
    <comment>Label</comment>
  </data>
  <data name="PartsFieldSearch" xml:space="preserve">
    <value>Parts Field Search</value>
    <comment>Label</comment>
  </data>
  <data name="SerialNumberFieldSearch" xml:space="preserve">
    <value>Serial Number Field Search</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceChargeFieldSearch" xml:space="preserve">
    <value>Service Charge Field Search</value>
    <comment>Label</comment>
  </data>
  <data name="ServicerequestFieldSearch" xml:space="preserve">
    <value>Service request Field Search</value>
    <comment>Label</comment>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>Model Search</value>
    <comment>Label</comment>
  </data>
  <data name="AlreadyAssociatedPleaseSelectfromDropDown" xml:space="preserve">
    <value>Already Associated Please Select from Drop Down</value>
  </data>
  <data name="PartySearch" xml:space="preserve">
    <value>Party Search</value>
    <comment>Label</comment>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed" xml:space="preserve">
    <value>Are you sure you want to cancel?</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidServiceRequestNumber" xml:space="preserve">
    <value>Invalid Service Request Number</value>
    <comment>Message</comment>
  </data>
  <data name="EmployeeIDisalreadyused" xml:space="preserve">
    <value>Duplicate Employee code</value>
    <comment>Message</comment>
  </data>
  <data name="ResourceUtilizationReport" xml:space="preserve">
    <value>Resource Utilization Report</value>
    <comment>Label</comment>
  </data>
  <data name="ServiceRequestDistributionChart" xml:space="preserve">
    <value>Service Request Distribution Chart</value>
    <comment>Label</comment>
  </data>
  <data name="FreeHours" xml:space="preserve">
    <value>Free Hours</value>
    <comment>Label</comment>
  </data>
  <data name="TotalAllocatedHours" xml:space="preserve">
    <value>Total Allocated Hours</value>
    <comment>Label</comment>
  </data>
  <data name="TotalWorkingHours" xml:space="preserve">
    <value>Total Working Hours</value>
    <comment>Label</comment>
  </data>
  <data name="UtilizationPercentage" xml:space="preserve">
    <value>Utilization %</value>
    <comment>Label</comment>
  </data>
  <data name="customersearch" xml:space="preserve">
    <value>Customer Search</value>
    <comment>Label</comment>
  </data>
  <data name="partnumbersearch" xml:space="preserve">
    <value>Part Number Search</value>
    <comment>Label</comment>
  </data>
  <data name="SRCount" xml:space="preserve">
    <value>Service Requests Count</value>
  </data>
  <data name="ToDatecannotbelessthanCurrentDate" xml:space="preserve">
    <value>To Date cannot be less than Current Date</value>
    <comment>Message</comment>
  </data>
  <data name="JobCardNumberSearch" xml:space="preserve">
    <value>Job Card Number Search</value>
  </data>
  <data name="AvgResolutionTime" xml:space="preserve">
    <value>Avg Resolution Time</value>
    <comment>Label</comment>
  </data>
  <data name="BarChart" xml:space="preserve">
    <value>Bar Chart</value>
    <comment>Label</comment>
  </data>
  <data name="NoOfSRCompleted" xml:space="preserve">
    <value>Number Of Service Request Completed</value>
    <comment>Label</comment>
  </data>
  <data name="NoOfSRRecieved" xml:space="preserve">
    <value>Number Of Service Request Recieved</value>
    <comment>Label</comment>
  </data>
  <data name="PieChart" xml:space="preserve">
    <value>Pie Chart</value>
    <comment>Label</comment>
  </data>
  <data name="ToDatecannotbegreaterthanCurrentDate" xml:space="preserve">
    <value>To Date cannot be greater than Current Date</value>
    <comment>Message</comment>
  </data>
  <data name="GraphCategorySelection" xml:space="preserve">
    <value>Graph Category Selection</value>
    <comment>Label</comment>
  </data>
  <data name="GraphType" xml:space="preserve">
    <value>Graph Type</value>
    <comment>Label</comment>
  </data>
  <data name="AmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>Amount Is Beyond Acceptable Limit</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseSelectGraphCategory" xml:space="preserve">
    <value>Please Select Graph Category</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseSelectGraphType" xml:space="preserve">
    <value>Please Select Graph Type</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>Please enter login ID</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseEnterPassword" xml:space="preserve">
    <value>Please enter password</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseSelectBranch" xml:space="preserve">
    <value>Please select branch</value>
    <comment>Message</comment>
  </data>
  <data name="UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain" xml:space="preserve">
    <value>User name or password you entered is incorrect. Please try again..</value>
    <comment>Message</comment>
  </data>
  <data name="RecieviedCount" xml:space="preserve">
    <value>Received Count</value>
  </data>
  <data name="todatemustbegreaterthanorequaltofromdate" xml:space="preserve">
    <value>To date must be greater than or equal to From date</value>
  </data>
  <data name="servicedatecannotbelessthancurrentdate" xml:space="preserve">
    <value>Service date cannot be less than Current date</value>
  </data>
  <data name="CallDateCanNotBeGreaterThanCurrentDate" xml:space="preserve">
    <value>Call Date and Time cannot be greater than current Date and Time</value>
    <comment>Message</comment>
  </data>
  <data name="Operator" xml:space="preserve">
    <value>Operator</value>
    <comment>Label</comment>
  </data>
  <data name="PreffixSuffixDoesntExists" xml:space="preserve">
    <value>Preffix Suffix Doesnt Exists</value>
    <comment>Message</comment>
  </data>
  <data name="FromDatecannotbegreaterthanToDate" xml:space="preserve">
    <value>From Date cannot be greater than To Date</value>
    <comment>Message</comment>
  </data>
  <data name="deletedsuccessfully" xml:space="preserve">
    <value>Deleted Successfully</value>
  </data>
  <data name="enterfromdate" xml:space="preserve">
    <value>Enter From Date</value>
  </data>
  <data name="fromdatecannotbegreaterthentodate" xml:space="preserve">
    <value>From Date cannot be greater than To Date</value>
  </data>
  <data name="duplicatebranch" xml:space="preserve">
    <value>Duplicate Branch</value>
  </data>
  <data name="Jobamendedwillcreatenewversionwanttoproceed" xml:space="preserve">
    <value>Job amended, will create new version want to proceed?</value>
    <comment>Message</comment>
  </data>
  <data name="close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="open" xml:space="preserve">
    <value>Open</value>
    <comment>Label</comment>
  </data>
  <data name="JobcardArchived" xml:space="preserve">
    <value>Job card Archived</value>
    <comment>Label</comment>
  </data>
  <data name="editstate" xml:space="preserve">
    <value>Edit State</value>
  </data>
  <data name="InvalidPhone" xml:space="preserve">
    <value>Invalid Phone</value>
    <comment>Message</comment>
  </data>
  <data name="Duplicate" xml:space="preserve">
    <value>Duplicate</value>
  </data>
  <data name="alreadyexists" xml:space="preserve">
    <value>already exists</value>
  </data>
  <data name="ModelFieldSearch" xml:space="preserve">
    <value>Model Field Search</value>
  </data>
  <data name="PartyFielSearch" xml:space="preserve">
    <value>Party Field Search</value>
  </data>
  <data name="InvalidBranchSelection" xml:space="preserve">
    <value>Prefixsuffix is created as company specific. Cannot change to Branch.</value>
  </data>
  <data name="ReceivedCount" xml:space="preserve">
    <value>Received Count</value>
    <comment>Label</comment>
  </data>
  <data name="FilterAllQueBranch" xml:space="preserve">
    <value>Filter All Que Branch Specific ?</value>
    <comment>Label</comment>
  </data>
  <data name="Duplicateentriesof" xml:space="preserve">
    <value>Duplicate</value>
    <comment>Message</comment>
  </data>
  <data name="QuestInformaticsPrivateLimited" xml:space="preserve">
    <value>Quest Informatics Private Limited</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>Parts</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="VersionDate" xml:space="preserve">
    <value>Version Date</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>Version Number</value>
  </data>
  <data name="VersionNumberandDate" xml:space="preserve">
    <value>Version Number and Date</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>Re-Login</value>
  </data>
  <data name="of" xml:space="preserve">
    <value>of</value>
    <comment>Label</comment>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
    <comment>Label</comment>
  </data>
  <data name="Norecordstoview" xml:space="preserve">
    <value>No records to view</value>
    <comment>Label</comment>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>You have been Logged out successfully</value>
    <comment>Message</comment>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>Assign</value>
    <comment>Label</comment>
  </data>
  <data name="GroupQue" xml:space="preserve">
    <value>Group Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Lock" xml:space="preserve">
    <value>Lock</value>
    <comment>Label</comment>
  </data>
  <data name="MyQue" xml:space="preserve">
    <value>My Queue</value>
    <comment>Label</comment>
  </data>
  <data name="Others" xml:space="preserve">
    <value>Others</value>
    <comment>Label</comment>
  </data>
  <data name="UnLock" xml:space="preserve">
    <value>UnLock</value>
    <comment>Label</comment>
  </data>
  <data name="AND" xml:space="preserve">
    <value>AND</value>
    <comment>Label</comment>
  </data>
  <data name="DocumentExport" xml:space="preserve">
    <value>Document Export</value>
    <comment>Label</comment>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>Equal</value>
    <comment>Label</comment>
  </data>
  <data name="Excel" xml:space="preserve">
    <value>Excel</value>
    <comment>Label</comment>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
    <comment>Label</comment>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>Less Than</value>
    <comment>Label</comment>
  </data>
  <data name="Like" xml:space="preserve">
    <value>Like</value>
    <comment>Label</comment>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>Not Equal</value>
    <comment>Label</comment>
  </data>
  <data name="OR" xml:space="preserve">
    <value>OR</value>
    <comment>Label</comment>
  </data>
  <data name="PDF" xml:space="preserve">
    <value>PDF</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseenterIntegerValue" xml:space="preserve">
    <value>PleaseenterIntegerValue</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseselectaColumn" xml:space="preserve">
    <value>Please select a Column</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseselectaCondition" xml:space="preserve">
    <value>Please select a Condition</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseselectaOperator" xml:space="preserve">
    <value>Please select a Operator</value>
    <comment>Label</comment>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>Select Column</value>
    <comment>Label</comment>
  </data>
  <data name="SelectCondition" xml:space="preserve">
    <value>Select Condition</value>
    <comment>Label</comment>
  </data>
  <data name="SelectOperator" xml:space="preserve">
    <value>Select Operator</value>
    <comment>Label</comment>
  </data>
  <data name="ValueisMandatoryforselectedColumn" xml:space="preserve">
    <value>Value is Mandatory for selected Column</value>
    <comment>Label</comment>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
    <comment>Label</comment>
  </data>
  <data name="Where" xml:space="preserve">
    <value>Where</value>
    <comment>Label</comment>
  </data>
  <data name="TopModel" xml:space="preserve">
    <value>Top 10 Models</value>
    <comment>Label</comment>
  </data>
  <data name="Userdonthaveaccesstopartymaster" xml:space="preserve">
    <value>User dont have access to Party Master</value>
    <comment>Message</comment>
  </data>
  <data name="Userdonthaveaccesstoproductmaster" xml:space="preserve">
    <value>User dont have access to Product Master</value>
    <comment>Message</comment>
  </data>
  <data name="Userdonthaveeditaccesstoproductmaster" xml:space="preserve">
    <value>User dont have Edit access to Product Master</value>
    <comment>Message</comment>
  </data>
  <data name="FinancialYear" xml:space="preserve">
    <value>Financial Year</value>
  </data>
  <data name="CompanyFinancialYear" xml:space="preserve">
    <value>Company Financial Year</value>
    <comment>Label</comment>
  </data>
  <data name="financialyearalredyselected" xml:space="preserve">
    <value>Financial year already selected</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidCompanySelection" xml:space="preserve">
    <value>Prefixsuffix is created as Branch specific. Cannot change to Company. </value>
  </data>
  <data name="Personal" xml:space="preserve">
    <value>Personal</value>
    <comment>Label</comment>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
    <comment>Label</comment>
  </data>
  <data name="RequestNumber" xml:space="preserve">
    <value>Request Number</value>
    <comment>Label</comment>
  </data>
  <data name="Userdonthaveeditaccess" xml:space="preserve">
    <value>User dont have Edit Access</value>
  </data>
  <data name="Youdonothaveeditpermission" xml:space="preserve">
    <value>You do not have edit permission</value>
    <comment>Message</comment>
  </data>
  <data name="Defaultgridsizealreadyavailable" xml:space="preserve">
    <value>Default grid size already available</value>
    <comment>Message</comment>
  </data>
  <data name="TaxDetail" xml:space="preserve">
    <value>Tax Detail</value>
    <comment>Label</comment>
  </data>
  <data name="InvalidMobile" xml:space="preserve">
    <value>Invalid Mobile</value>
    <comment>Message</comment>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Not Found</value>
    <comment>Message</comment>
  </data>
  <data name="QuantityCannotbeZero" xml:space="preserve">
    <value>Quantity Cannot be Zero</value>
    <comment>Message</comment>
  </data>
  <data name="RateCannotbeZero" xml:space="preserve">
    <value>Rate Cannot be Zero</value>
    <comment>Message</comment>
  </data>
  <data name="Break" xml:space="preserve">
    <value>Break</value>
  </data>
  <data name="CompanyCalender" xml:space="preserve">
    <value>Company Calender</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Friday</value>
  </data>
  <data name="Holidays" xml:space="preserve">
    <value>Holidays</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Monday</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Saturday</value>
  </data>
  <data name="Shift" xml:space="preserve">
    <value>Shift</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>Sunday</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Thursday</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Tuesday</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Wednesday</value>
    <comment>Label</comment>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Working Days</value>
    <comment>Label</comment>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Working Time</value>
    <comment>Label</comment>
  </data>
  <data name="enteryear" xml:space="preserve">
    <value>Enter Year</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="selectcompany" xml:space="preserve">
    <value>Select Company</value>
  </data>
  <data name="selectshift" xml:space="preserve">
    <value>Select Shift</value>
  </data>
  <data name="RatingshouldBetween1and10" xml:space="preserve">
    <value>Rating should between 1 and 10</value>
  </data>
  <data name="FinancialyearcannotbeGreaterthannextrowsfinancialyear" xml:space="preserve">
    <value>Financial year cannot be greater than next rows Financial year</value>
    <comment>Message</comment>
  </data>
  <data name="Financialyearcannotbelessthanpreviousrowsfinancialyear" xml:space="preserve">
    <value>Financial year cannot be less than previous rows Financial year</value>
    <comment>Message</comment>
  </data>
  <data name="Fromdatecannotbelessthanorequaltopreviousrowtodate" xml:space="preserve">
    <value>From Date cannot be less than or equal to previous rows To Date</value>
    <comment>Message</comment>
  </data>
  <data name="alreadyexistsforthelocation" xml:space="preserve">
    <value>already exists for the location</value>
  </data>
  <data name="Userislocked" xml:space="preserve">
    <value>User is locked</value>
  </data>
  <data name="Userisnotactive" xml:space="preserve">
    <value>User is not active</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>Invalid</value>
  </data>
  <data name="isinactive" xml:space="preserve">
    <value>is Inactive</value>
  </data>
  <data name="CannotdeleteasTaxTypeisreferenced" xml:space="preserve">
    <value>Cannot delete as Tax Type is referenced</value>
  </data>
  <data name="BreakHourscannotbegreatorthanWorkingHours" xml:space="preserve">
    <value>Break Hours cannot be greator than Working Hours</value>
    <comment>Message</comment>
  </data>
  <data name="FromTimecannotbegreaterthanToTime" xml:space="preserve">
    <value>From Time cannot be greater than To Time</value>
    <comment>Message</comment>
  </data>
  <data name="duplicatedate" xml:space="preserve">
    <value>Duplicate Date</value>
  </data>
  <data name="Yearshouldbebetween2000and2999" xml:space="preserve">
    <value>Year should be between 2000 and 2999</value>
  </data>
  <data name="DuplicateCode" xml:space="preserve">
    <value>Duplicate Code</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateCode1" xml:space="preserve">
    <value>Duplicate Description</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateCodeandDescription" xml:space="preserve">
    <value>Duplicate Code and Description</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateCodeandDescription1" xml:space="preserve">
    <value>Duplicate Code and Description</value>
    <comment>Message</comment>
  </data>
  <data name="ServiceCode" xml:space="preserve">
    <value>Service Code</value>
    <comment>Label</comment>
  </data>
  <data name="Duplicateentries" xml:space="preserve">
    <value>Duplicate Entries</value>
  </data>
  <data name="Queue" xml:space="preserve">
    <value>Queue</value>
    <comment>Label</comment>
  </data>
  <data name="DuplicateEmployee" xml:space="preserve">
    <value>Duplicate Employee</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateFunctionGroup" xml:space="preserve">
    <value>Duplicate Function Group</value>
    <comment>Message</comment>
  </data>
  <data name="BranchNameisalreadypresent" xml:space="preserve">
    <value>Duplicate Branch Name</value>
  </data>
  <data name="Pleaseenterserailnumber" xml:space="preserve">
    <value>Please enter serial number</value>
    <comment>Message</comment>
  </data>
  <data name="EditOperationDetails" xml:space="preserve">
    <value>Edit Operation Details</value>
    <comment>Message</comment>
  </data>
  <data name="TopModelwithMaximumSR" xml:space="preserve">
    <value>Top 10 Models with Maximum Service Request</value>
    <comment>Label</comment>
  </data>
  <data name="SRBasedonCalltype" xml:space="preserve">
    <value>Service Request Based on Call Type</value>
  </data>
  <data name="CustomerQuotationArchived" xml:space="preserve">
    <value>Customer Quotation Archived</value>
  </data>
  <data name="AreyousureyouwanttoLogout" xml:space="preserve">
    <value>Are you sure you want to logout</value>
  </data>
  <data name="PleaseselectServicerequestnumber" xml:space="preserve">
    <value>Please select service request number</value>
    <comment>Message</comment>
  </data>
  <data name="inactiveproduct" xml:space="preserve">
    <value>In Active Product</value>
  </data>
  <data name="Noeditpermissionforproductmaster" xml:space="preserve">
    <value>No Edit permission for Product Master</value>
    <comment>Message</comment>
  </data>
  <data name="Abandon" xml:space="preserve">
    <value>Abandon</value>
    <comment>Label</comment>
  </data>
  <data name="EventStartDateCannotbeLessthanJobCardDate" xml:space="preserve">
    <value>Event start date cannot be less than Job card date</value>
    <comment>Message</comment>
  </data>
  <data name="cannotclosethejobwithoutproductdetails" xml:space="preserve">
    <value>Cannot close the job without product details</value>
    <comment>Message</comment>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
    <comment>Label</comment>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Old Password</value>
    <comment>Label</comment>
  </data>
  <data name="IncompleteOperationsarepresent" xml:space="preserve">
    <value>Incomplete operations are present, do you want to close the Job card?</value>
    <comment>Message</comment>
  </data>
  <data name="IncorrectPassword" xml:space="preserve">
    <value>Incorrect Password</value>
  </data>
  <data name="newpasswordandConfirmpasswordarenotmatching" xml:space="preserve">
    <value>New Password and Confirm Password are not matching</value>
  </data>
  <data name="OperationStartdatecannotbelessthanJobcarddate" xml:space="preserve">
    <value>Operation start date cannot be less than job card date</value>
    <comment>Message</comment>
  </data>
  <data name="Eventstarttimecoincideswithothereventsdoyouwanttocontinue" xml:space="preserve">
    <value>Event start time coincides with other events, do you want to continue?</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseaddatleastoneoperationdetail" xml:space="preserve">
    <value>Please add atleast one operation detail</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidSerialNumber" xml:space="preserve">
    <value>Invalid serial number</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidDecimal" xml:space="preserve">
    <value>Invalid Decimal</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseEnterDateAs" xml:space="preserve">
    <value>Please Enter Date As</value>
  </data>
  <data name="PartySchedule" xml:space="preserve">
    <value>Party Schedule</value>
  </data>
  <data name="ProductSchedule" xml:space="preserve">
    <value>Product Schedule</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Schedule Type</value>
  </data>
  <data name="InvalidQuantity" xml:space="preserve">
    <value>Invalid Quantity.</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidRate" xml:space="preserve">
    <value>Invalid Rate.</value>
    <comment>Message</comment>
  </data>
  <data name="PartAlreadyAdded" xml:space="preserve">
    <value>Part Already Added.</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumberalreadyselected" xml:space="preserve">
    <value>Part Number already selected</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumberisBlank" xml:space="preserve">
    <value>Part Number is blank.</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumbernotassociatedtotheselectedproductdetails" xml:space="preserve">
    <value>Part Number not associated to the selected product details.</value>
    <comment>Message</comment>
  </data>
  <data name="Quicklinks" xml:space="preserve">
    <value>Quick Links</value>
  </data>
  <data name="PersonalCalender" xml:space="preserve">
    <value>Personal Calendar</value>
    <comment>Label</comment>
  </data>
  <data name="capslockison" xml:space="preserve">
    <value>Caps Lock is on</value>
    <comment>Label</comment>
  </data>
  <data name="CCToAssignee" xml:space="preserve">
    <value>CC To Assignee</value>
  </data>
  <data name="EscalateTo" xml:space="preserve">
    <value>Escalate To</value>
  </data>
  <data name="EscalationHours" xml:space="preserve">
    <value>Escalation Hours</value>
  </data>
  <data name="IsEmail" xml:space="preserve">
    <value>Is Email</value>
  </data>
  <data name="IsSms" xml:space="preserve">
    <value>Is Sms</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
</root>