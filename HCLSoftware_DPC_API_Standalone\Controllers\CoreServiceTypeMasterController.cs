﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreServiceTypeMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreServiceTypeMasterController : ApiController
    {
        #region ::: Select Particular ServiceType in edit mode /Mithun:::
        /// <summary>
        /// To Select Particular Service Type in edit mode
        /// </summary> 
        [Route("api/CoreServiceTypeMaster/SelectParticularServiceTypeID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularServiceTypeID([FromBody] SelectParticularServiceTypeIDList SelectParticularServiceTypeIDObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.SelectParticularServiceTypeID(SelectParticularServiceTypeIDObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        //grid
        #region ::: Load Service Type Landing Grid /Mithun:::
        /// <summary>
        /// To Load Service type Landing Grid
        /// </summary>   

        [Route("api/CoreServiceTypeMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreServicesList SelectObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceTypeMasterServices.Select(SelectObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: Insert /Mithun:::
        /// <summary>
        /// ServiceType Insert
        /// </summary>  
        [Route("api/CoreServiceTypeMaster/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertCoreServiceTypeList InsertObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.Insert(InsertObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Update /Mithun:::
        /// <summary>
        /// ServiceType Update
        /// </summary>   
        [Route("api/CoreServiceTypeMaster/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateCoreServiceList UpdateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.Update(UpdateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete /Mithun:::
        /// <summary>
        /// ServiceType Delete
        /// </summary>   
        [Route("api/CoreServiceTypeMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreServiceList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select ServiceType Locale /Mithun :::
        /// <summary>
        /// to Load Service Type Locale
        /// </summary>   
        [Route("api/CoreServiceTypeMaster/SelServiceTypeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelServiceTypeLocale([FromBody] SelServiceTypeLocaleList SelServiceTypeLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.SelServiceTypeLocale(SelServiceTypeLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Service Type Native /Mihtun:::
        /// <summary>
        /// to Select ServiceType Native
        /// </summary>  
        [Route("api/CoreServiceTypeMaster/SelServiceTypeNative")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelServiceTypeNative([FromBody] SelServiceTypeNativeList SelServiceTypeNativeObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceTypeMasterServices.SelServiceTypeNative(SelServiceTypeNativeObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return (Response.Value);

        }
        #endregion

        #region ::: Insert Service Type Locale /Mithun:::
        /// <summary>
        /// to Insert ServiceType Locale
        /// </summary> 
        [Route("api/CoreServiceTypeMaster/InsertServiceTypeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertServiceTypeLocale([FromBody] InsertServiceTypeLocaleList InsertServiceTypeLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.InsertServiceTypeLocale(InsertServiceTypeLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Update Service Type Locale /Mithun:::
        /// <summary>
        /// to Update ServiceType Locale
        /// </summary> 
        [Route("api/CoreServiceTypeMaster/UpdateServiceTypeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateServiceTypeLocale([FromBody] UpdateServiceTypeLocaleList UpdateServiceTypeLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.UpdateServiceTypeLocale(UpdateServiceTypeLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Check Duplicate ServiceType /Mithun:::
        /// <summary>
        /// to check if the Service type is already exists or not
        /// </summary>
        [Route("api/CoreServiceTypeMaster/CheckServiceType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckServiceType([FromBody] CheckServiceTypeList CheckServiceTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.CheckServiceType(CheckServiceTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectServiceTypeOperationDetails /Mithun:::
        /// <summary>
        /// to Select ServiceType Operati on Details
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreServiceTypeMaster/SelectServiceTypeOperationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceTypeOperationDetails([FromBody] SelectServiceTypeOperationDetailsList SelectServiceTypeOperationDetailsObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreServiceTypeMasterServices.SelectServiceTypeOperationDetails(SelectServiceTypeOperationDetailsObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return (Response.Value);

        }
        #endregion

        #region ::: ChangeOperationCode /Mithun:::
        /// <summary>
        /// TO Change OperationCode
        /// </summary>
        [Route("api/CoreServiceTypeMaster/ChangeOperationCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ChangeOperationCode([FromBody] ChangeOperationCodeList ChangeOperationCodeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.ChangeOperationCode(ChangeOperationCodeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: SaveServiceTypeOperationDetails /Mithun:::
        /// <summary>
        /// to Save ServiceType Operaton Details
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreServiceTypeMaster/SaveServiceTypeOperationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveServiceTypeOperationDetails([FromBody] SaveServiceTypeOperationDetailsList SaveServiceTypeOperationDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.SaveServiceTypeOperationDetails(SaveServiceTypeOperationDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: DeleteServiceTypeOperationDetails /Mithun:::
        /// <summary>
        /// to Delete ServiceType Operation Details
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreServiceTypeMaster/DeleteServiceTypeOperationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteServiceTypeOperationDetails([FromBody] DeleteServiceTypeOperationDetailsList DeleteServiceTypeOperationDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreServiceTypeMasterServices.DeleteServiceTypeOperationDetails(DeleteServiceTypeOperationDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Export /Mithun :::

        [Route("api/CoreServiceTypeMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectCoreServicesList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;
            try
            {


                object Response = await CoreServiceTypeMasterServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion



    }
}
