using PBC.UtilityService.Utilities.Models;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Interface for ACL Properties service operations
    /// </summary>
    public interface IACLPropertiesService
    {
        /// <summary>
        /// Gets all ACL Properties
        /// </summary>
        /// <returns>Collection of ACL Properties</returns>
        Task<IEnumerable<ACLPropertiesDto>> GetAllAsync();

        /// <summary>
        /// Gets ACL Properties by Object ID
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <returns>ACL Properties if found, null otherwise</returns>
        Task<ACLPropertiesDto?> GetByObjectIdAsync(int objectId);

        /// <summary>
        /// Creates new ACL Properties
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Created ACL Properties</returns>
        Task<ACLPropertiesDto> CreateAsync(CreateACLPropertiesRequest request);

        /// <summary>
        /// Updates existing ACL Properties
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <param name="request">Update request</param>
        /// <returns>Updated ACL Properties if found, null otherwise</returns>
        Task<ACLPropertiesDto?> UpdateAsync(int objectId, UpdateACLPropertiesRequest request);

        /// <summary>
        /// Deletes ACL Properties by Object ID
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <returns>True if deleted, false if not found</returns>
        Task<bool> DeleteAsync(int objectId);

        /// <summary>
        /// Checks if ACL Properties exists for the given Object ID
        /// </summary>
        /// <param name="objectId">The Object ID</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(int objectId);
    }
}
