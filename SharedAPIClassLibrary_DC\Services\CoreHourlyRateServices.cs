﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Xml.Linq;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace SharedAPIClassLibrary_AMERP
{
    public class CoreHourlyRateServices
    {
        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update Hourly Rate
        /// </summary>
        public static IActionResult Save(SaveHourlyRateList SaveObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                int BranchID = Convert.ToInt32(SaveObj.Branch);
                //  GNM_User User = (GNM_User)SaveObj.UserDetails.FirstOrDefault();
                JObject jObj = JObject.Parse(SaveObj.data);
                int Count = jObj["rows"].Count();

                // Create XML from JSON data
                XElement ratesXml = new XElement("Rates",
                    from row in jObj["rows"]
                    select new XElement("Rate",
                        new XAttribute("HourlyRate_ID", (int)row["HourlyRate_ID"]),
                        new XAttribute("HourlyRate", (decimal)row["HourlyRate"]),
                        new XAttribute("EffectiveDate", (DateTime)row["EffectiveDate"]),
                        new XAttribute("Company_ID", Convert.ToInt32(SaveObj.Company_ID)),
                        new XAttribute("Branch_ID", Convert.ToInt32(SaveObj.Branch)),
                        new XAttribute("Modifiedby", Convert.ToInt32(SaveObj.User_ID)),
                        new XAttribute("ModifiedDate", DateTime.Now)
                    )
                );

                // Use ADO.NET to call the stored procedure
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveHourlyRate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@HourlyRatesXml", SqlDbType.Xml) { Value = ratesXml.ToString() });

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                Msg = string.Empty;
            }
            //return Msg;
            return new JsonResult(Msg);
        }


        #endregion

        #region ::: Select Landing grid /Mithun:::
        /// <summary>
        /// Select Landing grid
        /// </summary>
        private static IQueryable<HourlyRateMaster> GetAllHourlyRate(GetAllHourlyRateList GetAllHourlyRateObj, string constring, int LogException, int CompID)
        {
            int Company_ID = CompID;

            List<HourlyRateMaster> hourlyRateMasters = new List<HourlyRateMaster>();

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetAllHourlyRate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID });

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                hourlyRateMasters.Add(new HourlyRateMaster
                                {
                                    HourlyRate_ID = reader.GetInt32(0),
                                    HourlyRate = reader.GetDecimal(1),
                                    EffectiveDate = reader.GetDateTime(2),
                                    EffectiveDateSort = reader.GetDateTime(2).ToString("dd-MMM-yyyy"),
                                    HourlyRateSort = reader.GetDecimal(1).ToString("0.00")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return hourlyRateMasters.AsQueryable();
        }

        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// Select
        /// </summary>
        public static IActionResult Select(SelectHourlyRateList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonData = default(dynamic);
            IQueryable<HourlyRateMaster> IQHourlyRateMaster = null;
            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectObj.Company_ID);

                GetAllHourlyRateList GetAllHourlyRateObj = new GetAllHourlyRateList();
                IQHourlyRateMaster = GetAllHourlyRate(GetAllHourlyRateObj, constring, LogException, Company_ID);

                if (_search)
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    IQHourlyRateMaster = IQHourlyRateMaster.FilterSearch<HourlyRateMaster>(filtersobj);

                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQHourlyRateMaster = IQHourlyRateMaster.AdvanceSearch<HourlyRateMaster>(advnfilter);
                }

                IQHourlyRateMaster = IQHourlyRateMaster.OrderByField<HourlyRateMaster>(sidx, sord);

                Count = IQHourlyRateMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQHourlyRateMaster.AsEnumerable()
                            select new
                            {
                                HourlyRate_ID = a.HourlyRate_ID,
                                //edit = "<img id='" + a.HourlyRate_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.HourlyRate_ID + "' class='StateEdit' editmode='false'/>",
                                edit = "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.HourlyRate_ID + "' key='" + a.HourlyRate_ID + "' class='StateEdit' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.HourlyRate_ID + "' defaultchecked=''  id='chk" + a.HourlyRate_ID + "' class='StateDelete'/>",
                                HourlyRate = a.HourlyRate,
                                a.EffectiveDate,
                                a.EffectiveDateSort,
                                a.HourlyRateSort
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    // filter = Request.Params["filters"],
                    // advanceFilter = Request.Params["Query"],
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }
        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete State
        /// </summary>
        public static IActionResult Delete(DeleteHourlyRateList DeleteObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            var Culture = "Resource_" + DeleteObj.Lang;
            try
            {
                int BranchID = Convert.ToInt32(DeleteObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = DeleteObj.UserDetails.FirstOrDefault();
                JObject jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        int ID = (int)jObj["rows"][i]["id"];

                        // Delete the record using ADO.NET
                        using (SqlCommand cmd = new SqlCommand("DELETE FROM GNM_HourlyRate WHERE HourlyRate_ID = @ID", conn))
                        {
                            cmd.Parameters.Add(new SqlParameter("@ID", SqlDbType.Int) { Value = ID });
                            cmd.ExecuteNonQuery();
                        }

                        // Log the deletion
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(DeleteObj.Company_ID),
                        //    BranchID,
                        //    User.User_ID,
                        //    Common.GetObjectID("CoreHourlyRate",constring),
                        //    ID,
                        //    0,
                        //    0,
                        //    "Deleted Hourly Rate- " + ID,
                        //    false,
                        //    Convert.ToInt32(DeleteObj.MenuID),
                        //    Convert.ToDateTime(DeleteObj.LoggedINDateTime)
                        //);
                    }
                }

                Msg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    // Log other SQL exceptions
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            catch (Exception ex)
            {
                // Log general exceptions
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            //return Msg;
            return new JsonResult(Msg);
        }
        #endregion

        #region ::: getPreviousDate /Mithun:::
        /// <summary>
        /// getPreviousDate
        /// </summary>   
        public static IActionResult getPreviousEffectiveFromDate(getPreviousEffectiveFromDateList getPreviousEffectiveFromDateObj, string constring, int LogException)
        {
            var JsonData = default(dynamic);
            string CurDate = "";
            int Company_ID = Convert.ToInt32(getPreviousEffectiveFromDateObj.Company_ID);

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_getPreviousEffectiveFromDate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID });

                        if (string.IsNullOrEmpty(getPreviousEffectiveFromDateObj.Currenteditmode))
                        {
                            cmd.Parameters.Add(new SqlParameter("@HourlyRate_ID", SqlDbType.Int) { Value = DBNull.Value });
                        }
                        else
                        {
                            cmd.Parameters.Add(new SqlParameter("@HourlyRate_ID", SqlDbType.Int) { Value = getPreviousEffectiveFromDateObj.ID });
                        }

                        conn.Open();
                        object result = cmd.ExecuteScalar();

                        if (result != null && result != DBNull.Value)
                        {
                            DateTime effectiveDate = (DateTime)result;
                            CurDate = effectiveDate.ToString("dd-MMM-yyyy");
                        }
                        else
                        {
                            CurDate = "";
                        }
                    }
                }

                JsonData = new
                {
                    CurDate
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(JsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(JsonData);
        }

        #endregion



        public class getPreviousEffectiveFromDateList
        {
            public int Company_ID { get; set; }
            public int ID { get; set; }
            public string Currenteditmode { get; set; }
        }
        public class DeleteHourlyRateList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectHourlyRateList
        {
            public int Company_ID { get; set; }
        }
        public class GetAllHourlyRateList
        {
            public int Company_ID { get; set; }
        }
        public class HourlyRateMaster
        {
            public int HourlyRate_ID { get; set; }
            public decimal HourlyRate { get; set; }
            public string HourlyRateSort { get; set; }
            public DateTime EffectiveDate { get; set; }
            public string EffectiveDateSort { get; set; }
        }
        public class SaveHourlyRateList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
    }
}
