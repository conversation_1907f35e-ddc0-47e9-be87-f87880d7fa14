﻿using System.Web.Http;
using System.Web.Http.Cors;

namespace HCLSoftware_DPC_API_Standalone
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // Add authentication configuration



            //  config.Filters.Add(new JwtTokenValidationFilterAttribute());


            // Web API configuration and services
            // Enable CORS
            var cors = new EnableCorsAttribute("*", "*", "*");
            config.EnableCors(cors);

            // Web API routes
            config.MapHttpAttributeRoutes();

            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "api/{controller}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );


            //config.MessageHandlers.Add(new JwtTokenValidationHandler());


            //    config.Routes.MapHttpRoute(
            //    name: "DefaultA<PERSON>",
            //    routeTemplate: "api/{controller}/{action}",
            //    defaults: new { action = "Get" } // You can set a default action if needed
            //);

        }
    }
}
