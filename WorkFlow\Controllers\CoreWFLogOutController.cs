﻿using WorkFlow.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace WorkFlow.Controllers
{
    public class CoreWFLogOutController : Controller
    {
        //
        // GET: /CoreLogOut/
        public ActionResult LogOutView()
        {
            //GeneralEntities GEClient = new GeneralEntities();

            //GNM_User gu = GEClient.GNM_User.Where(uid => uid.User_ID == Convert.ToInt32(Session["User_ID"])).FirstOrDefault();
            //gu.User_Locked = false;
            //GEClient.SaveChanges();
            
            Session.Abandon();
            Session.Clear();
            Session.RemoveAll();
            return View("~/Views/Core/LogOutView.cshtml");
        }

    }
}
