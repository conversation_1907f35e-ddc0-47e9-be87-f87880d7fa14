﻿#region Assembly WorkFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// C:\Users\<USER>\Downloads\AM_ERP 1\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\AMMSCore\bin\WorkFlow.dll
// Decompiled with ICSharpCode.Decompiler 8.1.1.7464
#endregion

namespace WorkFlow.Models
{
    public class rules
    {
        public string field { get; set; }

        public string data { get; set; }
    }
#if false // Decompilation log
    '57' items in cache
    ------------------
    Resolve: 'mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll'
    ------------------
    Resolve: 'System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.Mvc.4.0.20710.0\lib\net40\System.Web.Mvc.dll'
    ------------------
    Resolve: 'System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Found single assembly: 'System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dll'
    ------------------
    Resolve: 'EntityFramework, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'EntityFramework, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Users\<USER>\Downloads\AM_ERP 1\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\AMMSCore\bin\EntityFramework.dll'
    ------------------
    Resolve: 'Newtonsoft.Json, Version=4.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed'
    Found single assembly: 'Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed'
    WARN: Version mismatch. Expected: '4.5.0.0', Got: '13.0.0.0'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll'
    ------------------
    Resolve: 'System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll'
    ------------------
    Resolve: 'System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Entity.dll'
    ------------------
    Resolve: 'System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll'
    ------------------
    Resolve: 'System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.WebApi.Core.4.0.20505.0\lib\net40\System.Web.Http.dll'
    ------------------
    Resolve: 'System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll'
    ------------------
    Resolve: 'LogSheetExporter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
    Found single assembly: 'LogSheetExporter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
    Load from: 'C:\Users\<USER>\Downloads\AM_ERP 1\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\AMMSCore\bin\LogSheetExporter.dll'
    ------------------
    Resolve: 'Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Found single assembly: 'Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll'
    ------------------
    Resolve: 'System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Found single assembly: 'System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll'
    ------------------
    Resolve: 'System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.WebApi.WebHost.4.0.20505.0\lib\net40\System.Web.Http.WebHost.dll'
    ------------------
    Resolve: 'System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.dll'
    ------------------
    Resolve: 'System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dll'
    ------------------
    Resolve: 'System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dll'
#endif
}
