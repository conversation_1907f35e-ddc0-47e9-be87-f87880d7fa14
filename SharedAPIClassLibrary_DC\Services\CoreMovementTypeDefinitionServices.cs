﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace SharedAPIClassLibrary_AMERP
{
    public class CoreMovementTypeDefinitionServices
    {

        #region ::: Select:::
        /// <summary>
        /// To select the All Movement type defination 
        /// </summary>        
        public static IActionResult Select(SelectMovementtypList SelectObj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            int count = 0;
            int total = 0;
            int LangID = Convert.ToInt32(SelectObj.UserLanguageID);
            var x = default(dynamic);


            IEnumerable<GNM_MovementTypeDefinition> liMovementType = null;
            IQueryable<PRMMovementType> iQPrty = null;
            IEnumerable<PRMMovementType> iEPrty = null;
            List<PRMMovementType> movementTypeList = new List<PRMMovementType>();

            List<GNM_MovementType> MovementTypeList = null;
            string Select = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "select").ToString();
            string Yes = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "Yes").ToString();
            string No = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "no").ToString();
            string Description = "-1:--" + Select + "--;";
            try
            {

                int CompanyID = SelectObj.Company_ID;
                int BranchId = Convert.ToInt32(SelectObj.Branch);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SELECT_CoreMovementTypeDefinition";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LangID", LangID);
                            command.Parameters.AddWithValue("@UserLanguageID", Convert.ToInt32(LangID));
                            command.Parameters.AddWithValue("@GeneralLanguageID", SelectObj.GeneralLanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data
                                while (reader.Read())
                                {
                                    var movementTypeData = new PRMMovementType
                                    {
                                        MovementType_ID = reader.GetInt32(reader.GetOrdinal("MovementType_ID")),
                                        MovementType_Description = reader.GetString(reader.GetOrdinal("MovementType_Description")),
                                        UnMoved = reader.GetInt32(reader.GetOrdinal("UnMoved")),
                                        Slow = reader.GetInt32(reader.GetOrdinal("Slow")),
                                        Medium = reader.GetInt32(reader.GetOrdinal("Medium")),
                                        Fast = reader.GetInt32(reader.GetOrdinal("Fast")),
                                        IsActive = reader.GetString(reader.GetOrdinal("IsActive")),
                                    };
                                    movementTypeList.Add(movementTypeData);
                                }

                                // Move to the next result set for DESCRIPTION
                                if (reader.NextResult() && reader.Read())
                                {
                                    Description = reader.IsDBNull(reader.GetOrdinal("DESCRIPTION")) ? null : reader.GetString(reader.GetOrdinal("DESCRIPTION"));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                iQPrty = movementTypeList.AsQueryable<PRMMovementType>();
                //FilterToolBar Search
                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        iQPrty = iQPrty.FilterSearch<PRMMovementType>(filtersObj);
                }//Advance Search
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<AdvanceFilter>();
                    iQPrty = iQPrty.AdvanceSearch<PRMMovementType>(advnfilter);
                }
                //Sorting 
                iQPrty = iQPrty.OrderByField<PRMMovementType>(sidx, sord);
                //Session["MovementTypeDefExport"] = iQPrty;
                count = iQPrty.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in iQPrty
                            select new
                            {
                                //edit = "<img id='" + a.MovementType_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.MovementType_ID + "' class='editMovementTypeMaster' editmode='false'/>",
                                edit = "<a title='Edit' href='#' id='" + a.MovementType_ID + "' key='" + a.MovementType_ID + "'  editmode='false' class='editMovementTypeMaster'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.MovementType_ID + "' id='chk" + a.MovementType_ID + "' class='chkDelMovementTypeMaster'/>",
                                ID = a.MovementType_ID,
                                MovementType_Description = a.MovementType_Description,
                                UnMoved = a.UnMoved,
                                Slow = a.Slow,
                                Medium = a.Medium,
                                Fast = a.Fast,
                                IsActive = a.IsActive,
                            }).ToList().Paginate(page, rows),
                    Description
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion
        #region ::: Insert:::
        /// <summary>
        /// Method to insert the MovementTypeDefination Master Header Table
        /// </summary>   
        public static IActionResult Insert(InsertMovementTypeDefinationList InsObj, string connString, int LogException)
        {
            var x = default(dynamic);
            JTokenReader jsonReader = null;
            var jsonResult = default(dynamic);
            int? MovementTypeDefinition_ID = 0;
            try
            {

                JObject jObj = JObject.Parse(InsObj.key);
                int count = jObj["rows"].Count();
                for (int i = 0; i < count; i++)
                {
                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ID"]);
                    jsonReader.Read();
                    string MoventTypeID = jsonReader.Value.ToString();

                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Desc"]);
                    jsonReader.Read();
                    int Description = Convert.ToInt32(jsonReader.Value.ToString());

                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["UnMoved"]);
                    jsonReader.Read();
                    int UnMoved = Convert.ToInt32(jsonReader.Value.ToString());

                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Slow"]);
                    jsonReader.Read();
                    int Slow = Convert.ToInt32(jsonReader.Value.ToString());

                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Medium"]);
                    jsonReader.Read();
                    int Medium = Convert.ToInt32(jsonReader.Value.ToString());

                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Fast"]);
                    jsonReader.Read();
                    int Fast = Convert.ToInt32(jsonReader.Value.ToString());

                    jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IsActive"]);
                    jsonReader.Read();
                    string IsActive = jsonReader.Value.ToString();


                    int? movementTypeID = string.IsNullOrEmpty(MoventTypeID) ? (int?)null : Convert.ToInt32(MoventTypeID);




                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_INSERTUPDATE_AM_ERP_Insert_CoreMovementTypeDefinition";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@MovementTypeDefinition_ID", movementTypeID);
                                command.Parameters.AddWithValue("@Description", Description);
                                command.Parameters.AddWithValue("@UnMoved", UnMoved);
                                command.Parameters.AddWithValue("@Slow", Slow);
                                command.Parameters.AddWithValue("@Medium", Medium);
                                command.Parameters.AddWithValue("@Fast", Medium);
                                command.Parameters.AddWithValue("@IsActive", IsActive == "true" ? true : false);
                                command.Parameters.AddWithValue("@Company_ID", InsObj.Company_ID);
                                command.Parameters.AddWithValue("@Branch_ID", InsObj.Branch);
                                command.Parameters.AddWithValue("@ModifiedBy", InsObj.User_ID);
                                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    // First result set: MovementTypeDefinition data
                                    while (reader.Read())
                                    {
                                        MovementTypeDefinition_ID = reader.IsDBNull(reader.GetOrdinal("MovementTypeDefinition_ID"))
                                       ? (int?)null
                                       : reader.GetInt32(reader.GetOrdinal("MovementTypeDefinition_ID"));

                                    }
                                    x = new
                                    {
                                        MoventTypeID = MovementTypeDefinition_ID
                                    };
                                    //if (MoventTypeID != "")
                                    //{

                                    //    // gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreMovementTypeDefinition")), UpdMovementType.MovementTypeDefinition_ID, 0, 0, "Update", false);
                                    //    gbl.InsertGPSDetails(Convert.ToInt32(InsObj.Company_ID.ToString()), Convert.ToInt32(InsObj.Branch), InsObj.User_ID, Common.GetObjectID("CoreMovementTypeDefinition"), MovementTypeDefinition_ID, 0, 0, "Updated Movement Type Definition ", false, Convert.ToInt32(InsObj.MenuID), Convert.ToDateTime(InsObj.LoggedINDateTime));

                                    //}
                                    //else
                                    //{

                                    //    //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreMovementTypeDefinition")), InsMovementType.MovementTypeDefinition_ID, 0, 0, "Insert", false);
                                    //    gbl.InsertGPSDetails(Convert.ToInt32(InsObj.Company_ID.ToString()), Convert.ToInt32(InsObj.Branch), InsObj.User_ID, Common.GetObjectID("CoreMovementTypeDefinition"), MovementTypeDefinition_ID, 0, 0, "Inserted Movement Type Definition ", false, Convert.ToInt32(InsObj.MenuID), Convert.ToDateTime(InsObj.LoggedINDateTime));

                                    //}


                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion
        #region ::: Delete :::
        /// <summary>
        /// to Delete the Movement type defination
        /// </summary>
        public static IActionResult Delete(DeleteMovementtypedefinationList DeleteObj, string connString, int LogException)
        {
            string errorMsg = "";
            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(DeleteObj.key);
                int rowCount = jobj["rows"].Count();

                GNM_MovementTypeDefinition deleteRow = null;
                int id = 0;
                for (int i = 0; i < rowCount; i++)
                {
                    reader = new JTokenReader(jobj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    reader.Read();
                    id = Convert.ToInt32(reader.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_Delete_CoreMovementTypeDefinition";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@MovementTypeDefinition_ID", id);




                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                }


                errorMsg = CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg = CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(errorMsg);
        }
        #endregion
        #region ::: ValidateMovementType :::
        /// <summary>
        /// to check duplicate movement type
        /// </summary>
        public static IActionResult ValidateMovementType(ValidateMovementTypeList ValidateMovementTypeObj, string connString, int LogException)
        {
            int status = 0;
            try
            {

                int BranchId = Convert.ToInt32(ValidateMovementTypeObj.Branch);

                //List<GNM_MovementTypeDefinition> checkRow = MovementTypeClient.GNM_MovementTypeDefinition.Where(i => i.Company_ID == User.Company_ID && i.MovementType_ID == MtypeID && i.Branch_ID == BranchId && i.MovementTypeDefinition_ID != primaryKey).ToList();
                List<GNM_MovementTypeDefinition> checkRow = new List<GNM_MovementTypeDefinition>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Validate_AM_ERP_ValidateMovementType_CoreMovementTypeDefinition";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", ValidateMovementTypeObj.Company_ID);
                            command.Parameters.AddWithValue("@MovementType_ID", ValidateMovementTypeObj.MtypeID);
                            command.Parameters.AddWithValue("@Branch_ID", BranchId);
                            command.Parameters.AddWithValue("@PrimaryKey", ValidateMovementTypeObj.primaryKey);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data
                                while (reader.Read())
                                {
                                    GNM_MovementTypeDefinition movementTypeDefinition = new GNM_MovementTypeDefinition
                                    {
                                        MovementTypeDefinition_ID = reader.GetInt32(reader.GetOrdinal("MovementTypeDefinition_ID")),
                                        MovementType_ID = reader.GetInt32(reader.GetOrdinal("MovementType_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                        IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                        ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                        Fast = reader.GetInt32(reader.GetOrdinal("Fast")),
                                        ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                        Unmoved = reader.GetInt32(reader.GetOrdinal("Unmoved")),
                                        Slow = reader.GetInt32(reader.GetOrdinal("Slow")),
                                        Medium = reader.GetInt32(reader.GetOrdinal("Medium"))
                                    };

                                    checkRow.Add(movementTypeDefinition);

                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                if (checkRow.Count() > 0)
                    status = 1;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return new JsonResult(status);
        }
        #endregion
        #region ::: Export :::
        /// <summary>
        /// Exporting Movement type defination Grid
        /// </summary>
        public static IQueryable<PRMMovementType> SelectList(SelectMovementtypList SelectObj, string constring, int LogException)
        {
            int count = 0;
            int total = 0;
            int LangID = Convert.ToInt32(SelectObj.UserLanguageID);
            var x = default(dynamic);


            IEnumerable<GNM_MovementTypeDefinition> liMovementType = null;
            IQueryable<PRMMovementType> iQPrty = null;
            IEnumerable<PRMMovementType> iEPrty = null;
            List<PRMMovementType> movementTypeList = new List<PRMMovementType>();

            List<GNM_MovementType> MovementTypeList = null;
            string Select = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "select").ToString();
            string Yes = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "Yes").ToString();
            string No = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "no").ToString();
            string Description = "-1:--" + Select + "--;";
            try
            {

                int CompanyID = SelectObj.Company_ID;
                int BranchId = Convert.ToInt32(SelectObj.Branch);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_SELECT_AM_ERP_SELECT_CoreMovementTypeDefinition";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LangID", LangID);
                            command.Parameters.AddWithValue("@UserLanguageID", Convert.ToInt32(LangID));
                            command.Parameters.AddWithValue("@GeneralLanguageID", SelectObj.GeneralLanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data
                                while (reader.Read())
                                {
                                    var movementTypeData = new PRMMovementType
                                    {
                                        MovementType_ID = reader.GetInt32(reader.GetOrdinal("MovementType_ID")),
                                        MovementType_Description = reader.GetString(reader.GetOrdinal("MovementType_Description")),
                                        UnMoved = reader.GetInt32(reader.GetOrdinal("UnMoved")),
                                        Slow = reader.GetInt32(reader.GetOrdinal("Slow")),
                                        Medium = reader.GetInt32(reader.GetOrdinal("Medium")),
                                        Fast = reader.GetInt32(reader.GetOrdinal("Fast")),
                                        IsActive = reader.GetString(reader.GetOrdinal("IsActive")),
                                    };
                                    movementTypeList.Add(movementTypeData);
                                }

                                // Move to the next result set for DESCRIPTION
                                if (reader.NextResult() && reader.Read())
                                {
                                    Description = reader.IsDBNull(reader.GetOrdinal("DESCRIPTION")) ? null : reader.GetString(reader.GetOrdinal("DESCRIPTION"));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                iQPrty = movementTypeList.AsQueryable<PRMMovementType>();
                //FilterToolBar Search
                if (SelectObj._search)
                {
                    string decodedValue = Uri.UnescapeDataString(SelectObj.filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        iQPrty = iQPrty.FilterSearch<PRMMovementType>(filtersObj);
                }//Advance Search
                if (SelectObj.advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(SelectObj.Query);
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<AdvanceFilter>();
                    iQPrty = iQPrty.AdvanceSearch<PRMMovementType>(advnfilter);
                }
                //Sorting 
                iQPrty = iQPrty.OrderByField<PRMMovementType>(SelectObj.sidx, SelectObj.sord);
                //Session["MovementTypeDefExport"] = iQPrty;



            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return iQPrty;
        }
        public static async Task<object> Export(SelectMovementtypList ExportObj, int LogException, string constring)
        {
            DataTable dt = new DataTable();
            try
            {

                List<PRMMovementType> arrParts = ((IEnumerable<PRMMovementType>)SelectList(ExportObj, constring, LogException)).ToList();

                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "description").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "UnMoved").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Slow").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Medium").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Fast").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Active").ToString());

                int cnt = arrParts.AsEnumerable().Count();
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(arrParts.ElementAt(i).MovementType_Description, arrParts.ElementAt(i).UnMoved, arrParts.ElementAt(i).Slow, arrParts.ElementAt(i).Medium, arrParts.ElementAt(i).Fast, arrParts.ElementAt(i).IsActive);
                }
                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("description");
                dtAlignment.Columns.Add("UnMoved");
                dtAlignment.Columns.Add("Slow");
                dtAlignment.Columns.Add("Medium");
                dtAlignment.Columns.Add("Fast");
                dtAlignment.Columns.Add("Active");
                dtAlignment.Rows.Add(0, 2, 2, 2, 2, 1);
                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportObj.Branch_ID,
                    dt1 = dtAlignment,



                    dt = dt,

                    FileName = "MovementTypeDefinition", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "MovementTypeDefinition").ToString(), // Set a default or dynamic header
                    exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };



                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;
                //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), Convert.ToInt32(ExportObj.Branch), ExportObj.User_ID, Common.GetObjectID("CoreMovementTypeDefinition"), 0, 0, 0, "Movement Type Definition-Export ", false, Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion\
    }
    #region MovementTypeListandObj Vinay 19/9/24
    /// <summary>
    /// MovementTypeListandObj
    /// </summary>
    public class ValidateMovementTypeList
    {
        public string Branch { get; set; }
        public int Company_ID { get; set; }
        public int MtypeID { get; set; }
        public int primaryKey { get; set; }
    }

    public class DeleteMovementtypedefinationList
    {
        public string key { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string UserCulture { get; set; }
    }

    public class InsertMovementTypeDefinationList
    {

        public int Company_ID { get; set; }


        public int Branch { get; set; }


        public int User_ID { get; set; }


        public string key { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }

    public class SelectMovementtypList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserCulture { get; set; }
        public bool _search { get; set; }
        public string filters { get; set; }
        public bool advnce { get; set; }
        public string Query { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public int exprtType { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public int? Branch_ID { get; set; }
        public bool IsDelete { get; set; }
        public int rows { get; set; }
        public int page { get; set; }
        public int nd { get; set; }
        public bool advance { get; set; }


    }
    #endregion
    #region ::: CoreMovementTypeDefinitionClasses :::
    /// <summary>
    /// CoreMovementTypeDefinitionClasses
    /// </summary>
    /// 
    public partial class GNM_MovementType
    {
        public GNM_MovementType()
        {
            this.GNM_MovementTypeDefinition = new HashSet<GNM_MovementTypeDefinition>();
            this.GNM_MovementTypeLocale = new HashSet<GNM_MovementTypeLocale>();
        }

        public int MovementType_ID { get; set; }
        public string Description { get; set; }
        public int Company_ID { get; set; }
        public bool IsActive { get; set; }

        public virtual ICollection<GNM_MovementTypeDefinition> GNM_MovementTypeDefinition { get; set; }
        public virtual ICollection<GNM_MovementTypeLocale> GNM_MovementTypeLocale { get; set; }
    }
    public partial class GNM_MovementTypeDefinition
    {
        public int MovementTypeDefinition_ID { get; set; }
        public int MovementType_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public bool IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int Unmoved { get; set; }
        public int Slow { get; set; }
        public int Medium { get; set; }
        public int Fast { get; set; }

        public virtual GNM_MovementType GNM_MovementType { get; set; }
    }
    public partial class GNM_MovementTypeLocale
    {
        public int MovementTypeLocale_ID { get; set; }
        public int MovementType_ID { get; set; }
        public string Description { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_MovementType GNM_MovementType { get; set; }
    }
    public class PRMMovementType
    {
        public int MovementType_ID { get; set; }
        public string MovementType_Description { get; set; }
        public string IsActive { get; set; }
        public int UnMoved { get; set; }
        public int Slow { get; set; }
        public int Medium { get; set; }
        public int Fast { get; set; }
        public string Description { get; set; }
        public string Company { get; set; }
        public int Company_ID { get; set; }
    }
    #endregion
}
