﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDeskManagerLandingPageServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskManagerLandingPageController : ApiController
    {

        #region ::: GetInitialData Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To GetInitialData
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/GetInitialData")]
        [HttpPost]
        [JwtTokenValidationFilter]

        public IHttpActionResult GetInitialData([FromBody] HelpDeskManagerLandingPageGetInitialDataList HelpDeskManagerLandingPageGetInitialDataobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.GetInitialData(HelpDeskManagerLandingPageGetInitialDataobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ResolutionTimeOfClosedCases  11-11-2024:::
        /// <summary>
        /// To ResolutionTimeOfClosedCases
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/ResolutionTimeOfClosedCases")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ResolutionTimeOfClosedCases([FromBody] ResolutionTimeOfClosedCasesList ResolutionTimeOfClosedCasesobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.ResolutionTimeOfClosedCases(ResolutionTimeOfClosedCasesobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ResolutionTimeOfClosedCasesd Not Used in this controller Uday Kumar J B  11-11-2024:::
        /// <summary>
        /// To ResolutionTimeOfClosedCasesd
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/ResolutionTimeOfClosedCasesd")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ResolutionTimeOfClosedCasesd([FromBody] ResolutionTimeOfClosedCasesdList ResolutionTimeOfClosedCasesdobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.ResolutionTimeOfClosedCasesd(ResolutionTimeOfClosedCasesdobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: NumberOfCasesBasedOnIssueArea  Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To NumberOfCasesBasedOnIssueArea
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/NumberOfCasesBasedOnIssueArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult NumberOfCasesBasedOnIssueArea([FromBody] NumberOfCasesBasedOnIssueAreaList NumberOfCasesBasedOnIssueAreaobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.NumberOfCasesBasedOnIssueArea(NumberOfCasesBasedOnIssueAreaobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: getCallSummary Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To get Call Summary
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/getCallSummary")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getCallSummary([FromBody] getCallSummaryList getCallSummaryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string dbname = ConfigurationManager.AppSettings.Get("DbName");
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.getCallSummary(getCallSummaryobj, connString, LogException, dbname);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectGraphPendingCasesData  Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Select Graph Pending Cases Data
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/SelectGraphPendingCasesData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectGraphPendingCasesData([FromBody] SelectGraphPendingCasesDataList SelectGraphPendingCasesDataobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.SelectGraphPendingCasesData(SelectGraphPendingCasesDataobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectHyperLinkStatus Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Select HyperLink Status
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/SelectHyperLinkStatus")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectHyperLinkStatus([FromBody] SelectHyperLinkStatusList SelectHyperLinkStatusObj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskManagerLandingPageServices.SelectHyperLinkStatus(SelectHyperLinkStatusObj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectHyperLinkStatusByCallOwner Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Select HyperLink Status By CallOwner
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/SelectHyperLinkStatusByCallOwner")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectHyperLinkStatusByCallOwner([FromBody] SelectHyperLinkStatusByCallOwnerList SelectHyperLinkStatusByCallOwnerobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskManagerLandingPageServices.SelectHyperLinkStatusByCallOwner(SelectHyperLinkStatusByCallOwnerobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar issue  J B 11-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDeskManagerLandingList HelpDeskManagerLandingobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDeskManagerLandingobj.sidx;
            string sord = HelpDeskManagerLandingobj.sord;
            string filter = HelpDeskManagerLandingobj.filter;
            string advnceFilter = HelpDeskManagerLandingobj.advanceFilter;

            try
            {


                Object Response = await HelpDeskManagerLandingPageServices.Export(HelpDeskManagerLandingobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion



        #region ::: SelWorkFlowSummary issue Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// SelWorkFlowSummary
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskManagerLandingPage/SelWorkFlowSummary")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelWorkFlowSummary([FromBody] SelWorkFlowSummaryList SelWorkFlowSummaryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskManagerLandingPageServices.SelWorkFlowSummary(SelWorkFlowSummaryobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: getCompanys Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// getCompanys
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDeskManagerLandingPage/getCompanys")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getCompanys([FromBody] getCompanysList getCompanysobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskManagerLandingPageServices.getCompanys(getCompanysobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


    }
}