﻿#region Assembly WorkFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// C:\Users\<USER>\Downloads\AM_ERP 1\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\AMMSCore\bin\WorkFlow.dll
// Decompiled with ICSharpCode.Decompiler 8.1.1.7464
#endregion

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace WorkFlow.Models
{
    public static class ExtensionMethods
    {
        public static IQueryable<T> OrderByField<T>(this IQueryable<T> q, string SortField, string sord)
        {
            MethodCallExpression expression = null;
            if (SortField != string.Empty && sord != string.Empty)
            {
                ParameterExpression parameterExpression = Expression.Parameter(typeof(T), "p");
                MemberExpression body = Expression.Property(parameterExpression, SortField);
                LambdaExpression lambdaExpression = Expression.Lambda(body, parameterExpression);
                string methodName = ((sord == "asc") ? "OrderBy" : "OrderByDescending");
                Type[] typeArguments = new Type[2]
                {
                    q.ElementType,
                    lambdaExpression.Body.Type
                };
                expression = Expression.Call(typeof(Queryable), methodName, typeArguments, q.Expression, lambdaExpression);
                IQueryable<T> queryable = q.Provider.CreateQuery<T>(expression);
            }

            return q.Provider.CreateQuery<T>(expression);
        }

        public static IQueryable<T> FilterSearch<T>(this IQueryable<T> q, Filters filters)
        {
            ParameterExpression parameterExpression = Expression.Parameter(typeof(T), string.Empty);
            MemberExpression memberExpression = null;
            ConstantExpression constantExpression = null;
            string empty = string.Empty;
            int count = filters.rules.Count;
            BinaryExpression binaryExpression = null;
            for (int i = 0; i < count; i++)
            {
                memberExpression = Expression.PropertyOrField(parameterExpression, filters.rules.ElementAt(i).field);
                constantExpression = Expression.Constant(filters.rules.ElementAt(i).data.ConvertToType(memberExpression.Type));
                if (binaryExpression == null)
                {
                    binaryExpression = LikeSearch(memberExpression, constantExpression);
                    continue;
                }

                BinaryExpression right = LikeSearch(memberExpression, constantExpression);
                binaryExpression = Expression.And(binaryExpression, right);
            }

            Expression<Func<T, bool>> expression = Expression.Lambda<Func<T, bool>>(binaryExpression, new ParameterExpression[1] { parameterExpression });
            return q.Where(expression.Compile()).AsQueryable();
        }

        public static IQueryable<T> AdvanceSearch<T>(this IQueryable<T> q, AdvanceFilter advnfilters)
        {
            ParameterExpression parameterExpression = Expression.Parameter(typeof(T), string.Empty);
            MemberExpression memberExpression = null;
            ConstantExpression constantExpression = null;
            string empty = string.Empty;
            int count = advnfilters.rules.Count;
            BinaryExpression binaryExpression = null;
            BinaryExpression binaryExpression2 = null;
            string empty2 = string.Empty;
            string empty3 = string.Empty;
            object obj = null;
            for (int i = 0; i < count; i++)
            {
                memberExpression = Expression.PropertyOrField(parameterExpression, advnfilters.rules.ElementAt(i).Field);
                constantExpression = Expression.Constant(DecryptString_CE(advnfilters.rules.ElementAt(i).Data).ConvertToType(memberExpression.Type));
                empty3 = advnfilters.rules.ElementAt(i).Condition;
                empty2 = advnfilters.rules.ElementAt(i).Operator;
                switch (empty2.ToLower())
                {
                    case "equals":
                        {
                            ConstantExpression type3 = Expression.Constant(Convert.ToByte(1), typeof(byte));
                            binaryExpression2 = EqualSearch(memberExpression, constantExpression, type3);
                            break;
                        }
                    case "equal":
                        {
                            ConstantExpression type2 = Expression.Constant(Convert.ToByte(1), typeof(byte));
                            binaryExpression2 = EqualSearch(memberExpression, constantExpression, type2);
                            break;
                        }
                    case "notequal":
                        {
                            ConstantExpression type = Expression.Constant(Convert.ToByte(2), typeof(byte));
                            binaryExpression2 = EqualSearch(memberExpression, constantExpression, type);
                            break;
                        }
                    case "lessthan":
                        binaryExpression2 = Expression.LessThan(memberExpression, constantExpression);
                        break;
                    case "greaterthan":
                        binaryExpression2 = Expression.GreaterThan(memberExpression, constantExpression);
                        break;
                    case "like":
                        binaryExpression2 = LikeSearch(memberExpression, constantExpression);
                        break;
                }

                //binaryExpression = empty3 switch
                //{
                //    "AND" => Expression.And(binaryExpression, binaryExpression2),
                //    "OR" => Expression.Or(binaryExpression, binaryExpression2),
                //    _ => binaryExpression2,
                //};
                switch (empty3)
                {
                    case "AND":
                        binaryExpression = Expression.AndAlso(binaryExpression, binaryExpression2);
                        break;
                    case "OR":
                        binaryExpression = Expression.OrElse(binaryExpression, binaryExpression2);
                        break;
                    default:
                        binaryExpression = binaryExpression2;
                        break;
                }
            }

            Expression<Func<T, bool>> expression = Expression.Lambda<Func<T, bool>>(binaryExpression, new ParameterExpression[1] { parameterExpression });
            return q.Where(expression.Compile()).AsQueryable();
        }
        public static string DecryptString_CE(string str)
        {
            str = Uri.UnescapeDataString(str).Replace("%lthash%", "&#");
            return str;
        }
        public static List<T> Paginate<T>(this List<T> q, int page, int rows)
        {
            int num = page * rows - rows;
            int num2 = q.Count();
            int count = ((num2 < num) ? num2 : ((num2 - num < rows) ? (q.Count() - num) : rows));
            if (num <= num2)
            {
                return q.GetRange(num, count);
            }

            return q.GetRange(0, 0);
        }

        public static object ConvertToType(this object objct, Type type)
        {
            if (type == typeof(int))
            {
                objct = Convert.ToInt32(objct);
            }
            else if (type == typeof(decimal))
            {
                objct = Convert.ToDecimal(objct);
            }
            else if (type == typeof(DateTime))
            {
                objct = Convert.ToString(objct);
            }
            else if (type == typeof(string))
            {
                objct = objct.ToString().ToLower();
            }
            else if (type == typeof(bool))
            {
                objct = ((objct.ToString().ToLower() == "yes") ? true : false);
            }
            else if (type.Name == "Nullable`1")
            {
                objct = ((objct.ToString().ToLower() == "yes") ? ((object)true) : ((objct.ToString().ToLower() == "no") ? ((object)false) : objct));
            }

            return objct;
        }

        public static bool Like(string field, string value)
        {
            return field?.ToLower().Contains(value.ToLower()) ?? false;
        }

        public static bool LikeInteger(int field, int value)
        {
            return field.ToString().Contains(value.ToString());
        }

        public static bool LikeDecimal(decimal field, decimal value)
        {
            return field.ToString().Contains(value.ToString());
        }

        public static bool LikeNullableBool(bool? field, bool value)
        {
            return field == value;
        }

        public static bool LikeBool(bool field, bool value)
        {
            return field == value;
        }

        public static bool LikeDate(DateTime field, string value)
        {
            return field.ToString("dd-MMM-yyyy").ToLower().Contains(value.ToLower());
        }

        public static BinaryExpression LikeSearch(Expression field, Expression value)
        {
            BinaryExpression binaryExpression = null;
            MethodCallExpression methodCallExpression = null;
            if (value.Type == typeof(int))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeInteger"), field, value);
            }
            else if (value.Type == typeof(decimal))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeDecimal"), field, value);
            }
            else if (value.Type == typeof(string) && field.Type == typeof(DateTime))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeDate"), field, value);
            }
            else if (value.Type == typeof(string))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("Like"), field, value);
            }
            else if (value.Type.Name == "Nullable`1")
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeNullableBool"), field, value);
            }
            else if (value.Type == typeof(bool))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeBool"), field, value);
            }

            return Expression.Or(methodCallExpression, methodCallExpression);
        }

        public static BinaryExpression EqualSearch(Expression field, Expression value, ConstantExpression type)
        {
            BinaryExpression binaryExpression = null;
            MethodCallExpression methodCallExpression = null;
            if (value.Type == typeof(int))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualInteger"), field, value, type);
            }
            else if (value.Type == typeof(decimal))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualDecimal"), field, value, type);
            }
            else if (value.Type == typeof(string))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualString"), field, value, type);
            }
            else if (value.Type.Name == "Nullable`1")
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualNullableBool"), field, value, type);
            }
            else if (value.Type == typeof(bool))
            {
                methodCallExpression = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualBool"), field, value, type);
            }

            return Expression.Or(methodCallExpression, methodCallExpression);
        }

        public static bool EqualString(string field, string value, byte type)
        {
            if (type == 1)
            {
                return field != null && field.ToLower() == value.ToLower();
            }

            return field != null && field.ToLower() != value.ToLower();
        }

        public static bool EqualInteger(int field, int value, byte type)
        {
            if (type == 1)
            {
                return field.ToString() == value.ToString();
            }

            return field.ToString() != value.ToString();
        }

        public static bool EqualDecimal(decimal field, decimal value, byte type)
        {
            if (type == 1)
            {
                return field.ToString() == value.ToString();
            }

            return field.ToString() != value.ToString();
        }

        public static bool EqualNullableBool(bool? field, bool value, byte type)
        {
            if (type == 1)
            {
                return field == value;
            }

            return field != value;
        }

        public static bool EqualBool(bool field, bool value, byte type)
        {
            if (type == 1)
            {
                return field == value;
            }

            return field != value;
        }
    }
#if false // Decompilation log
    '57' items in cache
    ------------------
    Resolve: 'mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll'
    ------------------
    Resolve: 'System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.Mvc.4.0.20710.0\lib\net40\System.Web.Mvc.dll'
    ------------------
    Resolve: 'System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Found single assembly: 'System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dll'
    ------------------
    Resolve: 'EntityFramework, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'EntityFramework, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Users\<USER>\Downloads\AM_ERP 1\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\AMMSCore\bin\EntityFramework.dll'
    ------------------
    Resolve: 'Newtonsoft.Json, Version=4.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed'
    Found single assembly: 'Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed'
    WARN: Version mismatch. Expected: '4.5.0.0', Got: '13.0.0.0'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll'
    ------------------
    Resolve: 'System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll'
    ------------------
    Resolve: 'System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.Entity.dll'
    ------------------
    Resolve: 'System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dll'
    ------------------
    Resolve: 'System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.WebApi.Core.4.0.20505.0\lib\net40\System.Web.Http.dll'
    ------------------
    Resolve: 'System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Found single assembly: 'System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dll'
    ------------------
    Resolve: 'LogSheetExporter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
    Found single assembly: 'LogSheetExporter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
    Load from: 'C:\Users\<USER>\Downloads\AM_ERP 1\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\AMMSCore\bin\LogSheetExporter.dll'
    ------------------
    Resolve: 'Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Found single assembly: 'Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll'
    ------------------
    Resolve: 'System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Found single assembly: 'System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll'
    ------------------
    Resolve: 'System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.WebApi.WebHost.4.0.20505.0\lib\net40\System.Web.Http.WebHost.dll'
    ------------------
    Resolve: 'System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\HCLTFS_SW\AM_ERP\API_DRIVEN\GUI\HCLSoftware_AMP_MicroService_GUI\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.dll'
    ------------------
    Resolve: 'System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dll'
    ------------------
    Resolve: 'System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Found single assembly: 'System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35'
    Load from: 'C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dll'
#endif
}
