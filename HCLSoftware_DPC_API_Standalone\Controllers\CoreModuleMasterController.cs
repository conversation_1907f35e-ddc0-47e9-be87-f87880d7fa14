﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreModuleMasterController : ApiController
    {


        #region ::: SelectMenu :::
        /// <summary>
        /// SelectMenu
        /// </summary>
        /// <param name="SelectMenuObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/SelectMenu")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMenu([FromBody] SelectMenuList SelectMenuObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModuleMasterServices.SelectMenu(connstring, SelectMenuObj, sidx, rows, page, sord, _search, nd, filters, advnce, LogException, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region ::: Insert /Vinay:::
        /// <summary>
        /// Insert
        /// </summary>
        /// <param name="InsertObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/Insert")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertList InsertObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.Insert(InsertObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectModule :::
        /// <summary>
        /// SelectModule
        /// </summary>
        /// <param name="SelectMenuObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/SelectModule")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModule([FromBody] SelectModuleList SelectModuleObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModuleMasterServices.SelectModule(connstring, SelectModuleObj, sidx, rows, page, sord, _search, nd, filters, advnce, LogException);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Edit /Vinay:::
        /// <summary>
        /// Edit
        /// </summary>
        /// <param name="EditObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/Edit")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Edit([FromBody] EditList EditObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.Edit(EditObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: DeleteMenu /Vinay:::
        /// <summary>
        /// DeleteMenu
        /// </summary>
        /// <param name="DeleteMenuObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/DeleteMenu")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteMenu([FromBody] DeleteMenuList DeleteMenuObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.DeleteMenu(DeleteMenuObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: DeleteModule /Vinay:::
        /// <summary>
        /// DeleteModule
        /// </summary>
        /// <param name="DeleteModuleObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/DeleteModule")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteModule([FromBody] DeleteModuleList DeleteModuleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.DeleteModule(DeleteModuleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: getModuleLocale /Vinay:::
        /// <summary>
        /// getModuleLocale
        /// </summary>
        /// <param name="getModuleLocaleObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/getModuleLocale")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getModuleLocale([FromBody] getModuleLocaleList getModuleLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.getModuleLocale(getModuleLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: ModuleLocaleSave /Vinay:::
        /// <summary>
        /// getModuleLocale
        /// </summary>
        /// <param name="ModuleLocaleSaveObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/ModuleLocaleSave")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ModuleLocaleSave([FromBody] ModuleLocaleSaveList ModuleLocaleSaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.ModuleLocaleSave(ModuleLocaleSaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: getMenuLocaleName /Vinay:::
        /// <summary>
        /// getMenuLocaleName
        /// </summary>
        /// <param name="getMenuLocaleNameObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/getMenuLocaleName")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getMenuLocaleName([FromBody] getMenuLocaleNameList getMenuLocaleNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.getMenuLocaleName(getMenuLocaleNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: MenuLocaleSave /Vinay:::
        /// <summary>
        /// MenuLocaleSave
        /// </summary>
        /// <param name="MenuLocaleSaveObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/MenuLocaleSave")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MenuLocaleSave([FromBody] MenuLocaleSaveList MenuLocaleSaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.MenuLocaleSave(MenuLocaleSaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: ChkDuplicateModules /Vinay:::
        /// <summary>
        /// ChkDuplicateModules
        /// </summary>
        /// <param name="ChkDuplicateModulesObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/ChkDuplicateModules")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ChkDuplicateModules([FromBody] ChkDuplicateModulesList ChkDuplicateModulesObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.ChkDuplicateModules(ChkDuplicateModulesObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: CheckModuleLocaleExists /Vinay:::
        /// <summary>
        /// CheckModuleLocaleExists
        /// </summary>
        /// <param name="CheckModuleLocaleExistsObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/CheckModuleLocaleExists")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckModuleLocaleExists([FromBody] CheckModuleLocaleExistsList CheckModuleLocaleExistsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModuleMasterServices.CheckModuleLocaleExists(CheckModuleLocaleExistsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region Export
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreModuleMaster/Export")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectMenuList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;


            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;



            try
            {


                Object Response = await CoreModuleMasterServices.Export(ExportObj, connstring, LogException, filters, advnceFilters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion
    }
}