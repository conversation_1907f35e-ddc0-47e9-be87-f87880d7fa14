﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using static SharedAPIClassLibrary_DC.Utilities.Common;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskManagerLandingPageServices
    {

        #region ::: GetInitialData Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To GetInitialData
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult GetInitialData(HelpDeskManagerLandingPageGetInitialDataList HelpDeskManagerLandingPageGetInitialDataobj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            try
            {
                int company_ID = Convert.ToInt32(HelpDeskManagerLandingPageGetInitialDataobj.Company_ID);
                int object_ID = Common.GetObjectID("HelpDeskUserLandingPage");

                // Initialize TimeSlot variable
                string TimeSlot = string.Empty;

                // ADO.NET to call the stored procedure
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetTimeSlotByCompanyID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", company_ID);
                        cmd.Parameters.AddWithValue("@Param_Name", "TIMESLOT");

                        conn.Open();
                        TimeSlot = Convert.ToString(cmd.ExecuteScalar());
                    }
                }

                // Process TimeSlot to get TimeSlotArray
                string[] TimeSlotArray = TimeSlot.Split(',');

                // Get needtochangePassword from Session
                bool needtochangePassword = Convert.ToBoolean(HelpDeskManagerLandingPageGetInitialDataobj.NeedToChangepassword);

                // Create JSON result
                jsonResult = new
                {
                    object_ID,
                    TimeSlotArray,
                    needtochangePassword
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: Check The Date Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To check the Date 
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static int CheckValidDate(string FromDate, string ToDate)
        {
            if (FromDate != "" && ToDate != "")
            {
                if (CheckValidFromDate(FromDate) == 0 && CheckValidToDate(ToDate) == 0)
                {
                    return 0;
                }
                else if (CheckValidFromDate(FromDate) == 0)
                {
                    return 1;
                }
                else if (CheckValidToDate(ToDate) == 0)
                {
                    return 2;
                }
            }
            else if (FromDate != "" && ToDate == "")
            {
                if (CheckValidFromDate(FromDate) == 0)
                {
                    return 1;
                }
            }
            else if (FromDate == "" && ToDate != "")
            {
                if (CheckValidToDate(ToDate) == 0)
                {
                    return 2;
                }
            }
            return -1;
        }

        public static int CheckValidFromDate(string FromDate)
        {
            try
            {
                DateTime temp = Convert.ToDateTime(FromDate);
                return 1;
            }
            catch (Exception e)
            {
                return 0;
            }
        }

        public static int CheckValidToDate(string ToDate)
        {
            try
            {
                DateTime temp = Convert.ToDateTime(ToDate);
                return 1;
            }
            catch (Exception e)
            {
                return 0;
            }
        }
        #endregion


        #region ::: ResolutionTimeOfClosedCases 11-11-2024:::
        /// <summary>
        /// To ResolutionTimeOfClosedCases
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult ResolutionTimeOfClosedCases(ResolutionTimeOfClosedCasesList ResolutionTimeOfClosedCasesobj, string connString, int LogException)
        {
            var JsonResult = default(dynamic);
            try
            {
                string frmdate = ResolutionTimeOfClosedCasesobj.FromDate;
                string todate = ResolutionTimeOfClosedCasesobj.ToDate;
                bool BusinessAreaAll = Convert.ToBoolean(ResolutionTimeOfClosedCasesobj.BusinessAreaAll);
                bool BusinessAreaDomestic = Convert.ToBoolean(ResolutionTimeOfClosedCasesobj.BusinessAreaDomestic);
                string CompanyIds = string.Empty;
                string BranchIds = string.Empty;

                if (Uri.UnescapeDataString(ResolutionTimeOfClosedCasesobj.Company) != null && Uri.UnescapeDataString(ResolutionTimeOfClosedCasesobj.Company) != "")
                {
                    MD_Company companies = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(ResolutionTimeOfClosedCasesobj.Company))).ToObject<MD_Company>();
                    CompanyIds = string.Join(",", companies.Companies.Select(c => c.ID));
                }

                if (Uri.UnescapeDataString(ResolutionTimeOfClosedCasesobj.Branch) != null && Uri.UnescapeDataString(ResolutionTimeOfClosedCasesobj.Branch) != "")
                {
                    MD_Branch branches = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(ResolutionTimeOfClosedCasesobj.Branch))).ToObject<MD_Branch>();
                    BranchIds = string.Join(",", branches.Branchs.Select(b => b.ID));
                }

                List<HD_ServiceRequest> SRequestAll = new List<HD_ServiceRequest>();

                // Retrieve closed cases using ADO.NET and stored procedure
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetClosedCasesByCompanyAndBranch", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIds);
                        cmd.Parameters.AddWithValue("@BusinessAreaAll", BusinessAreaAll);
                        cmd.Parameters.AddWithValue("@BusinessAreaDomestic", BusinessAreaDomestic);

                        // Create a DataTable to hold the result of the stored procedure
                        DataTable dt = new DataTable();

                        // Open the connection and fill the DataTable with the data
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            conn.Open();
                            da.Fill(dt);
                        }

                        // Now map the DataTable to your list of HD_ServiceRequest objects
                        foreach (DataRow row in dt.Rows)
                        {
                            SRequestAll.Add(new HD_ServiceRequest
                            {
                                ServiceRequest_ID = dt.Columns.Contains("RequestID") && row["RequestID"] != DBNull.Value ? Convert.ToInt32(row["RequestID"]) : 0,
                                ServiceRequestNumber = dt.Columns.Contains("ServiceRequestNumber") && row["ServiceRequestNumber"] != DBNull.Value ? row["ServiceRequestNumber"].ToString() : string.Empty,
                                ServiceRequestDate = dt.Columns.Contains("ServiceRequestDate") && row["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(row["ServiceRequestDate"]) : DateTime.MinValue,
                                Quotation_ID = dt.Columns.Contains("Quotation_ID") && row["Quotation_ID"] != DBNull.Value ? Convert.ToInt32(row["Quotation_ID"]) : (int?)null,
                                QuotationNumber = dt.Columns.Contains("QuotationNumber") && row["QuotationNumber"] != DBNull.Value ? row["QuotationNumber"].ToString() : string.Empty,
                                JobCard_ID = dt.Columns.Contains("JobCard_ID") && row["JobCard_ID"] != DBNull.Value ? Convert.ToInt32(row["JobCard_ID"]) : (int?)null,
                                JobCardNumber = dt.Columns.Contains("JobCardNumber") && row["JobCardNumber"] != DBNull.Value ? row["JobCardNumber"].ToString() : string.Empty,
                                CallStatus_ID = dt.Columns.Contains("CallStatus_ID") && row["CallStatus_ID"] != DBNull.Value ? Convert.ToInt32(row["CallStatus_ID"]) : 0,
                                ParentIssue_ID = dt.Columns.Contains("ParentIssue_ID") && row["ParentIssue_ID"] != DBNull.Value ? Convert.ToInt32(row["ParentIssue_ID"]) : (int?)null,
                                Product_Unique_Number = dt.Columns.Contains("Product_Unique_Number") && row["Product_Unique_Number"] != DBNull.Value ? row["Product_Unique_Number"].ToString() : string.Empty,
                                Party_ID = dt.Columns.Contains("Party_ID") && row["Party_ID"] != DBNull.Value ? Convert.ToInt32(row["Party_ID"]) : 0,
                                PartyContactPerson_ID = dt.Columns.Contains("PartyContactPerson_ID") && row["PartyContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(row["PartyContactPerson_ID"]) : 0,
                                Model_ID = dt.Columns.Contains("Model_ID") && row["Model_ID"] != DBNull.Value ? Convert.ToInt32(row["Model_ID"]) : (int?)null,
                                Brand_ID = dt.Columns.Contains("Brand_ID") && row["Brand_ID"] != DBNull.Value ? Convert.ToInt32(row["Brand_ID"]) : (int?)null,
                                ProductType_ID = dt.Columns.Contains("ProductType_ID") && row["ProductType_ID"] != DBNull.Value ? Convert.ToInt32(row["ProductType_ID"]) : (int?)null,
                                SerialNumber = dt.Columns.Contains("SerialNumber") && row["SerialNumber"] != DBNull.Value ? row["SerialNumber"].ToString() : string.Empty,
                                ProductReading = dt.Columns.Contains("ProductReading") && row["ProductReading"] != DBNull.Value ? Convert.ToInt32(row["ProductReading"]) : (int?)null,
                                IsUnderWarranty = dt.Columns.Contains("IsUnderWarranty") && row["IsUnderWarranty"] != DBNull.Value ? Convert.ToBoolean(row["IsUnderWarranty"]) : false,
                                CallMode_ID = dt.Columns.Contains("CallMode_ID") && row["CallMode_ID"] != DBNull.Value ? Convert.ToInt32(row["CallMode_ID"]) : 0,
                                CallPriority_ID = dt.Columns.Contains("CallPriority_ID") && row["CallPriority_ID"] != DBNull.Value ? Convert.ToInt32(row["CallPriority_ID"]) : (int?)null,
                                CallComplexity_ID = dt.Columns.Contains("CallComplexity_ID") && row["CallComplexity_ID"] != DBNull.Value ? Convert.ToInt32(row["CallComplexity_ID"]) : 0,
                                CallDateAndTime = dt.Columns.Contains("CallDateAndTime") && row["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallDateAndTime"]) : DateTime.MinValue,
                                PromisedCompletionDate = dt.Columns.Contains("PromisedCompletionDate") && row["PromisedCompletionDate"] != DBNull.Value ? Convert.ToDateTime(row["PromisedCompletionDate"]) : (DateTime?)null,
                                Region_ID = dt.Columns.Contains("Region_ID") && row["Region_ID"] != DBNull.Value ? Convert.ToInt32(row["Region_ID"]) : (int?)null,
                                CallDescription = dt.Columns.Contains("CallDescription") && row["CallDescription"] != DBNull.Value ? row["CallDescription"].ToString() : string.Empty,
                                IssueArea_ID = dt.Columns.Contains("IssueArea_ID") && row["IssueArea_ID"] != DBNull.Value ? Convert.ToInt32(row["IssueArea_ID"]) : (int?)null,
                                IssueSubArea_ID = dt.Columns.Contains("IssueSubArea_ID") && row["IssueSubArea_ID"] != DBNull.Value ? Convert.ToInt32(row["IssueSubArea_ID"]) : (int?)null,
                                FunctionGroup_ID = dt.Columns.Contains("FunctionGroup_ID") && row["FunctionGroup_ID"] != DBNull.Value ? Convert.ToInt32(row["FunctionGroup_ID"]) : (int?)null,
                                IsUnderBreakDown = dt.Columns.Contains("IsUnderBreakDown") && row["IsUnderBreakDown"] != DBNull.Value ? Convert.ToBoolean(row["IsUnderBreakDown"]) : false,
                                QuestionaryLevel1_ID = dt.Columns.Contains("QuestionaryLevel1_ID") && row["QuestionaryLevel1_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel1_ID"]) : (int?)null,
                                QuestionaryLevel2_ID = dt.Columns.Contains("QuestionaryLevel2_ID") && row["QuestionaryLevel2_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel2_ID"]) : (int?)null,
                                QuestionaryLevel3_ID = dt.Columns.Contains("QuestionaryLevel3_ID") && row["QuestionaryLevel3_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel3_ID"]) : (int?)null,
                                DefectGroup_ID = dt.Columns.Contains("DefectGroup_ID") && row["DefectGroup_ID"] != DBNull.Value ? Convert.ToInt32(row["DefectGroup_ID"]) : (int?)null,
                                DefectName_ID = dt.Columns.Contains("DefectName_ID") && row["DefectName_ID"] != DBNull.Value ? Convert.ToInt32(row["DefectName_ID"]) : (int?)null,
                                RootCause = dt.Columns.Contains("RootCause") && row["RootCause"] != DBNull.Value ? row["RootCause"].ToString() : string.Empty,
                                InformationCollected = dt.Columns.Contains("InformationCollected") && row["InformationCollected"] != DBNull.Value ? row["InformationCollected"].ToString() : string.Empty,
                                CallClosureDateAndTime = dt.Columns.Contains("CallClosureDateAndTime") && row["CallClosureDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallClosureDateAndTime"]) : (DateTime?)null,
                                ClosureType_ID = dt.Columns.Contains("ClosureType_ID") && row["ClosureType_ID"] != DBNull.Value ? Convert.ToInt32(row["ClosureType_ID"]) : (int?)null,
                                ClosingDescription = dt.Columns.Contains("ClosingDescription") && row["ClosingDescription"] != DBNull.Value ? row["ClosingDescription"].ToString() : string.Empty,
                                Company_ID = dt.Columns.Contains("Company_ID") && row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0,
                                Branch_ID = dt.Columns.Contains("Branch_ID") && row["Branch_ID"] != DBNull.Value ? Convert.ToInt32(row["Branch_ID"]) : 0,
                                ModifiedDate = dt.Columns.Contains("ModifiedDate") && row["ModifiedDate"] != DBNull.Value ? Convert.ToDateTime(row["ModifiedDate"]) : (DateTime?)null,
                                ModifiedBy_ID = dt.Columns.Contains("ModifiedBy_ID") && row["ModifiedBy_ID"] != DBNull.Value ? Convert.ToInt32(row["ModifiedBy_ID"]) : (int?)null,
                                Document_no = dt.Columns.Contains("Document_no") && row["Document_no"] != DBNull.Value ? Convert.ToInt32(row["Document_no"]) : (int?)null,
                                CaseType_ID = dt.Columns.Contains("CaseType_ID") && row["CaseType_ID"] != DBNull.Value ? Convert.ToInt32(row["CaseType_ID"]) : 0,
                                ActionRemarks = dt.Columns.Contains("ActionRemarks") && row["ActionRemarks"] != DBNull.Value ? row["ActionRemarks"].ToString() : string.Empty,
                                Product_ID = dt.Columns.Contains("Product_ID") && row["Product_ID"] != DBNull.Value ? Convert.ToInt32(row["Product_ID"]) : (int?)null,
                                CustomerRating = dt.Columns.Contains("CustomerRating") && row["CustomerRating"] != DBNull.Value ? Convert.ToInt32(row["CustomerRating"]) : (int?)null,
                                FinancialYear = dt.Columns.Contains("FinancialYear") && row["FinancialYear"] != DBNull.Value ? Convert.ToInt32(row["FinancialYear"]) : (int?)null,
                                IsCallBlocked = dt.Columns.Contains("IsCallBlocked") && row["IsCallBlocked"] != DBNull.Value ? Convert.ToBoolean(row["IsCallBlocked"]) : (bool?)null,
                                StockBlocking_ID = dt.Columns.Contains("StockBlocking_ID") && row["StockBlocking_ID"] != DBNull.Value ? Convert.ToInt32(row["StockBlocking_ID"]) : (int?)null,
                                EnquiryStage_ID = dt.Columns.Contains("EnquiryStage_ID") && row["EnquiryStage_ID"] != DBNull.Value ? Convert.ToInt32(row["EnquiryStage_ID"]) : (int?)null,
                                SalesQuotation_ID = dt.Columns.Contains("SalesQuotation_ID") && row["SalesQuotation_ID"] != DBNull.Value ? Convert.ToInt32(row["SalesQuotation_ID"]) : (int?)null,
                                SalesQuotationNumber = dt.Columns.Contains("SalesQuotationNumber") && row["SalesQuotationNumber"] != DBNull.Value ? row["SalesQuotationNumber"].ToString() : string.Empty,
                                SalesOrder_ID = dt.Columns.Contains("SalesOrder_ID") && row["SalesOrder_ID"] != DBNull.Value ? Convert.ToInt32(row["SalesOrder_ID"]) : (int?)null,
                                SalesOrderNumber = dt.Columns.Contains("SalesOrderNumber") && row["SalesOrderNumber"] != DBNull.Value ? row["SalesOrderNumber"].ToString() : string.Empty,
                                SalesOrderDate = dt.Columns.Contains("SalesOrderDate") && row["SalesOrderDate"] != DBNull.Value ? Convert.ToDateTime(row["SalesOrderDate"]) : (DateTime?)null,
                                CallOwner_ID = dt.Columns.Contains("CallOwner_ID") && row["CallOwner_ID"] != DBNull.Value ? Convert.ToInt32(row["CallOwner_ID"]) : (int?)null,
                                Flexi1 = dt.Columns.Contains("Flexi1") && row["Flexi1"] != DBNull.Value ? row["Flexi1"].ToString() : string.Empty,
                                Flexi2 = dt.Columns.Contains("Flexi2") && row["Flexi2"] != DBNull.Value ? row["Flexi2"].ToString() : string.Empty,
                                ResolutionTime = dt.Columns.Contains("ResolutionTime") && row["ResolutionTime"] != DBNull.Value ? row["ResolutionTime"].ToString() : string.Empty,
                                ResponseTime = dt.Columns.Contains("ResponseTime") && row["ResponseTime"] != DBNull.Value ? row["ResponseTime"].ToString() : string.Empty,
                                AttachmentCount = dt.Columns.Contains("AttachmentCount") && row["AttachmentCount"] != DBNull.Value ? Convert.ToByte(row["AttachmentCount"]) : (byte?)null,
                                CustomerFeedbackDate = dt.Columns.Contains("CustomerFeedbackDate") && row["CustomerFeedbackDate"] != DBNull.Value ? Convert.ToDateTime(row["CustomerFeedbackDate"]) : (DateTime?)null,
                                IsNegetiveFeedback = dt.Columns.Contains("IsNegetiveFeedback") && row["IsNegetiveFeedback"] != DBNull.Value ? Convert.ToBoolean(row["IsNegetiveFeedback"]) : (bool?)null,
                                IsDealer = dt.Columns.Contains("IsDealer") && row["IsDealer"] != DBNull.Value ? Convert.ToBoolean(row["IsDealer"]) : (bool?)null,
                                IsDealerList = dt.Columns.Contains("IsDealerList") && row["IsDealerList"] != DBNull.Value ? Convert.ToByte(row["IsDealerList"]) : (byte?)null,
                                ProductRateType = dt.Columns.Contains("ProductRateType") && row["ProductRateType"] != DBNull.Value ? Convert.ToByte(row["ProductRateType"]) : (byte?)null,
                                ChildTicket_Sequence_ID = dt.Columns.Contains("ChildTicket_Sequence_ID") && row["ChildTicket_Sequence_ID"] != DBNull.Value ? Convert.ToInt32(row["ChildTicket_Sequence_ID"]) : (int?)null,
                                ResponseTime24 = dt.Columns.Contains("ResponseTime24") && row["ResponseTime24"] != DBNull.Value ? row["ResponseTime24"].ToString() : string.Empty,
                                ResolutionTime24 = dt.Columns.Contains("ResolutionTime24") && row["ResolutionTime24"] != DBNull.Value ? row["ResolutionTime24"].ToString() : string.Empty,
                                Current_AssignTo = dt.Columns.Contains("Current_AssignTo") && row["Current_AssignTo"] != DBNull.Value ? Convert.ToInt32(row["Current_AssignTo"]) : (int?)null,
                                ContractorID = dt.Columns.Contains("ContractorID") && row["ContractorID"] != DBNull.Value ? Convert.ToInt32(row["ContractorID"]) : (int?)null,
                                ContractorContactPerson_ID = dt.Columns.Contains("ContractorContactPerson_ID") && row["ContractorContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(row["ContractorContactPerson_ID"]) : (int?)null,
                                ScheduledType_ID = dt.Columns.Contains("ScheduledType_ID") && row["ScheduledType_ID"] != DBNull.Value ? Convert.ToInt32(row["ScheduledType_ID"]) : (int?)null,
                                ExpectedArrivalDateTime = dt.Columns.Contains("ExpectedArrivalDateTime") && row["ExpectedArrivalDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpectedArrivalDateTime"]) : (DateTime?)null,
                                ActualArrivalDateTime = dt.Columns.Contains("ActualArrivalDateTime") && row["ActualArrivalDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ActualArrivalDateTime"]) : (DateTime?)null,
                                ExpectedDepartureDateTime = dt.Columns.Contains("ExpectedDepartureDateTime") && row["ExpectedDepartureDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpectedDepartureDateTime"]) : (DateTime?)null,
                                ActualDepartureDateTime = dt.Columns.Contains("ActualDepartureDateTime") && row["ActualDepartureDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ActualDepartureDateTime"]) : (DateTime?)null,
                                NoofTechs = dt.Columns.Contains("NoofTechs") && row["NoofTechs"] != DBNull.Value ? Convert.ToInt32(row["NoofTechs"]) : (int?)null,
                                ShiftHours = dt.Columns.Contains("ShiftHours") && row["ShiftHours"] != DBNull.Value ? Convert.ToInt32(row["ShiftHours"]) : (int?)null,
                                IsWIPBay = dt.Columns.Contains("IsWIPBay") && row["IsWIPBay"] != DBNull.Value ? Convert.ToBoolean(row["IsWIPBay"]) : (bool?)null
                            });
                        }
                    }
                }
                // Retrieve TimeSlot using ADO.NET and stored procedure
                string TimeSlot = string.Empty;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetTimeSlotByCompanyID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", ResolutionTimeOfClosedCasesobj.Company_ID);
                        cmd.Parameters.AddWithValue("@Param_Name", "TIMESLOT");

                        conn.Open();
                        TimeSlot = Convert.ToString(cmd.ExecuteScalar());
                    }
                }

                string[] TimeSlotArray = TimeSlot.Split(',');
                List<TimeSlot> timeSlotList = TimeSlotArray.Select(ts => new TimeSlot { Name = ts, Count = 0 }).ToList();
                timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray.Last()) + 1).ToString(), Count = 0 });

                foreach (var sr in SRequestAll)
                {
                    int THour = Convert.ToInt32((sr.ResolutionTime).Split(':')[0]);
                    timeSlotList = CalculateTimeSlot(timeSlotList, THour);
                }

                JsonResult = new
                {
                    timeSlotList = timeSlotList
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(JsonResult);
        }
        #endregion


        #region ::: CalculateTimeSlot 11-11-2024:::
        /// <summary>
        /// To CalculateTimeSlot
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static List<TimeSlot> CalculateTimeSlot(List<TimeSlot> Timeslot, int Value)
        {
            try
            {
                for (int i = 0; i < Timeslot.Count; i++)
                {
                    if (Value >= Convert.ToInt32(Timeslot[Timeslot.Count - 1].Name))
                    {
                        Timeslot[Timeslot.Count - 1].Count++;
                        break;
                    }
                    else if (Value <= Convert.ToInt32(Timeslot[i].Name) - 1)
                    {
                        Timeslot[i].Count++;
                        break;
                    }
                }
            }
            catch (Exception ex) { }
            return Timeslot;
        }
        #endregion


        #region ::: ResolutionTimeOfClosedCasesd Uday Kumar J B  11-11-2024:::
        /// <summary>
        /// To ResolutionTimeOfClosedCasesd
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult ResolutionTimeOfClosedCasesd(ResolutionTimeOfClosedCasesdList ResolutionTimeOfClosedCasesdobj, string connString, int LogException)
        {
            var JsonResult = default(dynamic);
            try
            {
                var JResult = default(dynamic);
                List<HD_ServiceRequest> SRequestAll = new List<HD_ServiceRequest>();
                List<HD_ServiceRequest> SREmployeeBranchWiseArray = new List<HD_ServiceRequest>();
                string frmdate = ResolutionTimeOfClosedCasesdobj.FromDate;
                string todate = ResolutionTimeOfClosedCasesdobj.ToDate;
                bool BusinessAreaAll = Convert.ToBoolean(ResolutionTimeOfClosedCasesdobj.BusinessAreaAll);
                bool BusinessAreaDomestic = Convert.ToBoolean(ResolutionTimeOfClosedCasesdobj.BusinessAreaDomestic);

                string CompanyIds = string.Empty;

                if (ResolutionTimeOfClosedCasesdobj.Company != null && ResolutionTimeOfClosedCasesdobj.Company != "")
                {
                    MD_Company companies = JObject.Parse(Common.DecryptString(ResolutionTimeOfClosedCasesdobj.Company)).ToObject<MD_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;

                if (ResolutionTimeOfClosedCasesdobj.Branch != null && ResolutionTimeOfClosedCasesdobj.Branch != "")
                {
                    MD_Branch branchs = JObject.Parse(Common.DecryptString(ResolutionTimeOfClosedCasesdobj.Branch)).ToObject<MD_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                }

                var strCompanyIds = CompanyIds.Split(',');
                var strBranchIds = BranchIds.Split(',');

                List<TimeSlot> timeSlotList = new List<TimeSlot>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int iik = 0; iik < strCompanyIds.Length; iik++)
                    {
                        int CompanyID = Convert.ToInt32(strCompanyIds[iik]);

                        for (int ik = 0; ik < strBranchIds.Length; ik++)
                        {
                            int branchID = Convert.ToInt32(strBranchIds[ik]);

                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetClosedServiceRequests", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                                cmd.Parameters.AddWithValue("@BranchID", branchID);
                                cmd.Parameters.AddWithValue("@EmployeeID", ResolutionTimeOfClosedCasesdobj.Employee_ID);
                                cmd.Parameters.AddWithValue("@BusinessAreaAll", BusinessAreaAll);
                                cmd.Parameters.AddWithValue("@BusinessAreaDomestic", BusinessAreaDomestic);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                                // Load data into DataTable
                                DataTable dt = new DataTable();
                                using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                                {
                                    da.Fill(dt);
                                }

                                // Iterate over DataTable rows and map to HD_ServiceRequest objects
                                foreach (DataRow row in dt.Rows)
                                {
                                    HD_ServiceRequest serviceRequest = new HD_ServiceRequest
                                    {
                                        ServiceRequest_ID = row["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(row["ServiceRequest_ID"]) : 0,
                                        ServiceRequestNumber = row["ServiceRequestNumber"].ToString(),
                                        ServiceRequestDate = row["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(row["ServiceRequestDate"]) : DateTime.MinValue,
                                        Quotation_ID = row["Quotation_ID"] != DBNull.Value ? Convert.ToInt32(row["Quotation_ID"]) : (int?)null,
                                        QuotationNumber = row["QuotationNumber"].ToString(),
                                        JobCard_ID = row["JobCard_ID"] != DBNull.Value ? Convert.ToInt32(row["JobCard_ID"]) : (int?)null,
                                        JobCardNumber = row["JobCardNumber"].ToString(),
                                        CallStatus_ID = row["CallStatus_ID"] != DBNull.Value ? Convert.ToInt32(row["CallStatus_ID"]) : 0,
                                        ParentIssue_ID = row["ParentIssue_ID"] != DBNull.Value ? Convert.ToInt32(row["ParentIssue_ID"]) : (int?)null,
                                        Product_Unique_Number = row["Product_Unique_Number"].ToString(),
                                        Party_ID = row["Party_ID"] != DBNull.Value ? Convert.ToInt32(row["Party_ID"]) : 0,
                                        PartyContactPerson_ID = row["PartyContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(row["PartyContactPerson_ID"]) : 0,
                                        Model_ID = row["Model_ID"] != DBNull.Value ? Convert.ToInt32(row["Model_ID"]) : (int?)null,
                                        Brand_ID = row["Brand_ID"] != DBNull.Value ? Convert.ToInt32(row["Brand_ID"]) : (int?)null,
                                        ProductType_ID = row["ProductType_ID"] != DBNull.Value ? Convert.ToInt32(row["ProductType_ID"]) : (int?)null,
                                        SerialNumber = row["SerialNumber"].ToString(),
                                        ProductReading = row["ProductReading"] != DBNull.Value ? Convert.ToInt32(row["ProductReading"]) : (int?)null,
                                        IsUnderWarranty = Convert.ToBoolean(row["IsUnderWarranty"]),
                                        CallMode_ID = row["CallMode_ID"] != DBNull.Value ? Convert.ToInt32(row["CallMode_ID"]) : 0,
                                        CallPriority_ID = row["CallPriority_ID"] != DBNull.Value ? Convert.ToInt32(row["CallPriority_ID"]) : (int?)null,
                                        CallComplexity_ID = row["CallComplexity_ID"] != DBNull.Value ? Convert.ToInt32(row["CallComplexity_ID"]) : 0,
                                        CallDateAndTime = row["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallDateAndTime"]) : DateTime.MinValue,
                                        PromisedCompletionDate = row["PromisedCompletionDate"] != DBNull.Value ? Convert.ToDateTime(row["PromisedCompletionDate"]) : (DateTime?)null,
                                        Region_ID = row["Region_ID"] != DBNull.Value ? Convert.ToInt32(row["Region_ID"]) : (int?)null,
                                        CallDescription = row["CallDescription"].ToString(),
                                        IssueArea_ID = row["IssueArea_ID"] != DBNull.Value ? Convert.ToInt32(row["IssueArea_ID"]) : (int?)null,
                                        IssueSubArea_ID = row["IssueSubArea_ID"] != DBNull.Value ? Convert.ToInt32(row["IssueSubArea_ID"]) : (int?)null,
                                        FunctionGroup_ID = row["FunctionGroup_ID"] != DBNull.Value ? Convert.ToInt32(row["FunctionGroup_ID"]) : (int?)null,
                                        IsUnderBreakDown = Convert.ToBoolean(row["IsUnderBreakDown"]),
                                        QuestionaryLevel1_ID = row["QuestionaryLevel1_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel1_ID"]) : (int?)null,
                                        QuestionaryLevel2_ID = row["QuestionaryLevel2_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel2_ID"]) : (int?)null,
                                        QuestionaryLevel3_ID = row["QuestionaryLevel3_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel3_ID"]) : (int?)null,
                                        DefectGroup_ID = row["DefectGroup_ID"] != DBNull.Value ? Convert.ToInt32(row["DefectGroup_ID"]) : (int?)null,
                                        DefectName_ID = row["DefectName_ID"] != DBNull.Value ? Convert.ToInt32(row["DefectName_ID"]) : (int?)null,
                                        RootCause = row["RootCause"].ToString(),
                                        InformationCollected = row["InformationCollected"].ToString(),
                                        CallClosureDateAndTime = row["CallClosureDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallClosureDateAndTime"]) : (DateTime?)null,
                                        ClosureType_ID = row["ClosureType_ID"] != DBNull.Value ? Convert.ToInt32(row["ClosureType_ID"]) : (int?)null,
                                        ClosingDescription = row["ClosingDescription"].ToString(),
                                        Company_ID = row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0,
                                        Branch_ID = row["Branch_ID"] != DBNull.Value ? Convert.ToInt32(row["Branch_ID"]) : 0,
                                        ModifiedDate = row["ModifiedDate"] != DBNull.Value ? Convert.ToDateTime(row["ModifiedDate"]) : (DateTime?)null,
                                        ModifiedBy_ID = row["ModifiedBy_ID"] != DBNull.Value ? Convert.ToInt32(row["ModifiedBy_ID"]) : (int?)null,
                                        Document_no = row["Document_no"] != DBNull.Value ? Convert.ToInt32(row["Document_no"]) : (int?)null,
                                        CaseType_ID = row["CaseType_ID"] != DBNull.Value ? Convert.ToInt32(row["CaseType_ID"]) : 0,
                                        ActionRemarks = row["ActionRemarks"].ToString(),
                                        Product_ID = row["Product_ID"] != DBNull.Value ? Convert.ToInt32(row["Product_ID"]) : (int?)null,
                                        CustomerRating = row["CustomerRating"] != DBNull.Value ? Convert.ToInt32(row["CustomerRating"]) : (int?)null,
                                        FinancialYear = row["FinancialYear"] != DBNull.Value ? Convert.ToInt32(row["FinancialYear"]) : (int?)null,
                                        IsCallBlocked = row["IsCallBlocked"] != DBNull.Value ? Convert.ToBoolean(row["IsCallBlocked"]) : (bool?)null,
                                        StockBlocking_ID = row["StockBlocking_ID"] != DBNull.Value ? Convert.ToInt32(row["StockBlocking_ID"]) : (int?)null,
                                        EnquiryStage_ID = row["EnquiryStage_ID"] != DBNull.Value ? Convert.ToInt32(row["EnquiryStage_ID"]) : (int?)null,
                                        SalesQuotation_ID = row["SalesQuotation_ID"] != DBNull.Value ? Convert.ToInt32(row["SalesQuotation_ID"]) : (int?)null,
                                        SalesQuotationNumber = row["SalesQuotationNumber"].ToString(),
                                        SalesOrder_ID = row["SalesOrder_ID"] != DBNull.Value ? Convert.ToInt32(row["SalesOrder_ID"]) : (int?)null,
                                        SalesOrderNumber = row["SalesOrderNumber"].ToString(),
                                        SalesOrderDate = row["SalesOrderDate"] != DBNull.Value ? Convert.ToDateTime(row["SalesOrderDate"]) : (DateTime?)null,
                                        CallOwner_ID = row["CallOwner_ID"] != DBNull.Value ? Convert.ToInt32(row["CallOwner_ID"]) : (int?)null,
                                        Flexi1 = row["Flexi1"].ToString(),
                                        Flexi2 = row["Flexi2"].ToString(),
                                        ResolutionTime = row["ResolutionTime"].ToString(),
                                        ResponseTime = row["ResponseTime"].ToString(),
                                        AttachmentCount = row["AttachmentCount"] != DBNull.Value ? Convert.ToByte(row["AttachmentCount"]) : (byte?)null,
                                        CustomerFeedbackDate = row["CustomerFeedbackDate"] != DBNull.Value ? Convert.ToDateTime(row["CustomerFeedbackDate"]) : (DateTime?)null,
                                        IsNegetiveFeedback = row["IsNegetiveFeedback"] != DBNull.Value ? Convert.ToBoolean(row["IsNegetiveFeedback"]) : (bool?)null,
                                        IsDealer = row["IsDealer"] != DBNull.Value ? Convert.ToBoolean(row["IsDealer"]) : (bool?)null,
                                        IsDealerList = row["IsDealerList"] != DBNull.Value ? Convert.ToByte(row["IsDealerList"]) : (byte?)null,
                                        ProductRateType = row["ProductRateType"] != DBNull.Value ? Convert.ToByte(row["ProductRateType"]) : (byte?)null,
                                        ChildTicket_Sequence_ID = row["ChildTicket_Sequence_ID"] != DBNull.Value ? Convert.ToInt32(row["ChildTicket_Sequence_ID"]) : (int?)null,
                                        ResponseTime24 = row["ResponseTime24"].ToString(),
                                        ResolutionTime24 = row["ResolutionTime24"].ToString(),
                                        Current_AssignTo = row["Current_AssignTo"] != DBNull.Value ? Convert.ToInt32(row["Current_AssignTo"]) : (int?)null,
                                        ContractorID = row["ContractorID"] != DBNull.Value ? Convert.ToInt32(row["ContractorID"]) : (int?)null,
                                        ContractorContactPerson_ID = row["ContractorContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(row["ContractorContactPerson_ID"]) : (int?)null,
                                        ScheduledType_ID = row["ScheduledType_ID"] != DBNull.Value ? Convert.ToInt32(row["ScheduledType_ID"]) : (int?)null,
                                        ExpectedArrivalDateTime = row["ExpectedArrivalDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpectedArrivalDateTime"]) : (DateTime?)null,
                                        ActualArrivalDateTime = row["ActualArrivalDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ActualArrivalDateTime"]) : (DateTime?)null,
                                        ExpectedDepartureDateTime = row["ExpectedDepartureDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpectedDepartureDateTime"]) : (DateTime?)null,
                                        ActualDepartureDateTime = row["ActualDepartureDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ActualDepartureDateTime"]) : (DateTime?)null,
                                        NoofTechs = row["NoofTechs"] != DBNull.Value ? Convert.ToInt32(row["NoofTechs"]) : (int?)null,
                                        ShiftHours = row["ShiftHours"] != DBNull.Value ? Convert.ToInt32(row["ShiftHours"]) : (int?)null,
                                        IsWIPBay = row["IsWIPBay"] != DBNull.Value ? Convert.ToBoolean(row["IsWIPBay"]) : (bool?)null,
                                    };

                                    // Add to your list
                                    SRequestAll.Add(serviceRequest);
                                }
                            }
                            // Get TimeSlot value using ADO.NET from stored procedure
                            using (SqlCommand timeSlotCmd = new SqlCommand("SP_AMERP_HelpDesk_GetTimeSlot", conn))
                            {
                                timeSlotCmd.CommandType = CommandType.StoredProcedure;
                                timeSlotCmd.Parameters.AddWithValue("@CompanyID", ResolutionTimeOfClosedCasesdobj.Company_ID);
                                timeSlotCmd.Parameters.AddWithValue("@ParamName", "TIMESLOT");

                                string TimeSlot = Convert.ToString(timeSlotCmd.ExecuteScalar());  // ExecuteScalar to get single value

                                if (!string.IsNullOrEmpty(TimeSlot))
                                {
                                    string[] TimeSlotArray = TimeSlot.Split(',');
                                    List<TimeSlot> timeSlotListTemp = new List<TimeSlot>();
                                    for (int i = 0; i < TimeSlotArray.Length; i++)
                                    {
                                        timeSlotListTemp.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0 });
                                    }
                                    timeSlotListTemp.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0 });

                                    foreach (var sr in SRequestAll)
                                    {
                                        int THour = Convert.ToInt32((sr.ResolutionTime).Split(':')[0]);
                                        timeSlotListTemp = CalculateTimeSlot(timeSlotListTemp, THour);
                                    }

                                    if (timeSlotListTemp != null)
                                    {
                                        foreach (var item in timeSlotListTemp)
                                        {
                                            timeSlotList.Add(item);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (timeSlotList != null)
                    {
                        for (int i = 0; i < timeSlotList.Count(); i++)
                        {
                            int index = i;
                            int j = index + 1;

                            while (j < timeSlotList.Count())
                            {
                                if (timeSlotList[i].Name == timeSlotList[j].Name)
                                {
                                    timeSlotList[i].Count += timeSlotList[j].Count;
                                    timeSlotList.RemoveAt(j);
                                }
                                else
                                {
                                    j++;
                                }
                            }
                        }
                    }
                }

                JsonResult = new
                {
                    timeSlotList = timeSlotList,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonResult);
        }
        #endregion


        #region ::: NumberOfCasesBasedOnIssueArea doubt Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To NumberOfCasesBasedOnIssueArea
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult NumberOfCasesBasedOnIssueArea(NumberOfCasesBasedOnIssueAreaList NumberOfCasesBasedOnIssueAreaobj, string connString, int LogException)
        {
            var JResult = default(dynamic);
            var SRCountListStatusReport = new List<ServiceRequestCount>();
            string frmdate = NumberOfCasesBasedOnIssueAreaobj.FromDate;
            string todate = NumberOfCasesBasedOnIssueAreaobj.ToDate;
            bool BusinessAreaAll = Convert.ToBoolean(NumberOfCasesBasedOnIssueAreaobj.BusinessAreaAll);
            bool BusinessAreaDomestic = Convert.ToBoolean(NumberOfCasesBasedOnIssueAreaobj.BusinessAreaDomestic);

            try
            {
                string CompanyIds = string.Empty;
                if (Uri.UnescapeDataString(NumberOfCasesBasedOnIssueAreaobj.Company) != null && Uri.UnescapeDataString(NumberOfCasesBasedOnIssueAreaobj.Company) != "")
                {
                    MD_Company companies = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(NumberOfCasesBasedOnIssueAreaobj.Company))).ToObject<MD_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;

                if (Uri.UnescapeDataString(NumberOfCasesBasedOnIssueAreaobj.Branch) != null && Uri.UnescapeDataString(NumberOfCasesBasedOnIssueAreaobj.Branch) != "")
                {
                    MD_Branch branchs = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(NumberOfCasesBasedOnIssueAreaobj.Branch))).ToObject<MD_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                }

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    foreach (var companyId in CompanyIds.Split(','))
                    {
                        int CompanyID = Convert.ToInt32(companyId);

                        // Fetch HD_ServiceRequest records using stored procedure
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequests", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@EmployeeID", NumberOfCasesBasedOnIssueAreaobj.Employee_ID);
                            cmd.Parameters.AddWithValue("@BusinessAreaAll", BusinessAreaAll);
                            cmd.Parameters.AddWithValue("@BusinessAreaDomestic", BusinessAreaDomestic);

                            var adapter = new SqlDataAdapter(cmd);
                            var SRequestAll = new DataTable();
                            adapter.Fill(SRequestAll);

                            DateTime? FromDate = string.IsNullOrEmpty(frmdate) ? (DateTime?)null : Convert.ToDateTime(frmdate);
                            DateTime? ToDate = string.IsNullOrEmpty(todate) ? (DateTime?)null : Convert.ToDateTime(todate).AddDays(1);

                            var filteredRequests = SRequestAll.AsEnumerable().Where(row =>
                                (FromDate == null || row.Field<DateTime>("CallDateAndTime") >= FromDate) &&
                                (ToDate == null || row.Field<DateTime>("CallDateAndTime") < ToDate)
                            );

                            // Filter for the current year and group by Type and Year, counting occurrences
                            using (SqlCommand countCmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByYear", conn))
                            {
                                countCmd.CommandType = CommandType.StoredProcedure;
                                countCmd.Parameters.AddWithValue("@CurrentYear", DateTime.Now.Year);

                                var countAdapter = new SqlDataAdapter(countCmd);
                                var countTable = new DataTable();
                                countAdapter.Fill(countTable);

                                foreach (DataRow row in countTable.Rows)
                                {
                                    SRCountListStatusReport.Add(new ServiceRequestCount
                                    {
                                        Type = row["Type"].ToString(),
                                        Year = Convert.ToInt32(row["Year"]),
                                        Count = Convert.ToInt32(row["Count"])
                                    });
                                }
                            }
                        }
                    }
                }

                JResult = new
                {
                    IssueAreaData = SRCountListStatusReport.Select(ct => new
                    {
                        Name = ct.Type,
                        Count = ct.Count,
                        BranchShortCode = ct.BranchShortCode
                    })
                };


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JResult);
        }
        #endregion


        #region ::: getCallSummary Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To get Call Summary
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult getCallSummary(getCallSummaryList getCallSummaryobj, string connString, int LogException, string dbname)
        {
            var jsonResult = default(dynamic);
            int OB = 0, NC = 0, CCOB = 0, CCN = 0, TCC = 0, WIP = 0, OH = 0, ESC = 0, CB = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    int WorkFlow_ID = Common.GetWorkFlowID("Case Registration", dbname, connString, LogException);
                    // Query to get the EndStepID
                    int EndStepID = 0;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepTypeID", conn))
                    {
                        EndStepID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Query to get the ClosedStatusID
                    int ClosedStatusID = 0;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@WorkFlow_ID", WorkFlow_ID); // Assuming WorkFlow_ID is available
                        cmd.Parameters.AddWithValue("@EndStepID", EndStepID);
                        ClosedStatusID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Query to get CreatedStatusID
                    int CreatedStatusID = 0;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusIDByName", conn))
                    {
                        CreatedStatusID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Query to get InProgressStatusID
                    int InProgressStatusID = 0;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusIDInProgress", conn))
                    {
                        InProgressStatusID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Query to get OnHoldStatusID
                    int OnHoldStatusID = 0;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusIDHold", conn))
                    {
                        OnHoldStatusID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Query to get EscalatedStatusID
                    int EscalatedStatusID = 0;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusIDEscalated", conn))
                    {
                        EscalatedStatusID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    bool BusinessAreaAll = Convert.ToBoolean(getCallSummaryobj.BusinessAreaAll);
                    bool BusinessAreaDomestic = Convert.ToBoolean(getCallSummaryobj.BusinessAreaDomestic);

                    string CompanyIds = Uri.UnescapeDataString(HttpUtility.UrlDecode(getCallSummaryobj.Company));
                    string BranchIds = Uri.UnescapeDataString(HttpUtility.UrlDecode(getCallSummaryobj.Branch));

                    int? isImportExport = BusinessAreaAll ? (int?)null : (BusinessAreaDomestic ? 0 : 1);

                    // Parse Companies
                    var companyIds = JsonConvert.DeserializeObject<MD_Company>(CompanyIds)?
                        .Companies.Select(c => c.ID)
                        .ToList() ?? new List<int>();

                    // Parse Branches
                    var branchIds = JsonConvert.DeserializeObject<MD_Branch>(BranchIds)?
                        .Branchs.Select(b => b.ID)
                        .ToList() ?? new List<int>();

                    // Nested loop with error-safe parsing
                    foreach (var companyId in companyIds)
                    {
                        foreach (var branchId in branchIds)
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCallSummaryCounts", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Company_ID", companyId);
                                cmd.Parameters.AddWithValue("@Branch_ID", branchId);
                                cmd.Parameters.AddWithValue("@ClosedStatusID", ClosedStatusID);
                                cmd.Parameters.AddWithValue("@CreatedStatusID", CreatedStatusID);
                                cmd.Parameters.AddWithValue("@InProgressStatusID", InProgressStatusID);
                                cmd.Parameters.AddWithValue("@OnHoldStatusID", OnHoldStatusID);
                                cmd.Parameters.AddWithValue("@EscalatedStatusID", EscalatedStatusID);
                                cmd.Parameters.AddWithValue("@IsImportExport", isImportExport);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        OB += reader.GetInt32(reader.GetOrdinal("OpeningBalance"));
                                        NC += reader.GetInt32(reader.GetOrdinal("NewCalls"));
                                        CCOB += reader.GetInt32(reader.GetOrdinal("CallsClosedOpeningBalance"));
                                        CCN += reader.GetInt32(reader.GetOrdinal("CallsClosedNew"));
                                        WIP += reader.GetInt32(reader.GetOrdinal("WorkInProgress"));
                                        OH += reader.GetInt32(reader.GetOrdinal("OnHold"));
                                        ESC += reader.GetInt32(reader.GetOrdinal("Escalated"));
                                    }
                                }
                            }
                        }
                    }

                    TCC = CCOB + CCN;
                    CB = WIP;

                    jsonResult = new
                    {
                        OB,
                        NC,
                        CCOB,
                        CCN,
                        TCC,
                        WIP,
                        OH,
                        ESC,
                        CB
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    OB,
                    NC,
                    CCOB,
                    CCN,
                    TCC,
                    WIP,
                    OH,
                    ESC,
                    CB
                };
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SelectGraphPendingCasesData Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Select Graph Pending Cases Data
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectGraphPendingCasesData(SelectGraphPendingCasesDataList SelectGraphPendingCasesDataobj, string connString, int LogException)
        {
            var JResult = default(dynamic);
            var timeSlotList = new List<TimeSlot>();
            string frmdate = SelectGraphPendingCasesDataobj.FromDate;
            string todate = SelectGraphPendingCasesDataobj.ToDate;
            bool businessAreaAll = Convert.ToBoolean(SelectGraphPendingCasesDataobj.BusinessAreaAll);
            bool businessAreaDomestic = Convert.ToBoolean(SelectGraphPendingCasesDataobj.BusinessAreaDomestic);
            string CompanyIds = string.Empty;
            string BranchIds = string.Empty;
            string businessAreaType = businessAreaAll ? "All" : (businessAreaDomestic ? "Domestic" : "Export");

            if (Uri.UnescapeDataString(SelectGraphPendingCasesDataobj.Company) != null && Uri.UnescapeDataString(SelectGraphPendingCasesDataobj.Company) != "")
            {
                MD_Company companies = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(SelectGraphPendingCasesDataobj.Company))).ToObject<MD_Company>();

                for (int i = 0; i < companies.Companies.Count; i++)
                {
                    CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                }
                CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
            }

            if (Uri.UnescapeDataString(SelectGraphPendingCasesDataobj.Branch) != null && Uri.UnescapeDataString(SelectGraphPendingCasesDataobj.Branch) != "")
            {
                MD_Branch branchs = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(SelectGraphPendingCasesDataobj.Branch))).ToObject<MD_Branch>();

                for (int i = 0; i < branchs.Branchs.Count; i++)
                {
                    BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                }
                BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
            }

            var strCompanyIds = CompanyIds.Split(',');
            var strBranchIds = BranchIds.Split(',');

            string ageingReportIn = string.Empty;

            try
            {
                foreach (var companyIdStr in strCompanyIds)
                {
                    int companyId = int.Parse(companyIdStr.Trim());
                    foreach (var branchIdStr in strBranchIds)
                    {
                        int branchId = int.Parse(branchIdStr.Trim());

                        var pendingServiceRequests = GetPendingServiceRequests(connString, companyId, branchId, Convert.ToInt32(SelectGraphPendingCasesDataobj.Employee_ID), businessAreaType);
                        if (frmdate != string.Empty || todate != string.Empty)
                        {
                            var fromDate = frmdate != string.Empty ? DateTime.Parse(frmdate) : DateTime.MinValue;
                            var toDate = todate != string.Empty ? DateTime.Parse(todate).AddDays(1) : DateTime.MaxValue;
                            pendingServiceRequests = pendingServiceRequests.Where(i => i.CallDateAndTime >= fromDate && i.CallDateAndTime < toDate).ToList();
                        }

                        ageingReportIn = GetCompanyParam(connString, companyId, "AGEINGREPORT");
                        if (!string.IsNullOrEmpty(ageingReportIn))
                        {
                            string timeSlot = GetCompanyParam(connString, companyId, ageingReportIn == "HOURS" ? "TIMESLOT" : "DAYSLOT");
                            string[] timeSlotArray = timeSlot.Split(',');

                            List<TimeSlot> timeSlotListTemp = timeSlotArray.Select(t => new TimeSlot { Name = t, Count = 0 }).ToList();
                            timeSlotListTemp.Add(new TimeSlot { Name = (int.Parse(timeSlotArray.Last()) + 1).ToString(), Count = 0 });

                            foreach (var sr in pendingServiceRequests)
                            {
                                int timeValue = ageingReportIn == "HOURS"
                                    ? (int)(DateTime.Now - sr.CallDateAndTime).TotalHours
                                    : (int)(DateTime.Now - sr.CallDateAndTime).TotalDays;
                                timeSlotListTemp = CalculateTimeSlot(timeSlotListTemp, timeValue);
                            }
                            timeSlotList.AddRange(timeSlotListTemp);
                        }
                    }
                }

                JResult = new { timeSlotList, AgeingReportIn = ageingReportIn.ToUpper() };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(JResult);
        }

        private static List<HD_ServiceRequest> GetPendingServiceRequests(string connString, int companyId, int branchId, int employeeId, string businessAreaType)
        {
            var pendingServiceRequests = new List<HD_ServiceRequest>();

            using (var conn = new SqlConnection(connString))
            using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPendingServiceRequests", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyId);
                cmd.Parameters.AddWithValue("@BranchID", branchId);
                cmd.Parameters.AddWithValue("@EmployeeID", employeeId);
                cmd.Parameters.AddWithValue("@BusinessAreaType", businessAreaType);

                // Load data into DataTable
                DataTable dt = new DataTable();
                using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                {
                    da.Fill(dt);
                }

                // Iterate over DataTable rows and map to HD_ServiceRequest objects
                foreach (DataRow row in dt.Rows)
                {
                    pendingServiceRequests.Add(new HD_ServiceRequest
                    {
                        ServiceRequest_ID = dt.Columns.Contains("RequestID") && row["RequestID"] != DBNull.Value ? Convert.ToInt32(row["RequestID"]) : 0,
                        ServiceRequestNumber = dt.Columns.Contains("ServiceRequestNumber") && row["ServiceRequestNumber"] != DBNull.Value ? row["ServiceRequestNumber"].ToString() : string.Empty,
                        ServiceRequestDate = dt.Columns.Contains("ServiceRequestDate") && row["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(row["ServiceRequestDate"]) : DateTime.MinValue,
                        Quotation_ID = dt.Columns.Contains("Quotation_ID") && row["Quotation_ID"] != DBNull.Value ? Convert.ToInt32(row["Quotation_ID"]) : (int?)null,
                        QuotationNumber = dt.Columns.Contains("QuotationNumber") && row["QuotationNumber"] != DBNull.Value ? row["QuotationNumber"].ToString() : string.Empty,
                        JobCard_ID = dt.Columns.Contains("JobCard_ID") && row["JobCard_ID"] != DBNull.Value ? Convert.ToInt32(row["JobCard_ID"]) : (int?)null,
                        JobCardNumber = dt.Columns.Contains("JobCardNumber") && row["JobCardNumber"] != DBNull.Value ? row["JobCardNumber"].ToString() : string.Empty,
                        CallStatus_ID = dt.Columns.Contains("CallStatus_ID") && row["CallStatus_ID"] != DBNull.Value ? Convert.ToInt32(row["CallStatus_ID"]) : 0,
                        ParentIssue_ID = dt.Columns.Contains("ParentIssue_ID") && row["ParentIssue_ID"] != DBNull.Value ? Convert.ToInt32(row["ParentIssue_ID"]) : (int?)null,
                        Product_Unique_Number = dt.Columns.Contains("Product_Unique_Number") && row["Product_Unique_Number"] != DBNull.Value ? row["Product_Unique_Number"].ToString() : string.Empty,
                        Party_ID = dt.Columns.Contains("Party_ID") && row["Party_ID"] != DBNull.Value ? Convert.ToInt32(row["Party_ID"]) : 0,
                        PartyContactPerson_ID = dt.Columns.Contains("PartyContactPerson_ID") && row["PartyContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(row["PartyContactPerson_ID"]) : 0,
                        Model_ID = dt.Columns.Contains("Model_ID") && row["Model_ID"] != DBNull.Value ? Convert.ToInt32(row["Model_ID"]) : (int?)null,
                        Brand_ID = dt.Columns.Contains("Brand_ID") && row["Brand_ID"] != DBNull.Value ? Convert.ToInt32(row["Brand_ID"]) : (int?)null,
                        ProductType_ID = dt.Columns.Contains("ProductType_ID") && row["ProductType_ID"] != DBNull.Value ? Convert.ToInt32(row["ProductType_ID"]) : (int?)null,
                        SerialNumber = dt.Columns.Contains("SerialNumber") && row["SerialNumber"] != DBNull.Value ? row["SerialNumber"].ToString() : string.Empty,
                        ProductReading = dt.Columns.Contains("ProductReading") && row["ProductReading"] != DBNull.Value ? Convert.ToInt32(row["ProductReading"]) : (int?)null,
                        IsUnderWarranty = dt.Columns.Contains("IsUnderWarranty") && row["IsUnderWarranty"] != DBNull.Value ? Convert.ToBoolean(row["IsUnderWarranty"]) : false,
                        CallMode_ID = dt.Columns.Contains("CallMode_ID") && row["CallMode_ID"] != DBNull.Value ? Convert.ToInt32(row["CallMode_ID"]) : 0,
                        CallPriority_ID = dt.Columns.Contains("CallPriority_ID") && row["CallPriority_ID"] != DBNull.Value ? Convert.ToInt32(row["CallPriority_ID"]) : (int?)null,
                        CallComplexity_ID = dt.Columns.Contains("CallComplexity_ID") && row["CallComplexity_ID"] != DBNull.Value ? Convert.ToInt32(row["CallComplexity_ID"]) : 0,
                        CallDateAndTime = dt.Columns.Contains("CallDateAndTime") && row["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallDateAndTime"]) : DateTime.MinValue,
                        PromisedCompletionDate = dt.Columns.Contains("PromisedCompletionDate") && row["PromisedCompletionDate"] != DBNull.Value ? Convert.ToDateTime(row["PromisedCompletionDate"]) : (DateTime?)null,
                        Region_ID = dt.Columns.Contains("Region_ID") && row["Region_ID"] != DBNull.Value ? Convert.ToInt32(row["Region_ID"]) : (int?)null,
                        CallDescription = dt.Columns.Contains("CallDescription") && row["CallDescription"] != DBNull.Value ? row["CallDescription"].ToString() : string.Empty,
                        IssueArea_ID = dt.Columns.Contains("IssueArea_ID") && row["IssueArea_ID"] != DBNull.Value ? Convert.ToInt32(row["IssueArea_ID"]) : (int?)null,
                        IssueSubArea_ID = dt.Columns.Contains("IssueSubArea_ID") && row["IssueSubArea_ID"] != DBNull.Value ? Convert.ToInt32(row["IssueSubArea_ID"]) : (int?)null,
                        FunctionGroup_ID = dt.Columns.Contains("FunctionGroup_ID") && row["FunctionGroup_ID"] != DBNull.Value ? Convert.ToInt32(row["FunctionGroup_ID"]) : (int?)null,
                        IsUnderBreakDown = dt.Columns.Contains("IsUnderBreakDown") && row["IsUnderBreakDown"] != DBNull.Value ? Convert.ToBoolean(row["IsUnderBreakDown"]) : false,
                        QuestionaryLevel1_ID = dt.Columns.Contains("QuestionaryLevel1_ID") && row["QuestionaryLevel1_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel1_ID"]) : (int?)null,
                        QuestionaryLevel2_ID = dt.Columns.Contains("QuestionaryLevel2_ID") && row["QuestionaryLevel2_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel2_ID"]) : (int?)null,
                        QuestionaryLevel3_ID = dt.Columns.Contains("QuestionaryLevel3_ID") && row["QuestionaryLevel3_ID"] != DBNull.Value ? Convert.ToInt32(row["QuestionaryLevel3_ID"]) : (int?)null,
                        DefectGroup_ID = dt.Columns.Contains("DefectGroup_ID") && row["DefectGroup_ID"] != DBNull.Value ? Convert.ToInt32(row["DefectGroup_ID"]) : (int?)null,
                        DefectName_ID = dt.Columns.Contains("DefectName_ID") && row["DefectName_ID"] != DBNull.Value ? Convert.ToInt32(row["DefectName_ID"]) : (int?)null,
                        RootCause = dt.Columns.Contains("RootCause") && row["RootCause"] != DBNull.Value ? row["RootCause"].ToString() : string.Empty,
                        InformationCollected = dt.Columns.Contains("InformationCollected") && row["InformationCollected"] != DBNull.Value ? row["InformationCollected"].ToString() : string.Empty,
                        CallClosureDateAndTime = dt.Columns.Contains("CallClosureDateAndTime") && row["CallClosureDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallClosureDateAndTime"]) : (DateTime?)null,
                        ClosureType_ID = dt.Columns.Contains("ClosureType_ID") && row["ClosureType_ID"] != DBNull.Value ? Convert.ToInt32(row["ClosureType_ID"]) : (int?)null,
                        ClosingDescription = dt.Columns.Contains("ClosingDescription") && row["ClosingDescription"] != DBNull.Value ? row["ClosingDescription"].ToString() : string.Empty,
                        Company_ID = dt.Columns.Contains("Company_ID") && row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0,
                        Branch_ID = dt.Columns.Contains("Branch_ID") && row["Branch_ID"] != DBNull.Value ? Convert.ToInt32(row["Branch_ID"]) : 0,
                        ModifiedDate = dt.Columns.Contains("ModifiedDate") && row["ModifiedDate"] != DBNull.Value ? Convert.ToDateTime(row["ModifiedDate"]) : (DateTime?)null,
                        ModifiedBy_ID = dt.Columns.Contains("ModifiedBy_ID") && row["ModifiedBy_ID"] != DBNull.Value ? Convert.ToInt32(row["ModifiedBy_ID"]) : (int?)null,
                        Document_no = dt.Columns.Contains("Document_no") && row["Document_no"] != DBNull.Value ? Convert.ToInt32(row["Document_no"]) : (int?)null,
                        CaseType_ID = dt.Columns.Contains("CaseType_ID") && row["CaseType_ID"] != DBNull.Value ? Convert.ToInt32(row["CaseType_ID"]) : 0,
                        ActionRemarks = dt.Columns.Contains("ActionRemarks") && row["ActionRemarks"] != DBNull.Value ? row["ActionRemarks"].ToString() : string.Empty,
                        Product_ID = dt.Columns.Contains("Product_ID") && row["Product_ID"] != DBNull.Value ? Convert.ToInt32(row["Product_ID"]) : (int?)null,
                        CustomerRating = dt.Columns.Contains("CustomerRating") && row["CustomerRating"] != DBNull.Value ? Convert.ToInt32(row["CustomerRating"]) : (int?)null,
                        FinancialYear = dt.Columns.Contains("FinancialYear") && row["FinancialYear"] != DBNull.Value ? Convert.ToInt32(row["FinancialYear"]) : (int?)null,
                        IsCallBlocked = dt.Columns.Contains("IsCallBlocked") && row["IsCallBlocked"] != DBNull.Value ? Convert.ToBoolean(row["IsCallBlocked"]) : (bool?)null,
                        StockBlocking_ID = dt.Columns.Contains("StockBlocking_ID") && row["StockBlocking_ID"] != DBNull.Value ? Convert.ToInt32(row["StockBlocking_ID"]) : (int?)null,
                        EnquiryStage_ID = dt.Columns.Contains("EnquiryStage_ID") && row["EnquiryStage_ID"] != DBNull.Value ? Convert.ToInt32(row["EnquiryStage_ID"]) : (int?)null,
                        SalesQuotation_ID = dt.Columns.Contains("SalesQuotation_ID") && row["SalesQuotation_ID"] != DBNull.Value ? Convert.ToInt32(row["SalesQuotation_ID"]) : (int?)null,
                        SalesQuotationNumber = dt.Columns.Contains("SalesQuotationNumber") && row["SalesQuotationNumber"] != DBNull.Value ? row["SalesQuotationNumber"].ToString() : string.Empty,
                        SalesOrder_ID = dt.Columns.Contains("SalesOrder_ID") && row["SalesOrder_ID"] != DBNull.Value ? Convert.ToInt32(row["SalesOrder_ID"]) : (int?)null,
                        SalesOrderNumber = dt.Columns.Contains("SalesOrderNumber") && row["SalesOrderNumber"] != DBNull.Value ? row["SalesOrderNumber"].ToString() : string.Empty,
                        SalesOrderDate = dt.Columns.Contains("SalesOrderDate") && row["SalesOrderDate"] != DBNull.Value ? Convert.ToDateTime(row["SalesOrderDate"]) : (DateTime?)null,
                        CallOwner_ID = dt.Columns.Contains("CallOwner_ID") && row["CallOwner_ID"] != DBNull.Value ? Convert.ToInt32(row["CallOwner_ID"]) : (int?)null,
                        Flexi1 = dt.Columns.Contains("Flexi1") && row["Flexi1"] != DBNull.Value ? row["Flexi1"].ToString() : string.Empty,
                        Flexi2 = dt.Columns.Contains("Flexi2") && row["Flexi2"] != DBNull.Value ? row["Flexi2"].ToString() : string.Empty,
                        ResolutionTime = dt.Columns.Contains("ResolutionTime") && row["ResolutionTime"] != DBNull.Value ? row["ResolutionTime"].ToString() : string.Empty,
                        ResponseTime = dt.Columns.Contains("ResponseTime") && row["ResponseTime"] != DBNull.Value ? row["ResponseTime"].ToString() : string.Empty,
                        AttachmentCount = dt.Columns.Contains("AttachmentCount") && row["AttachmentCount"] != DBNull.Value ? Convert.ToByte(row["AttachmentCount"]) : (byte?)null,
                        CustomerFeedbackDate = dt.Columns.Contains("CustomerFeedbackDate") && row["CustomerFeedbackDate"] != DBNull.Value ? Convert.ToDateTime(row["CustomerFeedbackDate"]) : (DateTime?)null,
                        IsNegetiveFeedback = dt.Columns.Contains("IsNegetiveFeedback") && row["IsNegetiveFeedback"] != DBNull.Value ? Convert.ToBoolean(row["IsNegetiveFeedback"]) : (bool?)null,
                        IsDealer = dt.Columns.Contains("IsDealer") && row["IsDealer"] != DBNull.Value ? Convert.ToBoolean(row["IsDealer"]) : (bool?)null,
                        IsDealerList = dt.Columns.Contains("IsDealerList") && row["IsDealerList"] != DBNull.Value ? Convert.ToByte(row["IsDealerList"]) : (byte?)null,
                        ProductRateType = dt.Columns.Contains("ProductRateType") && row["ProductRateType"] != DBNull.Value ? Convert.ToByte(row["ProductRateType"]) : (byte?)null,
                        ChildTicket_Sequence_ID = dt.Columns.Contains("ChildTicket_Sequence_ID") && row["ChildTicket_Sequence_ID"] != DBNull.Value ? Convert.ToInt32(row["ChildTicket_Sequence_ID"]) : (int?)null,
                        ResponseTime24 = dt.Columns.Contains("ResponseTime24") && row["ResponseTime24"] != DBNull.Value ? row["ResponseTime24"].ToString() : string.Empty,
                        ResolutionTime24 = dt.Columns.Contains("ResolutionTime24") && row["ResolutionTime24"] != DBNull.Value ? row["ResolutionTime24"].ToString() : string.Empty,
                        Current_AssignTo = dt.Columns.Contains("Current_AssignTo") && row["Current_AssignTo"] != DBNull.Value ? Convert.ToInt32(row["Current_AssignTo"]) : (int?)null,
                        ContractorID = dt.Columns.Contains("ContractorID") && row["ContractorID"] != DBNull.Value ? Convert.ToInt32(row["ContractorID"]) : (int?)null,
                        ContractorContactPerson_ID = dt.Columns.Contains("ContractorContactPerson_ID") && row["ContractorContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(row["ContractorContactPerson_ID"]) : (int?)null,
                        ScheduledType_ID = dt.Columns.Contains("ScheduledType_ID") && row["ScheduledType_ID"] != DBNull.Value ? Convert.ToInt32(row["ScheduledType_ID"]) : (int?)null,
                        ExpectedArrivalDateTime = dt.Columns.Contains("ExpectedArrivalDateTime") && row["ExpectedArrivalDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpectedArrivalDateTime"]) : (DateTime?)null,
                        ActualArrivalDateTime = dt.Columns.Contains("ActualArrivalDateTime") && row["ActualArrivalDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ActualArrivalDateTime"]) : (DateTime?)null,
                        ExpectedDepartureDateTime = dt.Columns.Contains("ExpectedDepartureDateTime") && row["ExpectedDepartureDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ExpectedDepartureDateTime"]) : (DateTime?)null,
                        ActualDepartureDateTime = dt.Columns.Contains("ActualDepartureDateTime") && row["ActualDepartureDateTime"] != DBNull.Value ? Convert.ToDateTime(row["ActualDepartureDateTime"]) : (DateTime?)null,
                        NoofTechs = dt.Columns.Contains("NoofTechs") && row["NoofTechs"] != DBNull.Value ? Convert.ToInt32(row["NoofTechs"]) : (int?)null,
                        ShiftHours = dt.Columns.Contains("ShiftHours") && row["ShiftHours"] != DBNull.Value ? Convert.ToInt32(row["ShiftHours"]) : (int?)null,
                        IsWIPBay = dt.Columns.Contains("IsWIPBay") && row["IsWIPBay"] != DBNull.Value ? Convert.ToBoolean(row["IsWIPBay"]) : (bool?)null
                    });
                }
            }
            return pendingServiceRequests;
        }

        private static string GetCompanyParam(string connString, int companyId, string paramName)
        {
            using (var conn = new SqlConnection(connString))
            using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyParam", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyId);
                cmd.Parameters.AddWithValue("@ParamName", paramName);

                conn.Open();
                return cmd.ExecuteScalar()?.ToString();
            }
        }
        #endregion


        #region ::: SelectHyperLinkStatus Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Select HyperLink Status
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectHyperLinkStatus(SelectHyperLinkStatusList SelectHyperLinkStatusObj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            List<AgeingAnalysis> RSList = new List<AgeingAnalysis>();
            MD_Branch branchs = new MD_Branch();
            string Branch_IDs = "";
            if (Uri.UnescapeDataString(SelectHyperLinkStatusObj.BranchObj) != null && Uri.UnescapeDataString(SelectHyperLinkStatusObj.BranchObj) != "")
            {
                branchs = JObject.Parse(Uri.UnescapeDataString(Common.DecryptString(SelectHyperLinkStatusObj.BranchObj))).ToObject<MD_Branch>();

                foreach (var i in branchs.Branchs)
                {
                    Branch_IDs += i.ID + ",";
                }
                Branch_IDs = Branch_IDs.Remove(Branch_IDs.LastIndexOf(","));
            }

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetAgeingAnalysis", connection);
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    cmd.Parameters.AddWithValue("@Company_ID", SelectHyperLinkStatusObj.Company_ID);
                    cmd.Parameters.AddWithValue("@Branch_IDs", Branch_IDs); // Replace with actual branch IDs
                    cmd.Parameters.AddWithValue("@Status", SelectHyperLinkStatusObj.Status);
                    cmd.Parameters.AddWithValue("@BusinessAreaAll", Convert.ToBoolean(SelectHyperLinkStatusObj.BusinessAreaAll));
                    cmd.Parameters.AddWithValue("@BusinessAreaDomestic", Convert.ToBoolean(SelectHyperLinkStatusObj.BusinessAreaDomestic));

                    connection.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    while (reader.Read())
                    {
                        RSList.Add(new AgeingAnalysis
                        {
                            Ageing = reader["AGEING"].ToString(),
                            Count = Convert.ToInt32(reader["COUNT"])
                        });
                    }
                }

                count = RSList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = (from result in RSList
                            select new
                            {
                                CallOwner = result.Ageing,
                                Count = result.Count
                            }).ToArray()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion


        #region ::: SelectHyperLinkStatusByCallOwner Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Select HyperLink Status By CallOwner
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectHyperLinkStatusByCallOwner(SelectHyperLinkStatusByCallOwnerList SelectHyperLinkStatusByCallOwnerobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            DateTime localTime = DateTime.Now;
            string Branch_IDs = "";
            MD_Branch branchs = new MD_Branch();
            if (Uri.UnescapeDataString(SelectHyperLinkStatusByCallOwnerobj.BranchObj) != null && Uri.UnescapeDataString(SelectHyperLinkStatusByCallOwnerobj.BranchObj) != "")
            {
                branchs = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(SelectHyperLinkStatusByCallOwnerobj.BranchObj))).ToObject<MD_Branch>();

                foreach (var i in branchs.Branchs)
                {
                    Branch_IDs += i.ID + ",";
                }
                Branch_IDs = Branch_IDs.Remove(Branch_IDs.LastIndexOf(","));
            }
            try
            {
                // SQL connection
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string AgeingReportIn = null;
                    // Set up the command to execute the stored procedure
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetAgeingReportParam", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add the input parameter for Company_ID
                        cmd.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int));
                        cmd.Parameters["@Company_ID"].Value = SelectHyperLinkStatusByCallOwnerobj.Company_ID;

                        // Open the connection
                        conn.Open();

                        // Execute the command and retrieve the result
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                // Read the first row and get the Param_value
                                while (reader.Read())
                                {
                                    AgeingReportIn = reader["ParamValue"] as string; // Retrieve the Param_value
                                }
                            }
                        }
                    }

                    // SQL command
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_SelectHyperLinkStatusByCallOwner", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Parameters
                        cmd.Parameters.AddWithValue("@Company_ID", SelectHyperLinkStatusByCallOwnerobj.Company_ID);
                        cmd.Parameters.AddWithValue("@CallOwner", SelectHyperLinkStatusByCallOwnerobj.CallOwner);
                        cmd.Parameters.AddWithValue("@Branch_IDs", Branch_IDs); // Branch IDs as comma-separated string
                        cmd.Parameters.AddWithValue("@Status", SelectHyperLinkStatusByCallOwnerobj.Status.ToString());
                        cmd.Parameters.AddWithValue("@BusinessAreaAll", Convert.ToBoolean(SelectHyperLinkStatusByCallOwnerobj.BusinessAreaAll));
                        cmd.Parameters.AddWithValue("@BusinessAreaDomestic", Convert.ToBoolean(SelectHyperLinkStatusByCallOwnerobj.BusinessAreaDomestic));

                        // Execute the command and retrieve data
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var serviceRequests = new List<ServiceRequestAgeing>();

                            while (reader.Read())
                            {
                                var serviceRequest = new ServiceRequestAgeing
                                {
                                    ServiceRequest_ID = !reader.IsDBNull(reader.GetOrdinal("SERVICEREQUEST_ID"))
                                    ? reader.GetInt32(reader.GetOrdinal("SERVICEREQUEST_ID"))
                                    : 0, // Default value for integers
                                    ServiceRequestNumber = !reader.IsDBNull(reader.GetOrdinal("SERVICEREQUESTNUMBER"))
                                    ? reader.GetString(reader.GetOrdinal("SERVICEREQUESTNUMBER"))
                                    : string.Empty, // Default value for strings
                                    CallDateAndTime = !reader.IsDBNull(reader.GetOrdinal("CALLDATEANDTIMEDATE"))
                                    ? reader.GetDateTime(reader.GetOrdinal("CALLDATEANDTIMEDATE")).ToString("dd-MMM-yyyy")
                                    : string.Empty, // Default value for dates
                                    Party_Name = !reader.IsDBNull(reader.GetOrdinal("PARTY_NAME"))
                                    ? reader.GetString(reader.GetOrdinal("PARTY_NAME"))
                                    : string.Empty,
                                    Brand_Name = !reader.IsDBNull(reader.GetOrdinal("BRAND_NAME"))
                                    ? reader.GetString(reader.GetOrdinal("BRAND_NAME"))
                                    : string.Empty,
                                    ProductType_Name = !reader.IsDBNull(reader.GetOrdinal("PRODUCTTYPE_NAME"))
                                    ? reader.GetString(reader.GetOrdinal("PRODUCTTYPE_NAME"))
                                    : string.Empty,
                                    Model_Name = !reader.IsDBNull(reader.GetOrdinal("MODEL_NAME"))
                                    ? reader.GetString(reader.GetOrdinal("MODEL_NAME"))
                                    : string.Empty,
                                    SerialNumber = !reader.IsDBNull(reader.GetOrdinal("SERIALNUMBER"))
                                    ? reader.GetString(reader.GetOrdinal("SERIALNUMBER"))
                                    : string.Empty,
                                    status = !reader.IsDBNull(reader.GetOrdinal("STATUS"))
                                    ? reader.GetString(reader.GetOrdinal("STATUS"))
                                    : string.Empty,
                                    ActionRemarks = !reader.IsDBNull(reader.GetOrdinal("ACTIONREMARKS"))
                                    ? reader.GetString(reader.GetOrdinal("ACTIONREMARKS"))
                                    : string.Empty,
                                    NotesDescription = !reader.IsDBNull(reader.GetOrdinal("NOTESDESCRIPTION"))
                                    ? reader.GetString(reader.GetOrdinal("NOTESDESCRIPTION"))
                                    : string.Empty,
                                    Name = !reader.IsDBNull(reader.GetOrdinal("NAME"))
                                    ? reader.GetString(reader.GetOrdinal("NAME"))
                                    : string.Empty,
                                    Department = !reader.IsDBNull(reader.GetOrdinal("DEPARTMENT"))
                                    ? reader.GetString(reader.GetOrdinal("DEPARTMENT"))
                                    : string.Empty
                                };
                                serviceRequests.Add(serviceRequest);
                            }
                            var serviceRequestQuery = serviceRequests.AsQueryable();

                            // Now apply filtering, sorting, and pagination logic like LINQ
                            if (_search)
                            {
                                Filters filterobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                                if (filterobj.rules.Count() > 0)
                                {
                                    for (int i = 0; i < filterobj.rules.Count(); i++)
                                    {
                                        if (filterobj.rules.ElementAt(i).field == "AgeingHoursMinutes")
                                        {
                                            filterobj.rules.ElementAt(i).field = "AgeingHours";
                                        }
                                    }
                                    serviceRequestQuery = serviceRequestQuery.FilterSearch<ServiceRequestAgeing>(filterobj);
                                }
                            }

                            count = serviceRequestQuery.Count();

                            total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                            if (count < (rows * page) && count != 0)
                            {
                                page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                            }
                            jsonobj = new
                            {
                                TotalPages = total,
                                PageNo = page,
                                RecordCount = count,
                                rows = (from SR in serviceRequestQuery
                                        select new
                                        {
                                            SR.ServiceRequest_ID,
                                            ServiceRequestNumber = "<span key='" + SR.ServiceRequest_ID + "' style='cursor:pointer'  class='TxtHyperLink' onclick='OpenServiceRequest(this)'>" + SR.ServiceRequestNumber + "</span>",//Server.HtmlEncode(SR.ServiceRequestNumber),
                                            CallDate = SR.CallDateAndTime,
                                            Party_Name = SR.Party_Name,
                                            Brand_Name = SR.Brand_Name,
                                            ProductType_Name = SR.ProductType_Name,
                                            Model_Name = SR.Model_Name,
                                            SerialNumber = (SR.SerialNumber == null || SR.SerialNumber == "") ? "" : SR.SerialNumber,
                                            status = SR.status,
                                            AgeingHoursMinutes = Convert.ToInt32((Convert.ToInt32(localTime.Subtract(SR.CallDateOrder).TotalMinutes)) / 60).ToString() + ":" + ((Convert.ToInt32((localTime.Subtract(SR.CallDateOrder).TotalMinutes) % 60).ToString().Length == 1 ? "0" + Convert.ToInt32((localTime.Subtract(SR.CallDateOrder).TotalMinutes) % 60).ToString() : Convert.ToInt32((localTime.Subtract(SR.CallDateOrder).TotalMinutes) % 60).ToString()).ToString()),
                                            AgeingDays = SR.AgeingDays,
                                            AgeingDaysInt = SR.AgeingDaysInt,
                                            ActionRemarks = SR.ActionRemarks,
                                            NotesDescription = SR.NotesDescription,
                                            Name = SR.Name,
                                            Department = SR.Department
                                        }).ToList().Paginate(page, rows),
                                AgeingIn = AgeingReportIn.ToUpper()
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion


        #region ::: Export Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static async Task<object> Export(HelpDeskManagerLandingList HelpDeskManagerLandingobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                string status = HelpDeskManagerLandingobj.Status?.ToString();
                bool BusinessAreaAll = Convert.ToBoolean(HelpDeskManagerLandingobj.BusinessAreaAll);
                bool BusinessAreaDomestic = Convert.ToBoolean(HelpDeskManagerLandingobj.BusinessAreaDomestic);

                string CompanyIds = string.Empty;
                if (Uri.UnescapeDataString(HelpDeskManagerLandingobj.Company) != null && Uri.UnescapeDataString(HelpDeskManagerLandingobj.Company) != "")
                {
                    MD_Company companies = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(HelpDeskManagerLandingobj.Company))).ToObject<MD_Company>();
                    CompanyIds = string.Join(", ", companies.Companies.Select(c => c.ID.ToString()));
                }

                string BranchIds = string.Empty;
                if (Uri.UnescapeDataString(HelpDeskManagerLandingobj.Branch) != null && Uri.UnescapeDataString(HelpDeskManagerLandingobj.Branch) != "")
                {
                    MD_Branch branches = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(HelpDeskManagerLandingobj.Branch))).ToObject<MD_Branch>();
                    BranchIds = string.Join(", ", branches.Branchs.Select(b => b.ID.ToString()));
                }

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Get status IDs from stored procedure
                    var command = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusIDs", connection)
                    {
                        CommandType = CommandType.StoredProcedure
                    };

                    var reader = command.ExecuteReader();
                    reader.Read();
                    int createdStatusID = reader.GetInt32(0);
                    int inProgressStatusID = reader.GetInt32(1);
                    int onHoldStatusID = reader.GetInt32(2);
                    int escalatedStatusID = reader.GetInt32(3);
                    reader.Close();

                    // Fetch service request ageing data
                    command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestAgeingDataWithNotes", connection)
                    {
                        CommandType = CommandType.StoredProcedure
                    };
                    command.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                    command.Parameters.AddWithValue("@BranchIds", BranchIds);
                    command.Parameters.AddWithValue("@Status", status);

                    var serviceRequestAgeingData = new List<ServiceRequestAgeing>();

                    reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        var data = new ServiceRequestAgeing
                        {
                            QueryEscalatedTo = reader["QUERYESCALATEDTO"] == DBNull.Value ? null : reader["QUERYESCALATEDTO"].ToString(),
                            ServiceRequest_ID = reader["SERVICEREQUEST_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["SERVICEREQUEST_ID"]),
                            Brand_Name = reader["BRAND_NAME"] == DBNull.Value ? string.Empty : reader["BRAND_NAME"].ToString(),
                            ProductType_Name = reader["PRODUCTTYPE_NAME"] == DBNull.Value ? string.Empty : reader["PRODUCTTYPE_NAME"].ToString(),
                            Model_Name = reader["MODEL_NAME"] == DBNull.Value ? string.Empty : reader["MODEL_NAME"].ToString(),
                            Party_Name = reader["PARTY_NAME"] == DBNull.Value ? string.Empty : reader["PARTY_NAME"].ToString(),
                            CallDateOrder = reader["CALLDATEORDER"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["CALLDATEORDER"]),
                            SerialNumber = reader["SERIALNUMBER"] == DBNull.Value ? string.Empty : reader["SERIALNUMBER"].ToString(),
                            status = reader["STATUS"] == DBNull.Value ? string.Empty : reader["STATUS"].ToString(),
                            ActionRemarks = reader["ACTIONREMARKS"] == DBNull.Value ? string.Empty : reader["ACTIONREMARKS"].ToString(),
                            NotesDescription = reader["NOTESDESCRIPTION"] == DBNull.Value ? string.Empty : reader["NOTESDESCRIPTION"].ToString(),
                            Name = reader["NAME"] == DBNull.Value ? string.Empty : reader["NAME"].ToString(),
                            Department = reader["DEPARTMENT"] == DBNull.Value ? string.Empty : reader["DEPARTMENT"].ToString(),
                            CompanyName = reader["COMPANYNAME"] == DBNull.Value ? string.Empty : reader["COMPANYNAME"].ToString()
                        };
                        serviceRequestAgeingData.Add(data);
                    }
                    reader.Close();

                    var serviceRequestQuery = serviceRequestAgeingData.AsQueryable();

                    // Apply filters if required
                    if (filter.ToString() != "null")
                    {
                        Filters filters = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                        if (filters.rules.Any())
                        {
                            serviceRequestQuery = serviceRequestQuery.FilterSearch<ServiceRequestAgeing>(filters);
                        }
                    }

                    // Prepare the DataTable to export
                    DataTable dt = CreateDataTable(HelpDeskManagerLandingobj);

                    if (serviceRequestQuery.Any())
                    {
                        foreach (var item in serviceRequestQuery)
                        {
                            dt.Rows.Add(
                                item.CompanyName,
                                item.QueryEscalatedTo,
                                item.ServiceRequest_ID,
                                item.CallDateOrder,
                                item.Party_Name,
                                item.Brand_Name,
                                item.ProductType_Name,
                                item.Model_Name,
                                item.SerialNumber,
                                item.status,
                                item.AgeingHours,
                                item.AgeingDays,
                                item.ActionRemarks,
                                item.NotesDescription,
                                item.Name,
                                item.Department
                            );
                        }
                        DataTable dtAlignment = dt.Clone(); // Alignment table with the same structure
                        dtAlignment.Rows.Add(Enumerable.Repeat(0, dt.Columns.Count).ToArray()); // Add a dummy row for alignment
                        ReportExportList reportExportList = new ReportExportList
                        {
                            Company_ID = HelpDeskManagerLandingobj.Company_ID, // Assuming this is available in ExportObj
                            Branch = HelpDeskManagerLandingobj.Branch.ToString(),
                            GeneralLanguageID = HelpDeskManagerLandingobj.GeneralLanguageID,
                            UserLanguageID = HelpDeskManagerLandingobj.LanguageID,
                            Options = new DataTable(),
                            dt = dt,
                            Alignment = dtAlignment,
                            FileName = "CallDetails", // Set a default or dynamic filename
                            Header = CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "CallDetails").ToString(), // Set a default or dynamic header
                            exprtType = HelpDeskManagerLandingobj.exportType, // Assuming export type as 1 for Excel, adjust as needed
                            UserCulture = HelpDeskManagerLandingobj.UserCulture
                        };

                        var result = await ReportExport.Export(reportExportList, connString, LogException);
                        return result.Value;
                        // Export the data to the required format
                        // ReportExport.Export(HelpDeskManagerLandingobj.exportType, dt, new DataTable(), dtAlignment, "CallDetails", "CallDetails");
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return false;
        }

        private static DataTable CreateDataTable(HelpDeskManagerLandingList HelpDeskManagerLandingobj)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "Company").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "CallOwner").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "CaseNumber").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "Date").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "PartyName").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "Brand").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "ProductType").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "model").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "serialnumber").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "status").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "AgeingHours").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "AgeingDays").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "ActionRemarks").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "NotesDescription").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "Name").ToString());
            dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskManagerLandingobj.UserCulture.ToString(), "Department").ToString());
            return dt;
        }
        #endregion


        #region ::: SelWorkFlowSummary Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// SelWorkFlowSummary
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelWorkFlowSummary(SelWorkFlowSummaryList SelWorkFlowSummaryobj, string connString, int LogException)
        {

            var x = default(dynamic);
            try
            {
                int userID = SelWorkFlowSummaryobj.User_ID;
                string CompanyIds = string.Empty;

                if (Uri.UnescapeDataString(SelWorkFlowSummaryobj.Company) != null && Uri.UnescapeDataString(SelWorkFlowSummaryobj.Company) != "")
                {
                    MD_Company companies = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(SelWorkFlowSummaryobj.Company))).ToObject<MD_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;

                if (Uri.UnescapeDataString(SelWorkFlowSummaryobj.Branch) != null && Uri.UnescapeDataString(SelWorkFlowSummaryobj.Branch) != "")
                {
                    MD_Branch branchs = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(SelWorkFlowSummaryobj.Branch))).ToObject<MD_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                }

                var strCompanyIds = CompanyIds.Split(',');
                var strBranchIds = BranchIds.Split(',');
                List<WorkFlowSummary> summary = GetWorkFlowSummary(connString, CompanyIds, 1, SelWorkFlowSummaryobj.User_ID, BranchIds, true);

                x = from a in summary
                    select new
                    {
                        ID = a.StatusID,
                        Name = a.StatusName.ToString(),
                        Count = a.Count,
                        MaxValue = a.MaxValue,
                        Mode = a.Mode,
                    };

                SelWorkFlowSummaryobj.MaxStatusValue = (summary.Count == 0) ? 0 : summary.ElementAt(0).MaxValue;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }

        public static List<WorkFlowSummary> GetWorkFlowSummary(string connString, string companyID, int workFlowID, int UserID, string branchID, bool fromOthers)
        {
            List<WorkFlowSummary> WFSummary = new List<WorkFlowSummary>();
            string IsFromServiceQuotation = string.Empty;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();

                // Fetch MyQueueList based on IsFromServiceQuotation
                var myQueueList = GetMyQueueList(conn, workFlowID, UserID, IsFromServiceQuotation);

                // Fetch various entities
                var serviceRequests = GetFSMCoreHDServiceRequest(conn, companyID, branchID);
                var jobCards = GetSRTJobCardHeader(conn, companyID, branchID);
                var purchaseOrders = GetPurchaseOrderList(conn, companyID, branchID);
                var purchaseOrderStatuses = GetPurchaseOrderStatus(conn);
                var partsOrders = GetPRTCorePartsOrder(conn, companyID, branchID);

                // Process steps and statuses
                int endStepTypeId = GetEndStepTypeID(conn);
                var closeStepIds = GetCloseStepIDs(conn, workFlowID, endStepTypeId);

                // Loop through steps and statuses
                foreach (var status in GetStepStatuses(conn))
                {
                    WorkFlowSummary summary = new WorkFlowSummary
                    {
                        StatusID = status.WFStepStatus_ID,
                        StatusName = status.WFStepStatus_Nm,
                        MaxValue = GetMaxStepStatusID(conn),
                        Mode = 4
                    };

                    foreach (var step in GetStepsByStatus(conn, status.WFStepStatus_ID, workFlowID))
                    {
                        var caseProgress = GetCaseProgress(conn, workFlowID, step.WFSteps_ID, closeStepIds);

                        int count = 0;
                        if (workFlowID == 1)
                        {
                            count = GetServiceRequestCount(conn, caseProgress, serviceRequests);
                        }
                        else if (workFlowID == 3)
                        {
                            count = GetJobCardCount(conn, caseProgress, jobCards);
                        }
                        else if (workFlowID == 4)
                        {
                            count = GetPurchaseOrderCount(conn, caseProgress, purchaseOrders, purchaseOrderStatuses);
                        }
                        else if (workFlowID == 5)
                        {
                            count = GetPartsOrderCount(conn, caseProgress, partsOrders);
                        }

                        summary.Count += count;
                    }

                    if (summary.Count > 0)
                    {
                        WFSummary.Add(summary);
                    }
                }

                // Process additional queues if required
                if (fromOthers)
                {
                    var myQueueSummary = GetSRMyQueue(conn, companyID, workFlowID, UserID);
                    if (myQueueList.Count() > 0)
                    {
                        WorkFlowSummary SummaryMyQueue = new WorkFlowSummary();
                        List<WF_WFStepStatus> stepStatuses = GetStepStatuses(conn);
                        SummaryMyQueue.StatusID = stepStatuses.Max(s => s.WFStepStatus_ID) + 1;
                        SummaryMyQueue.StatusName = "MyQueue";
                        SummaryMyQueue.Count = myQueueList.Count();
                        SummaryMyQueue.Mode = 1;
                        SummaryMyQueue.MaxValue = GetMaxStepStatusID(conn);
                        WFSummary.Add(SummaryMyQueue);
                    }
                }

                List<OEMIndicator> RowIndGroup = GetGroupQueue(conn, companyID, workFlowID, UserID, branchID);
                if (RowIndGroup.Count > 0)
                {
                    WorkFlowSummary SummaryGroupQueue = new WorkFlowSummary();
                    List<WF_WFStepStatus> stepStatuses = GetStepStatuses(conn);
                    SummaryGroupQueue.StatusID = stepStatuses.Max(a => a.WFStepStatus_ID) + 2;
                    SummaryGroupQueue.StatusName = "GroupQueue";
                    SummaryGroupQueue.Count = RowIndGroup.Count;
                    SummaryGroupQueue.Mode = 2;
                    SummaryGroupQueue.MaxValue = GetMaxStepStatusID(conn);
                    WFSummary.Add(SummaryGroupQueue);
                }

                List<OEMIndicator> RowIndAll = GetAllQueue(conn, companyID, workFlowID, UserID, 0, branchID);
                if (RowIndAll.Count > 0)
                {
                    WorkFlowSummary SummaryAllQueue = new WorkFlowSummary();
                    List<WF_WFStepStatus> stepStatuses = GetStepStatuses(conn);
                    SummaryAllQueue.StatusID = stepStatuses.Max(a => a.WFStepStatus_ID) + 3;
                    SummaryAllQueue.StatusName = "AllQueue";
                    SummaryAllQueue.Count = RowIndAll.Count;
                    SummaryAllQueue.Mode = 3;
                    SummaryAllQueue.MaxValue = GetMaxStepStatusID(conn);
                    WFSummary.Add(SummaryAllQueue);
                }
            }

            return WFSummary;
        }

        public static List<OEMIndicator> GetAllQueue(SqlConnection conn, string companyID, int workFlowID, int userID, int StatusID, string branchID)
        {
            int ID = 0;
            List<OEMIndicator> RowInd = new List<OEMIndicator>();
            WF_WFStepStatus StepStutus = null;
            object[] parameters = { companyID, branchID };

            // ADO.NET query execution for fetching data
            List<WF_WFStepLink> Stlink = ExecuteStoredProc<WF_WFStepLink>(conn, "SP_AMERP_HelpDesk_GetWFStepLink", new SqlParameter("@companyID", companyID), new SqlParameter("@branchID", branchID)).ToList();
            List<HD_ServiceRequest> SRequset = ExecuteStoredProc<HD_ServiceRequest>(conn, "SP_AMERP_HelpDesk_GetHDServiceRequest", new SqlParameter("@companyID", companyID), new SqlParameter("@branchID", branchID)).ToList();
            List<SRT_JobCardHeader> JCard = ExecuteStoredProc<SRT_JobCardHeader>(conn, "SP_HelpDesk_GetSRTJobCardHeader", new SqlParameter("@companyID", companyID), new SqlParameter("@branchID", branchID)).ToList();
            List<PRT_CorePurchaseOrder> PurchaseOrderList = ExecuteStoredProc<PRT_CorePurchaseOrder>(conn, "SP_AMERP_HelpDesk_GetPRTPurchaseOrder", new SqlParameter("@companyID", companyID), new SqlParameter("@branchID", branchID)).ToList();
            List<PRT_CorePartsOrder> PartsOrderList = ExecuteStoredProc<PRT_CorePartsOrder>(conn, "SP_AMERP_HelpDesk_GetPRTPartsOrder", new SqlParameter("@companyID", companyID), new SqlParameter("@branchID", branchID)).ToList();

            dynamic dummy = null;
            var list = dummy;
            int EndStepTypeID = 0;
            try
            {
                EndStepTypeID = ExecuteStoredProc<int>(conn, "SP_AMERP_HelpDesk_GetEndStepTypeIDGetAllQueue", new SqlParameter("@workflowID", workFlowID)).FirstOrDefault();

                // Fetch the role ID for the user
                int roleID = ExecuteStoredProc<int>(conn, "SP_AMERP_HelpDesk_GetRoleIDForUser", new SqlParameter("@userID", userID), new SqlParameter("@workflowID", workFlowID)).FirstOrDefault();

                // Transaction List based on workflow status
                List<WF_WFCase_Progress> transactionList = GetWFTransactionList(conn, workFlowID, StatusID, roleID, EndStepTypeID);

                // Process transactions based on Workflow ID
                if (workFlowID == 1) // Service Request
                {
                    list = transactionList.Select(a => new
                    {
                        a.Transaction_ID,
                        indicator = GetIndicator(a, roleID, userID, EndStepTypeID, SRequset.Cast<int>(), workFlowID)
                    });
                }
                else if (workFlowID == 3) // Job Card
                {
                    list = transactionList.Select(a => new
                    {
                        a.Transaction_ID,
                        indicator = GetIndicator(a, roleID, userID, EndStepTypeID, JCard.Cast<int>(), workFlowID)
                    });
                }
                else if (workFlowID == 4) // Purchase Order
                {
                    list = transactionList.Select(a => new
                    {
                        a.Transaction_ID,
                        indicator = GetIndicator(a, roleID, userID, EndStepTypeID, PurchaseOrderList.Cast<int>(), workFlowID)
                    });
                }
                else if (workFlowID == 5) // Parts Order
                {
                    list = transactionList.Select(a => new
                    {
                        a.Transaction_ID,
                        indicator = GetIndicator(a, roleID, userID, EndStepTypeID, PartsOrderList.Cast<int>(), workFlowID)
                    });
                }


                // Add data to RowInd
                foreach (var a in list)
                {
                    OEMIndicator RInd = new OEMIndicator();
                    RInd.TransactionID = a.Transaction_ID;
                    RInd.IndicatorType = a.indicator;
                    RInd.IsLock = false;
                    RInd.IsStatusNull = StepStutus == null ? true : false;
                    RowInd.Add(RInd);
                }
            }
            catch (Exception ex)
            {
                // Log the exception (ex)
            }

            return RowInd;
        }

        // Helper method to execute stored procedure and return a list of results
        public static List<T> ExecuteStoredProc<T>(SqlConnection conn, string storedProcName, params SqlParameter[] parameters)
        {
            List<T> result = new List<T>();
            using (SqlCommand cmd = new SqlCommand(storedProcName, conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddRange(parameters);
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    var entityList = new List<T>();
                    while (reader.Read())
                    {
                        var entity = Activator.CreateInstance<T>();
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var propertyInfo = entity.GetType().GetProperty(reader.GetName(i));
                            if (propertyInfo != null && reader[i] != DBNull.Value)
                            {
                                propertyInfo.SetValue(entity, reader[i]);
                            }
                        }
                        entityList.Add(entity);
                    }
                    result = entityList;
                }
            }
            return result;
        }

        // Helper method to fetch WF transactions
        public static List<WF_WFCase_Progress> GetWFTransactionList(SqlConnection conn, int workFlowID, int StatusID, int roleID, int EndStepTypeID)
        {
            string query = "SP_AMERP_HelpDesk_GetWFCaseProgressGetAllQueue";
            SqlParameter[] parameters = {
        new SqlParameter("@workFlowID", workFlowID),
        new SqlParameter("@StatusID", StatusID),
        new SqlParameter("@roleID", roleID),
        new SqlParameter("@EndStepTypeID", EndStepTypeID)
    };

            return ExecuteStoredProc<WF_WFCase_Progress>(conn, query, parameters);
        }

        // Helper method to get indicator based on the current status of the transaction
        public static int GetIndicator(WF_WFCase_Progress a, int roleID, int userID, int EndStepTypeID, IEnumerable<int> entityList, int workFlowID)
        {
            if (a == null)
            {
                return 0; // Default status when 'a' is null
            }

            // Try to convert Transaction_ID to int (if it's not already)
            int? transactionId = null;
            if (a.Transaction_ID != null && int.TryParse(a.Transaction_ID.ToString(), out int tempTransactionId))
            {
                transactionId = tempTransactionId;
            }

            // Try to convert Addresse_ID to int (if it's not already)
            int? addresseId = null;
            if (a.Addresse_ID != null && int.TryParse(a.Addresse_ID.ToString(), out int tempAddresseId))
            {
                addresseId = tempAddresseId;
            }

            // Validate input and apply logic
            if (transactionId.HasValue && entityList?.Contains(transactionId.Value) == true)
            {
                return 3;
            }
            else if (addresseId.HasValue && addresseId.Value == userID && a.Addresse_Flag == 1)
            {
                return 1; // Status for user-specific logic
            }
            else if (addresseId.HasValue && addresseId.Value == roleID && a.Addresse_Flag == 0)
            {
                return 2; // Status for role-specific logic
            }
            else
            {
                return 4; // Default status
            }
        }

        public static List<OEMIndicator> GetGroupQueue(SqlConnection conn, string companyID, int workFlowID, int userID, string branchID)
        {
            List<OEMIndicator> rowIndicators = new List<OEMIndicator>();

            try
            {
                List<int> roleID = new List<int>();
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFRoleIDs", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserID", userID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            roleID.Add(reader.GetInt32(0));
                        }
                    }
                }

                // Prepare in-condition string for role IDs
                string inCondition = string.Join(",", roleID);

                List<WF_WFCase_Progress> transactionList = new List<WF_WFCase_Progress>();
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFCaseProgress", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                    cmd.Parameters.AddWithValue("@InCondition", inCondition);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            WF_WFCase_Progress progress = new WF_WFCase_Progress
                            {
                                Transaction_ID = reader["Transaction_ID"] != DBNull.Value
                                                ? (int)reader["Transaction_ID"]
                                                : 0,
                            };
                            transactionList.Add(progress);
                        }
                    }
                }

                IEnumerable<HD_ServiceRequest> sRequest = ExecuteStoredProcedure<HD_ServiceRequest>("SP_AMERP_HelpDesK_GetServiceRequestsGetGroupQueue", conn, companyID, branchID);
                IEnumerable<SRT_JobCardHeader> jCard = ExecuteStoredProcedure<SRT_JobCardHeader>("SP_AMERP_HelpDesk_GetJobCards", conn, companyID, branchID);
                IEnumerable<WF_WFStepLink> stLink = ExecuteStoredProcedure<WF_WFStepLink>("SP_AMERP_HelpDesk_GetWFStepLink", conn, companyID, branchID);
                List<PRT_CorePurchaseOrder> purchaseOrderList = ExecuteStoredProcedure<PRT_CorePurchaseOrder>("SP_AMERP_HelpDesk_GetPurchaseOrders", conn, companyID, branchID);
                List<PRT_CorePartsOrder> partsOrderList = ExecuteStoredProcedure<PRT_CorePartsOrder>("SP_AMERP_HelpDesk_GetPartsOrders", conn, companyID, branchID);

                IEnumerable<dynamic> list = Enumerable.Empty<dynamic>();

                if (workFlowID == 1) // Service Request
                    list = transactionList.Join(sRequest, a => a.Transaction_ID, sr => sr.ServiceRequest_ID, (a, sr) => new { a.Transaction_ID });

                if (workFlowID == 3) // Job Card
                    list = transactionList.Join(jCard, a => a.Transaction_ID, jc => jc.JobCard_ID, (a, jc) => new { a.Transaction_ID });

                if (workFlowID == 4) // Purchase Order
                    list = transactionList.Join(purchaseOrderList, a => a.Transaction_ID, po => po.PurchaseOrder_ID, (a, po) => new { a.Transaction_ID });

                if (workFlowID == 5) // Parts Order
                    list = transactionList.Join(partsOrderList, a => a.Transaction_ID, po => po.PartsOrder_ID, (a, po) => new { a.Transaction_ID });

                foreach (var a in list)
                {
                    OEMIndicator indicator = new OEMIndicator
                    {
                        TransactionID = a.Transaction_ID,
                        IndicatorType = 0,
                        IsLock = false,
                        IsStatusNull = false
                    };
                    rowIndicators.Add(indicator);
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return rowIndicators;
        }

        // Helper method for stored procedure execution
        private static List<T> ExecuteStoredProcedure<T>(string spName, SqlConnection conn, string companyID, string branchID)
        {
            List<T> results = new List<T>();
            using (SqlCommand cmd = new SqlCommand(spName, conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        T obj = Activator.CreateInstance<T>();
                        foreach (var property in typeof(T).GetProperties())
                        {
                            if (!reader.HasColumn(property.Name)) continue; // Ensure column exists
                            var value = reader[property.Name];
                            if (value != DBNull.Value)
                            {
                                property.SetValue(obj, Convert.ChangeType(value, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType));
                            }
                            else if (Nullable.GetUnderlyingType(property.PropertyType) != null)
                            {
                                property.SetValue(obj, null); // Set to null for nullable types
                            }
                        }
                        results.Add(obj);
                    }
                }
            }
            return results;
        }


        public static List<OEMIndicator> GetSRMyQueue(SqlConnection conn, string companyID, int workFlowID, int userID)
        {
            List<OEMIndicator> rowIndicators = new List<OEMIndicator>();

            try
            {
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetSRMyQueue", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                    cmd.Parameters.AddWithValue("@UserID", userID);

                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            OEMIndicator indicator = new OEMIndicator
                            {
                                TransactionID = reader["Transaction_ID"] != DBNull.Value
                                                ? (int)reader["Transaction_ID"]
                                                : 0,
                                IndicatorType = 0, // Assuming default value as per original code
                                IsLock = reader["Locked_Ind"] != DBNull.Value
                                         ? Convert.ToBoolean(reader["Locked_Ind"])
                                         : false,
                                IsStatusNull = false
                            };

                            rowIndicators.Add(indicator);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return rowIndicators;
        }

        private static List<WF_WFCase_Progress> GetMyQueueList(SqlConnection conn, int workFlowID, int userID, string isFromServiceQuotation)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetMyQueueList", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                cmd.Parameters.AddWithValue("@UserID", userID);
                cmd.Parameters.AddWithValue("@IsFromServiceQuotation", isFromServiceQuotation);
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<WF_WFCase_Progress> myQueueList = new List<WF_WFCase_Progress>();
                    while (reader.Read())
                    {
                        // Map fields to WF_WFCase_Progress object
                        myQueueList.Add(new WF_WFCase_Progress
                        {
                            // Map fields here
                        });
                    }
                    return myQueueList;
                }
            }
        }

        private static List<HD_ServiceRequest> GetFSMCoreHDServiceRequest(SqlConnection conn, string companyID, string branchID)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetFSMCoreHDServiceRequest", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<HD_ServiceRequest> serviceRequests = new List<HD_ServiceRequest>();
                    while (reader.Read())
                    {
                        serviceRequests.Add(new HD_ServiceRequest
                        {
                            ServiceRequest_ID = reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID")),
                            // Map other fields as needed
                        });
                    }
                    return serviceRequests;
                }
            }
        }

        private static List<SRT_JobCardHeader> GetSRTJobCardHeader(SqlConnection conn, string companyID, string branchID)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetSRTJobCardHeader", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<SRT_JobCardHeader> jobCards = new List<SRT_JobCardHeader>();
                    while (reader.Read())
                    {
                        jobCards.Add(new SRT_JobCardHeader
                        {
                            JobCard_ID = reader.GetInt32(reader.GetOrdinal("JobCard_ID")),
                            // Map other fields as needed
                        });
                    }
                    return jobCards;
                }
            }
        }


        private static List<intClass> GetPurchaseOrderStatus(SqlConnection conn)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPurchaseOrderStatus", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<intClass> statuses = new List<intClass>();
                    while (reader.Read())
                    {
                        statuses.Add(new intClass
                        {
                            ID = reader.GetInt32(reader.GetOrdinal("ID"))
                        });
                    }
                    return statuses;
                }
            }
        }

        private static List<int> GetCloseStepIDs(SqlConnection conn, int workFlowID, int endStepTypeID)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCloseStepIDs", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                cmd.Parameters.AddWithValue("@EndStepTypeID", endStepTypeID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<int> closeStepIds = new List<int>();
                    while (reader.Read())
                    {
                        closeStepIds.Add(reader.GetInt32(reader.GetOrdinal("WFSteps_ID")));
                    }
                    return closeStepIds;
                }
            }
        }

        private static List<WF_WFCase_Progress> GetCaseProgress(SqlConnection conn, int workFlowID, int wfStepsID, List<int> closeStepIDs)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCaseProgress", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                cmd.Parameters.AddWithValue("@WFStepsID", wfStepsID);
                cmd.Parameters.AddWithValue("@CloseStepIDs", string.Join(",", closeStepIDs));

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<WF_WFCase_Progress> caseProgressList = new List<WF_WFCase_Progress>();
                    while (reader.Read())
                    {
                        caseProgressList.Add(new WF_WFCase_Progress
                        {
                            Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                            // Map other fields as needed
                        });
                    }
                    return caseProgressList;
                }
            }
        }


        private static List<PRT_CorePurchaseOrder> GetPurchaseOrderList(SqlConnection conn, string companyID, string branchID)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPurchaseOrderList", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<PRT_CorePurchaseOrder> purchaseOrders = new List<PRT_CorePurchaseOrder>();
                    while (reader.Read())
                    {
                        purchaseOrders.Add(new PRT_CorePurchaseOrder
                        {
                            PurchaseOrder_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                            // Map other fields as needed
                        });
                    }
                    return purchaseOrders;
                }
            }
        }


        private static List<PRT_CorePartsOrder> GetPRTCorePartsOrder(SqlConnection conn, string companyID, string branchID)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPRTCorePartsOrder", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<PRT_CorePartsOrder> partsOrders = new List<PRT_CorePartsOrder>();
                    while (reader.Read())
                    {
                        partsOrders.Add(new PRT_CorePartsOrder
                        {
                            PartsOrder_ID = reader.GetInt32(reader.GetOrdinal("PartsOrder_ID")),
                            // Map other fields as needed
                        });
                    }
                    return partsOrders;
                }
            }
        }
        private static int GetEndStepTypeID(SqlConnection conn)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetEndStepTypeID", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                return (int)cmd.ExecuteScalar();
            }
        }


        private static List<WF_WFStepStatus> GetStepStatuses(SqlConnection conn)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetStepStatuses", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<WF_WFStepStatus> stepStatuses = new List<WF_WFStepStatus>();
                    while (reader.Read())
                    {
                        stepStatuses.Add(new WF_WFStepStatus
                        {
                            WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                            WFStepStatus_Nm = reader.GetString(reader.GetOrdinal("WFStepStatus_Nm"))
                            // Map other fields as needed
                        });
                    }
                    return stepStatuses;
                }
            }
        }

        private static int GetMaxStepStatusID(SqlConnection conn)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetMaxStepStatusID", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                return (int)cmd.ExecuteScalar();
            }
        }


        private static List<WF_WFSteps> GetStepsByStatus(SqlConnection conn, int workFlowID, int stepStatusID)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetStepsByStatus", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                cmd.Parameters.AddWithValue("@StepStatusID", stepStatusID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    List<WF_WFSteps> steps = new List<WF_WFSteps>();
                    while (reader.Read())
                    {
                        steps.Add(new WF_WFSteps
                        {
                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                            // Map other fields as needed
                        });
                    }
                    return steps;
                }
            }
        }


        private static int GetServiceRequestCount(SqlConnection conn, List<WF_WFCase_Progress> caseProgress, List<HD_ServiceRequest> serviceRequests)
        {
            int count = 0;
            foreach (var progress in caseProgress)
            {
                count += serviceRequests.Count(sr => sr.ServiceRequest_ID == progress.Transaction_ID);
            }
            return count;
        }

        private static int GetJobCardCount(SqlConnection conn, List<WF_WFCase_Progress> caseProgress, List<SRT_JobCardHeader> jobCards)
        {
            int count = 0;
            foreach (var progress in caseProgress)
            {
                count += jobCards.Count(jc => jc.JobCard_ID == progress.Transaction_ID);
            }
            return count;
        }

        private static int GetPurchaseOrderCount(SqlConnection conn, List<WF_WFCase_Progress> caseProgress, List<PRT_CorePurchaseOrder> purchaseOrders, List<intClass> purchaseOrderStatus)
        {
            int count = 0;
            foreach (var progress in caseProgress)
            {
                count += purchaseOrders.Count(po => po.PurchaseOrder_ID == progress.Transaction_ID && purchaseOrderStatus.Any(s => s.ID == po.POStatus_ID));
            }
            return count;
        }


        private static int GetPartsOrderCount(SqlConnection conn, List<WF_WFCase_Progress> caseProgress, List<PRT_CorePartsOrder> partsOrders)
        {
            int count = 0;
            foreach (var progress in caseProgress)
            {
                count += partsOrders.Count(po => po.PartsOrder_ID == progress.Transaction_ID);
            }
            return count;
        }



        #endregion


        #region ::: getCompanys Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// getCompanys
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult getCompanys(getCompanysList getCompanysobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            MD_Company companys = new MD_Company();
            int count = 0, total = 0;

            try
            {
                if (Uri.UnescapeDataString(getCompanysobj.CompanyOBJ) != null && Uri.UnescapeDataString(getCompanysobj.CompanyOBJ) != "")
                {
                    companys = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(getCompanysobj.CompanyOBJ))).ToObject<MD_Company>();
                }

                count = companys.Companies.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = (from c in companys.Companies select new { c.ID, c.Name }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            { }
            return new JsonResult(jsonobj);
        }
        #endregion


        #region ::: HelpDeskManagerLanding List and Obj Classes Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// getCompanys
        /// </summary>
        /// <returns>...</returns>
        ///
        public class SelWorkFlowSummaryList
        {
            public int User_ID { get; set; }
            public string Company { get; set; }
            public string Branch { get; set; }
            public int MaxStatusValue { get; set; }
        }
        public class HelpDeskManagerLandingList
        {
            public string Status { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string Branch { get; set; }
            public string Company { get; set; }
            public string UserCulture { get; set; }
            public int exportType { get; set; }
            public int Company_ID { get; set; }
            public int BranchID { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
        }

        public class getCompanysList
        {

            public string CompanyOBJ { get; set; }
        }
        public class SelectHyperLinkStatusByCallOwnerList
        {
            public int Company_ID { get; set; }
            public string CallOwner { get; set; }
            public string Status { get; set; }
            public string BranchObj { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
        }

        public class SelectHyperLinkStatusList
        {
            public int Company_ID { get; set; }
            public string Status { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string BranchObj { get; set; }
        }
        public class SelectGraphPendingCasesDataList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string Company { get; set; }
            public int Employee_ID { get; set; }
            public string Branch { get; set; }
        }

        public class getCallSummaryList
        {
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string Branch { get; set; }
            public string Company { get; set; }
        }


        public class NumberOfCasesBasedOnIssueAreaList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string Company { get; set; }
            public int Employee_ID { get; set; }
            public int Company_ID { get; set; }
            public string Branch { get; set; }
        }

        public class ResolutionTimeOfClosedCasesdList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string Company { get; set; }
            public int Employee_ID { get; set; }
            public int Company_ID { get; set; }
            public string Branch { get; set; }
        }



        public class ResolutionTimeOfClosedCasesList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string BusinessAreaAll { get; set; }
            public string BusinessAreaDomestic { get; set; }
            public string Company { get; set; }
            public int Company_ID { get; set; }
            public string Branch { get; set; }
        }
        public class HelpDeskManagerLandingPageGetInitialDataList
        {
            public int Company_ID { get; set; }
            public string NeedToChangepassword { get; set; }
        }
        #endregion


        #region ::: HelpDeskManagerLanding  Classes Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// getCompanys
        /// </summary>
        /// <returns>...</returns>
        ///
        public class AgeingAnalysis
        {
            public string Ageing { get; set; }
            public int Count { get; set; }
        }

        public class OEMIndicator
        {
            public int IndicatorType { get; set; }
            public bool IsLock { get; set; }
            public int TransactionID { get; set; }
            public bool IsStatusNull { get; set; }
        }
        public partial class PRT_CorePartsOrder
        {
            public int PartsOrder_ID { get; set; }
            public string PartsOrderNumber { get; set; }
            public System.DateTime PartsOrderDate { get; set; }
            public int PartsOrderType_ID { get; set; }
            public Nullable<int> ServiceRequest_ID { get; set; }
            public Nullable<int> PartsQuotation_ID { get; set; }
            public int CustomerOrderClass_ID { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public Nullable<int> OrderBranch_ID { get; set; }
            public string PartyReferenceDetail { get; set; }
            public Nullable<int> ConsigneeAddress_ID { get; set; }
            public Nullable<int> InvoiceAddress_ID { get; set; }
            public string PaymentTerms { get; set; }
            public Nullable<bool> EnableVersion { get; set; }
            public Nullable<bool> IsArchived { get; set; }
            public Nullable<decimal> TotalAmount { get; set; }
            public int PartsOrderStatus_ID { get; set; }
            public Nullable<int> PurchaseOrder_ID { get; set; }
            public Nullable<bool> IsBackToBackOrderAllocation { get; set; }
            public Nullable<decimal> DiscountPercentage { get; set; }
            public Nullable<decimal> DiscountAmount { get; set; }
            public Nullable<decimal> DiscountedAmount { get; set; }
            public Nullable<int> TaxStructure_ID { get; set; }
            public Nullable<decimal> TotalTaxableAmount { get; set; }
            public Nullable<decimal> TaxAmount { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public Nullable<int> FinancialYear { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> DocumentNumber { get; set; }
            public Nullable<int> PartsOrderVersion { get; set; }
            public Nullable<int> JobCard_ID { get; set; }
            public string JobCardNumber { get; set; }
            public Nullable<int> StockTransferRequest_ID { get; set; }
            public string StockTransferRequestNumber { get; set; }
            public Nullable<int> OrderBranchConsignee_ID { get; set; }
            public Nullable<int> WareHouse_ID { get; set; }
            public string Remarks { get; set; }
            public Nullable<bool> IsDealer { get; set; }

            public virtual PRT_CorePartsOrder PRT_PartsOrder1 { get; set; }
            public virtual PRT_CorePartsOrder PRT_PartsOrder2 { get; set; }
            public virtual PRT_CorePurchaseOrder PRT_PurchaseOrder { get; set; }
        }
        public partial class PRT_CorePurchaseOrder
        {
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public int PurchaseOrder_ID { get; set; }
            public string PurchaseOrderNumber { get; set; }
            public byte PurchaseOrderVersion { get; set; }
            public System.DateTime PurchaseOrderDate { get; set; }
            public int TypeofPurchase_ID { get; set; }
            public int Supplier_ID { get; set; }
            public Nullable<int> PurchaseOrderClass_ID { get; set; }
            public int InvoiceAddress_ID { get; set; }
            public int ConsigneeAddress_ID { get; set; }
            public string PaymentTerms { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public string SerialNumber { get; set; }
            public decimal TotalOrderAmount { get; set; }
            public string Remarks { get; set; }
            public bool EnableVersion { get; set; }
            public bool IsArchived { get; set; }
            public int FinancialYear { get; set; }
            public int Updated_by { get; set; }
            public System.DateTime Updated_Date { get; set; }
            public Nullable<System.DateTime> Expected_Delivery_Date { get; set; }
            public Nullable<int> DocumentNumber { get; set; }
            public Nullable<decimal> DiscountPercentage { get; set; }
            public Nullable<decimal> DiscountAmount { get; set; }
            public Nullable<decimal> DiscountedAmount { get; set; }
            public Nullable<int> TaxStructure_ID { get; set; }
            public Nullable<decimal> TotalTaxableAmount { get; set; }
            public Nullable<decimal> TaxAmount { get; set; }
            public int PurchaseOrderStatus_ID { get; set; }
            public Nullable<int> WareHouse_ID { get; set; }
            public int POStatus_ID { get; set; }
            public Nullable<int> ModeOfShipment_ID { get; set; }
            public Nullable<int> SUPPLIERQUOTATION_ID { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public Nullable<bool> IsUnderBreakDown { get; set; }
            public Nullable<bool> IsDealer { get; set; }
        }

        public class WorkFlowSummary
        {
            public int StatusID { get; set; }
            public string StatusName { get; set; }
            public int Count { get; set; }
            public int MaxValue { get; set; }
            public int Mode { get; set; }
        }
        public partial class SRT_JobCardHeader
        {

            public int JobCard_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public string JobCardNumber { get; set; }
            public Nullable<System.DateTime> JobCardDate { get; set; }
            public Nullable<int> JobCardStatus_ID { get; set; }
            public Nullable<int> ServiceRequest_ID { get; set; }
            public Nullable<int> Quotation_ID { get; set; }
            public int Party_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public string SerialNumber { get; set; }
            public Nullable<int> Reading { get; set; }
            public int JobPriority { get; set; }
            public bool IsComponent { get; set; }
            public Nullable<int> DelayReason_ID { get; set; }
            public Nullable<System.DateTime> ActualStartDate { get; set; }
            public Nullable<System.DateTime> ActualEndDate { get; set; }
            public Nullable<System.DateTime> PlannedStartDate { get; set; }
            public string ActionForNextService { get; set; }
            public string CustomerComplaint { get; set; }
            public string CauseOfFailure { get; set; }
            public string CorrectiveAction { get; set; }
            public Nullable<System.DateTime> PlannedCompletionDate { get; set; }
            public Nullable<int> FailureReading { get; set; }
            public Nullable<int> RepairReading { get; set; }
            public Nullable<int> ProductLocation_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> PrimarySegment_ID { get; set; }
            public Nullable<int> SecondarySegment_ID { get; set; }
            public Nullable<int> DocumentNumber { get; set; }
            public Nullable<int> FinancialYear { get; set; }
            public Nullable<int> PartyContactPersonDetails_ID { get; set; }
            public Nullable<int> MachineStatus_ID { get; set; }
            public Nullable<int> Campaign_ID { get; set; }
            public string TransactionNumber { get; set; }
            public Nullable<int> Billto_ID { get; set; }
            public Nullable<int> CancelledReasn_ID { get; set; }
            public Nullable<int> Charge_To { get; set; }
            public string CouponNumber { get; set; }
            public Nullable<int> InsuranceParty_ID { get; set; }
            public string QuotationNumber { get; set; }
            public string ServiceRequestNumber { get; set; }
            public bool IsUnderwarranty { get; set; }
            public Nullable<int> ProductCSA_ID { get; set; }
            public Nullable<int> Invoice_ID { get; set; }
            public string InvoiceNumber { get; set; }
            public Nullable<int> InternalInvoice_ID { get; set; }
            public Nullable<int> ServiceInvoice_ID { get; set; }
            public Nullable<int> WarrantyClaim_ID { get; set; }
            public Nullable<int> ProductWarranty_ID { get; set; }
            public Nullable<int> MandatoryClaim_ID { get; set; }
            public Nullable<short> AttachmentCount { get; set; }
            public string Remarks { get; set; }
            public Nullable<bool> IsEarlyWarning { get; set; }
            public Nullable<decimal> ServiceChargeAmount { get; set; }
            public byte JobCardVersion { get; set; }
            public bool IsArchived { get; set; }
            public string GUIDJobCard { get; set; }
            public Nullable<byte> IsDealerList { get; set; }
            public Nullable<bool> IsBreakdown { get; set; }
            public Nullable<System.DateTime> JobActualStartDateTime { get; set; }
            public Nullable<System.DateTime> JobActualEndDateTime { get; set; }
            public Nullable<bool> IsNewJobAdded { get; set; }
            public Nullable<bool> IsInsideWork { get; set; }
            public string GatePass_No { get; set; }
            public Nullable<System.DateTime> GatePass_InDate { get; set; }
            public Nullable<System.DateTime> GatePass_OutDate { get; set; }
            public Nullable<decimal> GatePass_OutsideCharges { get; set; }
            public Nullable<int> Bay_ID { get; set; }
            public Nullable<System.DateTime> JobPlannedCompletionDate { get; set; }
            public string JobNumber { get; set; }
            public Nullable<System.DateTime> JobDate { get; set; }
            public Nullable<int> Job_SequenceID { get; set; }
            public Nullable<int> Current_AssignTo { get; set; }
            public Nullable<int> WO_ParentID { get; set; }
            public Nullable<int> ParentQuotation_ID { get; set; }
            public Nullable<int> OutsidePartner_ID { get; set; }
            public string Product_UniqueNumber { get; set; }
            public Nullable<decimal> AmountReceived { get; set; }
            public Nullable<System.DateTime> JobPlannedStartDate { get; set; }
            public Nullable<int> WorkSiteAddress_ID { get; set; }
            public Nullable<decimal> PartyDiscount { get; set; }
            public string EarlyWarningComments { get; set; }
            public Nullable<int> ContractorID { get; set; }
            public Nullable<int> ContractorContactPerson_ID { get; set; }
            public Nullable<int> Charge_To_Type { get; set; }
            public Nullable<bool> IsRework { get; set; }
            public Nullable<bool> IsUpsell { get; set; }
            public Nullable<int> UpsellEmployee_ID { get; set; }
            public Nullable<int> Rework_WorkOrderID { get; set; }
            public Nullable<int> CustomerRating { get; set; }
            public Nullable<System.DateTime> CustomerFeedBackDate { get; set; }
            public string CustomerFeedBack { get; set; }
            public Nullable<bool> IsNegetiveFeedBack { get; set; }
            public string PONumber { get; set; }
            public string FrontViewRemarks { get; set; }
            public string RearViewRemarks { get; set; }
            public string FrontRightViewRemarks { get; set; }
            public string FrontLeftViewRemarks { get; set; }
            public Nullable<decimal> StreetSideFrontTypePressure { get; set; }
            public Nullable<decimal> StreetSideRearType1Pressure { get; set; }
            public Nullable<decimal> StreetSideRearType2Pressure { get; set; }
            public Nullable<decimal> CurbSideFrontTypePressure { get; set; }
            public Nullable<decimal> CurbSideRearType1Pressure { get; set; }
            public Nullable<decimal> CurbSideRearType2Pressure { get; set; }
            public Nullable<bool> IsSentToSAP { get; set; }
            public Nullable<int> FixedPrice_ID { get; set; }
            public Nullable<decimal> FuelGaugeBefore { get; set; }
            public Nullable<decimal> FuelGaugeAfter { get; set; }
            public string SAPInvoiceNumber { get; set; }
            public Nullable<decimal> USCADExchangeRate { get; set; }
            public Nullable<bool> IsUpdateQuoteParts { get; set; }
            public Nullable<bool> IsUpdateQuotelabour { get; set; }
            public Nullable<bool> IsUpdateQuoteMisc { get; set; }
            public Nullable<bool> IsFristApproval { get; set; }
            public Nullable<bool> IsEdited { get; set; }
            public Nullable<int> EditedBy { get; set; }
            public Nullable<bool> IsReadyForSAPprocess { get; set; }
            public Nullable<bool> AdditionJobEstimate { get; set; }
            public Nullable<bool> IsVehicleArrived { get; set; }
            public Nullable<System.DateTime> SAPInvoiceDate { get; set; }
            public Nullable<byte> AddresseType { get; set; }
            public Nullable<decimal> BilledInvoicePayableHours { get; set; }
            public Nullable<decimal> ActualInvoicePayableHours { get; set; }
            public Nullable<decimal> BilledInternalInvoiceHours { get; set; }
            public Nullable<decimal> ActualInternalInvoiceHours { get; set; }
            public Nullable<decimal> BilledWarrantyChargedHours { get; set; }
            public Nullable<decimal> ActualWarrantyChargedHours { get; set; }
            public string Billed_VS_Actual_DiscrepancyReason { get; set; }
            public Nullable<int> BilledActualSI_Reason_ID { get; set; }
            public Nullable<int> BilledActualII_Reason_ID { get; set; }
            public Nullable<int> BilledActualWC_Reason_ID { get; set; }
            public Nullable<int> SAPInvoiceErrorStatus_ID { get; set; }
            public Nullable<bool> IsVariance { get; set; }
            public Nullable<bool> IsBranchMobileRate { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<System.DateTime> ModifiedBy { get; set; }
            public string KeyTagNumber { get; set; }
            public Nullable<int> ScheduledType_ID { get; set; }
            public Nullable<int> Year { get; set; }
            public Nullable<byte> Month { get; set; }
            public Nullable<System.DateTime> SentDate { get; set; }
            public Nullable<bool> IsAllWOClosed { get; set; }
            public Nullable<int> ShipTo_ID { get; set; }
            public Nullable<System.DateTime> ActualDepartureDateTime { get; set; }
            public Nullable<System.DateTime> ActualArrivalDateTime { get; set; }
        }
        class intClass
        {
            public int ID { get; set; }
        }
        public class ServiceRequestAgeing
        {
            public int ServiceRequest_ID { get; set; }
            public string ServiceRequestNumber { get; set; }
            public DateTime CallDateOrder { get; set; }
            public string CallDateAndTime { get; set; }
            public string Party_Name { get; set; }
            public string Brand_Name { get; set; }
            public string Model_Name { get; set; }
            public string SerialNumber { get; set; }
            public string FinancialYear { get; set; }
            public string Lock { get; set; }
            public string status { get; set; }
            public string QuotationNumber { get; set; }
            public string JobNumber { get; set; }
            public int AgeingHoursMinutes { get; set; }
            public string AgeingHours { get; set; }
            public string AgeingDays { get; set; }
            public string ProductType_Name { get; set; }
            public DateTime CallDateAndTimeDate { get; set; }
            public int AgeingDaysInt { get; set; }
            public string ActionRemarks { get; set; }
            public string NotesDescription { get; set; }
            public string Name { get; set; }
            public string Department { get; set; }
            public string QueryEscalatedTo { get; set; }
            public string CompanyName { get; set; } // Added by Venkateshwari.. CR 5 Changes ---- 10_sep-2015.
            public string BranchName { get; set; } // Added by Venkateshwari  CR 5 Changes QA Correction -29-Sep-2015
            public int Company_ID { get; set; } // Added by Venkateshwari.. CR 5 Changes ---- 10_sep-2015.
            public string Region { get; set; }

        }
        public class ServiceRequestCount
        {
            public int Year { get; set; }
            public int Month { get; set; }
            public string Type { get; set; }
            public int Count { get; set; }
            public string Date { get; set; }
            public int StatusID { get; set; }
            public List<int> StatusIDs { get; set; }
            public int StatusCount { get; set; }
            public string MonthName { get; set; }
            public string StatusName { get; set; }
            public int CompletedCount { get; set; }
            public int PendingCount { get; set; }
            public int InProgressCount { get; set; }
            public int OnHoldCount { get; set; }
            public int ClosedCount { get; set; }
            public double TimeDiff { get; set; }
            public string AvgResolution { get; set; }
            public string BranchShortCode { get; set; }
        }

        public class MD_Company
        {
            public List<MD_Companies> Companies
            {
                get;
                set;
            }
        }
        public class MD_Companies
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        public class MD_Branch
        {
            public List<MD_Branchs> Branchs
            {
                get;
                set;
            }
        }
        public class MD_Branchs
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        public class TimeSlot
        {
            public string Name { get; set; }
            public int Count { get; set; }
            public string TempName { get; set; }
            public string Hours { get; set; }
        }
        public partial class HD_ServiceRequestNotesDetail
        {
            public int ServiceRequestNotes_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int CreatedBy { get; set; }
            public System.DateTime CreatedDateAndTime { get; set; }
            public string NotesDescription { get; set; }
            public string Department { get; set; }
            public string Name { get; set; }
            public Nullable<bool> IsFollowUp { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_SRFollowUpInviteDetails
        {
            public int SRFollowUpInviteDetails_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int SRFollowUpDetails_ID { get; set; }
            public bool Invitee_Type { get; set; }
            public Nullable<int> Employee_ID { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public bool IsAttended { get; set; }

            public virtual HD_SRFollowUpDetails HD_SRFollowUpDetails { get; set; }
            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_SRFollowUpDetails
        {
            public HD_SRFollowUpDetails()
            {
                this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
            }

            public int SRFollowUpDetails_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public string FollowUpDescription { get; set; }
            public System.DateTime StartDateandTime { get; set; }
            public Nullable<System.DateTime> EndDateandTime { get; set; }
            public int FollowUpMode_ID { get; set; }
            public int FollowUpStatus_ID { get; set; }
            public string Remarks { get; set; }

            public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_CRERCRepository
        {
            public int CRERCRepository_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public string RequestPartNumber { get; set; }
            public string RequestPartDescription { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Parts_ID { get; set; }
            public bool IsEPC { get; set; }
            public bool IsPricedInSAP { get; set; }
            public Nullable<decimal> Rate { get; set; }
            public bool IsNLS { get; set; }
            public bool IsIPN { get; set; }
            public Nullable<System.DateTime> LRDDate { get; set; }
            public bool IsMailAttachment { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }

        public partial class HD_ServiceRequestAttachmentInfo
        {
            public int ServiceRequestAttachmentInfo_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public string FileName { get; set; }
            public int FileSize { get; set; }
            public string FileDescription { get; set; }
            public System.DateTime FileUploadDate { get; set; }
            public int FileUploadedByEmployee_ID { get; set; }
            public bool IsMailAttachment { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_SRPRODUCTDETAILSALLOCATION
        {
            public int SRPRODUCTDETAILSALLOCATION_ID { get; set; }
            public int SRPRODUCTDETAILS_ID { get; set; }
            public int PRODUCT_ID { get; set; }

            public virtual HD_SRProductDetails HD_SRProductDetails { get; set; }
        }
        public partial class HD_ServiceRequestPartsList
        {
            public int ServiceRequestPartsList_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int Parts_ID { get; set; }
            public int UnitOfMeasurement { get; set; }
            public decimal Quantity { get; set; }
            public string Remarks { get; set; }
            public decimal Rate { get; set; }
            public Nullable<decimal> MRP { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_SRProductDetails
        {
            public HD_SRProductDetails()
            {
                this.HD_SRPRODUCTDETAILSALLOCATION = new HashSet<HD_SRPRODUCTDETAILSALLOCATION>();
            }

            public int SRProductDetails_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int Model_ID { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public decimal Quantity { get; set; }
            public Nullable<decimal> ActiveQuantity { get; set; }
            public Nullable<decimal> WonQuantity { get; set; }
            public string LostSaleReasons { get; set; }
            public Nullable<int> Competitor_ID { get; set; }
            public Nullable<int> CompetitorModel_ID { get; set; }
            public Nullable<int> CompetitorBrand_ID { get; set; }
            public Nullable<int> CompetitorProductType_ID { get; set; }
            public Nullable<decimal> CompetitorPrice { get; set; }
            public Nullable<int> PrimarySegment_ID { get; set; }
            public Nullable<int> SecondarySegment_ID { get; set; }
            public string Remarks { get; set; }
            public Nullable<decimal> Rate { get; set; }
            public Nullable<int> PACKINGTYPE_ID { get; set; }
            public string ReferenceDetails { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
            public virtual ICollection<HD_SRPRODUCTDETAILSALLOCATION> HD_SRPRODUCTDETAILSALLOCATION { get; set; }
        }
        public partial class HD_CustomerFeedbackQuestion
        {
            public HD_CustomerFeedbackQuestion()
            {
                this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
            }

            public int Question_ID { get; set; }
            public string Question { get; set; }
            public string CaseType_IDs { get; set; }
            public string IssueArea_IDs { get; set; }
            public string IssueSubArea_IDs { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsRating { get; set; }
            public bool IsFeedback { get; set; }
            public bool IsActive { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> QuestionCode { get; set; }

            public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
        }
        public partial class HD_SRCustomerQuestionFeedBack
        {
            public int QuestionFeedBack_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int Question_ID { get; set; }
            public System.DateTime FeedBackDate { get; set; }
            public string FeedBack { get; set; }
            public Nullable<byte> Rating { get; set; }

            public virtual HD_CustomerFeedbackQuestion HD_CustomerFeedbackQuestion { get; set; }
            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_ServiceRequest
        {
            public HD_ServiceRequest()
            {
                this.HD_ServiceRequestNotesDetail = new HashSet<HD_ServiceRequestNotesDetail>();
                this.HD_SRFollowUpDetails = new HashSet<HD_SRFollowUpDetails>();
                this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
                this.HD_CRERCRepository = new HashSet<HD_CRERCRepository>();
                this.HD_ServiceRequestAttachmentInfo = new HashSet<HD_ServiceRequestAttachmentInfo>();
                this.HD_ServiceRequestPartsList = new HashSet<HD_ServiceRequestPartsList>();
                this.HD_SRProductDetails = new HashSet<HD_SRProductDetails>();
                this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
            }

            public int ServiceRequest_ID { get; set; }
            public string ServiceRequestNumber { get; set; }
            public System.DateTime ServiceRequestDate { get; set; }
            public Nullable<int> Quotation_ID { get; set; }
            public string QuotationNumber { get; set; }
            public Nullable<int> JobCard_ID { get; set; }
            public string JobCardNumber { get; set; }
            public int CallStatus_ID { get; set; }
            public Nullable<int> ParentIssue_ID { get; set; }
            public string Product_Unique_Number { get; set; }
            public int Party_ID { get; set; }
            public int PartyContactPerson_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public string SerialNumber { get; set; }
            public Nullable<int> ProductReading { get; set; }
            public bool IsUnderWarranty { get; set; }
            public int CallMode_ID { get; set; }
            public Nullable<int> CallPriority_ID { get; set; }
            public int CallComplexity_ID { get; set; }
            public System.DateTime CallDateAndTime { get; set; }
            public Nullable<System.DateTime> PromisedCompletionDate { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string CallDescription { get; set; }
            public Nullable<int> IssueArea_ID { get; set; }
            public Nullable<int> IssueSubArea_ID { get; set; }
            public Nullable<int> FunctionGroup_ID { get; set; }
            public bool IsUnderBreakDown { get; set; }
            public Nullable<int> QuestionaryLevel1_ID { get; set; }
            public Nullable<int> QuestionaryLevel2_ID { get; set; }
            public Nullable<int> QuestionaryLevel3_ID { get; set; }
            public Nullable<int> DefectGroup_ID { get; set; }
            public Nullable<int> DefectName_ID { get; set; }
            public string RootCause { get; set; }
            public string InformationCollected { get; set; }
            public Nullable<System.DateTime> CallClosureDateAndTime { get; set; }
            public Nullable<int> ClosureType_ID { get; set; }
            public string ClosingDescription { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }
            public Nullable<int> ModifiedBy_ID { get; set; }
            public Nullable<int> Document_no { get; set; }
            public int CaseType_ID { get; set; }
            public string ActionRemarks { get; set; }
            public Nullable<int> Product_ID { get; set; }
            public Nullable<int> CustomerRating { get; set; }
            public Nullable<int> FinancialYear { get; set; }
            public Nullable<bool> IsCallBlocked { get; set; }
            public Nullable<int> StockBlocking_ID { get; set; }
            public Nullable<int> EnquiryStage_ID { get; set; }
            public Nullable<int> SalesQuotation_ID { get; set; }
            public string SalesQuotationNumber { get; set; }
            public Nullable<int> SalesOrder_ID { get; set; }
            public string SalesOrderNumber { get; set; }
            public Nullable<System.DateTime> SalesOrderDate { get; set; }
            public Nullable<int> CallOwner_ID { get; set; }
            public string Flexi1 { get; set; }
            public string Flexi2 { get; set; }
            public string ResolutionTime { get; set; }
            public string ResponseTime { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public Nullable<System.DateTime> CustomerFeedbackDate { get; set; }
            public Nullable<bool> IsNegetiveFeedback { get; set; }
            public Nullable<bool> IsDealer { get; set; }
            public Nullable<byte> IsDealerList { get; set; }
            public Nullable<byte> ProductRateType { get; set; }
            public Nullable<int> ChildTicket_Sequence_ID { get; set; }
            public string ResponseTime24 { get; set; }
            public string ResolutionTime24 { get; set; }
            public Nullable<int> Current_AssignTo { get; set; }
            public Nullable<int> ContractorID { get; set; }
            public Nullable<int> ContractorContactPerson_ID { get; set; }
            public Nullable<int> ScheduledType_ID { get; set; }
            public Nullable<System.DateTime> ExpectedArrivalDateTime { get; set; }
            public Nullable<System.DateTime> ActualArrivalDateTime { get; set; }
            public Nullable<System.DateTime> ExpectedDepartureDateTime { get; set; }
            public Nullable<System.DateTime> ActualDepartureDateTime { get; set; }
            public Nullable<int> NoofTechs { get; set; }
            public Nullable<int> ShiftHours { get; set; }
            public Nullable<bool> IsWIPBay { get; set; }

            public virtual ICollection<HD_ServiceRequestNotesDetail> HD_ServiceRequestNotesDetail { get; set; }
            public virtual ICollection<HD_SRFollowUpDetails> HD_SRFollowUpDetails { get; set; }
            public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
            public virtual ICollection<HD_CRERCRepository> HD_CRERCRepository { get; set; }
            public virtual ICollection<HD_ServiceRequestAttachmentInfo> HD_ServiceRequestAttachmentInfo { get; set; }
            public virtual ICollection<HD_ServiceRequestPartsList> HD_ServiceRequestPartsList { get; set; }
            public virtual ICollection<HD_SRProductDetails> HD_SRProductDetails { get; set; }
            public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
        }
        #endregion


    }
}
