﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Linq;
using PBC.UtilityService.Utilities.DTOs;
using LS = PBC.UtilityService.Utilities;

namespace PBC.UtilityService.Utilities
{

    public static class ReturnIPAddress
    {
        //public static string ReturnIP()
        //{
        //    string VisitorsIPAddr = string.Empty;
        //    // First Method
        //    if (HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        //    }
        //    else if (HttpContext.Current.Request.UserHostAddress.Length != 0)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.UserHostAddress;
        //    }

        //    return VisitorsIPAddr;
        //}
        // public static string ReturnIP1()
        //{
        //    string VisitorsIPAddr = string.Empty;
        //    // First Method
        //    if (HttpContext.Current.Request.ServerVariables["HTTP_CLIENT_IP"] != null)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.ServerVariables["HTTP_CLIENT_IP"].ToString();

        //        if (!string.IsNullOrEmpty(VisitorsIPAddr))
        //         {
        //             string[] ipRange = VisitorsIPAddr.Split(',');
        //             VisitorsIPAddr = ipRange[0].Trim();
        //         }
        //    }
        //    return VisitorsIPAddr;
        //}
        //public static string ReturnIP()
        //{
        //    string VisitorsIPAddr = string.Empty;
        //    if (HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"] != null)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"].ToString();
        //        if (!string.IsNullOrEmpty(VisitorsIPAddr))
        //        {
        //            string[] ipRange = VisitorsIPAddr.Split(',');
        //            VisitorsIPAddr = ipRange[0].Trim();
        //        }
        //    }
        //    return VisitorsIPAddr;
        //}

    }

    public class DocumentExport
    {

        #region :::DocumentExport   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get DocumentExport 
        /// </summary>
        /// 
        public static async Task<JsonResult> Export(ExportList ExportObj, string connstring, int LogException /*,int exprtType, DataTable dt, DataTable dt1, string FileName, string Header*/)
        {


            string bid = ExportObj.Branch == null ? "0" : ExportObj.Branch.ToString();
            int BranchID = Convert.ToInt32(bid);
            string cName = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "CompanyName").ToString();
            string BName = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "BranchName").ToString();
            string BAddress = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "BranchAddress").ToString();
            string companyName = "";

            string BranchName = "";
            string BranchAddress = "";

            SqlCommand command = null;
            using (SqlConnection conn = new SqlConnection(connstring))
            {
                string query = "UP_Export_AM_ERP_Export_Common";



                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", ExportObj.Company_ID);
                        command.Parameters.AddWithValue("@BranchID", BranchID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyName = reader["Company_Name"] as string;
                                BranchName = reader["Branch_Name"] as string;
                                BranchAddress = reader["Branch_Address"] as string;
                            }


                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            string ReportImagePath = "";
            if (System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath") != null)
                ReportImagePath = System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath").ToString();
            try
            {
                int colCount = ExportObj.dt.Columns.Count;

                StringBuilder sb = new StringBuilder();
                sb.Append("<table>");
                // added by shivanand 14-Jun-2018 started
                if (ReportImagePath != "")
                    //sb.Append("</tr rowspan='10'><td colspan='" + colCount + "'>" + CompanyClient.GNM_HEADERFOOTERPRINT.Where(h => h.COMPANY_ID == UserDetails.Company_ID && h.Branch_ID == BranchID).Select(h => h.HEADERTEMPLATE).FirstOrDefault() + "</td><tr></tr>");
                    sb.Append("</tr rowspan='4'><td rowspan='3'> <img src='" + ReportImagePath + "'></img></td><tr></tr>");
                // added by shivanand 14-Jun-2018 end
                sb.Append("<tr><td colspan='" + colCount + "' style='text-align:center'>");
                sb.Append("<B>" + ExportObj.Header + "</B>");
                sb.Append("</td></tr>");
                sb.Append("<tr></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + BAddress + "</B> :</B></td><td colspan='" + (colCount - 1) + "' >" + BranchAddress + "</td></tr><tr></tr></table>");
                sb.Append("<table border='1'>");
                sb.Append("<thead><tr>");
                for (int i = 0; i < colCount; i++)
                {
                    sb.Append("<td style='text-align: center;background-color:gray;'><B>");
                    sb.Append(ExportObj.dt.Columns[i].ColumnName.ToString());
                    sb.Append("</B></td>");
                }
                sb.Append("</tr></thead>");
                for (int j = 0; j < ExportObj.dt.Rows.Count; j++)
                {
                    sb.Append("<tr>");
                    for (int k = 0; k < colCount; k++)
                    {
                        string align = (ExportObj.dt1.Rows[0].ItemArray[k].ToString() == "0" ? "left" : (ExportObj.dt1.Rows[0].ItemArray[k].ToString() == "1" ? "center" : "right"));

                        sb.Append("<td 'style=text-align:" + align + ";font-weight:bold'>");

                        if (((ExportObj.dt.Rows[j].ItemArray[0].ToString() == "" && ExportObj.Header != CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "WIPVehicles").ToString()) || ExportObj.dt.Rows[j].ItemArray[0].ToString() == CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "GrandTotalSumofallDaysindate").ToString() || ExportObj.dt.Rows[j].ItemArray[0].ToString().Contains("Sub-Total (Day Total)")) && ExportObj.dt.Rows[j].ItemArray[k].ToString() != "")
                        {
                            sb.Append("<b>" + ExportObj.dt.Rows[j].ItemArray[k].ToString() + "</b>");
                        }
                        else if (ExportObj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "ClockInDateTime").ToString()) || ExportObj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "CustomerAccountNumber").ToString()))
                        {
                            string Value = "=concatenate(\"" + ExportObj.dt.Rows[j].ItemArray[k].ToString() + "\")";
                            sb.Append(Value);
                        }
                        else
                        {
                            sb.Append(ExportObj.dt.Rows[j].ItemArray[k].ToString());
                        }
                        sb.Append("</td>");
                    }
                    sb.Append("</tr>");
                }
                sb.Append("</table>");

                if (ExportObj.exprtType == 1) // Excel Export
                {
                    ExportObj.FileName = ExportObj.FileName + ".xls";

                    byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                    string base64String = Convert.ToBase64String(fileBytes);


                    return new JsonResult(new
                    {
                        FileName = ExportObj.FileName,
                        FileContent = base64String,
                        ContentType = "application/vnd.ms-excel"
                    });
                }
                else
                {

                    using (MemoryStream ms = new MemoryStream())
                    {
                        Document document = new Document(PageSize.A4);
                        PdfWriter writer = PdfWriter.GetInstance(document, ms);
                        document.Open();

                        // Example header
                        Paragraph title = new Paragraph(ExportObj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD));
                        title.Alignment = Element.ALIGN_CENTER;
                        document.Add(title);

                        // Adding Company, Branch Name, and Address
                        document.Add(new Paragraph($"{cName}: {companyName}", FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph($"{BName}: {BranchName}", FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph($"Branch Address: {BranchAddress}", FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph(""));
                        document.Add(new Paragraph(""));
                        document.Add(new Paragraph(""));
                        document.Add(new Paragraph(""));

                        // Add spacing above the table
                        Paragraph tableSpacing = new Paragraph();
                        tableSpacing.SpacingAfter = 20f; // 20 points space before the table
                        document.Add(tableSpacing);

                        // Data table setup
                        PdfPTable dataTable = new PdfPTable(ExportObj.dt.Columns.Count)
                        {
                            WidthPercentage = 100,      // Set table to take 100% width of the page
                            SpacingBefore = 10f         // Space before the table in points
                        };

                        // Adding header cells to the table
                        foreach (DataColumn column in ExportObj.dt.Columns)
                        {
                            dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                            {
                                BackgroundColor = BaseColor.LIGHT_GRAY,
                                HorizontalAlignment = Element.ALIGN_CENTER
                            });
                        }

                        // Adding data cells to the table
                        foreach (DataRow row in ExportObj.dt.Rows)
                        {
                            foreach (var cell in row.ItemArray)
                            {
                                string value = cell.ToString();
                                PdfPCell pdfCell = new PdfPCell(new Phrase(value));

                                // Determine alignment using the provided alignment table logic
                                pdfCell.HorizontalAlignment = Element.ALIGN_LEFT; // Default alignment if index is out of range

                                dataTable.AddCell(pdfCell);
                            }
                        }
                        document.Add(dataTable);
                        document.Close();

                        // Convert the memory stream to a byte array and prepare the response
                        byte[] fileBytes = ms.ToArray();
                        string base64String = Convert.ToBase64String(fileBytes);

                        return new JsonResult(new
                        {
                            FileName = ExportObj.FileName,
                            FileContent = base64String,
                            ContentType = "application/pdf"
                        });
                    }
                }

            }
            catch (Exception ex) { }
            return null;
        }
        #endregion

    }

    public static class ReportExport
    {

        #region :::ReportExport   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get ReportExport 
        /// </summary>
        /// 
        public static async Task<JsonResult> Export(ReportExportList ExportObj, string connstring, int LogException)
        {
            //DataSet selection;

            string bid = (string)ExportObj.Branch;
            int GeneralLangauageID = Convert.ToInt32(ExportObj.GeneralLanguageID);
            int UserLanagauageID = Convert.ToInt32(ExportObj.UserLanguageID);

            int BranchID = Convert.ToInt32(bid);

            string cName = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "CompanyName").ToString();
            string BName = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "BranchName").ToString();
            string BAddress = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "BranchAddress").ToString();
            string companyName = "";
            //string companyName = (GeneralLangauageID == UserLanagauageID) ? CompanyClient.GNM_Company.Where(a => a.Company_ID == UserDetails.Company_ID).FirstOrDefault().Company_Name : CompanyClient.GNM_CompanyLocale.Where(a => a.Company_ID == UserDetails.Company_ID).FirstOrDefault().Company_Name;
            string BranchName = "";
            string BranchAddress = "";
            //if (BranchID != 0)
            //    BranchName = (GeneralLangauageID == UserLanagauageID) ? CompanyClient.GNM_Branch.Where(a => a.Branch_ID == BranchID).FirstOrDefault().Branch_Name : CompanyClient.GNM_BranchLocale.Where(a => a.Branch_ID == BranchID).FirstOrDefault().Branch_Name;
            //string BranchAddress = CompanyClient.GNM_Branch.Where(a => a.Branch_ID == BranchID).FirstOrDefault().Branch_Address;
            SqlCommand command = null;
            using (SqlConnection conn = new SqlConnection(connstring))
            {
                string query = "UP_Export_AM_ERP_Export_CommonReportExport";



                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", ExportObj.Company_ID);
                        command.Parameters.AddWithValue("@BranchID", BranchID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", GeneralLangauageID);
                        command.Parameters.AddWithValue("@UserLanguageID", UserLanagauageID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyName = reader["Company_Name"] as string;
                                BranchName = reader["Branch_Name"] as string;
                                BranchAddress = reader["Branch_Address"] as string;
                            }


                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            string ReportImagePath = "";
            if (System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath") != null)
                ReportImagePath = System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath").ToString();
            try
            {
                int colCount = ExportObj.dt.Columns.Count;
                //if (ExportObj.exprtType == 1)
                //{
                StringBuilder sb = new StringBuilder();
                sb.Append("<table>");
                // added by shivanand 14-Jun-2018 started
                if (ReportImagePath != "")
                    //sb.Append("</tr rowspan='10'><td colspan='" + colCount + "'>" + CompanyClient.GNM_HEADERFOOTERPRINT.Where(h => h.COMPANY_ID == UserDetails.Company_ID && h.Branch_ID == BranchID).Select(h => h.HEADERTEMPLATE).FirstOrDefault() + "</td><tr></tr>");
                    sb.Append("</tr><td <img src='" + ReportImagePath + "'></img></td><tr></tr>");
                // added by shivanand 14-Jun-2018 ended
                sb.Append("<tr><td colspan='" + colCount + "' style='text-align:center'>");
                sb.Append("<B>" + ExportObj.Header + "</B>");
                sb.Append("</td></tr>");
                sb.Append("<tr></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + BAddress + "</B></td><td colspan='" + (colCount - 1) + "' >" + BranchAddress + "</td></tr><tr></tr></table>");
                sb.Append("<table>");
                for (int j = 0; j < ExportObj.Options.Columns.Count; j++)
                {
                    sb.Append("<tr>");
                    for (int k = 0; k < (ExportObj.Options.Rows.Count); k++)
                    {
                        sb.Append("<td style='text-align:right'>");
                        sb.Append("<B>" + ExportObj.Options.Columns[j].ColumnName.ToString() + "</B>:</td><td style='text-align:left'> " + ExportObj.Options.Rows[k].ItemArray[j].ToString());
                        sb.Append("</td>");
                    }
                    sb.Append("</tr>");
                }
                sb.Append("<tr></tr>");
                sb.Append("</table>");
                sb.Append("<table border='1'>");
                sb.Append("<thead><tr>");
                for (int i = 0; i < colCount; i++)
                {
                    sb.Append("<td style='text-align: center;background-color:gray;'>");
                    sb.Append("<b>" + ExportObj.dt.Columns[i].ColumnName.ToString() + "</b>");
                    sb.Append("</td>");
                }
                sb.Append("</tr></thead>");
                for (int j = 0; j < ExportObj.dt.Rows.Count; j++)
                {
                    sb.Append("<tr>");
                    for (int k = 0; k < colCount; k++)
                    {
                        string align = (ExportObj.Alignment.Rows[0].ItemArray[k].ToString() == "0" ? "left" : (ExportObj.Alignment.Rows[0].ItemArray[k].ToString() == "1" ? "center" : "right"));
                        sb.Append("<td 'style=text-align:" + align + ";'>");
                        if (ExportObj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "CustomerAccountNumber").ToString()))
                        {
                            string Value = "=concatenate(\"" + ExportObj.dt.Rows[j].ItemArray[k].ToString() + "\")";
                            sb.Append(Value);
                        }
                        else
                        {
                            sb.Append(ExportObj.dt.Rows[j].ItemArray[k].ToString());
                        }
                        sb.Append("</td>");
                    }
                    sb.Append("</tr>");
                }
                sb.Append("</table>");



                if (ExportObj.exprtType == 1) // Excel Export
                {
                    ExportObj.FileName = ExportObj.FileName + ".xls";

                    byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                    string base64String = Convert.ToBase64String(fileBytes);


                    return new JsonResult(new
                    {
                        FileName = ExportObj.FileName,
                        FileContent = base64String,
                        ContentType = "application/vnd.ms-excel"
                    });
                }
                else
                {

                    ExportObj.FileName = ExportObj.FileName + ".pdf";
                    using (MemoryStream ms = new MemoryStream())
                    {
                        Document document = new Document(PageSize.A4);
                        PdfWriter writer = PdfWriter.GetInstance(document, ms);
                        document.Open();

                        // Title header
                        Paragraph title = new Paragraph(ExportObj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                        {
                            Alignment = Element.ALIGN_CENTER
                        };
                        document.Add(title);

                        // Company, Branch, and Address details
                        document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph("Branch Address: " + BranchAddress, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph(" "));  // Adding extra spacing
                        document.Add(new Paragraph(" "));

                        // Options Table setup
                        PdfPTable optionsTable = new PdfPTable(2)
                        {
                            WidthPercentage = 100,   // Set to 100% page width
                            SpacingBefore = 10f      // Adds 10 points of space before the table
                        };

                        // Adding header cells to the Options Table
                        foreach (DataColumn column in ExportObj.Options.Columns)
                        {
                            optionsTable.AddCell(new PdfPCell(new Phrase(column.ColumnName)) { HorizontalAlignment = Element.ALIGN_CENTER });
                        }

                        // Adding data cells to the Options Table
                        foreach (DataRow row in ExportObj.Options.Rows)
                        {
                            foreach (var cell in row.ItemArray)
                            {
                                optionsTable.AddCell(new PdfPCell(new Phrase(cell.ToString())) { HorizontalAlignment = Element.ALIGN_LEFT });
                            }
                        }
                        document.Add(optionsTable);

                        // Adding spacing between Options and Data tables
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Data Table setup
                        PdfPTable dataTable = new PdfPTable(colCount)
                        {
                            WidthPercentage = 100,   // Set to 100% page width
                            SpacingBefore = 10f      // Adds 10 points of space before the table
                        };

                        // Adding header cells to the Data Table
                        foreach (DataColumn column in ExportObj.dt.Columns)
                        {
                            dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                            {
                                BackgroundColor = BaseColor.LIGHT_GRAY,
                                HorizontalAlignment = Element.ALIGN_CENTER
                            });
                        }

                        // Adding data cells to the Data Table with alignment based on ExportObj.Alignment
                        foreach (DataRow row in ExportObj.dt.Rows)
                        {
                            foreach (var cell in row.ItemArray)
                            {
                                string value = cell.ToString();
                                PdfPCell pdfCell = new PdfPCell(new Phrase(value));
                                int columnIndex = Array.IndexOf(ExportObj.dt.Columns.Cast<DataColumn>().ToArray(), cell);

                                if (columnIndex >= 0 && columnIndex < ExportObj.Alignment.Rows[0].ItemArray.Length)
                                {
                                    object alignment = ExportObj.Alignment.Rows[0].ItemArray[columnIndex];
                                    pdfCell.HorizontalAlignment = GetCellAlignment(alignment);
                                }
                                else
                                {
                                    pdfCell.HorizontalAlignment = Element.ALIGN_LEFT;
                                }

                                dataTable.AddCell(pdfCell);
                            }
                        }
                        document.Add(dataTable);
                        document.Close();

                        // Convert the memory stream to a byte array and prepare the response
                        byte[] fileBytes = ms.ToArray();
                        string base64String = Convert.ToBase64String(fileBytes);

                        return new JsonResult(new
                        {
                            FileName = ExportObj.FileName,
                            FileContent = base64String,
                            ContentType = "application/pdf"
                        });
                    }
                }

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return null;
        }
        private static int GetCellAlignment(object alignmentValue)
        {
            if (alignmentValue == null)
                return Element.ALIGN_LEFT; // Default alignment

            string alignment = alignmentValue.ToString().ToLower();
            switch (alignment)
            {
                case "center":
                    return Element.ALIGN_CENTER;
                case "right":
                    return Element.ALIGN_RIGHT;
                default:
                    return Element.ALIGN_LEFT; // Default to left alignment
            }
        }
        #endregion

    }


    public static class DashBoardExport
    {
        #region :::DashBoardExport   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get DashBoardExport 
        /// </summary>
        /// 
        public static async Task<JsonResult> Export(DashBoardExportList Obj, string connstring, int LogException)
        {
            string bid = Obj.Branch == null ? "0" : Obj.Branch.ToString();
            int GeneralLangauageID = Convert.ToInt32(Obj.GeneralLanguageID);
            int UserLanagauageID = Convert.ToInt32(Obj.UserLanguageID);
            int Company_ID = Convert.ToInt32(Obj.Company_ID);
            int BranchID = Convert.ToInt32(bid);

            string cName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyName").ToString();
            string BName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BranchName").ToString();
            string FD = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "fromdate").ToString();
            string TD = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ToDate").ToString();
            string companyName = "";
            string BranchName = "";
            SqlCommand command = null;
            using (SqlConnection conn = new SqlConnection(connstring))
            {
                string query = "UP_Export_AM_ERP_Export_CommonReportExport";


                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                        command.Parameters.AddWithValue("@BranchID", BranchID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", GeneralLangauageID);
                        command.Parameters.AddWithValue("@UserLanguageID", UserLanagauageID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyName = reader["Company_Name"] as string;
                                BranchName = reader["Branch_Name"] as string;
                            }


                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            string ReportImagePath = "";
            if (System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath") != null)
                ReportImagePath = System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath").ToString();
            try
            {
                int colCount = Obj.dt.Columns.Count;
                DateTime? FromDate = Obj.FromDate;
                if (FromDate == null)
                {
                    using (SqlConnection conn = new SqlConnection(connstring))
                    {
                        string query = @"SELECT CAST(Company_FinancialYear_FromDate AS DATE) 
                       FROM GNM_CompanyFinancialYear 
                       WHERE GETDATE() BETWEEN Company_FinancialYear_FromDate AND Company_FinancialYear_ToDate 
                       AND Company_ID = @Company_ID";


                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Company_ID", Company_ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                object result = command.ExecuteScalar();

                                if (result != null && result != DBNull.Value)
                                {
                                    FromDate = Convert.ToDateTime(result);
                                }
                                else
                                {
                                    FromDate = null;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }
                else
                {
                    FromDate = Obj.FromDate;
                }
                DateTime? ToDate = (Obj.ToDate == null) ? DateTime.Now : Obj.ToDate;
                string FromdateString = Convert.ToDateTime(FromDate).ToString("dd-MMM-yyyy");
                string TodateString = Convert.ToDateTime(ToDate).ToString("dd-MMM-yyyy");
                StringBuilder sb = new StringBuilder();
                sb.Append("<table>");
                // added by shivanand 14-Jun-2018 started
                if (ReportImagePath != "")
                    sb.Append("</tr><td <img src='" + ReportImagePath + "'></img></td><tr></tr>");
                sb.Append("<tr></tr>");
                sb.Append("<tr><td colspan='" + colCount + "' style='text-align:center'>");
                if (Obj.Header != null && Obj.Header != "")
                {
                    sb.Append("<B>" + Obj.Header + "</B>");
                }
                sb.Append("</td></tr>");
                sb.Append("<tr></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + FD + "</B> :</td><td style='text-align:left' >" + FromdateString + "</td></tr>");
                sb.Append("<tr><td style='text-align:right'><B>" + TD + "</B> :</td><td style='text-align:left' >" + TodateString + "</td></tr>");
                sb.Append("<table border='1'>");
                sb.Append("<thead><tr>");


                for (int i = 0; i < colCount; i++)
                {
                    sb.Append("<td style='text-align: center;background-color:gray;'><B>");
                    sb.Append(Obj.dt.Columns[i].ColumnName.ToString());
                    sb.Append("</B></td>");
                }
                sb.Append("</tr></thead>");
                for (int j = 0; j < Obj.dt.Rows.Count; j++)
                {
                    sb.Append("<tr>");
                    for (int k = 0; k < colCount; k++)
                    {
                        string align = "left";
                        if (Obj.dt1.Rows[0].ItemArray[k].ToString() == "1") align = "center";
                        else if (Obj.dt1.Rows[0].ItemArray[k].ToString() == "2") align = "right";
                        sb.Append("<td 'style=text-align:" + align + ";'>");
                        if (Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ETOFromTime").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ETOTotime").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerAccountNumber").ToString()))
                        {
                            string Value = "=concatenate(\"" + Obj.dt.Rows[j].ItemArray[k].ToString() + "\")";
                            sb.Append(Value);
                        }
                        else
                        {
                            sb.Append(Obj.dt.Rows[j].ItemArray[k].ToString());
                        }
                        sb.Append("</td>");
                    }
                    sb.Append("</tr>");
                }
                sb.Append("</table>");

                if (Obj.exprtType == 1) // Excel Export
                {
                    Obj.FileName = Obj.FileName + ".xls";

                    byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                    string base64String = Convert.ToBase64String(fileBytes);


                    return new JsonResult(new
                    {
                        FileName = Obj.FileName,
                        FileContent = base64String,
                        ContentType = "application/vnd.ms-excel"
                    });
                }
                else
                {

                    Obj.FileName = Obj.FileName + ".pdf";
                    using (MemoryStream ms = new MemoryStream())
                    {
                        Document document = new Document(PageSize.A4);
                        PdfWriter writer = PdfWriter.GetInstance(document, ms);
                        document.Open();

                        // Header Title
                        Paragraph title = new Paragraph(Obj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                        {
                            Alignment = Element.ALIGN_CENTER
                        };
                        document.Add(title);

                        // Adding Company, Branch, and Date Details
                        document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph($"{FD}: {FromdateString}", FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph($"{TD}: {TodateString}", FontFactory.GetFont("Arial", 12)));

                        // Adding space before the options table
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Options Table setup
                        PdfPTable optionsTable = new PdfPTable(2)
                        {
                            WidthPercentage = 100,    // Full page width
                            SpacingBefore = 5f       // Space before the table
                        };
                        document.Add(optionsTable);

                        // Adding space before the data table
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Data Table setup
                        PdfPTable dataTable = new PdfPTable(colCount)
                        {
                            WidthPercentage = 100,    // Full page width
                            SpacingBefore = 5f       // Space before the table
                        };

                        // Adding column headers with a gray background
                        foreach (DataColumn column in Obj.dt.Columns)
                        {
                            dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                            {
                                BackgroundColor = BaseColor.LIGHT_GRAY,
                                HorizontalAlignment = Element.ALIGN_CENTER
                            });
                        }

                        // Adding data rows to the table
                        foreach (DataRow row in Obj.dt.Rows)
                        {
                            foreach (var cell in row.ItemArray)
                            {
                                string value = cell.ToString();
                                PdfPCell pdfCell = new PdfPCell(new Phrase(value))
                                {
                                    HorizontalAlignment = Element.ALIGN_LEFT   // Default alignment
                                };

                                // If alignment logic is needed, implement here

                                dataTable.AddCell(pdfCell);
                            }
                        }
                        document.Add(dataTable);

                        // Close the document
                        document.Close();

                        // Convert the memory stream to a byte array and return as JSON result
                        byte[] fileBytes = ms.ToArray();
                        string base64String = Convert.ToBase64String(fileBytes);

                        return new JsonResult(new
                        {
                            FileName = Obj.FileName,
                            FileContent = base64String,
                            ContentType = "application/pdf"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return null;
        }
        #endregion
    }


    public static class ReportExportCR5
    {

        #region :::ReportExportCR5   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get ReportExportCR5 
        /// </summary>
        /// 
        public static async Task<JsonResult> Export(ExportReportExportCR5List Obj, string connString, int LogException)
        {

            string bid = (string)Obj.Branch;
            int BranchID = Convert.ToInt32(bid);
            string cName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyName").ToString();
            string BName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BranchName").ToString();
            string companyName = "";

            string BranchName = "";


            SqlCommand command = null;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Export_AM_ERP_Export_Common";



                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                        command.Parameters.AddWithValue("@BranchID", BranchID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyName = reader["Company_Name"] as string;
                                BranchName = reader["Branch_Name"] as string;

                            }


                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            try
            {
                int colCount = Obj.dt.Columns.Count;
                if (Obj.exprtType == 1)
                {
                    Obj.FileName = Obj.FileName + ".xls";
                    StringBuilder sb = new StringBuilder();
                    sb.Append("<table><tr><td colspan='" + colCount + "' style='text-align:center'>");
                    sb.Append("<B>" + Obj.Header + "</B>");
                    sb.Append("</td></tr>");
                    sb.Append("<tr></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr><tr></tr></table>");
                    sb.Append("<table>");
                    for (int j = 0; j < Obj.Options.Columns.Count; j++)
                    {
                        sb.Append("<tr>");
                        for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                        {
                            sb.Append("<td style='text-align:right'>");
                            sb.Append("<B>" + Obj.Options.Columns[j].ColumnName.ToString() + "</B> :</td><td style='text-align:left'> " + Obj.Options.Rows[k].ItemArray[j].ToString());
                            sb.Append("</td>");
                        }
                        sb.Append("</tr>");
                    }
                    sb.Append("<tr></tr>");
                    sb.Append("</table>");
                    sb.Append("<table>");
                    for (int k = 0; k < Obj.selection.Tables.Count; k++)
                    {
                        DataTable Local = Obj.selection.Tables[k];
                        sb.Append("<tr>");
                        sb.Append("<td style='text-align:right'><b>" + Local.Columns[0].ColumnName.ToString() + "</b> :</td>");
                        sb.Append("<td colspan='" + (colCount - 1) + "'>");
                        for (int i = 0; i < Local.Rows.Count; i++)
                        {
                            sb.Append(Local.Rows[i].ItemArray[0].ToString());
                        }
                        sb.Append("</td>");
                        sb.Append("</tr>");
                    }
                    sb.Append("<tr></tr>");
                    sb.Append("</table>");
                    sb.Append("<table border='1'>");
                    sb.Append("<thead><tr>");
                    for (int i = 0; i < colCount; i++)
                    {
                        sb.Append("<td style='text-align: center;background-color:gray;'>");
                        sb.Append("<b>" + Obj.dt.Columns[i].ColumnName.ToString() + "</b>");
                        sb.Append("</td>");
                    }
                    sb.Append("</tr></thead>");
                    for (int j = 0; j < Obj.dt.Rows.Count; j++)
                    {
                        sb.Append("<tr>");
                        for (int k = 0; k < colCount; k++)
                        {
                            string align = (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "0" ? "left" : (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "1" ? "center" : "right"));
                            sb.Append("<td  'style=text-align:'" + align + "';'>");
                            sb.Append(Obj.dt.Rows[j].ItemArray[k].ToString());
                            sb.Append("</td>");
                        }
                        sb.Append("</tr>");
                    }
                    sb.Append("</table>");
                    byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                    string base64String = Convert.ToBase64String(fileBytes);


                    return new JsonResult(new
                    {
                        FileName = Obj.FileName,
                        FileContent = base64String,
                        ContentType = "application/vnd.ms-excel"
                    });

                }
                else if (Obj.exprtType == 2)
                {
                    Obj.FileName = Obj.FileName + ".pdf";
                    using (MemoryStream ms = new MemoryStream())
                    {
                        Document document = new Document(PageSize.A3);
                        PdfWriter writer = PdfWriter.GetInstance(document, ms);
                        document.Open();

                        // Header Title
                        Paragraph title = new Paragraph(Obj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                        {
                            Alignment = Element.ALIGN_CENTER
                        };
                        document.Add(title);

                        // Adding Company, Branch, and Date Details
                        document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));

                        for (int j = 0; j < Obj.Options.Columns.Count; j++)
                        {
                            for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                            {
                                document.Add(new Paragraph(Obj.Options.Columns[j].ColumnName.ToString() + ": " + Obj.Options.Rows[k].ItemArray[j].ToString(), FontFactory.GetFont("Arial", 12)));
                            }
                        }
                        for (int k = 0; k < Obj.selection.Tables.Count; k++)
                        {
                            DataTable Local = Obj.selection.Tables[k];

                            // Get the column name from the first column
                            string columnName = Local.Columns[0].ColumnName.ToString();

                            // Create a string builder for appending values
                            StringBuilder paragraphText = new StringBuilder();
                            paragraphText.Append(columnName + ": ");

                            // Append all the values from the first column
                            for (int i = 0; i < Local.Rows.Count; i++)
                            {
                                paragraphText.Append(Local.Rows[i][0].ToString() + " ");
                            }

                            // Add the constructed text as a new paragraph
                            document.Add(new Paragraph(paragraphText.ToString(), FontFactory.GetFont("Arial", 12)));
                        }
                        // Adding space before the options table
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Options Table setup
                        PdfPTable optionsTable = new PdfPTable(2)
                        {
                            WidthPercentage = 100,    // Full page width
                            SpacingBefore = 5f       // Space before the table
                        };
                        document.Add(optionsTable);

                        // Adding space before the data table
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Data Table setup
                        PdfPTable dataTable = new PdfPTable(colCount)
                        {
                            WidthPercentage = 100,    // Full page width
                            SpacingBefore = 5f       // Space before the table
                        };

                        // Adding column headers with a gray background
                        foreach (DataColumn column in Obj.dt.Columns)
                        {
                            dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                            {
                                BackgroundColor = BaseColor.LIGHT_GRAY,
                                HorizontalAlignment = Element.ALIGN_CENTER
                            });
                        }

                        // Adding data rows to the table
                        foreach (DataRow row in Obj.dt.Rows)
                        {
                            foreach (var cell in row.ItemArray)
                            {
                                string value = cell.ToString();
                                PdfPCell pdfCell = new PdfPCell(new Phrase(value))
                                {
                                    HorizontalAlignment = Element.ALIGN_LEFT   // Default alignment
                                };

                                // If alignment logic is needed, implement here

                                dataTable.AddCell(pdfCell);
                            }
                        }
                        document.Add(dataTable);

                        // Close the document
                        document.Close();

                        // Convert the memory stream to a byte array and return as JSON result
                        byte[] fileBytes = ms.ToArray();
                        string base64String = Convert.ToBase64String(fileBytes);

                        return new JsonResult(new
                        {
                            FileName = Obj.FileName,
                            FileContent = base64String,
                            ContentType = "application/pdf"
                        });
                    }
                }
            }
            catch (Exception ex) { }
            return null;
        }
        #endregion
    }


    public static class ReportExport1
    {


        #region :::ReportExport1   Uday Kumar J B 27-11-2024 :::
        /// <summary>
        /// To Get ReportExportCR5 
        /// </summary>
        /// 
        public static async Task<JsonResult> Export(ExportReportExport1List Obj, string connString, int LogException)
        {
            string cName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyName").ToString();
            string BName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BranchName").ToString();
            string bid = Obj.Branch == null ? "0" : Obj.Branch.ToString();
            int BranchID = Convert.ToInt32(bid);
            string companyName = "";

            string BranchName = "";


            SqlCommand command = null;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Export_AM_ERP_Export_Common";



                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                        command.Parameters.AddWithValue("@BranchID", BranchID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyName = reader["Company_Name"] as string;
                                BranchName = reader["Branch_Name"] as string;

                            }


                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            //Logo added fot Excell export
            string ReportImagePath = "";
            if (System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath") != null)
                ReportImagePath = System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath").ToString();
            try
            {
                int colCount = Obj.dt.Columns.Count;
                if (Obj.exprtType == 1)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.Append("<table>");
                    if (ReportImagePath != "")
                        sb.Append("</tr><td <img src='" + ReportImagePath + "'></img></td><tr></tr>");
                    sb.Append("<tr></tr>");
                    sb.Append("<tr><td colspan='" + colCount + "' style='text-align:center'>");
                    sb.Append("<B>" + Obj.Header + "</B>");
                    sb.Append("</td></tr>");
                    sb.Append("<tr></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr><tr></tr></table>");
                    sb.Append("<table>");
                    for (int j = 0; j < Obj.Options.Columns.Count; j++)
                    {
                        sb.Append("<tr>");
                        for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                        {
                            sb.Append("<td style='text-align:right'>");
                            sb.Append("<B>" + Obj.Options.Columns[j].ColumnName.ToString() + "</B> :</td><td style='text-align:left'> " + Obj.Options.Rows[k].ItemArray[j].ToString());
                            sb.Append("</td>");
                        }
                        sb.Append("</tr>");
                    }
                    sb.Append("<tr></tr>");
                    sb.Append("</table>");
                    sb.Append("<table>");
                    for (int k = 0; k < Obj.selection.Tables.Count; k++)
                    {
                        DataTable Local = Obj.selection.Tables[k];
                        sb.Append("<tr>");
                        sb.Append("<td style='text-align:right'><b>" + Local.Columns[0].ColumnName.ToString() + "</b> :</td>");
                        sb.Append("<td colspan='" + (colCount - 1) + "'>");
                        for (int i = 0; i < Local.Rows.Count; i++)
                        {
                            sb.Append(Local.Rows[i].ItemArray[0].ToString());
                        }
                        sb.Append("</td>");
                        sb.Append("</tr>");
                    }
                    sb.Append("<tr></tr>");
                    sb.Append("</table>");
                    sb.Append("<table border='1'>");
                    sb.Append("<thead><tr>");
                    for (int i = 0; i < colCount; i++)
                    {
                        sb.Append("<td style='text-align: center;background-color:gray;'>");
                        sb.Append("<b>" + Obj.dt.Columns[i].ColumnName.ToString() + "</b>");
                        sb.Append("</td>");
                    }
                    sb.Append("</tr></thead>");
                    for (int j = 0; j < Obj.dt.Rows.Count; j++)
                    {
                        sb.Append("<tr>");
                        for (int k = 0; k < colCount; k++)
                        {
                            string align = (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "0" ? "left" : (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "1" ? "center" : "right"));
                            sb.Append("<td  'style=text-align:'" + align + "';'>");
                            if (Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "partnumberr").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QtyIssuedReturned").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SAPInvoiceNumber").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerAccountNumber").ToString()))
                            {
                                string Value = "=concatenate(\"" + Obj.dt.Rows[j].ItemArray[k].ToString() + "\")";
                                sb.Append(Value);
                            }
                            else
                            {
                                sb.Append(Obj.dt.Rows[j].ItemArray[k].ToString());
                            }
                            sb.Append("</td>");
                        }
                        sb.Append("</tr>");
                    }
                    sb.Append("</table>");
                    byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                    string base64String = Convert.ToBase64String(fileBytes);


                    return new JsonResult(new
                    {
                        FileName = Obj.FileName,
                        FileContent = base64String,
                        ContentType = "application/vnd.ms-excel"
                    });
                }
                else if (Obj.exprtType == 2)
                {
                    Obj.FileName = Obj.FileName + ".pdf";
                    using (MemoryStream ms = new MemoryStream())
                    {
                        Document document = new Document(PageSize.A3);
                        PdfWriter writer = PdfWriter.GetInstance(document, ms);
                        document.Open();

                        // Header Title
                        Paragraph title = new Paragraph(Obj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                        {
                            Alignment = Element.ALIGN_CENTER
                        };
                        document.Add(title);

                        // Adding Company, Branch, and Date Details
                        document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                        document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));

                        for (int j = 0; j < Obj.Options.Columns.Count; j++)
                        {
                            for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                            {
                                document.Add(new Paragraph(Obj.Options.Columns[j].ColumnName.ToString() + ": " + Obj.Options.Rows[k].ItemArray[j].ToString(), FontFactory.GetFont("Arial", 12)));
                            }
                        }
                        for (int k = 0; k < Obj.selection.Tables.Count; k++)
                        {
                            DataTable Local = Obj.selection.Tables[k];

                            // Get the column name from the first column
                            string columnName = Local.Columns[0].ColumnName.ToString();

                            // Create a string builder for appending values
                            StringBuilder paragraphText = new StringBuilder();
                            paragraphText.Append(columnName + ": ");

                            // Append all the values from the first column
                            for (int i = 0; i < Local.Rows.Count; i++)
                            {
                                paragraphText.Append(Local.Rows[i][0].ToString() + " ");
                            }

                            // Add the constructed text as a new paragraph
                            document.Add(new Paragraph(paragraphText.ToString(), FontFactory.GetFont("Arial", 12)));
                        }
                        // Adding space before the options table
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Options Table setup
                        PdfPTable optionsTable = new PdfPTable(2)
                        {
                            WidthPercentage = 100,    // Full page width
                            SpacingBefore = 5f       // Space before the table
                        };
                        document.Add(optionsTable);

                        // Adding space before the data table
                        document.Add(new Paragraph(" "));
                        document.Add(new Paragraph(" "));

                        // Data Table setup
                        PdfPTable dataTable = new PdfPTable(colCount)
                        {
                            WidthPercentage = 100,    // Full page width
                            SpacingBefore = 5f       // Space before the table
                        };

                        // Adding column headers with a gray background
                        foreach (DataColumn column in Obj.dt.Columns)
                        {
                            dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                            {
                                BackgroundColor = BaseColor.LIGHT_GRAY,
                                HorizontalAlignment = Element.ALIGN_CENTER
                            });
                        }

                        // Adding data rows to the table
                        foreach (DataRow row in Obj.dt.Rows)
                        {
                            foreach (var cell in row.ItemArray)
                            {
                                string value = cell.ToString();
                                PdfPCell pdfCell = new PdfPCell(new Phrase(value))
                                {
                                    HorizontalAlignment = Element.ALIGN_LEFT   // Default alignment
                                };

                                // If alignment logic is needed, implement here

                                dataTable.AddCell(pdfCell);
                            }
                        }
                        document.Add(dataTable);

                        // Close the document
                        document.Close();

                        // Convert the memory stream to a byte array and return as JSON result
                        byte[] fileBytes = ms.ToArray();
                        string base64String = Convert.ToBase64String(fileBytes);

                        return new JsonResult(new
                        {
                            FileName = Obj.FileName,
                            FileContent = base64String,
                            ContentType = "application/pdf"
                        });
                    }
                }
            }
            catch (Exception ex) { }

            return null;

        }
        #endregion

    }
}
