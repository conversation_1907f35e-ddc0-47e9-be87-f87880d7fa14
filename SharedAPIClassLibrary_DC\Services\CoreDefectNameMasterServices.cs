﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreDefectNameMasterServices
    {

        #region ::: Export Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// Exporting 
        /// </summary>
        public static async Task<object> Export(ExportCoreDefectNameMasterList ExportCoreDefectNameMasterobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            List<DefectObjects> defectDataList = new List<DefectObjects>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Fetch data from database using stored procedure
                int CompanyID = Convert.ToInt32(ExportCoreDefectNameMasterobj.Company_ID);
                string YesE = CommonFunctionalities.GetResourceString(ExportCoreDefectNameMasterobj.GeneralCulture.ToString(), "Yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(ExportCoreDefectNameMasterobj.GeneralCulture.ToString(), "No").ToString();
                string YesL = CommonFunctionalities.GetResourceString(ExportCoreDefectNameMasterobj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(ExportCoreDefectNameMasterobj.UserCulture.ToString(), "No").ToString();

                List<DefectName> defectNames = new List<DefectName>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    string storedProcedure = ExportCoreDefectNameMasterobj.LanguageID == Convert.ToInt32(ExportCoreDefectNameMasterobj.GeneralLanguageID)
                        ? "sp_SelectDefectNameGeneralLanguage"
                        : "sp_SelectDefectNameLocaleLanguage";

                    using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@DefectGroupID", ExportCoreDefectNameMasterobj.DefectGroupID);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        if (storedProcedure == "sp_SelectDefectNameLocaleLanguage")
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", ExportCoreDefectNameMasterobj.LanguageID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                defectNames.Add(new DefectName
                                {
                                    DefectName_ID = reader.GetInt32(reader.GetOrdinal("DefectName_ID")),
                                    DefectName_Code = reader.GetString(reader.GetOrdinal("DefectName_Code")),
                                    DefectName_Description = reader.GetString(reader.GetOrdinal("DefectName_Description")),
                                    DefectName_IsActive = reader.GetBoolean(reader.GetOrdinal("DefectName_IsActive")) ? (storedProcedure == "sp_SelectDefectNameGeneralLanguage" ? YesE : YesL) : (storedProcedure == "sp_SelectDefectNameGeneralLanguage" ? NoE : NoL)
                                });
                            }
                        }
                    }
                }

                // Sorting and filtering using LINQ-to-Objects
                IQueryable<DefectName> IQDefectName = defectNames.AsQueryable();

                // Apply standard filters if present
                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        IQDefectName = IQDefectName.FilterSearch(filtersObj);
                }


                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    IQDefectName = IQDefectName.AdvanceSearch(advnfilter);
                }

                // Convert filtered data to DataTable
                DataTable dt = new DataTable();
                dt.Columns.Add("DefectID");
                dt.Columns.Add("DefectName");
                dt.Columns.Add("DefectCode");
                dt.Columns.Add("DefectIsActive");

                foreach (var defect in IQDefectName)
                {
                    DataRow row = dt.NewRow();
                    row["DefectID"] = defect.DefectName_ID;
                    row["DefectName"] = defect.DefectName_Description;
                    row["DefectCode"] = defect.DefectName_Code;
                    row["DefectIsActive"] = defect.DefectName_IsActive;
                    dt.Rows.Add(row);
                }

                // Define dt1 as per your original logic (if needed)
                DataTable dt1 = new DataTable();
                dt1.Columns.Add("DefectID");
                dt1.Columns.Add("DefectName");
                dt1.Columns.Add("DefectCode");
                dt1.Columns.Add("DefectIsActive");
                dt1.Rows.Add("0", "0", "1");
                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportCoreDefectNameMasterobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportCoreDefectNameMasterobj.Branch_ID,
                    dt1 = dt1,


                    dt = dt,

                    FileName = "Defect Name", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportCoreDefectNameMasterobj.UserCulture.ToString(), "DefectName").ToString(), // Set a default or dynamic header
                    exprtType = ExportCoreDefectNameMasterobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportCoreDefectNameMasterobj.UserCulture
                };

                // Call the Export method
                //return  DocumentExport.Export(ExportCoreDefectNameMasterobj.exprtType, dt, dt1, "Defects", "Defects");
                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


        #region ::: CheckDefectNameLocale Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// CheckDefectNameLocale 
        /// </summary>
        /// 
        public static IActionResult CheckDefectNameLocale(string connString, CheckDefectNameLocaleList CheckDefectNameLocaleobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string DefectNameDesc = Common.DecryptString(CheckDefectNameLocaleobj.DefectName);
                string Code = Common.DecryptString(CheckDefectNameLocaleobj.DefectName_Code);
                int Language_ID = Convert.ToInt32(CheckDefectNameLocaleobj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Check by DefectName_Code
                    using (SqlCommand cmd = new SqlCommand("sp_CheckDefectNameLocaleByCode", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@DefectGroupID", CheckDefectNameLocaleobj.DefectGroupID);
                        cmd.Parameters.AddWithValue("@DefectName_Code", Code);
                        cmd.Parameters.AddWithValue("@DefectNameLocaleID", CheckDefectNameLocaleobj.DefectNameLocaleID);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                        int result = Convert.ToInt32(cmd.ExecuteScalar());
                        if (result > 0)
                        {
                            Count = 1;
                        }
                    }

                    // Check by DefectName_Description
                    if (Count == 0)
                    {
                        using (SqlCommand cmd = new SqlCommand("sp_CheckDefectNameLocaleByName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@DefectGroupID", CheckDefectNameLocaleobj.DefectGroupID);
                            cmd.Parameters.AddWithValue("@DefectName_Description", DefectNameDesc);
                            cmd.Parameters.AddWithValue("@DefectNameLocaleID", CheckDefectNameLocaleobj.DefectNameLocaleID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                            int result = Convert.ToInt32(cmd.ExecuteScalar());
                            if (result > 0)
                            {
                                Count = 2;
                            }
                        }
                    }

                    // Check by DefectName_Code and DefectName_Description
                    if (Count == 0)
                    {
                        using (SqlCommand cmd = new SqlCommand("sp_CheckDefectNameLocaleByCodeAndName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@DefectGroupID", CheckDefectNameLocaleobj.DefectGroupID);
                            cmd.Parameters.AddWithValue("@DefectName_Code", Code);
                            cmd.Parameters.AddWithValue("@DefectName_Description", DefectNameDesc);
                            cmd.Parameters.AddWithValue("@DefectNameLocaleID", CheckDefectNameLocaleobj.DefectNameLocaleID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                            int result = Convert.ToInt32(cmd.ExecuteScalar());
                            if (result > 0)
                            {
                                Count = 3;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: CheckDefectName Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Check DefectName already exists 
        /// </summary>//Val
        /// 
        public static IActionResult CheckDefectName(string connString, CheckDefectNameList CheckDefectNameobj)
        {
            int Count = 0;
            string Val = CheckDefectNameobj.Val.Trim();
            int CompanyID = Convert.ToInt32(CheckDefectNameobj.Company_ID);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_CheckDefectName", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@Val", Val);
                    command.Parameters.AddWithValue("@DefectGroupID", CheckDefectNameobj.DefectGroupID);
                    command.Parameters.AddWithValue("@ColumnName", CheckDefectNameobj.ColumnName);
                    command.Parameters.AddWithValue("@DefectNameID", CheckDefectNameobj.DefectNameID);
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);

                    SqlParameter countParam = new SqlParameter("@Count", SqlDbType.Int);
                    countParam.Direction = ParameterDirection.Output;
                    command.Parameters.Add(countParam);

                    command.ExecuteNonQuery();

                    Count = Convert.ToInt32(countParam.Value);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
            return new JsonResult(Count);
        }

        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        /// 
        public static IActionResult SelectReferenceMaster(string connString, SelectReferenceMasterCoreDefectNameMasterList SelectReferenceMasterCoreDefectNameMasterobj)
        {
            var Masterdata = default(dynamic);
            int CompanyID = Convert.ToInt32(SelectReferenceMasterCoreDefectNameMasterobj.Company_ID);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    string spName = "";
                    if (SelectReferenceMasterCoreDefectNameMasterobj.LanguageID == Convert.ToInt32(SelectReferenceMasterCoreDefectNameMasterobj.GeneralLanguageID))
                    {
                        spName = "Up_Upd_AM_ERP_SelectReferenceMaster_GeneralLanguageID";
                    }
                    else
                    {
                        spName = "Up_Upd_AM_ERP_SelectReferenceMaster_OtherLanguageID";
                    }

                    SqlCommand command = new SqlCommand(spName, connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@IssueAreaID", SelectReferenceMasterCoreDefectNameMasterobj.IssueArea_ID);
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);
                    if (SelectReferenceMasterCoreDefectNameMasterobj.LanguageID != Convert.ToInt32(SelectReferenceMasterCoreDefectNameMasterobj.GeneralLanguageID))
                    {
                        command.Parameters.AddWithValue("@LanguageID", SelectReferenceMasterCoreDefectNameMasterobj.LanguageID);
                    }

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        List<object> data = new List<object>();
                        while (reader.Read())
                        {
                            data.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }

                        Masterdata = new
                        {
                            DefectGroupMasterData = data
                        };
                    }
                }

                return new JsonResult(Masterdata);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }

        #endregion


        #region ::: Select Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Select DefectName for a DefectGroup
        /// </summary>
        public static IActionResult Select(string connString, SelectCoreDefectNameMasterList SelectCoreDefectNameMasterobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filter, bool advnce, string advnceFilter)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string AppPath = string.Empty;
            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectCoreDefectNameMasterobj.Company_ID);
                string YesE = CommonFunctionalities.GetResourceString(SelectCoreDefectNameMasterobj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectCoreDefectNameMasterobj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(SelectCoreDefectNameMasterobj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectCoreDefectNameMasterobj.UserCulture.ToString(), "no").ToString();
                var jsonData = default(dynamic);

                List<DefectName> defectNames = new List<DefectName>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    string storedProcedure = SelectCoreDefectNameMasterobj.LanguageID == Convert.ToInt32(SelectCoreDefectNameMasterobj.GeneralLanguageID)
                        ? "sp_SelectDefectNameGeneralLanguage"
                        : "sp_SelectDefectNameLocaleLanguage";

                    using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@DefectGroupID", SelectCoreDefectNameMasterobj.DefectGroupID);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        if (storedProcedure == "sp_SelectDefectNameLocaleLanguage")
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", SelectCoreDefectNameMasterobj.LanguageID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                defectNames.Add(new DefectName
                                {
                                    DefectName_ID = reader.GetInt32(reader.GetOrdinal("DefectName_ID")),
                                    DefectName_Code = reader.GetString(reader.GetOrdinal("DefectName_Code")),
                                    DefectName_Description = reader.GetString(reader.GetOrdinal("DefectName_Description")),
                                    DefectName_IsActive = reader.GetBoolean(reader.GetOrdinal("DefectName_IsActive")) ? (storedProcedure == "sp_SelectDefectNameGeneralLanguage" ? YesE : YesL) : (storedProcedure == "sp_SelectDefectNameGeneralLanguage" ? NoE : NoL)
                                });
                            }
                        }
                    }
                }

                // Sorting and filtering using LINQ-to-Objects
                IQueryable<DefectName> IQDefectName = defectNames.AsQueryable();

                if (_search)
                {
                    Filters filterObject = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filter))).ToObject<Filters>();
                    if (filterObject.rules.Count() > 0)
                        IQDefectName = IQDefectName.FilterSearch<DefectName>(filterObject);
                }
                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilter)).ToObject<AdvanceFilter>();
                    IQDefectName = IQDefectName.AdvanceSearch<DefectName>(advnfilter);
                }

                IQDefectName = IQDefectName.OrderByField<DefectName>(sidx, sord);

                Count = IQDefectName.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQDefectName.AsEnumerable()
                            select new
                            {
                                ID = a.DefectName_ID,
                                edit = "<a title='View' href='#' id='" + a.DefectName_ID + "' key='" + a.DefectName_ID + "' class='DefectNameEdit font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.DefectName_ID + "' defaultchecked=''  id='chk" + a.DefectName_ID + "' class='DefectNameDelete'/>",
                                DefectName_Code = a.DefectName_Code,
                                DefectName_Description = a.DefectName_Description,
                                DefectName_IsActive = a.DefectName_IsActive,
                                Locale = "<a key='" + a.DefectName_ID + "' src='" + AppPath + "/Content/local.png' class='DefectNameLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>",
                                View = "<img id='" + a.DefectName_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.DefectName_ID + "' class='ViewDefectNameLocale'/>",
                            }).ToList().Paginate(page, rows),
                    records = Count
                };

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: Delete Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Delete Defect Name
        /// </summary>
        /// 
        public static IActionResult Delete(string connString, DeleteCoreDefectNameMasterList DeleteCoreDefectNameMasterobj)
        {
            string ErrorMsg = string.Empty;
            JObject jObj = null;
            JTokenReader jTR = null;
            int Count = 0;
            int ID = 0;
            DateTime LoginDatetime = Convert.ToDateTime(DeleteCoreDefectNameMasterobj.LoggedINDateTime);
            try
            {
                jObj = JObject.Parse(DeleteCoreDefectNameMasterobj.key);
                Count = jObj["rows"].Count();
                int CompanyID = Convert.ToInt32(DeleteCoreDefectNameMasterobj.Company_ID);
                string defectname = string.Empty;
                ID = 0;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                        jTR.Read();
                        ID = Convert.ToInt32(jTR.Value);

                        // Execute the stored procedure to delete the defect name
                        using (SqlCommand command = new SqlCommand("usp_DeleteDefectName", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@DefectNameID", ID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.ExecuteNonQuery();
                        }

                        // Execute the stored procedure to delete the locale
                        using (SqlCommand command = new SqlCommand("usp_DeleteDefectNameLocale", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@DefectNameID", ID);
                            command.ExecuteNonQuery();
                        }

                        // Execute the stored procedure to get the defect name code
                        using (SqlCommand command = new SqlCommand("usp_SelectDefectNameCode", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@DefectNameID", ID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            defectname = (string)command.ExecuteScalar();
                        }

                        // Log the deletion
                        string logMessage = "Deleted " + defectname;
                        // gbl.InsertGPSDetails(Convert.ToInt32(DeleteCoreDefectNameMasterobj.Company_ID), Convert.ToInt32(DeleteCoreDefectNameMasterobj.Branch), Convert.ToInt32(DeleteCoreDefectNameMasterobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreDefectNameMaster")), ID, 0, 0, logMessage, false, Convert.ToInt32(DeleteCoreDefectNameMasterobj.MenuID), LoginDatetime, null);
                    }
                }

                ErrorMsg += CommonFunctionalities.GetResourceString(DeleteCoreDefectNameMasterobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException?.InnerException?.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint") == true)
                {
                    ErrorMsg += CommonFunctionalities.GetResourceString(DeleteCoreDefectNameMasterobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(ErrorMsg);
        }
        #endregion


        #region ::: Save Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Insert and Update Defect Name
        /// </summary>
        /// 
        public static IActionResult Save(string connString, SaveCoreDefectNameMasterList SaveCoreDefectNameMasterobj)
        {
            string ErrorMsg = string.Empty;
            JObject jObj = null;
            JTokenReader jTR = null;
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                ErrorMsg = string.Empty;
                //GNM_User UserDetails = SaveCoreDefectNameMasterobj.UserDetails.FirstOrDefault();
                jObj = JObject.Parse(SaveCoreDefectNameMasterobj.data);
                int CompanyID = Convert.ToInt32(SaveCoreDefectNameMasterobj.Company_ID);
                DateTime LoginDatetime = Convert.ToDateTime(SaveCoreDefectNameMasterobj.LoggedINDateTime);
                Count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["DefectGroup_ID"]);
                        jTR.Read();
                        int DefectGroup_ID = Convert.ToInt32(jTR.Value.ToString());

                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["DefectName_ID"]);
                        jTR.Read();
                        int DefectName_ID = Convert.ToInt32(jTR.Value.ToString() == "" ? "0" : jTR.Value.ToString());

                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["DefectName_Code"]);
                        jTR.Read();
                        string DefectName_Code = Common.DecryptString(jTR.Value.ToString());

                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["DefectName_Description"]);
                        jTR.Read();
                        string DefectName_Description = Uri.UnescapeDataString(Common.DecryptString(jTR.Value.ToString()));

                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["DefectName_IsActive"]);
                        jTR.Read();
                        bool DefectName_IsActive = Convert.ToBoolean(jTR.Value.ToString());

                        // Call the stored procedure
                        using (SqlCommand command = new SqlCommand("dbo.Up_Ins_AM_ERP_sp_SaveDefectName", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@DefectNameID", DefectName_ID);
                            command.Parameters.AddWithValue("@DefectGroupID", DefectGroup_ID);
                            command.Parameters.AddWithValue("@DefectNameCode", DefectName_Code);
                            command.Parameters.AddWithValue("@DefectNameDescription", DefectName_Description);
                            command.Parameters.AddWithValue("@DefectNameIsActive", DefectName_IsActive);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);

                            SqlParameter newDefectNameIDParam = new SqlParameter("@NewDefectNameID", SqlDbType.Int);
                            newDefectNameIDParam.Direction = ParameterDirection.Output;
                            command.Parameters.Add(newDefectNameIDParam);

                            command.ExecuteNonQuery();

                            int newDefectNameID = (int)newDefectNameIDParam.Value;

                            // Log the action
                            string action = DefectName_ID > 0 ? "Updated" : "Inserted";
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(SaveCoreDefectNameMasterobj.Company_ID),
                            //    Convert.ToInt32(SaveCoreDefectNameMasterobj.Branch),
                            //    Convert.ToInt32(SaveCoreDefectNameMasterobj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreDefectNameMaster")),
                            //    newDefectNameID,
                            //    0,
                            //    0,
                            //    $"{action} {DefectName_Code}",
                            //    false,
                            //    Convert.ToInt32(SaveCoreDefectNameMasterobj.MenuID),
                            //    LoginDatetime,
                            //    null
                            //);
                        }
                    }
                }

                ErrorMsg = "Saved";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                ErrorMsg = string.Empty;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                ErrorMsg = string.Empty;
            }
            return new JsonResult(ErrorMsg);
        }

        #endregion


        #region ::: SelectParticularDefectName Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Select Particular Defect Name
        /// </summary>
        /// 
        public static IActionResult SelectParticularDefectName(string connString, SelectParticularDefectNameList SelectParticularDefectNameobj)
        {
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_SelectParticularDefectName", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@DefectNameID", SelectParticularDefectNameobj.DefectNameID);
                        command.Parameters.AddWithValue("@LanguageID", SelectParticularDefectNameobj.LanguageID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                x = new
                                {
                                    DefectName_ID = reader["DefectName_ID"],
                                    DefectName_Code = reader["DefectName_Code"],
                                    DefectName_Description = reader["DefectName_Description"],
                                    DefectName_IsActive = reader["DefectName_IsActive"],
                                    DefectGroup_ID = reader["DefectGroup_ID"],
                                    DefectNameLocale_ID = reader["DefectNameLocale_ID"],
                                    DefectNameLocale_Code = reader["DefectNameLocale_Code"],
                                    DefectNameLocale_Description = reader["DefectNameLocale_Description"]
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
            return new JsonResult(x);
        }


        #endregion


        #region ::: UpdateLocale Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// 
        public static IActionResult UpdateLocale(string connString, UpdateLocaleCoreDefectNameMasterList UpdateLocaleCoreDefectNameMasterobj)
        {
            int DefectNameLocaleID = 0;
            var x = default(dynamic);
            JObject jObj = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                HD_DefectNameLocale DLRow = null;
                HD_DefectNameLocale AddDlRow = null;
                jObj = JObject.Parse(UpdateLocaleCoreDefectNameMasterobj.data);

                DLRow = jObj.ToObject<HD_DefectNameLocale>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (DLRow.DefectNameLocale_ID != 0)
                    {
                        // Update existing DefectNameLocale
                        using (SqlCommand cmd = new SqlCommand("sp_UpdateDefectNameLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@DefectNameLocale_ID", DLRow.DefectNameLocale_ID);
                            cmd.Parameters.AddWithValue("@DefectName_Code", Common.DecryptString(DLRow.DefectName_Code));
                            cmd.Parameters.AddWithValue("@DefectName_Description", Common.DecryptString(DLRow.DefectName_Description));

                            cmd.ExecuteNonQuery();
                        }

                        DefectNameLocaleID = DLRow.DefectNameLocale_ID;

                        // Insert GPS Details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.Company_ID),
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.Branch),
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreDefectNameMaster")),
                        //    DLRow.DefectName_ID, 0, 0, "Update", false,
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.MenuID)
                        //);
                    }
                    else
                    {
                        // Insert new DefectNameLocale
                        using (SqlCommand cmd = new SqlCommand("sp_InsertDefectNameLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@DefectName_Code", Common.DecryptString(DLRow.DefectName_Code));
                            cmd.Parameters.AddWithValue("@DefectName_Description", Common.DecryptString(DLRow.DefectName_Description));
                            cmd.Parameters.AddWithValue("@DefectName_ID", DLRow.DefectName_ID);
                            cmd.Parameters.AddWithValue("@LanguageID", DLRow.Language_ID);
                            SqlParameter outputParam = new SqlParameter("@DefectNameLocale_ID", SqlDbType.Int)
                            {
                                Direction = ParameterDirection.Output
                            };
                            cmd.Parameters.Add(outputParam);

                            cmd.ExecuteNonQuery();
                            DefectNameLocaleID = (int)outputParam.Value;
                        }

                        // Insert GPS Details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.Company_ID),
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.Branch),
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreDefectNameMaster")),
                        //    DLRow.DefectName_ID, 0, 0, "Insert", false,
                        //    Convert.ToInt32(UpdateLocaleCoreDefectNameMasterobj.MenuID)
                        //);
                    }

                    x = new
                    {
                        DefectNameLocaleID = DefectNameLocaleID
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }

        #endregion


        #region ::: CoreDefectNameMaster List and obj classes Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// 
        public class UpdateLocaleCoreDefectNameMasterList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class SelectParticularDefectNameList
        {
            public int DefectNameID { get; set; }
            public int LanguageID { get; set; }
        }
        public class SaveCoreDefectNameMasterList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string data { get; set; }
            public int Company_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class DeleteCoreDefectNameMasterList
        {
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public string UserCulture { get; set; }
        }
        public class SelectCoreDefectNameMasterList
        {
            public int Company_ID { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int DefectGroupID { get; set; }
        }

        public class SelectReferenceMasterCoreDefectNameMasterList
        {
            public int LanguageID { get; set; }
            public int IssueArea_ID { get; set; }
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
        }

        public class CheckDefectNameList
        {
            public string Val { get; set; }
            public int DefectGroupID { get; set; }
            public string ColumnName { get; set; }
            public int DefectNameID { get; set; }
            public int Company_ID { get; set; }
        }
        public class CheckDefectNameLocaleList
        {
            public int DefectGroupID { get; set; }
            public string DefectName_Code { get; set; }
            public int DefectNameLocaleID { get; set; }
            public string DefectName { get; set; }
            public string UserLanguageID { get; set; }
        }
        public class ExportCoreDefectNameMasterList
        {
            public int exprtType { get; set; }

            public int DefectGroupID { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public string GeneralCulture { get; set; }
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
        }
        #endregion


        #region :::CoreDefectNameMaster classes Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// CoreDefectNameMaster Classes 
        /// </summary>
        /// 
        public partial class HD_DefectNameLocale
        {
            public int DefectNameLocale_ID { get; set; }
            public int DefectName_ID { get; set; }
            public string DefectName_Code { get; set; }
            public string DefectName_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual HD_DefectName HD_DefectName { get; set; }
        }
        public partial class HD_DefectName
        {
            public HD_DefectName()
            {
                this.HD_DefectNameLocale = new HashSet<HD_DefectNameLocale>();
            }

            public int DefectName_ID { get; set; }
            public int DefectGroup_ID { get; set; }
            public string DefectName_Code { get; set; }
            public string DefectName_Description { get; set; }
            public Nullable<bool> DefectName_IsActive { get; set; }
            public int Company_ID { get; set; }

            public virtual ICollection<HD_DefectNameLocale> HD_DefectNameLocale { get; set; }
        }
        public class DefectName
        {
            public int DefectName_ID
            {
                get;
                set;
            }

            public int DefectGroup_ID
            {
                get;
                set;
            }

            public string DefectName_Code
            {
                get;
                set;
            }

            public string DefectName_Description
            {
                get;
                set;
            }

            public string DefectName_IsActive
            {
                get;
                set;
            }
        }

        public class DefectObjects
        {
            public int Defect_ID { get; set; }
            public string Defect_Name { get; set; }
            public string Defect_Code { get; set; }
            public bool Defect_IsActive { get; set; }
        }
        #endregion

    }
}
