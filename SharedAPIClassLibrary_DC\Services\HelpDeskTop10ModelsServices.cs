﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskTop10ModelsServices
    {


        #region ::: SelectTopModel Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Top Model
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectTopModel(HelpDeskTop10ModelsSelectTopModelList HelpDeskTop10ModelsSelectTopModelobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = new object();
            int count = 0;
            int total = 0;

            try
            {
                string frmdate = HelpDeskTop10ModelsSelectTopModelobj.FromDate;
                string todate = HelpDeskTop10ModelsSelectTopModelobj.ToDate;
                string CompanyIDs = HelpDeskTop10ModelsSelectTopModelobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDeskTop10ModelsSelectTopModelobj.BranchIDs.TrimEnd(new char[] { ',' });
                int UserLangID = Convert.ToInt32(HelpDeskTop10ModelsSelectTopModelobj.UserLanguageID);
                int GeneralLangID = Convert.ToInt32(HelpDeskTop10ModelsSelectTopModelobj.GeneralLanguageID);

                DateTime? fromDate = string.IsNullOrEmpty(frmdate) ? (DateTime?)null : Convert.ToDateTime(frmdate);
                DateTime? toDate = string.IsNullOrEmpty(todate) ? (DateTime?)null : Convert.ToDateTime(todate).AddDays(1);

                // Temporary lists for data
                List<TopModelObjects> prtyProdDtlList = new List<TopModelObjects>();
                List<TopModelObjects> prtyProdDtlListRpt = new List<TopModelObjects>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch Service Requests using ADO.NET
                    DataTable dtServiceRequests = new DataTable();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestsTop10Model", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        cmd.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);

                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            adapter.Fill(dtServiceRequests);
                        }
                    }

                    // Fetch Models
                    DataTable dtModels = new DataTable();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelsTop10Model", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLangID);

                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            adapter.Fill(dtModels);
                        }
                    }

                    // Process Service Requests
                    foreach (DataRow srvc in dtServiceRequests.Rows)
                    {
                        string companyName = string.Empty;
                        string branchName = string.Empty;

                        // Fetch company and branch names using SP
                        using (SqlDataAdapter adapter = new SqlDataAdapter("SP_AMERP_HelpDesk_GetCompanyAndBranchNames", conn))
                        {
                            adapter.SelectCommand.CommandType = CommandType.StoredProcedure;
                            adapter.SelectCommand.Parameters.AddWithValue("@CompanyID", srvc.Field<int>("Company_ID"));
                            adapter.SelectCommand.Parameters.AddWithValue("@BranchID", srvc.Field<int>("Branch_ID"));

                            DataTable dts = new DataTable();
                            adapter.Fill(dts);

                            if (dts.Rows.Count > 0)
                            {
                                companyName = dts.Rows[0]["Company_Name"] != DBNull.Value ? dts.Rows[0]["Company_Name"].ToString() : string.Empty;
                                branchName = dts.Rows[0]["Branch_Name"] != DBNull.Value ? dts.Rows[0]["Branch_Name"].ToString() : string.Empty;
                            }
                        }

                        // Add to the primary list
                        prtyProdDtlListRpt.Add(new TopModelObjects
                        {
                            Model = dtModels.AsEnumerable()
                                            .FirstOrDefault(m => m.Field<int>("Model_ID") == srvc.Field<int>("Model_ID"))?
                                            .Field<string>("Model_Name"),
                            ModelID = srvc.Field<int>("Model_ID"),
                            Company_Name = companyName,
                            Branch_Name = branchName,
                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srvc.Field<int>("Branch_ID"))
                        });
                    }
                }

                // Group and calculate counts
                var groupedModels = prtyProdDtlListRpt.GroupBy(p => p.Model)
                                                      .Select(g => new TopModelObjects
                                                      {
                                                          Model = g.Key,
                                                          Count = g.Count()
                                                      })
                                                      .OrderByDescending(p => p.Count)
                                                      .Take(10)
                                                      .ToList();

                prtyProdDtlList.AddRange(groupedModels);

                // Pagination and sorting
                var sortedData = prtyProdDtlList.AsQueryable().OrderByField(sidx, sord);
                count = sortedData.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling((double)count / rows)) : 0;

                // Prepare JSON data
                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = sortedData.ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                // Log exceptions if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion




        #region ::: SelectBrandAndBranch Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Brand And Branch
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectBrandAndBranch(HelpDeskTop10ModelsSelectBrandAndBranchList HelpDeskTop10ModelsSelectBrandAndBranchobj, string connString, int LogException)
        {
            var ModelList = new List<Top_Branchs>();
            var BrandList = new List<Top_Branchs>();
            dynamic Masterdata = null;

            try
            {
                string CompanyArray = string.IsNullOrWhiteSpace(HelpDeskTop10ModelsSelectBrandAndBranchobj.CompanyArray) ? "0" : HelpDeskTop10ModelsSelectBrandAndBranchobj.CompanyArray;
                string[] CompanyIDs = CompanyArray.Split(',');

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    foreach (var companyIdStr in CompanyIDs)
                    {
                        if (int.TryParse(companyIdStr, out int CompanyID))
                        {
                            // Get employee branches
                            var gEmployeeBranch = new List<int>();
                            using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetEmployeeBranchesTop10Models", connection))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@EmployeeID", HelpDeskTop10ModelsSelectBrandAndBranchobj.Employee_ID);

                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        gEmployeeBranch.Add(reader.GetInt32(0)); // Branch_ID
                                    }
                                }
                            }

                            if (HelpDeskTop10ModelsSelectBrandAndBranchobj.LanguageID == Convert.ToInt32(HelpDeskTop10ModelsSelectBrandAndBranchobj.GeneralLanguageID))
                            {
                                // Get brands
                                using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetBrandsByCompanyID", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                                    using (var reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ModelList.Add(new Top_Branchs
                                            {
                                                ID = reader.GetInt32(0), // Brand_ID
                                                Name = reader.GetString(1) // RefMasterDetail_Name
                                            });
                                        }
                                    }
                                }

                                // Get branches
                                using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetBranchesByCompanyID", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                                    using (var reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            var branchId = reader.GetInt32(0); // Branch_ID
                                            if (gEmployeeBranch.Contains(branchId))
                                            {
                                                BrandList.Add(new Top_Branchs
                                                {
                                                    ID = branchId,
                                                    Name = reader.GetString(1) // Branch_Name
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                // Get localized brands
                                using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetLocalizedBrandsTop10Models", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                                    cmd.Parameters.AddWithValue("@LanguageID", HelpDeskTop10ModelsSelectBrandAndBranchobj.LanguageID);

                                    using (var reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ModelList.Add(new Top_Branchs
                                            {
                                                ID = reader.GetInt32(0), // Brand_ID
                                                Name = reader.GetString(1) // RefMasterDetail_Name
                                            });
                                        }
                                    }
                                }

                                // Get localized branches
                                using (var cmd = new SqlCommand("SP_AMERP_HelpDesk_GetLocalizedBranches", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                                    cmd.Parameters.AddWithValue("@LanguageID", HelpDeskTop10ModelsSelectBrandAndBranchobj.LanguageID);

                                    using (var reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            var branchId = reader.GetInt32(0); // Branch_ID
                                            if (gEmployeeBranch.Contains(branchId))
                                            {
                                                BrandList.Add(new Top_Branchs
                                                {
                                                    ID = branchId,
                                                    Name = reader.GetString(1) // Branch_Name
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                Masterdata = new
                {
                    Model = ModelList
                        .OrderBy(m => m.Name)
                        .Select(m => new { m.ID, m.Name })
                        .Distinct(),
                    Branch = BrandList
                        .OrderBy(b => b.Name)
                        .Select(b => new { b.ID, b.Name })
                        .Distinct()
                };


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion



        #region ::: Export Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Export Top Model
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static async Task<object> Export(HelpDeskTop10ModelsSelectTopModelList HelpDeskTop10ModelsSelectTopModelobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {

            string BranchName = string.Empty;
            string All = string.Empty;

            try
            {

                DataTable dtOptions = new DataTable();
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "todate").ToString());
                dtOptions.Rows.Add(HelpDeskTop10ModelsSelectTopModelobj.FromDate, HelpDeskTop10ModelsSelectTopModelobj.ToDate);
                string frmdate = HelpDeskTop10ModelsSelectTopModelobj.FromDate;
                string todate = HelpDeskTop10ModelsSelectTopModelobj.ToDate;
                string CompanyIDs = HelpDeskTop10ModelsSelectTopModelobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDeskTop10ModelsSelectTopModelobj.BranchIDs.TrimEnd(new char[] { ',' });
                int UserLangID = Convert.ToInt32(HelpDeskTop10ModelsSelectTopModelobj.UserLanguageID);
                int GeneralLangID = Convert.ToInt32(HelpDeskTop10ModelsSelectTopModelobj.GeneralLanguageID);

                DateTime? fromDate = string.IsNullOrEmpty(frmdate) ? (DateTime?)null : Convert.ToDateTime(frmdate);
                DateTime? toDate = string.IsNullOrEmpty(todate) ? (DateTime?)null : Convert.ToDateTime(todate).AddDays(1);

                // Temporary lists for data
                List<TopModelObjects> prtyProdDtlList = new List<TopModelObjects>();
                List<TopModelObjects> prtyProdDtlListRpt = new List<TopModelObjects>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch Service Requests using ADO.NET
                    DataTable dtServiceRequests = new DataTable();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestsTop10Model", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        cmd.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);

                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            adapter.Fill(dtServiceRequests);
                        }
                    }

                    // Fetch Models
                    DataTable dtModels = new DataTable();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelsTop10Model", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLangID);

                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            adapter.Fill(dtModels);
                        }
                    }

                    // Process Service Requests
                    foreach (DataRow srvc in dtServiceRequests.Rows)
                    {
                        string companyName = string.Empty;
                        string branchName = string.Empty;

                        // Fetch company and branch names using SP
                        using (SqlDataAdapter adapter = new SqlDataAdapter("SP_AMERP_HelpDesk_GetCompanyAndBranchNames", conn))
                        {
                            adapter.SelectCommand.CommandType = CommandType.StoredProcedure;
                            adapter.SelectCommand.Parameters.AddWithValue("@CompanyID", srvc.Field<int>("Company_ID"));
                            adapter.SelectCommand.Parameters.AddWithValue("@BranchID", srvc.Field<int>("Branch_ID"));

                            DataTable dts = new DataTable();
                            adapter.Fill(dts);

                            if (dts.Rows.Count > 0)
                            {
                                companyName = dts.Rows[0]["Company_Name"] != DBNull.Value ? dts.Rows[0]["Company_Name"].ToString() : string.Empty;
                                branchName = dts.Rows[0]["Branch_Name"] != DBNull.Value ? dts.Rows[0]["Branch_Name"].ToString() : string.Empty;
                            }
                        }


                        // Add to the primary list
                        prtyProdDtlListRpt.Add(new TopModelObjects
                        {
                            Model = dtModels.AsEnumerable()
                                            .FirstOrDefault(m => m.Field<int>("Model_ID") == srvc.Field<int>("Model_ID"))?
                                            .Field<string>("Model_Name"),
                            ModelID = srvc.Field<int>("Model_ID"),
                            Company_Name = companyName,
                            Branch_Name = branchName,
                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srvc.Field<int>("Branch_ID"))
                        });
                    }
                }
                DataSet ds = new DataSet();

                DataTable dtbrnds = new DataTable();
                dtbrnds.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "Brand").ToString());
                Brand brnds = JObject.Parse(Common.DecryptString(HelpDeskTop10ModelsSelectTopModelobj.Brands)).ToObject<Brand>();
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < brnds.Brands.Count(); i++)
                {
                    sb.Append(brnds.Brands.ElementAt(i).Name + ", ");
                }
                dtbrnds.Rows.Add(sb.ToString().TrimEnd(new char[] { ',', ' ' }));
                //}
                ds.Tables.Add(dtbrnds);

                List<TopModelObjects> topModel = prtyProdDtlListRpt.ToList();
                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "SlNo").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "Company").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "Branch").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "model").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "CRCount").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Sl No");
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("CompanyName");
                DtAlignment.Columns.Add("BranchName");
                DtAlignment.Columns.Add("Model");
                DtAlignment.Columns.Add("Count");
                DtAlignment.Rows.Add(2, 0, 0, 0, 0, 2);

                IQueryable<TopModelObjects> iQPrtyProdDtl = null;
                IEnumerable<TopModelObjects> prtyProdDtlRpt = null;


                prtyProdDtlRpt = from a in topModel
                                 group a by new { a.Company_Name, a.Branch_Name, a.ModelID } into e
                                 select new TopModelObjects
                                 {
                                     Region = e.FirstOrDefault().Region,
                                     Company_Name = e.FirstOrDefault().Company_Name,
                                     Branch_Name = e.FirstOrDefault().Branch_Name,
                                     Model = e.FirstOrDefault().Model,
                                     ModelID = e.FirstOrDefault().ModelID,
                                     Count = e.Count(),
                                 };
                iQPrtyProdDtl = prtyProdDtlRpt.AsQueryable<TopModelObjects>();


                for (int i = 0; i < iQPrtyProdDtl.Count(); i++)
                {
                    dt.Rows.Add(i + 1, iQPrtyProdDtl.ElementAt(i).Region, iQPrtyProdDtl.ElementAt(i).Company_Name, iQPrtyProdDtl.ElementAt(i).Branch_Name, iQPrtyProdDtl.ElementAt(i).Model, iQPrtyProdDtl.ElementAt(i).Count);
                }
                ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                {
                    FileName = "Top10Model",
                    Branch = Convert.ToString(HelpDeskTop10ModelsSelectTopModelobj.Branch),
                    Company_ID = HelpDeskTop10ModelsSelectTopModelobj.Company_ID,
                    UserCulture = HelpDeskTop10ModelsSelectTopModelobj.UserCulture,
                    dt = dt, // You can populate this with actual data as needed
                    exprtType = HelpDeskTop10ModelsSelectTopModelobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                    Header = CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "Top10Model").ToString(),
                    Options = dtOptions, // Populate this with your report options
                    selection = ds, // Add selection-related data here
                    Alignment = DtAlignment // Define alignment details for table columns
                };
                var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                return result.Value;
                // ReportExportCR5.Export(HelpDeskTop10ModelsSelectTopModelobj.exprtType, dt, dtOptions, ds, DtAlignment, "Top10Model", CommonFunctionalities.GetResourceString(HelpDeskTop10ModelsSelectTopModelobj.UserCulture.ToString(), "Top10Model").ToString());
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return false;
        }
        #endregion



        #region ::: HelpDeskTop10Models List and obj classes Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// HelpDeskTop10Models
        /// </summary>
        /// <returns>...</returns>
        ///
        public class HelpDeskTop10ModelsSelectBrandAndBranchList
        {
            public string CompanyArray { get; set; }
            public int Employee_ID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }

        public class HelpDeskTop10ModelsSelectTopModelList
        {

            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserCulture { get; set; }
            public string Brands { get; set; }
            public int Branch { get; set; }
            public int Company_ID { get; set; }
            public int exprtType { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }


        }
        #endregion



        #region ::: HelpDeskTop10Models classes Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// HelpDeskTop10Models
        /// </summary>
        /// <returns>...</returns>
        ///
        public class TopModelObjects
        {

            public int BrandID
            {
                get;
                set;
            }
            public int ModelID
            {
                get;
                set;
            }
            public string Model
            {
                get;
                set;
            }
            public string Brand
            {
                get;
                set;
            }
            public int Count
            {
                get;
                set;
            }
            public int Company_ID
            {
                get;
                set;
            }
            public int Branch_ID
            {
                get;
                set;
            }
            public string Company_Name
            {
                get;
                set;
            }
            public string Branch_Name
            {
                get;
                set;
            }
            public string Region
            {
                get;
                set;
            }
        }
        public class Brand
        {
            public List<Brands> Brands
            {
                get;
                set;
            }
        }
        public class Brands
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        public class Top_Branchs
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        #endregion

    }
}
