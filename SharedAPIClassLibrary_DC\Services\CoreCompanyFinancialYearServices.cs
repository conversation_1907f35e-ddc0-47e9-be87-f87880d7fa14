﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreCompanyFinancialYearServices
    {

        #region ::: Select /Mithun:::
        /// <summary>
        /// to select all the Financial Year of the company
        /// </summary> 

        public static IActionResult Select(SelectCompanyFinancialYearList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var x = default(dynamic);
            try
            {
                int companyID = Convert.ToInt32(SelectObj.Company_ID);

                // Connect to your database
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create command for calling stored procedure
                    using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectCompanyFinancialYear", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyID", companyID);

                        // Execute command and retrieve results
                        using (var reader = command.ExecuteReader())
                        {
                            // Process result set for data
                            var financialYearList = new List<CompanyFinancialYearData>();
                            while (reader.Read())
                            {
                                var financialYear = new CompanyFinancialYearData
                                {
                                    Company_FinancialYear_ID = (int)reader["Company_FinancialYear_ID"],
                                    delete = "<input type='checkbox' key='" + reader["Company_FinancialYear_ID"] + "' id='chk" + reader["Company_FinancialYear_ID"] + "' class='chkFinancialYearDelete' editmode='false'/>",
                                    Company_FinancialYear = int.Parse(reader["Company_FinancialYear"].ToString()),
                                    Company_FinancialYear_FromDate = ((DateTime)reader["Company_FinancialYear_FromDate"]).ToString("dd-MMM-yyyy"),
                                    Company_FinancialYear_ToDate = ((DateTime)reader["Company_FinancialYear_ToDate"]).ToString("dd-MMM-yyyy")
                                };
                                financialYearList.Add(financialYear);
                            }

                            reader.NextResult(); // Move to the next result set for total count
                            if (reader.Read())
                            {
                                int totalRecords = (int)reader["TotalRecords"];
                                int totalPages = rows > 0 ? (int)Math.Ceiling((double)totalRecords / rows) : 0;

                                // Prepare response object
                                x = new
                                {
                                    total = totalPages,
                                    page = page,
                                    records = totalRecords,
                                    data = financialYearList.Skip((page - 1) * rows).Take(rows)
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                // Logging can be done here if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // Return JSON response
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }


        #endregion

        #region ::: SelectCompany Mithun:::
        /// <summary>
        /// to get all the Company Names 
        /// </summary> 

        public static IActionResult SelectCompany(SelectCompanyList SelectCompanyObj, string constring, int LogException)
        {
            var regionArray = new List<dynamic>();
            try
            {
                int companyID = Convert.ToInt32(SelectCompanyObj.Company_ID);
                int userLanguageID = Convert.ToInt32(SelectCompanyObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCompanyObj.GeneralLanguageID);

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectCompany", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@Company_ID", companyID);
                    command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var item = new
                            {
                                Company_ID = reader.GetInt32(0),
                                Company_Name = reader.GetString(1)
                            };
                            regionArray.Add(item);
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(regionArray, JsonRequestBehavior.AllowGet);
            return new JsonResult(regionArray);
        }

        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// to save the data
        /// </summary>
        public static IActionResult Save(SaveCoreCompanyFinancialList SaveObj, string constring, int LogException)
        {
            try
            {
                string jsonData = SaveObj.Data;
                JObject jobj = JObject.Parse(jsonData);
                int Company_ID = Convert.ToInt32(SaveObj.Company_ID);
                int User_ID = Convert.ToInt32(SaveObj.User_ID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch GNM_Object records using stored procedure
                    SqlCommand objectCommand = new SqlCommand("Up_Sel_Am_Erp_SelectFinancialYearObjects", connection);
                    objectCommand.CommandType = CommandType.StoredProcedure;

                    List<GNM_Object> ObjectList = new List<GNM_Object>();

                    using (SqlDataReader objectReader = objectCommand.ExecuteReader())
                    {
                        while (objectReader.Read())
                        {
                            GNM_Object obj = new GNM_Object();
                            obj.Object_ID = Convert.ToInt32(objectReader["Object_ID"]);
                            obj.Object_Description = objectReader["Object_Description"].ToString();
                            // Map other properties as needed
                            ObjectList.Add(obj);
                        }
                    }

                    foreach (var ObjectItemList in ObjectList)
                    {
                        foreach (var row in jobj["rows"])
                        {
                            GNM_CompanyFinancialYear detailRow = row.ToObject<GNM_CompanyFinancialYear>();

                            if (detailRow.Company_FinancialYear_ID != 0)
                            {
                                // Update existing record using stored procedure
                                SqlCommand updateCommand = new SqlCommand("Up_Upd_Am_Erp_UpdateCompanyFinancialYear", connection);
                                updateCommand.CommandType = CommandType.StoredProcedure;
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear_ID", detailRow.Company_FinancialYear_ID);
                                updateCommand.Parameters.AddWithValue("@Company_ID", detailRow.Company_ID);
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear", detailRow.Company_FinancialYear);
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear_FromDate", detailRow.Company_FinancialYear_FromDate);
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear_ToDate", detailRow.Company_FinancialYear_ToDate);

                                updateCommand.ExecuteNonQuery();

                                // Log update
                                // gbl.InsertGPSDetails(Company_ID, Convert.ToInt32(SaveObj.Branch), User_ID, Common.GetObjectID("CoreCompanyFinancialYear",constring), detailRow.Company_FinancialYear_ID, 0, 0, "Updated " + detailRow.Company_FinancialYear, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));

                            }
                            else
                            {
                                // Insert new record using stored procedure
                                SqlCommand insertCommand = new SqlCommand("Up_Ins_Am_Erp_InsertCompanyFinancialYear", connection);
                                insertCommand.CommandType = CommandType.StoredProcedure;
                                insertCommand.Parameters.AddWithValue("@Company_ID", detailRow.Company_ID);
                                insertCommand.Parameters.AddWithValue("@Company_FinancialYear", detailRow.Company_FinancialYear);
                                insertCommand.Parameters.AddWithValue("@Company_FinancialYear_FromDate", detailRow.Company_FinancialYear_FromDate);
                                insertCommand.Parameters.AddWithValue("@Company_FinancialYear_ToDate", detailRow.Company_FinancialYear_ToDate);

                                SqlParameter newFinancialYearIDParam = new SqlParameter("@NewFinancialYearID", SqlDbType.Int);
                                newFinancialYearIDParam.Direction = ParameterDirection.Output;
                                insertCommand.Parameters.Add(newFinancialYearIDParam);

                                insertCommand.ExecuteNonQuery();

                                int newFinancialYearID = Convert.ToInt32(newFinancialYearIDParam.Value);

                                // Log insert
                                //   gbl.InsertGPSDetails(Company_ID, Convert.ToInt32(SaveObj.Branch), User_ID, Common.GetObjectID("CoreCompanyFinancialYear",constring), newFinancialYearID, 0, 0, "Inserted " + detailRow.Company_FinancialYear, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));

                            }

                            // Handle PrefixSuffixList logic using stored procedure for select query
                            // Call stored procedure to fetch PrefixSuffix records
                            SqlCommand selectCommand = new SqlCommand("Up_Sel_Am_Erp_SelectFinacialYearPrefixSuffix", connection);
                            selectCommand.CommandType = CommandType.StoredProcedure;
                            selectCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                            selectCommand.Parameters.AddWithValue("@Object_ID", ObjectItemList.Object_ID);
                            selectCommand.Parameters.AddWithValue("@FinancialYear", detailRow.Company_FinancialYear - 1);

                            using (SqlDataReader reader = selectCommand.ExecuteReader())
                            {
                                if (!reader.HasRows)
                                {
                                    // Insert new PrefixSuffix record using stored procedure
                                    SqlCommand insertPrefixSuffixCommand = new SqlCommand("Up_Ins_Am_Erp_InsertPrefixsSuffixs", connection);
                                    insertPrefixSuffixCommand.CommandType = CommandType.StoredProcedure;
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Object_ID", ObjectItemList.Object_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Start_Number", 1);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Prefix", "");
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Suffix", "");
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@FromDate", detailRow.Company_FinancialYear_FromDate);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@ToDate", Convert.ToDateTime(detailRow.Company_FinancialYear_ToDate.ToString().Split(' ')[0] + " 23:59:59.000"));
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@FinancialYear", detailRow.Company_FinancialYear);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Company_FinancialYear_ID", detailRow.Company_FinancialYear_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@ModifiedBY", User_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                    insertPrefixSuffixCommand.ExecuteNonQuery();

                                }
                                else
                                {
                                    // Update existing PrefixSuffix record using stored procedure
                                    while (reader.Read())
                                    {
                                        SqlCommand updatePrefixSuffixCommand = new SqlCommand("Up_Upd_Am_Erp_UpdatePrefixsSuffixs", connection);
                                        updatePrefixSuffixCommand.CommandType = CommandType.StoredProcedure;
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@PrefixSuffix_ID", reader["PrefixSuffix_ID"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Branch_ID", reader["Branch_ID"] == DBNull.Value ? DBNull.Value : (object)reader["Branch_ID"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Start_Number", reader["Start_Number"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Prefix", reader["Prefix"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Suffix", reader["Suffix"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@ToDate", Convert.ToDateTime(detailRow.Company_FinancialYear_ToDate.ToString().Split(' ')[0] + " 23:59:59.000"));
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@ModifiedBY", User_ID);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                        updatePrefixSuffixCommand.ExecuteNonQuery();

                                    }
                                }
                            }
                        }

                    }
                    return new JsonResult(new { success = true });
                }

            }

            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { success = false });
            }
        }


        #endregion

        #region ::: Delete Mithun:::
        /// <summary>
        /// to Delete the details
        /// </summary>

        public static IActionResult Delete(DeleteCoreCompanyFinancialList DeleteObj, string constring, int LogException)
        {
            string errorMsg = "";
            int value = 0;
            try
            {
                JObject jobj = JObject.Parse(DeleteObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    for (int i = 0; i < rowCount; i++)
                    {
                        JToken idToken = jobj["rows"].ElementAt(i)["id"];
                        id = idToken.Value<int>();

                        // Delete command
                        cmd.CommandText = "DELETE FROM GNM_CompanyFinancialYear WHERE Company_FinancialYear_ID = @id";
                        cmd.Parameters.Clear();
                        cmd.Parameters.AddWithValue("@id", id);
                        cmd.ExecuteNonQuery();
                    }
                }

                errorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();

                // Logging the deletion
                //    gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID), Convert.ToInt32(DeleteObj.Branch), Convert.ToInt32(DeleteObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyFinancialYear",constring)), id, 0, 0, "Deleted " + value, false, Convert.ToInt32(DeleteObj.MenuID), Convert.ToDateTime(DeleteObj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: CheckFinancialYear /Mithun:::
        /// <summary>
        /// to check if the financial year is already selected for the company
        /// </summary>
        public static IActionResult CheckFinancialYear(CheckFinancialYearList CheckFinancialYearObj, string constring, int LogException)
        {
            int status = 0;
            try
            {
                int companyID = Convert.ToInt32(CheckFinancialYearObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckCompanyFinancialYear", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Input parameters
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@FinancialYear", CheckFinancialYearObj.financialYear);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckFinancialYearObj.primaryKey);

                        // Execute the command
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // Read the status value from the result set
                                status = reader.GetInt32(0);
                            }
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                // Log SQL exceptions with more details
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
                }
                // Handle SQL exception
            }
            catch (Exception ex)
            {
                // Log general exceptions with more details
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle other exceptions
            }

            return new JsonResult(status);
        }





        #endregion

        #region ::: GetAllSavedFinancialYears /Mithun:::
        /// <summary>
        /// to check if the financial year is already selected for the company
        /// </summary>

        public static IActionResult GetAllSavedFinancialYears(GetAllSavedFinancialYearsList GetAllSavedFinancialYearsObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            try
            {
                int companyID = Convert.ToInt32(GetAllSavedFinancialYearsObj.Company_ID);

                // Step 1: ADO.NET to execute stored procedure
                List<string> financialYears = new List<string>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetAllSavedFinancialYears", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string financialYear = reader["Company_FinancialYear"].ToString();
                            financialYears.Add(financialYear);
                        }
                    }
                }

                // Step 2: LINQ to construct JSON result
                var x = financialYears.Select(f => new { Company_FinancialYear = f });

                jsonResult = new
                {
                    financialYear = x.ToArray()
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // Handle web exception
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // Handle other exceptions
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion












        public class GetAllSavedFinancialYearsList
        {
            public int Company_ID { get; set; }
        }
        public class CheckFinancialYearList
        {
            public int Company_ID { get; set; }
            public int financialYear { get; set; }
            public int primaryKey { get; set; }
        }
        public class DeleteCoreCompanyFinancialList
        {
            public string key { get; set; }
            public string Lang { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class SaveCoreCompanyFinancialList
        {
            public string Data { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class SelectCompanyList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectCompanyFinancialYearList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }


        public partial class GNM_CompanyFinancialYear
        {
            public int Company_FinancialYear_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public int Company_FinancialYear { get; set; }
            public System.DateTime Company_FinancialYear_FromDate { get; set; }
            public System.DateTime Company_FinancialYear_ToDate { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
        }

        public class CompanyFinancialYearData
        {
            public string edit { get; set; }
            public string delete { get; set; }
            public int Company_FinancialYear_ID { get; set; }
            public int Company_FinancialYear { get; set; }
            public string Company_FinancialYear_FromDate { get; set; }
            public string Company_FinancialYear_ToDate { get; set; }
        }

    }
}
