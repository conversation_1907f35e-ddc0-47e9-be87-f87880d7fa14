﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreQuestionnaireLevel1MasterController : ApiController
    {
        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/SelectReferenceMaster")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterQuestionnaireLevel1List SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.SelectReferenceMaster(SelectReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To Select Questions for IssueSubArea
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/Select")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreQuestionnaireLevel1List SelectObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreQuestionnaireLevel1MasterServices.Select(SelectObj, connstring, LogException, sidx, sord, page, rows, _search, advnce, filters, advnceFilters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectParticularQuestionnaireLevel1 /Mithun:::
        /// <summary>
        /// SelectParticularQuestionnaireLevel1
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/SelectParticularQuestionnaireLevel1")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularQuestionnaireLevel1([FromBody] SelectParticularQuestionnaireLevel1List SelectParticularQuestionnaireLevel1Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.SelectParticularQuestionnaireLevel1(SelectParticularQuestionnaireLevel1Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Questionnaire Level1 Question
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/Delete")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreQuestionnaireLevel1List DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/UpdateLocale")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleCoreQuestionnaireLevel1List UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckQuestionnaireLevel1Locale /Mithun:::
        /// <summary>
        /// CheckQuestionnaireLevel1Locale
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/CheckQuestionnaireLevel1Locale")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestionnaireLevel1Locale([FromBody] CheckQuestionnaireLevel1LocaleList CheckQuestionnaireLevel1LocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.CheckQuestionnaireLevel1Locale(CheckQuestionnaireLevel1LocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/Save")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] CoreQuestionnaireLevel1SaveList saveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.Save(saveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckQuestion /Mithun:::
        /// <summary>
        /// To Check Question in Question Level1 with Issue Sub AreaID already exists 
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/CheckQuestionnaireLevel1")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestionnaireLevel1([FromBody] CheckQuestionnaireLevel1List CheckQuestionnaireLevel1Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel1MasterServices.CheckQuestionnaireLevel1(CheckQuestionnaireLevel1Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region Export
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel1Master/Export")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectCoreQuestionnaireLevel1List ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;


            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;



            try
            {


                Object Response = await CoreQuestionnaireLevel1MasterServices.Export(ExportObj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion




    }
}
