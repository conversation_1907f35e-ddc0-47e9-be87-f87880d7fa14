﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Tr_CaseSummaryServices
    {



        #region ::: SelectStatusAndBranch Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Status And Branch
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectStatusAndBranch(HelpDesk_Tr_CaseSummarySelectStatusAndBranchList HelpDesk_Tr_CaseSummarySelectStatusAndBranchobj, string connString, int LogException)
        {
            var Masterdata = new object();
            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand
                    {
                        Connection = conn,
                        CommandType = CommandType.StoredProcedure
                    };

                    if (HelpDesk_Tr_CaseSummarySelectStatusAndBranchobj.LanguageID == Convert.ToInt32(HelpDesk_Tr_CaseSummarySelectStatusAndBranchobj.GeneralLanguageID))
                    {
                        cmd.CommandText = "SP_AMERP_HelpDesk_GetCaseStatusByDefaultLanguage";
                    }
                    else
                    {
                        cmd.CommandText = "SP_AMERP_HelpDesk_GetCaseStatusByOtherLanguage";
                    }

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        var caseStatus = new List<object>();
                        while (reader.Read())
                        {
                            caseStatus.Add(new
                            {
                                ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                Name = reader.GetString(reader.GetOrdinal("Name"))
                            });
                        }

                        Masterdata = new
                        {
                            CaseStatus = caseStatus
                        };
                    }
                }


            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion



        #region ::: Select Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult Select(HelpDesk_Tr_CaseSummarySelectList HelpDesk_Tr_CaseSummarySelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int count = 0, total = 0;
                var serviceRequests = new List<HD_ServiceRequest>();
                var finalData = new List<ServiceRequest>();

                int userId = HelpDesk_Tr_CaseSummarySelectobj.User_ID;
                int langId = HelpDesk_Tr_CaseSummarySelectobj.Language_ID;

                // Retrieve Input Parameters
                string companyData = Uri.EscapeUriString(HelpDesk_Tr_CaseSummarySelectobj.CompanyData);
                string branchData = Uri.EscapeUriString(HelpDesk_Tr_CaseSummarySelectobj.BranchData);
                string fromDate = HelpDesk_Tr_CaseSummarySelectobj.FromDate;
                string toDate = HelpDesk_Tr_CaseSummarySelectobj.ToDate;
                Status stats = JObject.Parse(HelpDesk_Tr_CaseSummarySelectobj.WFStatus).ToObject<Status>();
                string companyIDs = ExtractIdsFromJson(companyData, "Companys");
                string branchIDs = ExtractIdsFromJson(branchData, "Branchs");
                DateTime? parsedFromDate = string.IsNullOrEmpty(fromDate) ? (DateTime?)null : Convert.ToDateTime(fromDate);
                DateTime? parsedToDate = string.IsNullOrEmpty(toDate) ? (DateTime?)null : Convert.ToDateTime(toDate).AddDays(1);  // Add a day to the ToDate


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Service Requests
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestsHelpDesk_Tr_CaseSummary", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", companyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", branchIDs);

                        // Add the FromDate and ToDate parameters if they are provided
                        if (parsedFromDate.HasValue)
                            cmd.Parameters.AddWithValue("@FromDate", parsedFromDate.Value);
                        else
                            cmd.Parameters.AddWithValue("@FromDate", DBNull.Value);

                        if (parsedToDate.HasValue)
                            cmd.Parameters.AddWithValue("@ToDate", parsedToDate.Value);
                        else
                            cmd.Parameters.AddWithValue("@ToDate", DBNull.Value);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceRequests.Add(new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    ServiceRequestNumber = reader["ServiceRequestNumber"]?.ToString(),
                                    ServiceRequestDate = Convert.ToDateTime(reader["ServiceRequestDate"]),
                                    Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Model_ID"]),
                                    IsDealer = Convert.ToBoolean(reader["IsDealer"]),
                                    IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueArea_ID"]),
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    SerialNumber = reader["SerialNumber"]?.ToString(),
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    CallStatus_ID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"])
                                });
                            }
                        }
                    }

                    if (stats != null)
                    {
                        serviceRequests = serviceRequests
                            .Where(sr => stats.WFStatus.Any(s => s.ID == sr.CallStatus_ID))
                            .ToList();
                    }

                    if (HelpDesk_Tr_CaseSummarySelectobj.GeneralLanguageCode?.ToString() == HelpDesk_Tr_CaseSummarySelectobj.UserLanguageCode?.ToString())
                    {
                        // Language-specific logic
                        foreach (var request in serviceRequests)
                        {
                            var companyName = string.Empty;
                            var branchName = string.Empty;

                            // Fetch Company and Branch Names
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyBranchNames", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyID", request.Company_ID);
                                cmd.Parameters.AddWithValue("@BranchID", request.Branch_ID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        companyName = reader.GetString(0);
                                        branchName = reader.GetString(1);
                                    }
                                }
                            }
                            finalData.Add(new ServiceRequest
                            {
                                ServiceRequest_ID = request.ServiceRequest_ID,
                                SerialNumber = request.SerialNumber,
                                PartyName = (bool)request.IsDealer ? branchName : GetPartyName(request.Party_ID, conn, LogException),
                                IssueArea = request.IssueArea_ID.HasValue ? GetRefMasterDetailName(request.IssueArea_ID.Value, conn, LogException) : null,
                                IssueSubArea = request.IssueSubArea_ID.HasValue ? GetIssueSubAreaDescription(request.IssueSubArea_ID.Value, conn, LogException) : null,
                                Status = request.CallStatus_ID != 0 ? GetWFStepStatusName(request.CallStatus_ID, conn, LogException) : null,
                                ServiceRequestDate = request.ServiceRequestDate != default(DateTime) ? request.ServiceRequestDate.ToString("dd-MMM-yyyy") : null,
                                RequestNumber = request.ServiceRequestNumber ?? string.Empty,
                                Model = request.Model_ID.HasValue ? GetModelName(request.Model_ID.Value, conn, LogException) : null,
                                FunctionGroup = request.FunctionGroup_ID.HasValue ? GetRefMasterDetailName(request.FunctionGroup_ID.Value, conn, LogException) : null,
                                CompanyName = companyName,
                                BranchName = branchName
                            });


                        }
                    }
                }

                // Determine which data to include in JSON
                var responseData = finalData.Any() ? finalData.Cast<object>() : serviceRequests.Cast<object>();


                // Pagination
                count = responseData.Count();
                total = (int)Math.Ceiling((double)count / rows);

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = responseData.Skip((page - 1) * rows).Take(rows).ToList()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }



        private static string ExtractIdsFromJson(string jsonData, string property)
        {
            if (string.IsNullOrEmpty(jsonData)) return string.Empty;

            try
            {
                // Double decode the URI-encoded string
                string decodedData = Uri.UnescapeDataString(Uri.UnescapeDataString(jsonData));

                // Decrypt the decoded string (if encryption is applied)
                string decryptedData = Common.DecryptString(decodedData);




                // Parse the decrypted JSON
                var parsedData = JObject.Parse(decryptedData);

                // Extract the property as a JArray
                var items = parsedData[property] as JArray;

                if (items != null)
                {
                    var ids = new List<string>();
                    foreach (var item in items)
                    {
                        ids.Add(item["ID"].ToString());
                    }
                    return string.Join(",", ids);
                }
            }
            catch (JsonReaderException ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }

            return string.Empty;
        }



        public static string GetRefMasterDetailName(int refMasterDetailID, SqlConnection conn, int LogException)
        {
            string refMasterDetailName = string.Empty;

            try
            {
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetRefMasterDetailName", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("@RefMasterDetail_ID", SqlDbType.Int)).Value = refMasterDetailID;

                    object result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        refMasterDetailName = result.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return refMasterDetailName;
        }

        public static string GetIssueSubAreaDescription(int issueSubAreaID, SqlConnection conn, int LogException)
        {
            string description = string.Empty;

            try
            {
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetIssueSubAreaDescription", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("@IssueSubArea_ID", SqlDbType.Int)).Value = issueSubAreaID;

                    object result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        description = result.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return description;
        }

        public static string GetWFStepStatusName(int wfStepStatusID, SqlConnection conn, int LogException)
        {
            string statusName = string.Empty;

            try
            {
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusName", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("@WFStepStatus_ID", SqlDbType.Int)).Value = wfStepStatusID;

                    object result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        statusName = result.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return statusName;
        }
        public static string GetModelName(int modelID, SqlConnection conn, int LogException)
        {
            string modelName = string.Empty;

            try
            {
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelName", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("@Model_ID", SqlDbType.Int)).Value = modelID;

                    object result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        modelName = result.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return modelName;
        }
        public static string GetPartyName(int partyID, SqlConnection conn, int LogException)
        {
            string partyName = string.Empty;

            try
            {
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartyName", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("@Party_ID", SqlDbType.Int)).Value = partyID;

                    object result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        partyName = result.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return partyName;
        }
        #endregion


        #region ::: Export Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static async Task<object> Export(HelpDesk_Tr_CaseSummarySelectList HelpDesk_Tr_CaseSummarySelectobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            int Count = 0;
            try
            {
                var serviceRequests = new List<HD_ServiceRequest>();
                var finalData = new List<ServiceRequest>();

                int userId = HelpDesk_Tr_CaseSummarySelectobj.User_ID;
                int langId = HelpDesk_Tr_CaseSummarySelectobj.Language_ID;

                // Retrieve Input Parameters
                string companyData = Uri.EscapeUriString(HelpDesk_Tr_CaseSummarySelectobj.CompanyData);
                string branchData = Uri.EscapeUriString(HelpDesk_Tr_CaseSummarySelectobj.BranchData);
                string fromDate = HelpDesk_Tr_CaseSummarySelectobj.FromDate;
                string toDate = HelpDesk_Tr_CaseSummarySelectobj.ToDate;
                Status stats = JObject.Parse(HelpDesk_Tr_CaseSummarySelectobj.WFStatus).ToObject<Status>();
                string companyIDs = ExtractIdsFromJson(companyData, "Companys");
                string branchIDs = ExtractIdsFromJson(branchData, "Branchs");
                DateTime? parsedFromDate = string.IsNullOrEmpty(fromDate) ? (DateTime?)null : Convert.ToDateTime(fromDate);
                DateTime? parsedToDate = string.IsNullOrEmpty(toDate) ? (DateTime?)null : Convert.ToDateTime(toDate).AddDays(1);  // Add a day to the ToDate


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Service Requests
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestsHelpDesk_Tr_CaseSummary", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", companyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", branchIDs);

                        // Add the FromDate and ToDate parameters if they are provided
                        if (parsedFromDate.HasValue)
                            cmd.Parameters.AddWithValue("@FromDate", parsedFromDate.Value);
                        else
                            cmd.Parameters.AddWithValue("@FromDate", DBNull.Value);

                        if (parsedToDate.HasValue)
                            cmd.Parameters.AddWithValue("@ToDate", parsedToDate.Value);
                        else
                            cmd.Parameters.AddWithValue("@ToDate", DBNull.Value);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceRequests.Add(new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    ServiceRequestNumber = reader["ServiceRequestNumber"]?.ToString(),
                                    ServiceRequestDate = Convert.ToDateTime(reader["ServiceRequestDate"]),
                                    Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Model_ID"]),
                                    IsDealer = Convert.ToBoolean(reader["IsDealer"]),
                                    IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueArea_ID"]),
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    SerialNumber = reader["SerialNumber"]?.ToString(),
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    CallStatus_ID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"])
                                });
                            }
                        }
                    }

                    if (stats != null)
                    {
                        serviceRequests = serviceRequests
                            .Where(sr => stats.WFStatus.Any(s => s.ID == sr.CallStatus_ID))
                            .ToList();
                    }

                    if (HelpDesk_Tr_CaseSummarySelectobj.GeneralLanguageCode?.ToString() == HelpDesk_Tr_CaseSummarySelectobj.UserLanguageCode?.ToString())
                    {
                        // Language-specific logic
                        foreach (var request in serviceRequests)
                        {
                            var companyName = string.Empty;
                            var branchName = string.Empty;

                            // Fetch Company and Branch Names
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyBranchNames", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyID", request.Company_ID);
                                cmd.Parameters.AddWithValue("@BranchID", request.Branch_ID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        companyName = reader.GetString(0);
                                        branchName = reader.GetString(1);
                                    }
                                }
                            }
                            finalData.Add(new ServiceRequest
                            {
                                ServiceRequest_ID = request.ServiceRequest_ID,
                                SerialNumber = request.SerialNumber,
                                PartyName = (bool)request.IsDealer ? branchName : GetPartyName(request.Party_ID, conn, LogException),
                                IssueArea = request.IssueArea_ID.HasValue ? GetRefMasterDetailName(request.IssueArea_ID.Value, conn, LogException) : null,
                                IssueSubArea = request.IssueSubArea_ID.HasValue ? GetIssueSubAreaDescription(request.IssueSubArea_ID.Value, conn, LogException) : null,
                                Status = request.CallStatus_ID != 0 ? GetWFStepStatusName(request.CallStatus_ID, conn, LogException) : null,
                                ServiceRequestDate = request.ServiceRequestDate != default(DateTime) ? request.ServiceRequestDate.ToString("dd-MMM-yyyy") : null,
                                RequestNumber = request.ServiceRequestNumber ?? string.Empty,
                                Model = request.Model_ID.HasValue ? GetModelName(request.Model_ID.Value, conn, LogException) : null,
                                FunctionGroup = request.FunctionGroup_ID.HasValue ? GetRefMasterDetailName(request.FunctionGroup_ID.Value, conn, LogException) : null,
                                CompanyName = companyName,
                                BranchName = branchName
                            });


                        }
                    }
                }

                // Determine which data to include in JSON
                var responseData = finalData.Any() ? finalData.Cast<object>() : serviceRequests.Cast<object>();
                DataTable dtOptions = new DataTable();
                //dtOptions.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Branch").ToString());                
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "todate").ToString());
                dtOptions.Rows.Add(HelpDesk_Tr_CaseSummarySelectobj.FromDate.ToString(), HelpDesk_Tr_CaseSummarySelectobj.ToDate.ToString());
                DataSet ds = new DataSet();
                DataTable dtbrnds = new DataTable();
                dtbrnds.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Status").ToString());
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < stats.WFStatus.Count(); i++)
                {
                    sb.Append(stats.WFStatus.ElementAt(i).Name + ";");
                }
                dtbrnds.Rows.Add(sb.ToString().TrimEnd(new char[] { ';' }));
                ds.Tables.Add(dtbrnds);
                List<ServiceRequest> FinalResult = new List<ServiceRequest>();
                DataTable dt = new DataTable();
                FinalResult = responseData.Cast<ServiceRequest>().ToList();
                dt.Columns.Add("SI No");//1
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Company").ToString());//2
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Branch").ToString());    //3            
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "caseregistrationnumber").ToString());//4
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Date").ToString());//5
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Party").ToString());//6
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "model").ToString());//7
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "serialnumber").ToString());//8
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "IssueArea").ToString());//9
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "IssueSubArea").ToString());//10
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "FunctionGroup").ToString());//11
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "status").ToString());//12

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("slno"); //1
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Company").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Branch").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "caseregistrationnumber").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Date").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "Party").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "model").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "serialnumber").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "IssueArea").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "IssueSubArea").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "FunctionGroup").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "status").ToString());
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                Count = FinalResult.Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        dt.Rows.Add(i + 1, FinalResult.ElementAt(i).CompanyName, FinalResult.ElementAt(i).BranchName, FinalResult.ElementAt(i).RequestNumber, FinalResult.ElementAt(i).ServiceRequestDate, FinalResult.ElementAt(i).PartyName, FinalResult.ElementAt(i).Model, FinalResult.ElementAt(i).SerialNumber, FinalResult.ElementAt(i).IssueArea, FinalResult.ElementAt(i).IssueSubArea, FinalResult.ElementAt(i).FunctionGroup, FinalResult.ElementAt(i).Status);
                    }
                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = HelpDesk_Tr_CaseSummarySelectobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = HelpDesk_Tr_CaseSummarySelectobj.Branch,
                    dt1 = DtAlignment,


                    dt = dt,

                    FileName = "TicketSummary", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "CaseSummary").ToString(), // Set a default or dynamic header
                    exprtType = HelpDesk_Tr_CaseSummarySelectobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = HelpDesk_Tr_CaseSummarySelectobj.UserCulture
                };
                var res = await DocumentExport.Export(reportExportList, connString, LogException);
                return res.Value;
                //  DocumentExport.Export(HelpDesk_Tr_CaseSummarySelectobj.exprtType, dt, DtAlignment, "TicketSummary", CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseSummarySelectobj.UserCulture.ToString(), "CaseSummary").ToString());
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return false;
        }
        #endregion



        #region ::: HelpDesk_Tr_CaseSummary List and obj Classes Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_CaseSummary
        /// </summary>
        /// <returns>...</returns>
        /// 
        public class HelpDesk_Tr_CaseSummarySelectList
        {

            public string UserCulture { get; set; }
            public string StatusNames { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public int exprtType { get; set; }
            public int User_ID { get; set; }
            public int Language_ID { get; set; }
            public string CompanyData { get; set; }
            public string BranchData { get; set; }
            public string WFStatus { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }

        public class HelpDesk_Tr_CaseSummarySelectStatusAndBranchList
        {
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }
        #endregion


        #region ::: HelpDesk_Tr_CaseSummary Classes Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_CaseSummary
        /// </summary>
        /// <returns>...</returns>
        /// 
        public class ServiceRequest
        {
            public int ServiceRequest_ID { get; set; }
            public string RequestNumber { get; set; }
            public string ServiceRequestDate { get; set; }
            public string Model { get; set; }
            public string SerialNumber { get; set; }
            public string IssueArea { get; set; }
            public string IssueSubArea { get; set; }
            public string Status { get; set; }
            public string PartyName { get; set; }
            public string FunctionGroup { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
        }

        public class Status
        {
            public List<WFStatus> WFStatus
            {
                get;
                set;
            }
        }
        public class WFStatus
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        #endregion


    }
}
