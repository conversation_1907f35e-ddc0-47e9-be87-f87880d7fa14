﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CorePartsRatecontractImportServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CorePartsRatecontractImportController : ApiController
    {


        #region ::: ImportPartsRatecontractTemplate :::
        /// <summary>
        /// ImportPartsRatecontractTemplate
        /// </summary>
        [Route("api/_PrevostDashboard/ImportPartsRatecontractTemplate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ImportPartsRatecontractTemplate()
        {
            var Response = default(dynamic);
            try
            {
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsRatecontractImportServices.ImportPartsRatecontractTemplate(LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UploadFile Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// UploadFile
        /// </summary>
        /// 
        [Route("api/CorePartsRatecontractImport/UploadFile")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> UploadFile()
        {
            try
            {
                var provider = new MultipartMemoryStreamProvider();
                await Request.Content.ReadAsMultipartAsync(provider);
                string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string companyId = null, effectiveFrom = null, effectiveTo = null,
                       userCulture = null, branchId = null, userId = null,
                       menuId = null, loggedInDateTime = null, Currency_ID = null;
                IFormFile partsPrice = null;

                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        var value = await content.ReadAsStringAsync();
                        switch (name)
                        {
                            case "userId": userId = value; break;
                            case "companyId": companyId = value; break;
                            case "effectiveFrom": effectiveFrom = value; break;
                            case "effectiveTo": effectiveTo = value; break;
                            case "userCulture": userCulture = value; break;
                            case "menuId": menuId = value; break;
                            case "branchId": branchId = value; break;
                            case "loggedInDateTime": loggedInDateTime = value; break;
                        }
                    }
                    else if (name == "formFile")
                    {
                        var stream = await content.ReadAsStreamAsync();
                        partsPrice = new FormFile(stream, 0, stream.Length, name, content.Headers.ContentDisposition.FileName.Trim('"'));
                    }
                }

                var response = CorePartsRatecontractImportServices.UploadFile(partsPrice, connString, Convert.ToInt32(companyId), Convert.ToDateTime(effectiveFrom), Convert.ToDateTime(effectiveTo), Convert.ToInt32(userId), Convert.ToInt32(menuId), Convert.ToInt32(branchId), userCulture, Convert.ToDateTime(loggedInDateTime), Convert.ToInt32(Currency_ID));
                return Ok(response);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }
        #endregion



        #region ::: Get Party Parts Rate Contract Detail Grid Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsRatecontractImport/SelectPartsRateContractGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartsRateContractGrid([FromBody] SelectPartsRateContractGridList SelectPartsRateContractGridobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsRatecontractImportServices.SelectPartsRateContractGrid(connString, SelectPartsRateContractGridobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Get Party Parts Rate Contract Detail Grid Uday KumarJ B 21-08-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CorePartsRatecontractImport/SelectPartyPartsRateContractGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyPartsRateContractGrid([FromBody] SelectPartyPartsRateContractGridList SelectPartyPartsRateContractGridobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsRatecontractImportServices.SelectPartyPartsRateContractGrid(connString, SelectPartyPartsRateContractGridobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region :::RateContractExport Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// RateContractExport
        /// </summary>
        /// 
        [Route("api/CorePartsRatecontractImport/RateContractExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> RateContractExport([FromBody] RateContractExportList RateContractExportobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            int exprtType = 1;
            try
            {


                Object Response = CorePartsRatecontractImportServices.RateContractExport(RateContractExportobj, connstring);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region :::Export Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ///Export
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsRatecontractImport/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Export([FromBody] SelectPartsRateContractGridList SelectPartsRateContractGridobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsRatecontractImportServices.Export(connString, SelectPartsRateContractGridobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Landing Grid Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// Get Landing Grid  data
        /// </summary>
        /// 
        [Route("api/CorePartsRatecontractImport/GetLandingData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetLandingData([FromBody] GetLandingDataList GetLandingDataobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsRatecontractImportServices.GetLandingData(connString, GetLandingDataobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckAccess Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// CheckAccess
        /// </summary>
        ///
        [Route("api/CorePartsRatecontractImport/CheckAccess")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckAccess([FromBody] CheckAccessList CheckAccessobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartsRatecontractImportServices.CheckAccess(connString, CheckAccessobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}