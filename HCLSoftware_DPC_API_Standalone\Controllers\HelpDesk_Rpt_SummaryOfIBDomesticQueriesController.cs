﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Rpt_SummaryOfIBDomesticQueriesController : ApiController
    {


        #region  ::: Generate SummaryOfBusinessCalls Report Uday Kumar J B 14-11-2024 :::
        /// <summary>
        /// Generate Summary Of Business Calls Report
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_SummaryOfIBDomesticQueries/SummaryOfBusinessCallsReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SummaryOfBusinessCallsReport([FromBody] SummaryOfBusinessCallsReportList SummaryOfBusinessCallsReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices.SummaryOfBusinessCallsReport(SummaryOfBusinessCallsReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Generate TodaysCallSummary Report Uday Kumar J B 14-11-2024 :::
        /// <summary>
        ///  Generate Todays Call Summary Report
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_SummaryOfIBDomesticQueries/TodaysCallSummaryReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult TodaysCallSummaryReport([FromBody] TodaysCallSummaryReportList TodaysCallSummaryReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices.TodaysCallSummaryReport(TodaysCallSummaryReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Generate Dailyopencalls Report Uday Kumar J B 14-11-2024:::
        /// <summary>
        ///  Generate TDaily open calls Report
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_SummaryOfIBDomesticQueries/DailyopencallsReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyopencallsReport([FromBody] DailyopencallsReportList DailyopencallsReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices.DailyopencallsReport(DailyopencallsReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Generate Daily open calls Report Export Uday Kumar J B 14-11-2024:::
        [System.Web.Http.Route("api/HelpDesk_Rpt_SummaryOfIBDomesticQueries/Export")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] DailyopencallsReportList DailyopencallsReportobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = DailyopencallsReportobj.sidx;
            string sord = DailyopencallsReportobj.sord;
            string filter = DailyopencallsReportobj.filter;
            string advnceFilter = DailyopencallsReportobj.advanceFilter;
            try
            {


                object Response = await HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices.Export(DailyopencallsReportobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion



    }
}