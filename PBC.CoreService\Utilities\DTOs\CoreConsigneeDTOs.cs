using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PBC.CoreService.Utilities.DTOs
{
    #region Request DTOs
    
    public class LoadBranchDropdownList
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string Branch { get; set; }
    }

    public class SelectConsigneeList
    {
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int LanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string? GeneralCulture { get; set; }
        public string? UserCulture { get; set; }
    }

    public class GetAllconsigneeList
    {
        public string? GeneralCulture { get; set; }
        public string? UserCulture { get; set; }
        public int GeneralLanguageID { get; set; }
        public int LanguageID { get; set; }
        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string? BranchName { get; set; }
        public string? User_ID { get; set; }
        public int MenuID { get; set; }
        public int LoggedINDateTime { get; set; }
        public int UserLanguageID { get; set; }
        public int exprtType { get; set; }
        public string? filter { get; set; }
        public string? advanceFilter { get; set; }
        public string? sidx { get; set; }
        public string? sord { get; set; }
    }

    public class SaveList
    {
        public string? data { get; set; }
        public string? Branch { set; get; }
        public int User_ID { get; set; }
        public int Company_ID { set; get; }
        public int MenuID { set; get; }
        public DateTime LoggedINDateTime { set; get; }
    }

    public class CheckConsigneeList
    {
        public int BranchID { get; set; }
        public string? ConsigneeLocation { set; get; }
        public string? ConsigneeID { set; get; }
    }

    public class CheckConsigneeAddressList
    {
        public int BranchID { set; get; }
        public string? ConsigneeAddress { set; get; }
        public int ConsigneeID { set; get; }
    }

    public class SelectParticularConsigneeList
    {
        public int ConsigneeID { get; set; }
        public int Language_ID { set; get; }
    }

    public class DeleteList
    {
        public string? key { set; get; }
        public string? Branch { set; get; }
        public int User_ID { set; get; }
        public int Company_ID { get; set; }
        public string? GeneralCulture { set; get; }
        public int MenuID { set; get; }
        public DateTime LoggedINDateTime { get; set; }
    }

    public class UpdateLocaleList
    {
        public string? data { set; get; }
        public int Company_ID { set; get; }
        public string? Branch { set; get; }
        public int User_ID { set; get; }
        public int MenuID { get; set; }
    }

    public class CheckConsigneeLocaleList
    {
        public int UserLanguageID { set; get; }
        public int BranchID { set; get; }
        public string? ConsigneeLocation { set; get; }
        public int ConsigneeLocaleID { set; get; }
    }

    public class CheckConsigneeAddressLocaleList
    {
        public int UserLanguageID { set; get; }
        public int BranchID { set; get; }
        public string? ConsigneeAddress { set; get; }
        public int ConsigneeLocaleID { set; get; }
    }

    public class CheckWareHouseList
    {
        public int Company_ID { set; get; }
        public int WareHouse_ID { set; get; }
        public int BranchID { get; set; }
        public int Consignee_ID { set; get; }
    }

    #endregion

    #region Response DTOs

    public class Branchs
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }

    public class ConsigneeMaster
    {
        public int Consignee_ID { get; set; }
        public string Consignee_Location { get; set; }
        public string Consignee_Address { get; set; }
        public int WareHouse_ID { get; set; }
        public string WareHouse { get; set; }
        public string Consignee_IsDefault { get; set; }
        public bool IsDefault { get; set; }
        public string Consignee_IsActive { get; set; }
    }

    public partial class GNM_Consignee
    {
        public GNM_Consignee()
        {
            this.GNM_ConsigneeLocale = new HashSet<GNM_ConsigneeLocale>();
        }

        public int Consignee_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string ConsigneeLocation { get; set; }
        public string ConsigneeAddress { get; set; }
        public int WareHouse_ID { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }

        public virtual ICollection<GNM_ConsigneeLocale> GNM_ConsigneeLocale { get; set; }
    }

    public partial class GNM_ConsigneeLocale
    {
        public int ConsigneeLocale_ID { get; set; }
        public int Consignee_ID { get; set; }
        public int Language_ID { get; set; }
        public string ConsigneeLocation { get; set; }
        public string ConsigneeAddress { get; set; }

        public virtual GNM_Consignee GNM_Consignee { get; set; }
    }

    public class GNM_WareHouse
    {
        public int WareHouse_ID { get; set; }
        public string? WareHouseName { get; set; }
    }

    public class GNM_WareHouseLocale
    {
        public int WareHouseLocale_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public int Language_ID { get; set; }
        public string? WareHouseName { get; set; }
    }

    public class ReportExportList
    {
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public System.Data.DataTable? Options { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public System.Data.DataTable? Alignment { get; set; }
        public string? FileName { get; set; }
        public string? Header { get; set; }
        public int exprtType { get; set; }
        public string? UserCulture { get; set; }
    }

    #endregion

    #region ::: Request DTOs with Configuration :::

    public class LoadBranchDropdownRequestWithConfig
    {
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class SelectConsigneeRequestWithConfig
    {
        public GetAllconsigneeList SelectConsigneeObj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; } = string.Empty;
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; } = string.Empty;
        public bool Advnce { get; set; }
        public string AdvanceFilter { get; set; } = string.Empty;
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class SaveConsigneeRequestWithConfig
    {
        public SaveList SaveObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class CheckConsigneeRequestWithConfig
    {
        public CheckConsigneeList CheckConsigneeObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class CheckConsigneeAddressRequestWithConfig
    {
        public CheckConsigneeAddressList CheckConsigneeAddressObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class SelectParticularConsigneeRequestWithConfig
    {
        public SelectParticularConsigneeList SelectParticularConsigneeObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class DeleteConsigneeRequestWithConfig
    {
        public DeleteList DeleteObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class UpdateLocaleRequestWithConfig
    {
        public UpdateLocaleList UpdateLocaleObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class CheckConsigneeLocaleRequestWithConfig
    {
        public CheckConsigneeLocaleList CheckConsigneeLocaleObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class CheckConsigneeAddressLocaleRequestWithConfig
    {
        public CheckConsigneeAddressLocaleList CheckConsigneeAddressLocaleObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class CheckWareHouseRequestWithConfig
    {
        public CheckWareHouseList CheckWareHouseObj { get; set; } = new();
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class ExportConsigneeRequestWithConfig
    {
        public GetAllconsigneeList ExportObj { get; set; } = new();
        public string Filter { get; set; } = string.Empty;
        public string AdvanceFilter { get; set; } = string.Empty;
        public string Sidx { get; set; } = string.Empty;
        public string Sord { get; set; } = string.Empty;
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    #endregion

    #region Filter DTOs (Local copies for microservice isolation)

    public class Filters
    {
        public string groupOp { get; set; }
        public List<rules> rules { get; set; }
    }

    public class rules
    {
        public string field { get; set; }
        public string op { get; set; }
        public string data { get; set; }
    }

    public class AdvanceFilter
    {
        public string groupOp { get; set; }
        public List<AdvanceFilterRules> rules { get; set; }
    }

    public class AdvanceFilterRules
    {
        public string field { get; set; }
        public string op { get; set; }
        public string data { get; set; }
    }

    #endregion
}
