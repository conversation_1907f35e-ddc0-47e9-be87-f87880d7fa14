﻿using SharedAPIClassLibrary_AMERP;
using SharedAPIClassLibrary_AMERP.Services;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreCompanyMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreCompanyMasterController : ApiController
    {

        #region:::SelectAllCompanies /Mithun:::
        /// <summary>
        /// to get all the Company Pages
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreCompanyMaster/SelectAllCompanies")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllCompanies([FromBody] SelectAllCompaniesList SelectAllCompaniesObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectAllCompanies(SelectAllCompaniesObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectParticularCompany /Mithun:::
        /// <summary>
        /// to get details of the Particular company
        /// </summary>  
        [Route("api/CoreCompanyMaster/SelectParticularCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularCompany([FromBody] SelectParticularCompanyList SelectParticularCompanyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectParticularCompany(SelectParticularCompanyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectCompanyLocaleDetails /Mithun:::
        /// <summary>
        /// to get details of the Particular company Locale details
        /// </summary> 

        [Route("api/CoreCompanyMaster/SelectCompanyLocaleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompanyLocaleDetails([FromBody] SelectCompanyLocaleDetailsList SelectCompanyLocaleDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectCompanyLocaleDetails(SelectCompanyLocaleDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: SelectCountry /Mithun:::
        /// <summary>
        /// to get all countries
        /// </summary>  
        [Route("api/CoreCompanyMaster/SelectCountry")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCountry([FromBody] SelectCountryList SelectCountryObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectCountry(SelectCountryObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Currency /Mithun:::
        /// <summary>
        /// to Select Currency
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectCurrency")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCurrency([FromBody] SelectCurrencyList SelectCurrencyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectCurrency(SelectCurrencyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectState /Mithun:::
        /// <summary>
        /// to get all the states for the selected country
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectState")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectState([FromBody] SelectStateList SelectStateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectState(SelectStateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Update Company header /Mithun:::
        /// <summary>
        /// to update the Company Header
        /// </summary> 
        [Route("api/CoreCompanyMaster/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateCoreCompanyList UpdateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.Update(UpdateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Company header /Mithun:::
        /// <summary>
        /// to Insert the Company Header
        /// </summary> 
        [Route("api/CoreCompanyMaster/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertCoreCompanyList InsertObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.Insert(InsertObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectAllBranchForACompany /Mithun:::
        /// <summary>
        /// to select all the branches of the company
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectAllBranchForACompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllBranchForACompany([FromBody] SelectAllBranchForACompanyList SelectAllBranchForACompanyObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectAllBranchForACompany(SelectAllBranchForACompanyObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectLocaleBranch /Mithun:::
        /// <summary>
        /// to select all the branch locale details
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectLocaleBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectLocaleBranch([FromBody] SelectLocaleBranchList SelectLocaleBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectLocaleBranch(SelectLocaleBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertBranchHeader /Mithun:::
        /// <summary>
        /// to insert branch header
        /// </summary> 
        [Route("api/CoreCompanyMaster/InsertBranchHeader")]
        [HttpPost]
       // [JwtTokenValidationFilter]
        public IHttpActionResult InsertBranchHeader([FromBody] InsertBranchHeaderList InsertBranchHeaderObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.InsertBranchHeader(InsertBranchHeaderObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: SelectParticularBranch /Mithun:::
        /// <summary>
        /// to select the details of particular branch for editing
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectParticularBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularBranch([FromBody] SelectParticularBranchList SelectParticularBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectParticularBranch(SelectParticularBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateBranchHeader /Mithun:::
        /// <summary>
        /// to update the branch header
        /// </summary> 
        [Route("api/CoreCompanyMaster/UpdateBranchHeader")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateBranchHeader([FromBody] UpdateBranchHeaderList UpdateBranchHeaderObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.UpdateBranchHeader(UpdateBranchHeaderObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectBranchTaxDetails /Mithun:::
        /// <summary>
        /// to select all the branch tax details
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectBranchTaxDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchTaxDetails([FromBody] SelectBranchTaxDetailsList SelectBranchTaxDetailsObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectBranchTaxDetails(SelectBranchTaxDetailsObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveBranchTaxDetail /Mithun:::
        /// <summary>
        /// to update branch tax 
        /// </summary> 
        [Route("api/CoreCompanyMaster/SaveBranchTaxDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveBranchTaxDetail([FromBody] SaveBranchTaxDetailList SaveBranchTaxDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SaveBranchTaxDetail(SaveBranchTaxDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectAllCompanyRelationships /Mithun:::
        /// <summary>
        /// to select all Relationships of company with others 
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectAllCompanyRelationships")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllCompanyRelationships([FromBody] SelectAllCompanyRelationshipsList SelectAllCompanyRelationshipsObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectAllCompanyRelationships(SelectAllCompanyRelationshipsObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveRelation /Mithun:::
        /// <summary>
        /// to update company relation details
        /// </summary> 
        [Route("api/CoreCompanyMaster/SaveRelation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveRelation([FromBody] SaveRelationList SaveRelationObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SaveRelation(SaveRelationObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectAllBrandsForaCompany /Mithun:::
        /// <summary>
        /// to select all the brands associated with the company
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectAllBrandsForaCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllBrandsForaCompany([FromBody] SelectAllBrandsForaCompanyList SelectAllBrandsForaCompanyObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectAllBrandsForaCompany(SelectAllBrandsForaCompanyObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveBrandDetails /Mithun:::
        /// <summary>
        /// to update brand association with the company
        /// </summary> 
        [Route("api/CoreCompanyMaster/SaveBrandDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveBrandDetails([FromBody] SaveBrandDetailsList SaveBrandDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SaveBrandDetails(SaveBrandDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectAllEmployeeDetails /Mithun:::
        /// <summary>
        /// to select all the employees of the company
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectAllEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllEmployeeDetails([FromBody] SelectAllEmployeeDetailsList SelectAllEmployeeDetailsObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectAllEmployeeDetails(SelectAllEmployeeDetailsObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelEmployeelocale /Mithun:::
        /// <summary>
        /// to select the employees locale detail of the company
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelEmployeelocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEmployeelocale([FromBody] SelEmployeelocaleList SelEmployeelocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelEmployeelocale(SelEmployeelocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectSpecificEmployeeDetails /Mithun:::
        /// <summary>
        /// to select the specific employee detail of the company for editing
        /// </summary> 
        [Route("api/CoreCompanyMaster/SelectSpecificEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificEmployeeDetails([FromBody] SelectSpecificEmployeeDetailsList SelectSpecificEmployeeDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectSpecificEmployeeDetails(SelectSpecificEmployeeDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertEmployeeDetails /Mithun:::
        /// <summary>
        /// to insert the employee details
        /// </summary> 
        [Route("api/CoreCompanyMaster/InsertEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertEmployeeDetails([FromBody] InsertEmployeeDetailsList InsertEmployeeDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.InsertEmployeeDetails(InsertEmployeeDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateEmployeeDetails /Mithun:::
        /// <summary>
        /// to update the employee details
        /// </summary> 
        [Route("api/CoreCompanyMaster/UpdateEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateEmployeeDetails([FromBody] UpdateEmployeeDetailsList UpdateEmployeeDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.UpdateEmployeeDetails(UpdateEmployeeDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertCompanyLocale /Mithun:::
        /// <summary>
        /// to insert the company locale details
        /// </summary>
        [Route("api/CoreCompanyMaster/InsertCompanyLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertCompanyLocale([FromBody] InsertCompanyLocaleList InsertCompanyLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.InsertCompanyLocale(InsertCompanyLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateCompanyLocale /Mithun:::
        /// <summary>
        /// to update the company locale details
        /// </summary>
        [Route("api/CoreCompanyMaster/UpdateCompanyLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateCompanyLocale([FromBody] UpdateCompanyLocaleList UpdateCompanyLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.UpdateCompanyLocale(UpdateCompanyLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertBranchLocale /Mithun:::
        /// <summary>
        /// to insert the branch locale details
        /// </summary>
        [Route("api/CoreCompanyMaster/InsertBranchLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertBranchLocale([FromBody] InsertBranchLocaleList InsertBranchLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.InsertBranchLocale(InsertBranchLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateBranchLocale /Mithun:::
        /// <summary>
        /// to update the branch locale details
        /// </summary>
        [Route("api/CoreCompanyMaster/UpdateBranchLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateBranchLocale([FromBody] UpdateBranchLocaleList UpdateBranchLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.UpdateBranchLocale(UpdateBranchLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertEmployeeLocale /Mithun:::
        /// <summary>
        /// to insert the Employee locale details
        /// </summary>
        [Route("api/CoreCompanyMaster/InsertEmployeeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertEmployeeLocale([FromBody] InsertEmployeeLocaleList InsertEmployeeLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.InsertEmployeeLocale(InsertEmployeeLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateEmployeeLocale /Mithun:::
        /// <summary>
        /// to update the Employee locale details
        /// </summary>
        [Route("api/CoreCompanyMaster/UpdateEmployeeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateEmployeeLocale([FromBody] UpdateEmployeeLocaleList UpdateEmployeeLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.UpdateEmployeeLocale(UpdateEmployeeLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteCompany /Mithun:::
        /// <summary>
        /// to Delete the Company details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteCompany([FromBody] DeleteCompanyList DeleteCompanyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteCompany(DeleteCompanyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteBranch /Mithun:::
        /// <summary>
        /// to Delete the Branch details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteBranch([FromBody] DeleteBranchList DeleteBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteBranch(DeleteBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteBranchTaxDetails /Mithun:::
        /// <summary>
        /// to Delete the Branch tax details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteBranchTaxDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteBranchTaxDetails([FromBody] DeleteBranchTaxDetailsList DeleteBranchTaxDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteBranchTaxDetails(DeleteBranchTaxDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteCompanyRelation /Mithun:::
        /// <summary>
        /// to Delete the Company Relation details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteCompanyRelation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteCompanyRelation([FromBody] DeleteCompanyRelationList DeleteCompanyRelationObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteCompanyRelation(DeleteCompanyRelationObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteCompanyBrands /Mithun:::
        /// <summary>
        /// to Delete the Company brand details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteCompanyBrands")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteCompanyBrands([FromBody] DeleteCompanyBrandsList DeleteCompanyBrandsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteCompanyBrands(DeleteCompanyBrandsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteCompanyEmployee /Mithun:::
        /// <summary>
        /// to Delete the Company Employee details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteCompanyEmployee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteCompanyEmployee([FromBody] DeleteCompanyEmployeeList DeleteCompanyEmployeeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteCompanyEmployee(DeleteCompanyEmployeeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckBranchTaxDetails /Mithun:::
        /// <summary>
        /// to check if the Tax detail has already been selected for the branch
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckBranchTaxDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckBranchTaxDetails([FromBody] CheckBranchTaxDetailsList CheckBranchTaxDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckBranchTaxDetails(CheckBranchTaxDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckCompanyBrandAssociation /Mithun:::
        /// <summary>
        /// to check if the brand is already associated with the company
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckCompanyBrandAssociation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCompanyBrandAssociation([FromBody] CheckCompanyBrandAssociationList CheckCompanyBrandAssociationObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckCompanyBrandAssociation(CheckCompanyBrandAssociationObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckCompanyRelations /Mithun:::
        /// <summary>
        /// to check if the Company is already associated with the selected company
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckCompanyRelations")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCompanyRelations([FromBody] CheckCompanyRelationsList CheckCompanyRelationsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckCompanyRelations(CheckCompanyRelationsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckEmployeeSkills /Mithun:::
        /// <summary>
        /// to check if the employee is already associated with the skills
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckEmployeeSkills")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeSkills([FromBody] CheckEmployeeSkillsList CheckEmployeeSkillsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckEmployeeSkills(CheckEmployeeSkillsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelAllEmployeeSkillset /Mithun:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>

        [Route("api/CoreCompanyMaster/SelAllEmployeeSkillset")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllEmployeeSkillset([FromBody] SelAllEmployeeSkillsetList SelAllEmployeeSkillsetObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelAllEmployeeSkillset(SelAllEmployeeSkillsetObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }

        #endregion

        #region ::: SaveSkills /Mithun:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        [Route("api/CoreCompanyMaster/SaveSkills")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveSkills([FromBody] SaveSkillsList SaveSkillsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SaveSkills(SaveSkillsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteSkills /Mithun:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>

        [Route("api/CoreCompanyMaster/DeleteSkills")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteSkills([FromBody] DeleteSkillsList DeleteSkillsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteSkills(DeleteSkillsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelAllBranchTaxCode /Mithun:::
        /// <summary>
        /// to select all the branch Tax Codes
        /// </summary>
        [Route("api/CoreCompanyMaster/SelAllBranchTaxCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllBranchTaxCode([FromBody] SelAllBranchTaxCodeList SelAllBranchTaxCodeObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelAllBranchTaxCode(SelAllBranchTaxCodeObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveBranchTaxCode /Mithun:::
        /// <summary>
        /// to update Branch tax code
        /// </summary>

        [Route("api/CoreCompanyMaster/SaveBranchTaxCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveBranchTaxCode([FromBody] SaveBranchTaxCodeList SaveBranchTaxCodeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SaveBranchTaxCode(SaveBranchTaxCodeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteBranchTaxCodes /Mithun:::
        /// <summary>
        /// to Delete the Branch Tax Codes
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteBranchTaxCodes")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteBranchTaxCodes([FromBody] DeleteBranchTaxCodesList DeleteBranchTaxCodesObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteBranchTaxCodes(DeleteBranchTaxCodesObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelAllEmployeeBranches /Mithun:::
        /// <summary>
        /// to select all the branches associated with the Employee
        /// </summary>
        [Route("api/CoreCompanyMaster/SelAllEmployeeBranches")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllEmployeeBranches([FromBody] SelAllEmployeeBranchesList SelAllEmployeeBranchesObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelAllEmployeeBranches(SelAllEmployeeBranchesObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveEmployeeBranch /Mithun:::
        /// <summary>
        /// to save Employee Branch Association
        /// </summary>
        [Route("api/CoreCompanyMaster/SaveEmployeeBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmployeeBranch([FromBody] SaveEmployeeBranchList SaveEmployeeBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SaveEmployeeBranch(SaveEmployeeBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteEmployeeBranch /Mithun:::
        /// <summary>
        /// to Delete the Employee Branch details
        /// </summary>
        [Route("api/CoreCompanyMaster/DeleteEmployeeBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteEmployeeBranch([FromBody] DeleteEmployeeBranchList DeleteEmployeeBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.DeleteEmployeeBranch(DeleteEmployeeBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckEmployeeBranch /Mithun:::
        /// <summary>
        /// to check if the employee is already associated with the branch
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckEmployeeBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeBranch([FromBody] CheckEmployeeBranchList CheckEmployeeBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckEmployeeBranch(CheckEmployeeBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckCompanyName /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckCompanyName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCompanyName([FromBody] CheckCompanyNameList CheckCompanyNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckCompanyName(CheckCompanyNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckBranchName /Mihtun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckBranchName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckBranchName([FromBody] CheckBranchNameList CheckBranchNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckBranchName(CheckBranchNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: GetBranchCount /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        [Route("api/CoreCompanyMaster/GetBranchCount")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetBranchCount([FromBody] GetBranchCountList GetBranchCountObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.GetBranchCount(GetBranchCountObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckEmployeeID /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckEmployeeID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeID([FromBody] CheckEmployeeIDList CheckEmployeeIDObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckEmployeeID(CheckEmployeeIDObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckLocaleCompanyName /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckLocaleCompanyName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckLocaleCompanyName([FromBody] CheckLocaleCompanyNameList CheckLocaleCompanyNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckLocaleCompanyName(CheckLocaleCompanyNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckLocaleBranchName :::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckLocaleBranchName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckLocaleBranchName([FromBody] CheckLocaleBranchNameList CheckLocaleBranchNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckLocaleBranchName(CheckLocaleBranchNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectAllEmployeeMastersData /Mithun:::
        /// <summary>
        /// to select masters for drop down
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectAllEmployeeMastersData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllEmployeeMastersData([FromBody] SelectAllEmployeeMastersDataList SelectAllEmployeeMastersDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectAllEmployeeMastersData(SelectAllEmployeeMastersDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectOnlyCompanyMastersData /Mihtun:::
        /// <summary>
        /// to select masters for drop down
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectOnlyCompanyMastersData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOnlyCompanyMastersData([FromBody] SelectOnlyCompanyMastersDataList SelectOnlyCompanyMastersDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectOnlyCompanyMastersData(SelectOnlyCompanyMastersDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectBranchMastersData /Mithun:::
        /// <summary>
        /// to select masters for drop down
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectBranchMastersData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchMastersData([FromBody] SelectBranchMastersDataList SelectBranchMastersDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.SelectBranchMastersData(SelectBranchMastersDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckBranchTaxDetails /Mihtun:::
        /// <summary>
        /// to check if the Tax detail has already been selected for the branch
        /// </summary>
        [Route("api/CoreCompanyMaster/CheckOrderingCostofParent")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckOrderingCostofParent([FromBody] CheckOrderingCostofParentList CheckOrderingCostofParentObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.CheckOrderingCostofParent(CheckOrderingCostofParentObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select release notes info /Mithun:::
        /// <summary>
        ///  to Select release notes info
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectReleaseNoteInformation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReleaseNoteInformation([FromBody] SelectReleaseNoteInformationList SelectReleaseNoteInformationObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectReleaseNoteInformation(SelectReleaseNoteInformationObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: GetNextReleaseInfo /Mithun:::
        /// <summary>
        /// GetNextReleaseInfo
        /// </summary>
        [Route("api/CoreCompanyMaster/GetNextReleaseInfo")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetNextReleaseInfo([FromBody] GetNextReleaseInfoList GetNextReleaseInfoObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyMasterServices.GetNextReleaseInfo(GetNextReleaseInfoObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::SelectMonthEndLog /Mithun:::
        /// <summary>
        /// SelectMonthEndLog
        /// </summary>
        [Route("api/CoreCompanyMaster/SelectMonthEndLog")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMonthEndLog([FromBody] SelectMonthEndLogList SelectMonthEndLogObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyMasterServices.SelectMonthEndLog(SelectMonthEndLogObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion


        #region::: CompanyExport /Mithun :::

        [Route("api/CoreCompanyMaster/CompanyExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> CompanyExport([FromBody] SelectAllCompaniesList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;
            try
            {

                Object Response = await CoreCompanyMasterServices.CompanyExport(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion



        #region::: BranchExport /Mithun :::

        [Route("api/CoreCompanyMaster/BranchExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> BranchExport([FromBody] SelectAllBranchForACompanyList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;
            try
            {

                Object Response = await CoreCompanyMasterServices.BranchExport(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion


        #region::: EmployeeExport /Mithun :::

        [Route("api/CoreCompanyMaster/EmployeeExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> EmployeeExport([FromBody] SelectAllEmployeeDetailsList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;
            try
            {

                Object Response = await CoreCompanyMasterServices.EmployeeExport(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion


     




    }

}
