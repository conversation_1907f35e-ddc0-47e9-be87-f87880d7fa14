using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Utilities.DTOs
{
    /// <summary>
    /// Data Transfer Object for AdvanceFilter
    /// </summary>
    public class AdvanceFilterDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the filter
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the filter
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the filter
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the collection of filter rules
        /// </summary>
        public ICollection<RulesDto> Rules { get; set; } = new List<RulesDto>();

        /// <summary>
        /// Gets or sets the logical operator between rule groups (AND/OR)
        /// </summary>
        public string? LogicalOperator { get; set; }

        /// <summary>
        /// Gets or sets whether the filter is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the creation date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the last modified date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Data Transfer Object for Rules
    /// </summary>
    public class RulesDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the rule
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the field name to filter on
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the data/value to filter with
        /// </summary>
        public string? Data { get; set; }

        /// <summary>
        /// Gets or sets the comparison operator
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the logical condition
        /// </summary>
        public string? Condition { get; set; }

        /// <summary>
        /// Gets or sets the order/sequence of this rule
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// Request DTO for creating AdvanceFilter
    /// </summary>
    public class CreateAdvanceFilterRequest
    {
        /// <summary>
        /// Gets or sets the name of the filter
        /// </summary>
        [StringLength(100)]
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the filter
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the collection of filter rules
        /// </summary>
        [Required]
        public ICollection<CreateRulesRequest> Rules { get; set; } = new List<CreateRulesRequest>();

        /// <summary>
        /// Gets or sets the logical operator between rule groups (AND/OR)
        /// </summary>
        [StringLength(10)]
        public string? LogicalOperator { get; set; } = "AND";

        /// <summary>
        /// Gets or sets whether the filter is active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Request DTO for creating Rules
    /// </summary>
    public class CreateRulesRequest
    {
        /// <summary>
        /// Gets or sets the field name to filter on
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the data/value to filter with
        /// </summary>
        [StringLength(500)]
        public string? Data { get; set; }

        /// <summary>
        /// Gets or sets the comparison operator
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the logical condition
        /// </summary>
        [StringLength(10)]
        public string? Condition { get; set; }

        /// <summary>
        /// Gets or sets the order/sequence of this rule
        /// </summary>
        [Range(0, int.MaxValue)]
        public int Order { get; set; }
    }

    /// <summary>
    /// Request DTO for updating AdvanceFilter
    /// </summary>
    public class UpdateAdvanceFilterRequest
    {
        /// <summary>
        /// Gets or sets the name of the filter
        /// </summary>
        [StringLength(100)]
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the filter
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the collection of filter rules
        /// </summary>
        public ICollection<CreateRulesRequest>? Rules { get; set; }

        /// <summary>
        /// Gets or sets the logical operator between rule groups (AND/OR)
        /// </summary>
        [StringLength(10)]
        public string? LogicalOperator { get; set; }

        /// <summary>
        /// Gets or sets whether the filter is active
        /// </summary>
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// Response DTO for filter validation and SQL generation
    /// </summary>
    public class FilterValidationResponse
    {
        /// <summary>
        /// Gets or sets whether the filter is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Gets or sets validation error messages
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the generated SQL WHERE clause (if valid)
        /// </summary>
        public string? GeneratedSql { get; set; }

        /// <summary>
        /// Gets or sets the parameters for the SQL query
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }
}
