using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace PBC.AggregatorService.DTOs
{
    #region Request DTOs (from client to gateway)
    /// <summary>
    /// Request object for checking old password (from client)
    /// </summary>
    public class CheckOldPasswordList
    {
        /// <summary>
        /// User ID to check password for
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// Encrypted old password to verify
        /// </summary>
        [Required]
        public string OldPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request object for changing user password (from client)
    /// </summary>
    public class ChangePasswordList
    {
        /// <summary>
        /// User ID whose password is being changed
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// JSON string containing encrypted old and new passwords
        /// Format: {"OldPassword":"encrypted_value","NewPassword":"encrypted_value"}
        /// </summary>
        [Required]
        [Description("JSON string with OldPassword and NewPassword fields")]
        public string data { get; set; } = string.Empty;

        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// Branch ID
        /// </summary>
        [Required]
        public int Branch { get; set; }

        /// <summary>
        /// Menu ID for audit purposes
        /// </summary>
        [Required]
        public int MenuID { get; set; }

        /// <summary>
        /// Timestamp when user logged in
        /// </summary>
        [Required]
        public DateTime LoggedINDateTime { get; set; }
    }
    #endregion

    #region Request DTOs with Configuration (from gateway to PBC.CoreService)
    /// <summary>
    /// Request object for checking old password with configuration (gateway to CoreService)
    /// </summary>
    public class CheckOldPasswordRequestWithConfig
    {
        /// <summary>
        /// User ID to check password for
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// Encrypted old password to verify
        /// </summary>
        [Required]
        public string OldPassword { get; set; } = string.Empty;

        /// <summary>
        /// Database connection string (from gateway configuration)
        /// </summary>
        [Required]
        public string ConnString { get; set; } = string.Empty;

        /// <summary>
        /// Log exception flag (from gateway configuration)
        /// </summary>
        [Required]
        public int LogException { get; set; }
    }

    /// <summary>
    /// Request object for changing user password with configuration (gateway to CoreService)
    /// </summary>
    public class ChangePasswordRequestWithConfig
    {
        /// <summary>
        /// User ID whose password is being changed
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// JSON string containing encrypted old and new passwords
        /// Format: {"OldPassword":"encrypted_value","NewPassword":"encrypted_value"}
        /// </summary>
        [Required]
        [Description("JSON string with OldPassword and NewPassword fields")]
        public string data { get; set; } = string.Empty;

        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// Branch ID
        /// </summary>
        [Required]
        public int Branch { get; set; }

        /// <summary>
        /// Menu ID for audit purposes
        /// </summary>
        [Required]
        public int MenuID { get; set; }

        /// <summary>
        /// Timestamp when user logged in
        /// </summary>
        [Required]
        public DateTime LoggedINDateTime { get; set; }

        /// <summary>
        /// Database connection string (from gateway configuration)
        /// </summary>
        [Required]
        public string ConnString { get; set; } = string.Empty;

        /// <summary>
        /// Log exception flag (from gateway configuration)
        /// </summary>
        [Required]
        public int LogException { get; set; }
    }
    #endregion
}
