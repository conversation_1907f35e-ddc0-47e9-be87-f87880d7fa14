﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\BayManagementServices\**" />
    <Compile Remove="Services\HelpDeskServices\**" />
    <Compile Remove="Services\InterfaceServices\**" />
    <Compile Remove="Services\PartsAndServiceServices\**" />
    <Compile Remove="Services\PartsServices\**" />
    <Compile Remove="Services\ServiceServices\**" />
    <Compile Remove="Services\TAMSFactoryServices\**" />
    <EmbeddedResource Remove="Services\BayManagementServices\**" />
    <EmbeddedResource Remove="Services\HelpDeskServices\**" />
    <EmbeddedResource Remove="Services\InterfaceServices\**" />
    <EmbeddedResource Remove="Services\PartsAndServiceServices\**" />
    <EmbeddedResource Remove="Services\PartsServices\**" />
    <EmbeddedResource Remove="Services\ServiceServices\**" />
    <EmbeddedResource Remove="Services\TAMSFactoryServices\**" />
    <None Remove="Services\BayManagementServices\**" />
    <None Remove="Services\HelpDeskServices\**" />
    <None Remove="Services\InterfaceServices\**" />
    <None Remove="Services\PartsAndServiceServices\**" />
    <None Remove="Services\PartsServices\**" />
    <None Remove="Services\ServiceServices\**" />
    <None Remove="Services\TAMSFactoryServices\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="App_GlobalResources\Resource_fn.Designer.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Include="SharedAPIClassLibrary_DC - Backup.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AngleSharp" Version="0.17.1" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.302.3" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.305.19" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.16.0" />
    <PackageReference Include="EPPlus" Version="7.4.1" />
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="FastMember" Version="1.5.0" />
    <PackageReference Include="HtmlSanitizer" Version="8.0.795" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PuppeteerSharp" Version="20.0.4" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.4" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="itextsharp">
      <HintPath>$(dll_base_path)\Ref_Dll\open-source\itextsharp\4.1.6.0\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="JQGridExtensions">
      <HintPath>$(dll_base_path)\Ref_Dll\open-source\JQGridExtensions\1.0.0.0\JQGridExtensions.dll</HintPath>
    </Reference>
    <Reference Include="LogSheetExporter">
      <HintPath>..\..\..\..\..\..\Ref_Dll\in-house\LogSheetExporter\1.0.0.0\LogSheetExporter.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>$(dll_base_path)\Ref_Dll\open-source\Newtonsoft.Json\13.0.1\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="App_GlobalResources\Resource_en.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resource_en.resx</DependentUpon>
    </Compile>
    <Compile Update="App_GlobalResources\Resource_Fr.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resource_Fr.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="App_GlobalResources\Resource_en.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource_en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="App_GlobalResources\Resource_Fr.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource_Fr.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
