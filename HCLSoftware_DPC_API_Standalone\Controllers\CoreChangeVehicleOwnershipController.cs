﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreChangeVehicleOwnershipServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreChangeVehicleOwnershipController : ApiController
    {


        #region ::: Select Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To select All product
        /// </summary>
        /// 
        [Route("api/CoreChangeVehicleOwnership/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreChangeVehicleOwnershipList SelectCoreChangeVehicleOwnershipobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreChangeVehicleOwnershipServices.Select(connString, SelectCoreChangeVehicleOwnershipobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveProductCustomerDetails Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To Save Product Customer Details
        /// </summary>
        /// 
        [Route("api/CoreChangeVehicleOwnership/SaveProductCustomerDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductCustomerDetails([FromBody] SaveProductCustomerDetailsCoreChangeVehicleOwnershipList SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreChangeVehicleOwnershipServices.SaveProductCustomerDetails(connString, SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectFieldSearchSerialNumber Uday Kumar J B 16-08-2024 Pending:::
        /// <summary>
        /// To Select Field Search SerialNumber
        /// </summary>
        [Route("api/CoreChangeVehicleOwnership/SelectFieldSearchSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchSerialNumber([FromBody] SelectFieldSearchSerialNumberList SelectFieldSearchSerialNumberobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreChangeVehicleOwnershipServices.SelectFieldSearchSerialNumber(connString, SelectFieldSearchSerialNumberobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Method To Get Products For Serial Uday Kumar 16-08-2024 :::
        /// <summary> 
        /// Method To Get Products For Serial
        /// </summary>
        /// 
        [Route("api/CoreChangeVehicleOwnership/GetProductForSerial")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductForSerial([FromBody] GetProductForSerialList GetProductForSerialobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreChangeVehicleOwnershipServices.GetProductForSerial(connString, GetProductForSerialobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Get Party Details Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreChangeVehicleOwnership/GetPartyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetails([FromBody] GetPartyDetailsCoreChangeVehicleOwnershipList GetPartyDetailsCoreChangeVehicleOwnershipobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreChangeVehicleOwnershipServices.GetPartyDetails(connString, GetPartyDetailsCoreChangeVehicleOwnershipobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Get Party Details by ID Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreChangeVehicleOwnership/GetPartyDetailsbyID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetailsbyID([FromBody] GetPartyDetailsbyIDaList GetPartyDetailsbyIDobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreChangeVehicleOwnershipServices.GetPartyDetailsbyID(connString, GetPartyDetailsbyIDobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Get Party Detail Grid Uday Kumar J B 16-08-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/CoreChangeVehicleOwnership/SelectPartyDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyDetailGrid([FromBody] SelectPartyDetailGridCList SelectPartyDetailGridobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreChangeVehicleOwnershipServices.SelectPartyDetailGrid(connString, SelectPartyDetailGridobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Get Customer Search Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To get Customer Search 
        /// </summary>
        /// <returns>...</returns>
        ///
        [Route("api/CoreChangeVehicleOwnership/SelectFieldSearchParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchParty([FromBody] SelectFieldSearchPartyList SelectFieldSearchPartyobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreChangeVehicleOwnershipServices.SelectFieldSearchParty(connString, SelectFieldSearchPartyobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

    }
}