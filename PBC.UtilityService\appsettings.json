{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=UtilitiesSharedAPI;Trusted_Connection=true;MultipleActiveResultSets=true", "FSMGOLD": "Data Source=(localdb)\\mssqllocaldb;Initial Catalog=UtilitiesSharedAPI;Integrated Security=True;MultipleActiveResultSets=true"}, "AppSettings": {"AMP_SP": "^Y8~JJ"}, "LogError": "1", "ServicePorts": {"CoreService": "5001", "HelpdeskService": "5002", "UtilitiesService": "5003", "AggregatorService": "5004", "WorkflowService": "5005"}, "ServiceUrls": {"CoreService": "http://localhost:5001", "HelpdeskService": "http://localhost:5002", "AggregatorService": "http://localhost:5004", "WorkflowService": "http://localhost:5005"}}