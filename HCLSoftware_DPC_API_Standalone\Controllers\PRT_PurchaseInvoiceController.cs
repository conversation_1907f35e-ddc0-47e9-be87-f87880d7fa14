﻿using SharedAPIClassLibrary_AMERP;
using SharedAPIClassLibrary_AMERP.Services;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class PRT_PurchaseInvoiceController : ApiController
    {

        #region::: Save /Vinay:::
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/PRT_PurchaseInvoice/Save")]
        [HttpPost]
         [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SavePurchaseInvoiceList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = PRT_PurchaseInvoiceServices.Save(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

      
    }
}