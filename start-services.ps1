Write-Host "Starting PBC Microservices..." -ForegroundColor Green
Write-Host ""

Write-Host "Starting PBC.UtilityService on port 5003..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd PBC.UtilityService; dotnet run --urls=http://localhost:5003"

Write-Host "Waiting 5 seconds for UtilityService to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "Starting PBC.CoreService on port 5001..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd PBC.CoreService; dotnet run --urls=http://localhost:5001"

Write-Host "Waiting 5 seconds for CoreService to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "Starting PBC.HelpdeskService on port 5002..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd PBC.HelpdeskService; dotnet run --urls=http://localhost:5002"

Write-Host "Waiting 5 seconds for HelpdeskService to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "Starting PBC.AggregatorService on port 5004..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd PBC.AggregatorService; dotnet run --urls=http://localhost:5004"

Write-Host "Waiting 5 seconds for AggregatorService to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "Starting PBC.WorkflowService on port 5005..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd PBC.WorkflowService; dotnet run --urls=http://localhost:5005"

Write-Host ""
Write-Host "Services are starting..." -ForegroundColor Green
Write-Host "- PBC.AggregatorService: http://localhost:5004/swagger" -ForegroundColor Cyan
Write-Host "- PBC.CoreService: http://localhost:5001/swagger" -ForegroundColor Cyan
Write-Host "- PBC.HelpdeskService: http://localhost:5002/swagger" -ForegroundColor Cyan
Write-Host "- PBC.UtilityService: http://localhost:5003/swagger" -ForegroundColor Cyan
Write-Host "- PBC.WorkflowService: http://localhost:5005/swagger" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
