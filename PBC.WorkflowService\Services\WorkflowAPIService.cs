using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using PBC.WorkflowService.Models;

namespace PBC.WorkflowService.Services
{
    public class WorkflowAPIService : IWorkflowAPIService
    {
        private readonly ILogger<WorkflowAPIService> _logger;
        private WorkFlowEntity? client = null;
        private GenEntities? genEnt = null;
        private SqlConnection? con;
        private SqlCommand? cmd;
        private SqlDataReader? dr;

        public WorkflowAPIService(ILogger<WorkflowAPIService> logger)
        {
            _logger = logger;
        }

        #region ::: InsertWorkFlowHistory:::
        /// <Author>"Kiran NR"</Author>
        /// <CreatedDate>"11-10-2012"</CreatedDate>
        /// <Purpose >"To insert the movement history"</Purpose>
        /// <returns></returns> 
        public void InsertWorkFlowHistory(string conn, CaseProgressObjects CPDetails, SMSTemplate SMSCutomerObj, SMSTemplate SMSAssigneeObj, int Branch_ID = 0)
        {
            try
            {
                // Initialize database contexts
                client = new WorkFlowEntity(conn);
                genEnt = new GenEntities(conn);

                int stepTypeID = 0;
                var gnmCaseProgress = client.WF_WFCase_Progress.OrderByDescending(a => a.WFCaseProgress_ID).Where(a => a.Transaction_ID == CPDetails.transactionNumber && a.WorkFlow_ID == CPDetails.workFlowID).ToList();
                if (gnmCaseProgress.Count > 0)
                {
                    stepTypeID = client.WF_WFSteps.Where(i => i.WFSteps_ID == CPDetails.NextStepID).Select(i => i.WFStepType_ID).FirstOrDefault();
                    gnmCaseProgress[0].Action_Chosen = CPDetails.actionID;
                    gnmCaseProgress[0].Action_Remarks = CPDetails.actionRemarks;
                    gnmCaseProgress[0].Actioned_By = CPDetails.actionBy;
                    gnmCaseProgress[0].Action_Time = Branch_ID != 0 ? LocalTime(conn, Branch_ID, DateTime.Now) : DateTime.Now;
                    gnmCaseProgress[0].WFNextStep_ID = CPDetails.NextStepID;

                    client.SaveChanges();
                }
                else
                {
                    stepTypeID = client.WF_WFSteps.Where(i => i.WFSteps_ID == CPDetails.currentStepID).Select(i => i.WFStepType_ID).FirstOrDefault();
                }


                string stepTypeName = client.WF_WFStepType.Where(i => i.WFStepType_ID == stepTypeID).Select(i => i.WFStepType_Nm).FirstOrDefault();
                WF_WFStepLink stepLinkrowMail = client.WF_WFStepLink.Where(i => i.Company_ID == CPDetails.CompanyID && i.WorkFlow_ID == CPDetails.workFlowID && i.FrmWFSteps_ID == CPDetails.NextStepID).FirstOrDefault();

                WF_WFStepLink stepLinkrow = client.WF_WFStepLink.Where(i => i.Company_ID == CPDetails.CompanyID && i.WorkFlow_ID == CPDetails.workFlowID && i.FrmWFSteps_ID == CPDetails.currentStepID && i.WFAction_ID == CPDetails.actionID && i.ToWFSteps_ID == CPDetails.NextStepID).FirstOrDefault();
                if (stepTypeName.ToLower() != "End".ToLower())
                {
                    WF_WFCase_Progress rowObj = new WF_WFCase_Progress();
                    rowObj.WorkFlow_ID = CPDetails.workFlowID;
                    rowObj.Transaction_ID = CPDetails.transactionNumber;
                    rowObj.WFSteps_ID = CPDetails.NextStepID;
                    rowObj.Addresse_ID = CPDetails.AssignTo;
                    rowObj.Addresse_Flag = CPDetails.addresseType;
                    rowObj.Received_Time = Branch_ID != 0 ? LocalTime(conn, Branch_ID, DateTime.Now) : DateTime.Now; //CPDetails.receivedTime;
                    client.WF_WFCase_Progress.Add(rowObj);
                    client.SaveChanges();
                }

                if (CPDetails.currentStepID != CPDetails.NextStepID)
                {
                    if (stepTypeName.ToLower() != "Static".ToLower())
                    {
                        if (stepLinkrow != null)
                        {
                            if (stepLinkrow.IsSMSSentToAddressee)
                            {
                                if (stepLinkrowMail != null)
                                {
                                    if (stepLinkrowMail.Addresse_Flag == 1)
                                    {
                                        int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == CPDetails.AssignTo).Select(i => i.Employee_ID).FirstOrDefault();
                                        string mobileNumber = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_MobileNumber).FirstOrDefault();

                                        if (mobileNumber != null && mobileNumber.Trim() != "")//Added by Shashi
                                        {
                                            WF_Sms newSMSRow = new WF_Sms();
                                            newSMSRow.Sms_Text = CPDetails.smsTextAddressee;
                                            newSMSRow.Sms_Queue_Date = Branch_ID != 0 ? LocalTime(conn, Branch_ID, DateTime.Now) : DateTime.Now;
                                            newSMSRow.Sms_Mobile_Number = mobileNumber;
                                            newSMSRow.Parameter1_value = SMSAssigneeObj.Param1;
                                            newSMSRow.Parameter2_value = SMSAssigneeObj.Param2;
                                            newSMSRow.Parameter3_value = SMSAssigneeObj.Param3;
                                            newSMSRow.Parameter4_value = SMSAssigneeObj.Param4;
                                            newSMSRow.Template_ID = SMSAssigneeObj.Template_ID;
                                            client.WF_Sms.Add(newSMSRow);
                                            client.SaveChanges();
                                        }
                                    }
                                }
                                //else if (stepLinkrowMail.Addresse_Flag == 0)
                                //{
                                //    List<ResultForAction> ResAction = GetListOfStaffForaRoleID(CPDetails.CompanyID, CPDetails.RoleID, 0, CPDetails.currentStepID, CPDetails.actionID);

                                //    foreach (var c in ResAction)
                                //    {
                                //        int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == c.ID).Select(i => i.Employee_ID).FirstOrDefault();
                                //        string mobileNumber = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_MobileNumber).FirstOrDefault();

                                //        WF_Sms newSMSRow = new WF_Sms();
                                //        newSMSRow.Sms_Text = CPDetails.smsTextAddressee;
                                //        newSMSRow.Sms_Queue_Date = DateTime.Now;
                                //        newSMSRow.Sms_Mobile_Number = mobileNumber;
                                //        client.WF_Sms.Add(newSMSRow);
                                //        client.SaveChanges();
                                //    }
                                //}
                            }
                            if (stepLinkrow.IsSMSSentToCustomer)
                            {
                                if (CPDetails.customerMobileNumber != null && CPDetails.customerMobileNumber.Trim() != "")//Added by Shashi
                                {
                                    WF_Sms newSMSRow = new WF_Sms();
                                    newSMSRow.Sms_Text = CPDetails.smsTextCustomer;
                                    newSMSRow.Sms_Queue_Date = Branch_ID != 0 ? LocalTime(conn, Branch_ID, DateTime.Now) : DateTime.Now;
                                    newSMSRow.Sms_Mobile_Number = CPDetails.customerMobileNumber;
                                    newSMSRow.Parameter1_value = SMSCutomerObj.Param1;
                                    newSMSRow.Parameter2_value = SMSCutomerObj.Param2;
                                    newSMSRow.Parameter3_value = SMSCutomerObj.Param3;
                                    newSMSRow.Parameter4_value = SMSCutomerObj.Param4;
                                    newSMSRow.Template_ID = SMSCutomerObj.Template_ID;
                                    client.WF_Sms.Add(newSMSRow);
                                    client.SaveChanges();
                                }
                            }
                            if (stepLinkrow.IsEmailSentToAddresse)
                            {
                                if (stepLinkrowMail != null)
                                {
                                    if (stepLinkrowMail.Addresse_Flag == 1)
                                    {
                                        int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == CPDetails.AssignTo).Select(i => i.Employee_ID).FirstOrDefault();
                                        string emailID = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_Email).FirstOrDefault();

                                        WF_Email newEmailRow = new WF_Email();
                                        newEmailRow.Email_To = emailID;
                                        newEmailRow.Email_Subject = CPDetails.emailSubAddressee;
                                        newEmailRow.Email_Body = CPDetails.emailBodyAddress;
                                        //added by Kavitha-start
                                        newEmailRow.Email_Bcc = CPDetails.AddresseBcc;
                                        newEmailRow.Email_cc = CPDetails.AddresseCC;
                                        newEmailRow.Email_IsError = false;
                                        newEmailRow.NoOfAttempts = 0;
                                        //added by Kavitha-end
                                        newEmailRow.Email_Queue_Date = Branch_ID != 0 ? LocalTime(conn, Branch_ID, DateTime.Now) : DateTime.Now;
                                        client.WF_Email.Add(newEmailRow);
                                        client.SaveChanges();
                                    }
                                }
                                //else if (stepLinkrowMail.Addresse_Flag == 0)
                                //{
                                //    List<ResultForAction> ResAction = GetListOfStaffForaRoleID(CPDetails.CompanyID, CPDetails.RoleID, 0, CPDetails.currentStepID, CPDetails.actionID);

                                //    foreach (var c in ResAction)
                                //    {
                                //        int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == c.ID).Select(i => i.Employee_ID).FirstOrDefault();
                                //        string emailID = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_Email).FirstOrDefault();

                                //        WF_Email newEmailRow = new WF_Email();
                                //        newEmailRow.Email_To = emailID;
                                //        newEmailRow.Email_Subject = CPDetails.emailSubAddressee;
                                //        newEmailRow.Email_Body = CPDetails.emailBodyAddress;
                                //        newEmailRow.Email_Queue_Date = DateTime.Now;
                                //        client.WF_Email.Add(newEmailRow);
                                //        client.SaveChanges();
                                //    }
                                //}

                            }
                            if (stepLinkrow.IsEmailSentToCustomer)
                            {
                                WF_Email newEmailRow = new WF_Email();
                                newEmailRow.Email_To = CPDetails.customerEmailID;
                                newEmailRow.Email_Subject = CPDetails.emailSubCustomer;
                                newEmailRow.Email_Body = CPDetails.emailBodyCustomer;
                                //added by Kavitha-start
                                newEmailRow.Email_Bcc = CPDetails.CustomerBcc;
                                newEmailRow.Email_cc = CPDetails.customerCC;
                                newEmailRow.Email_IsError = false;
                                newEmailRow.NoOfAttempts = 0;
                                //added by Kavitha-end
                                newEmailRow.Email_Queue_Date = Branch_ID != 0 ? LocalTime(conn, Branch_ID, DateTime.Now) : DateTime.Now;
                                client.WF_Email.Add(newEmailRow);
                                client.SaveChanges();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InsertWorkFlowHistory: {ErrorMessage}", ex.Message);
                throw; // Re-throw to let the caller handle it
            }
            finally
            {
                // Dispose of database contexts
                client?.Dispose();
                genEnt?.Dispose();
            }
        }

        #endregion
        public DateTime LocalTime(string Conn, int Branch_ID, DateTime servertime)
        {
            int Timezoneid = 0;
            string StandardTimeZone = "";
            DateTime localtime = DateTime.Now;

            try
            {

                using (SqlConnection con = new SqlConnection(Conn))
                {
                    cmd = new SqlCommand("select TimeZoneID from GNM_Branch where Branch_ID='" + Branch_ID + "'", con);
                    con.Open();
                    Timezoneid = Convert.ToInt32(cmd.ExecuteScalar());
                    con.Close();
                }
                try
                {
                    using (SqlConnection con = new SqlConnection(Conn))
                    {
                        cmd = new SqlCommand("select RefMasterDetail_Name from GNM_RefMasterDetail where RefMasterDetail_ID='" + Timezoneid + "'", con);
                        con.Open();
                        StandardTimeZone = cmd.ExecuteScalar().ToString();
                        con.Close();
                    }

                    localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return localtime;
        }
        public bool CheckIsInvokeChildObject(int companyID, int workFlowID, int stepID, int ActionID, int tostepid)
        {
            bool result = false;

            WF_WFStepLink StepL = null;
            StepL = client.WF_WFStepLink.Where(a => a.Company_ID == companyID && a.WorkFlow_ID == workFlowID && a.FrmWFSteps_ID == stepID && a.WFAction_ID == ActionID && a.ToWFSteps_ID == tostepid).FirstOrDefault();
            result = (StepL.InvokeChildObject_ID == null) ? false : true;
            return result;
        }
        public string InvokeChildAction(int steplinkID)
        {

            WF_WFStepLink CurrentStepL = null;
            int objectID = 0;
            string ObjAction = string.Empty;

            string ObjectName = string.Empty;
            WF_WFChildActions act = null;
            try
            {
                CurrentStepL = client.WF_WFStepLink.Where(a => a.WFStepLink_ID == steplinkID).FirstOrDefault();
                objectID = Convert.ToInt32(CurrentStepL.InvokeChildObject_ID);
                act = client.WF_WFChildActions.Where(i => i.ChildActions_ID == CurrentStepL.InvokeChildObjectAction).FirstOrDefault();
                ObjAction = (act == null) ? "" : act.Actions_Name;
                ObjectName = genEnt.WF_Object.Where(m => m.Object_ID == objectID).Select(m => m.Object_Name).FirstOrDefault();
                ObjectName = ObjectName + "/" + ObjAction;
            }
            catch (Exception ex)
            {
                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in InvokeChildAction: {ErrorMessage}", ex.Message);
            }

            return ObjectName;
        }
    }
}
