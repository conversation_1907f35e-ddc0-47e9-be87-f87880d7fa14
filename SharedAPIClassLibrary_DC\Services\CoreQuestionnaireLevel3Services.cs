﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreQuestionnaireLevel3Services
    {
        static string AppPath = String.Empty;


        #region SelectReferenceMaster vinay n
        /// <summary>
        /// SelectReferenceMaster
        /// </summary>
        /// <param name="SelectReferenceMasterObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterLista SelectReferenceMasterObj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            List<QuestionnaireLevelData> masterDataList = new List<QuestionnaireLevelData>();
            int isDefault = 0;
            try
            {
                int CompanyID = Convert.ToInt32(SelectReferenceMasterObj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectReferenceMasterQuestionnaireLevel3";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageCode", SelectReferenceMasterObj.UserLanguageCode.ToString());
                            command.Parameters.AddWithValue("@GeneralLanguageCode", SelectReferenceMasterObj.GeneralLanguageCode.ToString());
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    masterDataList.Add(Data);
                                }

                                if (reader.NextResult() && reader.Read())
                                {
                                    isDefault = reader.GetInt32(reader.GetOrdinal("Isdefault"));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                Masterdata = new
                {
                    ReferenceMasterData = masterDataList,
                    Isdefault = isDefault
                };

                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion
        #region SelectQuestionnaireLevel1Master vinay n
        /// <summary>
        /// SelectQuestionnaireLevel1Master
        /// </summary>
        /// <param name="SelectQuestionnaireLevel1MasterObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectQuestionnaireLevel1Master(SelectQuestionnaireLevel1MasterList SelectQuestionnaireLevel1MasterObj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            List<QuestionnaireLevelData> masterDataList = new List<QuestionnaireLevelData>();
            int CompanyID = Convert.ToInt32(SelectQuestionnaireLevel1MasterObj.Company_ID);
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectQuestionnaireLevel1Master_QuestionnaireLevel3";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@IssueAreaID", SelectQuestionnaireLevel1MasterObj.IssueAreaID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageID", SelectQuestionnaireLevel1MasterObj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(SelectQuestionnaireLevel1MasterObj.GeneralLanguageID));

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    masterDataList.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                Masterdata = new
                {
                    QuestionnaireLevel1MasterData = masterDataList,

                };
                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion
        #region SelectQuestionnaireLevel2Master vinay n
        /// <summary>
        /// SelectQuestionnaireLevel2Master
        /// </summary>
        /// <param name="SelectQuestionnaireLevel2MasterObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectQuestionnaireLevel2Master(SelectQuestionnaireLevel2MasterList SelectQuestionnaireLevel2MasterObj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            int CompanyID = Convert.ToInt32(SelectQuestionnaireLevel2MasterObj.Company_ID);
            List<QuestionnaireLevelData> masterDataList = new List<QuestionnaireLevelData>();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectQuestionnaireLevel2Master_QuestionnaireLevel3";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@IssueAreaID", SelectQuestionnaireLevel2MasterObj.IssueAreaID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel1ID", SelectQuestionnaireLevel2MasterObj.QuestionnaireLevel1ID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageID", SelectQuestionnaireLevel2MasterObj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(SelectQuestionnaireLevel2MasterObj.GeneralLanguageID));

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    masterDataList.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                Masterdata = new
                {
                    QuestionnaireLevel2MasterData = masterDataList,

                };

                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion
        #region Select vinay n
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="SelectQuestionnaireLevel3ListObj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Select(SelectQuestionnaireLevel3List SelectQuestionnaireLevel3ListObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException)
        {

            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectQuestionnaireLevel3ListObj.Company_ID);
                List<QuestionnaireLevel3> masterDataList = new List<QuestionnaireLevel3>();

                IQueryable<QuestionnaireLevel3> IQQuestionnaireLevel3 = null;
                IEnumerable<QuestionnaireLevel3> IEQuestionnaireLevel3Array = null;
                string YesE = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.UserCulture.ToString(), "no").ToString();


                var jsonData = default(dynamic);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SELECTQuestionnaireLevel3";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@IssueAreaID", SelectQuestionnaireLevel3ListObj.IssueAreaID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel1ID", SelectQuestionnaireLevel3ListObj.QuestionnaireLevel1ID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel2ID", SelectQuestionnaireLevel3ListObj.QuestionnaireLevel2ID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageID", SelectQuestionnaireLevel3ListObj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(SelectQuestionnaireLevel3ListObj.GeneralLanguageID));
                            command.Parameters.AddWithValue("@YesE", YesE);
                            command.Parameters.AddWithValue("@NoE", NoE);
                            command.Parameters.AddWithValue("@YesL", YesL);
                            command.Parameters.AddWithValue("@NoL", NoL);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevel3
                                    {
                                        QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] != DBNull.Value ? (int)reader["QuestionaryLevel3_ID"] : default(int),
                                        QuestionLevel3 = reader["QuestionLevel3"] != DBNull.Value ? (string)reader["QuestionLevel3"] : string.Empty,
                                        QuestionLevel3_IsActive = reader["QuestionLevel3_IsActive"] != DBNull.Value ? (string)reader["QuestionLevel3_IsActive"] : string.Empty
                                    };
                                    masterDataList.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                IQQuestionnaireLevel3 = masterDataList.AsQueryable<QuestionnaireLevel3>();

                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQQuestionnaireLevel3 = IQQuestionnaireLevel3.FilterSearch<QuestionnaireLevel3>(filtersObj);
                }
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse((decodedValue)).ToObject<AdvanceFilter>();
                    IQQuestionnaireLevel3 = IQQuestionnaireLevel3.AdvanceSearch<QuestionnaireLevel3>(advnfilter);
                }

                IQQuestionnaireLevel3 = IQQuestionnaireLevel3.OrderByField<QuestionnaireLevel3>(sidx, sord);

                if (SelectQuestionnaireLevel3ListObj.LanguageID == Convert.ToInt32(SelectQuestionnaireLevel3ListObj.GeneralLanguageID))
                {
                    //Session["IQQuestionnaireLevel3"] = IQQuestionnaireLevel3.AsEnumerable();
                }

                Count = IQQuestionnaireLevel3.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                //Added by Ravi on 05-Jan-2014 for HelpDesk QA Corrections
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                //---End
                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQQuestionnaireLevel3.AsEnumerable()
                            select new
                            {
                                ID = a.QuestionaryLevel3_ID,
                                edit = "<img id='" + a.QuestionaryLevel3_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.QuestionaryLevel3_ID + "' class='QuestionnaireLevel3Edit' editmode='false'/>",
                                delete = "<input type='checkbox' key='" + a.QuestionaryLevel3_ID + "' defaultchecked=''  id='chk" + a.QuestionaryLevel3_ID + "' class='QuestionnaireLevel3Delete'/>",
                                QuestionLevel3 = a.QuestionLevel3,
                                QuestionLevel3_IsActive = a.QuestionLevel3_IsActive,
                                Locale = "<img key='" + a.QuestionaryLevel3_ID + "' src='" + AppPath + "/Content/local.png' class='QuestionnaireLevel3Locale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                View = "<img id='" + a.QuestionaryLevel3_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.QuestionaryLevel3_ID + "' class='ViewQuestionnaireLevel3Locale'/>",
                            }).ToList().Paginate(page, rows),
                    records = Count
                };

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion
        #region SelectParticularQuestionnaireLevel3 vinay n
        /// <summary>
        /// SelectParticularQuestionnaireLevel3
        /// </summary>
        /// <param name="SelectParticularQuestionnaireLevel3Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectParticularQuestionnaireLevel3(SelectParticularQuestionnaireLevel3List SelectParticularQuestionnaireLevel3Obj, string connString, int LogException)
        {
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularQuestionnaireLevel3Obj.UserLanguageID);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectParticularQuestionnaireLevel3QuestionnaireLevel3";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionnaireLevel3ID", SelectParticularQuestionnaireLevel3Obj.QuestionnaireLevel3ID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    x = new
                                    {
                                        QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] != DBNull.Value ? Convert.ToInt32(reader["QuestionaryLevel3_ID"]) : (int?)null,
                                        QuestionLevel3 = reader["QuestionLevel3"] != DBNull.Value ? reader["QuestionLevel3"].ToString() : string.Empty,
                                        QuestionLevel3_IsActive = reader["QuestionLevel3_IsActive"] != DBNull.Value ? (bool)reader["QuestionLevel3_IsActive"] : (bool?)null,
                                        QuestionaryLevel2_ID = reader["QuestionaryLevel2_ID"] != DBNull.Value ? Convert.ToInt32(reader["QuestionaryLevel2_ID"]) : (int?)null,
                                        QuestionaryLevel1_ID = reader["QuestionaryLevel1_ID"] != DBNull.Value ? Convert.ToInt32(reader["QuestionaryLevel1_ID"]) : (int?)null,
                                        IssueArea_ID = reader["IssueArea_ID"] != DBNull.Value ? Convert.ToInt32(reader["IssueArea_ID"]) : (int?)null,
                                        QuestionnaireLevel3LocaleID = reader["QuestionnaireLevel3LocaleID"] != DBNull.Value ? reader["QuestionnaireLevel3LocaleID"].ToString() : string.Empty,
                                        QuestionnaireLevel3LocaleName = reader["QuestionnaireLevel3LocaleName"] != DBNull.Value ? reader["QuestionnaireLevel3LocaleName"].ToString() : string.Empty
                                    };
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            return new JsonResult(x);
        }
        #endregion
        #region Save vinay n
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="SaveQuestionnaireLevel3ListObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        public static IActionResult Save(SaveQuestionnaireLevel3List SaveQuestionnaireLevel3ListObj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {

                var Operation = string.Empty;
                int QuestionaryLevel3ID = 0;
                var QuestionLev3 = string.Empty;

                var jObj = JObject.Parse(SaveQuestionnaireLevel3ListObj.data);
                int Count = jObj["rows"].Count();
                for (int i = 0; i < Count; i++)
                {
                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionaryLevel2_ID"]);
                    jTR.Read();
                    int QuestionaryLevel2_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionaryLevel1_ID"]);
                    jTR.Read();
                    int QuestionaryLevel1_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IssueArea_ID"]);
                    jTR.Read();
                    int IssueArea_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionaryLevel3_ID"]);
                    jTR.Read();
                    int QuestionaryLevel3_ID = Convert.ToInt32(jTR.Value.ToString() == "" ? "0" : jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionLevel3"]);
                    jTR.Read();
                    string QuestionLevel3 = jTR.Value.ToString();

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionLevel3_IsActive"]);
                    jTR.Read();
                    bool QuestionLevel3_IsActive = Convert.ToBoolean(jTR.Value.ToString());
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_InsertUpdate_AM_ERP_SaveQuestionnaireLevel3";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@QuestionaryLevel3_ID", QuestionaryLevel3_ID);
                                command.Parameters.AddWithValue("@QuestionLevel3", QuestionLevel3);
                                command.Parameters.AddWithValue("@QuestionaryLevel2_ID", QuestionaryLevel2_ID);
                                command.Parameters.AddWithValue("@QuestionLevel3_IsActive", QuestionLevel3_IsActive);
                                command.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(SaveQuestionnaireLevel3ListObj.Company_ID));
                                command.Parameters.AddWithValue("@IssueArea_ID", IssueArea_ID);
                                command.Parameters.AddWithValue("@QuestionaryLevel1_ID", QuestionaryLevel1_ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {

                                        Operation = reader["Operation"].ToString();
                                        QuestionaryLevel3ID = Convert.ToInt32(reader["QuestionaryLevel3_ID"]);
                                        QuestionLev3 = reader["QuestionLevel3"].ToString();

                                    }



                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    //gbl.InsertGPSDetails(Convert.ToInt32(SaveQuestionnaireLevel3ListObj.Company_ID.ToString()), Convert.ToInt32(SaveQuestionnaireLevel3ListObj.Branch), SaveQuestionnaireLevel3ListObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel3Master"), QuestionaryLevel3ID, 0, 0, Operation + QuestionLev3 + "", false, Convert.ToInt32(SaveQuestionnaireLevel3ListObj.MenuID), Convert.ToDateTime(SaveQuestionnaireLevel3ListObj.LoggedINDateTime));



                }
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);

        }
        #endregion
        #region UpdateLocale vinay n
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// <param name="UpdateLocaleQuestionnaireLevel3Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static ActionResult UpdateLocale(UpdateLocaleQuestionnaireLevel3List UpdateLocaleQuestionnaireLevel3Obj, string connString, int LogException)
        {
            int QuestionLevel3LocaleID = 0;
            var x = new { QuestionLevel3LocaleID = QuestionLevel3LocaleID };

            try
            {
                var jObj = JObject.Parse(UpdateLocaleQuestionnaireLevel3Obj.data);
                HD_QuestionnaireLevel3Locale QLRow = jObj.ToObject<HD_QuestionnaireLevel3Locale>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand("UP_Ins_AM_ERP_UpdateQuestionnaireLevel3Locale", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    SqlParameter parameterID = command.Parameters.Add("@QuestionnaireLevel3Locale_ID", SqlDbType.Int);
                    SqlParameter parameterQuestionLevel3 = command.Parameters.Add("@QuestionLevel3", SqlDbType.NVarChar, -1);
                    SqlParameter QuestionLevel3_ID = command.Parameters.Add("@QuestionLevel3_ID", SqlDbType.Int);
                    SqlParameter Language_ID = command.Parameters.Add("@Language_ID", SqlDbType.Int);

                    parameterID.Value = QLRow.QuestionnaireLevel3Locale_ID;
                    parameterQuestionLevel3.Value = QLRow.QuestionLevel3;
                    QuestionLevel3_ID.Value = QLRow.QuestionLevel3_ID;
                    Language_ID.Value = QLRow.Language_ID;




                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            QuestionLevel3LocaleID = Convert.ToInt32(reader[0]);
                        }
                    }
                }


                //gbl.InsertGPSDetails(Convert.ToInt32(UpdateLocaleQuestionnaireLevel3Obj.Company_ID), Convert.ToInt32(UpdateLocaleQuestionnaireLevel3Obj.Branch), Convert.ToInt32(UpdateLocaleQuestionnaireLevel3Obj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreQuestionnaireLevel3Master")), QLRow.QuestionLevel3_ID, 0, 0, "Update", false, Convert.ToInt32(UpdateLocaleQuestionnaireLevel3Obj.MenuID));

                x = new { QuestionLevel3LocaleID = QuestionLevel3LocaleID };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(x);
        }
        #endregion
        #region CheckQuestionLevel3 vinay n
        /// <summary>
        /// CheckQuestionLevel3
        /// </summary>
        /// <param name="CheckQuestionLevel3Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static ActionResult CheckQuestionLevel3(CheckQuestionLevel3List CheckQuestionLevel3Obj, string connString, int LogException)
        {
            int Count = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand("UP_Chk_AM_ERP_CheckQuestionLevel3Locale", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Parameters
                    command.Parameters.AddWithValue("@IssueAreaID", CheckQuestionLevel3Obj.IssueAreaID);
                    command.Parameters.AddWithValue("@QuestionLevel1ID", CheckQuestionLevel3Obj.QuestionLevel1ID);
                    command.Parameters.AddWithValue("@QuestionLevel2ID", CheckQuestionLevel3Obj.QuestionLevel2ID);
                    command.Parameters.AddWithValue("@QuestionaireLevel3", CheckQuestionLevel3Obj.QuestionLevel3);
                    command.Parameters.AddWithValue("@QuestionLevel3LocaleID", CheckQuestionLevel3Obj.QuestionLevel3LocaleID);
                    command.Parameters.AddWithValue("@LanguageID", CheckQuestionLevel3Obj.Language_ID);

                    // Output parameter to retrieve the count
                    SqlParameter countParameter = new SqlParameter("@Count", SqlDbType.Int);
                    countParameter.Direction = ParameterDirection.Output;
                    command.Parameters.Add(countParameter);

                    // Execute the command
                    command.ExecuteNonQuery();

                    // Retrieve the count from the output parameter
                    Count = Convert.ToInt32(countParameter.Value);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion
        #region Delete vinay n
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DeleteQuestionLevel3Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Delete(DeleteQuestionLevel3List DeleteQuestionLevel3Obj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {
                var jObj = JObject.Parse(DeleteQuestionLevel3Obj.key);
                int Count = jObj["rows"].Count();

                HD_QuestionaryLevel3 deleteRow = null;
                HD_QuestionnaireLevel3Locale deletelocaleRow = null;
                int ID = 0;

                for (int i = 0; i < Count; i++)
                {
                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Del_AM_ERP_DeleteQuestionaryLevel3";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ID", ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteScalar();


                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(Convert.ToInt32(DeleteQuestionLevel3Obj.Company_ID.ToString()), Convert.ToInt32(DeleteQuestionLevel3Obj.Branch), DeleteQuestionLevel3Obj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel1Master"), ID, 0, 0, "Deleted " + ID, false, Convert.ToInt32(DeleteQuestionLevel3Obj.MenuID), Convert.ToDateTime(DeleteQuestionLevel3Obj.LoggedINDateTime));
                }

                Msg += CommonFunctionalities.GetResourceString(DeleteQuestionLevel3Obj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetResourceString(DeleteQuestionLevel3Obj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    Msg += ex.Message;
                }
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region CheckQuestionnaireLevel3 vinay n
        /// <summary>
        /// CheckQuestionnaireLevel3
        /// </summary>
        /// <param name="CheckQuestionnaireLevel3Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckQuestionnaireLevel3(CheckQuestionnaireLevel3List CheckQuestionnaireLevel3Obj, string connString, int LogException)
        {
            int Count = 0;
            int CompanyID = Convert.ToInt32(CheckQuestionnaireLevel3Obj.Company_ID);
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Chk_AM_ERP_CheckQuestionnaireLevel3";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.Add(new SqlParameter("@QuestionnaireLevel2ID", CheckQuestionnaireLevel3Obj.QuestionnaireLevel2ID));
                        command.Parameters.Add(new SqlParameter("@QuestionLevel3", CheckQuestionnaireLevel3Obj.QuestionLevel3));
                        command.Parameters.Add(new SqlParameter("@QuestionnaireLevel3ID", CheckQuestionnaireLevel3Obj.QuestionnaireLevel3ID));
                        command.Parameters.Add(new SqlParameter("@IssueArea_ID", CheckQuestionnaireLevel3Obj.IssueArea_ID));
                        command.Parameters.Add(new SqlParameter("@QuestionaryLevel1_ID", CheckQuestionnaireLevel3Obj.QuestionaryLevel1_ID));
                        command.Parameters.Add(new SqlParameter("@CompanyID", CompanyID));


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        // Execute the command and read the result
                        SqlDataReader reader = command.ExecuteReader();
                        if (reader.Read())
                        {
                            Count = Convert.ToInt32(reader["ResultCode"]);
                        }


                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                }
                return new JsonResult(Count);
            }
        }
        #endregion


        #region ::: Export :::
        public static List<QuestionnaireLevel3> SelectList(SelectQuestionnaireLevel3List SelectQuestionnaireLevel3ListObj, string constring, int LogException)
        {

            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectQuestionnaireLevel3ListObj.Company_ID);
                List<QuestionnaireLevel3> masterDataList = new List<QuestionnaireLevel3>();

                IQueryable<QuestionnaireLevel3> IQQuestionnaireLevel3 = null;
                IEnumerable<QuestionnaireLevel3> IEQuestionnaireLevel3Array = null;
                string YesE = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectQuestionnaireLevel3ListObj.UserCulture.ToString(), "no").ToString();


                var jsonData = default(dynamic);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_SELECT_AM_ERP_SELECTQuestionnaireLevel3";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@IssueAreaID", SelectQuestionnaireLevel3ListObj.IssueAreaID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel1ID", SelectQuestionnaireLevel3ListObj.QuestionnaireLevel1ID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel2ID", SelectQuestionnaireLevel3ListObj.QuestionnaireLevel2ID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageID", SelectQuestionnaireLevel3ListObj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(SelectQuestionnaireLevel3ListObj.GeneralLanguageID));
                            command.Parameters.AddWithValue("@YesE", YesE);
                            command.Parameters.AddWithValue("@NoE", NoE);
                            command.Parameters.AddWithValue("@YesL", YesL);
                            command.Parameters.AddWithValue("@NoL", NoL);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevel3
                                    {
                                        QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] != DBNull.Value ? (int)reader["QuestionaryLevel3_ID"] : default(int),
                                        QuestionLevel3 = reader["QuestionLevel3"] != DBNull.Value ? (string)reader["QuestionLevel3"] : string.Empty,
                                        QuestionLevel3_IsActive = reader["QuestionLevel3_IsActive"] != DBNull.Value ? (string)reader["QuestionLevel3_IsActive"] : string.Empty
                                    };
                                    masterDataList.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                IQQuestionnaireLevel3 = masterDataList.AsQueryable<QuestionnaireLevel3>();

                if (SelectQuestionnaireLevel3ListObj.filters != "null" && SelectQuestionnaireLevel3ListObj.filters != "undefined")
                {
                    string decodedValue = Uri.UnescapeDataString(SelectQuestionnaireLevel3ListObj.filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQQuestionnaireLevel3 = IQQuestionnaireLevel3.FilterSearch<QuestionnaireLevel3>(filtersObj);
                }
                if (SelectQuestionnaireLevel3ListObj.Query != "null" && SelectQuestionnaireLevel3ListObj.Query != "undefined")
                {
                    string decodedValue = Uri.UnescapeDataString(SelectQuestionnaireLevel3ListObj.Query);
                    AdvanceFilter advnfilter = JObject.Parse((decodedValue)).ToObject<AdvanceFilter>();
                    IQQuestionnaireLevel3 = IQQuestionnaireLevel3.AdvanceSearch<QuestionnaireLevel3>(advnfilter);
                }

                IQQuestionnaireLevel3 = IQQuestionnaireLevel3.OrderByField<QuestionnaireLevel3>(SelectQuestionnaireLevel3ListObj.sidx, SelectQuestionnaireLevel3ListObj.sord);





                return IQQuestionnaireLevel3.ToList();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
        }
        public static async Task<object> Export(SelectQuestionnaireLevel3List ExportObj, string connString, int LogException)
        {
            IQueryable<QuestionnaireLevel3> IQQuestionnaireLevel3 = null;
            int Count = 0;
            DataTable Dt = new DataTable();
            try
            {

                List<QuestionnaireLevel3> IQQuestionnaireLevel3List = ((List<QuestionnaireLevel3>)SelectList(ExportObj, connString, LogException));
                IQQuestionnaireLevel3 = IQQuestionnaireLevel3List.AsQueryable();
                var QuestionnaireLevel3Array = from a in IQQuestionnaireLevel3.AsEnumerable()
                                               select new
                                               {
                                                   a.QuestionLevel3,
                                                   a.QuestionLevel3_IsActive
                                               };

                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "QuestionnaireLevel3").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "active").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Questionnaire Level3");
                DtAlignment.Columns.Add("Is Active?");

                DtAlignment.Rows.Add(0, 0);

                Count = QuestionnaireLevel3Array.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        Dt.Rows.Add(QuestionnaireLevel3Array.ElementAt(i).QuestionLevel3, QuestionnaireLevel3Array.ElementAt(i).QuestionLevel3_IsActive);
                    }

                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea").ToString());
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel1").ToString());
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel2").ToString());
                    Dt1.Rows.Add(Common.DecryptString(ExportObj.IssueArea), Common.DecryptString(ExportObj.QuestionnaireLevel1), Common.DecryptString((ExportObj.QuestionnaireLevel2Description)));

                    //ReportExport.Export(ExportObj.exprtType, Dt, Dt1, DtAlignment, "QuestionnaireLevel3", CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel3").ToString());
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "QuestionnaireLevel3", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel3").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };

                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), Convert.ToInt32(ExportObj.Branch), ExportObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel3Master"), 0, 0, 0, "Questionnaire Lvevel3-Export ", false, Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }

        #endregion
    }
    #region ::: CoreQuestionnaireLevel3ListsAndObjectClasses :::
    /// <summary>
    /// CoreQuestionnaireLevel3ListsAndObjectClasses
    /// </summary>
    /// 
    public class SelectReferenceMasterLista
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
    }
    public class SelectQuestionnaireLevel1MasterList
    {
        public int Company_ID { get; set; }
        public int IssueAreaID { get; set; }
        public int LanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }
    public class SelectQuestionnaireLevel2MasterList
    {
        public int Company_ID { get; set; }
        public int IssueAreaID { get; set; }
        public int QuestionnaireLevel1ID { get; set; }
        public int LanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }
    public class SelectQuestionnaireLevel3List
    {
        public int Company_ID { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public int IssueAreaID { get; set; }
        public int QuestionnaireLevel1ID { get; set; }
        public int QuestionnaireLevel2ID { get; set; }
        public int LanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string IssueArea { get; set; }
        public string QuestionnaireLevel1 { get; set; }
        public int exprtType { get; set; }
        public string QuestionnaireLevel2Description { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public int Branch_ID { get; set; }
        public int UserLanguageID { get; set; }
        public bool _search { get; set; }
        public string filters { get; set; }
        public bool advnce { get; set; }
        public string Query { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }


    }
    public class SelectParticularQuestionnaireLevel3List
    {
        public int UserLanguageID { get; set; }
        public int QuestionnaireLevel3ID { get; set; }
    }
    public class SaveQuestionnaireLevel3List
    {
        public int User_ID { get; set; }
        public string data { get; set; }
        public string Company_ID { get; set; }
        public string Branch { get; set; }
        public string MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public class UpdateLocaleQuestionnaireLevel3List
    {
        public string data { get; set; }
        public int Company_ID { set; get; }
        public string Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
    }
    public class CheckQuestionLevel3List
    {
        public int IssueAreaID { get; set; }
        public int QuestionLevel1ID { set; get; }
        public int QuestionLevel2ID { set; get; }
        public int QuestionLevel3 { set; get; }
        public int QuestionLevel3LocaleID { set; get; }
        public int Language_ID { set; get; }
    }
    public class DeleteQuestionLevel3List
    {
        public string key { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { set; get; }
        public string UserCulture { get; set; }
        public string Branch { set; get; }
        public string MenuID { set; get; }
        public DateTime LoggedINDateTime { set; get; }
    }
    public class CheckQuestionnaireLevel3List
    {
        public int Company_ID { get; set; }
        public int QuestionnaireLevel2ID { set; get; }
        public string QuestionLevel3 { set; get; }
        public int QuestionnaireLevel3ID { set; get; }
        public int IssueArea_ID { set; get; }
        public int QuestionaryLevel1_ID { set; get; }

    }
    #endregion
    #region ::: QuestionnaireLevel3Classes :::
    /// <summary>
    /// QuestionnaireLevel3Classes
    /// </summary>
    /// 
    public class QuestionnaireLevel3
    {
        public int QuestionaryLevel3_ID
        {
            get;
            set;
        }
        public int Company_ID
        {
            get;
            set;
        }
        public int QuestionaryLevel2_ID
        {
            get;
            set;
        }

        public string QuestionLevel3
        {
            get;
            set;
        }

        public string QuestionLevel3_IsActive
        {
            get;
            set;
        }
    }


    public partial class HD_QuestionaryLevel3
    {
        public HD_QuestionaryLevel3()
        {
            this.HD_QuestionnaireLevel3Locale = new HashSet<HD_QuestionnaireLevel3Locale>();
        }

        public int QuestionaryLevel3_ID { get; set; }
        public int QuestionaryLevel2_ID { get; set; }
        public string QuestionLevel3 { get; set; }
        public bool QuestionLevel3_IsActive { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> QuestionaryLevel1_ID { get; set; }
        public Nullable<int> IssueArea_ID { get; set; }

        public virtual HD_QuestionaryLevel1 HD_QuestionaryLevel1 { get; set; }
        public virtual HD_QuestionaryLevel2 HD_QuestionaryLevel2 { get; set; }
        public virtual ICollection<HD_QuestionnaireLevel3Locale> HD_QuestionnaireLevel3Locale { get; set; }
    }
    public partial class HD_QuestionnaireLevel3Locale
    {
        public int QuestionnaireLevel3Locale_ID { get; set; }
        public int QuestionLevel3_ID { get; set; }
        public string QuestionLevel3 { get; set; }
        public int Language_ID { get; set; }

        public virtual HD_QuestionaryLevel3 HD_QuestionaryLevel3 { get; set; }
    }
    public partial class HD_QuestionaryLevel1
    {
        public HD_QuestionaryLevel1()
        {
            this.HD_QuestionaryLevel2 = new HashSet<HD_QuestionaryLevel2>();
            this.HD_QuestionaryLevel3 = new HashSet<HD_QuestionaryLevel3>();
            this.HD_QuestionnaireLevel1Locale = new HashSet<HD_QuestionnaireLevel1Locale>();
        }

        public int QuestionaryLevel1_ID { get; set; }
        public int IssueArea_ID { get; set; }
        public Nullable<int> IssueSubArea_ID { get; set; }
        public string QuestionLevel1 { get; set; }
        public bool QuestionLevel1_IsActive { get; set; }
        public int Company_ID { get; set; }

        public virtual ICollection<HD_QuestionaryLevel2> HD_QuestionaryLevel2 { get; set; }
        public virtual ICollection<HD_QuestionaryLevel3> HD_QuestionaryLevel3 { get; set; }
        public virtual ICollection<HD_QuestionnaireLevel1Locale> HD_QuestionnaireLevel1Locale { get; set; }
    }
    public partial class HD_QuestionnaireLevel1Locale
    {
        public int QuestionnaireLevel1Locale_ID { get; set; }
        public int QuestionLevel1_ID { get; set; }
        public string QuestionLevel1 { get; set; }
        public int Language_ID { get; set; }

        public virtual HD_QuestionaryLevel1 HD_QuestionaryLevel1 { get; set; }
    }
    public partial class HD_QuestionaryLevel2
    {
        public HD_QuestionaryLevel2()
        {
            this.HD_QuestionaryLevel3 = new HashSet<HD_QuestionaryLevel3>();
            this.HD_QuestionnaireLevel2Locale = new HashSet<HD_QuestionnaireLevel2Locale>();
        }

        public int QuestionaryLevel2_ID { get; set; }
        public int QuestionaryLevel1_ID { get; set; }
        public string QuestionLevel2 { get; set; }
        public bool QuestionLevel2_IsActive { get; set; }
        public int Company_ID { get; set; }
        public int IssueArea_ID { get; set; }

        public virtual HD_QuestionaryLevel1 HD_QuestionaryLevel1 { get; set; }
        public virtual ICollection<HD_QuestionaryLevel3> HD_QuestionaryLevel3 { get; set; }
        public virtual ICollection<HD_QuestionnaireLevel2Locale> HD_QuestionnaireLevel2Locale { get; set; }
    }
    public partial class HD_QuestionnaireLevel2Locale
    {
        public int QuestionnaireLevel2Locale_ID { get; set; }
        public int QuestionLevel2_ID { get; set; }
        public string QuestionLevel2 { get; set; }
        public int Language_ID { get; set; }

        public virtual HD_QuestionaryLevel2 HD_QuestionaryLevel2 { get; set; }
    }
    #endregion
}
