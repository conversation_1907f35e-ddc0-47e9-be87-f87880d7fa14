﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP.Utilities;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreCompanyCalenderMasterController : ApiController
    {

        #region ::: SelectShiftType /Mithun:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectShiftType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectShiftType([FromBody] SelectShiftTypeList SelectShiftTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectShiftType(SelectShiftTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectShiftDays /Mithun:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectShiftDays")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectShiftDays([FromBody] SelectShiftDaysList SelectShiftDaysObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectShiftDays(SelectShiftDaysObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: GetBranchesList /Mithun::: 
        /// <summary>
        /// To Get Branches List
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/SelectBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranch([FromBody] SelectBranchList SelectBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectBranch(SelectBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectBranchHoliday /Mithun:::
        /// <summary>
        /// To Get Branches List
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/SelectBranchHoliday")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchHoliday([FromBody] SelectBranchHolidayList SelectBranchHolidayObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectBranchHoliday(SelectBranchHolidayObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Year /Mithun:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 

        [Route("api/CoreCompanyCalenderMaster/SelectCalendarYear")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarYear([FromBody] SelectCalendarYearList SelectCalendarYearObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarYear(SelectCalendarYearObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectCalendarToYear /Mithun:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectCalendarToYear")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarToYear([FromBody] SelectCalendarToYearList SelectCalendarToYearObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarToYear(SelectCalendarToYearObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectRegionForCopyCalendar /Mithun:::
        /// <summary>
        ////to Select Region and Company and its child
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectRegionForCopyCalendar")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectRegionForCopyCalendar([FromBody] SelectRegionForCopyCalendarList SelectRegionForCopyCalendarObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectRegionForCopyCalendar(SelectRegionForCopyCalendarObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Shift Type /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectCalendarShiftType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarShiftType([FromBody] SelectCalendarShiftTypeList SelectCalendarShiftTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarShiftType(SelectCalendarShiftTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Shift Type /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectCalendarShiftTypeYear")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarShiftTypeYear([FromBody] SelectCalendarShiftTypeYearList SelectCalendarShiftTypeYearObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarShiftTypeYear(SelectCalendarShiftTypeYearObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Shitf Days /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 

        [Route("api/CoreCompanyCalenderMaster/SelectCalendarShiftDays")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarShiftDays([FromBody] SelectCalendarShiftDaysList SelectCalendarShiftDaysObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarShiftDays(SelectCalendarShiftDaysObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Shitf Days /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectCalendarShiftDaysYear")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarShiftDaysYear([FromBody] SelectCalendarShiftDaysYearList SelectCalendarShiftDaysYearObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarShiftDaysYear(SelectCalendarShiftDaysYearObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Shitf  /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectCalendarShift")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarShift([FromBody] SelectCalendarShiftList SelectCalendarShiftObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarShift(SelectCalendarShiftObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Shitf  /Mihtun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectCalendarShiftYear")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCalendarShiftYear([FromBody] SelectCalendarShiftYearList SelectCalendarShiftYearObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectCalendarShiftYear(SelectCalendarShiftYearObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectRegionForHoliday /Mithun:::
        /// <summary>
        ///to Select Region and Company and its child
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectRegionForHoliday")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectRegionForHoliday([FromBody] SelectRegionForHolidayList SelectRegionForHolidayObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectRegionForHoliday(SelectRegionForHolidayObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterList1 SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectReferenceMaster(SelectReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: Refernce Master Shift Type Save /Mihtun:::
        /// <summary>
        /// To save the Product Master
        /// </summary>   
        [Route("api/CoreCompanyCalenderMaster/ReferenceMasterShiftTypeSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ReferenceMasterShiftTypeSave([FromBody] ReferenceMasterShiftTypeSaveList ReferenceMasterShiftTypeSaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.ReferenceMasterShiftTypeSave(ReferenceMasterShiftTypeSaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Refernce Master Shift Days Save /Mithun:::
        /// <summary>
        /// To save the Product Master
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/ReferenceMasterShiftDaysSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ReferenceMasterShiftDaysSave([FromBody] ReferenceMasterShiftDaysSaveList ReferenceMasterShiftDaysSaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.ReferenceMasterShiftDaysSave(ReferenceMasterShiftDaysSaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Refernce Master Shift Save /Mithun:::
        /// <summary>
        /// To save the Product Master
        /// </summary> 

        [Route("api/CoreCompanyCalenderMaster/ReferenceMasterShiftSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ReferenceMasterShiftSave([FromBody] ReferenceMasterShiftSaveList ReferenceMasterShiftSaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.ReferenceMasterShiftSave(ReferenceMasterShiftSaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectDesignation /Mithun:::
        /// <summary>
        ///to Select Company and its child
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectDesignation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDesignation([FromBody] SelectDesignationList SelectDesignationObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectDesignation(SelectDesignationObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Employee /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectEmployee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectEmployee([FromBody] SelectEmployeeList SelectEmployeeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectEmployee(SelectEmployeeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectYearShiftDetails /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectYearShiftDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectYearShiftDetails([FromBody] SelectYearShiftDetailsList SelectYearShiftDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectYearShiftDetails(SelectYearShiftDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: SelectExistsShiftDetails /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/SelectExistsShiftDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectExistsShiftDetails([FromBody] SelectExistsShiftDetailsList SelectExistsShiftDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectExistsShiftDetails(SelectExistsShiftDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion


        #region ::: SelectExistsShiftDaysDetails /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary>  

        [Route("api/CoreCompanyCalenderMaster/SelectExistsShiftDaysDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectExistsShiftDaysDetails([FromBody] SelectExistsShiftDaysDetailsList SelectExistsShiftDaysDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectExistsShiftDaysDetails(SelectExistsShiftDaysDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        // griid
        #region ::: Select /Mihtun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCompanyCalanderList SelectObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyCalenderMasterServices.Select(SelectObj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion


        #region ::: SelectBranchHolidays /Mithun:::
        /// <summary>
        /// To Select Field Search Service
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreCompanyCalenderMaster/SelectBranchCalendarHolidays")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchCalendarHolidays([FromBody] SelectBranchCalendarHolidaysList SelectBranchCalendarHolidaysObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SelectBranchCalendarHolidays(SelectBranchCalendarHolidaysObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectCompanyCalendarBranch /Mithun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/SelectCompanyCalendarBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompanyCalendarBranch([FromBody] SelectCompanyCalendarBranchList SelectCompanyCalendarBranchObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyCalenderMasterServices.SelectCompanyCalendarBranch(SelectCompanyCalendarBranchObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectCompanyCalendarBranch /Mithun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/SelectCompanyCalendar")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompanyCalendar([FromBody] SelectCompanyCalendarList SelectCompanyCalendarObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyCalenderMasterServices.SelectCompanyCalendar(SelectCompanyCalendarObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveCopyCalendar /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/SaveCopyCalendar")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveCopyCalendar([FromBody] SaveCopyCalendarList SaveCopyCalendarObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SaveCopyCalendar(SaveCopyCalendarObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SaveCopyCalendarHolidays /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/SaveCopyCalendarHolidays")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveCopyCalendarHolidays([FromBody] SaveCopyCalendarHolidaysList SaveCopyCalendarHolidaysObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SaveCopyCalendarHolidays(SaveCopyCalendarHolidaysObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SaveCopyCalendar /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>

        [Route("api/CoreCompanyCalenderMaster/SaveCopyLastYearCalendar")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveCopyLastYearCalendar([FromBody] SaveCopyLastYearCalendarList SaveCopyLastYearCalendarObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.SaveCopyLastYearCalendar(SaveCopyLastYearCalendarObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckCalendarAlreadyExists /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/CheckCalendarAlreadyExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCalendarAlreadyExists([FromBody] CheckCalendarAlreadyExistsList CheckCalendarAlreadyExistsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.CheckCalendarAlreadyExists(CheckCalendarAlreadyExistsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Holidays
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteHolidayList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.Delete(DeleteObj, Conn);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: CheckName /Mithun:::
        /// <summary>
        /// To Check Date
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/CheckName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckName([FromBody] CheckNameList CheckNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.CheckName(CheckNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckDate /Mithun:::
        /// <summary>
        /// To Check Date
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/CheckDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDate([FromBody] CheckDateList CheckDateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.CheckDate(CheckDateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CalculateRegularHours /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        [Route("api/CoreCompanyCalenderMaster/CalculateRegularHours")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CalculateRegularHours([FromBody] CalculateRegularHoursList CalculateRegularHoursObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.CalculateRegularHours(CalculateRegularHoursObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion


        #region ::: CheckOvertime /Mithun:::
        /// <summary>
        /// To Check OverTime
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/CheckOvertime")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckOvertime([FromBody] CheckOvertimeList CheckOvertimeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.CheckOvertime(CheckOvertimeObj, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Checkdoubletime /Mithun:::
        /// <summary>
        /// To Check OverTime
        /// </summary>
        [Route("api/CoreCompanyCalenderMaster/Checkdoubletime")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Checkdoubletime([FromBody] CheckdoubletimeList CheckdoubletimeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyCalenderMasterServices.Checkdoubletime(CheckdoubletimeObj, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        [Route("api/CoreCompanyCalenderMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectCompanyCalanderList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;


            string filters = HttpContext.Current.Request.Params["filters"];


            string Query = HttpContext.Current.Request.Params["Query"];



            try
            {


                object Response = await CoreCompanyCalenderMasterServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }


        #region ::: HolidayUpload :::
        /// <summary>
        /// HolidayUpload
        /// </summary>
        [Route("api/_PrevostDashboard/HolidayUpload")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> HolidayUpload()
        {
            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                string userCulture = null;
                int? CompanycalendarID = null;
                IFormFile CalendarHoliday = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "userCulture":
                                userCulture = value;
                                break;
                            case "CompanycalendarID":
                                CompanycalendarID = Convert.ToInt32(value);
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            CalendarHoliday = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = CoreCompanyCalenderMasterServices.HolidayUpload(CalendarHoliday, (int)CompanycalendarID, LogException, userCulture, Conn);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }
        #endregion

    }

}
