﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreQuestionnaireLevel2MasterController : ApiController
    {
        #region ::: SelectQuestionnaireLevel1 /Mithun:::
        /// <summary>
        /// To get QustionnaireLevel1 records for a IssueArea
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/SelectQuestionLevel1")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectQuestionLevel1([FromBody] SelectQuestionLevel1List SelectQuestionLevel1Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.SelectQuestionLevel1(SelectQuestionLevel1Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckQuestionnaireLevel2Locale /Mithun:::
        /// <summary>
        /// CheckQuestionnaireLevel2Locale
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/CheckQuestionnaireLevel2Locale")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestionnaireLevel2Locale([FromBody] CheckQuestionnaireLevel2LocaleList CheckQuestionnaireLevel2LocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.CheckQuestionnaireLevel2Locale(CheckQuestionnaireLevel2LocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/SelectReferenceMaster")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterCoreQuestionnaireLevel2List SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.SelectReferenceMaster(SelectReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Load QuestionnaireLevel2Grid /Mithun:::
        /// <summary>
        /// Loading QuestionnaireLevel2 Grid
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/Select")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreQuestionnaireLevel2List SelectObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreQuestionnaireLevel2MasterServices.Select(SelectObj, connstring, LogException, sidx, sord, page, rows, _search, advnce, filters, advnceFilters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectParticularQuestionnaireLevel2 /Mithun:::
        /// <summary>
        /// SelectParticularQuestionnaireLevel2
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/SelectParticularQuestionnaireLevel2")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularQuestionnaireLevel2([FromBody] SelectParticularQuestionnaireLevel2List SelectParticularQuestionnaireLevel2Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.SelectParticularQuestionnaireLevel2(SelectParticularQuestionnaireLevel2Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Save QuestionnareLevel2 /Mithun:::
        /// <summary>
        /// SaveQuestionnaireLevel2
        /// </summary>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/Save")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveQuestionnareLevel2List SaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.Save(SaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// UpdateLocale
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/UpdateLocale")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleCoreQuestionnaireLevel2List UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete QuestionnaireLevel2 /Mithun:::
        /// <summary>
        /// Delete
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/Delete")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteQuestionnaireLevel2List DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckQuestionnaireLevel2 /Mithun:::
        /// <summary>
        /// CheckQuestionnaireLevel2
        /// </summary> 
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/CheckQuestionnaireLevel2")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestionnaireLevel2([FromBody] CheckQuestionnaireLevel2List CheckQuestionnaireLevel2Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel2MasterServices.CheckQuestionnaireLevel2(CheckQuestionnaireLevel2Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region Export
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreQuestionnaireLevel2Master/Export")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectCoreQuestionnaireLevel2List ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





            try
            {


                Object Response = await CoreQuestionnaireLevel2MasterServices.Export(ExportObj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion


    }

}
