﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class CoreCompanyCalenderMasterServices
    {

        // not changed in front end
        #region ::: SelectShiftType /Mithun:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        public static IActionResult SelectShiftType(SelectShiftTypeList SelectShiftTypeObj, string constring, int LogException)
        {
            try
            {
                var Masterdata = default(dynamic);
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get RefMasterID 
                    int RefMasterID;
                    using (var cmd = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = @RefMasterName", connection))
                    {
                        cmd.Parameters.AddWithValue("@RefMasterName", "SHIFTTYPE");
                        RefMasterID = (int)cmd.ExecuteScalar();
                    }

                    // Get Region_ID 
                    int? Region_ID;
                    using (var cmd = new SqlCommand("SELECT Region_ID FROM GNM_Branch WHERE Branch_ID = @BranchID", connection))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", SelectShiftTypeObj.Branch_ID);
                        Region_ID = (int?)cmd.ExecuteScalar();
                    }

                    // Get RefMasterList 
                    List<GNM_RefMasterDetail> RefMasterList = new List<GNM_RefMasterDetail>();
                    using (SqlCommand command = new SqlCommand("UP_AMERP_GetRefMasterDetail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        command.Parameters.AddWithValue("@RegionID", Region_ID);


                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int id = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID"));
                                string name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"));
                                // Process the results
                                GNM_RefMasterDetail refMasterDetail = new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = id,
                                    RefMasterDetail_Name = name
                                };

                                // Add the instance to the RefMasterList
                                RefMasterList.Add(refMasterDetail);
                            }
                        }
                    }

                    int Language_ID = Convert.ToInt32(SelectShiftTypeObj.UserLanguageID);

                    if (SelectShiftTypeObj.UserLanguageCode.ToString() == SelectShiftTypeObj.GeneralLanguageCode.ToString())
                    {
                        Masterdata = new
                        {
                            Data = from Ref in RefMasterList
                                   orderby Ref.RefMasterDetail_ID
                                   select new
                                   {
                                       ID = Ref.RefMasterDetail_ID,
                                       Name = Ref.RefMasterDetail_Name
                                   },
                        };
                    }
                    else
                    {
                        // Get RefMasterDetailLocale and join with RefMasterList using ADO.NET
                        List<dynamic> JoinedList = new List<dynamic>();
                        using (SqlCommand command = new SqlCommand("UP_AMERP_GetRefMasterDetailWithLocale", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@RegionID", Region_ID);

                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    int id = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID"));
                                    string name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"));
                                    // Process the results
                                }
                            }
                        }

                        Masterdata = new
                        {
                            Data = JoinedList.OrderBy(x => x.ID)
                        };
                    }
                }

                //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                return new JsonResult(Masterdata);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectShiftDays /Mithun:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        public static IActionResult SelectShiftDays(SelectShiftDaysList SelectShiftDaysObj, string constring, int LogException)
        {
            try
            {
                var Masterdata = default(dynamic);
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get RefMasterID for SHIFTDAYS
                    int RefMasterID;
                    using (var cmd = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = @RefMasterName", connection))
                    {
                        cmd.Parameters.AddWithValue("@RefMasterName", "SHIFTDAYS");
                        RefMasterID = (int)cmd.ExecuteScalar();
                    }

                    // Get ShiftTypeShortCode
                    string ShiftTypeShortCode;
                    using (var cmd = new SqlCommand("SELECT RefMasterDetail_Short_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ShiftTypeID", connection))
                    {
                        cmd.Parameters.AddWithValue("@ShiftTypeID", SelectShiftDaysObj.ShiftType_ID);
                        ShiftTypeShortCode = (string)cmd.ExecuteScalar();
                    }

                    // Get Region_ID for Branch_ID
                    int? Region_ID;
                    using (var cmd = new SqlCommand("SELECT Region_ID FROM GNM_Branch WHERE Branch_ID = @BranchID", connection))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", SelectShiftDaysObj.Branch_ID);
                        Region_ID = (int?)cmd.ExecuteScalar();
                    }

                    // Get RefMasterList based on RefMasterID and Region_ID
                    List<GNM_RefMasterDetail> RefMasterList = new List<GNM_RefMasterDetail>();
                    using (SqlCommand command = new SqlCommand("UP_AMERP_GetRefMasterDetail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        command.Parameters.AddWithValue("@RegionID", Region_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int id = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID"));
                                string name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"));
                                // Process the results
                                GNM_RefMasterDetail refMasterDetail = new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = id,
                                    RefMasterDetail_Name = name
                                };

                                // Add the instance to the RefMasterList
                                RefMasterList.Add(refMasterDetail);
                            }
                        }
                    }


                    int Language_ID = Convert.ToInt32(SelectShiftDaysObj.UserLanguageID);
                    System.Data.DataTable resultTable = new System.Data.DataTable();
                    resultTable.Columns.Add("ID", typeof(int));
                    resultTable.Columns.Add("Name", typeof(string));

                    if (SelectShiftDaysObj.UserLanguageCode.ToString() == SelectShiftDaysObj.GeneralLanguageCode.ToString())
                    {
                        foreach (var Ref in RefMasterList)
                        {
                            DataRow row = resultTable.NewRow();
                            row["ID"] = Ref.RefMasterDetail_ID;
                            row["Name"] = Ref.RefMasterDetail_Name;
                            resultTable.Rows.Add(row);
                        }
                    }
                    else
                    {
                        // Get RefMasterDetailLocale and join with RefMasterList
                        using (var cmd = new SqlCommand("UP_AMERP_GetRefMasterDetailWithLocale", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            cmd.Parameters.AddWithValue("@RegionID", Region_ID);

                            connection.Open();
                            using (var reader = cmd.ExecuteReader())
                            {
                                resultTable.Columns.Add("ID", typeof(int));
                                resultTable.Columns.Add("Name", typeof(string));

                                while (reader.Read())
                                {
                                    DataRow row = resultTable.NewRow();
                                    row["ID"] = reader["RefMasterDetail_ID"];
                                    row["Name"] = reader["RefMasterDetail_Name"];
                                    resultTable.Rows.Add(row);
                                }

                                // Process resultTable as needed
                            }
                        }
                    }

                    Masterdata = new
                    {
                        Data = resultTable.AsEnumerable().Select(row => new
                        {
                            ID = row.Field<int>("ID"),
                            Name = row.Field<string>("Name")
                        }).OrderBy(x => x.ID).ToList()
                    };

                    //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                    return new JsonResult(Masterdata);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: GetBranchesList /Mithun::: 
        /// <summary>
        /// To Get Branches List
        /// </summary>
        public static IActionResult SelectBranch(SelectBranchList SelectBranchObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectBranchObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectBranchObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectBranchObj.Company_ID);
                int userEmployeeID = Convert.ToInt32(SelectBranchObj.User_Employee_ID);

                string RegionID = SelectBranchObj.RegionID.TrimEnd(new char[] { ',' });
                List<APINameID> branchList = new List<APINameID>();

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch branch data using stored procedure
                    using (var cmd = new SqlCommand("UP_AMERP_GetBranchList", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RegionID", RegionID);
                        cmd.Parameters.AddWithValue("@BranchID", SelectBranchObj.Branch_ID);
                        cmd.Parameters.AddWithValue("@UserEmployeeID", userEmployeeID);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new APINameID
                                {
                                    ID = (int)reader["ID"],
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }
                }

                if (userLanguageID == generalLanguageID)
                {
                    var branchData = branchList.Select(a => new
                    {
                        ID = a.ID,
                        Name = a.Name
                    }).OrderBy(a => a.Name).ToList();

                    Masterdata = new
                    {
                        BranchData = branchData
                    };
                }
                else
                {
                    var branchData = new List<object>();
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        foreach (var branch in branchList)
                        {
                            // Fetch localized branch names using stored procedure
                            using (var cmd = new SqlCommand("UP_AMERP_GetLocaleBranchNames", connection))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@BranchID", branch.ID);
                                cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                                using (var reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        branchData.Add(new
                                        {
                                            ID = branch.ID,
                                            Name = reader["Branch_Name"].ToString()
                                        });
                                    }
                                }
                            }
                        }
                    }

                    branchData = branchData.OrderBy(a => ((dynamic)a).Name).ToList(); // Corrected ordering

                    Masterdata = new
                    {
                        BranchData = branchData
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }
        #endregion

        #region ::: SelectBranchHoliday /Mithun:::
        /// <summary>
        /// To Get Branches List
        /// </summary>
        public static IActionResult SelectBranchHoliday(SelectBranchHolidayList SelectBranchHolidayObj, string constring, int LogException)
        {
            var Masterdata = new object();
            try
            {
                int userLanguageID = Convert.ToInt32(SelectBranchHolidayObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectBranchHolidayObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectBranchHolidayObj.Company_ID);
                int userEmployeeID = Convert.ToInt32(SelectBranchHolidayObj.User_Employee_ID);

                string RegionID = SelectBranchHolidayObj.RegionID.TrimEnd(new char[] { ',' });
                List<APINameID> branchList = new List<APINameID>();

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand("UP_SEL_AMERP_GetDistinctBranches", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RegionIDs", SelectBranchHolidayObj.RegionID);
                        cmd.Parameters.AddWithValue("@BranchID", SelectBranchHolidayObj.Branch_ID);
                        cmd.Parameters.AddWithValue("@UserEmployeeID", userEmployeeID);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new APINameID
                                {
                                    ID = (int)reader["ID"],
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }
                }

                if (userLanguageID == generalLanguageID)
                {
                    var branchData = branchList.Select(a => new
                    {
                        ID = a.ID,
                        Name = a.Name
                    }).OrderBy(a => a.Name).ToList();

                    Masterdata = new
                    {
                        BranchData = branchData
                    };
                }
                else
                {
                    var branchData = new List<object>();
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        foreach (var branch in branchList)
                        {
                            string localeQuery = @"
                    SELECT 
                        c.Branch_Name 
                    FROM 
                        GNM_BranchLocale c 
                    WHERE 
                        c.Branch_ID = @BranchID 
                        AND c.Language_ID = @LanguageID";

                            using (var cmd = new SqlCommand(localeQuery, connection))
                            {
                                cmd.Parameters.AddWithValue("@BranchID", branch.ID);
                                cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                                using (var reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        branchData.Add(new
                                        {
                                            ID = branch.ID,
                                            Name = reader["Branch_Name"].ToString()
                                        });
                                    }
                                }
                            }
                        }
                    }

                    branchData = branchData.OrderBy(a => a).ToList(); // error 

                    Masterdata = new
                    {
                        BranchData = branchData
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion

        #region ::: Select Year /Mithun:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarYear(SelectCalendarYearList SelectCalendarYearObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarYearObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarYearObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarYearObj.Company_ID);
                string BranchID = SelectCalendarYearObj.BranchID.TrimEnd(new char[] { ',' });

                List<APINameID> yearList = new List<APINameID>();

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (var cmd = new SqlCommand("UP_AMERP_GetCalendarYearsList", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchID);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                yearList.Add(new APINameID
                                {
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }
                }

                jsonData = new
                {
                    YearValues = from a in yearList
                                 orderby a.Name
                                 select new
                                 {
                                     Name = a.Name
                                 }
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SelectCalendarToYear /Mithun:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarToYear(SelectCalendarToYearList SelectCalendarToYearObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarToYearObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarToYearObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarToYearObj.Company_ID);
                string BranchID = SelectCalendarToYearObj.BranchID.TrimEnd(new char[] { ',' });

                List<APINameID> FinancialYearList = new List<APINameID>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand("USP_AMERP_GetLatestCompanyFinancialYear", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);

                        connection.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                FinancialYearList.Add(new APINameID
                                {
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }
                }

                jsonData = new
                {
                    YearValues = from a in FinancialYearList
                                 orderby a.Name
                                 select new
                                 {
                                     Name = a.Name
                                 }
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: SelectRegionForCopyCalendar /Mithun:::
        /// <summary>
        ////to Select Region and Company and its child
        /// </summary> 
        public static IActionResult SelectRegionForCopyCalendar(SelectRegionForCopyCalendarList SelectRegionForCopyCalendarObj, string constring, int LogException)
        {
            int UserLang = Convert.ToInt32(SelectRegionForCopyCalendarObj.UserLanguageID);
            int Company_ID = Convert.ToInt32(SelectRegionForCopyCalendarObj.Company_ID);
            int UserID = Convert.ToInt32(SelectRegionForCopyCalendarObj.User_Employee_ID);
            var jsonData = default(dynamic);
            List<APINameID> RegionList = new List<APINameID>();

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch region data
                    SqlCommand command = new SqlCommand("UP_AMERP_getGetRegionList", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@CompanyID", Company_ID);
                    command.Parameters.AddWithValue("@UserID", UserID);
                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        APINameID region = new APINameID();
                        region.ID = Convert.ToInt32(reader["ID"]);
                        region.Name = reader["Name"].ToString();
                        RegionList.Add(region);
                    }
                    reader.Close();

                    // Fetch localized region names if necessary
                    if (Convert.ToInt32(SelectRegionForCopyCalendarObj.UserLanguageID) != Convert.ToInt32(SelectRegionForCopyCalendarObj.GeneralLanguageID))
                    {
                        foreach (var region in RegionList)
                        {
                            command = new SqlCommand("UP_AMERP_GetLocaleBranchNames", connection);
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RegionID", region.ID);
                            command.Parameters.AddWithValue("@UserLang", UserLang);
                            SqlDataReader localeReader = command.ExecuteReader();

                            if (localeReader.Read())
                            {
                                region.ID = Convert.ToInt32(localeReader["ID"]);
                                region.Name = localeReader["Name"].ToString();
                            }

                            localeReader.Close();
                        }
                    }

                    jsonData = new
                    {
                        Region = (from a in RegionList
                                  orderby a.Name
                                  select new
                                  {
                                      ID = a.ID,
                                      Name = a.Name
                                  }),
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Shift Type /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarShiftType(SelectCalendarShiftTypeList SelectCalendarShiftTypeObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarShiftTypeObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarShiftTypeObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarShiftTypeObj.Company_ID);
                string BranchID = SelectCalendarShiftTypeObj.BranchID.TrimEnd(',');
                string Year = SelectCalendarShiftTypeObj.Year.TrimEnd(',');

                int RefMasterID = 0;
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFTTYPE'", connection))
                    {
                        RefMasterID = (int)cmd.ExecuteScalar();
                    }
                }

                List<APINameID> RefMasterList = new List<APINameID>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand("UP_AMERP_GetCalendarShiftTypes", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchID);
                        cmd.Parameters.AddWithValue("@Year", int.Parse(Year));

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = (int)reader["ID"],
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }
                }

                if (SelectCalendarShiftTypeObj.UserLanguageCode.ToString() == SelectCalendarShiftTypeObj.GeneralLanguageCode.ToString())
                {
                    jsonData = new
                    {
                        Data = from Ref in RefMasterList
                               orderby Ref.ID
                               select new
                               {
                                   ID = Ref.ID,
                                   Name = Ref.Name
                               }
                    };
                }
                else
                {
                    List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        using (var cmd = new SqlCommand("SELECT * FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMasterID", connection))
                        {
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = (int)reader["RefMasterDetail_ID"],
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                                    });
                                }
                            }
                        }
                    }

                    jsonData = new
                    {
                        Data = from Ref in RefMasterList
                               join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                               orderby RefL.RefMasterDetail_ID
                               select new
                               {
                                   ID = RefL.RefMasterDetail_ID,
                                   Name = RefL.RefMasterDetail_Name
                               }
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Shift Type /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarShiftTypeYear(SelectCalendarShiftTypeYearList SelectCalendarShiftTypeYearObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarShiftTypeYearObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarShiftTypeYearObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarShiftTypeYearObj.Company_ID);
                string Year = SelectCalendarShiftTypeYearObj.Year.TrimEnd(new char[] { ',' });

                List<APINameID> RefMasterList = new List<APINameID>();
                int RefMasterID = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get RefMasterID
                    using (SqlCommand cmd = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFTTYPE'", conn))
                    {
                        RefMasterID = (int)cmd.ExecuteScalar();
                    }

                    // Get RefMasterList
                    string storedProcedureName = "UP_AMERP_GetCalendarShiftTypes";
                    using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@BranchIDs", SelectCalendarShiftTypeYearObj.BranchID);
                        cmd.Parameters.AddWithValue("@Year", Year);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }
                        }
                    }

                }

                if (SelectCalendarShiftTypeYearObj.UserLanguageCode.ToString() == SelectCalendarShiftTypeYearObj.GeneralLanguageCode.ToString())
                {
                    jsonData = new
                    {
                        Data = from Ref in RefMasterList
                               orderby Ref.ID
                               select new
                               {
                                   ID = Ref.ID,
                                   Name = Ref.Name
                               },
                    };
                }
                else
                {
                    List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string localeQuery = "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMasterID";

                        using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(0),
                                        RefMasterDetail_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }
                    }

                    jsonData = new
                    {
                        Data = from Ref in RefMasterList
                               join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                               orderby RefL.RefMasterDetail_ID
                               select new
                               {
                                   ID = RefL.RefMasterDetail_ID,
                                   Name = RefL.RefMasterDetail_Name
                               },
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Shitf Days /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarShiftDays(SelectCalendarShiftDaysList SelectCalendarShiftDaysObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarShiftDaysObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarShiftDaysObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarShiftDaysObj.Company_ID);
                string BranchID = SelectCalendarShiftDaysObj.BranchID.TrimEnd(new char[] { ',' });
                string Year = SelectCalendarShiftDaysObj.Year.TrimEnd(new char[] { ',' });
                string ShiftType = SelectCalendarShiftDaysObj.ShiftType.TrimEnd(new char[] { ',' });

                List<APINameID> RefMasterList = new List<APINameID>();

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand("UP_AMERP_GetCalendarShiftDays", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchID);
                        cmd.Parameters.AddWithValue("@Year", Convert.ToInt32(Year));
                        cmd.Parameters.AddWithValue("@ShiftTypes", ShiftType);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = (int)reader["ID"],
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }
                }

                if (SelectCalendarShiftDaysObj.UserLanguageCode.ToString() == SelectCalendarShiftDaysObj.GeneralLanguageCode.ToString())
                {
                    jsonData = new
                    {
                        Data = from Ref in RefMasterList
                               orderby Ref.ID
                               select new
                               {
                                   ID = Ref.ID,
                                   Name = Ref.Name
                               }
                    };
                }
                else
                {
                    // Assuming RefMasterID is still necessary for your application
                    int RefMasterID = 0;
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        using (var cmd = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFTDAYS'", connection))
                        {
                            RefMasterID = (int)cmd.ExecuteScalar();
                        }
                    }

                    List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();

                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        using (var cmd = new SqlCommand("SELECT * FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMasterID", connection))
                        {
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = (int)reader["RefMasterDetail_ID"],
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                                    });
                                }
                            }
                        }
                    }

                    jsonData = new
                    {
                        Data = from Ref in RefMasterList
                               join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                               orderby RefL.RefMasterDetail_ID
                               select new
                               {
                                   ID = RefL.RefMasterDetail_ID,
                                   Name = RefL.RefMasterDetail_Name
                               }
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Shitf Days /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 

        public static IActionResult SelectCalendarShiftDaysYear(SelectCalendarShiftDaysYearList SelectCalendarShiftDaysYearObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarShiftDaysYearObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarShiftDaysYearObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarShiftDaysYearObj.Company_ID);

                // Trim inputs
                string Year = SelectCalendarShiftDaysYearObj.Year.TrimEnd(new char[] { ',' });
                string ShiftType = SelectCalendarShiftDaysYearObj.ShiftType.TrimEnd(new char[] { ',' });

                int RefMasterID;
                List<APINameID> RefMasterList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get RefMasterID
                    string refMasterQuery = "SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFTDAYS'";
                    using (SqlCommand cmd = new SqlCommand(refMasterQuery, conn))
                    {
                        RefMasterID = (int)cmd.ExecuteScalar();
                    }

                    // Get RefMasterList using stored procedure
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetCalendarShiftDays", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchIDs", SelectCalendarShiftDaysYearObj.BranchID);
                        cmd.Parameters.AddWithValue("@Year", Year);
                        cmd.Parameters.AddWithValue("@ShiftTypes", ShiftType);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }

                    if (SelectCalendarShiftDaysYearObj.UserLanguageCode.ToString() == SelectCalendarShiftDaysYearObj.GeneralLanguageCode.ToString())
                    {
                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   orderby Ref.ID
                                   select new
                                   {
                                       ID = Ref.ID,
                                       Name = Ref.Name
                                   }
                        };
                    }
                    else
                    {
                        List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();
                        string localeQuery = "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMasterID";

                        using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(0),
                                        RefMasterDetail_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                                   orderby RefL.RefMasterDetail_ID
                                   select new
                                   {
                                       ID = RefL.RefMasterDetail_ID,
                                       Name = RefL.RefMasterDetail_Name
                                   }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Shitf  /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarShift(SelectCalendarShiftList SelectCalendarShiftObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarShiftObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarShiftObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarShiftObj.Company_ID);
                string BranchID = SelectCalendarShiftObj.BranchID.TrimEnd(new char[] { ',' });
                string Year = SelectCalendarShiftObj.Year.TrimEnd(new char[] { ',' });
                string ShiftType = SelectCalendarShiftObj.ShiftType.TrimEnd(new char[] { ',' });
                string ShiftDays = SelectCalendarShiftObj.ShiftDays.TrimEnd(new char[] { ',' });

                List<APINameID> RefMasterList = new List<APINameID>();

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Main query using stored procedure
                    using (var cmd = new SqlCommand("UP_AMERP_GetCalendarShiftDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchID);
                        cmd.Parameters.AddWithValue("@Year", Convert.ToInt32(Year));
                        cmd.Parameters.AddWithValue("@ShiftTypes", ShiftType);
                        cmd.Parameters.AddWithValue("@ShiftDays", ShiftDays);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = (int)reader["ID"],
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }

                    if (SelectCalendarShiftObj.UserLanguageCode.ToString() == SelectCalendarShiftObj.GeneralLanguageCode.ToString())
                    {
                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   orderby Ref.ID
                                   select new
                                   {
                                       ID = Ref.ID,
                                       Name = Ref.Name
                                   }
                        };
                    }
                    else
                    {
                        List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();
                        int RefMasterID;

                        // Get RefMasterID
                        using (var cmdRefMaster = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFT'", connection))
                        {
                            RefMasterID = (int)cmdRefMaster.ExecuteScalar();
                        }

                        // Get RefMasterDetailLocale
                        string localeQuery = "SELECT * FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMasterID";

                        using (var cmdLocale = new SqlCommand(localeQuery, connection))
                        {
                            cmdLocale.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            using (var readerLocale = cmdLocale.ExecuteReader())
                            {
                                while (readerLocale.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = (int)readerLocale["RefMasterDetail_ID"],
                                        RefMasterDetail_Name = readerLocale["RefMasterDetail_Name"].ToString()
                                    });
                                }
                            }
                        }

                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                                   orderby RefL.RefMasterDetail_ID
                                   select new
                                   {
                                       ID = RefL.RefMasterDetail_ID,
                                       Name = RefL.RefMasterDetail_Name
                                   }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: Select Shitf  /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectCalendarShiftYear(SelectCalendarShiftYearList SelectCalendarShiftYearObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCalendarShiftYearObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCalendarShiftYearObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectCalendarShiftYearObj.Company_ID);

                // Clean input parameters
                string Year = SelectCalendarShiftYearObj.Year.TrimEnd(new char[] { ',' });
                string ShiftType = SelectCalendarShiftYearObj.ShiftType.TrimEnd(new char[] { ',' });
                string ShiftDays = SelectCalendarShiftYearObj.ShiftDays.TrimEnd(new char[] { ',' });

                int RefMasterID;
                List<APINameID> RefMasterList = new List<APINameID>();

                // Using ADO.NET to replace EF


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get RefMasterID
                    using (SqlCommand cmd = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFT'", conn))
                    {
                        RefMasterID = (int)cmd.ExecuteScalar();
                    }

                    using (var cmd = new SqlCommand("UP_AMERP_GetCalendarShiftDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@BranchIDs", SelectCalendarShiftYearObj.BranchID);
                        cmd.Parameters.AddWithValue("@Year", Convert.ToInt32(Year));
                        cmd.Parameters.AddWithValue("@ShiftTypes", ShiftType);
                        cmd.Parameters.AddWithValue("@ShiftDays", ShiftDays);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = (int)reader["ID"],
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }

                    if (SelectCalendarShiftYearObj.UserLanguageCode.ToString() == SelectCalendarShiftYearObj.GeneralLanguageCode.ToString())
                    {
                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   orderby Ref.ID
                                   select new
                                   {
                                       ID = Ref.ID,
                                       Name = Ref.Name
                                   },
                        };
                    }
                    else
                    {
                        List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();

                        // Get RefMasterDetailLocale
                        string localeQuery = "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMasterID";
                        using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(0),
                                        RefMasterDetail_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                                   orderby RefL.RefMasterDetail_ID
                                   select new
                                   {
                                       ID = RefL.RefMasterDetail_ID,
                                       Name = RefL.RefMasterDetail_Name
                                   },
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: SelectRegionForHoliday /Mithun:::
        /// <summary>
        ///to Select Region and Company and its child
        /// </summary> 
        public static IActionResult SelectRegionForHoliday(SelectRegionForHolidayList SelectRegionForHolidayObj, string constring, int LogException)
        {
            int UserLang = Convert.ToInt32(SelectRegionForHolidayObj.UserLanguageID);
            int GeneralLang = Convert.ToInt32(SelectRegionForHolidayObj.GeneralLanguageID);
            int Company_ID = Convert.ToInt32(SelectRegionForHolidayObj.Company_ID);
            var jsonData = new object();

            try
            {
                List<APINameID> RegionList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Call the stored procedure
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_SelectRegionForHoliday", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@User_Employee_ID", Convert.ToInt32(SelectRegionForHolidayObj.User_Employee_ID));
                        cmd.Parameters.AddWithValue("@UserLang", UserLang);
                        cmd.Parameters.AddWithValue("@GeneralLang", GeneralLang);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RegionList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }
                }

                if (UserLang == GeneralLang)
                {
                    jsonData = new
                    {
                        Region = (from a in RegionList
                                  orderby a.Name
                                  select new
                                  {
                                      ID = a.ID,
                                      Name = a.Name
                                  }),
                    };
                }
                else
                {
                    string localeQuery = "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE Language_ID=@UserLang AND RefMasterDetail_ID IN (@RegionIDs)";

                    List<GNM_RefMasterDetailLocale> LocaleList = new List<GNM_RefMasterDetailLocale>();

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@UserLang", UserLang);
                            cmd.Parameters.AddWithValue("@RegionIDs", string.Join(",", RegionList.Select(r => r.ID)));
                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    LocaleList.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(0),
                                        RefMasterDetail_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }
                    }

                    jsonData = new
                    {
                        Region = (from a in RegionList
                                  join b in LocaleList on a.ID equals b.RefMasterDetail_ID
                                  orderby b.RefMasterDetail_Name
                                  select new
                                  {
                                      ID = b.RefMasterDetail_ID,
                                      Name = b.RefMasterDetail_Name
                                  }),
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To Select Refrence Master records
        /// </summary> 
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterList1 SelectReferenceMasterObj, string constring, int LogException)
        {
            try
            {
                var Masterdata = default(dynamic);
                int RefMasterID;
                int GenShiftID;
                string ShiftDays;
                string ShiftTypeShortCode;
                string ShortCode;
                int? Region_ID;
                int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);

                // Get RefMasterID
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFT'";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        connection.Open();
                        RefMasterID = (int)command.ExecuteScalar();
                    }
                }

                // Get GenShiftID
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT Shift_ID FROM GNM_CompanyCalender WHERE ShiftDays = @ShiftDays_ID AND IsGeneralShift = 1";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ShiftDays_ID", SelectReferenceMasterObj.ShiftDays_ID);
                        connection.Open();

                        object result = command.ExecuteScalar();
                        GenShiftID = result != DBNull.Value && result != null ? Convert.ToInt32(result) : 0;  // Handle null or DBNull
                    }

                }

                // Get ShiftDays
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ShiftDays_ID";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ShiftDays_ID", SelectReferenceMasterObj.ShiftDays_ID);
                        connection.Open();
                        ShiftDays = (string)command.ExecuteScalar();
                    }
                }

                // Get ShiftTypeShortCode
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMasterDetail_Short_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ShiftType_ID";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ShiftType_ID", SelectReferenceMasterObj.ShiftType_ID);
                        connection.Open();
                        ShiftTypeShortCode = (string)command.ExecuteScalar();
                    }
                }

                ShortCode = ShiftTypeShortCode + '-' + ShiftDays;

                // Get Region_ID
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT Region_ID FROM GNM_Branch WHERE Branch_ID = @Branch_ID";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Branch_ID", SelectReferenceMasterObj.Branch_ID);
                        connection.Open();
                        Region_ID = (int?)command.ExecuteScalar();
                    }
                }

                // Get RefMasterDetail
                System.Data.DataTable RefMasterDetailTable = new System.Data.DataTable();
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = @"
                SELECT RefMasterDetail_ID, RefMasterDetail_Name 
                FROM GNM_RefMasterDetail 
                WHERE RefMaster_ID = @RefMasterID AND RefMasterDetail_IsActive = 1";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        adapter.Fill(RefMasterDetailTable);
                    }
                }

                // Prepare the final result
                if (SelectReferenceMasterObj.UserLanguageCode.ToString() == SelectReferenceMasterObj.GeneralLanguageCode.ToString())
                {
                    var data = from DataRow row in RefMasterDetailTable.Rows
                               orderby row["RefMasterDetail_Name"]
                               select new
                               {
                                   ID = row["RefMasterDetail_ID"],
                                   Name = row["RefMasterDetail_Name"]
                               };

                    Masterdata = new
                    {
                        Data = data.Distinct(),
                        GenShiftID
                    };
                }
                else
                {
                    // Get RefMasterDetailLocale
                    System.Data.DataTable RefMasterDetailLocaleTable = new System.Data.DataTable();
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        string query = @"
                    SELECT RefMasterDetail_ID, RefMasterDetail_Name 
                    FROM GNM_RefMasterDetailLocale 
                    WHERE RefMaster_ID = @RefMasterID AND Language_ID = @Language_ID";
                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);
                            SqlDataAdapter adapter = new SqlDataAdapter(command);
                            adapter.Fill(RefMasterDetailLocaleTable);
                        }
                    }

                    var data = from DataRow refRow in RefMasterDetailTable.Rows
                               join DataRow refLRow in RefMasterDetailLocaleTable.Rows on refRow["RefMasterDetail_ID"] equals refLRow["RefMasterDetail_ID"]
                               orderby refLRow["RefMasterDetail_Name"]
                               select new
                               {
                                   ID = refLRow["RefMasterDetail_ID"],
                                   Name = refLRow["RefMasterDetail_Name"]
                               };

                    Masterdata = new
                    {
                        Data = data.Distinct(),
                        GenShiftID
                    };
                }

                //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                return new JsonResult(Masterdata);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Refernce Master Shift Type Save /Mihtun:::
        /// <summary>
        /// To save the Product Master
        /// </summary>      
        public static IActionResult ReferenceMasterShiftTypeSave(ReferenceMasterShiftTypeSaveList ReferenceMasterShiftTypeSaveObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            int count = 0;
            string PName = string.Empty;
            int userLanguageID = Convert.ToInt32(ReferenceMasterShiftTypeSaveObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(ReferenceMasterShiftTypeSaveObj.GeneralLanguageID);
            int? Region_ID;
            int RefMasterID;
            bool RefMasterDetail_IsActive;
            int RefMasterDetail_ID;
            int CompanyID;
            int User_ID;

            try
            {
                // Get user details from session
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = ReferenceMasterShiftTypeSaveObj.UserDetails.FirstOrDefault();
                CompanyID = ReferenceMasterShiftTypeSaveObj.Company_ID;
                User_ID = ReferenceMasterShiftTypeSaveObj.User_ID;

                // Get RefMasterID for SHIFTTYPE
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFTTYPE'";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        connection.Open();
                        RefMasterID = (int)command.ExecuteScalar();
                    }
                }

                // Get Region_ID for Branch_ID
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT Region_ID FROM GNM_Branch WHERE Branch_ID = @Branch_ID";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Branch_ID", ReferenceMasterShiftTypeSaveObj.Branch_ID);
                        connection.Open();
                        Region_ID = (int?)command.ExecuteScalar();
                    }
                }

                // Check if ShiftTypeShortCode exists
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = @"
                SELECT COUNT(*)
                FROM GNM_RefMasterDetail
                WHERE RefMaster_ID = @RefMasterID AND Region_ID = @Region_ID AND RefMasterDetail_Short_Name = @ShiftType";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        command.Parameters.AddWithValue("@Region_ID", Region_ID);
                        command.Parameters.AddWithValue("@ShiftType", ReferenceMasterShiftTypeSaveObj.ShiftType.ToLower());
                        connection.Open();
                        count = (int)command.ExecuteScalar();
                    }
                }

                if (count > 0)
                {
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        string query = @"
                    SELECT RefMasterDetail_IsActive, RefMasterDetail_ID
                    FROM GNM_RefMasterDetail
                    WHERE RefMaster_ID = @RefMasterID AND Region_ID = @Region_ID AND RefMasterDetail_Short_Name = @ShiftType";
                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Region_ID", Region_ID);
                            command.Parameters.AddWithValue("@ShiftType", ReferenceMasterShiftTypeSaveObj.ShiftType.ToLower());
                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    RefMasterDetail_IsActive = reader.GetBoolean(0);
                                    RefMasterDetail_ID = reader.GetInt32(1);
                                }
                                else
                                {
                                    throw new Exception("Failed to retrieve RefMasterDetail.");
                                }
                            }
                        }
                    }

                    if (RefMasterDetail_IsActive)
                    {
                        jsonResult = new
                        {
                            Result = "Exists",
                            Name = PName,
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "InActive",
                            Name = PName,
                        };
                    }
                }
                else
                {
                    // Insert into GNM_RefMasterDetail using stored procedure
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand("InsertRefMasterDetail", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", CompanyID);
                            command.Parameters.AddWithValue("@ShiftType", ReferenceMasterShiftTypeSaveObj.ShiftType);
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Region_ID", Region_ID);
                            command.Parameters.AddWithValue("@ModifiedBy", User_ID);
                            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            connection.Open();
                            RefMasterDetail_ID = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }

                    // Insert into GNM_RefMasterDetailLocale using stored procedure
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand("InsertRefMasterDetailLocale", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterDetail_ID", RefMasterDetail_ID);
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@ShiftType", ReferenceMasterShiftTypeSaveObj.ShiftType);
                            command.Parameters.AddWithValue("@Language_ID", userLanguageID);
                            connection.Open();
                            command.ExecuteNonQuery();
                        }
                    }

                    jsonResult = new
                    {
                        RefMasterDetail_ID = RefMasterDetail_ID,
                        Result = "Success",
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        #region ::: Refernce Master Shift Days Save /Mithun:::
        /// <summary>
        /// To save the Product Master
        /// </summary>      
        public static IActionResult ReferenceMasterShiftDaysSave(ReferenceMasterShiftDaysSaveList ReferenceMasterShiftDaysSaveObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            int userLanguageID = Convert.ToInt32(ReferenceMasterShiftDaysSaveObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(ReferenceMasterShiftDaysSaveObj.GeneralLanguageID);
            int RefMasterDetail_ID = 0;
            int count = 0;
            string PName = string.Empty;

            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = ReferenceMasterShiftDaysSaveObj.UserDetails.FirstOrDefault();
                int CompanyID = ReferenceMasterShiftDaysSaveObj.Company_ID;
                int User_ID = ReferenceMasterShiftDaysSaveObj.User_ID;

                // Get RefMasterID for SHIFTDAYS
                int RefMasterID;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFTDAYS'";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        connection.Open();
                        RefMasterID = (int)command.ExecuteScalar();
                    }
                }

                // Get Region_ID for Branch_ID
                int? Region_ID;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT Region_ID FROM GNM_Branch WHERE Branch_ID = @Branch_ID";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Branch_ID", ReferenceMasterShiftDaysSaveObj.Branch_ID);
                        connection.Open();
                        Region_ID = (int?)command.ExecuteScalar();
                    }
                }

                // Get ShiftTypeShortCode
                string ShiftTypeShortCode;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMasterDetail_Short_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ShiftType";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ShiftType", ReferenceMasterShiftDaysSaveObj.ShiftType);
                        connection.Open();
                        ShiftTypeShortCode = (string)command.ExecuteScalar();
                    }
                }

                // Check if ShiftDaysShortCode exists
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand("UP_AMERP_CheckShiftDaysExist", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        command.Parameters.AddWithValue("@Region_ID", Region_ID);
                        command.Parameters.AddWithValue("@ShiftDaysShortCode", ShiftTypeShortCode.ToLower());
                        command.Parameters.AddWithValue("@ShiftDays", ReferenceMasterShiftDaysSaveObj.ShiftDays.ToLower());
                        connection.Open();
                        count = (int)command.ExecuteScalar();
                    }
                }

                if (count > 0)
                {
                    bool RefMasterDetail_IsActive;
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand("UP_AMERP_GetExistingShiftDays", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Region_ID", Region_ID);
                            command.Parameters.AddWithValue("@ShiftDaysShortCode", ShiftTypeShortCode.ToLower());
                            command.Parameters.AddWithValue("@ShiftDays", ReferenceMasterShiftDaysSaveObj.ShiftDays.ToLower());
                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    RefMasterDetail_IsActive = reader.GetBoolean(0);
                                    RefMasterDetail_ID = reader.GetInt32(1);
                                }
                                else
                                {
                                    throw new Exception("Failed to retrieve RefMasterDetail.");
                                }
                            }
                        }
                    }

                    if (RefMasterDetail_IsActive)
                    {
                        jsonResult = new
                        {
                            Result = "Exists",
                            Name = PName,
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "InActive",
                            Name = PName,
                        };
                    }
                }
                else
                {
                    // Insert into GNM_RefMasterDetail using stored procedure
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand("UP_INS_AMERP_InsertShiftDays", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", CompanyID);
                            command.Parameters.AddWithValue("@ShiftTypeShortCode", ShiftTypeShortCode);
                            command.Parameters.AddWithValue("@ShiftDays", ReferenceMasterShiftDaysSaveObj.ShiftDays);
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Region_ID", Region_ID);
                            command.Parameters.AddWithValue("@ModifiedBy", User_ID);
                            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            connection.Open();
                            RefMasterDetail_ID = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }

                    jsonResult = new
                    {
                        RefMasterDetail_ID = RefMasterDetail_ID,
                        Result = "Success",
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: Refernce Master Shift Save /Mithun:::
        /// <summary>
        /// To save the Product Master
        /// </summary>      
        public static IActionResult ReferenceMasterShiftSave(ReferenceMasterShiftSaveList ReferenceMasterShiftSaveObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            int userLanguageID = Convert.ToInt32(ReferenceMasterShiftSaveObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(ReferenceMasterShiftSaveObj.GeneralLanguageID);
            int RefMasterDetail_ID = 0;
            int count = 0;
            string PName = string.Empty;

            try
            {
                GNM_User User = ReferenceMasterShiftSaveObj.UserDetails.FirstOrDefault();
                //GNM_User User = (GNM_User)Session["UserDetails"];
                int CompanyID = ReferenceMasterShiftSaveObj.Company_ID;
                int User_ID = ReferenceMasterShiftSaveObj.User_ID;

                // Get RefMasterID for SHIFT
                int RefMasterID;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SHIFT'";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        connection.Open();
                        RefMasterID = (int)command.ExecuteScalar();
                    }
                }

                // Get Region_ID for Branch_ID
                int? Region_ID;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT Region_ID FROM GNM_Branch WHERE Branch_ID = @Branch_ID";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Branch_ID", ReferenceMasterShiftSaveObj.Branch_ID);
                        connection.Open();
                        Region_ID = (int?)command.ExecuteScalar();
                    }
                }

                // Get ShiftTypeShortCode
                string ShiftTypeShortCode;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMasterDetail_Short_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ShiftType";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ShiftType", ReferenceMasterShiftSaveObj.ShiftType);
                        connection.Open();
                        ShiftTypeShortCode = (string)command.ExecuteScalar();
                    }
                }

                // Get ShiftDaysDesc
                string ShiftDaysDesc;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ShiftDays";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ShiftDays", ReferenceMasterShiftSaveObj.ShiftDays);
                        connection.Open();
                        ShiftDaysDesc = (string)command.ExecuteScalar();
                    }
                }

                // Construct ShortCode
                string ShortCode = ShiftTypeShortCode + '-' + ShiftDaysDesc;

                // Check if Shift exists
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand("UP_AMERP_CheckShiftExists", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        command.Parameters.AddWithValue("@Region_ID", Region_ID);
                        command.Parameters.AddWithValue("@ShortCode", ShortCode);
                        connection.Open();
                        count = (int)command.ExecuteScalar();
                    }
                }

                if (count > 0)
                {
                    bool RefMasterDetail_IsActive;
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand("UP_AMERP_GetExistingShift", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Region_ID", Region_ID);
                            command.Parameters.AddWithValue("@ShortCode", ShortCode);
                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    RefMasterDetail_IsActive = reader.GetBoolean(0);
                                    RefMasterDetail_ID = reader.GetInt32(1);
                                }
                                else
                                {
                                    throw new Exception("Failed to retrieve RefMasterDetail.");
                                }
                            }
                        }
                    }

                    if (RefMasterDetail_IsActive)
                    {
                        jsonResult = new
                        {
                            Result = "Exists",
                            Name = PName,
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "InActive",
                            Name = PName,
                        };
                    }
                }
                else
                {
                    // Insert into GNM_RefMasterDetail using stored procedure
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand("UP_INS_AMERP_InsertShift", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", CompanyID);
                            command.Parameters.AddWithValue("@ShortCode", ShortCode);
                            command.Parameters.AddWithValue("@Shift", ReferenceMasterShiftSaveObj.Shift);
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@Region_ID", Region_ID);
                            command.Parameters.AddWithValue("@ModifiedBy", User_ID);
                            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            connection.Open();
                            RefMasterDetail_ID = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }

                    jsonResult = new
                    {
                        RefMasterDetail_ID = RefMasterDetail_ID,
                        Result = "Success",
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: SelectDesignation /Mithun:::
        /// <summary>
        ///to Select Company and its child
        /// </summary> 
        public static IActionResult SelectDesignation(SelectDesignationList SelectDesignationObj, string constring, int LogException)
        {
            int UserLang = Convert.ToInt32(SelectDesignationObj.UserLanguageID);
            int GeneralLang = Convert.ToInt32(SelectDesignationObj.GeneralLanguageID);
            int Company_ID = Convert.ToInt32(SelectDesignationObj.Company_ID);
            string Query = "";
            var jsonData = default(dynamic);

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    // Get RefMaster_Name for DESIGNATION
                    string refNameQuery = "SELECT RefMaster_Name FROM GNM_RefMaster WHERE RefMaster_Name = 'DESIGNATION'";
                    string RefName = string.Empty;

                    using (SqlCommand refNameCommand = new SqlCommand(refNameQuery, connection))
                    {
                        RefName = refNameCommand.ExecuteScalar() as string;
                    }

                    if (!string.IsNullOrEmpty(RefName))
                    {
                        // Get Designation List
                        List<APINameID> DesignationList = new List<APINameID>();

                        using (SqlCommand command = new SqlCommand("UP_AMERP_GetDesignationList", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefName", RefName);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    DesignationList.Add(new APINameID
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.GetString(reader.GetOrdinal("Name"))
                                    });
                                }
                            }
                        }
                        if (UserLang == GeneralLang)
                        {
                            jsonData = new
                            {
                                Designation = DesignationList
                                            .OrderBy(a => a.Name)
                                            .Select(a => new
                                            {
                                                ID = a.ID,
                                                Name = a.Name
                                            })
                            };
                        }
                        else
                        {
                            // Get localized designations
                            List<dynamic> LocalizedDesignationList = new List<dynamic>();

                            using (SqlCommand command = new SqlCommand("UP_AMERP_GetLocalizedDesignationList", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@UserLang", UserLang);
                                command.Parameters.AddWithValue("@RefName", RefName);

                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        LocalizedDesignationList.Add(new
                                        {
                                            ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                            Name = reader.GetString(reader.GetOrdinal("Name"))
                                        });
                                    }
                                }
                            }

                            jsonData = new
                            {
                                Designation = LocalizedDesignationList.OrderBy(a => a.Name)
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Employee /Mithun:::
        /// <summary>
        ///to Select Branch for Reports
        /// </summary> 
        public static IActionResult SelectEmployee(SelectEmployeeList SelectEmployeeObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            var MasterName = SelectEmployeeObj.starts_with == null ? "" : SelectEmployeeObj.starts_with.Trim();
            string DesignationID = SelectEmployeeObj.DesignationID.TrimEnd(new char[] { ',' });
            try
            {
                int userLanguageID = Convert.ToInt32(SelectEmployeeObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectEmployeeObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectEmployeeObj.Company_ID);

                List<APINameID> EmployeeList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Execute main employee query stored procedure
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_SelectEmployees_Main", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@DesignationID", DesignationID);
                        cmd.Parameters.AddWithValue("@BranchID", SelectEmployeeObj.BranchID);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        if (!string.IsNullOrEmpty(MasterName))
                        {
                            cmd.Parameters.AddWithValue("@MasterName", MasterName);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                EmployeeList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }

                    // Execute localized employee query stored procedure if needed
                    if (userLanguageID != generalLanguageID)
                    {
                        List<APINameID> LocalizedEmployeeList = new List<APINameID>();

                        using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_SelectEmployees_Localized", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@DesignationID", DesignationID);
                            cmd.Parameters.AddWithValue("@BranchID", SelectEmployeeObj.BranchID);
                            cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                            cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    LocalizedEmployeeList.Add(new APINameID
                                    {
                                        ID = reader.GetInt32(0),
                                        Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        jsonData = new
                        {
                            EmployeeValues = from a in EmployeeList
                                             join b in LocalizedEmployeeList on a.ID equals b.ID
                                             orderby a.Name
                                             select new
                                             {
                                                 ID = b.ID,
                                                 Name = b.Name
                                             }
                        };
                    }
                    else
                    {
                        jsonData = new
                        {
                            EmployeeValues = from a in EmployeeList
                                             orderby a.Name
                                             select new
                                             {
                                                 ID = a.ID,
                                                 Name = a.Name
                                             }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SelectYearShiftDetails /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        public static IActionResult SelectYearShiftDetails(SelectYearShiftDetailsList SelectYearShiftDetailsObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic);

            try
            {
                int userLanguageID = Convert.ToInt32(SelectYearShiftDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectYearShiftDetailsObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectYearShiftDetailsObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_SelectYearShiftDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@Year", SelectYearShiftDetailsObj.Year);
                        cmd.Parameters.AddWithValue("@Shift_ID", SelectYearShiftDetailsObj.Shift_ID);
                        cmd.Parameters.AddWithValue("@Branch_ID", SelectYearShiftDetailsObj.Branch_ID);
                        cmd.Parameters.AddWithValue("@CompanyCalendar_ID", SelectYearShiftDetailsObj.CompanyCalendar_ID);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                byte IsDaily = Convert.ToByte(reader["IsOverTimeDWM"]);
                                int WorkingDays = 0;
                                string WD = reader["CompanyCalender_WorkingDays"].ToString();
                                string[] ChkArray = WD.Split(',');
                                for (int i = 0; i < ChkArray.Length; i++)
                                {
                                    if (ChkArray[i].ToString() == "1") { WorkingDays = WorkingDays + 1; }
                                }

                                string dobleTime = reader["DoubleTimeApplicableDays"].ToString();
                                string[] ChkDTArray = dobleTime.Split(',');

                                int ShiftMinutes = 0;
                                if (reader["ShiftHours"] != DBNull.Value)
                                {
                                    string[] ChkshiftArray = reader["ShiftHours"].ToString().Split(':');
                                    if (ChkArray.Length > 1)
                                        ShiftMinutes = (Convert.ToInt32(ChkshiftArray[0]) * 60) + Convert.ToInt32(ChkshiftArray[1]);
                                    else ShiftMinutes = (Convert.ToInt32(ChkshiftArray[0]) * 60);
                                }

                                string FromHours = reader["CompanyCalender_StartTime"].ToString().Split(':')[0] + ":" + reader["CompanyCalender_StartTime"].ToString().Split(':')[1];
                                string ToHours = reader["CompanyCalender_EndTime"].ToString().Split(':')[0] + ":" + reader["CompanyCalender_EndTime"].ToString().Split(':')[1];
                                string BrekFromHour = reader["Break_StartTime"].ToString().Split(':')[0] + ":" + reader["Break_StartTime"].ToString().Split(':')[1];
                                string BreakToHour = reader["Break_EndTime"].ToString().Split(':')[0] + ":" + reader["Break_EndTime"].ToString().Split(':')[1];

                                Masterdata = new
                                {
                                    CompanyCalender_ID = Convert.ToInt32(reader["CompanyCalender_ID"]),
                                    FromHours = FromHours,
                                    ToHours = ToHours,
                                    BrekFromHour = BrekFromHour,
                                    BreakToHour = BreakToHour,
                                    IsGeneralShift = Convert.ToBoolean(reader["IsGeneralShift"]),
                                    Monday = (ChkArray[0].ToString() == "1" ? true : false),
                                    Tuesday = (ChkArray[1].ToString() == "1" ? true : false),
                                    Wednesday = (ChkArray[2].ToString() == "1" ? true : false),
                                    Thursday = (ChkArray[3].ToString() == "1" ? true : false),
                                    Friday = (ChkArray[4].ToString() == "1" ? true : false),
                                    Saturday = (ChkArray[5].ToString() == "1" ? true : false),
                                    Sunday = (ChkArray[6].ToString() == "1" ? true : false),
                                    DTMonday = (ChkDTArray[0].ToString() == "1" ? true : false),
                                    DTWednesday = (ChkDTArray[2].ToString() == "1" ? true : false),
                                    DTThursday = (ChkDTArray[3].ToString() == "1" ? true : false),
                                    DTFriday = (ChkDTArray[4].ToString() == "1" ? true : false),
                                    DTSaturday = (ChkDTArray[5].ToString() == "1" ? true : false),
                                    DTSunday = (ChkDTArray[6].ToString() == "1" ? true : false),
                                    RegularTime = (IsDaily == 1) ? Convert.ToDateTime(reader["ShiftHours"].ToString()).ToString("hh:mm") : SplitAndGet((ShiftMinutes * WorkingDays).ToString()),
                                    OverTimeMinutes = (reader["OverTimeMinutes"] != DBNull.Value && Convert.ToInt32(reader["OverTimeMinutes"]) > 0) ? (IsDaily == 1) ? Convert.ToDateTime(SplitAndGet(reader["OverTimeMinutes"].ToString())).ToString("hh:mm") : SplitAndGet(reader["OverTimeMinutes"].ToString()) : "",
                                    DoubleTimeMinutes = (reader["DoubleTimeMinutes"] != DBNull.Value && Convert.ToInt32(reader["DoubleTimeMinutes"]) > 0) ? (IsDaily == 1) ? Convert.ToDateTime(SplitAndGet(reader["DoubleTimeMinutes"].ToString())).ToString("hh:mm") : SplitAndGet(reader["DoubleTimeMinutes"].ToString()) : "",
                                    YearID = Convert.ToInt32(reader["Company_FinancialYear_ID"]),
                                    Year = Convert.ToInt32(reader["CompanyCalender_Year"]),
                                    ShiftType_ID = Convert.ToInt32(reader["ShiftType_ID"]),
                                    ShiftType = reader["ShiftType"].ToString(),
                                    ShiftDays = Convert.ToInt32(reader["ShiftDays"]),
                                    ShiftDay = reader["ShiftDay"].ToString(),
                                    Shift_ID = Convert.ToInt32(reader["Shift_ID"]),
                                    Shift = reader["Shift"].ToString(),
                                    Active = Convert.ToBoolean(reader["CompanyCalendarActive"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };

            }

            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion
        public static string SplitAndGet(string SLATime)
        {
            double a = Convert.ToDouble(SLATime);
            string InTimeFormat = Math.Floor(a / 60).ToString() + ":" + (Math.Round(a % 60).ToString().Length == 1 ? "0" + Math.Round(a % 60).ToString() : Math.Round(a % 60).ToString());
            return InTimeFormat;
        }

        #region ::: SelectExistsShiftDetails /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        public static IActionResult SelectExistsShiftDetails(SelectExistsShiftDetailsList SelectExistsShiftDetailsObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic); // Placeholder for your dynamic data

            try
            {
                // Assuming these are from your existing implementation
                int userLanguageID = Convert.ToInt32(SelectExistsShiftDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectExistsShiftDetailsObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectExistsShiftDetailsObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get GNM_CompanyCalender details
                    SqlCommand cmdCalender = new SqlCommand("UP_SEL_AMERP_SelectExistsShiftDetails", conn);
                    cmdCalender.CommandType = CommandType.StoredProcedure;
                    cmdCalender.Parameters.AddWithValue("@Shift_ID", SelectExistsShiftDetailsObj.Shift_ID);

                    SqlDataReader readerCalender = cmdCalender.ExecuteReader();
                    if (!readerCalender.Read())
                    {
                        //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                        return new JsonResult(Masterdata);
                    }

                    int CompanyCalender_ID = Convert.ToInt32(readerCalender["CompanyCalender_ID"]);
                    string CompanyCalender_WorkingDays = readerCalender["CompanyCalender_WorkingDays"].ToString();
                    string DoubleTimeApplicableDays = readerCalender["DoubleTimeApplicableDays"].ToString();
                    string ShiftHours = readerCalender["ShiftHours"].ToString();
                    int OverTimeMinutes = readerCalender["OverTimeMinutes"] != DBNull.Value ? Convert.ToInt32(readerCalender["OverTimeMinutes"]) : 0;
                    int DoubleTimeMinutes = readerCalender["DoubleTimeMinutes"] != DBNull.Value ? Convert.ToInt32(readerCalender["DoubleTimeMinutes"]) : 0;

                    string FromHours = readerCalender["CompanyCalender_StartTime"].ToString().Split(':')[0] + ":" + readerCalender["CompanyCalender_StartTime"].ToString().Split(':')[1];
                    string ToHours = readerCalender["CompanyCalender_EndTime"].ToString().Split(':')[0] + ":" + readerCalender["CompanyCalender_EndTime"].ToString().Split(':')[1];
                    string BrekFromHour = readerCalender["Break_StartTime"].ToString().Split(':')[0] + ":" + readerCalender["Break_StartTime"].ToString().Split(':')[1];
                    string BreakToHour = readerCalender["Break_EndTime"].ToString().Split(':')[0] + ":" + readerCalender["Break_EndTime"].ToString().Split(':')[1];
                    bool IsGeneralShift = Convert.ToBoolean(readerCalender["IsGeneralShift"]);

                    readerCalender.Close();

                    // Get IsOverTimeDWM
                    SqlCommand cmdOverTime = new SqlCommand("UP_SEL_AMERP_SelectIsOverTimeDWM", conn);
                    cmdOverTime.CommandType = CommandType.StoredProcedure;
                    cmdOverTime.Parameters.AddWithValue("@Branch_ID", SelectExistsShiftDetailsObj.Branch_ID);

                    SqlDataReader readerOverTime = cmdOverTime.ExecuteReader();
                    byte IsDaily = 0;
                    if (readerOverTime.Read())
                    {
                        IsDaily = Convert.ToByte(readerOverTime["IsOverTimeDWM"]);
                    }

                    readerOverTime.Close();

                    // Process Working Days
                    int WorkingDays = 0;
                    string[] ChkArray = CompanyCalender_WorkingDays.Split(new char[] { ',' });
                    for (int i = 0; i < ChkArray.Length; i++)
                    {
                        if (ChkArray[i].ToString() == "1") { WorkingDays = WorkingDays + 1; }
                    }

                    // Process Double Time Applicable Days
                    string[] ChkDTArray = DoubleTimeApplicableDays.Split(new char[] { ',' });

                    // Calculate Shift Minutes
                    int ShiftMinutes = 0;
                    if (!string.IsNullOrEmpty(ShiftHours))
                    {
                        string[] ChkshiftArray = ShiftHours.Split(new char[] { ':' });
                        if (ChkshiftArray.Length > 1)
                            ShiftMinutes = (Convert.ToInt32(ChkshiftArray[0]) * 60) + Convert.ToInt32(ChkshiftArray[1]);
                        else
                            ShiftMinutes = (Convert.ToInt32(ChkshiftArray[0]) * 60);
                    }

                    // Construct Masterdata object
                    Masterdata = new
                    {
                        CompanyCalender_ID = CompanyCalender_ID,
                        FromHours = FromHours,
                        ToHours = ToHours,
                        BrekFromHour = BrekFromHour,
                        BreakToHour = BreakToHour,
                        IsGeneralShift = IsGeneralShift,
                        // Include other fields as needed
                    };
                }
            }
            catch (Exception ex)
            {
                // Error handling
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }

            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion

        #region ::: SelectExistsShiftDaysDetails /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        public static IActionResult SelectExistsShiftDaysDetails(SelectExistsShiftDaysDetailsList SelectExistsShiftDaysDetailsObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic); // Placeholder for your dynamic data

            try
            {
                // Assuming these are from your existing implementation
                int userLanguageID = Convert.ToInt32(SelectExistsShiftDaysDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectExistsShiftDaysDetailsObj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectExistsShiftDaysDetailsObj.Company_ID);


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get GNM_CompanyCalender details
                    SqlCommand cmdCalender = new SqlCommand("UP_SEL_AMERP_SelectExistsShiftDaysDetails", conn);
                    cmdCalender.CommandType = CommandType.StoredProcedure;
                    cmdCalender.Parameters.AddWithValue("@ShiftDays", SelectExistsShiftDaysDetailsObj.ShiftDays);

                    SqlDataReader readerCalender = cmdCalender.ExecuteReader();
                    if (!readerCalender.Read())
                    {
                        //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                        return new JsonResult(Masterdata);
                    }

                    int CompanyCalender_ID = Convert.ToInt32(readerCalender["CompanyCalender_ID"]);
                    string CompanyCalender_WorkingDays = readerCalender["CompanyCalender_WorkingDays"].ToString();
                    string DoubleTimeApplicableDays = readerCalender["DoubleTimeApplicableDays"].ToString();

                    readerCalender.Close();

                    // Get IsOverTimeDWM
                    SqlCommand cmdOverTime = new SqlCommand("UP_SEL_AMERP_SelectIsOverTimeDWM", conn);
                    cmdOverTime.CommandType = CommandType.StoredProcedure;
                    cmdOverTime.Parameters.AddWithValue("@Branch_ID", SelectExistsShiftDaysDetailsObj.Branch_ID);

                    SqlDataReader readerOverTime = cmdOverTime.ExecuteReader();
                    byte IsDaily = 0;
                    if (readerOverTime.Read())
                    {
                        IsDaily = Convert.ToByte(readerOverTime["IsOverTimeDWM"]);
                    }

                    readerOverTime.Close();

                    // Process Working Days
                    string[] ChkArray = CompanyCalender_WorkingDays.Split(new char[] { ',' });
                    string[] ChkDTArray = DoubleTimeApplicableDays.Split(new char[] { ',' });

                    // Construct Masterdata object
                    Masterdata = new
                    {
                        CompanyCalender_ID = CompanyCalender_ID,
                        Monday = (ChkArray[0].ToString() == "1" ? true : false),
                        Tuesday = (ChkArray[1].ToString() == "1" ? true : false),
                        Wednesday = (ChkArray[2].ToString() == "1" ? true : false),
                        Thursday = (ChkArray[3].ToString() == "1" ? true : false),
                        Friday = (ChkArray[4].ToString() == "1" ? true : false),
                        Saturday = (ChkArray[5].ToString() == "1" ? true : false),
                        Sunday = (ChkArray[6].ToString() == "1" ? true : false),
                        DTMonday = (ChkDTArray[0].ToString() == "1" ? true : false),
                        DTTuesday = (ChkDTArray[1].ToString() == "1" ? true : false),
                        DTWednesday = (ChkDTArray[2].ToString() == "1" ? true : false),
                        DTThursday = (ChkDTArray[3].ToString() == "1" ? true : false),
                        DTFriday = (ChkDTArray[4].ToString() == "1" ? true : false),
                        DTSaturday = (ChkDTArray[5].ToString() == "1" ? true : false),
                        DTSunday = (ChkDTArray[6].ToString() == "1" ? true : false)
                    };
                }
            }
            catch (Exception ex)
            {
                // Error handling
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }


        #endregion

        #region ::: Select /Mihtun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        public static IActionResult Select(SelectCompanyCalanderList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query)
        {
            try
            {
                int Count = 0;
                int Total = 0;

                var jsonData = default(dynamic);
                string YesL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "no").ToString();
                List<CompanyCalenderHolidays> companyCalenderHolidaysList = new List<CompanyCalenderHolidays>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "SELECT * FROM GNM_CompanyCalenderHolidays WHERE CompanyCalender_ID = @CompanyCalenderID";
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.Parameters.AddWithValue("@CompanyCalenderID", SelectObj.CompanyCalenderID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            companyCalenderHolidaysList.Add(new CompanyCalenderHolidays
                            {
                                CompanyCalenderHoliday_ID = Convert.ToInt32(reader["CompanyCalenderHoliday_ID"]),
                                CompanyCalenderHoliday_Date = Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]).ToString("dd-MMM-yyyy"),
                                CompanyCalenderHoliday_Name = reader["CompanyCalenderHoliday_Name"].ToString(),
                                CompanyCalenderHoliday_Date1 = Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]),
                                IsDoubleTimeApplicable = Convert.ToBoolean(reader["IsDoubleTimeApplicable"]) ? YesL : NoL
                            });
                        }
                    }
                }


                var filteredList = companyCalenderHolidaysList.AsQueryable();

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    filteredList = filteredList.FilterSearch<CompanyCalenderHolidays>(filtersObj);
                }

                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    filteredList = filteredList.AdvanceSearch<CompanyCalenderHolidays>(advnfilter);
                }

                filteredList = filteredList.OrderByField<CompanyCalenderHolidays>(sidx, sord);

                Count = filteredList.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                string EditLabel = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "edit").ToString();

                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in filteredList.AsEnumerable()
                            select new
                            {
                                ID = a.CompanyCalenderHoliday_ID,
                                edit = "<a title=" + EditLabel + " href='#' style='font-size: 13px;'  id='" + a.CompanyCalenderHoliday_ID + "' key='" + a.CompanyCalenderHoliday_ID + "' class='HolidayEdit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.CompanyCalenderHoliday_ID + "' defaultchecked=''  id='chk" + a.CompanyCalenderHoliday_ID + "' class='HolidayDelete'/>",
                                a.CompanyCalenderHoliday_Date,
                                a.CompanyCalenderHoliday_Name,
                                a.IsDoubleTimeApplicable
                            }).ToList().Paginate(page, rows),
                    records = Count
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectBranchHolidays /Mithun:::
        /// <summary>
        /// To Select Field Search Service
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectBranchCalendarHolidays(SelectBranchCalendarHolidaysList SelectBranchCalendarHolidaysObj, string constring, int LogException)
        {
            var JasonResult = default(dynamic);
            // var CompanyCalenderClient = new CompanyCalenderEntities();

            try
            {
                string YesL = CommonFunctionalities.GetGlobalResourceObject(SelectBranchCalendarHolidaysObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetGlobalResourceObject(SelectBranchCalendarHolidaysObj.UserCulture.ToString(), "no").ToString();

                List<CompanyCalenderHolidays> IECompanyCalenderHolidaysList = new List<CompanyCalenderHolidays>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_SelectBranchCalendarHolidays", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Branch_ID", SelectBranchCalendarHolidaysObj.Branch_ID);
                    cmd.Parameters.AddWithValue("@Year", SelectBranchCalendarHolidaysObj.Year);

                    SqlDataReader reader = cmd.ExecuteReader();

                    while (reader.Read())
                    {
                        IECompanyCalenderHolidaysList.Add(new CompanyCalenderHolidays
                        {
                            CompanyCalenderHoliday_Date1 = Convert.ToDateTime(reader["CompanyCalenderHoliday_Date1"]),
                            CompanyCalenderHoliday_Name = reader["CompanyCalenderHoliday_Name"].ToString(),
                            IsDoubleTimeApp = Convert.ToBoolean(reader["IsDoubleTimeApp"])
                        });
                    }
                }

                var Result = from a in IECompanyCalenderHolidaysList
                             select new
                             {
                                 CompanyCalenderHoliday_ID = 0,
                                 CompanyCalenderHoliday_Date = a.CompanyCalenderHoliday_Date1.ToString("dd-MMM-yyyy"),
                                 CompanyCalenderHoliday_Name = a.CompanyCalenderHoliday_Name,
                                 CompanyCalenderHoliday_Date1 = a.CompanyCalenderHoliday_Date1,
                                 IsDoubleTimeApplicable = a.IsDoubleTimeApp ? YesL : NoL
                             };

                JasonResult = new
                {
                    dataCamp = Result
                };

                //return Json(JasonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(JasonResult);
            }
            catch (Exception ex)
            {
                JasonResult = new
                {
                    Result = "Fail",
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(JasonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(JasonResult);
        }


        #endregion

        #region ::: SelectCompanyCalendarBranch /Mithun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        public static IActionResult SelectCompanyCalendarBranch(SelectCompanyCalendarBranchList SelectCompanyCalendarBranchObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCompanyCalendarBranchObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCompanyCalendarBranchObj.GeneralLanguageID);
                int Count = 0;
                int Total = 0;
                var jsonData = default(dynamic);
                IQueryable<BranchData> IQCompanyBranchArray = null;

                string YesL = CommonFunctionalities.GetResourceString(SelectCompanyCalendarBranchObj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectCompanyCalendarBranchObj.UserCulture.ToString(), "No").ToString();
                string Daily = CommonFunctionalities.GetResourceString(SelectCompanyCalendarBranchObj.UserCulture.ToString(), "Daily").ToString();
                string Weekly = CommonFunctionalities.GetResourceString(SelectCompanyCalendarBranchObj.UserCulture.ToString(), "Weekly").ToString();

                List<BranchData> branchList = new List<BranchData>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get Active Branches
                    List<GNM_Branch> IEBranchList = new List<GNM_Branch>();
                    using (SqlCommand command = new SqlCommand("UP_GET_AMERP_GetActiveBranches", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                IEBranchList.Add(new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(0),
                                    Branch_Name = reader.GetString(1),
                                    Branch_Active = reader.GetBoolean(2),
                                    IsOverTimeDWM = reader.GetByte(3),
                                    PayrollFileType_ID = reader.GetInt32(4)
                                });
                            }
                        }
                    }

                    // Get Branch Locales
                    List<GNM_BranchLocale> IEBranchLocaleList = new List<GNM_BranchLocale>();
                    using (SqlCommand command = new SqlCommand("UP_GET_AMERP_GetBranchLocales", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                IEBranchLocaleList.Add(new GNM_BranchLocale
                                {
                                    Branch_ID = reader.GetInt32(0),
                                    Branch_Name = reader.GetString(1)
                                });
                            }
                        }
                    }

                    // Get RefMasterDetails
                    List<GNM_RefMasterDetail> refMasterDetails = new List<GNM_RefMasterDetail>();
                    using (SqlCommand command = new SqlCommand("UP_GET_AMERP_GetRefMasterDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                refMasterDetails.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = reader.GetInt32(0),
                                    RefMasterDetail_Short_Name = reader.GetString(1)
                                });
                            }
                        }
                    }

                    if (userLanguageID == generalLanguageID)
                    {
                        branchList = (from a in IEBranchList
                                      join b in refMasterDetails on a.PayrollFileType_ID equals b.RefMasterDetail_ID
                                      where b.RefMasterDetail_Short_Name.ToUpper() == "SAP" || b.RefMasterDetail_Short_Name.ToUpper() == "FACTORY"
                                      select new BranchData
                                      {
                                          Branch_ID = a.Branch_ID,
                                          Branch_Name = a.Branch_Name,
                                          Branch_Active = a.Branch_Active ? YesL : NoL,
                                          IsOverTimeDWM = a.IsOverTimeDWM == 1 ? Daily : Weekly
                                      }).ToList();
                    }
                    else
                    {
                        branchList = (from a in IEBranchList
                                      join b in IEBranchLocaleList on a.Branch_ID equals b.Branch_ID
                                      join c in refMasterDetails on a.PayrollFileType_ID equals c.RefMasterDetail_ID
                                      where c.RefMasterDetail_Short_Name.ToUpper() == "SAP" || c.RefMasterDetail_Short_Name.ToUpper() == "FACTORY"
                                      select new BranchData
                                      {
                                          Branch_ID = a.Branch_ID,
                                          Branch_Name = b.Branch_Name,
                                          Branch_Active = a.Branch_Active ? YesL : NoL,
                                          IsOverTimeDWM = a.IsOverTimeDWM == 1 ? Daily : Weekly
                                      }).ToList();
                    }

                    IQCompanyBranchArray = branchList.AsQueryable().OrderByField(sidx, sord);

                    if (_search)
                    {
                        string decodedFilters = HttpUtility.UrlDecode(HttpUtility.UrlDecode(filters));

                        // Parse the decoded JSON into Filters object
                        Filters filtersObj = JObject.Parse(decodedFilters).ToObject<Filters>();

                        // Apply the filters to the collection
                        IQCompanyBranchArray = IQCompanyBranchArray.FilterSearch<BranchData>(filtersObj);
                    }


                    Count = IQCompanyBranchArray.Count();
                    Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                    string ViewLabel = CommonFunctionalities.GetResourceString(SelectCompanyCalendarBranchObj.UserCulture.ToString(), "view").ToString();

                    jsonData = new
                    {
                        total = Total,
                        page = page,
                        rows = IQCompanyBranchArray.AsEnumerable()
                                    .Select(a => new
                                    {
                                        Branch_ID = a.Branch_ID,
                                        edit = $"<a title={ViewLabel} href='#' style='font-size: 13px;' id='{a.Branch_ID}' key='{a.Branch_ID}' class='CompanyCalendarBranchEdit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                        delete = $"<input type='checkbox' key='{a.Branch_ID}' defaultchecked='' id='chk{a.Branch_ID}' class='CompanyCalendarBranchDelete'/>",
                                        Branch_Name = a.Branch_Name,
                                        Branch_Active = a.Branch_Active,
                                        PayRule = a.IsOverTimeDWM
                                    }).ToList().Paginate(page, rows),
                        records = Count
                    };

                    //return Json(jsonData, JsonRequestBehavior.AllowGet);
                    return new JsonResult(jsonData);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectCompanyCalendar /Mithun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        public static IActionResult SelectCompanyCalendar(SelectCompanyCalendarList SelectCompanyCalendarObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCompanyCalendarObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCompanyCalendarObj.GeneralLanguageID);
                int Count = 0;
                int Total = 0;
                int FinancialYear = DateTime.Now.Year;
                IQueryable<CompanyCalender> IQCompanyCalender = null;

                string YesL = CommonFunctionalities.GetResourceString(SelectCompanyCalendarObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectCompanyCalendarObj.UserCulture.ToString(), "no").ToString();

                List<CompanyCalender> companyCalenderList = new List<CompanyCalender>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "SELECT * FROM GNM_CompanyCalender WHERE Branch_ID = @Branch_ID";
                    if (!SelectCompanyCalendarObj.ViewAll)
                    {
                        query += " AND CompanyCalender_Year = @FinancialYear";
                    }

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@Branch_ID", SelectCompanyCalendarObj.Branch_ID);
                        if (!SelectCompanyCalendarObj.ViewAll)
                        {
                            cmd.Parameters.AddWithValue("@FinancialYear", FinancialYear);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CompanyCalender calender = new CompanyCalender
                                {
                                    CompanyCalender_ID = reader.GetInt32(reader.GetOrdinal("CompanyCalender_ID")),
                                    Shift_ID = reader.GetInt32(reader.GetOrdinal("Shift_ID")),
                                    Year = reader.GetInt32(reader.GetOrdinal("CompanyCalender_Year")),
                                    ShiftType = GetRefMasterDetailName(reader.GetInt32(reader.GetOrdinal("ShiftType_ID")), userLanguageID == generalLanguageID, constring),
                                    ShiftDays = GetRefMasterDetailName(reader.GetInt32(reader.GetOrdinal("ShiftDays")), userLanguageID == generalLanguageID, constring),
                                    ETimeSchedule = GetRefMasterDetailName(reader.GetInt32(reader.GetOrdinal("Shift_ID")), userLanguageID == generalLanguageID, constring),
                                    Active = Convert.ToBoolean(reader.GetBoolean(reader.GetOrdinal("CompanyCalendarActive"))) ? YesL : NoL,
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Branch_Name = GetBranchName(reader.GetInt32(reader.GetOrdinal("Branch_ID")), userLanguageID == generalLanguageID, constring)
                                };

                                companyCalenderList.Add(calender);
                            }
                        }
                    }
                }

                IQCompanyCalender = companyCalenderList.AsQueryable().OrderByField(sidx, sord);

                if (_search)
                {
                    // Step 1: Double decode the filters string
                    string decodedFilters = HttpUtility.UrlDecode(HttpUtility.UrlDecode(filters));

                    // Step 2: Decrypt the decoded string (if required, as per your original logic)
                    string decryptedFilters = Common.DecryptString(decodedFilters);

                    // Step 3: Parse the decrypted string into Filters object
                    Filters filtersObj = JObject.Parse(decryptedFilters).ToObject<Filters>();

                    // Step 4: Apply the filters to the IQCompanyCalender collection
                    IQCompanyCalender = IQCompanyCalender.FilterSearch<CompanyCalender>(filtersObj);
                }

                Count = IQCompanyCalender.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectCompanyCalendarObj.UserCulture.ToString(), "view").ToString();
                var jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQCompanyCalender.AsEnumerable()
                            select new
                            {
                                CompanyCalendar_ID = a.CompanyCalender_ID,
                                edit = "<a title=" + ViewLabel + " href='#' style='font-size: 13px;'  id='" + a.CompanyCalender_ID + "' key='" + a.CompanyCalender_ID + "' class='CompanyCalendarEdit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.CompanyCalender_ID + "' defaultchecked=''  id='chk" + a.CompanyCalender_ID + "' class='CompanyCalendarDelete'/>",
                                a.Year,
                                a.ShiftType,
                                a.ShiftDays,
                                a.ETimeSchedule,
                                a.Active,
                                a.Shift_ID,
                                a.Branch_ID,
                                a.Branch_Name
                            }).ToList().Paginate(page, rows),
                    records = Count
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        private static string GetRefMasterDetailName(int refMasterDetailID, bool isGeneralLanguage, string constring)
        {
            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();
                string table = isGeneralLanguage ? "GNM_RefMasterDetail" : "GNM_RefMasterDetailLocale";
                string query = $"SELECT RefMasterDetail_Name FROM {table} WHERE RefMasterDetail_ID = @RefMasterDetail_ID";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@RefMasterDetail_ID", refMasterDetailID);
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }

        private static string GetBranchName(int branchID, bool isGeneralLanguage, string constring)
        {
            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();
                string table = isGeneralLanguage ? "GNM_Branch" : "GNM_BranchLocale";
                string query = $"SELECT Branch_Name FROM {table} WHERE Branch_ID = @Branch_ID";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@Branch_ID", branchID);
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }

        #endregion

        #region ::: SaveCopyCalendar /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        public static IActionResult SaveCopyCalendar(SaveCopyCalendarList SaveCopyCalendarObj, string constring, int LogException)
        {
            var jsonData = new { Count = 0 };
            int Count = 0;
            string BranchArray = SaveCopyCalendarObj.BranchArray.TrimEnd(new char[] { ',' });
            string YearArray = SaveCopyCalendarObj.YearArray.TrimEnd(new char[] { ',' });
            string ShiftTypeArray = SaveCopyCalendarObj.ShiftTypeArray.TrimEnd(new char[] { ',' });
            string ShiftDaysArray = SaveCopyCalendarObj.ShiftDaysArray.TrimEnd(new char[] { ',' });
            string ShiftArray = SaveCopyCalendarObj.ShiftArray.TrimEnd(new char[] { ',' });
            try
            {
                var whereCondition = " Branch_ID IN (" + BranchArray + ") AND CompanyCalender_Year IN (" + YearArray + ") ";

                if (!string.IsNullOrEmpty(ShiftTypeArray))
                {
                    whereCondition += " AND ShiftType_ID IN (" + ShiftTypeArray + ")";
                }
                if (!string.IsNullOrEmpty(ShiftDaysArray))
                {
                    whereCondition += " AND ShiftDays IN (" + ShiftDaysArray + ")";
                }
                if (!string.IsNullOrEmpty(ShiftArray))
                {
                    whereCondition += " AND Shift_ID IN (" + ShiftArray + ")";
                }

                string query = "SELECT * FROM GNM_CompanyCalender WHERE CompanyCalendarActive = 1 AND " + whereCondition;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    List<GNM_CompanyCalender> calendarList = new List<GNM_CompanyCalender>();
                    while (reader.Read())
                    {
                        GNM_CompanyCalender cal = new GNM_CompanyCalender
                        {
                            CompanyCalender_ID = reader.GetInt32(reader.GetOrdinal("CompanyCalender_ID")),
                            Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                            CompanyCalender_Year = reader.GetInt32(reader.GetOrdinal("CompanyCalender_Year")),
                            Shift_ID = reader.GetInt32(reader.GetOrdinal("Shift_ID")),
                            CompanyCalender_StartTime = reader.GetTimeSpan(reader.GetOrdinal("CompanyCalender_StartTime")),
                            CompanyCalender_EndTime = reader.GetTimeSpan(reader.GetOrdinal("CompanyCalender_EndTime")),
                            CompanyCalender_WorkingDays = reader.GetString(reader.GetOrdinal("CompanyCalender_WorkingDays")),
                            ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                            ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                            IsGeneralShift = reader.GetBoolean(reader.GetOrdinal("IsGeneralShift")),
                            Break_StartTime = reader.GetTimeSpan(reader.GetOrdinal("Break_StartTime")),
                            Break_EndTime = reader.GetTimeSpan(reader.GetOrdinal("Break_EndTime")),
                            Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                            ShiftHours = reader.GetTimeSpan(reader.GetOrdinal("ShiftHours")),
                            DoubleTimeApplicableDays = reader.GetString(reader.GetOrdinal("DoubleTimeApplicableDays")),
                            ShiftType_ID = reader.GetInt32(reader.GetOrdinal("ShiftType_ID")),
                            ShiftDays = reader.GetInt32(reader.GetOrdinal("ShiftDays")),
                            CompanyCalendarActive = reader.GetBoolean(reader.GetOrdinal("CompanyCalendarActive")),
                            OverTimeMinutes = reader.GetInt32(reader.GetOrdinal("OverTimeMinutes")),
                            DoubleTimeMinutes = reader.GetInt32(reader.GetOrdinal("DoubleTimeMinutes"))
                        };

                        calendarList.Add(cal);
                    }
                    reader.Close();

                    foreach (var cal in calendarList)
                    {
                        string checkExistingQuery = "SELECT COUNT(*) FROM GNM_CompanyCalender WHERE Branch_ID = @BranchID AND CompanyCalender_Year = @Year AND Shift_ID = @ShiftID";
                        SqlCommand checkExistingCommand = new SqlCommand(checkExistingQuery, connection);
                        checkExistingCommand.Parameters.AddWithValue("@BranchID", SaveCopyCalendarObj.BranchID);
                        checkExistingCommand.Parameters.AddWithValue("@Year", cal.CompanyCalender_Year);
                        checkExistingCommand.Parameters.AddWithValue("@ShiftID", cal.Shift_ID);

                        int existingCount = (int)checkExistingCommand.ExecuteScalar();

                        if (existingCount == 0)
                        {
                            string insertProcedure = "UP_INS_AMERP_InsertCompanyCalender";
                            SqlCommand insertCommand = new SqlCommand(insertProcedure, connection);
                            insertCommand.CommandType = CommandType.StoredProcedure;
                            insertCommand.Parameters.AddWithValue("@Company_ID", cal.Company_ID);
                            insertCommand.Parameters.AddWithValue("@CompanyCalender_Year", cal.CompanyCalender_Year);
                            insertCommand.Parameters.AddWithValue("@Shift_ID", cal.Shift_ID);
                            insertCommand.Parameters.AddWithValue("@CompanyCalender_StartTime", cal.CompanyCalender_StartTime);
                            insertCommand.Parameters.AddWithValue("@CompanyCalender_EndTime", cal.CompanyCalender_EndTime);
                            insertCommand.Parameters.AddWithValue("@CompanyCalender_WorkingDays", cal.CompanyCalender_WorkingDays);
                            insertCommand.Parameters.AddWithValue("@ModifiedBy", cal.ModifiedBy);
                            insertCommand.Parameters.AddWithValue("@ModifiedDate", cal.ModifiedDate);
                            insertCommand.Parameters.AddWithValue("@IsGeneralShift", cal.IsGeneralShift);
                            insertCommand.Parameters.AddWithValue("@Break_StartTime", cal.Break_StartTime);
                            insertCommand.Parameters.AddWithValue("@Break_EndTime", cal.Break_EndTime);
                            insertCommand.Parameters.AddWithValue("@Branch_ID", SaveCopyCalendarObj.BranchID);
                            insertCommand.Parameters.AddWithValue("@ShiftHours", cal.ShiftHours);
                            insertCommand.Parameters.AddWithValue("@DoubleTimeApplicableDays", cal.DoubleTimeApplicableDays);
                            insertCommand.Parameters.AddWithValue("@ShiftType_ID", cal.ShiftType_ID);
                            insertCommand.Parameters.AddWithValue("@ShiftDays", cal.ShiftDays);
                            insertCommand.Parameters.AddWithValue("@CompanyCalendarActive", cal.CompanyCalendarActive);
                            insertCommand.Parameters.AddWithValue("@OverTimeMinutes", cal.OverTimeMinutes);
                            insertCommand.Parameters.AddWithValue("@DoubleTimeMinutes", cal.DoubleTimeMinutes);

                            Count += insertCommand.ExecuteNonQuery();

                            // Copy holidays
                            string selectHolidaysQuery = "SELECT * FROM GNM_CompanyCalenderHolidays WHERE CompanyCalender_ID = @CompanyCalender_ID";
                            SqlCommand selectHolidaysCommand = new SqlCommand(selectHolidaysQuery, connection);
                            selectHolidaysCommand.Parameters.AddWithValue("@CompanyCalender_ID", cal.CompanyCalender_ID);
                            SqlDataReader holidaysReader = selectHolidaysCommand.ExecuteReader();

                            List<GNM_CompanyCalenderHolidays> holidays = new List<GNM_CompanyCalenderHolidays>();
                            while (holidaysReader.Read())
                            {
                                GNM_CompanyCalenderHolidays holiday = new GNM_CompanyCalenderHolidays
                                {
                                    CompanyCalenderHoliday_Date = holidaysReader.GetDateTime(holidaysReader.GetOrdinal("CompanyCalenderHoliday_Date")),
                                    CompanyCalenderHoliday_Name = holidaysReader.GetString(holidaysReader.GetOrdinal("CompanyCalenderHoliday_Name")),
                                    IsDoubleTimeApplicable = holidaysReader.GetBoolean(holidaysReader.GetOrdinal("IsDoubleTimeApplicable"))
                                };

                                holidays.Add(holiday);
                            }
                            holidaysReader.Close();

                            foreach (var holiday in holidays)
                            {
                                string insertHolidayProcedure = "UP_INS_AMERP_InsertHoliday";
                                SqlCommand insertHolidayCommand = new SqlCommand(insertHolidayProcedure, connection);
                                insertHolidayCommand.CommandType = CommandType.StoredProcedure;
                                insertHolidayCommand.Parameters.AddWithValue("@CompanyCalender_ID", cal.CompanyCalender_ID);
                                insertHolidayCommand.Parameters.AddWithValue("@CompanyCalenderHoliday_Date", holiday.CompanyCalenderHoliday_Date);
                                insertHolidayCommand.Parameters.AddWithValue("@CompanyCalenderHoliday_Name", holiday.CompanyCalenderHoliday_Name);
                                insertHolidayCommand.Parameters.AddWithValue("@IsDoubleTimeApplicable", holiday.IsDoubleTimeApplicable);

                                insertHolidayCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }

                jsonData = new { Count = Count };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);

        }

        #endregion

        #region ::: SaveCopyCalendarHolidays /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        public static IActionResult SaveCopyCalendarHolidays(SaveCopyCalendarHolidaysList SaveCopyCalendarHolidaysObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            int Count = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string BranchArray = SaveCopyCalendarHolidaysObj.BranchArray.TrimEnd(new char[] { ',' });
                    string YearArray = SaveCopyCalendarHolidaysObj.YearArray.TrimEnd(new char[] { ',' });

                    string whereCondition = " Branch_ID in(" + BranchArray + ") and CompanyCalender_Year =" + YearArray + " ";

                    string query = "SELECT * FROM GNM_CompanyCalender WHERE CompanyCalendarActive = 1 AND " + whereCondition;
                    SqlCommand command = new SqlCommand(query, connection);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var year = reader["CompanyCalender_Year"];
                            var Shift_ID = reader["Shift_ID"];
                            var CompanyCalendar_Id = reader["CompanyCalender_ID"];
                            int calendarId = Convert.ToInt32(CompanyCalendar_Id);

                            string holidayQuery = "SELECT * FROM GNM_CompanyCalenderHolidays WHERE CompanyCalender_ID = @CalendarId";
                            SqlCommand holidayCommand = new SqlCommand(holidayQuery, connection);
                            holidayCommand.Parameters.AddWithValue("@CalendarId", calendarId);

                            using (SqlDataReader holidayReader = holidayCommand.ExecuteReader())
                            {
                                while (holidayReader.Read())
                                {
                                    var HolidayName = holidayReader["CompanyCalenderHoliday_Name"];
                                    var HolidayDate = holidayReader["CompanyCalenderHoliday_Date"];
                                    var IsDoubleTimeApplicable = holidayReader["IsDoubleTimeApplicable"];

                                    // Use stored procedure to check if the holiday exists
                                    SqlCommand checkExistsCommand = new SqlCommand("UP_AMERP_CheckHolidayExists", connection);
                                    checkExistsCommand.CommandType = CommandType.StoredProcedure;
                                    checkExistsCommand.Parameters.AddWithValue("@CalendarId", calendarId);
                                    checkExistsCommand.Parameters.AddWithValue("@HolidayDate", HolidayDate);

                                    int holidayCount = (int)checkExistsCommand.ExecuteScalar();

                                    if (holidayCount == 0)
                                    {
                                        // Use stored procedure to insert the holiday
                                        SqlCommand insertCommand = new SqlCommand("UP_INS_AMERP_InsertHoliday", connection);
                                        insertCommand.CommandType = CommandType.StoredProcedure;
                                        insertCommand.Parameters.AddWithValue("@CalendarId", calendarId);
                                        insertCommand.Parameters.AddWithValue("@HolidayDate", HolidayDate);
                                        insertCommand.Parameters.AddWithValue("@HolidayName", HolidayName);
                                        insertCommand.Parameters.AddWithValue("@IsDoubleTimeApplicable", IsDoubleTimeApplicable);

                                        insertCommand.ExecuteNonQuery();

                                        Count++;
                                    }
                                }
                            }
                        }
                    }
                }

                jsonData = new
                {
                    Count = Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: SaveCopyCalendar /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        public static IActionResult SaveCopyLastYearCalendar(SaveCopyLastYearCalendarList SaveCopyLastYearCalendarObj, string constring, int LogException)
        {
            var jsonData = new { Count = 0 };
            int Count = 0;

            try
            {
                string ShiftTypeArray = SaveCopyLastYearCalendarObj.ShiftTypeArray.TrimEnd(new char[] { ',' });
                string ShiftDaysArray = SaveCopyLastYearCalendarObj.ShiftDaysArray.TrimEnd(new char[] { ',' });
                string ShiftArray = SaveCopyLastYearCalendarObj.ShiftArray.TrimEnd(new char[] { ',' });

                string whereCondition = " Branch_ID = @BranchID AND CompanyCalender_Year = @FromYear ";

                if (!string.IsNullOrEmpty(ShiftTypeArray))
                {
                    whereCondition += " AND ShiftType_ID IN (" + ShiftTypeArray + ")";
                }
                if (!string.IsNullOrEmpty(ShiftDaysArray))
                {
                    whereCondition += " AND ShiftDays IN (" + ShiftDaysArray + ")";
                }
                if (!string.IsNullOrEmpty(ShiftArray))
                {
                    whereCondition += " AND Shift_ID IN (" + ShiftArray + ")";
                }

                string query = "SELECT * FROM GNM_CompanyCalender WHERE CompanyCalendarActive = 1 AND " + whereCondition;


                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@BranchID", SaveCopyLastYearCalendarObj.BranchID);
                    command.Parameters.AddWithValue("@FromYear", SaveCopyLastYearCalendarObj.FromYear);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        int year = reader.GetInt32(reader.GetOrdinal("CompanyCalender_Year"));
                        int shiftID = reader.GetInt32(reader.GetOrdinal("Shift_ID"));

                        // Check if the entry already exists for the destination year and shift
                        SqlCommand checkExistingCommand = new SqlCommand("UP_AMER_CheckExistingCompanyCalendarEntry", connection);
                        checkExistingCommand.CommandType = CommandType.StoredProcedure;
                        checkExistingCommand.Parameters.AddWithValue("@BranchID", SaveCopyLastYearCalendarObj.BranchID);
                        checkExistingCommand.Parameters.AddWithValue("@ToYear", SaveCopyLastYearCalendarObj.ToYear);
                        checkExistingCommand.Parameters.AddWithValue("@ShiftID", shiftID);

                        SqlParameter existingCountParam = checkExistingCommand.Parameters.Add("@ExistingCount", SqlDbType.Int);
                        existingCountParam.Direction = ParameterDirection.Output;

                        checkExistingCommand.ExecuteNonQuery();
                        int existingCount = Convert.ToInt32(existingCountParam.Value);

                        if (existingCount == 0)
                        {
                            // Insert new entry
                            SqlCommand insertCommand = new SqlCommand("UP_INS_AMERP_InsertCompanyCalendarCopyEntry", connection);
                            insertCommand.CommandType = CommandType.StoredProcedure;
                            insertCommand.Parameters.AddWithValue("@FromYear", SaveCopyLastYearCalendarObj.FromYear);
                            insertCommand.Parameters.AddWithValue("@ToYear", SaveCopyLastYearCalendarObj.ToYear);
                            insertCommand.Parameters.AddWithValue("@BranchID", SaveCopyLastYearCalendarObj.BranchID);
                            insertCommand.Parameters.AddWithValue("@ShiftID", shiftID);

                            SqlParameter countParam = insertCommand.Parameters.Add("@Count", SqlDbType.Int);
                            countParam.Direction = ParameterDirection.Output;

                            insertCommand.ExecuteNonQuery();
                            Count += Convert.ToInt32(countParam.Value);
                        }
                    }
                }

                jsonData = new
                {
                    Count = Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: CheckCalendarAlreadyExists /Mithun:::
        /// <summary>
        /// To Insert Calender Details
        /// </summary>
        public static IActionResult CheckCalendarAlreadyExists(CheckCalendarAlreadyExistsList CheckCalendarAlreadyExistsObj, string constring, int LogException)
        {
            var jsonData = new { Count = 0 };
            int Count = 0;
            string whereCondition = string.Empty;

            try
            {
                string ShiftArray = CheckCalendarAlreadyExistsObj.ShiftArray.TrimEnd(new char[] { ',' });

                whereCondition = "Branch_ID = @BranchID AND CompanyCalender_Year = @FromYear";

                if (!string.IsNullOrEmpty(ShiftArray))
                {
                    whereCondition += " AND Shift_ID IN (" + ShiftArray + ")";
                }

                string query = "SELECT * FROM GNM_CompanyCalender WHERE CompanyCalendarActive = 1 AND " + whereCondition;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", CheckCalendarAlreadyExistsObj.BranchID);
                        cmd.Parameters.AddWithValue("@FromYear", CheckCalendarAlreadyExistsObj.FromYear);

                        List<GNM_CompanyCalender> CalendarList = new List<GNM_CompanyCalender>();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CalendarList.Add(new GNM_CompanyCalender
                                {
                                    // Map your reader fields to GNM_CompanyCalender properties
                                    CompanyCalender_Year = (int)reader["CompanyCalender_Year"],
                                    Shift_ID = (int)reader["Shift_ID"],
                                    Branch_ID = (int)reader["Branch_ID"],
                                    // Add other properties here
                                });
                            }
                        }

                        if (CalendarList.Count > 0)
                        {
                            foreach (var calendar in CalendarList)
                            {
                                string checkQuery = "SELECT COUNT(*) FROM GNM_CompanyCalender WHERE Branch_ID = @BranchID AND CompanyCalender_Year = @ToYear AND Shift_ID = @ShiftID";
                                using (SqlCommand checkCmd = new SqlCommand(checkQuery, conn))
                                {
                                    checkCmd.Parameters.AddWithValue("@BranchID", CheckCalendarAlreadyExistsObj.BranchID);
                                    checkCmd.Parameters.AddWithValue("@ToYear", CheckCalendarAlreadyExistsObj.ToYear);
                                    checkCmd.Parameters.AddWithValue("@ShiftID", calendar.Shift_ID);

                                    int count = (int)checkCmd.ExecuteScalar();
                                    if (count > 0)
                                    {
                                        Count++;
                                    }
                                }
                            }

                            jsonData = new
                            {
                                Count = Count
                            };
                        }
                        else
                        {
                            jsonData = new
                            {
                                Count = Count
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Holidays
        /// </summary>
        public static IActionResult Delete(DeleteHolidayList DeleteObj, string constring)
        {
            var Culture = "Resource_" + DeleteObj.Lang;
            string Msg = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();
                int ID = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < Count; i++)
                            {
                                ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"]);

                                string deleteQuery = "DELETE FROM GNM_CompanyCalenderHolidays WHERE CompanyCalenderHoliday_ID = @ID";

                                using (SqlCommand cmd = new SqlCommand(deleteQuery, conn, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@ID", ID);
                                    cmd.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            //gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID), Convert.ToInt32(DeleteObj.Branch), Convert.ToInt32(DeleteObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyCalenderMaster",constring)), ID, 0, 0, "Delete", false, Convert.ToInt32(DeleteObj.MenuID));

                            Msg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                Msg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            }
                            else
                            {
                                throw;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                Msg += "An error occurred while deleting the records.";
            }
            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion

        #region ::: CheckName /Mithun:::
        /// <summary>
        /// To Check Date
        /// </summary>
        public static IActionResult CheckName(CheckNameList CheckNameObj, string constring, int LogException)
        {
            int Count = 0;

            SqlConnection connection = null;
            SqlCommand command = null;
            SqlDataReader reader = null;

            try
            {
                connection = new SqlConnection(constring);
                connection.Open();

                string HDName = CheckNameObj.HolidayName;

                string query = @"SELECT COUNT(*) FROM GNM_CompanyCalenderHolidays 
                         WHERE CompanyCalender_ID = @CompanyCalender_ID 
                         AND CompanyCalenderHoliday_ID != @HolidayID 
                         AND CompanyCalenderHoliday_Name = @HDName";

                command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@CompanyCalender_ID", CheckNameObj.CompanyCalender_ID);
                command.Parameters.AddWithValue("@HolidayID", CheckNameObj.HolidayID);
                command.Parameters.AddWithValue("@HDName", HDName);

                object result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    Count = Convert.ToInt32(result);
                }
            }
            catch (Exception ex)
            {
                // Handle exception (logging, etc.)
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Clean up resources
                if (reader != null)
                {
                    reader.Close();
                }
                if (command != null)
                {
                    command.Dispose();
                }
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                }
            }

            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region ::: CheckDate /Mithun:::
        /// <summary>
        /// To Check Date
        /// </summary>
        public static IActionResult CheckDate(CheckDateList CheckDateObj, string constring, int LogException)
        {
            int Count = 0;

            SqlConnection connection = null;
            SqlCommand command = null;
            SqlDataReader reader = null;

            try
            {
                connection = new SqlConnection(constring);
                connection.Open();

                DateTime HDate = Convert.ToDateTime(CheckDateObj.HolidayDate).Date;

                string query = @"SELECT COUNT(*) FROM GNM_CompanyCalenderHolidays 
                         WHERE CompanyCalender_ID = @CompanyCalender_ID 
                         AND CompanyCalenderHoliday_ID != @HolidayID 
                         AND CONVERT(date, CompanyCalenderHoliday_Date) = @HDate";

                command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@CompanyCalender_ID", CheckDateObj.CompanyCalender_ID);
                command.Parameters.AddWithValue("@HolidayID", CheckDateObj.HolidayID);
                command.Parameters.AddWithValue("@HDate", HDate);

                object result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    Count = Convert.ToInt32(result);
                }
            }
            catch (Exception ex)
            {
                // Handle exception (logging, etc.)
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Clean up resources
                if (reader != null)
                {
                    reader.Close();
                }
                if (command != null)
                {
                    command.Dispose();
                }
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                }
            }

            //return Count;
            return new JsonResult(Count);
        }

        #endregion


        #region ::: CalculateRegularHours /Mithun:::
        /// <summary>
        /// To Select Year Shift Details
        /// </summary> 
        public static IActionResult CalculateRegularHours(CalculateRegularHoursList CalculateRegularHoursObj, string constring, int LogException)
        {
            string Result = string.Empty;
            try
            {
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get IsOverTimeDWM
                    byte IsDaily;
                    using (var cmd = new SqlCommand("SELECT IsOverTimeDWM FROM GNM_Branch WHERE Branch_ID = @BranchID", connection))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", CalculateRegularHoursObj.BranchID);
                        IsDaily = Convert.ToByte(cmd.ExecuteScalar());
                    }

                    // Calculate Working Days
                    int WorkingDays = CalculateRegularHoursObj.Working_Days.Split(',').Count(d => d == "1");

                    // Construct the SQL query for calculating Regular Hours
                    string query = @"
                DECLARE @RegularHours TIME = CASE 
                    WHEN (CAST(@FromHours AS DATETIME) > CAST(@ToHours AS DATETIME)) 
                    THEN CAST((CAST('23:00:00.000' AS DATETIME) - CAST(@FromHours AS DATETIME) + CAST('01:00:00.000' AS DATETIME)) + 
                    (CAST(@ToHours AS DATETIME) - CAST('00:00:00.000' AS DATETIME)) - 
                    (CAST(@BreakTo AS DATETIME) - CAST(@BreakFrom AS DATETIME)) AS TIME) 
                    ELSE CAST((CAST(@ToHours AS DATETIME) - CAST(@FromHours AS DATETIME)) - 
                    (CAST(@BreakTo AS DATETIME) - CAST(@BreakFrom AS DATETIME)) AS TIME) 
                END 
                SELECT @RegularHours AS RegularHours";

                    TimeSpan regularHours;
                    using (var cmd = new SqlCommand(query, connection))
                    {
                        cmd.Parameters.AddWithValue("@FromHours", CalculateRegularHoursObj.FromHours);
                        cmd.Parameters.AddWithValue("@ToHours", CalculateRegularHoursObj.ToHours);
                        cmd.Parameters.AddWithValue("@BreakFrom", CalculateRegularHoursObj.BreakFrom);
                        cmd.Parameters.AddWithValue("@BreakTo", CalculateRegularHoursObj.BreakTo);

                        regularHours = (TimeSpan)cmd.ExecuteScalar();
                    }

                    // Calculate ShiftHours
                    int ShiftHours = (int)regularHours.TotalMinutes;

                    // Format the result
                    if (IsDaily == 1)
                    {
                        Result = regularHours.ToString(@"hh\:mm");
                    }
                    else
                    {
                        Result = ((ShiftHours * WorkingDays) / 60).ToString() + ":00";
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Result;
            return new JsonResult(Result);
        }

        #endregion


        #region ::: CheckOvertime /Mithun:::
        /// <summary>
        /// To Check OverTime
        /// </summary>
        public static IActionResult CheckOvertime(CheckOvertimeList CheckOvertimeObj, int LogException)
        {
            bool IsOverTimeLess = false;
            try
            {
                int OverTimeMinutes = 0;
                int RegularTimeMinutes = 0;
                if (CheckOvertimeObj.Overtime != "")
                {
                    string[] ChkArray = CheckOvertimeObj.Overtime.Split(new char[] { ':' });
                    if (ChkArray.Length > 1)
                        OverTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60) + Convert.ToInt32(ChkArray[1]);
                    else OverTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60);
                }
                if (CheckOvertimeObj.RegularTime != "")
                {
                    string[] ChkArray = CheckOvertimeObj.RegularTime.Split(new char[] { ':' });
                    if (ChkArray.Length > 1)
                        RegularTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60) + Convert.ToInt32(ChkArray[1]);
                    else RegularTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60);
                }
                if (OverTimeMinutes < RegularTimeMinutes) { IsOverTimeLess = true; }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return IsOverTimeLess;
            return new JsonResult(IsOverTimeLess);
        }
        #endregion


        #region ::: Checkdoubletime /Mithun:::
        /// <summary>
        /// To Check doubletime
        /// </summary>
        public static IActionResult Checkdoubletime(CheckdoubletimeList CheckdoubletimeObj, int LogException)
        {
            bool IsDoubleTimeLess = false;
            try
            {
                int OverTimeMinutes = 0;
                int doubleTimeMinutes = 0;
                if (CheckdoubletimeObj.Overtime != "")
                {
                    string[] ChkArray = CheckdoubletimeObj.Overtime.Split(new char[] { ':' });
                    if (ChkArray.Length > 1)
                        OverTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60) + Convert.ToInt32(ChkArray[1]);
                    else OverTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60);
                }
                if (CheckdoubletimeObj.doubleTime != "")
                {
                    string[] ChkArray = CheckdoubletimeObj.doubleTime.Split(new char[] { ':' });
                    if (ChkArray.Length > 1)
                        doubleTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60) + Convert.ToInt32(ChkArray[1]);
                    else doubleTimeMinutes = (Convert.ToInt32(ChkArray[0]) * 60);
                }
                if (doubleTimeMinutes < OverTimeMinutes) { IsDoubleTimeLess = true; }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return IsDoubleTimeLess;
            return new JsonResult(IsDoubleTimeLess);
        }
        #endregion




        #region ::: Data for Export /Mithun :::


        private static IQueryable<CompanyCalenderHolidays> GetCompanyCalenderHolidays(int CompanyCalenderID, string sidx, string sord, string UserCulture, bool isSearch, bool isAdvancedSearch, string filters, string Query)
        {
            List<CompanyCalenderHolidays> holidaysList = new List<CompanyCalenderHolidays>();

            string YesL = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "yes").ToString();
            string NoL = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "no").ToString();

            string query = "SELECT CompanyCalenderHoliday_ID, CompanyCalenderHoliday_Date, CompanyCalenderHoliday_Name, IsDoubleTimeApplicable " +
                           "FROM GNM_CompanyCalenderHolidays WHERE CompanyCalender_ID = @CompanyCalenderID";

            using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["YourConnectionString"].ConnectionString))
            {
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@CompanyCalenderID", CompanyCalenderID);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            holidaysList.Add(new CompanyCalenderHolidays
                            {
                                CompanyCalenderHoliday_ID = Convert.ToInt32(reader["CompanyCalenderHoliday_ID"]),
                                CompanyCalenderHoliday_Date = Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]).ToString("dd-MMM-yyyy"),
                                CompanyCalenderHoliday_Name = reader["CompanyCalenderHoliday_Name"].ToString(),
                                CompanyCalenderHoliday_Date1 = Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]),
                                IsDoubleTimeApplicable = Convert.ToBoolean(reader["IsDoubleTimeApplicable"]) ? YesL : NoL
                            });
                        }
                    }
                }
            }

            IQueryable<CompanyCalenderHolidays> IQCompanyCalenderHolidays = holidaysList.AsQueryable();

            if (isSearch)
            {
                Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                {
                    IQCompanyCalenderHolidays = IQCompanyCalenderHolidays.FilterSearch<CompanyCalenderHolidays>(filtersobj);
                }
            }

            if (isAdvancedSearch)
            {
                AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                IQCompanyCalenderHolidays = IQCompanyCalenderHolidays.AdvanceSearch<CompanyCalenderHolidays>(advnfilter);
            }

            IQCompanyCalenderHolidays = IQCompanyCalenderHolidays.OrderByField<CompanyCalenderHolidays>(sidx, sord);

            return IQCompanyCalenderHolidays;
        }

        #endregion


        #region::: Export /Mithun :::

        public static async Task<object> Export(SelectCompanyCalanderList ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                // Decrypt the Company and Shift
                string Company = Common.DecryptString(ExportObj.Company);
                string Shift = Common.DecryptString(ExportObj.Shift);

                // Create a DataTable to store the holiday data
                DataTable DtData = new DataTable();

                // Fetch data using the GetCompanyCalenderHolidays method instead of the session-based IQCompanyCalenderHolidays
                IQueryable<CompanyCalenderHolidays> IQCompanyCalenderHolidays = GetCompanyCalenderHolidays(ExportObj.CompanyCalenderID, ExportObj.sidx, ExportObj.sord, ExportObj.UserCulture, ExportObj._search, ExportObj.advnce, filters, Query);

                // Create a list for the data extraction from IQCompanyCalenderHolidays
                var CompanyCalenderHolidaysArray = from a in IQCompanyCalenderHolidays.AsEnumerable()
                                                   select new
                                                   {
                                                       a.CompanyCalenderHoliday_Date,
                                                       a.CompanyCalenderHoliday_Name
                                                   };

                // Define columns for the DataTable
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Date").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Name").ToString());

                // Get the count of holiday records
                int Count = CompanyCalenderHolidaysArray.AsEnumerable().Count();
                if (Count > 0)
                {
                    // Populate the DataTable with the holiday data
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(CompanyCalenderHolidaysArray.ElementAt(i).CompanyCalenderHoliday_Date, CompanyCalenderHolidaysArray.ElementAt(i).CompanyCalenderHoliday_Name);
                    }

                    // Create DataTable for the criteria (Company, Year, Shift)
                    DataTable DtCriteria = new DataTable();
                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Company").ToString());
                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Year").ToString());
                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Shift").ToString());
                    DtCriteria.Rows.Add(Company, ExportObj.Year, Common.DecryptString(Shift));

                    // Create DataTable for alignment
                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Company").ToString());
                    DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Year").ToString());
                    DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "Shift").ToString());
                    DtAlignment.Rows.Add(0, 0);

                    // Create the ReportExportList object
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = DtCriteria,
                        dt = DtData,
                        Alignment = DtAlignment,
                        FileName = "Companycalender", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Companycalender").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };

                    // Export the data
                    var result = await ReportExport.Export(reportExportList, constring, LogException);
                    return result.Value;
                }
                else
                {
                    // Return some message or handle the case when there is no data to export
                    return "No data available to export.";
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return "Error during export process.";
            }
        }

        #endregion



        public static UploadHolidayList SelectHoliday(DateTime HolidayDate, string HolidayName, bool IsDoubleTime, int CompanycalendarID, string connString)
        {
            UploadHolidayList HolidayUploadRow = new UploadHolidayList();

            // Define the SQL query
            string query = @"SELECT TOP 1 CompanyCalenderHoliday_Date, CompanyCalenderHoliday_Name, CompanyCalender_ID 
                     FROM GNM_CompanyCalenderHolidays
                     WHERE LOWER(CompanyCalenderHoliday_Name) = @HolidayName 
                     AND CompanyCalenderHoliday_Date = @HolidayDate 
                     AND CompanyCalender_ID = @CompanycalendarID";

            // Set up ADO.NET objects
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to avoid SQL injection
                    cmd.Parameters.AddWithValue("@HolidayName", HolidayName.ToLower());
                    cmd.Parameters.AddWithValue("@HolidayDate", HolidayDate);
                    cmd.Parameters.AddWithValue("@CompanycalendarID", CompanycalendarID);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (!reader.HasRows)  // If no holiday exists, create a new one
                        {
                            HolidayUploadRow.CompanyCalenderHoliday_Date = HolidayDate;
                            HolidayUploadRow.CompanyCalenderHoliday_Name = HolidayName;
                            HolidayUploadRow.IsDoubleTimeApplicable = IsDoubleTime ? "Yes" : "No";
                            HolidayUploadRow.DoubleTimeApplicable = IsDoubleTime;
                        }
                        else  // If holiday exists, mark as exists
                        {
                            HolidayUploadRow.IsExists = true;
                        }
                    }
                }
            }

            return HolidayUploadRow;
        }



        public static EncapHolidayList ValidateHoliday(DataTable dt, int CompanycalendarID, string UserCulture, string connString)
        {
            List<UploadHolidayList> HolidayList = new List<UploadHolidayList>();
            StringBuilder sb = new StringBuilder();
            sb.Append("<table border='1'><thead><tr><td>" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Date").ToString() + "</td><td>" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Name").ToString() + "</td><td>" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "IsDoubletime").ToString() + "</td><td>" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Remarks").ToString() + "</td></tr></thead>");
            bool IsError = false;
            bool Invaliddate = false;
            bool Duplicate = false;
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                IsError = false;
                string Remarks = string.Empty;
                UploadHolidayList rowObj = null;

                if (dt.Rows[i][0] == null || dt.Rows[i][0].ToString() == "")
                {
                    IsError = true;
                    Remarks += CommonFunctionalities.GetResourceString(UserCulture.ToString(), "DateBlank").ToString();
                }
                if (dt.Rows[i][1] == null || dt.Rows[i][1].ToString() == "")
                {
                    IsError = true;
                    Remarks += CommonFunctionalities.GetResourceString(UserCulture.ToString(), "NameBlank").ToString();
                }
                if (dt.Rows[i][2] == null || dt.Rows[i][2].ToString() == "")
                {
                    IsError = true;
                    Remarks += "\n" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "IsDoubletimeBlank").ToString();
                }

                if (!IsError)
                {
                    //DateTime Test;
                    //if (DateTime.TryParseExact(dt.Rows[i][0].ToString(), "m/dd/yyyy", null, DateTimeStyles.None, out Test) == true)
                    //{
                    var CurrentDate = DateTime.Now;
                    if (CurrentDate > Convert.ToDateTime(dt.Rows[i][0]))
                    {
                        Invaliddate = true;
                        IsError = true;
                    }
                    //}
                    //else
                    //{
                    //    Invaliddate = true;
                    //    IsError = true;
                    //}
                    if (!IsError)
                    {
                        DateTime Date = Convert.ToDateTime(dt.Rows[i][0]);
                        string Name = (dt.Rows[i][1]).ToString();
                        bool IsDoubleTime = (dt.Rows[i][2]).ToString() == "Yes" ? true : false;// Convert.ToBoolean(dt.Rows[i][2]);

                        rowObj = SelectHoliday(Date, Name, IsDoubleTime, CompanycalendarID, connString);

                        if (rowObj.IsExists == true)
                        {
                            IsError = true;
                        }
                        if (dt.AsEnumerable().Where(a => a[0].ToString().Trim() == Convert.ToDateTime(rowObj.CompanyCalenderHoliday_Date).ToString()).Count() > 1)
                        {
                            IsError = true;
                            Duplicate = true;
                        }
                        if (dt.AsEnumerable().Where(a => a[1].ToString().Trim() == rowObj.CompanyCalenderHoliday_Name).Count() > 1)
                        {
                            IsError = true;
                            Duplicate = true;
                        }
                        if (dt.AsEnumerable().Where(a => a[0].ToString().Trim() == Convert.ToDateTime(rowObj.CompanyCalenderHoliday_Date).ToString() && a[1].ToString().Trim() == rowObj.CompanyCalenderHoliday_Name).Count() > 1)
                        {
                            IsError = true;
                            Duplicate = true;
                        }
                    }
                }
                if (IsError)
                {
                    if (rowObj != null)
                    {
                        if (rowObj.IsExists == true)
                        {
                            Remarks += "\n" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "alreadyexists").ToString();
                            sb.Append("<tr><td>" + dt.Rows[i][0].ToString() + "</td><td>" + dt.Rows[i][1].ToString() + "</td><td>" + dt.Rows[i][2].ToString() + "</td>");
                            sb.Append("<td>" + Remarks + "</td></tr>");
                        }
                        if (Duplicate == true)
                        {
                            Remarks += "\n" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Duplicateentries").ToString();
                            sb.Append("<tr><td>" + dt.Rows[i][0].ToString() + "</td><td>" + dt.Rows[i][1].ToString() + "</td><td>" + dt.Rows[i][2].ToString() + "</td>");
                            sb.Append("<td>" + Remarks + "</td></tr>");
                        }
                    }
                    else if (Invaliddate == true)
                    {
                        Remarks += "\n" + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "InvalidDate").ToString();
                        sb.Append("<tr><td>" + dt.Rows[i][0].ToString() + "</td><td>" + dt.Rows[i][1].ToString() + "</td><td>" + dt.Rows[i][2].ToString() + "</td>");
                        sb.Append("<td>" + Remarks + "</td></tr>");
                    }
                    else
                    {
                        sb.Append("<tr><td>" + dt.Rows[i][0].ToString() + "</td><td>" + dt.Rows[i][1].ToString() + "</td><td>" + dt.Rows[i][2].ToString() + "</td>");
                        sb.Append("<td>" + Remarks + "</td></tr>");
                    }
                }
                else
                {
                    if (rowObj != null)
                    {
                        HolidayList.Add(rowObj);
                    }
                }
            }
            sb.Append("</table>");
            EncapHolidayList result = new EncapHolidayList();
            result.HasError = IsError;
            result.CalendarHolidayList = HolidayList;
            result.ErrorString = sb.ToString();
            return result;
        }






        #region ::: UploadTemplate :::
        public static HttpResponseMessage HolidayUpload(IFormFile CalendarHoliday, int CompanycalendarID, int LogException, string userCulture, string connString)
        {
            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
            try
            {
                Common common = new Common();
                EncapHolidayList HolidayClass = null;
                string fileExtension = Path.GetExtension(CalendarHoliday.FileName); // Get file extension
                DataTable dt = null;

                // Check if the file extension is valid
                if (fileExtension == ".xls" || fileExtension == ".xlsx" || fileExtension == ".csv")
                {
                    // Use IFormFile's OpenReadStream to process the file
                    using (var fileStream = CalendarHoliday.OpenReadStream())
                    {
                        dt = Common.ExcelReader(fileStream, fileExtension);
                    }

                    if (dt != null)
                    {
                        HolidayClass = ValidateHoliday(dt, CompanycalendarID, userCulture, connString);
                        //Session["HolidayUploaded"] = HolidayClass;

                        // If errors are found, create a response with the error file
                        if (HolidayClass.HasError)
                        {
                            // Assuming the error details are stored in HolidayClass.ErrorString
                            var errorContent = new StringContent(HolidayClass.ErrorString); // Assuming the error string is in plain text or CSV format

                            // Set up the response message for file download
                            response = new HttpResponseMessage(HttpStatusCode.OK)
                            {
                                Content = errorContent
                            };

                            // Set headers to download the file as an Excel document
                            response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.ms-excel");
                            response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                            {
                                FileName = "ErrorFile.xls"
                            };

                            return response;
                        }
                        //Session["ErrorCode"] = HolidayClass.HasError && HolidayClass.CalendarHolidayList.Count > 0 ? 1 : !HolidayClass.HasError && HolidayClass.CalendarHolidayList.Count > 0 ? 2 : 0;
                        // If there are no errors, return a success response
                        response = new HttpResponseMessage(HttpStatusCode.OK)
                        {
                            Content = new StringContent("Holiday uploaded successfully!")
                        };

                        response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
                    }
                }
                else
                {
                    //Session["ErrorCode"] = 3;
                    // Handle invalid file type case (could return a different response)
                    response = new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("Unsupported file type.")
                    };
                    response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
                }
            }
            catch (Exception ex)
            {
                // Log the exception if required
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Return an error response in case of an exception
                response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("An error occurred while processing the file upload.")
                };
                response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
            }

            return response; // Return the constructed response
        }
        #endregion













        #region ::: Classes :::

        public class UploadHolidayList
        {
            public int CompanyCalender_ID { get; set; }
            public int CompanyCalenderHoliday_ID { get; set; }
            public string CompanyCalenderHoliday_Name { get; set; }
            public DateTime CompanyCalenderHoliday_Date { get; set; }
            public string IsDoubleTimeApplicable { get; set; }
            public bool DoubleTimeApplicable { get; set; }
            public bool IsExists { get; set; }
        }

        public class EncapHolidayList
        {
            public List<UploadHolidayList> CalendarHolidayList { get; set; }
            public bool HasError { get; set; }
            public string ErrorString { get; set; }
            public bool IsValidFile
            {
                get;
                set;
            }
        }

        public class CheckdoubletimeList
        {
            public string doubleTime { get; set; }
            public string Overtime { get; set; }
        }
        public class CheckOvertimeList
        {
            public string RegularTime { get; set; }
            public string Overtime { get; set; }
        }
        public class CalculateRegularHoursList
        {
            public string Working_Days { get; set; }
            public string FromHours { get; set; }
            public string ToHours { get; set; }
            public string BreakFrom { get; set; }
            public string BreakTo { get; set; }
            public int BranchID { get; set; }
        }
        public class CheckDateList
        {
            public int HolidayID { get; set; }
            public int CompanyCalender_ID { get; set; }
            public string HolidayDate { get; set; }
        }
        public class CheckNameList
        {
            public int HolidayID { get; set; }
            public int CompanyCalender_ID { get; set; }
            public string HolidayName { get; set; }
        }
        public class DeleteHolidayList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string Lang { get; set; }
            public string key { get; set; }
        }

        public class CheckCalendarAlreadyExistsList
        {
            public int BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int FromYear { get; set; }
            public int ToYear { get; set; }
            public string ShiftArray { get; set; }
        }
        public class SaveCopyLastYearCalendarList
        {
            public int BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int FromYear { get; set; }
            public int ToYear { get; set; }
            public string ShiftArray { get; set; }
            public string ShiftDaysArray { get; set; }
            public string ShiftTypeArray { get; set; }

        }
        public class SaveCopyCalendarHolidaysList
        {
            public int BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string BranchArray { get; set; }
            public string YearArray { get; set; }

        }
        public class SaveCopyCalendarList
        {
            public int BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string BranchArray { get; set; }
            public string YearArray { get; set; }
            public string ShiftTypeArray { get; set; }
            public string ShiftDaysArray { get; set; }
            public string ShiftArray { get; set; }


        }


        public partial class GNM_CompanyCalendarNonWorkingRule
        {
            public int CompanyCalendarNonWorkingDay_ID { get; set; }
            public int CompanyCalender_ID { get; set; }
            public byte WeekDay { get; set; }
            public Nullable<int> RegularHoursFrom { get; set; }
            public Nullable<int> RegularHoursTo { get; set; }
            public Nullable<int> OvertimeHoursFrom { get; set; }
            public Nullable<int> OvertimeHoursTo { get; set; }
            public Nullable<int> DoubletimeHoursFrom { get; set; }
            public Nullable<int> DoubletimeHoursTo { get; set; }
            public Nullable<System.DateTime> WeekDate { get; set; }
            public string WeekNumber { get; set; }
            public Nullable<bool> IsBetweenWorkingDays { get; set; }

            public virtual GNM_CompanyCalender GNM_CompanyCalender { get; set; }
        }
        public partial class GNM_CompanyCalendarWorkingDate
        {
            public int CompanyCalendarWorkingDate_ID { get; set; }
            public int CompanyCalender_ID { get; set; }
            public System.DateTime WeekDate { get; set; }
            public string WeekNumber { get; set; }

            public virtual GNM_CompanyCalender GNM_CompanyCalender { get; set; }
        }
        public partial class GNM_CompanyCalenderHolidays
        {
            public int CompanyCalenderHoliday_ID { get; set; }
            public int CompanyCalender_ID { get; set; }
            public System.DateTime CompanyCalenderHoliday_Date { get; set; }
            public string CompanyCalenderHoliday_Name { get; set; }
            public bool IsDoubleTimeApplicable { get; set; }

            public virtual GNM_CompanyCalender GNM_CompanyCalender { get; set; }
        }
        public partial class GNM_CompanyCalender
        {
            public GNM_CompanyCalender()
            {
                this.GNM_CompanyCalendarNonWorkingRule = new HashSet<GNM_CompanyCalendarNonWorkingRule>();
                this.GNM_CompanyCalendarWorkingDate = new HashSet<GNM_CompanyCalendarWorkingDate>();
                this.GNM_CompanyCalenderHolidays = new HashSet<GNM_CompanyCalenderHolidays>();
            }

            public int CompanyCalender_ID { get; set; }
            public int Company_ID { get; set; }
            public int CompanyCalender_Year { get; set; }
            public int Shift_ID { get; set; }
            public System.TimeSpan CompanyCalender_StartTime { get; set; }
            public System.TimeSpan CompanyCalender_EndTime { get; set; }
            public string CompanyCalender_WorkingDays { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public bool IsGeneralShift { get; set; }
            public System.TimeSpan Break_StartTime { get; set; }
            public System.TimeSpan Break_EndTime { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public Nullable<System.TimeSpan> ShiftHours { get; set; }
            public string DoubleTimeApplicableDays { get; set; }
            public Nullable<int> OverTimeMinutes { get; set; }
            public Nullable<int> DoubleTimeMinutes { get; set; }
            public Nullable<int> ShiftType_ID { get; set; }
            public Nullable<int> ShiftDays { get; set; }
            public Nullable<bool> CompanyCalendarActive { get; set; }
            public bool IsByDate { get; set; }

            public virtual ICollection<GNM_CompanyCalendarNonWorkingRule> GNM_CompanyCalendarNonWorkingRule { get; set; }
            public virtual ICollection<GNM_CompanyCalendarWorkingDate> GNM_CompanyCalendarWorkingDate { get; set; }
            public virtual ICollection<GNM_CompanyCalenderHolidays> GNM_CompanyCalenderHolidays { get; set; }
        }
        public class SelectCompanyCalendarList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch_ID { get; set; }
            public bool ViewAll { get; set; }
            public string UserCulture { get; set; }
        }
        public class CompanyCalender
        {
            public int CompanyCalender_ID { get; set; }
            public int Shift_ID { get; set; }
            public int? Branch_ID { get; set; }
            public string Branch_Name { get; set; }
            public int Year { get; set; }
            public string ShiftType { get; set; }
            public string ShiftDays { get; set; }
            public string ETimeSchedule { get; set; }
            public string Active { get; set; }
        }

        public class SelectCompanyCalendarBranchList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
            public string UserCulture { get; set; }
        }
        public class BranchData
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int Branch_ID { get; set; }
            public string Branch_Name { get; set; }
            public string Branch_Phone { get; set; }
            public string Branch_Location { get; set; }
            public string Branch_Email { get; set; }
            public string Branch_HeadOffice { get; set; }
            public string Branch_External { get; set; }
            public string Branch_Active { get; set; }
            public string Local { get; set; }
            public string IsOverTimeDWM { get; set; }
        }

        public class SelectBranchCalendarHolidaysList
        {
            public int Branch_ID { get; set; }
            public int Year { get; set; }
            public string UserCulture { get; set; }
        }
        public class SelectCompanyCalanderList
        {
            public int Company_ID { get; set; }
            public int CompanyCalenderID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int LanguageID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string UserCulture { get; set; }
            public string Company { get; set; }
            public string Shift { get; set; }
            public int Year { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public bool _search { get; set; }
            public bool advnce { get; set; }
        }


        public class CompanyCalenderHolidays
        {

            public int CompanyCalenderHoliday_ID
            {
                get;
                set;
            }
            public string CompanyCalenderHoliday_Date
            {
                get;
                set;
            }
            public DateTime CompanyCalenderHoliday_Date1
            {
                get;
                set;
            }
            public string CompanyCalenderHoliday_Name
            {
                get;
                set;
            }
            public string IsDoubleTimeApplicable
            {
                get;
                set;
            }
            public bool IsDoubleTimeApp
            {
                get;
                set;
            }
        }
        public class SelectExistsShiftDaysDetailsList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Year { get; set; }
            public int Branch_ID { get; set; }
            public int ShiftType { get; set; }
            public int ShiftDays { get; set; }

        }
        public class SelectExistsShiftDetailsList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Year { get; set; }
            public int Shift_ID { get; set; }
            public int Branch_ID { get; set; }
            public int ShiftType { get; set; }
            public int ShiftDays { get; set; }

        }
        public class SelectYearShiftDetailsList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Year { get; set; }
            public int Shift_ID { get; set; }
            public int Branch_ID { get; set; }
            public int CompanyCalendar_ID { get; set; }

        }
        public class SelectEmployeeList
        {
            public int BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string DesignationID { get; set; }
            public string starts_with { get; set; }

        }
        public class SelectDesignationList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }
        public class ReferenceMasterShiftSaveList
        {
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int ShiftDays { get; set; }
            public string Shift { get; set; }
            public string ShiftType { get; set; }
            public List<GNM_User> UserDetails { get; set; }

        }
        public class ReferenceMasterShiftDaysSaveList
        {
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int ShiftType { get; set; }
            public string ShiftDays { get; set; }
            public List<GNM_User> UserDetails { get; set; }

        }
        public class ReferenceMasterShiftTypeSaveList
        {
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string ShiftType { get; set; }
            public string ShiftTypeLocale { get; set; }
            public List<GNM_User> UserDetails { get; set; }

        }
        public class SelectReferenceMasterList1
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int ShiftDays_ID { get; set; }
            public int ShiftType_ID { get; set; }
            public int Branch_ID { get; set; }
            public int Year { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }

        }
        public class SelectRegionForHolidayList
        {

            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_Employee_ID { get; set; }


        }
        public class SelectCalendarShiftYearList
        {
            public int BranchID { get; set; }
            public string Year { get; set; }
            public string ShiftType { get; set; }
            public string ShiftDays { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }
        public class SelectCalendarShiftList
        {
            public string BranchID { get; set; }
            public string Year { get; set; }
            public string ShiftType { get; set; }
            public string ShiftDays { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }
        public class SelectCalendarShiftDaysYearList
        {
            public int BranchID { get; set; }
            public string Year { get; set; }
            public string ShiftType { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }
        public class SelectCalendarShiftDaysList
        {
            public string BranchID { get; set; }
            public string Year { get; set; }
            public string ShiftType { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }
        public class SelectCalendarShiftTypeYearList
        {
            public int BranchID { get; set; }
            public string Year { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }
        public class SelectCalendarShiftTypeList
        {
            public string BranchID { get; set; }
            public string Year { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }
        public class SelectRegionForCopyCalendarList
        {
            public int User_Employee_ID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }
        public class SelectCalendarToYearList
        {
            public string BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }


        }
        public class SelectCalendarYearList
        {
            public string BranchID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }
        public class SelectBranchHolidayList
        {
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_Employee_ID { get; set; }
            public string RegionID { get; set; }

        }
        public class SelectBranchList
        {
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_Employee_ID { get; set; }
            public string RegionID { get; set; }

        }
        public class SelectShiftDaysList
        {
            public int Branch_ID { get; set; }
            public int ShiftType_ID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }
        public class SelectShiftTypeList
        {
            public int Branch_ID { get; set; }
            public int UserLanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }

        public class APINameID
        {
            public int ID { get; set; }
            public string Name { get; set; }
        }
        public partial class GNM_BranchLocale
        {
            public int Branch_Locale_ID { get; set; }
            public int Branch_ID { get; set; }
            public int Language_ID { get; set; }
            public string Branch_Name { get; set; }
            public string Branch_Address { get; set; }
            public string Branch_Location { get; set; }
            public string Branch_ShortName { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
        }
        public partial class GNM_BranchTaxStructure
        {
            public int BranchTaxStructure_ID { get; set; }
            public int Branch_ID { get; set; }
            public int TaxStructure_ID { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
        }
        public partial class GNM_HEADERFOOTERPRINTLOCALE
        {
            public int HEADERFOOTERPRINTLOCALE_ID { get; set; }
            public int HEADERFOOTERPRINT_ID { get; set; }
            public int LANGUAGE_ID { get; set; }
            public string HEADERTEMPLATE { get; set; }
            public string FOOTERTEMPLATE { get; set; }

            public virtual GNM_HEADERFOOTERPRINT GNM_HEADERFOOTERPRINT { get; set; }
        }
        public partial class GNM_HEADERFOOTERPRINT
        {
            public GNM_HEADERFOOTERPRINT()
            {
                this.GNM_HEADERFOOTERPRINTLOCALE = new HashSet<GNM_HEADERFOOTERPRINTLOCALE>();
            }

            public int HEADERFOOTERPRINT_ID { get; set; }
            public Nullable<int> COMPANY_ID { get; set; }
            public string HEADERTEMPLATE { get; set; }
            public string FOOTERTEMPLATE { get; set; }
            public Nullable<int> Branch_ID { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
            public virtual GNM_Company GNM_Company { get; set; }
            public virtual ICollection<GNM_HEADERFOOTERPRINTLOCALE> GNM_HEADERFOOTERPRINTLOCALE { get; set; }
        }
        public partial class GNM_EmployeeTrainingLocaleDetails
        {
            public int TrainingLocaleDetails_ID { get; set; }
            public Nullable<int> Training_ID { get; set; }
            public string TrainingLocale_Name { get; set; }
            public string TrainingLocale_Evaluation { get; set; }
            public string TrainingLocale_Faculty { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_EmployeeTrainingDetails GNM_EmployeeTrainingDetails { get; set; }
        }
        public partial class GNM_EmployeeTrainingDetails
        {
            public GNM_EmployeeTrainingDetails()
            {
                this.GNM_EmployeeTrainingLocaleDetails = new HashSet<GNM_EmployeeTrainingLocaleDetails>();
            }

            public int TrainingDetails_ID { get; set; }
            public Nullable<int> Employee_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public string TrainingName { get; set; }
            public Nullable<int> Level_ID { get; set; }
            public Nullable<System.DateTime> FromDate { get; set; }
            public Nullable<System.DateTime> ToDate { get; set; }
            public string Evaluation { get; set; }
            public string Faculty { get; set; }
            public Nullable<int> Status { get; set; }

            public virtual ICollection<GNM_EmployeeTrainingLocaleDetails> GNM_EmployeeTrainingLocaleDetails { get; set; }
            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_CompanyEmployeeLocale
        {
            public int Company_Employee_Locale_ID { get; set; }
            public int Company_Employee_ID { get; set; }
            public int Language_ID { get; set; }
            public string Company_Employee_Name { get; set; }
            public string Company_Employee_Address { get; set; }
            public string Company_Employee_Location { get; set; }

            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_CompanyEmployeeSkillset
        {
            public int Employee_Skillset_ID { get; set; }
            public int CompnayEmployee_ID { get; set; }
            public int Skillset_ID { get; set; }
            public int Employee_Skillset_Rating { get; set; }
            public Nullable<int> Level_ID { get; set; }

            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_EmployeeETODetails
        {
            public int EmployeeETO_ID { get; set; }
            public int Employee_ID { get; set; }
            public Nullable<int> Year { get; set; }
            public Nullable<int> AvailableHours { get; set; }
            public Nullable<int> UsedHours { get; set; }
            public Nullable<int> PendingHours { get; set; }
            public Nullable<int> CarryoverHours { get; set; }
            public Nullable<int> UsedCarryoverHours { get; set; }
            public Nullable<int> BankCode_ID { get; set; }
            public Nullable<int> UsedCurrentYearETOHoursJanToMar { get; set; }
            public Nullable<System.DateTime> Validity { get; set; }
            public Nullable<bool> Isactive { get; set; }

            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_EmployeeETOLogDetails
        {
            public int EmployeeETOLog_ID { get; set; }
            public int Employee_ID { get; set; }
            public int ModifiedBy { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }
            public Nullable<bool> IsAdded { get; set; }

            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_CompanyEmployeeQUALIFICATION
        {
            public int EMPLOYEEQUALIFICATION_ID { get; set; }
            public int Company_Employee_ID { get; set; }
            public string QUALIFICATION { get; set; }
            public string COURSE { get; set; }
            public string INSTITUTENAME { get; set; }
            public Nullable<int> YOP { get; set; }
            public string PASSPERCENTAGE { get; set; }

            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_CompanyEmployeeEXPERIENCE
        {
            public int GNM_CompanyEmployeeWORKING_ID { get; set; }
            public int Company_Employee_ID { get; set; }
            public string COMPANY_NAME { get; set; }
            public Nullable<int> Department_ID { get; set; }
            public Nullable<int> DESIGNATION_ID { get; set; }
            public Nullable<int> EXPERIENCE { get; set; }

            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_CompanyEmployee
        {
            public GNM_CompanyEmployee()
            {
                this.GNM_EmployeeTrainingDetails = new HashSet<GNM_EmployeeTrainingDetails>();
                this.GNM_CompanyEmployeeLocale = new HashSet<GNM_CompanyEmployeeLocale>();
                this.GNM_CompanyEmployeeSkillset = new HashSet<GNM_CompanyEmployeeSkillset>();
                this.GNM_EmployeeBranch = new HashSet<GNM_EmployeeBranch>();
                this.GNM_EmployeeETODetails = new HashSet<GNM_EmployeeETODetails>();
                this.GNM_EmployeeETOLogDetails = new HashSet<GNM_EmployeeETOLogDetails>();
                this.GNM_EmployeedownLines = new HashSet<GNM_EmployeedownLines>();
                this.GNM_EmployeedownLines1 = new HashSet<GNM_EmployeedownLines>();
                this.GNM_CompanyEmployeeQUALIFICATION = new HashSet<GNM_CompanyEmployeeQUALIFICATION>();
                this.GNM_CompanyEmployeeEXPERIENCE = new HashSet<GNM_CompanyEmployeeEXPERIENCE>();
            }

            public int Company_Employee_ID { get; set; }
            public string Employee_ID { get; set; }
            public string Company_Employee_Name { get; set; }
            public int Company_ID { get; set; }
            public int Country_ID { get; set; }
            public int State_ID { get; set; }
            public string Company_Employee_MobileNumber { get; set; }
            public string Company_Employee_Landline_Number { get; set; }
            public string Company_Employee_ZipCode { get; set; }
            public Nullable<System.DateTime> Company_Employee_ActiveFrom { get; set; }
            public Nullable<System.DateTime> Company_Employee_ValidateUpTo { get; set; }
            public bool Company_Employee_Active { get; set; }
            public Nullable<int> Company_Employee_Manager_ID { get; set; }
            public string Company_Employee_Address { get; set; }
            public string Company_Employee_Location { get; set; }
            public string Company_Employee_Email { get; set; }
            public int Company_Employee_Department_ID { get; set; }
            public int Company_Employee_Designation_ID { get; set; }
            public int ModifiedBy { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }
            public Nullable<decimal> HourlyRate { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public Nullable<bool> IsEligibleForOT { get; set; }
            public Nullable<byte> ExemptionHours { get; set; }
            public Nullable<bool> IsOnJob { get; set; }
            public Nullable<int> JobID { get; set; }
            public Nullable<bool> IsEligibleForIncentive { get; set; }
            public Nullable<bool> IsUnderProductiveMonitoring { get; set; }
            public Nullable<bool> IsConsideredForPayroll { get; set; }
            public Nullable<int> Bay_ID { get; set; }
            public string PHOTONAME { get; set; }
            public string EmployeeImagePath { get; set; }
            public Nullable<System.DateTime> ClockedOnJobStartTime { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
            public virtual ICollection<GNM_EmployeeTrainingDetails> GNM_EmployeeTrainingDetails { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeLocale> GNM_CompanyEmployeeLocale { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeSkillset> GNM_CompanyEmployeeSkillset { get; set; }
            public virtual ICollection<GNM_EmployeeBranch> GNM_EmployeeBranch { get; set; }
            public virtual ICollection<GNM_EmployeeETODetails> GNM_EmployeeETODetails { get; set; }
            public virtual ICollection<GNM_EmployeeETOLogDetails> GNM_EmployeeETOLogDetails { get; set; }
            public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines { get; set; }
            public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines1 { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeQUALIFICATION> GNM_CompanyEmployeeQUALIFICATION { get; set; }
            public virtual ICollection<GNM_CompanyEmployeeEXPERIENCE> GNM_CompanyEmployeeEXPERIENCE { get; set; }
        }
        public partial class GNM_EmployeeBranch
        {
            public int EmployeeBranch_ID { get; set; }
            public int CompanyEmployee_ID { get; set; }
            public int Branch_ID { get; set; }
            public Nullable<bool> IsDefault { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        }
        public partial class GNM_TERMSANDCONDITIONSLOCALE
        {
            public int TERMSANDCONDITIONSLOCALE_ID { get; set; }
            public int TERMSANDCONDITIONS_ID { get; set; }
            public string TERMSANDCONDITIONS { get; set; }

            public virtual GNM_TERMSANDCONDITIONS GNM_TERMSANDCONDITIONS { get; set; }
        }
        public partial class GNM_TERMSANDCONDITIONS
        {
            public GNM_TERMSANDCONDITIONS()
            {
                this.GNM_TERMSANDCONDITIONSLOCALE = new HashSet<GNM_TERMSANDCONDITIONSLOCALE>();
            }

            public int TERMSANDCONDITIONS_ID { get; set; }
            public Nullable<int> COMPANY_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public Nullable<int> TERMS_ID { get; set; }
            public Nullable<int> OBJECT_ID { get; set; }
            public string TERMSANDCONDITIONS { get; set; }
            public bool ISDEFAULT { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
            public virtual GNM_Company GNM_Company { get; set; }
            public virtual ICollection<GNM_TERMSANDCONDITIONSLOCALE> GNM_TERMSANDCONDITIONSLOCALE { get; set; }
        }
        public partial class GNM_HourlyRate
        {
            public int HourlyRate_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public decimal HourlyRate { get; set; }
            public System.DateTime EffectiveDate { get; set; }
            public Nullable<int> Modifiedby { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }
            public Nullable<decimal> OvertimeHourlyRate { get; set; }
            public Nullable<decimal> DoubleTimeHourlyRate { get; set; }
            public Nullable<decimal> BranchMobileRate { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
            public virtual GNM_Company GNM_Company { get; set; }
        }
        public partial class GNM_EmployeedownLines
        {
            public int EmployeeDownLine_ID { get; set; }
            public int CompanyEmployee_ID { get; set; }
            public int Branch_ID { get; set; }
            public int ManagerEmployee_ID { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
            public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
            public virtual GNM_CompanyEmployee GNM_CompanyEmployee1 { get; set; }
        }
        public partial class GNM_BranchTaxCodes
        {
            public int BranchTaxCode_ID { get; set; }
            public int Branch_ID { get; set; }
            public string BranchTaxCodeName { get; set; }
            public string BranchTaxCode { get; set; }
            public string GSTINPortalUserName { get; set; }
            public string GSTINPortalPassword { get; set; }

            public virtual GNM_Branch GNM_Branch { get; set; }
        }
        public partial class GNM_Branch
        {
            public GNM_Branch()
            {
                this.GNM_BranchLocale = new HashSet<GNM_BranchLocale>();
                this.GNM_BranchTaxStructure = new HashSet<GNM_BranchTaxStructure>();
                this.GNM_EmployeeBranch = new HashSet<GNM_EmployeeBranch>();
                this.GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>();
                this.GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>();
                this.GNM_HourlyRate = new HashSet<GNM_HourlyRate>();
                this.GNM_EmployeedownLines = new HashSet<GNM_EmployeedownLines>();
                this.GNM_BranchTaxCodes = new HashSet<GNM_BranchTaxCodes>();
            }

            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public string Branch_Name { get; set; }
            public string Branch_ShortName { get; set; }
            public string Branch_ZipCode { get; set; }
            public int Country_ID { get; set; }
            public int State_ID { get; set; }
            public string Branch_Phone { get; set; }
            public string Branch_Fax { get; set; }
            public bool Branch_HeadOffice { get; set; }
            public bool Branch_Active { get; set; }
            public string Branch_Address { get; set; }
            public string Branch_Location { get; set; }
            public string Branch_Email { get; set; }
            public string Branch_Mobile { get; set; }
            public Nullable<bool> Branch_External { get; set; }
            public Nullable<int> TimeZoneID { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public Nullable<int> Currency_ID { get; set; }
            public Nullable<int> LanguageID { get; set; }
            public Nullable<byte> IsOverTimeDWM { get; set; }
            public Nullable<decimal> Yearly_Sales_Target { get; set; }
            public Nullable<decimal> Rework_Target { get; set; }
            public Nullable<decimal> Cust_Satisfaction_Target { get; set; }
            public Nullable<decimal> RO_with_Rework_Target { get; set; }
            public Nullable<decimal> RO_with_Cust_Satisfaction_Target { get; set; }
            public Nullable<int> DueDays { get; set; }
            public Nullable<int> PayrollSystem_ID { get; set; }
            public Nullable<int> MaxCarryOverHours { get; set; }
            public Nullable<System.DateTime> ConsumedCarryOverByDate { get; set; }
            public Nullable<bool> IsHourlyRateBranchWise { get; set; }
            public Nullable<int> PayrollFileType_ID { get; set; }
            public Nullable<decimal> Yearly_Parts_Target { get; set; }
            public Nullable<decimal> Variance_Percentage { get; set; }
            public Nullable<decimal> Variance_Value { get; set; }
            public Nullable<int> ETOExtensionHours { get; set; }
            public Nullable<int> ETOMultiples { get; set; }
            public Nullable<decimal> BilledVsActualVariance_Percentage { get; set; }
            public Nullable<byte> TypeofPayroll { get; set; }
            public Nullable<decimal> LessVariance_Percentage { get; set; }
            public Nullable<decimal> LessVariance_Value { get; set; }
            public Nullable<decimal> IIMoreVariance_Percentage { get; set; }
            public Nullable<decimal> IIMoreVariance_Value { get; set; }
            public Nullable<decimal> IILessVariance_Percentage { get; set; }
            public Nullable<decimal> IILessVariance_Value { get; set; }
            public Nullable<int> WorkingMinutes { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
            public virtual ICollection<GNM_BranchLocale> GNM_BranchLocale { get; set; }
            public virtual ICollection<GNM_BranchTaxStructure> GNM_BranchTaxStructure { get; set; }
            public virtual ICollection<GNM_EmployeeBranch> GNM_EmployeeBranch { get; set; }
            public virtual ICollection<GNM_HEADERFOOTERPRINT> GNM_HEADERFOOTERPRINT { get; set; }
            public virtual ICollection<GNM_TERMSANDCONDITIONS> GNM_TERMSANDCONDITIONS { get; set; }
            public virtual ICollection<GNM_HourlyRate> GNM_HourlyRate { get; set; }
            public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines { get; set; }
            public virtual ICollection<GNM_BranchTaxCodes> GNM_BranchTaxCodes { get; set; }
        }
        public partial class GNM_Company_Company_Relation
        {
            public int Company_Relationship_ID { get; set; }
            public int ManufacturerCompany_ID { get; set; }
            public int DealerCompany_ID { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
            public virtual GNM_Company GNM_Company1 { get; set; }
        }
        public partial class GNM_CompanyBrands
        {
            public int Company_Brand_ID { get; set; }
            public int Company_ID { get; set; }
            public int Brand_ID { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
        }
        public partial class GNM_CompanyLocale
        {
            public int Company_Locale_ID { get; set; }
            public int Company_ID { get; set; }
            public string Company_Name { get; set; }
            public string Company_ShortName { get; set; }
            public string Company_Address { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
        }
        public partial class GNM_CompanyFinancialYear
        {
            public int Company_FinancialYear_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public int Company_FinancialYear { get; set; }
            public System.DateTime Company_FinancialYear_FromDate { get; set; }
            public System.DateTime Company_FinancialYear_ToDate { get; set; }

            public virtual GNM_Company GNM_Company { get; set; }
        }
        public partial class GNM_Company
        {
            public GNM_Company()
            {
                this.GNM_Company_Company_Relation = new HashSet<GNM_Company_Company_Relation>();
                this.GNM_Company_Company_Relation1 = new HashSet<GNM_Company_Company_Relation>();
                this.GNM_CompanyBrands = new HashSet<GNM_CompanyBrands>();
                this.GNM_CompanyLocale = new HashSet<GNM_CompanyLocale>();
                this.GNM_Branch = new HashSet<GNM_Branch>();
                this.GNM_CompanyFinancialYear = new HashSet<GNM_CompanyFinancialYear>();
                this.GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>();
                this.GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>();
                this.GNM_HourlyRate = new HashSet<GNM_HourlyRate>();
                this.GNM_CompanyEmployee = new HashSet<GNM_CompanyEmployee>();
            }

            public int Company_ID { get; set; }
            public string Company_Name { get; set; }
            public string Company_ShortName { get; set; }
            public int Currency_ID { get; set; }
            public string Company_Address { get; set; }
            public string Company_Type { get; set; }
            public bool Company_Active { get; set; }
            public string Company_LogoName { get; set; }
            public Nullable<int> Company_Parent_ID { get; set; }
            public string Remarks { get; set; }
            public byte DefaultGridSize { get; set; }
            public Nullable<decimal> JobCardCushionHours { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> CompanyTheme_ID { get; set; }
            public Nullable<int> QuotationValidity { get; set; }
            public string CompanyFont { get; set; }
            public Nullable<decimal> InventoryCarryingFactoy_Percentage { get; set; }
            public Nullable<int> OrderingCost { get; set; }

            public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation { get; set; }
            public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation1 { get; set; }
            public virtual ICollection<GNM_CompanyBrands> GNM_CompanyBrands { get; set; }
            public virtual ICollection<GNM_CompanyLocale> GNM_CompanyLocale { get; set; }
            public virtual ICollection<GNM_Branch> GNM_Branch { get; set; }
            public virtual ICollection<GNM_CompanyFinancialYear> GNM_CompanyFinancialYear { get; set; }
            public virtual ICollection<GNM_HEADERFOOTERPRINT> GNM_HEADERFOOTERPRINT { get; set; }
            public virtual ICollection<GNM_TERMSANDCONDITIONS> GNM_TERMSANDCONDITIONS { get; set; }
            public virtual ICollection<GNM_HourlyRate> GNM_HourlyRate { get; set; }
            public virtual ICollection<GNM_CompanyEmployee> GNM_CompanyEmployee { get; set; }
        }

        #endregion
    }
}
