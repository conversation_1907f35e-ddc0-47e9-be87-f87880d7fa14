using System;
using System.Collections.Generic;

namespace PBC.UtilityService.Utilities.Models
{
    public class WF_WFStepStatus
    {
        public int WFStepStatus_ID { get; set; }
        public string WFStepStatus_Nm { get; set; }
        public string StepStatusCode { get; set; }
    }

    public class WF_WFSteps
    {
        public int WFSteps_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WFStep_Name { get; set; }
        public int WFStepType_ID { get; set; }
        public int WFStepStatus_ID { get; set; }
        public bool WFStep_IsActive { get; set; }
        public string BranchCode { get; set; }
    }

    public class WF_WFStepLink
    {
        public int WFStepLink_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int Company_ID { get; set; }
        public int FrmWFSteps_ID { get; set; }
        public int WFAction_ID { get; set; }
        public int ToWFSteps_ID { get; set; }
        public int? Addresse_WFRole_ID { get; set; }
        public byte Addresse_Flag { get; set; }
        public bool IsSMSSentToCustomer { get; set; }
        public bool IsEmailSentToCustomer { get; set; }
        public bool IsSMSSentToAddressee { get; set; }
        public bool IsEmailSentToAddresse { get; set; }
        public bool AutoAllocationAllowed { get; set; }
        public bool IsVersionEnabled { get; set; }
        public int? InvokeParentWF_ID { get; set; }
        public int? InvokeParentWFLink_ID { get; set; }
        public int? InvokeChildObject_ID { get; set; }
        public int? InvokeChildObjectAction { get; set; }
        public int? WFField_ID { get; set; }
        public string AutoCondition { get; set; }
    }

    public class WF_WFCase_Progress
        {
            public int WFCaseProgress_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public int Transaction_ID { get; set; }

            public int WFSteps_ID { get; set; }

            public int? Addresse_ID { get; set; }

            public byte Addresse_Flag { get; set; }

            public DateTime Received_Time { get; set; }

            public int? Actioned_By { get; set; }

            public DateTime? Action_Time { get; set; }

            public int? Action_Chosen { get; set; }

            public string Action_Remarks { get; set; }

            public bool? Locked_Ind { get; set; }

            public int? WFNextStep_ID { get; set; }
        }

    public class Indicator
    {
        public int TransactionID { get; set; }
        public int IndicatorType { get; set; }
        public bool IsLock { get; set; }
    }

    public class HD_ServiceLevelAgreement
    {
        public int ServiceLevelAgreement_ID { get; set; }
        public int CallComplexity_ID { get; set; }
        public int CallPriority_ID { get; set; }
        public decimal ServiceLevelAgreement_Hours { get; set; }
        public int Company_ID { get; set; }
        public bool ServiceLevelAgreementHours_IsActive { get; set; }
        public int? Party_ID { get; set; }
    }

    public class GNM_RefMasterDetail
    {
        public int RefMasterDetail_ID { get; set; }
        public string RefMasterDetail_Name { get; set; }
    }

    public class GNM_RefMasterDetailLocale
    {
        public int RefMasterDetail_ID { get; set; }
        public int Language_ID { get; set; }
        public string RefMasterDetail_Name { get; set; }
    }

    public class WorkFlowSummary
    {
        public int StatusID { get; set; }
        public string StatusName { get; set; }
        public int Count { get; set; }
        public int Mode { get; set; }
        public int MaxValue { get; set; }
    }

    
}
