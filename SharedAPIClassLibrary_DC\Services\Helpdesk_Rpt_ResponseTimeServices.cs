﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class Helpdesk_Rpt_ResponseTimeServices
    {


        #region :::Select Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Select.
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult Select(Helpdesk_Rpt_ResponseTimeSelectList Helpdesk_Rpt_ResponseTimeSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonResult = default(dynamic);
            try
            {
                int Company_ID = Convert.ToInt32(Helpdesk_Rpt_ResponseTimeSelectobj.CompyID);
                int UserLang = Convert.ToInt32(Helpdesk_Rpt_ResponseTimeSelectobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(Helpdesk_Rpt_ResponseTimeSelectobj.GeneralLanguageID);

                string FromDate = Helpdesk_Rpt_ResponseTimeSelectobj.FromDate;
                string ToDate = Helpdesk_Rpt_ResponseTimeSelectobj.ToDate;
                DateTime FDate = Convert.ToDateTime(FromDate);
                DateTime TDate = Convert.ToDateTime(ToDate).AddDays(1);

                // Setup stored procedure parameters
                SqlParameter[] parameters = new SqlParameter[] {
                    new SqlParameter("@CompanyIDs", SqlDbType.VarChar) { Value = Helpdesk_Rpt_ResponseTimeSelectobj.CompanyIDs },
                    new SqlParameter("@BranchIDs", SqlDbType.VarChar) { Value = Helpdesk_Rpt_ResponseTimeSelectobj.BranchIDs },
                    new SqlParameter("@FromDate", SqlDbType.DateTime) { Value = FDate },
                    new SqlParameter("@ToDate", SqlDbType.DateTime) { Value = TDate },
                    new SqlParameter("@ActionData", SqlDbType.VarChar) { Value = Helpdesk_Rpt_ResponseTimeSelectobj.ActionData },
                    new SqlParameter("@UserLang", SqlDbType.Int) { Value = UserLang },
                    new SqlParameter("@GeneralLang", SqlDbType.Int) { Value = GeneralLang }
                };

                // Call stored procedure to get the data
                DataTable dt = ExecuteStoredProcedure("SP_AMERP_HelpDesk_GetServiceRequestReport", parameters, connString);

                // Map the results to a report list
                List<ServiceMGRToServiceENGReport> Report = dt.AsEnumerable().Select(row => new ServiceMGRToServiceENGReport
                {
                    ServiceRequest_ID = dt.Columns.Contains("ServiceRequest_ID") && row["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(row["ServiceRequest_ID"]) : 0,
                    ServiceRequestNumber = dt.Columns.Contains("ServiceRequestNumber") && row["ServiceRequestNumber"] != DBNull.Value ? row["ServiceRequestNumber"].ToString() : string.Empty,
                    ServiceRequestDateString = dt.Columns.Contains("ServiceRequestDate") && row["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(row["ServiceRequestDate"]).ToString("dd-MMM-yyyy") : string.Empty,
                    CallDateString = dt.Columns.Contains("CallDateAndTime") && row["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallDateAndTime"]).ToString("dd-MMM-yyyy hh:mm tt") : string.Empty,
                    Party = dt.Columns.Contains("Party_Name") && row["Party_Name"] != DBNull.Value ? row["Party_Name"].ToString() : string.Empty,
                    Model = dt.Columns.Contains("Model_Name") && row["Model_Name"] != DBNull.Value ? row["Model_Name"].ToString() : string.Empty,
                    Brand = dt.Columns.Contains("Brand_Name") && row["Brand_Name"] != DBNull.Value ? row["Brand_Name"].ToString() : string.Empty,
                    ProductType = dt.Columns.Contains("ProductType_Name") && row["ProductType_Name"] != DBNull.Value ? row["ProductType_Name"].ToString() : string.Empty,
                    SerialNumber = dt.Columns.Contains("SerialNumber") && row["SerialNumber"] != DBNull.Value ? row["SerialNumber"].ToString() : string.Empty,
                    ActionBy = dt.Columns.Contains("ActionBy") && row["ActionBy"] != DBNull.Value ? row["ActionBy"].ToString() : string.Empty,
                    ActionTaken = dt.Columns.Contains("ActionTaken") && row["ActionTaken"] != DBNull.Value ? row["ActionTaken"].ToString() : string.Empty,
                    AssignedTo = dt.Columns.Contains("AssignedTo") && row["AssignedTo"] != DBNull.Value ? row["AssignedTo"].ToString() : string.Empty,
                    Action_Chosen = dt.Columns.Contains("Action_Chosen") && row["Action_Chosen"] != DBNull.Value ? Convert.ToInt32(row["Action_Chosen"]) : 0,
                    CallStatus = dt.Columns.Contains("ServiceRequest_ID")
             ? GetCallStatus(connString, LogException, row["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(row["ServiceRequest_ID"]) : 0).ToString()
             : "0",
                    ResponseTime_WorkingHours = dt.Columns.Contains("ResponseTime_WorkingHours") && row["ResponseTime_WorkingHours"] != DBNull.Value ?
                                SplitAndGet(row["ResponseTime_WorkingHours"].ToString()) : string.Empty,
                    ResponseTime_24_Hours = dt.Columns.Contains("ResponseTime_24_Hours") && row["ResponseTime_24_Hours"] != DBNull.Value ?
                            SplitAndGet(row["ResponseTime_24_Hours"].ToString()) : string.Empty,
                    Company_ID = dt.Columns.Contains("Company_ID") && row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0,
                    Branch_ID = dt.Columns.Contains("Branch_ID") && row["Branch_ID"] != DBNull.Value ? Convert.ToInt32(row["Branch_ID"]) : 0,
                    Company_Name = dt.Columns.Contains("Company_Name") && row["Company_Name"] != DBNull.Value ? row["Company_Name"].ToString() : string.Empty,
                    Branch_Name = dt.Columns.Contains("Branch_Name") && row["Branch_Name"] != DBNull.Value ? row["Branch_Name"].ToString() : string.Empty,
                    Region = dt.Columns.Contains("Company_ID") ?
             getRegionNamebyCompany(connString, LogException, UserLang, GeneralLang,
             row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0) : string.Empty
                }).ToList();

                IQueryable<ServiceMGRToServiceENGReport> IQResponseTimeReport = Report.AsQueryable();

                // Filtering logic
                if (_search)
                {
                    Filters filterobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filterobj.rules.Count() > 0)
                    {
                        IQResponseTimeReport = IQResponseTimeReport.FilterSearch<ServiceMGRToServiceENGReport>(filterobj);
                    }
                }

                // Sorting logic
                switch (sidx)
                {
                    case "ServiceRequestNumber": sidx = "ServiceRequest_ID"; break;
                    case "ServiceRequestDateString": sidx = "ServiceRequestDate"; break;
                    case "CallDateString": sidx = "CallDate"; break;
                    case "ResponseTime_WorkingHours": sidx = "ResponseTime_WorkingHours_MIN"; break;
                    case "ResponseTime_24_Hours": sidx = "ResponseTime_24_Hours_MIN"; break;
                    default: break;
                };
                IQResponseTimeReport = IQResponseTimeReport.OrderByField<ServiceMGRToServiceENGReport>(sidx, sord);

                // Calculate total pages
                int count = IQResponseTimeReport.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                int NoOfEnq = IQResponseTimeReport.Select(s => s.ServiceRequest_ID).Distinct().Count();
                string TotalWorkingHours24 = count == 0 ? "0.00" : SplitAndGet(((IQResponseTimeReport.Sum(a => getMinutes(a.ResponseTime_24_Hours))) / NoOfEnq).ToString());
                string TotalWorkingHours = count == 0 ? "0.00" : SplitAndGet(((IQResponseTimeReport.Sum(a => getMinutes(a.ResponseTime_WorkingHours))) / NoOfEnq).ToString());



                // Adjusting page number
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                // Prepare the JSON response
                jsonResult = new
                {
                    TotalWorkingHours24,
                    TotalWorkingHours,
                    total = total,
                    page = page,
                    records = count,
                    data = IQResponseTimeReport.AsEnumerable()
                        .Select(a => new
                        {
                            ServiceRequest_ID = a.ServiceRequest_ID,
                            SRNumber = a.ServiceRequestNumber,
                            ServiceRequestNumber = "<span key='" + a.ServiceRequest_ID + "' style='cursor:pointer'  class='TxtHyperLink' >" + a.ServiceRequestNumber + "</span>",
                            ServiceRequestDateString = Convert.ToDateTime(a.ServiceRequestDateString).ToString("dd-MMM-yyyy"),
                            CallDateString = Convert.ToDateTime(a.CallDateString).ToString("dd-MMM-yyyy hh:mm tt"),
                            ServiceRequestDate = a.ServiceRequestDate,
                            CallDate = a.CallDate,
                            Party = a.Party,
                            Model = a.Model,
                            SerialNumber = a.SerialNumber,
                            ProductType = a.ProductType,
                            ActionTaken = a.ActionTaken,
                            AssignedTo = a.AssignedTo,
                            ActionBy = a.ActionBy,
                            CallStatus = a.CallStatus,
                            ResponseTime_WorkingHours = a.Action_Chosen == null ? "<span style='color:red'>" + a.ResponseTime_WorkingHours + "</span>" : a.ResponseTime_WorkingHours,
                            ResponseTime_24_Hours = a.Action_Chosen == null ? "<span style='color:red'>" + a.ResponseTime_24_Hours + "</span>" : a.ResponseTime_24_Hours,
                            Company_ID = a.Company_ID,
                            Branch_ID = a.Branch_ID,
                            Region = a.Region,
                            CompanyName = a.Company_Name,
                            BranchName = a.Branch_Name
                        }).ToList()
                };


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }

        public static string getRegionNamebyCompany(string connString, int LogException, int userLanguageID, int generalLanguageID, int? Company_ID)
        {
            string RegionName = "";
            SqlConnection connection = new SqlConnection(connString);
            try
            {
                // Open the connection
                connection.Open();

                // Step 1: Get the RegionID from the Company
                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetRegionIDByCompany", connection);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Company_ID", Company_ID ?? (object)DBNull.Value);

                SqlDataReader reader = cmd.ExecuteReader();
                int? RegionID = null;

                if (reader.Read())
                {
                    RegionID = reader.IsDBNull(0) ? (int?)null : reader.GetInt32(0);
                }
                reader.Close();

                // Step 2: Get the RegionName based on the RegionID and the Language IDs
                string regionQuery = userLanguageID == generalLanguageID
                    ? "SP_AMERP_HelpDesk_GetRegionNameByRegionID"
                    : "SP_AMERP_HelpDesk_GetRegionNameByRegionIDAndLanguage";

                SqlCommand regionCmd = new SqlCommand(regionQuery, connection);
                regionCmd.CommandType = CommandType.StoredProcedure;

                if (userLanguageID == generalLanguageID)
                {
                    regionCmd.Parameters.AddWithValue("@RegionID", RegionID ?? (object)DBNull.Value);
                }
                else
                {
                    regionCmd.Parameters.AddWithValue("@RegionID", RegionID ?? (object)DBNull.Value);
                    regionCmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                }

                SqlDataReader regionReader = regionCmd.ExecuteReader();

                if (regionReader.Read())
                {
                    RegionName = regionReader.IsDBNull(0) ? "" : regionReader.GetString(0);
                }

                regionReader.Close();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                connection.Close();
            }
            return RegionName;
        }

        public static string SplitAndGet(string SLATime)
        {
            if (string.IsNullOrEmpty(SLATime) || !double.TryParse(SLATime, out double a))
            {
                return "0:00"; // Default value for invalid input
            }

            string InTimeFormat = Math.Floor(a / 60).ToString() + ":" +
                                  (Math.Floor(a % 60).ToString("00")); // Ensures two-digit minutes
            return InTimeFormat;
        }


        private static int getMinutes(string p)
        {
            if (string.IsNullOrEmpty(p) || !p.Contains(":"))
            {
                return 0; // Default value for invalid input
            }

            var parts = p.Split(':');
            if (parts.Length == 2 && int.TryParse(parts[0], out int hours) && int.TryParse(parts[1], out int minutes))
            {
                return (hours * 60) + minutes;
            }

            return 0; // Default value for invalid input
        }


        private static DataTable ExecuteStoredProcedure(string storedProcedureName, SqlParameter[] parameters, string connString)
        {
            using (SqlConnection connection = new SqlConnection(connString))
            {
                SqlCommand command = new SqlCommand(storedProcedureName, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddRange(parameters);

                SqlDataAdapter dataAdapter = new SqlDataAdapter(command);
                DataTable dataTable = new DataTable();
                dataAdapter.Fill(dataTable);
                return dataTable;
            }
        }

        private static string GetCallStatus(string connString, int LogException, int ServiceRequest_ID)
        {
            string CallStatus = string.Empty;
            SqlConnection connection = new SqlConnection(connString);

            try
            {
                connection.Open();

                // Step 1: Get the CallStatus_ID from the HD_ServiceRequest table
                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCallStatusIDByServiceRequest", connection);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@ServiceRequest_ID", ServiceRequest_ID);

                SqlDataReader reader = cmd.ExecuteReader();
                int CallStatus_ID = 0;

                if (reader.Read())
                {
                    CallStatus_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0);
                }
                reader.Close();

                // Step 2: Get the CallStatus Name from the WF_WFStepStatus table using the CallStatus_ID
                if (CallStatus_ID > 0)
                {
                    SqlCommand statusCmd = new SqlCommand("SP_AMERP_HelpDesk_GetCallStatusNameByCallStatusID", connection);
                    statusCmd.CommandType = CommandType.StoredProcedure;
                    statusCmd.Parameters.AddWithValue("@CallStatus_ID", CallStatus_ID);

                    SqlDataReader statusReader = statusCmd.ExecuteReader();

                    if (statusReader.Read())
                    {
                        CallStatus = statusReader.IsDBNull(0) ? string.Empty : statusReader.GetString(0);
                    }
                    statusReader.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                connection.Close();
            }

            return CallStatus;
        }
        #endregion


        #region :::Export Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static async Task<object> Export(Helpdesk_Rpt_ResponseTimeSelectList Helpdesk_Rpt_ResponseTimeSelectobj, string DbName, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            try
            {
                DataTable dtOptions = new DataTable();
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "todate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ActionTaken").ToString());
                string frmdate = Helpdesk_Rpt_ResponseTimeSelectobj.FromDate.ToString();
                string todate = Helpdesk_Rpt_ResponseTimeSelectobj.ToDate.ToString();
                int Company_ID = Convert.ToInt32(Helpdesk_Rpt_ResponseTimeSelectobj.CompyID);
                int UserLang = Convert.ToInt32(Helpdesk_Rpt_ResponseTimeSelectobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(Helpdesk_Rpt_ResponseTimeSelectobj.GeneralLanguageID);

                string FromDate = Helpdesk_Rpt_ResponseTimeSelectobj.FromDate;
                string ToDate = Helpdesk_Rpt_ResponseTimeSelectobj.ToDate;
                DateTime FDate = Convert.ToDateTime(FromDate);
                DateTime TDate = Convert.ToDateTime(ToDate).AddDays(1);

                string ActionTaken = Helpdesk_Rpt_ResponseTimeSelectobj.ActionTaken.ToString();
                if (ActionTaken != "0")
                {
                    string[] str = ActionTaken.Split(',');

                    // Assuming GetActionsCount now returns a boolean
                    if (GetActionsCount(Helpdesk_Rpt_ResponseTimeSelectobj.CompanyIDs, connString, DbName, LogException) > 0)
                    {
                        ActionTaken = string.Empty;

                        // Loop over each action ID in the string array
                        for (int i = 0; i < str.Length; i++)
                        {
                            if (int.TryParse(str[i].ToString(), out int ActionID))
                            {

                                // Open a SQL connection for each ActionID
                                using (SqlConnection connection = new SqlConnection(connString))
                                {
                                    connection.Open();

                                    // Call the stored procedure to get the action name for the given ActionID
                                    using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetWFActionNameByID", connection))
                                    {
                                        command.CommandType = CommandType.StoredProcedure;
                                        command.Parameters.AddWithValue("@ActionID", ActionID); // Use ActionID here

                                        // Execute the command and get the result
                                        var results = command.ExecuteScalar();
                                        if (results != null)
                                        {
                                            // Append the result (action name) to the ActionTaken string
                                            ActionTaken += results.ToString() + ",";
                                        }
                                    }

                                    connection.Close();
                                }
                            }
                        }

                        // Remove the trailing comma
                        if (ActionTaken.EndsWith(","))
                        {
                            ActionTaken = ActionTaken.TrimEnd(',');
                        }
                    }
                    else
                    {
                        // If no actions, use default resource
                        ActionTaken = CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "All").ToString();
                    }
                }
                else
                {
                    ActionTaken = CommonFunctionalities.GetGlobalResourceObject(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "All").ToString();
                }

                // Setup stored procedure parameters
                SqlParameter[] parameters = new SqlParameter[] {
                    new SqlParameter("@CompanyIDs", SqlDbType.VarChar) { Value = Helpdesk_Rpt_ResponseTimeSelectobj.CompanyIDs },
                    new SqlParameter("@BranchIDs", SqlDbType.VarChar) { Value = Helpdesk_Rpt_ResponseTimeSelectobj.BranchIDs },
                    new SqlParameter("@FromDate", SqlDbType.DateTime) { Value = FDate },
                    new SqlParameter("@ToDate", SqlDbType.DateTime) { Value = TDate },
                    new SqlParameter("@ActionData", SqlDbType.VarChar) { Value = Helpdesk_Rpt_ResponseTimeSelectobj.ActionData },
                    new SqlParameter("@UserLang", SqlDbType.Int) { Value = UserLang },
                    new SqlParameter("@GeneralLang", SqlDbType.Int) { Value = GeneralLang }
                };

                // Call stored procedure to get the data
                DataTable dta = ExecuteStoredProcedure("SP_AMERP_HelpDesk_GetServiceRequestReport", parameters, connString);

                // Map the results to a report list
                List<ServiceMGRToServiceENGReport> Report = dta.AsEnumerable().Select(row => new ServiceMGRToServiceENGReport
                {
                    ServiceRequest_ID = dta.Columns.Contains("ServiceRequest_ID") && row["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(row["ServiceRequest_ID"]) : 0,
                    ServiceRequestNumber = dta.Columns.Contains("ServiceRequestNumber") && row["ServiceRequestNumber"] != DBNull.Value ? row["ServiceRequestNumber"].ToString() : string.Empty,
                    ServiceRequestDateString = dta.Columns.Contains("ServiceRequestDate") && row["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(row["ServiceRequestDate"]).ToString("dd-MMM-yyyy") : string.Empty,
                    CallDateString = dta.Columns.Contains("CallDateAndTime") && row["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(row["CallDateAndTime"]).ToString("dd-MMM-yyyy hh:mm tt") : string.Empty,
                    Party = dta.Columns.Contains("Party_Name") && row["Party_Name"] != DBNull.Value ? row["Party_Name"].ToString() : string.Empty,
                    Model = dta.Columns.Contains("Model_Name") && row["Model_Name"] != DBNull.Value ? row["Model_Name"].ToString() : string.Empty,
                    Brand = dta.Columns.Contains("Brand_Name") && row["Brand_Name"] != DBNull.Value ? row["Brand_Name"].ToString() : string.Empty,
                    ProductType = dta.Columns.Contains("ProductType_Name") && row["ProductType_Name"] != DBNull.Value ? row["ProductType_Name"].ToString() : string.Empty,
                    SerialNumber = dta.Columns.Contains("SerialNumber") && row["SerialNumber"] != DBNull.Value ? row["SerialNumber"].ToString() : string.Empty,
                    ActionBy = dta.Columns.Contains("ActionBy") && row["ActionBy"] != DBNull.Value ? row["ActionBy"].ToString() : string.Empty,
                    ActionTaken = dta.Columns.Contains("ActionTaken") && row["ActionTaken"] != DBNull.Value ? row["ActionTaken"].ToString() : string.Empty,
                    AssignedTo = dta.Columns.Contains("AssignedTo") && row["AssignedTo"] != DBNull.Value ? row["AssignedTo"].ToString() : string.Empty,
                    Action_Chosen = dta.Columns.Contains("Action_Chosen") && row["Action_Chosen"] != DBNull.Value ? Convert.ToInt32(row["Action_Chosen"]) : 0,
                    CallStatus = dta.Columns.Contains("ServiceRequest_ID") ? GetCallStatus(connString, LogException, row["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(row["ServiceRequest_ID"]) : 0).ToString() : "0",
                    ResponseTime_WorkingHours = dta.Columns.Contains("ResponseTime_WorkingHours") && row["ResponseTime_WorkingHours"] != DBNull.Value ?
                                SplitAndGet(row["ResponseTime_WorkingHours"].ToString()) : string.Empty,
                    ResponseTime_24_Hours = dta.Columns.Contains("ResponseTime_24_Hours") && row["ResponseTime_24_Hours"] != DBNull.Value ?
                            SplitAndGet(row["ResponseTime_24_Hours"].ToString()) : string.Empty,
                    Company_ID = dta.Columns.Contains("Company_ID") && row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0,
                    Branch_ID = dta.Columns.Contains("Branch_ID") && row["Branch_ID"] != DBNull.Value ? Convert.ToInt32(row["Branch_ID"]) : 0,
                    Company_Name = dta.Columns.Contains("Company_Name") && row["Company_Name"] != DBNull.Value ? row["Company_Name"].ToString() : string.Empty,
                    Branch_Name = dta.Columns.Contains("Branch_Name") && row["Branch_Name"] != DBNull.Value ? row["Branch_Name"].ToString() : string.Empty,
                    Region = dta.Columns.Contains("Company_ID") ?
             getRegionNamebyCompany(connString, LogException, UserLang, GeneralLang,
             row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : 0) : string.Empty
                }).ToList();

                IQueryable<ServiceMGRToServiceENGReport> IQResponseTimeReport = Report.AsQueryable();
                if (!string.IsNullOrEmpty(filter) && filter != "null" && filter != "undefined")
                {
                    Filters filters = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filter))).ToObject<Filters>();
                    if (filters.rules.Any())
                    {
                        IQResponseTimeReport = IQResponseTimeReport.FilterSearch<ServiceMGRToServiceENGReport>(filters);
                    }
                }

                // Sorting logic
                switch (sidx)
                {
                    case "ServiceRequestNumber": sidx = "ServiceRequest_ID"; break;
                    case "ServiceRequestDateString": sidx = "ServiceRequestDate"; break;
                    case "CallDateString": sidx = "CallDate"; break;
                    case "ResponseTime_WorkingHours": sidx = "ResponseTime_WorkingHours_MIN"; break;
                    case "ResponseTime_24_Hours": sidx = "ResponseTime_24_Hours_MIN"; break;
                    default: break;
                };
                IQResponseTimeReport = IQResponseTimeReport.OrderByField<ServiceMGRToServiceENGReport>(sidx, sord);

                // Calculate total pages
                int count = IQResponseTimeReport.Count();
                int NoOfEnq = IQResponseTimeReport.Select(s => s.ServiceRequest_ID).Distinct().Count();
                string TotalWorkingHours24 = count == 0 ? "0.00" : SplitAndGet(((IQResponseTimeReport.Sum(a => getMinutes(a.ResponseTime_24_Hours))) / NoOfEnq).ToString());
                string TotalWorkingHours = count == 0 ? "0.00" : SplitAndGet(((IQResponseTimeReport.Sum(a => getMinutes(a.ResponseTime_WorkingHours))) / NoOfEnq).ToString());
                dtOptions.Rows.Add(frmdate, todate, ActionTaken);

                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "Company").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "Branch").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ServiceRequestNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ServiceRequestDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "CallDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "Party").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "Model").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "SerialNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ProductType").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ActionBy").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ActionTaken").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "AssignedTo").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "status").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ResponseTimeInWorkingHours").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ResponseTime24Hours").ToString());
                List<ServiceMGRToServiceENGReport> iArray = IQResponseTimeReport.ToList();
                int countex = iArray.Count();
                for (int i = 0; i < countex; i++)
                {
                    dt.Rows.Add(
                            iArray[i].Region,
                            iArray[i].Company_Name,
                            iArray[i].Branch_Name,
                            iArray[i].ServiceRequestNumber,
                            iArray[i].ServiceRequestDateString,
                            iArray[i].CallDateString,
                            iArray[i].Party,
                            iArray[i].Model,
                            iArray[i].SerialNumber,
                            iArray[i].ProductType,
                            iArray[i].ActionBy,
                            iArray[i].ActionTaken,
                            iArray[i].AssignedTo,
                            iArray[i].CallStatus,
                            iArray[i].ResponseTime_WorkingHours,//List[i].ResponseTime_WorkingHoursSPAN,
                                                                //List[i].ResponseTime_24_HoursSPAN
                                                                //Convert.ToDecimal(List[i].ResponseTime_WorkingHours.Replace(':', '.')) > Convert.ToDecimal(List[i].ResponseTime_24_Hours.Replace(':', '.')) ? List[i].ResponseTime_WorkingHours : List[i].ResponseTime_24_Hours
                            iArray[i].ResponseTime_24_Hours
                        );
                }
                dt.Rows.Add("", "", "", "", "", "", "", "", "", "", "", "", "", CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "AverageTime").ToString(), TotalWorkingHours.ToString(), TotalWorkingHours24.ToString());
                DataSet ds = new DataSet();
                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Columns.Add("");
                dtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1);
                DataSet Ds = new DataSet();
                ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                {
                    FileName = "ResponseTimeReport",
                    Branch = Helpdesk_Rpt_ResponseTimeSelectobj.Branch,
                    Company_ID = Helpdesk_Rpt_ResponseTimeSelectobj.Company_ID,
                    UserCulture = Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture,
                    dt = dt, // You can populate this with actual data as needed
                    exprtType = Helpdesk_Rpt_ResponseTimeSelectobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                    Header = CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ResponseTimeReport").ToString(),
                    Options = dtOptions, // Populate this with your report options
                    selection = ds, // Add selection-related data here
                    Alignment = dtAlignment // Define alignment details for table columns
                };
                var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                return result.Value;
                //ReportExport1.Export(exprtType, dt, dtOptions, ds, dtAlignment, "ResponseTimeReport", HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "ResponseTimeReport").ToString());
                //  ReportExportCR5.Export(Helpdesk_Rpt_ResponseTimeSelectobj.exprtType, dt, dtOptions, ds, dtAlignment, "ResponseTimeReport", CommonFunctionalities.GetResourceString(Helpdesk_Rpt_ResponseTimeSelectobj.UserCulture.ToString(), "ResponseTimeReport").ToString());
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return false;
        }

        private static int GetActionsCount(string CompanyIDs, string connString, string DbName, int LogException)
        {
            CompanyIDs = CompanyIDs.TrimEnd(new char[] { ',' });
            int Count = 0;
            int WorkFlowID = Common.GetWorkFlowID("Case Registration", DbName, connString, LogException);

            SqlConnection connection = new SqlConnection(connString);

            try
            {
                connection.Open();

                // Step 1: Call the stored procedure with the necessary parameters
                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetActionsCountByCompany", connection);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);

                // Step 2: Execute the stored procedure and retrieve the count
                SqlDataReader reader = cmd.ExecuteReader();
                if (reader.Read())
                {
                    Count = reader.GetInt32(0); // Assumes the first column is the count
                }
                reader.Close();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                connection.Close();
            }

            return Count;
        }
        #endregion


        #region :::GetActionTaken Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// GetActionTaken
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetActionTaken(Helpdesk_Rpt_ResponseTimeGetActionTakenList Helpdesk_Rpt_ResponseTimeGetActionTakenobj, string connString, string DbName, int LogException)
        {
            string CompanyIDs = Helpdesk_Rpt_ResponseTimeGetActionTakenobj.CompanyIDs.TrimEnd(',');
            var JsonData = default(dynamic);

            try
            {
                int WorkFlowID = Common.GetWorkFlowID("Case Registration", DbName, connString, LogException);
                var actions = new List<dynamic>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Initialize the command to call the stored procedure
                    using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetActionsByCompanyAndWorkflow", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                        command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);

                        // Execute the command
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                actions.Add(new
                                {
                                    ID = reader["WFACTION_ID"],
                                    Name = reader["WFAction_Name"]
                                });
                            }
                        }
                    }

                    connection.Close();
                }

                JsonData = new { Actions = actions };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return new JsonResult(JsonData);
        }
        #endregion


        #region :::getWorkingHours Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// getWorkingHours
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult getWorkingHours(Helpdesk_Rpt_ResponseTimegetWorkingHoursList Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, string connString, string DbName, int LogException)
        {
            CallDateDetail detailForCallDate = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.Calldate, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.companyID);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTimeMinutes;
            double ETM = detailForCallDate.endTimeMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTimeMinutes;
            double BETM = detailForCallDate.BreakEndTimeMinute;
            double TotalBreakTime = BETM - BSTM;

            if (startTime < endTime)
            {
                // Make sure getWorkingHoursForNonNightShift returns an IActionResult
                return getWorkingHoursForNonNightShift(Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, connString, LogException, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.Calldate, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.ToDate, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.companyID, detailForCallDate);
            }
            else
            {
                // Make sure getWorkingHoursForNightShift returns an IActionResult
                return getWorkingHoursForNightShift(Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, connString, LogException, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.Calldate, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.ToDate, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.companyID, detailForCallDate);
            }
        }



        public static IActionResult getWorkingHoursForNonNightShift(Helpdesk_Rpt_ResponseTimegetWorkingHoursList Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, string connString, int LogException, DateTime? Calldate, DateTime ToDate, int companyID, CallDateDetail detailForCallDate)
        {
            DateTime? ActualCallDate = Calldate;
            //CallDateDetail detailForCallDate = new CallDateDetail();
            //detailForCallDate = GetDetailsForDate(Calldate, companyID);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTime.TotalMinutes;
            double ETM = detailForCallDate.endTime.TotalMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTime.TotalMinutes;
            double BETM = detailForCallDate.BreakEndTime.TotalMinutes;
            double TotalBreakTime = BETM - BSTM;
            double timeFromCallDate = 0.0;
            double timeFromToday = 0.0;

            if (Calldate.Value.Date == ToDate.Date && Calldate.Value.TimeOfDay < endTime && ToDate.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
            {
                if (ToDate.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > BreakstartTime && ToDate.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = BSTM - Calldate.Value.TimeOfDay.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                }
                else if (ToDate.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - BETM);
                }
                else if (ToDate.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes);
                }
            }
            else if (Calldate.Value.Date == ToDate.Date && Calldate.Value.TimeOfDay < endTime && ToDate.TimeOfDay > endTime && Calldate.Value.TimeOfDay > startTime)
            {
                if (Calldate.Value.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                }
                else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = (ETM - BETM);
                }
                else if (Calldate.Value.TimeOfDay > BreakEndTime)
                {
                    timeFromToday = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                }
            }
            else if (Calldate.Value.Date == ToDate.Date && ToDate.TimeOfDay < endTime && Calldate.Value.TimeOfDay < startTime)
            {
                if (ToDate.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - STM;
                }
                else if (ToDate.TimeOfDay > BreakstartTime && ToDate.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = BSTM - STM;
                }
                else if (ToDate.TimeOfDay > BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - STM) - (BETM - BSTM);
                }
            }
            else if (Calldate.Value.Date == ToDate.Date && ToDate.TimeOfDay > endTime && Calldate.Value.TimeOfDay < startTime)
            {
                timeFromToday = final;
            }
            if (Calldate.Value.Date < ToDate.Date)
            {
                if (Calldate.Value.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
                {
                    if (Calldate.Value.TimeOfDay < BreakstartTime)
                    {
                        timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                    }
                    else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                    {
                        timeFromCallDate = ETM - BETM;
                    }
                    else if (Calldate.Value.TimeOfDay > BreakEndTime)
                    {
                        timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    Calldate = Calldate.Value.AddDays(1);
                }
                else if (Calldate.Value.TimeOfDay < startTime)
                {
                    timeFromCallDate = final;
                    Calldate = Calldate.Value.AddDays(1);
                }
                else if (Calldate.Value.TimeOfDay > endTime)
                {
                    Calldate = Calldate.Value.AddDays(1);
                }
            }
            double TotalWorkingHours = 0.0;
            int yearChng = Calldate.Value.Year;
            CallDateDetail detail = new CallDateDetail();
            detail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, Calldate, companyID);
            while (Calldate.Value.Date < ToDate.Date)
            {
                string day = Calldate.Value.DayOfWeek.ToString();
                int year = Calldate.Value.Year;
                if (Calldate.Value.Year != yearChng)
                {
                    detail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, Calldate, companyID);
                }
                if (detail.WorkDays.Contains(day))
                {
                    TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                }
                Calldate = Calldate.Value.AddDays(1);
                yearChng = Calldate.Value.Year;
            }
            CallDateDetail todaysDetail = new CallDateDetail();
            todaysDetail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, ToDate, companyID);
            if (ToDate.TimeOfDay > todaysDetail.startTime && ToDate.TimeOfDay < todaysDetail.endTime && ToDate.Date != ActualCallDate.Value.Date)
            {
                if (ToDate.TimeOfDay < todaysDetail.BreakstartTime)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > todaysDetail.BreakstartTime && ToDate.TimeOfDay < todaysDetail.BreakEndTime)
                {
                    timeFromToday = todaysDetail.BreakstartTime.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > todaysDetail.BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes) - (todaysDetail.BreakEndTime.TotalMinutes - todaysDetail.BreakstartTime.TotalMinutes);
                }
            }
            if (ToDate.TimeOfDay > todaysDetail.endTime && ToDate.Date != ActualCallDate.Value.Date)
            {
                timeFromToday = todaysDetail.WorkHours;
            }
            List<DateTime> Hcount = GetCompanyCalenderHolidays(connString, LogException, companyID, Convert.ToDateTime(ActualCallDate), ToDate);
            double HDtime = 0.0;
            int holidayYear = 0;
            double holidayWhours = 0;

            foreach (var holidayDate in Hcount)
            {
                CallDateDetail holidayDetail = new CallDateDetail();

                // Check if the year has changed or if it's the first iteration
                if (holidayYear == 0 || holidayDate.Year != holidayYear)
                {
                    // Retrieve work hours for the specific holiday date
                    holidayDetail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, holidayDate, companyID);
                    holidayWhours = holidayDetail.WorkHours;
                }
                // Accumulate work hours for each holiday
                HDtime += holidayWhours;

                // Update holidayYear to track the current year
                holidayYear = holidayDate.Year;
            }
            // HDtime now holds the total work hours for all holidays in Hcount

            string h = (timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime).ToString();
            var val = h.Split('.');
            val[0] = val[0].Contains("-") ? "0" : val[0].ToString();
            return new JsonResult(Convert.ToInt32(val[0]));
        }

        public static IActionResult getWorkingHoursForNightShift(Helpdesk_Rpt_ResponseTimegetWorkingHoursList Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, string connString, int LogException, DateTime? Calldate, DateTime ToDate, int companyID, CallDateDetail detailForCallDate)
        {
            DateTime? ActualCallDate = Calldate;
            //CallDateDetail detailForCallDate = new CallDateDetail();
            //detailForCallDate = GetDetailsForDate(Calldate, companyID);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTimeMinutes;
            double ETM = detailForCallDate.endTimeMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTimeMinutes;
            double BETM = detailForCallDate.BreakEndTimeMinute;
            double TotalBreakTime = BETM - BSTM;
            double timeFromCallDate = 0.0;
            double timeFromToday = 0.0;

            if (Calldate.Value.Date == ToDate.Date)
            {
                if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (ToDate.TimeOfDay.TotalMinutes + 1440) < ETM)
                {
                    if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BSTM)
                    {
                        timeFromToday = (ToDate.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                    }
                    else if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BETM)
                    {
                        timeFromToday = ((ToDate.TimeOfDay.TotalMinutes + 1440) - BETM);
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes + 1440 > BETM)
                    {
                        timeFromToday = (ToDate.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                    }
                }
                else if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (ToDate.TimeOfDay.TotalMinutes + 1440) > ETM)
                {
                    timeFromToday = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                }
                else if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes) < STM && (ToDate.TimeOfDay.TotalMinutes) > STM)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - STM;
                }
                else if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes) > STM && (ToDate.TimeOfDay.TotalMinutes) > STM)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                }
            }
            if (Calldate.Value.Date < ToDate.Date)
            {
                if (Calldate.Value.TimeOfDay.TotalMinutes < ETM && (Calldate.Value.TimeOfDay.TotalMinutes) > STM)
                {
                    if (Calldate.Value.TimeOfDay.TotalMinutes < BSTM)
                    {
                        timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes > BSTM && Calldate.Value.TimeOfDay.TotalMinutes < BETM)
                    {
                        timeFromCallDate = ETM - BETM;
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes > BETM)
                    {
                        timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    Calldate = Calldate.Value.AddDays(1);
                }
                else if (Calldate.Value.TimeOfDay.TotalMinutes < STM)
                {
                    if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM)
                    {
                        if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BSTM)
                        {
                            timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                        }
                        else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BETM)
                        {
                            timeFromCallDate = ETM - (BETM);
                        }
                        else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BETM)
                        {
                            timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                        }
                        //Calldate = Calldate.Value.AddDays(1);
                    }
                    else
                    {
                        timeFromCallDate = final;
                        Calldate = Calldate.Value.AddDays(1);
                    }
                }
                else if (Calldate.Value.TimeOfDay.TotalMinutes > ETM)
                {
                    Calldate = Calldate.Value.AddDays(1);
                }
            }
            double TotalWorkingHours = 0.0;
            int yearChng = Calldate.Value.Year;
            CallDateDetail detail = new CallDateDetail();
            detail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, Calldate, companyID);
            double temp = 0.0;
            while (Calldate.Value.Date < ToDate.Date)
            {
                string day = Calldate.Value.DayOfWeek.ToString();
                int year = Calldate.Value.Year;
                if (Calldate.Value.Year != yearChng)
                {
                    detail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, Calldate, companyID);
                }
                if (detail.WorkDays.Contains(day))
                {
                    TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                    temp = detail.WorkHours;
                }
                Calldate = Calldate.Value.AddDays(1);
                yearChng = Calldate.Value.Year;
            }
            CallDateDetail todaysDetail = new CallDateDetail();
            todaysDetail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, ToDate, companyID);
            if (ToDate.TimeOfDay.TotalMinutes > todaysDetail.startTimeMinutes && ToDate.TimeOfDay.TotalMinutes < todaysDetail.endTimeMinutes && ToDate.Date != ActualCallDate.Value.Date)
            {
                if (ToDate.TimeOfDay.TotalMinutes < todaysDetail.BreakstartTimeMinutes)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes;
                }
                else if (ToDate.TimeOfDay.TotalMinutes > todaysDetail.BreakstartTimeMinutes && ToDate.TimeOfDay.TotalMinutes < todaysDetail.BreakEndTimeMinute)
                {
                    timeFromToday = todaysDetail.BreakstartTimeMinutes - todaysDetail.startTimeMinutes;
                }
                else if (ToDate.TimeOfDay.TotalMinutes > todaysDetail.BreakEndTimeMinute)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes);
                }
            }
            else if ((ToDate.TimeOfDay.TotalMinutes + 1440) > todaysDetail.startTimeMinutes && (ToDate.TimeOfDay.TotalMinutes + 1440) < todaysDetail.endTimeMinutes && ToDate.Date != ActualCallDate.Value.Date)
            {
                timeFromToday = ((ToDate.TimeOfDay.TotalMinutes + 1440) - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes) - temp;
                DateTime Tday = ActualCallDate.Value.AddDays(1);
                if (Tday.Date == ToDate.Date)
                {
                    timeFromToday = timeFromToday - todaysDetail.WorkHours;
                }
            }
            List<DateTime> Hcount = GetCompanyCalenderHolidays(connString, LogException, companyID, Convert.ToDateTime(ActualCallDate), ToDate);
            double HDtime = 0.0;
            int holidayYear = 0;
            double holidayWhours = 0;

            foreach (var holidayDate in Hcount)
            {
                CallDateDetail holidayDetail = new CallDateDetail();

                // Check if the year has changed or if it's the first iteration
                if (holidayYear == 0 || holidayDate.Year != holidayYear)
                {
                    // Retrieve work hours for the specific holiday date
                    holidayDetail = GetDetailsForDate(connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, holidayDate, companyID);
                    holidayWhours = holidayDetail.WorkHours;
                }
                // Accumulate work hours for each holiday
                HDtime += holidayWhours;

                // Update holidayYear to track the current year
                holidayYear = holidayDate.Year;
            }
            string h = (timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime).ToString();
            var val = h.Split('.');
            val[0] = val[0].Contains("-") ? "0" : val[0].ToString();
            return new JsonResult(Convert.ToInt32(val[0]));
        }

        public static CallDateDetail GetDetailsForDate(string connString, Helpdesk_Rpt_ResponseTimegetWorkingHoursList Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, DateTime? Calldate, int companyID)
        {
            CallDateDetail detail = null;
            try
            {
                DateDetails Data = null;
                DateDetails DataForYear = null;
                List<DateDetails> DateDetailsList = new List<DateDetails>();

                if (Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.DateDetails == null)
                {
                    Data = new DateDetails();
                    Data.Details = GetDetails(connString, Calldate, companyID);
                    Data.Year = Calldate.Value.Year;

                    DateDetailsList.Add(Data);
                }
                else
                {
                    DateDetailsList.Clear();
                    DateDetailsList = JsonConvert.DeserializeObject<List<DateDetails>>(Helpdesk_Rpt_ResponseTimegetWorkingHoursobj.DateDetails);
                    DataForYear = DateDetailsList.Where(Y => Y.Year == Calldate.Value.Year).FirstOrDefault();
                    if (DataForYear == null)//Details not found for a given Year
                    {
                        Data = new DateDetails();
                        Data.Details = GetDetails(connString, Calldate, companyID);
                        Data.Year = Calldate.Value.Year;

                        DateDetailsList.Add(Data);
                    }
                }

                DataForYear = DateDetailsList.Where(Y => Y.Year == Calldate.Value.Year).FirstOrDefault();
                detail = DataForYear.Details;
            }
            catch (Exception e) { }
            return detail;
        }
        public static List<DateTime> GetCompanyCalenderHolidays(string connString, int LogException, int companyID, DateTime actualCallDate, DateTime toDate)
        {
            var holidayDates = new List<DateTime>();

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Initialize the command to call the stored procedure
                    using (SqlCommand command = new SqlCommand("SP_GetCompanyCalenderHolidays", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@ActualCallDate", actualCallDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);

                        // Execute the command and read results
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // Adding each date from the result to the list
                                holidayDates.Add(Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]));
                            }
                        }
                    }

                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                // Log the exception (assuming a logging function is available)
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return holidayDates;
        }


        public static CallDateDetail GetDetails(string connString, DateTime? Calldate, int companyID)
        {
            CallDateDetail detail = new CallDateDetail();
            try
            {
                int year = Calldate?.Year ?? DateTime.Now.Year;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Retrieve Working Days
                    string WDays = GetWorkingDays(conn, companyID, year);

                    // Retrieve Start Time
                    TimeSpan startTime = GetTimeSpan(conn, "SP_AMERP_HelpDesk_GetCompanyStartTime", companyID, year);
                    double STM = startTime.TotalMinutes;

                    // Retrieve End Time
                    TimeSpan endTime = GetTimeSpan(conn, "SP_AMERP_HelpDesk_GetCompanyEndTime", companyID, year);
                    double ETM = (STM < endTime.TotalMinutes) ? endTime.TotalMinutes : (1440 + endTime.TotalMinutes);

                    // Retrieve Break Start Time
                    TimeSpan breakStartTime = GetTimeSpan(conn, "SP_AMERP_HelpDesk_GetCompanyBreakStartTime", companyID, year);
                    double BSTM = (breakStartTime.TotalMinutes < STM) ? (1440 + breakStartTime.TotalMinutes) : breakStartTime.TotalMinutes;

                    // Retrieve Break End Time
                    TimeSpan breakEndTime = GetTimeSpan(conn, "SP_AMERP_HelpDesk_GetCompanyBreakEndTime", companyID, year);
                    double BETM = (breakEndTime.TotalMinutes < STM) ? (1440 + breakEndTime.TotalMinutes) : breakEndTime.TotalMinutes;

                    double TotalBreakTime = BETM - BSTM;
                    double WHours = (ETM - STM) - TotalBreakTime;

                    // Populate Work Days List
                    detail.WorkDays = GetWorkDaysList(WDays);

                    detail.startTime = startTime;
                    detail.endTime = endTime;
                    detail.BreakstartTime = breakStartTime;
                    detail.BreakEndTime = breakEndTime;
                    detail.startTimeMinutes = STM;
                    detail.endTimeMinutes = ETM;
                    detail.BreakstartTimeMinutes = BSTM;
                    detail.BreakEndTimeMinute = BETM;
                    detail.WorkHours = WHours;
                }
            }
            catch (Exception e)
            {
                // Handle exception
            }
            return detail;
        }

        // Helper methods for each database operation
        private static string GetWorkingDays(SqlConnection conn, int companyID, int year)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyWorkingDays", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@Year", year);

                return cmd.ExecuteScalar() as string ?? string.Empty;
            }
        }

        private static TimeSpan GetTimeSpan(SqlConnection conn, string storedProcedure, int companyID, int year)
        {
            using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@Year", year);

                return (TimeSpan)(cmd.ExecuteScalar() ?? TimeSpan.Zero);
            }
        }

        private static List<string> GetWorkDaysList(string WDays)
        {
            List<string> WDlist = new List<string>();
            int count = 0;
            foreach (string a in WDays.Split(','))
            {
                count++;
                if (a == "1")
                {
                    switch (count)
                    {
                        case 1:
                            WDlist.Add("Monday");
                            break;
                        case 2:
                            WDlist.Add("Tuesday");
                            break;
                        case 3:
                            WDlist.Add("Wednesday");
                            break;
                        case 4:
                            WDlist.Add("Thursday");
                            break;
                        case 5:
                            WDlist.Add("Friday");
                            break;
                        case 6:
                            WDlist.Add("Saturday");
                            break;
                        case 7:
                            WDlist.Add("Sunday");
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
            }
            return WDlist;
        }
        #endregion


        #region :::Helpdesk_Rpt_ResponseTime list and obj classes Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Helpdesk_Rpt_ResponseTime list and obj Classes
        /// </summary>
        /// <returns>...</returns>
        /// 
        public class Helpdesk_Rpt_ResponseTimegetWorkingHoursList
        {
            public DateTime? Calldate { get; set; }
            public DateTime ToDate { get; set; }
            public int companyID { get; set; }
            public string DateDetails { get; set; }
        }

        public class Helpdesk_Rpt_ResponseTimeGetActionTakenList
        {
            public string CompanyIDs { get; set; }

        }


        public class Helpdesk_Rpt_ResponseTimeSelectList
        {
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public int CompyID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string ActionData { get; set; }
            public string UserCulture { get; set; }
            public string ActionTaken { get; set; }
            public int exprtType { get; set; }
            public string Branch { get; set; }
            public int Company_ID { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
        }
        #endregion


        #region :::Helpdesk_Rpt_ResponseTime  classes Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Helpdesk_Rpt_ResponseTime Classes
        /// </summary>
        /// <returns>...</returns>
        /// 
        public class DateDetails
        {
            public CallDateDetail Details { get; set; }
            public int Year { get; set; }
        }

        public class ServiceMGRToServiceENGReport
        {
            public int ServiceRequest_ID { get; set; }
            public string ServiceRequestNumber { get; set; }
            public string SRNumber { get; set; }
            public DateTime ServiceRequestDate { get; set; }
            public DateTime CallDate { get; set; }
            public string ServiceRequestDateString { get; set; }
            public string CallDateString { get; set; }
            public int Party_ID { get; set; }
            public int? Model_ID { get; set; }
            public int? ProductType_ID { get; set; }
            public string Party { get; set; }
            public string Model { get; set; }
            public string SerialNumber { get; set; }
            public string ProductType { get; set; }
            public string ActionTaken { get; set; }
            public string AssignedTo { get; set; }
            public string ActionBy { get; set; }
            public int? Action_Chosen { get; set; }
            public string ResponseTime_WorkingHours { get; set; }
            public string ResponseTime_24_Hours { get; set; }
            public string ResponseTime_WorkingHoursSPAN { get; set; }
            public string ResponseTime_24_HoursSPAN { get; set; }
            public int? ResponseTime_WorkingHours_MIN { get; set; }
            public int? ResponseTime_24_Hours_MIN { get; set; }
            public string CallStatus { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public string Company_Name { get; set; }
            public string Branch_Name { get; set; }
            public string Region { get; set; }
            public string Brand { get; set; }
        }

        public class CallDateDetail
        {
            public List<string> WorkDays { get; set; }
            public TimeSpan startTime { get; set; }
            public TimeSpan endTime { get; set; }
            public TimeSpan BreakstartTime { get; set; }
            public TimeSpan BreakEndTime { get; set; }
            public double startTimeMinutes { get; set; }
            public double endTimeMinutes { get; set; }
            public double BreakstartTimeMinutes { get; set; }
            public double BreakEndTimeMinute { get; set; }
            public double WorkHours { get; set; }
        }
        #endregion


    }
}
