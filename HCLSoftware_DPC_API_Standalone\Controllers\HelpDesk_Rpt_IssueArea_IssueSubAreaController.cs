﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Rpt_IssueArea_IssueSubAreaServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Rpt_IssueArea_IssueSubAreaController : ApiController
    {

        #region ::: SelectBranch Not Used Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To get the Select Branch
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/SelectBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranch([FromBody] HelpDesk_Rpt_IssueAreaSelectBranchList HelpDesk_Rpt_IssueAreaSelectBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_IssueArea_IssueSubAreaServices.SelectBranch(HelpDesk_Rpt_IssueAreaSelectBranchobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region :::GetModels Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// GetModels.
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/GetModels")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetModels([FromBody] HelpDesk_Rpt_IssueAreaGetModelsList HelpDesk_Rpt_IssueAreaGetModelsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_IssueArea_IssueSubAreaServices.GetModels(HelpDesk_Rpt_IssueAreaGetModelsobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region :::GetIssueSubArea Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// GetIssueSubArea.
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/GetIssueSubArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetIssueSubArea([FromBody] HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaList HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_IssueArea_IssueSubAreaServices.GetIssueSubArea(HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::GetIssueSubArea Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// GetIssueSubArea.
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/loadStatus")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult loadStatus([FromBody] HelpDesk_Rpt_IssueAreaGetloadStatusList HelpDesk_Rpt_IssueAreaGetloadStatusobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_IssueArea_IssueSubAreaServices.loadStatus(HelpDesk_Rpt_IssueAreaGetloadStatusobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: load Reference Master data Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/loadMasters")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult loadMasters([FromBody] HelpDesk_Rpt_IssueAreaGetloadMastersList HelpDesk_Rpt_IssueAreaGetloadMastersobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_IssueArea_IssueSubAreaServices.loadMasters(HelpDesk_Rpt_IssueAreaGetloadMastersobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region :::LoadGrid Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// LoadGrid.
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] HelpDesk_Rpt_IssueAreaGetSelectList HelpDesk_Rpt_IssueAreaGetSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_IssueArea_IssueSubAreaServices.Select(HelpDesk_Rpt_IssueAreaGetSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: To Export Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDesk_Rpt_IssueArea_IssueSubArea/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Rpt_IssueAreaGetSelectList HelpDesk_Rpt_IssueAreaGetSelectobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDesk_Rpt_IssueAreaGetSelectobj.sidx;
            string sord = HelpDesk_Rpt_IssueAreaGetSelectobj.sord;
            string filter = HelpDesk_Rpt_IssueAreaGetSelectobj.filter;
            string advnceFilter = HelpDesk_Rpt_IssueAreaGetSelectobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Rpt_IssueArea_IssueSubAreaServices.Export(HelpDesk_Rpt_IssueAreaGetSelectobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion




    }
}