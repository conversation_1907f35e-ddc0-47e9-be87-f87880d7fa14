﻿
using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace SharedAPIClassLibrary_AMERP
{
    public class CoreWareHouseServices
    {


        #region LoadBranchDropdown Vinay N 20/08/24
        /// <summary>
        /// LoadBranchDropdown
        /// </summary>
        /// <param name="LoadBranchDropdownObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult LoadBranchDropdown(LoadBranchDropdownWareHouseList LoadBranchDropdownObj, string connString, int LogException)
        {
            var jsonData = default(dynamic);

            try
            {

                int UserLang = Convert.ToInt32(LoadBranchDropdownObj.UserLanguageID);
                int GenLang = Convert.ToInt32(LoadBranchDropdownObj.GeneralLanguageID);
                int branchID = Convert.ToInt32(LoadBranchDropdownObj.Branch);
                var results = new List<QuestionnaireLevelData>();


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Load_AM_ERP_LoadBranchDropdown_WareHouse";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", LoadBranchDropdownObj.Company_ID);
                            command.Parameters.AddWithValue("@EmployeeID", LoadBranchDropdownObj.Employee_ID);
                            command.Parameters.AddWithValue("@UserLang", UserLang);
                            command.Parameters.AddWithValue("@GenLang", GenLang);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    results.Add(new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    });

                                }


                            }
                        }
                        jsonData = new
                        {
                            BranchArr = results,


                            branchID

                        };
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion
        #region getLandingGridData Vinay N 20/08/24
        /// <summary>
        /// getLandingGridData
        /// </summary>
        /// <param name="getLandingGridDataObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IQueryable<WareHouseMaster> getLandingGridData(getLandingGridDataList getLandingGridDataObj, string connString, int LogException)
        {
            IQueryable<WareHouseMaster> IQWareHouseMaster = null;
            try
            {
                var results = new List<WareHouseMaster>();



                IEnumerable<WareHouseMaster> IEWareHouseMasterArray = null;
                string YesE = CommonFunctionalities.GetResourceString(getLandingGridDataObj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(getLandingGridDataObj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(getLandingGridDataObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(getLandingGridDataObj.UserCulture.ToString(), "no").ToString();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_GET_AM_ERP_getLandingGridData_WareHouse";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BranchID", getLandingGridDataObj.Branch_ID);
                            command.Parameters.AddWithValue("@WareHouseTypeID", getLandingGridDataObj.floatTypeID);
                            command.Parameters.AddWithValue("@LanguageID", getLandingGridDataObj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(getLandingGridDataObj.GeneralLanguageID));
                            command.Parameters.AddWithValue("@YesE", YesE);
                            command.Parameters.AddWithValue("@NoE", NoE);
                            command.Parameters.AddWithValue("@YesL", YesL);
                            command.Parameters.AddWithValue("@NoL", NoL);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    results.Add(new WareHouseMaster
                                    {
                                        WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                        WareHouse_Name = reader.IsDBNull(reader.GetOrdinal("WareHouse_Name")) ? null : reader.GetString(reader.GetOrdinal("WareHouse_Name")),
                                        WareHouse_IsDefault = reader.IsDBNull(reader.GetOrdinal("WareHouse_IsDefault")) ? null : reader.GetString(reader.GetOrdinal("WareHouse_IsDefault")),
                                        WareHouse_IsActive = reader.IsDBNull(reader.GetOrdinal("WareHouse_IsActive")) ? null : reader.GetString(reader.GetOrdinal("WareHouse_IsActive")),
                                        IsDefault = reader.GetBoolean(reader.GetOrdinal("IsDefault")),
                                        IsVisible = reader.GetBoolean(reader.GetOrdinal("IsVisible")),
                                        IsVisibleString = reader.IsDBNull(reader.GetOrdinal("IsVisibleString")) ? null : reader.GetString(reader.GetOrdinal("IsVisibleString"))
                                    });

                                }



                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQWareHouseMaster = results.AsQueryable<WareHouseMaster>();

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return IQWareHouseMaster;
        }
        #endregion
        #region Select vinay n 21/8/24
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="SelectWareHouseObj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Select(getLandingGridDataList SelectWareHouseObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                //WareHouseClient = new WareHouseEntities();
                //IEnumerable<GNM_WareHouse> IEWareHouseList = WareHouseClient.GNM_WareHouse.Where(i => i.Branch_ID == Branch_ID && i.WareHouseType_ID == floatTypeID);
                //IEnumerable<GNM_WareHouseLocale> IEWareHouseLocaleList = WareHouseClient.GNM_WareHouseLocale.Where(i => i.GNM_WareHouse.Branch_ID == Branch_ID && i.Language_ID == LanguageID && i.GNM_WareHouse.WareHouseType_ID == floatTypeID);
                IQueryable<WareHouseMaster> IQWareHouseMaster = null;
                //IEnumerable<WareHouseMaster> IEWareHouseMasterArray = null;
                //string YesE = HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Yes").ToString();
                //string NoE = HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "No").ToString();
                //string YesL = HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Yes").ToString();
                //string NoL = HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "No").ToString();

                //if (LanguageID == Convert.ToInt32(Session["GeneralLanguageID"]))
                //{
                //    IEWareHouseMasterArray = (from a in IEWareHouseList
                //                              select new WareHouseMaster()
                //                              {
                //                                  WareHouse_ID = a.WareHouse_ID,
                //                                  WareHouse_Name = a.WareHouseName,
                //                                  WareHouse_IsDefault = (a.IsDefault == true ? YesE : NoE),
                //                                  WareHouse_IsActive = (a.IsActive == true ? YesE : NoE),
                //                                  IsDefault=a.IsDefault
                //                              });
                //}
                //else
                //{
                //    IEWareHouseMasterArray = from a in IEWareHouseList
                //                             join b in IEWareHouseLocaleList on a.WareHouse_ID equals b.WareHouse_ID
                //                             select new WareHouseMaster()
                //                             {
                //                                 WareHouse_ID = b.WareHouse_ID,
                //                                 WareHouse_Name = b.WareHouseName,
                //                                 WareHouse_IsDefault = (a.IsDefault == true ? YesL : NoL),
                //                                 WareHouse_IsActive = (a.IsActive == true ? YesL : NoL),
                //                                 IsDefault=a.IsDefault
                //                             };
                //}

                //  IQWareHouseMaster = IEWareHouseMasterArray.AsQueryable<WareHouseMaster>();


                IQWareHouseMaster = getLandingGridData(SelectWareHouseObj, connString, LogException);

                if (_search == true)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQWareHouseMaster = IQWareHouseMaster.FilterSearch<WareHouseMaster>(filtersObj);

                }
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<AdvanceFilter>();
                    IQWareHouseMaster = IQWareHouseMaster.AdvanceSearch<WareHouseMaster>(advnfilter);
                }

                IQWareHouseMaster = IQWareHouseMaster.OrderByField<WareHouseMaster>(sidx, sord);



                Count = IQWareHouseMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;


                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQWareHouseMaster.AsEnumerable()
                            select new
                            {
                                ID = a.WareHouse_ID,
                                //edit = "<img id='" + a.WareHouse_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.WareHouse_ID + "' class='WareHouseEdit' editmode='false'/>",
                                edit = "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.WareHouse_ID + "' class='WareHouseEdit' key='" + a.WareHouse_ID + "' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.WareHouse_ID + "' defaultchecked=''  id='chk" + a.WareHouse_ID + "'  " + ((a.IsDefault == true) ? "class='CannotDeleteWareHouse'" : "class='WareHouseDelete'") + "/>",
                                WareHouse_Name = a.WareHouse_Name,
                                WareHouse_IsDefault = a.WareHouse_IsDefault,
                                WareHouse_IsActive = a.WareHouse_IsActive,
                                IsVisibleString = a.IsVisibleString,
                                //Locale = "<img key='" + a.WareHouse_ID + "' src='" + AppPath + "/Content/local.png' class='WareHouseLocale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                Locale = "<a title='Localize' href='#' style='font-size: 13px;' width='20' height='20'  class='WareHouseLocale'  key='" + a.WareHouse_ID + "'><i class='fa fa-globe'></i></a>",

                                //View = "<img id='" + a.WareHouse_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.WareHouse_ID + "' class='ViewWareHouseLocale' WareHouse='" + a.WareHouse_Name + "'/>",
                                View = "<a title='Add' href='#' style='font-size: 13px;'  id='" + a.WareHouse_ID + "'  key='" + a.WareHouse_ID + "' class='ViewWareHouseLocale' WareHouse='" + a.WareHouse_Name + "'><i class='fa fa-plus'></i></a>",
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    //filter = Request.Params["filters"],
                    //advanceFilter = Request.Params["Query"],
                    SelectWareHouseObj.Branch_ID,
                    SelectWareHouseObj.LanguageID,
                    SelectWareHouseObj.floatTypeID,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion
        #region Save vinay 21/08/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="SaveWareHouseObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>


        public static IActionResult Save(SaveWareHouseList SaveWareHouseObj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {

                var Operation = string.Empty;

                var jObj = JObject.Parse(SaveWareHouseObj.data);
                int Count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    for (int i = 0; i < Count; i++)
                    {
                        GNM_WareHouse SRow = jObj["rows"].ElementAt(i).ToObject<GNM_WareHouse>();

                        using (SqlCommand command = new SqlCommand("UP_InsUpd_AM_ERP_SaveWareHouse", conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@WareHouse_ID", SRow.WareHouse_ID);
                            command.Parameters.AddWithValue("@WareHouseName", SRow.WareHouseName);
                            command.Parameters.AddWithValue("@IsDefault", SRow.IsDefault);
                            command.Parameters.AddWithValue("@IsActive", SRow.IsActive);
                            command.Parameters.AddWithValue("@Branch_ID", SRow.Branch_ID);
                            command.Parameters.AddWithValue("@IsVisible", SRow.IsVisible);
                            command.Parameters.AddWithValue("@WareHouseType_ID", SRow.WareHouseType_ID);
                            command.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(SaveWareHouseObj.Company_ID));

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Operation = reader["Operation"].ToString();
                                    SRow.WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]);
                                }
                            }
                        }

                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveWareHouseObj.Company_ID.ToString()), Convert.ToInt32(SaveWareHouseObj.Branch), SaveWareHouseObj.User_ID, Common.GetObjectID("CoreWareHouse"), SRow.WareHouse_ID, 0, 0, Operation + " WareHouse-" + SRow.WareHouseName + "", false, Convert.ToInt32(SaveWareHouseObj.MenuID), Convert.ToDateTime(SaveWareHouseObj.LoggedINDateTime));
                    }
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region CheckWareHouse vinay n 21/8/24
        /// <summary>
        /// CheckWareHouse
        /// </summary>
        /// <param name="CheckWareHouseObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckWareHouse(CheckWareHouse2List CheckWareHouseObj, string connString, int LogException)
        {
            int Count = 0;
            try
            {


                int dupcount = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_COUNT_AM_ERP_CheckWareHouse_WareHouse";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BranchID", CheckWareHouseObj.BranchID);
                            command.Parameters.AddWithValue("@WareHouseName", CheckWareHouseObj.WareHouseName);
                            command.Parameters.AddWithValue("@WareHouseID", CheckWareHouseObj.WareHouseID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    dupcount = reader.GetInt32(reader.GetOrdinal("COUNT"));



                                }
                                if (dupcount > 0)
                                {
                                    Count = 1;
                                }


                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion
        #region SelectParticularWareHouse vinay n 21/8/24
        /// <summary>
        /// SelectParticularWareHouse
        /// </summary>
        /// <param name="SelectParticularWareHouseObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectParticularWareHouse(SelectParticularWareHouseList SelectParticularWareHouseObj, string connString, int LogException)
        {
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularWareHouseObj.UserLanguageID);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_WarehouseDetails";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@WareHouseID", SelectParticularWareHouseObj.WareHouseID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    x = new
                                    {
                                        WareHouse_ID = reader["WareHouse_ID"] != DBNull.Value ? Convert.ToInt32(reader["WareHouse_ID"]) : (int?)null,
                                        WareHouse_Name = reader["WareHouse_Name"] != DBNull.Value ? reader["WareHouse_Name"].ToString() : string.Empty,
                                        WareHouse_IsDefault = reader["WareHouse_IsDefault"] != DBNull.Value ? (bool)reader["WareHouse_IsDefault"] : (bool?)null,
                                        WareHouse_IsActive = reader["WareHouse_IsActive"] != DBNull.Value ? (bool)reader["WareHouse_IsActive"] : (bool?)null,
                                        WareHouseLocale_ID = reader["WareHouseLocale_ID"] != DBNull.Value ? reader["WareHouseLocale_ID"].ToString() : string.Empty,
                                        WareHouseLocale_Name = reader["WareHouseLocale_Name"] != DBNull.Value ? reader["WareHouseLocale_Name"].ToString() : string.Empty
                                    };
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            return new JsonResult(x);
        }

        #endregion
        #region Delete vinay n 21/8/24
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DeleteWhereHouseObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Delete(DeleteWhereHouseList DeleteWhereHouseObj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {
                var jObj = JObject.Parse(DeleteWhereHouseObj.key);
                int Count = jObj["rows"].Count();

                int ID = 0;

                for (int i = 0; i < Count; i++)
                {
                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Del_AM_ERP_DeleteWareHouse";
                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ID", ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                            if (ex.InnerException != null && ex.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                Msg += CommonFunctionalities.GetResourceString(DeleteWhereHouseObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                return new JsonResult(Msg);
                            }
                            else
                            {
                                Msg += ex.Message;
                            }
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(Convert.ToInt32(DeleteWhereHouseObj.Company_ID.ToString()), Convert.ToInt32(DeleteWhereHouseObj.Branch), DeleteWhereHouseObj.User_ID, Common.GetObjectID("CoreWareHouse"), ID, 0, 0, "Deleted WareHouse-" + ID, false, Convert.ToInt32(DeleteWhereHouseObj.MenuID), Convert.ToDateTime(DeleteWhereHouseObj.LoggedINDateTime));
                }

                Msg += CommonFunctionalities.GetResourceString(DeleteWhereHouseObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetResourceString(DeleteWhereHouseObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    Msg += ex.Message;
                }
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region UpdateLocale vinay n 21/8/24
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// <param name="UpdateLocaleWhereHouseObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult UpdateLocale(UpdateLocaleWhereHouseList UpdateLocaleWhereHouseObj, string connString, int LogException)
        {
            int WareHouseLocale_ID = 0;
            var x = default(dynamic);
            try
            {

                GNM_WareHouseLocale SLRow = null;
                GNM_WareHouseLocale AddRow = null;
                var jObj = JObject.Parse(UpdateLocaleWhereHouseObj.data);

                SLRow = jObj.ToObject<GNM_WareHouseLocale>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_InsertUpdate_AM_ERP_UpdateLocale_WareHouse";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@WareHouseLocaleID", SLRow.WareHouseLocale_ID).Direction = ParameterDirection.InputOutput;
                            command.Parameters.AddWithValue("@WareHouseID", SLRow.WareHouse_ID);
                            command.Parameters.AddWithValue("@WareHouseName", SLRow.WareHouseName);
                            command.Parameters.AddWithValue("@Language_ID", SLRow.Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            command.ExecuteNonQuery();
                            WareHouseLocale_ID = (int)command.Parameters["@WareHouseLocaleID"].Value;

                        }

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                x = new
                {
                    WareHouseLocale_ID = WareHouseLocale_ID
                };

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion
        #region CheckWareHouseLocale vinay n 21/8/24
        /// <summary>
        /// CheckWareHouseLocale
        /// </summary>
        /// <param name="CheckWareHouseLocaleObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckWareHouseLocale(CheckWareHouseLocaleList CheckWareHouseLocaleObj, string connString, int LogException)
        {
            int Count = 0;
            try
            {


                int Language_ID = Convert.ToInt32(CheckWareHouseLocaleObj.UserLanguageID);
                int dupcount = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Check_AM_ERP_CheckWareHouseLocale_WareHouse";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BranchID", CheckWareHouseLocaleObj.BranchID);
                            command.Parameters.AddWithValue("@WareHouseName", CheckWareHouseLocaleObj.WareHouseName);
                            command.Parameters.AddWithValue("@WareHouseLocaleID", CheckWareHouseLocaleObj.WareHouseLocaleID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    dupcount = reader.GetInt32(reader.GetOrdinal("COUNT"));



                                }
                                if (dupcount > 0)
                                {
                                    Count = 1;
                                }


                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion
        #region CheckIfDefaultWarehouseExists vinay n 21/8/24
        /// <summary>
        /// CheckIfDefaultWarehouseExists
        /// </summary>
        /// <param name="CheckIfDefaultWarehouseExistsObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckIfDefaultWarehouseExists(CheckIfDefaultWarehouseExistsList CheckIfDefaultWarehouseExistsObj, string connString, int LogException)
        {
            int count = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_Chk_AM_ERP_CheckIfDefaultWarehouseExists", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.Add(new SqlParameter("@BranchID", CheckIfDefaultWarehouseExistsObj.BranchID));
                        command.Parameters.Add(new SqlParameter("@WarehouseTypeID", CheckIfDefaultWarehouseExistsObj.WarehouseTypeID));

                        SqlParameter outputCount = new SqlParameter("@Count", SqlDbType.Int);
                        outputCount.Direction = ParameterDirection.Output;
                        command.Parameters.Add(outputCount);

                        connection.Open();
                        command.ExecuteNonQuery();

                        count = (int)outputCount.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(count);
        }
        #endregion

        #region ::: Export :::
        /// <summary>
        /// To Export 
        /// </summary>
        public static async Task<object> Export(getLandingGridDataList ExportObj, string connString, int LogException)
        {
            int Count = 0;
            try
            {
                IQueryable<WareHouseMaster> IQWareHouseMaster = null;

                DataTable DtData = new DataTable();
                //IQWareHouseMaster = ((IQueryable<WareHouseMaster>)Session["IQWareHouseMaster"]);

                int Branch_ID = Convert.ToInt32(ExportObj.Branch_ID.ToString());
                int LanguageID = Convert.ToInt32(ExportObj.LanguageID.ToString());
                int floatTypeID = Convert.ToInt32(ExportObj.floatTypeID.ToString());

                IQWareHouseMaster = getLandingGridData(ExportObj, connString, LogException);
                //------------------------------------------------------------------------------------------------------------------------------

                if (ExportObj.filter.ToString() != "null" && ExportObj.filter.ToString() != "undefined")
                {
                    Filters filters = JObject.Parse(Common.DecryptString(ExportObj.filter)).ToObject<Filters>();
                    if (filters.rules.Count() > 0)
                    {
                        IQWareHouseMaster = IQWareHouseMaster.FilterSearch<WareHouseMaster>(filters);
                    }


                }
                if (ExportObj.advanceFilter.ToString() != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(ExportObj.advanceFilter).ToObject<AdvanceFilter>();

                    if (advnfilter.rules.Count() > 0)
                    {
                        IQWareHouseMaster = IQWareHouseMaster.AdvanceSearch<WareHouseMaster>(advnfilter);
                    }
                }

                IQWareHouseMaster = IQWareHouseMaster.OrderByField<WareHouseMaster>(ExportObj.sidx.ToString(), ExportObj.sord.ToString());



                //------------------------------------------------------------------------------------------------------------------------------
                var WareHouseArray = from a in IQWareHouseMaster.AsEnumerable()
                                     select new
                                     {
                                         a.WareHouse_Name,
                                         a.WareHouse_IsDefault,
                                         a.WareHouse_IsActive,
                                         a.IsVisibleString
                                     };

                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Name").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "IsDefault").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Visible").ToString());

                Count = WareHouseArray.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(WareHouseArray.ElementAt(i).WareHouse_Name, WareHouseArray.ElementAt(i).WareHouse_IsDefault, WareHouseArray.ElementAt(i).WareHouse_IsActive);
                    }

                    DataTable DtCriteria = new DataTable();

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("Name");
                    DtAlignment.Columns.Add("IsDefault");
                    DtAlignment.Columns.Add("Active");
                    DtAlignment.Columns.Add("Visible");
                    DtAlignment.Rows.Add(0, 1, 1, 1);
                    //Modified by Harish on 16-Dec-2014 for Version 2.0 corrections
                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Branch").ToString());
                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "WarehouseType").ToString());
                    DtCriteria.Rows.Add(Common.DecryptString(ExportObj.BranchName), Common.DecryptString(ExportObj.Warehousetype));
                    //correction ends
                    //ReportExport.Export(ExportObj.exprtType, DtData, DtCriteria, DtAlignment, "Warehouse", CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Warehouse").ToString());
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = DtCriteria,
                        dt = DtData,
                        Alignment = DtAlignment,
                        FileName = "Warehouse", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Warehouse").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };

                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;

                    //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), Convert.ToInt32(ExportObj.Branch), ExportObj.User_ID, Common.GetObjectID("CoreWareHouse"), 0, 0, 0, "WareHouse-Export", false, Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion
    }
    #region ::: CoreWareHouseListsAndObjectClasses :::
    /// <summary>
    /// CoreWareHouseListsAndObjectClasses
    /// </summary>
    /// 
    public class LoadBranchDropdownWareHouseList
    {
        public int Company_ID { get; set; }
        public int Employee_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Branch { get; set; }
    }
    public class getLandingGridDataList
    {
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public string Branch_ID { get; set; }
        public string floatTypeID { get; set; }
        public int LanguageID { get; set; }
        public string GeneralLanguageID { get; set; }
        public bool _search { get; set; }
        public string filter { get; set; }
        public bool advnce { get; set; }
        public string advanceFilter { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public string BranchName { get; set; }
        public int exprtType { get; set; }
        public string Warehousetype { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public int UserLanguageID { get; set; }

    }
    public class SaveWareHouseList
    {

        public string data { get; set; }
        public string Company_ID { get; set; }
        public int Branch { set; get; }
        public int MenuID { get; set; }
        public int LoggedINDateTime { get; set; }
        public int User_ID { get; set; }

    }
    public class CheckWareHouse2List
    {
        public int BranchID { get; set; }
        public string WareHouseName { get; set; }
        public int WareHouseID { get; set; }
    }
    public class SelectParticularWareHouseList
    {
        public int UserLanguageID { get; set; }
        public int WareHouseID { get; set; }

    }
    public class DeleteWhereHouseList
    {
        public string key { get; set; }
        public int User_ID { get; set; }
        public string GeneralCulture { get; set; }
    }
    public class UpdateLocaleWhereHouseList
    {
        public string data { set; get; }
    }
    public class CheckWareHouseLocaleList
    {
        public int UserLanguageID { set; get; }
        public int BranchID { get; set; }
        public string WareHouseName { set; get; }
        public int WareHouseLocaleID { get; set; }
    }
    public class CheckIfDefaultWarehouseExistsList
    {
        public int BranchID { set; get; }
        public int WarehouseTypeID { set; get; }
    }


    #endregion
    #region ::: CoreWareHouseClasses :::
    /// <summary>
    /// CoreWareHouseClasses
    /// </summary>
    /// 
    public class QuestionnaireLevelData
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }
    public class WareHouseMaster
    {
        public int WareHouse_ID
        {
            get;
            set;
        }

        public string WareHouse_Name
        {
            get;
            set;
        }

        public string WareHouse_IsDefault
        {
            get;
            set;
        }
        public bool IsDefault
        {
            get;
            set;
        }

        public string WareHouse_IsActive
        {
            get;
            set;
        }
        public bool? IsVisible
        {
            get;
            set;
        }
        public string IsVisibleString
        {
            get;
            set;
        }
    }

    #endregion
}
