using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Models;

namespace PBC.HelpdeskService.Services
{
    public interface IHelpDeskUserLandingPageServices
    {
        Task<IActionResult> SelectDealerNameAsync(SelectDealerNameList request, string connString, int LogException);
        Task<IActionResult> InitialSetup(InitialSetupList Obj, string connString, int LogException);
        Task<int> GetObjectIDAsync(string name, string connString, string query, int LogException);
        Task<string> GetBranchNameAsync(string connString, int LogException, int Branch_ID);
        Task<List<TabPosition>> GetTabsDataAsync(int companyId, string connString, int LogException);

        Task<bool> CheckAddPermissionsAsync(string name, string wfName, string helpDesk, int companyId, int logException, int userId, string connString);
        Task<IActionResult> ValidateCalldateandPCDAsync(validateCalldateandPCDList request);
        Task<IActionResult> CheckBayWorkshopAvailabilityAsync(CheckBayWorkshopAvailabilityList request, string connString, int LogException);
        Task<IActionResult> CheckForWorkshopBlockOverlapAsync(CheckForWorkshopBlockOverlapList request, string connString, int LogException);

        Task<IActionResult> SelectFieldSearchPartyAsync(SelectFieldSearchParty2List Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters);
        Task<IActionResult> GetDataAsync(GetDataUserLindingList Obj, string connString, int LogException);
        Task<IActionResult> GetDealerData(GetDealerDataList Obj, string connString, int LogException);
        Task<IActionResult> GetProductDetails(GetProductDetailsUserLandingList Obj, string connString, int LogException);
        Task<IActionResult> checkDuplicateContactPersonAsync(checkDuplicateContactPersonList Obj, string connString, int LogException);
        Task<IActionResult> GetOpenCampaignDetailsAsync(GetOpenCampaignDetailsList Obj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException);

        // Additional functions for endpoint creation
        Task<IActionResult> GetInitialData(GetInitialDataList Obj, string connString, int LogException);
        Task<IActionResult> SelectServiceRequest(SelectServiceRequestList Obj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException);
        Task<TabPosition> GetTabDetailsAsync(int ID, string Param, int Company_ID, string connString);
        Task<IActionResult> SelWorkFlowSummary(GetWorkFlowSummaryList Obj, string connString, int LogException);
        Task<IActionResult> SaveCustomer(SaveCustomerList Obj, string connString, int LogException);
        Task<IActionResult> UpdateIsEditTicket(UpdateIsEditTicketList Obj, string connString, int LogException);
    }
}
