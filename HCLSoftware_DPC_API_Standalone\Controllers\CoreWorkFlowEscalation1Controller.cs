﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreWorkFlowEscalation1Controller : ApiController
    {

        #region ::: SelectCompany Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Select Company
        /// </summary> 
        /// 
        [Route("api/CoreWorkFlowEscalation1/SelectCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompany([FromBody] SelectCompanyList SelectCompanyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWorkFlowEscalation1Services.SelectCompany(connString, SelectCompanyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectWorkFlow uday Kumar J B 30-09-2024 :::
        /// <summary>
        /// To Select WorkFlow
        /// </summary> 
        /// 
        [Route("api/CoreWorkFlowEscalation1/SelectWorkFlow")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectWorkFlow()
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWorkFlowEscalation1Services.SelectWorkFlow(connString);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To select All Log Report Data
        /// </summary>
        ///
        [Route("api/CoreWorkFlowEscalation1/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectWorkFlowList SelectWorkFlowobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreWorkFlowEscalation1Services.Select(connstring, SelectWorkFlowobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Escalation AlreadyExits  Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Escalation AlreadyExits
        /// </summary> 
        /// 
        [Route("api/CoreWorkFlowEscalation1/EscAlreadyExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult EscAlreadyExits([FromBody] EscAlreadyExitsList EscAlreadyExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWorkFlowEscalation1Services.EscAlreadyExits(connString, EscAlreadyExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Save
        /// </summary> 
        /// 
        [Route("api/CoreWorkFlowEscalation1/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveWorkFlowlist SaveWorkFlowobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWorkFlowEscalation1Services.Save(connString, SaveWorkFlowobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Delete
        /// </summary> 
        /// 
        [Route("api/CoreWorkFlowEscalation1/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteWorkFlowList DeleteWorkFlowobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreWorkFlowEscalation1Services.Delete(connString, DeleteWorkFlowobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Export
        /// </summary> 
        /// 
        [Route("api/CoreWorkFlowEscalation1/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportWorkFlowlist ExportWorkFlowobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportWorkFlowobj.sidx;
            string sord = ExportWorkFlowobj.sord;
            string filter = ExportWorkFlowobj.filter;
            string advnceFilter = ExportWorkFlowobj.advanceFilter;

            try
            {


                object Response = await CoreWorkFlowEscalation1Services.Export(ExportWorkFlowobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion



    }
}