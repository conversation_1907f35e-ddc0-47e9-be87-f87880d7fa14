﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreHeaderFooterPrintServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreHeaderFooterPrintController : ApiController
    {
        #region ::: GetTransaction /Mithun:::
        /// <summary>
        /// To GetTransaction
        /// </summary> 
        [Route("api/CoreHeaderFooterPrint/GetTransactions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTransactions([FromBody] GetTransactionsList GetTransactionsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreHeaderFooterPrintServices.GetTransactions(GetTransactionsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: save /Mithun:::
        /// <summary>
        ///  save Header Footer Print
        /// </summary>

        [Route("api/CoreHeaderFooterPrint/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveHeaderFooterList SaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreHeaderFooterPrintServices.Save(SaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Updatelocale /Mithun:::
        /// <summary>
        ///  to Update Locale
        /// </summary>
        [Route("api/CoreHeaderFooterPrint/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleHeaderFooterList UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreHeaderFooterPrintServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::SelectHeaderFooterGrid /Mithun:::
        /// <summary>
        /// To select Header Footer Grid
        /// </summary>
        [Route("api/CoreHeaderFooterPrint/SelectHeaderFooterGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectHeaderFooterGrid([FromBody] SelectHeaderFooterGridList SelectHeaderFooterGridObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreHeaderFooterPrintServices.SelectHeaderFooterGrid(SelectHeaderFooterGridObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region :::GetHeaderFooter /Mithun:::
        /// <summary>
        /// to Get Header Footer
        /// </summary>
        [Route("api/CoreHeaderFooterPrint/GetHeaderFooter")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetHeaderFooter([FromBody] GetHeaderFooterList GetHeaderFooterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreHeaderFooterPrintServices.GetHeaderFooter(GetHeaderFooterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::GetHeaderFooterLocale/Mithun:::
        /// <summary>
        /// to Get Header Footer Locale
        /// </summary>
        [Route("api/CoreHeaderFooterPrint/GetHeaderFooterLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetHeaderFooterLocale([FromBody] GetHeaderFooterLocaleList GetHeaderFooterLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreHeaderFooterPrintServices.GetHeaderFooterLocale(GetHeaderFooterLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::Delete /Mithun:::
        /// <summary>
        /// Delete
        /// </summary>
        [Route("api/CoreHeaderFooterPrint/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteHeaderFooterList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreHeaderFooterPrintServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}
