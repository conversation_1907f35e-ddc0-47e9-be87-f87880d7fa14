//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_User
    {
        public WF_User()
        {
            this.GNM_UserRole = new HashSet<WF_UserRole>();
            this.GNM_UserLocale = new HashSet<WF_UserLocale>();
        }
    
        public int User_ID { get; set; }
        public string User_Name { get; set; }
        public string User_LoginID { get; set; }
        public string User_Password { get; set; }
        public bool User_IsActive { get; set; }
        public bool User_Locked { get; set; }
        public Nullable<int> User_LoginCount { get; set; }
        public Nullable<int> User_FailedCount { get; set; }
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public byte User_Type_ID { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Partner_ID { get; set; }
        public string LandingPage { get; set; }
        public string User_IPAddress { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public string ReleaseVersionPopup { get; set; }
    
        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_CompanyEmployee GNM_CompanyEmployee { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual ICollection<WF_UserRole> GNM_UserRole { get; set; }
        public virtual ICollection<WF_UserLocale> GNM_UserLocale { get; set; }
    }
}
