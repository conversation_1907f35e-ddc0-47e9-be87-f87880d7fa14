﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="AMMSModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="AMMSModelStoreContainer">
          <EntitySet Name="GNM_Branch" EntityType="AMMSModel.Store.GNM_Branch" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Company" EntityType="AMMSModel.Store.GNM_Company" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_CompanyEmployee" EntityType="AMMSModel.Store.GNM_CompanyEmployee" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Email" EntityType="AMMSModel.Store.GNM_Email" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_EmployeeBranch" EntityType="AMMSModel.Store.GNM_EmployeeBranch" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Menu" EntityType="AMMSModel.Store.GNM_Menu" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_MenuLocale" EntityType="AMMSModel.Store.GNM_MenuLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Module" EntityType="AMMSModel.Store.GNM_Module" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_ModuleLocale" EntityType="AMMSModel.Store.GNM_ModuleLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Object" EntityType="AMMSModel.Store.GNM_Object" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_PartyBranchAssociation" EntityType="AMMSModel.Store.GNM_PartyBranchAssociation" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_PrefixSuffix" EntityType="AMMSModel.Store.GNM_PrefixSuffix" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_RefMasterDetail" EntityType="AMMSModel.Store.GNM_RefMasterDetail" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Role" EntityType="AMMSModel.Store.GNM_Role" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_RoleObject" EntityType="AMMSModel.Store.GNM_RoleObject" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Sms" EntityType="AMMSModel.Store.GNM_Sms" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_User" EntityType="AMMSModel.Store.GNM_User" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_UserLocale" EntityType="AMMSModel.Store.GNM_UserLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_UserRole" EntityType="AMMSModel.Store.GNM_UserRole" store:Type="Tables" Schema="dbo" />
          <AssociationSet Name="FK__GNM_Br__Curre__7B5B524B" Association="AMMSModel.Store.FK__GNM_Br__Curre__7B5B524B">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_Compa__Compa__59904A2C" Association="AMMSModel.Store.FK__GNM_Compa__Compa__59904A2C">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_Company" EntitySet="GNM_Company" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_Compa__Curre__7B5B524B" Association="AMMSModel.Store.FK__GNM_Compa__Curre__7B5B524B">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_Company" EntitySet="GNM_Company" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_RefMa__Compa__21C0F255" Association="AMMSModel.Store.FK__GNM_RefMa__Compa__21C0F255">
            <End Role="GNM_Company" EntitySet="GNM_Company" />
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_RefMa__CompaID" Association="AMMSModel.Store.FK__GNM_RefMa__CompaID">
            <End Role="GNM_Company" EntitySet="GNM_Company" />
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
          </AssociationSet>
          <AssociationSet Name="fk_B_refmstrdetailID" Association="AMMSModel.Store.fk_B_refmstrdetailID">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
          </AssociationSet>
          <AssociationSet Name="Fk_Branch_LanguageID" Association="AMMSModel.Store.Fk_Branch_LanguageID">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
          </AssociationSet>
          <AssociationSet Name="fk_CE_refmstrdetailID" Association="AMMSModel.Store.fk_CE_refmstrdetailID">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_Branch_GNM_Company" Association="AMMSModel.Store.FK_GNM_Branch_GNM_Company">
            <End Role="GNM_Company" EntitySet="GNM_Company" />
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_Branch_GNM_Country" Association="AMMSModel.Store.FK_GNM_Branch_GNM_Country">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Company" Association="AMMSModel.Store.FK_GNM_CompanyEmployee_GNM_Company">
            <End Role="GNM_Company" EntitySet="GNM_Company" />
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail" Association="AMMSModel.Store.FK_GNM_CompanyEmployee_GNM_Refmasterdetail">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail2" Association="AMMSModel.Store.FK_GNM_CompanyEmployee_GNM_Refmasterdetail2">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail3" Association="AMMSModel.Store.FK_GNM_CompanyEmployee_GNM_Refmasterdetail3">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_EmployeeBranch_GNM_Branch" Association="AMMSModel.Store.FK_GNM_EmployeeBranch_GNM_Branch">
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
            <End Role="GNM_EmployeeBranch" EntitySet="GNM_EmployeeBranch" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_EmployeeBranch_GNM_CompanyEmployee" Association="AMMSModel.Store.FK_GNM_EmployeeBranch_GNM_CompanyEmployee">
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
            <End Role="GNM_EmployeeBranch" EntitySet="GNM_EmployeeBranch" />
          </AssociationSet>
          <AssociationSet Name="Fk_GNM_Menu_Module_id" Association="AMMSModel.Store.Fk_GNM_Menu_Module_id">
            <End Role="GNM_Module" EntitySet="GNM_Module" />
            <End Role="GNM_Menu" EntitySet="GNM_Menu" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_MenuLocale_Language_ID" Association="AMMSModel.Store.FK_GNM_MenuLocale_Language_ID">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_MenuLocale" EntitySet="GNM_MenuLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_ModuleLocale_Language_ID" Association="AMMSModel.Store.FK_GNM_ModuleLocale_Language_ID">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_ModuleLocale" EntitySet="GNM_ModuleLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PartyBranchAssociation_GNM_Branch" Association="AMMSModel.Store.FK_GNM_PartyBranchAssociation_GNM_Branch">
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
            <End Role="GNM_PartyBranchAssociation" EntitySet="GNM_PartyBranchAssociation" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PrefixSuffix_GNM_Branch" Association="AMMSModel.Store.FK_GNM_PrefixSuffix_GNM_Branch">
            <End Role="GNM_Branch" EntitySet="GNM_Branch" />
            <End Role="GNM_PrefixSuffix" EntitySet="GNM_PrefixSuffix" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PrefixSuffix_GNM_Company" Association="AMMSModel.Store.FK_GNM_PrefixSuffix_GNM_Company">
            <End Role="GNM_Company" EntitySet="GNM_Company" />
            <End Role="GNM_PrefixSuffix" EntitySet="GNM_PrefixSuffix" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PrefixSuffix_GNM_Object" Association="AMMSModel.Store.FK_GNM_PrefixSuffix_GNM_Object">
            <End Role="GNM_Object" EntitySet="GNM_Object" />
            <End Role="GNM_PrefixSuffix" EntitySet="GNM_PrefixSuffix" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_RoleObject_Object_ID" Association="AMMSModel.Store.FK_GNM_RoleObject_Object_ID">
            <End Role="GNM_Object" EntitySet="GNM_Object" />
            <End Role="GNM_RoleObject" EntitySet="GNM_RoleObject" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_RoleObject_Role_id" Association="AMMSModel.Store.FK_GNM_RoleObject_Role_id">
            <End Role="GNM_Role" EntitySet="GNM_Role" />
            <End Role="GNM_RoleObject" EntitySet="GNM_RoleObject" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_User_Company_ID" Association="AMMSModel.Store.FK_GNM_User_Company_ID">
            <End Role="GNM_Company" EntitySet="GNM_Company" />
            <End Role="GNM_User" EntitySet="GNM_User" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_User_Employee_ID" Association="AMMSModel.Store.FK_GNM_User_Employee_ID">
            <End Role="GNM_CompanyEmployee" EntitySet="GNM_CompanyEmployee" />
            <End Role="GNM_User" EntitySet="GNM_User" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_User_Language_ID" Association="AMMSModel.Store.FK_GNM_User_Language_ID">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_User" EntitySet="GNM_User" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_UserLocale_GNM_RefMasterDetail" Association="AMMSModel.Store.FK_GNM_UserLocale_GNM_RefMasterDetail">
            <End Role="GNM_RefMasterDetail" EntitySet="GNM_RefMasterDetail" />
            <End Role="GNM_UserLocale" EntitySet="GNM_UserLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_UserLocale_GNM_User" Association="AMMSModel.Store.FK_GNM_UserLocale_GNM_User">
            <End Role="GNM_User" EntitySet="GNM_User" />
            <End Role="GNM_UserLocale" EntitySet="GNM_UserLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_MenuID" Association="AMMSModel.Store.FK_MenuID">
            <End Role="GNM_Menu" EntitySet="GNM_Menu" />
            <End Role="GNM_MenuLocale" EntitySet="GNM_MenuLocale" />
          </AssociationSet>
          <AssociationSet Name="Fk_Role_id" Association="AMMSModel.Store.Fk_Role_id">
            <End Role="GNM_Role" EntitySet="GNM_Role" />
            <End Role="GNM_UserRole" EntitySet="GNM_UserRole" />
          </AssociationSet>
          <AssociationSet Name="Fk_User_id" Association="AMMSModel.Store.Fk_User_id">
            <End Role="GNM_User" EntitySet="GNM_User" />
            <End Role="GNM_UserRole" EntitySet="GNM_UserRole" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="GNM_Branch">
          <Key>
            <PropertyRef Name="Branch_ID" />
          </Key>
          <Property Name="Branch_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="Branch_Name" Type="varchar" Nullable="false" MaxLength="70" />
          <Property Name="Branch_ShortName" Type="varchar" MaxLength="10" />
          <Property Name="Branch_ZipCode" Type="varchar" MaxLength="25" />
          <Property Name="Country_ID" Type="int" Nullable="false" />
          <Property Name="State_ID" Type="int" Nullable="false" />
          <Property Name="Branch_Phone" Type="varchar" MaxLength="25" />
          <Property Name="Branch_Fax" Type="varchar" MaxLength="30" />
          <Property Name="Branch_HeadOffice" Type="bit" Nullable="false" />
          <Property Name="Branch_Active" Type="bit" Nullable="false" />
          <Property Name="Branch_Address" Type="varchar" Nullable="false" MaxLength="200" />
          <Property Name="Branch_Location" Type="varchar" MaxLength="50" />
          <Property Name="Branch_Email" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="Branch_Mobile" Type="varchar" MaxLength="12" />
          <Property Name="Branch_External" Type="bit" />
          <Property Name="TimeZoneID" Type="int" />
          <Property Name="Region_ID" Type="int" />
          <Property Name="Currency_ID" Type="int" />
          <Property Name="LanguageID" Type="int" />
          <Property Name="IsOverTimeDWM" Type="tinyint" />
          <Property Name="Yearly_Sales_Target" Type="decimal" Precision="12" Scale="2" />
          <Property Name="Rework_Target" Type="decimal" Precision="12" Scale="2" />
          <Property Name="Cust_Satisfaction_Target" Type="decimal" Precision="12" Scale="2" />
          <Property Name="RO_with_Rework_Target" Type="decimal" Precision="12" Scale="2" />
          <Property Name="RO_with_Cust_Satisfaction_Target" Type="decimal" Precision="12" Scale="2" />
          <Property Name="DueDays" Type="int" />
        </EntityType>
        <EntityType Name="GNM_Company">
          <Key>
            <PropertyRef Name="Company_ID" />
          </Key>
          <Property Name="Company_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Company_Name" Type="varchar" Nullable="false" MaxLength="70" />
          <Property Name="Company_ShortName" Type="varchar" MaxLength="10" />
          <Property Name="Currency_ID" Type="int" Nullable="false" />
          <Property Name="Company_Address" Type="varchar" MaxLength="200" />
          <Property Name="Company_Type" Type="char" Nullable="false" MaxLength="1" />
          <Property Name="Company_Active" Type="bit" Nullable="false" />
          <Property Name="Company_LogoName" Type="varchar" MaxLength="25" />
          <Property Name="Company_Parent_ID" Type="int" />
          <Property Name="Remarks" Type="nvarchar" MaxLength="2000" />
          <Property Name="DefaultGridSize" Type="tinyint" Nullable="false" />
          <Property Name="JobCardCushionHours" Type="decimal" Precision="5" Scale="2" />
          <Property Name="ModifiedBy" Type="int" Nullable="false" />
          <Property Name="ModifiedDate" Type="datetime" Nullable="false" />
          <Property Name="CompanyTheme_ID" Type="int" />
          <Property Name="QuotationValidity" Type="int" />
          <Property Name="CompanyFont" Type="varchar" MaxLength="50" />
          <Property Name="InventoryCarryingFactoy_Percentage" Type="decimal" Precision="5" Scale="2" />
          <Property Name="OrderingCost" Type="int" />
        </EntityType>
        <EntityType Name="GNM_CompanyEmployee">
          <Key>
            <PropertyRef Name="Company_Employee_ID" />
          </Key>
          <Property Name="Company_Employee_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Employee_ID" Type="varchar" Nullable="false" MaxLength="10" />
          <Property Name="Company_Employee_Name" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="Country_ID" Type="int" Nullable="false" />
          <Property Name="State_ID" Type="int" Nullable="false" />
          <Property Name="Company_Employee_MobileNumber" Type="varchar" MaxLength="15" />
          <Property Name="Company_Employee_Landline_Number" Type="varchar" MaxLength="25" />
          <Property Name="Company_Employee_ZipCode" Type="varchar" MaxLength="25" />
          <Property Name="Company_Employee_ActiveFrom" Type="datetime" />
          <Property Name="Company_Employee_ValidateUpTo" Type="datetime" />
          <Property Name="Company_Employee_Active" Type="bit" Nullable="false" />
          <Property Name="Company_Employee_Manager_ID" Type="int" />
          <Property Name="Company_Employee_Address" Type="varchar" Nullable="false" MaxLength="200" />
          <Property Name="Company_Employee_Location" Type="varchar" MaxLength="50" />
          <Property Name="Company_Employee_Email" Type="varchar" MaxLength="50" />
          <Property Name="Company_Employee_Department_ID" Type="int" Nullable="false" />
          <Property Name="Company_Employee_Designation_ID" Type="int" Nullable="false" />
          <Property Name="ModifiedBy" Type="int" Nullable="false" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="HourlyRate" Type="decimal" Precision="12" Scale="2" />
          <Property Name="Region_ID" Type="int" />
          <Property Name="IsEligibleForOT" Type="bit" />
          <Property Name="ExemptionHours" Type="tinyint" />
          <Property Name="IsOnJob" Type="bit" />
          <Property Name="JobID" Type="int" />
        </EntityType>
        <EntityType Name="GNM_Email">
          <Key>
            <PropertyRef Name="Email_ID" />
          </Key>
          <Property Name="Email_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Email_Subject" Type="nvarchar" MaxLength="500" />
          <Property Name="Email_Body" Type="nvarchar(max)" />
          <Property Name="Email_To" Type="varchar" MaxLength="500" />
          <Property Name="Email_cc" Type="varchar" MaxLength="500" />
          <Property Name="Email_Bcc" Type="varchar" MaxLength="500" />
          <Property Name="Email_Queue_Date" Type="datetime" />
          <Property Name="Email_Sent_Date" Type="datetime" />
          <Property Name="Email_SentStatus" Type="bit" Nullable="false" />
          <Property Name="Email_Attachments" Type="nvarchar" MaxLength="500" />
          <Property Name="Email_IsError" Type="bit" />
          <Property Name="NoOfAttempts" Type="tinyint" />
        </EntityType>
        <EntityType Name="GNM_EmployeeBranch">
          <Key>
            <PropertyRef Name="EmployeeBranch_ID" />
          </Key>
          <Property Name="EmployeeBranch_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="CompanyEmployee_ID" Type="int" Nullable="false" />
          <Property Name="Branch_ID" Type="int" Nullable="false" />
          <Property Name="IsDefault" Type="bit" />
        </EntityType>
        <EntityType Name="GNM_Menu">
          <Key>
            <PropertyRef Name="Menu_ID" />
          </Key>
          <Property Name="Menu_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Module_ID" Type="int" Nullable="false" />
          <Property Name="Menu_Description" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="Parentmenu_ID" Type="int" />
          <Property Name="Object_ID" Type="int" />
          <Property Name="Menu_Path" Type="varchar" MaxLength="100" />
          <Property Name="Menu_SortOrder" Type="tinyint" Nullable="false" />
          <Property Name="Menu_IsActive" Type="bit" Nullable="false" />
          <Property Name="Menu_IconName" Type="varchar" MaxLength="200" />
        </EntityType>
        <EntityType Name="GNM_MenuLocale">
          <Key>
            <PropertyRef Name="MenuLocale_ID" />
          </Key>
          <Property Name="MenuLocale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
          <Property Name="Menu_ID" Type="int" Nullable="false" />
          <Property Name="Menu_Description" Type="nvarchar" Nullable="false" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_Module">
          <Key>
            <PropertyRef Name="Module_ID" />
          </Key>
          <Property Name="Module_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Module_Description" Type="varchar" Nullable="false" MaxLength="100" />
          <Property Name="Module_IsActive" Type="bit" Nullable="false" />
          <Property Name="Module_SortOrder" Type="tinyint" Nullable="false" />
          <Property Name="Module_IconName" Type="varchar" MaxLength="200" />
        </EntityType>
        <EntityType Name="GNM_ModuleLocale">
          <Key>
            <PropertyRef Name="ModuleLocale_ID" />
          </Key>
          <Property Name="ModuleLocale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
          <Property Name="Module_ID" Type="int" Nullable="false" />
          <Property Name="Module_Description" Type="nvarchar" Nullable="false" MaxLength="200" />
          <Property Name="Module_IconName" Type="varchar" MaxLength="200" />
        </EntityType>
        <EntityType Name="GNM_Object">
          <Key>
            <PropertyRef Name="Object_ID" />
          </Key>
          <Property Name="Object_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Object_Name" Type="varchar" Nullable="false" MaxLength="100" />
          <Property Name="Read_Action" Type="varchar" MaxLength="100" />
          <Property Name="Create_Action" Type="varchar" MaxLength="100" />
          <Property Name="Update_Action" Type="varchar" MaxLength="100" />
          <Property Name="Delete_Action" Type="varchar" MaxLength="100" />
          <Property Name="Export_Action" Type="varchar" MaxLength="100" />
          <Property Name="Print_Action" Type="varchar" MaxLength="100" />
          <Property Name="Object_IsActive" Type="bit" Nullable="false" />
          <Property Name="Object_Description" Type="varchar" Nullable="false" MaxLength="100" />
          <Property Name="Import_Action" Type="varchar" MaxLength="100" />
          <Property Name="Object_Type" Type="char" MaxLength="1" />
        </EntityType>
        <EntityType Name="GNM_PartyBranchAssociation">
          <Key>
            <PropertyRef Name="PartyBranch_ID" />
          </Key>
          <Property Name="PartyBranch_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Party_ID" Type="int" Nullable="false" />
          <Property Name="Branch_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_PrefixSuffix">
          <Key>
            <PropertyRef Name="PrefixSuffix_ID" />
          </Key>
          <Property Name="PrefixSuffix_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="Branch_ID" Type="int" />
          <Property Name="Object_ID" Type="int" Nullable="false" />
          <Property Name="Start_Number" Type="int" Nullable="false" />
          <Property Name="Prefix" Type="nvarchar" MaxLength="50" />
          <Property Name="Suffix" Type="nvarchar" MaxLength="50" />
          <Property Name="FromDate" Type="datetime" Nullable="false" />
          <Property Name="ToDate" Type="datetime" Nullable="false" />
          <Property Name="ModifiedBY" Type="int" Nullable="false" />
          <Property Name="ModifiedDate" Type="datetime" Nullable="false" />
          <Property Name="FinancialYear" Type="int" />
          <Property Name="Company_FinancialYear_ID" Type="int" />
        </EntityType>
        <EntityType Name="GNM_RefMasterDetail">
          <Key>
            <PropertyRef Name="RefMasterDetail_ID" />
          </Key>
          <Property Name="RefMasterDetail_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="RefMasterDetail_IsActive" Type="bit" Nullable="false" />
          <Property Name="RefMasterDetail_Short_Name" Type="nvarchar" MaxLength="15" />
          <Property Name="RefMasterDetail_Name" Type="varchar" Nullable="false" MaxLength="500" />
          <Property Name="Company_ID" Type="int" />
          <Property Name="ModifiedBy" Type="int" Nullable="false" />
          <Property Name="ModifiedDate" Type="datetime" Nullable="false" />
          <Property Name="RefMaster_ID" Type="int" Nullable="false" />
          <Property Name="RefMasterDetail_IsDefault" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_Role">
          <Key>
            <PropertyRef Name="Role_ID" />
          </Key>
          <Property Name="Role_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="Role_Name" Type="varchar" Nullable="false" MaxLength="30" />
        </EntityType>
        <EntityType Name="GNM_RoleObject">
          <Key>
            <PropertyRef Name="RoleObject_ID" />
          </Key>
          <Property Name="RoleObject_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Role_ID" Type="int" Nullable="false" />
          <Property Name="Object_ID" Type="int" Nullable="false" />
          <Property Name="RoleObject_Create" Type="bit" Nullable="false" />
          <Property Name="RoleObject_Read" Type="bit" Nullable="false" />
          <Property Name="RoleObject_Update" Type="bit" Nullable="false" />
          <Property Name="RoleObject_Delete" Type="bit" Nullable="false" />
          <Property Name="RoleObject_Print" Type="bit" Nullable="false" />
          <Property Name="RoleObject_Export" Type="bit" Nullable="false" />
          <Property Name="RoleObject_Import" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_Sms">
          <Key>
            <PropertyRef Name="Sms_ID" />
          </Key>
          <Property Name="Sms_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Sms_Text" Type="nvarchar" MaxLength="200" />
          <Property Name="Sms_Mobile_Number" Type="varchar" MaxLength="15" />
          <Property Name="Sms_Queue_Date" Type="datetime" />
          <Property Name="Sms_Sent_Date" Type="datetime" />
          <Property Name="Sms_SentStatus" Type="bit" Nullable="false" />
          <Property Name="Template_ID" Type="int" Nullable="false" />
          <Property Name="Parameter1_value" Type="varchar" MaxLength="100" />
          <Property Name="Parameter2_value" Type="varchar" MaxLength="100" />
          <Property Name="Parameter3_value" Type="varchar" MaxLength="100" />
          <Property Name="Parameter4_value" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_User">
          <Key>
            <PropertyRef Name="User_ID" />
          </Key>
          <Property Name="User_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="User_Name" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="User_LoginID" Type="varchar" Nullable="false" MaxLength="30" />
          <Property Name="User_Password" Type="varchar" Nullable="false" MaxLength="100" />
          <Property Name="User_IsActive" Type="bit" Nullable="false" />
          <Property Name="User_Locked" Type="bit" Nullable="false" />
          <Property Name="User_LoginCount" Type="int" />
          <Property Name="User_FailedCount" Type="int" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
          <Property Name="User_Type_ID" Type="tinyint" Nullable="false" />
          <Property Name="Employee_ID" Type="int" />
          <Property Name="Partner_ID" Type="int" />
          <Property Name="LandingPage" Type="varchar" MaxLength="50" />
          <Property Name="User_IPAddress" Type="varchar" MaxLength="20" />
          <Property Name="WareHouse_ID" Type="int" />
          <Property Name="ReleaseVersionPopup" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_UserLocale">
          <Key>
            <PropertyRef Name="User_Locale_ID" />
          </Key>
          <Property Name="User_Locale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="User_ID" Type="int" Nullable="false" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
          <Property Name="User_Name" Type="nvarchar" Nullable="false" MaxLength="140" />
        </EntityType>
        <EntityType Name="GNM_UserRole">
          <Key>
            <PropertyRef Name="UserRole_ID" />
          </Key>
          <Property Name="UserRole_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="User_ID" Type="int" Nullable="false" />
          <Property Name="Role_ID" Type="int" Nullable="false" />
        </EntityType>
        <Association Name="FK__GNM_Br__Curre__7B5B524B">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="0..1" />
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="Currency_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_Compa__Compa__59904A2C">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="0..1" />
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Company">
              <PropertyRef Name="CompanyTheme_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_Compa__Curre__7B5B524B">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Company">
              <PropertyRef Name="Currency_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_RefMa__Compa__21C0F255">
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="0..1" />
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_RefMasterDetail">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_RefMa__CompaID">
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="0..1" />
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_RefMasterDetail">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="fk_B_refmstrdetailID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="0..1" />
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="Region_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_Branch_LanguageID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="0..1" />
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="LanguageID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="fk_CE_refmstrdetailID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="0..1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Region_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_Branch_GNM_Company">
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="1" />
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_Branch_GNM_Country">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="Country_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Company">
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Country_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail2">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_Department_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail3">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_Designation_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_EmployeeBranch_GNM_Branch">
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="1" />
          <End Role="GNM_EmployeeBranch" Type="AMMSModel.Store.GNM_EmployeeBranch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Branch">
              <PropertyRef Name="Branch_ID" />
            </Principal>
            <Dependent Role="GNM_EmployeeBranch">
              <PropertyRef Name="Branch_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_EmployeeBranch_GNM_CompanyEmployee">
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="1" />
          <End Role="GNM_EmployeeBranch" Type="AMMSModel.Store.GNM_EmployeeBranch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_ID" />
            </Principal>
            <Dependent Role="GNM_EmployeeBranch">
              <PropertyRef Name="CompanyEmployee_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_GNM_Menu_Module_id">
          <End Role="GNM_Module" Type="AMMSModel.Store.GNM_Module" Multiplicity="1" />
          <End Role="GNM_Menu" Type="AMMSModel.Store.GNM_Menu" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Module">
              <PropertyRef Name="Module_ID" />
            </Principal>
            <Dependent Role="GNM_Menu">
              <PropertyRef Name="Module_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_MenuLocale_Language_ID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_MenuLocale" Type="AMMSModel.Store.GNM_MenuLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_MenuLocale">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_ModuleLocale_Language_ID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_ModuleLocale" Type="AMMSModel.Store.GNM_ModuleLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_ModuleLocale">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PartyBranchAssociation_GNM_Branch">
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="1" />
          <End Role="GNM_PartyBranchAssociation" Type="AMMSModel.Store.GNM_PartyBranchAssociation" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Branch">
              <PropertyRef Name="Branch_ID" />
            </Principal>
            <Dependent Role="GNM_PartyBranchAssociation">
              <PropertyRef Name="Branch_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PrefixSuffix_GNM_Branch">
          <End Role="GNM_Branch" Type="AMMSModel.Store.GNM_Branch" Multiplicity="0..1" />
          <End Role="GNM_PrefixSuffix" Type="AMMSModel.Store.GNM_PrefixSuffix" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Branch">
              <PropertyRef Name="Branch_ID" />
            </Principal>
            <Dependent Role="GNM_PrefixSuffix">
              <PropertyRef Name="Branch_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PrefixSuffix_GNM_Company">
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="1" />
          <End Role="GNM_PrefixSuffix" Type="AMMSModel.Store.GNM_PrefixSuffix" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_PrefixSuffix">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PrefixSuffix_GNM_Object">
          <End Role="GNM_Object" Type="AMMSModel.Store.GNM_Object" Multiplicity="1" />
          <End Role="GNM_PrefixSuffix" Type="AMMSModel.Store.GNM_PrefixSuffix" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Object">
              <PropertyRef Name="Object_ID" />
            </Principal>
            <Dependent Role="GNM_PrefixSuffix">
              <PropertyRef Name="Object_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_RoleObject_Object_ID">
          <End Role="GNM_Object" Type="AMMSModel.Store.GNM_Object" Multiplicity="1" />
          <End Role="GNM_RoleObject" Type="AMMSModel.Store.GNM_RoleObject" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Object">
              <PropertyRef Name="Object_ID" />
            </Principal>
            <Dependent Role="GNM_RoleObject">
              <PropertyRef Name="Object_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_RoleObject_Role_id">
          <End Role="GNM_Role" Type="AMMSModel.Store.GNM_Role" Multiplicity="1" />
          <End Role="GNM_RoleObject" Type="AMMSModel.Store.GNM_RoleObject" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Role">
              <PropertyRef Name="Role_ID" />
            </Principal>
            <Dependent Role="GNM_RoleObject">
              <PropertyRef Name="Role_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_User_Company_ID">
          <End Role="GNM_Company" Type="AMMSModel.Store.GNM_Company" Multiplicity="1" />
          <End Role="GNM_User" Type="AMMSModel.Store.GNM_User" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_User">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_User_Employee_ID">
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.Store.GNM_CompanyEmployee" Multiplicity="0..1" />
          <End Role="GNM_User" Type="AMMSModel.Store.GNM_User" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_ID" />
            </Principal>
            <Dependent Role="GNM_User">
              <PropertyRef Name="Employee_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_User_Language_ID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_User" Type="AMMSModel.Store.GNM_User" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_User">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_UserLocale_GNM_RefMasterDetail">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.Store.GNM_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_UserLocale" Type="AMMSModel.Store.GNM_UserLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_UserLocale">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_UserLocale_GNM_User">
          <End Role="GNM_User" Type="AMMSModel.Store.GNM_User" Multiplicity="1" />
          <End Role="GNM_UserLocale" Type="AMMSModel.Store.GNM_UserLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_User">
              <PropertyRef Name="User_ID" />
            </Principal>
            <Dependent Role="GNM_UserLocale">
              <PropertyRef Name="User_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_MenuID">
          <End Role="GNM_Menu" Type="AMMSModel.Store.GNM_Menu" Multiplicity="1" />
          <End Role="GNM_MenuLocale" Type="AMMSModel.Store.GNM_MenuLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Menu">
              <PropertyRef Name="Menu_ID" />
            </Principal>
            <Dependent Role="GNM_MenuLocale">
              <PropertyRef Name="Menu_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_Role_id">
          <End Role="GNM_Role" Type="AMMSModel.Store.GNM_Role" Multiplicity="1" />
          <End Role="GNM_UserRole" Type="AMMSModel.Store.GNM_UserRole" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Role">
              <PropertyRef Name="Role_ID" />
            </Principal>
            <Dependent Role="GNM_UserRole">
              <PropertyRef Name="Role_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_User_id">
          <End Role="GNM_User" Type="AMMSModel.Store.GNM_User" Multiplicity="1" />
          <End Role="GNM_UserRole" Type="AMMSModel.Store.GNM_UserRole" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_User">
              <PropertyRef Name="User_ID" />
            </Principal>
            <Dependent Role="GNM_UserRole">
              <PropertyRef Name="User_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="AMMSModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="GenEntities" p1:LazyLoadingEnabled="true">
          <EntitySet Name="WF_Branch" EntityType="AMMSModel.WF_Branch" />
          <EntitySet Name="WF_Company" EntityType="AMMSModel.WF_Company" />
          <EntitySet Name="WF_CompanyEmployee" EntityType="AMMSModel.WF_CompanyEmployee" />
          <EntitySet Name="WF_Email" EntityType="AMMSModel.WF_Email" />
          <EntitySet Name="WF_Menu" EntityType="AMMSModel.WF_Menu" />
          <EntitySet Name="WF_MenuLocale" EntityType="AMMSModel.WF_MenuLocale" />
          <EntitySet Name="WF_Module" EntityType="AMMSModel.WF_Module" />
          <EntitySet Name="WF_ModuleLocale" EntityType="AMMSModel.WF_ModuleLocale" />
          <EntitySet Name="WF_Object" EntityType="AMMSModel.WF_Object" />
          <EntitySet Name="WF_PartyBranchAssociation" EntityType="AMMSModel.WF_PartyBranchAssociation" />
          <EntitySet Name="WF_PrefixSuffix" EntityType="AMMSModel.WF_PrefixSuffix" />
          <EntitySet Name="WF_RefMasterDetail" EntityType="AMMSModel.WF_RefMasterDetail" />
          <EntitySet Name="WF_Role" EntityType="AMMSModel.WF_Role" />
          <EntitySet Name="WF_RoleObject" EntityType="AMMSModel.WF_RoleObject" />
          <EntitySet Name="WF_Sms" EntityType="AMMSModel.WF_Sms" />
          <EntitySet Name="WF_User" EntityType="AMMSModel.WF_User" />
          <EntitySet Name="WF_UserRole" EntityType="AMMSModel.WF_UserRole" />
          <AssociationSet Name="FK_GNM_Branch_GNM_Company" Association="AMMSModel.FK_GNM_Branch_GNM_Company">
            <End Role="GNM_Company" EntitySet="WF_Company" />
            <End Role="GNM_Branch" EntitySet="WF_Branch" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_Branch_GNM_Country" Association="AMMSModel.FK_GNM_Branch_GNM_Country">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_Branch" EntitySet="WF_Branch" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PartyBranchAssociation_GNM_Branch" Association="AMMSModel.FK_GNM_PartyBranchAssociation_GNM_Branch">
            <End Role="GNM_Branch" EntitySet="WF_Branch" />
            <End Role="GNM_PartyBranchAssociation" EntitySet="WF_PartyBranchAssociation" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PrefixSuffix_GNM_Branch" Association="AMMSModel.FK_GNM_PrefixSuffix_GNM_Branch">
            <End Role="GNM_Branch" EntitySet="WF_Branch" />
            <End Role="GNM_PrefixSuffix" EntitySet="WF_PrefixSuffix" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_Compa__Compa__10373EF7" Association="AMMSModel.FK__GNM_Compa__Compa__10373EF7">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_Company" EntitySet="WF_Company" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_Compa__Curre__7B5B524B" Association="AMMSModel.FK__GNM_Compa__Curre__7B5B524B">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_Company" EntitySet="WF_Company" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_RefMa__Compa__21C0F255" Association="AMMSModel.FK__GNM_RefMa__Compa__21C0F255">
            <End Role="GNM_Company" EntitySet="WF_Company" />
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Company" Association="AMMSModel.FK_GNM_CompanyEmployee_GNM_Company">
            <End Role="GNM_Company" EntitySet="WF_Company" />
            <End Role="GNM_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PrefixSuffix_GNM_Company" Association="AMMSModel.FK_GNM_PrefixSuffix_GNM_Company">
            <End Role="GNM_Company" EntitySet="WF_Company" />
            <End Role="GNM_PrefixSuffix" EntitySet="WF_PrefixSuffix" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_User_Company_ID" Association="AMMSModel.FK_GNM_User_Company_ID">
            <End Role="GNM_Company" EntitySet="WF_Company" />
            <End Role="GNM_User" EntitySet="WF_User" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail" Association="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail2" Association="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail2">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail3" Association="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail3">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_User_Employee_ID" Association="AMMSModel.FK_GNM_User_Employee_ID">
            <End Role="GNM_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
            <End Role="GNM_User" EntitySet="WF_User" />
          </AssociationSet>
          <AssociationSet Name="Fk_GNM_Menu_Module_id" Association="AMMSModel.Fk_GNM_Menu_Module_id">
            <End Role="GNM_Module" EntitySet="WF_Module" />
            <End Role="GNM_Menu" EntitySet="WF_Menu" />
          </AssociationSet>
          <AssociationSet Name="FK_MenuID" Association="AMMSModel.FK_MenuID">
            <End Role="GNM_Menu" EntitySet="WF_Menu" />
            <End Role="GNM_MenuLocale" EntitySet="WF_MenuLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_MenuLocale_Language_ID" Association="AMMSModel.FK_GNM_MenuLocale_Language_ID">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_MenuLocale" EntitySet="WF_MenuLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_ModuleID" Association="AMMSModel.FK_ModuleID">
            <End Role="GNM_Module" EntitySet="WF_Module" />
            <End Role="GNM_ModuleLocale" EntitySet="WF_ModuleLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_ModuleLocale_Language_ID" Association="AMMSModel.FK_GNM_ModuleLocale_Language_ID">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_ModuleLocale" EntitySet="WF_ModuleLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_PrefixSuffix_GNM_Object" Association="AMMSModel.FK_GNM_PrefixSuffix_GNM_Object">
            <End Role="GNM_Object" EntitySet="WF_Object" />
            <End Role="GNM_PrefixSuffix" EntitySet="WF_PrefixSuffix" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_RoleObject_Object_ID" Association="AMMSModel.FK_GNM_RoleObject_Object_ID">
            <End Role="GNM_Object" EntitySet="WF_Object" />
            <End Role="GNM_RoleObject" EntitySet="WF_RoleObject" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_User_Language_ID" Association="AMMSModel.FK_GNM_User_Language_ID">
            <End Role="GNM_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_User" EntitySet="WF_User" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_RoleObject_Role_id" Association="AMMSModel.FK_GNM_RoleObject_Role_id">
            <End Role="GNM_Role" EntitySet="WF_Role" />
            <End Role="GNM_RoleObject" EntitySet="WF_RoleObject" />
          </AssociationSet>
          <AssociationSet Name="Fk_Role_id" Association="AMMSModel.Fk_Role_id">
            <End Role="GNM_Role" EntitySet="WF_Role" />
            <End Role="GNM_UserRole" EntitySet="WF_UserRole" />
          </AssociationSet>
          <AssociationSet Name="Fk_User_id" Association="AMMSModel.Fk_User_id">
            <End Role="GNM_User" EntitySet="WF_User" />
            <End Role="GNM_UserRole" EntitySet="WF_UserRole" />
          </AssociationSet>
          <EntitySet Name="WF_EmployeeBranch" EntityType="AMMSModel.WF_EmployeeBranch" />
          <AssociationSet Name="FK_GNM_EmployeeBranch_GNM_Branch" Association="AMMSModel.FK_GNM_EmployeeBranch_GNM_Branch">
            <End Role="WF_Branch" EntitySet="WF_Branch" />
            <End Role="GNM_EmployeeBranch" EntitySet="WF_EmployeeBranch" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_EmployeeBranch_GNM_CompanyEmployee" Association="AMMSModel.FK_GNM_EmployeeBranch_GNM_CompanyEmployee">
            <End Role="WF_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
            <End Role="GNM_EmployeeBranch" EntitySet="WF_EmployeeBranch" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_Branc__Regio__4DAA618B" Association="AMMSModel.FK__GNM_Branc__Regio__4DAA618B">
            <End Role="WF_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="WF_Branch" EntitySet="WF_Branch" />
          </AssociationSet>
          <EntitySet Name="WF_UserLocale" EntityType="AMMSModel.WF_UserLocale" />
          <AssociationSet Name="FK__GNM_Br__Curre__7B5B524B" Association="AMMSModel.FK__GNM_Br__Curre__7B5B524B">
            <End Role="WF_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="WF_Branch" EntitySet="WF_Branch" />
          </AssociationSet>
          <AssociationSet Name="Fk_Branch_LanguageID" Association="AMMSModel.Fk_Branch_LanguageID">
            <End Role="WF_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="WF_Branch" EntitySet="WF_Branch" />
          </AssociationSet>
          <AssociationSet Name="fk_CE_refmstrdetailID" Association="AMMSModel.fk_CE_refmstrdetailID">
            <End Role="WF_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="WF_CompanyEmployee" EntitySet="WF_CompanyEmployee" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_UserLocale_GNM_RefMasterDetail" Association="AMMSModel.FK_GNM_UserLocale_GNM_RefMasterDetail">
            <End Role="WF_RefMasterDetail" EntitySet="WF_RefMasterDetail" />
            <End Role="GNM_UserLocale" EntitySet="WF_UserLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_UserLocale_GNM_User" Association="AMMSModel.FK_GNM_UserLocale_GNM_User">
            <End Role="WF_User" EntitySet="WF_User" />
            <End Role="GNM_UserLocale" EntitySet="WF_UserLocale" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="WF_Branch">
          <Key>
            <PropertyRef Name="Branch_ID" />
          </Key>
          <Property Name="Branch_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="Branch_Name" Type="String" Nullable="false" MaxLength="70" Unicode="false" FixedLength="false" />
          <Property Name="Branch_ShortName" Type="String" MaxLength="10" Unicode="false" FixedLength="false" />
          <Property Name="Branch_ZipCode" Type="String" MaxLength="25" Unicode="false" FixedLength="false" />
          <Property Name="Country_ID" Type="Int32" Nullable="false" />
          <Property Name="State_ID" Type="Int32" Nullable="false" />
          <Property Name="Branch_Phone" Type="String" MaxLength="25" Unicode="false" FixedLength="false" />
          <Property Name="Branch_Fax" Type="String" MaxLength="30" Unicode="false" FixedLength="false" />
          <Property Name="Branch_HeadOffice" Type="Boolean" Nullable="false" />
          <Property Name="Branch_Active" Type="Boolean" Nullable="false" />
          <Property Name="Branch_Address" Type="String" Nullable="false" MaxLength="200" Unicode="false" FixedLength="false" />
          <Property Name="Branch_Location" Type="String" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="Branch_Email" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="Branch_Mobile" Type="String" MaxLength="12" Unicode="false" FixedLength="false" />
          <Property Name="Branch_External" Type="Boolean" />
          <NavigationProperty Name="GNM_Company" Relationship="AMMSModel.FK_GNM_Branch_GNM_Company" FromRole="GNM_Branch" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK_GNM_Branch_GNM_Country" FromRole="GNM_Branch" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_PartyBranchAssociation" Relationship="AMMSModel.FK_GNM_PartyBranchAssociation_GNM_Branch" FromRole="GNM_Branch" ToRole="GNM_PartyBranchAssociation" />
          <NavigationProperty Name="GNM_PrefixSuffix" Relationship="AMMSModel.FK_GNM_PrefixSuffix_GNM_Branch" FromRole="GNM_Branch" ToRole="GNM_PrefixSuffix" />
          <NavigationProperty Name="GNM_EmployeeBranch" Relationship="AMMSModel.FK_GNM_EmployeeBranch_GNM_Branch" FromRole="WF_Branch" ToRole="GNM_EmployeeBranch" />
          <Property Type="Int32" Name="TimeZoneID" />
          <Property Type="Int32" Name="Region_ID" />
          <NavigationProperty Name="GNM_RefMasterDetail1" Relationship="AMMSModel.FK__GNM_Branc__Regio__4DAA618B" FromRole="WF_Branch" ToRole="WF_RefMasterDetail" />
          <Property Type="Int32" Name="Currency_ID" />
          <Property Type="Int32" Name="LanguageID" />
          <Property Type="Byte" Name="IsOverTimeDWM" />
          <Property Type="Decimal" Name="Yearly_Sales_Target" Precision="12" Scale="2" />
          <Property Type="Decimal" Name="Rework_Target" Precision="12" Scale="2" />
          <Property Type="Decimal" Name="Cust_Satisfaction_Target" Precision="12" Scale="2" />
          <Property Type="Decimal" Name="RO_with_Rework_Target" Precision="12" Scale="2" />
          <Property Type="Decimal" Name="RO_with_Cust_Satisfaction_Target" Precision="12" Scale="2" />
          <Property Type="Int32" Name="DueDays" />
          <NavigationProperty Name="GNM_RefMasterDetail2" Relationship="AMMSModel.FK__GNM_Br__Curre__7B5B524B" FromRole="WF_Branch" ToRole="WF_RefMasterDetail" />
          <NavigationProperty Name="GNM_RefMasterDetail21" Relationship="AMMSModel.Fk_Branch_LanguageID" FromRole="WF_Branch" ToRole="WF_RefMasterDetail" />
        </EntityType>
        <EntityType Name="WF_Company">
          <Key>
            <PropertyRef Name="Company_ID" />
          </Key>
          <Property Name="Company_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Company_Name" Type="String" Nullable="false" MaxLength="70" Unicode="false" FixedLength="false" />
          <Property Name="Company_ShortName" Type="String" MaxLength="10" Unicode="false" FixedLength="false" />
          <Property Name="Currency_ID" Type="Int32" Nullable="false" />
          <Property Name="Company_Address" Type="String" MaxLength="200" Unicode="false" FixedLength="false" />
          <Property Name="Company_Type" Type="String" Nullable="false" MaxLength="1" Unicode="false" FixedLength="true" />
          <Property Name="Company_Active" Type="Boolean" Nullable="false" />
          <Property Name="Company_LogoName" Type="String" MaxLength="25" Unicode="false" FixedLength="false" />
          <Property Name="Company_Parent_ID" Type="Int32" />
          <Property Name="Remarks" Type="String" MaxLength="2000" Unicode="true" FixedLength="false" />
          <Property Name="DefaultGridSize" Type="Byte" Nullable="false" />
          <Property Name="JobCardCushionHours" Type="Decimal" Precision="5" Scale="2" />
          <Property Name="ModifiedBy" Type="Int32" Nullable="false" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="CompanyTheme_ID" Type="Int32" />
          <NavigationProperty Name="GNM_Branch" Relationship="AMMSModel.FK_GNM_Branch_GNM_Company" FromRole="GNM_Company" ToRole="GNM_Branch" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK__GNM_Compa__Compa__10373EF7" FromRole="GNM_Company" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_RefMasterDetail1" Relationship="AMMSModel.FK__GNM_Compa__Curre__7B5B524B" FromRole="GNM_Company" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_RefMasterDetail2" Relationship="AMMSModel.FK__GNM_RefMa__Compa__21C0F255" FromRole="GNM_Company" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_CompanyEmployee" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Company" FromRole="GNM_Company" ToRole="GNM_CompanyEmployee" />
          <NavigationProperty Name="GNM_PrefixSuffix" Relationship="AMMSModel.FK_GNM_PrefixSuffix_GNM_Company" FromRole="GNM_Company" ToRole="GNM_PrefixSuffix" />
          <NavigationProperty Name="GNM_User" Relationship="AMMSModel.FK_GNM_User_Company_ID" FromRole="GNM_Company" ToRole="GNM_User" />
          <Property Type="Int32" Name="QuotationValidity" />
          <Property Type="String" Name="CompanyFont" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="Decimal" Name="InventoryCarryingFactoy_Percentage" Precision="5" Scale="2" />
          <Property Type="Int32" Name="OrderingCost" />
        </EntityType>
        <EntityType Name="WF_CompanyEmployee">
          <Key>
            <PropertyRef Name="Company_Employee_ID" />
          </Key>
          <Property Name="Company_Employee_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Employee_ID" Type="String" Nullable="false" MaxLength="10" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_Name" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="Country_ID" Type="Int32" Nullable="false" />
          <Property Name="State_ID" Type="Int32" Nullable="false" />
          <Property Name="Company_Employee_MobileNumber" Type="String" MaxLength="15" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_Landline_Number" Type="String" MaxLength="25" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_ZipCode" Type="String" MaxLength="25" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_ActiveFrom" Type="DateTime" Precision="3" />
          <Property Name="Company_Employee_ValidateUpTo" Type="DateTime" Precision="3" />
          <Property Name="Company_Employee_Active" Type="Boolean" Nullable="false" />
          <Property Name="Company_Employee_Manager_ID" Type="Int32" />
          <Property Name="Company_Employee_Address" Type="String" Nullable="false" MaxLength="200" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_Location" Type="String" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_Email" Type="String" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="Company_Employee_Department_ID" Type="Int32" Nullable="false" />
          <Property Name="Company_Employee_Designation_ID" Type="Int32" Nullable="false" />
          <Property Name="ModifiedBy" Type="Int32" Nullable="false" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <NavigationProperty Name="GNM_Company" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Company" FromRole="GNM_CompanyEmployee" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail" FromRole="GNM_CompanyEmployee" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_RefMasterDetail1" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail2" FromRole="GNM_CompanyEmployee" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_RefMasterDetail2" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail3" FromRole="GNM_CompanyEmployee" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_User" Relationship="AMMSModel.FK_GNM_User_Employee_ID" FromRole="GNM_CompanyEmployee" ToRole="GNM_User" />
          <NavigationProperty Name="GNM_EmployeeBranch" Relationship="AMMSModel.FK_GNM_EmployeeBranch_GNM_CompanyEmployee" FromRole="WF_CompanyEmployee" ToRole="GNM_EmployeeBranch" />
          <Property Type="Decimal" Name="HourlyRate" Precision="12" Scale="2" />
          <Property Type="Int32" Name="Region_ID" />
          <Property Type="Boolean" Name="IsEligibleForOT" />
          <Property Type="Byte" Name="ExemptionHours" />
          <Property Type="Boolean" Name="IsOnJob" />
          <Property Type="Int32" Name="JobID" />
          <NavigationProperty Name="GNM_RefMasterDetail3" Relationship="AMMSModel.fk_CE_refmstrdetailID" FromRole="WF_CompanyEmployee" ToRole="WF_RefMasterDetail" />
        </EntityType>
        <EntityType Name="WF_Email">
          <Key>
            <PropertyRef Name="Email_ID" />
          </Key>
          <Property Name="Email_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Email_Subject" Type="String" MaxLength="500" Unicode="true" FixedLength="false" />
          <Property Name="Email_Body" Type="String" MaxLength="Max" Unicode="true" FixedLength="false" />
          <Property Name="Email_To" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Email_cc" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Email_Bcc" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Email_Queue_Date" Type="DateTime" Precision="3" />
          <Property Name="Email_Sent_Date" Type="DateTime" Precision="3" />
          <Property Name="Email_SentStatus" Type="Boolean" Nullable="false" />
          <Property Type="String" Name="Email_Attachments" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Type="Boolean" Name="Email_IsError" />
          <Property Type="Byte" Name="NoOfAttempts" />
        </EntityType>
        <EntityType Name="WF_Menu">
          <Key>
            <PropertyRef Name="Menu_ID" />
          </Key>
          <Property Name="Menu_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Module_ID" Type="Int32" Nullable="false" />
          <Property Name="Menu_Description" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="Parentmenu_ID" Type="Int32" />
          <Property Name="Object_ID" Type="Int32" />
          <Property Name="Menu_Path" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Menu_SortOrder" Type="Byte" Nullable="false" />
          <Property Name="Menu_IsActive" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="GNM_Module" Relationship="AMMSModel.Fk_GNM_Menu_Module_id" FromRole="GNM_Menu" ToRole="GNM_Module" />
          <NavigationProperty Name="GNM_MenuLocale" Relationship="AMMSModel.FK_MenuID" FromRole="GNM_Menu" ToRole="GNM_MenuLocale" />
          <Property Type="String" Name="Menu_IconName" MaxLength="200" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_MenuLocale">
          <Key>
            <PropertyRef Name="MenuLocale_ID" />
          </Key>
          <Property Name="MenuLocale_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Language_ID" Type="Int32" Nullable="false" />
          <Property Name="Menu_ID" Type="Int32" Nullable="false" />
          <Property Name="Menu_Description" Type="String" Nullable="false" MaxLength="100" Unicode="true" FixedLength="false" />
          <NavigationProperty Name="GNM_Menu" Relationship="AMMSModel.FK_MenuID" FromRole="GNM_MenuLocale" ToRole="GNM_Menu" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK_GNM_MenuLocale_Language_ID" FromRole="GNM_MenuLocale" ToRole="GNM_RefMasterDetail" />
        </EntityType>
        <EntityType Name="WF_Module">
          <Key>
            <PropertyRef Name="Module_ID" />
          </Key>
          <Property Name="Module_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Module_Description" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Module_IsActive" Type="Boolean" Nullable="false" />
          <Property Name="Module_SortOrder" Type="Byte" Nullable="false" />
          <NavigationProperty Name="GNM_Menu" Relationship="AMMSModel.Fk_GNM_Menu_Module_id" FromRole="GNM_Module" ToRole="GNM_Menu" />
          <NavigationProperty Name="GNM_ModuleLocale" Relationship="AMMSModel.FK_ModuleID" FromRole="GNM_Module" ToRole="GNM_ModuleLocale" />
          <Property Type="String" Name="Module_IconName" MaxLength="200" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_ModuleLocale">
          <Key>
            <PropertyRef Name="ModuleLocale_ID" />
          </Key>
          <Property Name="ModuleLocale_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Language_ID" Type="Int32" Nullable="false" />
          <Property Name="Module_ID" Type="Int32" Nullable="false" />
          <Property Name="Module_Description" Type="String" Nullable="false" MaxLength="200" Unicode="true" FixedLength="false" />
          <NavigationProperty Name="GNM_Module" Relationship="AMMSModel.FK_ModuleID" FromRole="GNM_ModuleLocale" ToRole="GNM_Module" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK_GNM_ModuleLocale_Language_ID" FromRole="GNM_ModuleLocale" ToRole="GNM_RefMasterDetail" />
          <Property Type="String" Name="Module_IconName" MaxLength="200" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_Object">
          <Key>
            <PropertyRef Name="Object_ID" />
          </Key>
          <Property Name="Object_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Object_Name" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Read_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Create_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Update_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Delete_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Export_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Print_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Object_IsActive" Type="Boolean" Nullable="false" />
          <Property Name="Object_Description" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Import_Action" Type="String" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="Object_Type" Type="String" MaxLength="1" Unicode="false" FixedLength="true" />
          <NavigationProperty Name="GNM_PrefixSuffix" Relationship="AMMSModel.FK_GNM_PrefixSuffix_GNM_Object" FromRole="GNM_Object" ToRole="GNM_PrefixSuffix" />
          <NavigationProperty Name="GNM_RoleObject" Relationship="AMMSModel.FK_GNM_RoleObject_Object_ID" FromRole="GNM_Object" ToRole="GNM_RoleObject" />
        </EntityType>
        <EntityType Name="WF_PartyBranchAssociation">
          <Key>
            <PropertyRef Name="PartyBranch_ID" />
          </Key>
          <Property Name="PartyBranch_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Party_ID" Type="Int32" Nullable="false" />
          <Property Name="Branch_ID" Type="Int32" Nullable="false" />
          <NavigationProperty Name="GNM_Branch" Relationship="AMMSModel.FK_GNM_PartyBranchAssociation_GNM_Branch" FromRole="GNM_PartyBranchAssociation" ToRole="GNM_Branch" />
        </EntityType>
        <EntityType Name="WF_PrefixSuffix">
          <Key>
            <PropertyRef Name="PrefixSuffix_ID" />
          </Key>
          <Property Name="PrefixSuffix_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="Branch_ID" Type="Int32" />
          <Property Name="Object_ID" Type="Int32" Nullable="false" />
          <Property Name="Start_Number" Type="Int32" Nullable="false" />
          <Property Name="Prefix" Type="String" MaxLength="50" Unicode="true" FixedLength="false" />
          <Property Name="Suffix" Type="String" MaxLength="50" Unicode="true" FixedLength="false" />
          <Property Name="FromDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ToDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ModifiedBY" Type="Int32" Nullable="false" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="FinancialYear" Type="Int32" />
          <Property Name="Company_FinancialYear_ID" Type="Int32" />
          <NavigationProperty Name="GNM_Branch" Relationship="AMMSModel.FK_GNM_PrefixSuffix_GNM_Branch" FromRole="GNM_PrefixSuffix" ToRole="GNM_Branch" />
          <NavigationProperty Name="GNM_Company" Relationship="AMMSModel.FK_GNM_PrefixSuffix_GNM_Company" FromRole="GNM_PrefixSuffix" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_Object" Relationship="AMMSModel.FK_GNM_PrefixSuffix_GNM_Object" FromRole="GNM_PrefixSuffix" ToRole="GNM_Object" />
        </EntityType>
        <EntityType Name="WF_RefMasterDetail">
          <Key>
            <PropertyRef Name="RefMasterDetail_ID" />
          </Key>
          <Property Name="RefMasterDetail_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="RefMasterDetail_IsActive" Type="Boolean" Nullable="false" />
          <Property Name="RefMasterDetail_Short_Name" Type="String" MaxLength="15" Unicode="true" FixedLength="false" />
          <Property Name="RefMasterDetail_Name" Type="String" Nullable="false" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Company_ID" Type="Int32" />
          <Property Name="ModifiedBy" Type="Int32" Nullable="false" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="RefMaster_ID" Type="Int32" Nullable="false" />
          <NavigationProperty Name="GNM_Branch" Relationship="AMMSModel.FK_GNM_Branch_GNM_Country" FromRole="GNM_RefMasterDetail" ToRole="GNM_Branch" />
          <NavigationProperty Name="GNM_Company" Relationship="AMMSModel.FK__GNM_Compa__Compa__10373EF7" FromRole="GNM_RefMasterDetail" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_Company1" Relationship="AMMSModel.FK__GNM_Compa__Curre__7B5B524B" FromRole="GNM_RefMasterDetail" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_Company2" Relationship="AMMSModel.FK__GNM_RefMa__Compa__21C0F255" FromRole="GNM_RefMasterDetail" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_CompanyEmployee" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail" FromRole="GNM_RefMasterDetail" ToRole="GNM_CompanyEmployee" />
          <NavigationProperty Name="GNM_CompanyEmployee1" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail2" FromRole="GNM_RefMasterDetail" ToRole="GNM_CompanyEmployee" />
          <NavigationProperty Name="GNM_CompanyEmployee2" Relationship="AMMSModel.FK_GNM_CompanyEmployee_GNM_Refmasterdetail3" FromRole="GNM_RefMasterDetail" ToRole="GNM_CompanyEmployee" />
          <NavigationProperty Name="GNM_MenuLocale" Relationship="AMMSModel.FK_GNM_MenuLocale_Language_ID" FromRole="GNM_RefMasterDetail" ToRole="GNM_MenuLocale" />
          <NavigationProperty Name="GNM_ModuleLocale" Relationship="AMMSModel.FK_GNM_ModuleLocale_Language_ID" FromRole="GNM_RefMasterDetail" ToRole="GNM_ModuleLocale" />
          <NavigationProperty Name="GNM_User" Relationship="AMMSModel.FK_GNM_User_Language_ID" FromRole="GNM_RefMasterDetail" ToRole="GNM_User" />
          <Property Type="Boolean" Name="RefMasterDetail_IsDefault" Nullable="false" />
          <NavigationProperty Name="GNM_Branch1" Relationship="AMMSModel.FK__GNM_Branc__Regio__4DAA618B" FromRole="WF_RefMasterDetail" ToRole="WF_Branch" />
          <NavigationProperty Name="GNM_Branch2" Relationship="AMMSModel.FK__GNM_Br__Curre__7B5B524B" FromRole="WF_RefMasterDetail" ToRole="WF_Branch" />
          <NavigationProperty Name="GNM_Branch21" Relationship="AMMSModel.Fk_Branch_LanguageID" FromRole="WF_RefMasterDetail" ToRole="WF_Branch" />
          <NavigationProperty Name="GNM_CompanyEmployee3" Relationship="AMMSModel.fk_CE_refmstrdetailID" FromRole="WF_RefMasterDetail" ToRole="WF_CompanyEmployee" />
          <NavigationProperty Name="GNM_UserLocale" Relationship="AMMSModel.FK_GNM_UserLocale_GNM_RefMasterDetail" FromRole="WF_RefMasterDetail" ToRole="GNM_UserLocale" />
        </EntityType>
        <EntityType Name="WF_Role">
          <Key>
            <PropertyRef Name="Role_ID" />
          </Key>
          <Property Name="Role_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="Role_Name" Type="String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />
          <NavigationProperty Name="GNM_RoleObject" Relationship="AMMSModel.FK_GNM_RoleObject_Role_id" FromRole="GNM_Role" ToRole="GNM_RoleObject" />
          <NavigationProperty Name="GNM_UserRole" Relationship="AMMSModel.Fk_Role_id" FromRole="GNM_Role" ToRole="GNM_UserRole" />
        </EntityType>
        <EntityType Name="WF_RoleObject">
          <Key>
            <PropertyRef Name="RoleObject_ID" />
          </Key>
          <Property Name="RoleObject_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Role_ID" Type="Int32" Nullable="false" />
          <Property Name="Object_ID" Type="Int32" Nullable="false" />
          <Property Name="RoleObject_Create" Type="Boolean" Nullable="false" />
          <Property Name="RoleObject_Read" Type="Boolean" Nullable="false" />
          <Property Name="RoleObject_Update" Type="Boolean" Nullable="false" />
          <Property Name="RoleObject_Delete" Type="Boolean" Nullable="false" />
          <Property Name="RoleObject_Print" Type="Boolean" Nullable="false" />
          <Property Name="RoleObject_Export" Type="Boolean" Nullable="false" />
          <Property Name="RoleObject_Import" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="GNM_Object" Relationship="AMMSModel.FK_GNM_RoleObject_Object_ID" FromRole="GNM_RoleObject" ToRole="GNM_Object" />
          <NavigationProperty Name="GNM_Role" Relationship="AMMSModel.FK_GNM_RoleObject_Role_id" FromRole="GNM_RoleObject" ToRole="GNM_Role" />
        </EntityType>
        <EntityType Name="WF_Sms">
          <Key>
            <PropertyRef Name="Sms_ID" />
          </Key>
          <Property Name="Sms_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Sms_Text" Type="String" MaxLength="200" Unicode="true" FixedLength="false" />
          <Property Name="Sms_Mobile_Number" Type="String" MaxLength="15" Unicode="false" FixedLength="false" />
          <Property Name="Sms_Queue_Date" Type="DateTime" Precision="3" />
          <Property Name="Sms_Sent_Date" Type="DateTime" Precision="3" />
          <Property Name="Sms_SentStatus" Type="Boolean" Nullable="false" />
          <Property Type="Int32" Name="Template_ID" Nullable="false" />
          <Property Type="String" Name="Parameter1_value" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parameter2_value" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parameter3_value" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parameter4_value" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_User">
          <Key>
            <PropertyRef Name="User_ID" />
          </Key>
          <Property Name="User_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="User_Name" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="User_LoginID" Type="String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />
          <Property Name="User_Password" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="User_IsActive" Type="Boolean" Nullable="false" />
          <Property Name="User_Locked" Type="Boolean" Nullable="false" />
          <Property Name="User_LoginCount" Type="Int32" />
          <Property Name="User_FailedCount" Type="Int32" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="Language_ID" Type="Int32" Nullable="false" />
          <Property Name="User_Type_ID" Type="Byte" Nullable="false" />
          <Property Name="Employee_ID" Type="Int32" />
          <Property Name="Partner_ID" Type="Int32" />
          <NavigationProperty Name="GNM_Company" Relationship="AMMSModel.FK_GNM_User_Company_ID" FromRole="GNM_User" ToRole="GNM_Company" />
          <NavigationProperty Name="GNM_CompanyEmployee" Relationship="AMMSModel.FK_GNM_User_Employee_ID" FromRole="GNM_User" ToRole="GNM_CompanyEmployee" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK_GNM_User_Language_ID" FromRole="GNM_User" ToRole="GNM_RefMasterDetail" />
          <NavigationProperty Name="GNM_UserRole" Relationship="AMMSModel.Fk_User_id" FromRole="GNM_User" ToRole="GNM_UserRole" />
          <Property Type="String" Name="LandingPage" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="User_IPAddress" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Type="Int32" Name="WareHouse_ID" />
          <Property Type="String" Name="ReleaseVersionPopup" MaxLength="100" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="GNM_UserLocale" Relationship="AMMSModel.FK_GNM_UserLocale_GNM_User" FromRole="WF_User" ToRole="GNM_UserLocale" />
        </EntityType>
        <EntityType Name="WF_UserRole">
          <Key>
            <PropertyRef Name="UserRole_ID" />
          </Key>
          <Property Name="UserRole_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="User_ID" Type="Int32" Nullable="false" />
          <Property Name="Role_ID" Type="Int32" Nullable="false" />
          <NavigationProperty Name="GNM_Role" Relationship="AMMSModel.Fk_Role_id" FromRole="GNM_UserRole" ToRole="GNM_Role" />
          <NavigationProperty Name="GNM_User" Relationship="AMMSModel.Fk_User_id" FromRole="GNM_UserRole" ToRole="GNM_User" />
        </EntityType>
        <Association Name="FK_GNM_Branch_GNM_Company">
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="1" />
          <End Role="GNM_Branch" Type="AMMSModel.WF_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_Branch_GNM_Country">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_Branch" Type="AMMSModel.WF_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Branch">
              <PropertyRef Name="Country_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PartyBranchAssociation_GNM_Branch">
          <End Role="GNM_Branch" Type="AMMSModel.WF_Branch" Multiplicity="1" />
          <End Role="GNM_PartyBranchAssociation" Type="AMMSModel.WF_PartyBranchAssociation" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Branch">
              <PropertyRef Name="Branch_ID" />
            </Principal>
            <Dependent Role="GNM_PartyBranchAssociation">
              <PropertyRef Name="Branch_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PrefixSuffix_GNM_Branch">
          <End Role="GNM_Branch" Type="AMMSModel.WF_Branch" Multiplicity="0..1" />
          <End Role="GNM_PrefixSuffix" Type="AMMSModel.WF_PrefixSuffix" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Branch">
              <PropertyRef Name="Branch_ID" />
            </Principal>
            <Dependent Role="GNM_PrefixSuffix">
              <PropertyRef Name="Branch_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_Compa__Compa__10373EF7">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="0..1" />
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Company">
              <PropertyRef Name="CompanyTheme_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_Compa__Curre__7B5B524B">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_Company">
              <PropertyRef Name="Currency_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_RefMa__Compa__21C0F255">
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="0..1" />
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_RefMasterDetail">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Company">
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.WF_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PrefixSuffix_GNM_Company">
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="1" />
          <End Role="GNM_PrefixSuffix" Type="AMMSModel.WF_PrefixSuffix" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_PrefixSuffix">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_User_Company_ID">
          <End Role="GNM_Company" Type="AMMSModel.WF_Company" Multiplicity="1" />
          <End Role="GNM_User" Type="AMMSModel.WF_User" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Company">
              <PropertyRef Name="Company_ID" />
            </Principal>
            <Dependent Role="GNM_User">
              <PropertyRef Name="Company_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.WF_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Country_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail2">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.WF_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_Department_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_CompanyEmployee_GNM_Refmasterdetail3">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.WF_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_Designation_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_User_Employee_ID">
          <End Role="GNM_CompanyEmployee" Type="AMMSModel.WF_CompanyEmployee" Multiplicity="0..1" />
          <End Role="GNM_User" Type="AMMSModel.WF_User" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_CompanyEmployee">
              <PropertyRef Name="Company_Employee_ID" />
            </Principal>
            <Dependent Role="GNM_User">
              <PropertyRef Name="Employee_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_GNM_Menu_Module_id">
          <End Role="GNM_Module" Type="AMMSModel.WF_Module" Multiplicity="1" />
          <End Role="GNM_Menu" Type="AMMSModel.WF_Menu" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Module">
              <PropertyRef Name="Module_ID" />
            </Principal>
            <Dependent Role="GNM_Menu">
              <PropertyRef Name="Module_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_MenuID">
          <End Role="GNM_Menu" Type="AMMSModel.WF_Menu" Multiplicity="1" />
          <End Role="GNM_MenuLocale" Type="AMMSModel.WF_MenuLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Menu">
              <PropertyRef Name="Menu_ID" />
            </Principal>
            <Dependent Role="GNM_MenuLocale">
              <PropertyRef Name="Menu_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_MenuLocale_Language_ID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_MenuLocale" Type="AMMSModel.WF_MenuLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_MenuLocale">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ModuleID">
          <End Role="GNM_Module" Type="AMMSModel.WF_Module" Multiplicity="1" />
          <End Role="GNM_ModuleLocale" Type="AMMSModel.WF_ModuleLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Module">
              <PropertyRef Name="Module_ID" />
            </Principal>
            <Dependent Role="GNM_ModuleLocale">
              <PropertyRef Name="Module_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_ModuleLocale_Language_ID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_ModuleLocale" Type="AMMSModel.WF_ModuleLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_ModuleLocale">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_PrefixSuffix_GNM_Object">
          <End Role="GNM_Object" Type="AMMSModel.WF_Object" Multiplicity="1" />
          <End Role="GNM_PrefixSuffix" Type="AMMSModel.WF_PrefixSuffix" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Object">
              <PropertyRef Name="Object_ID" />
            </Principal>
            <Dependent Role="GNM_PrefixSuffix">
              <PropertyRef Name="Object_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_RoleObject_Object_ID">
          <End Role="GNM_Object" Type="AMMSModel.WF_Object" Multiplicity="1" />
          <End Role="GNM_RoleObject" Type="AMMSModel.WF_RoleObject" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Object">
              <PropertyRef Name="Object_ID" />
            </Principal>
            <Dependent Role="GNM_RoleObject">
              <PropertyRef Name="Object_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_User_Language_ID">
          <End Role="GNM_RefMasterDetail" Type="AMMSModel.WF_RefMasterDetail" Multiplicity="1" />
          <End Role="GNM_User" Type="AMMSModel.WF_User" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_User">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_RoleObject_Role_id">
          <End Role="GNM_Role" Type="AMMSModel.WF_Role" Multiplicity="1" />
          <End Role="GNM_RoleObject" Type="AMMSModel.WF_RoleObject" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Role">
              <PropertyRef Name="Role_ID" />
            </Principal>
            <Dependent Role="GNM_RoleObject">
              <PropertyRef Name="Role_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_Role_id">
          <End Role="GNM_Role" Type="AMMSModel.WF_Role" Multiplicity="1" />
          <End Role="GNM_UserRole" Type="AMMSModel.WF_UserRole" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_Role">
              <PropertyRef Name="Role_ID" />
            </Principal>
            <Dependent Role="GNM_UserRole">
              <PropertyRef Name="Role_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_User_id">
          <End Role="GNM_User" Type="AMMSModel.WF_User" Multiplicity="1" />
          <End Role="GNM_UserRole" Type="AMMSModel.WF_UserRole" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_User">
              <PropertyRef Name="User_ID" />
            </Principal>
            <Dependent Role="GNM_UserRole">
              <PropertyRef Name="User_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="WF_EmployeeBranch">
          <Key>
            <PropertyRef Name="EmployeeBranch_ID" />
          </Key>
          <Property Type="Int32" Name="EmployeeBranch_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="CompanyEmployee_ID" Nullable="false" />
          <Property Type="Int32" Name="Branch_ID" Nullable="false" />
          <NavigationProperty Name="GNM_Branch" Relationship="AMMSModel.FK_GNM_EmployeeBranch_GNM_Branch" FromRole="GNM_EmployeeBranch" ToRole="WF_Branch" />
          <NavigationProperty Name="GNM_CompanyEmployee" Relationship="AMMSModel.FK_GNM_EmployeeBranch_GNM_CompanyEmployee" FromRole="GNM_EmployeeBranch" ToRole="WF_CompanyEmployee" />
          <Property Type="Boolean" Name="IsDefault" />
        </EntityType>
        <Association Name="FK_GNM_EmployeeBranch_GNM_Branch">
          <End Type="AMMSModel.WF_Branch" Role="WF_Branch" Multiplicity="1" />
          <End Type="AMMSModel.WF_EmployeeBranch" Role="GNM_EmployeeBranch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_Branch">
              <PropertyRef Name="Branch_ID" />
            </Principal>
            <Dependent Role="GNM_EmployeeBranch">
              <PropertyRef Name="Branch_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_EmployeeBranch_GNM_CompanyEmployee">
          <End Type="AMMSModel.WF_CompanyEmployee" Role="WF_CompanyEmployee" Multiplicity="1" />
          <End Type="AMMSModel.WF_EmployeeBranch" Role="GNM_EmployeeBranch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_CompanyEmployee">
              <PropertyRef Name="Company_Employee_ID" />
            </Principal>
            <Dependent Role="GNM_EmployeeBranch">
              <PropertyRef Name="CompanyEmployee_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_Branc__Regio__4DAA618B">
          <End Type="AMMSModel.WF_RefMasterDetail" Role="WF_RefMasterDetail" Multiplicity="0..1" />
          <End Type="AMMSModel.WF_Branch" Role="WF_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="WF_Branch">
              <PropertyRef Name="Region_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="WF_UserLocale">
          <Key>
            <PropertyRef Name="User_Locale_ID" />
          </Key>
          <Property Type="Int32" Name="User_Locale_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="User_ID" Nullable="false" />
          <Property Type="Int32" Name="Language_ID" Nullable="false" />
          <Property Type="String" Name="User_Name" Nullable="false" MaxLength="140" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="GNM_RefMasterDetail" Relationship="AMMSModel.FK_GNM_UserLocale_GNM_RefMasterDetail" FromRole="GNM_UserLocale" ToRole="WF_RefMasterDetail" />
          <NavigationProperty Name="GNM_User" Relationship="AMMSModel.FK_GNM_UserLocale_GNM_User" FromRole="GNM_UserLocale" ToRole="WF_User" />
        </EntityType>
        <Association Name="FK__GNM_Br__Curre__7B5B524B">
          <End Type="AMMSModel.WF_RefMasterDetail" Role="WF_RefMasterDetail" Multiplicity="0..1" />
          <End Type="AMMSModel.WF_Branch" Role="WF_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="WF_Branch">
              <PropertyRef Name="Currency_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="Fk_Branch_LanguageID">
          <End Type="AMMSModel.WF_RefMasterDetail" Role="WF_RefMasterDetail" Multiplicity="0..1" />
          <End Type="AMMSModel.WF_Branch" Role="WF_Branch" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="WF_Branch">
              <PropertyRef Name="LanguageID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="fk_CE_refmstrdetailID">
          <End Type="AMMSModel.WF_RefMasterDetail" Role="WF_RefMasterDetail" Multiplicity="0..1" />
          <End Type="AMMSModel.WF_CompanyEmployee" Role="WF_CompanyEmployee" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="WF_CompanyEmployee">
              <PropertyRef Name="Region_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_UserLocale_GNM_RefMasterDetail">
          <End Type="AMMSModel.WF_RefMasterDetail" Role="WF_RefMasterDetail" Multiplicity="1" />
          <End Type="AMMSModel.WF_UserLocale" Role="GNM_UserLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_RefMasterDetail">
              <PropertyRef Name="RefMasterDetail_ID" />
            </Principal>
            <Dependent Role="GNM_UserLocale">
              <PropertyRef Name="Language_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_UserLocale_GNM_User">
          <End Type="AMMSModel.WF_User" Role="WF_User" Multiplicity="1" />
          <End Type="AMMSModel.WF_UserLocale" Role="GNM_UserLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_User">
              <PropertyRef Name="User_ID" />
            </Principal>
            <Dependent Role="GNM_UserLocale">
              <PropertyRef Name="User_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="AMMSModelStoreContainer" CdmEntityContainer="GenEntities">
          <EntitySetMapping Name="WF_Branch">
            <EntityTypeMapping TypeName="AMMSModel.WF_Branch">
              <MappingFragment StoreEntitySet="GNM_Branch">
                <ScalarProperty Name="DueDays" ColumnName="DueDays" />
                <ScalarProperty Name="RO_with_Cust_Satisfaction_Target" ColumnName="RO_with_Cust_Satisfaction_Target" />
                <ScalarProperty Name="RO_with_Rework_Target" ColumnName="RO_with_Rework_Target" />
                <ScalarProperty Name="Cust_Satisfaction_Target" ColumnName="Cust_Satisfaction_Target" />
                <ScalarProperty Name="Rework_Target" ColumnName="Rework_Target" />
                <ScalarProperty Name="Yearly_Sales_Target" ColumnName="Yearly_Sales_Target" />
                <ScalarProperty Name="IsOverTimeDWM" ColumnName="IsOverTimeDWM" />
                <ScalarProperty Name="LanguageID" ColumnName="LanguageID" />
                <ScalarProperty Name="Currency_ID" ColumnName="Currency_ID" />
                <ScalarProperty Name="Region_ID" ColumnName="Region_ID" />
                <ScalarProperty Name="TimeZoneID" ColumnName="TimeZoneID" />
                <ScalarProperty Name="Branch_ID" ColumnName="Branch_ID" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Branch_Name" ColumnName="Branch_Name" />
                <ScalarProperty Name="Branch_ShortName" ColumnName="Branch_ShortName" />
                <ScalarProperty Name="Branch_ZipCode" ColumnName="Branch_ZipCode" />
                <ScalarProperty Name="Country_ID" ColumnName="Country_ID" />
                <ScalarProperty Name="State_ID" ColumnName="State_ID" />
                <ScalarProperty Name="Branch_Phone" ColumnName="Branch_Phone" />
                <ScalarProperty Name="Branch_Fax" ColumnName="Branch_Fax" />
                <ScalarProperty Name="Branch_HeadOffice" ColumnName="Branch_HeadOffice" />
                <ScalarProperty Name="Branch_Active" ColumnName="Branch_Active" />
                <ScalarProperty Name="Branch_Address" ColumnName="Branch_Address" />
                <ScalarProperty Name="Branch_Location" ColumnName="Branch_Location" />
                <ScalarProperty Name="Branch_Email" ColumnName="Branch_Email" />
                <ScalarProperty Name="Branch_Mobile" ColumnName="Branch_Mobile" />
                <ScalarProperty Name="Branch_External" ColumnName="Branch_External" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Company">
            <EntityTypeMapping TypeName="AMMSModel.WF_Company">
              <MappingFragment StoreEntitySet="GNM_Company">
                <ScalarProperty Name="OrderingCost" ColumnName="OrderingCost" />
                <ScalarProperty Name="InventoryCarryingFactoy_Percentage" ColumnName="InventoryCarryingFactoy_Percentage" />
                <ScalarProperty Name="CompanyFont" ColumnName="CompanyFont" />
                <ScalarProperty Name="QuotationValidity" ColumnName="QuotationValidity" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Company_Name" ColumnName="Company_Name" />
                <ScalarProperty Name="Company_ShortName" ColumnName="Company_ShortName" />
                <ScalarProperty Name="Currency_ID" ColumnName="Currency_ID" />
                <ScalarProperty Name="Company_Address" ColumnName="Company_Address" />
                <ScalarProperty Name="Company_Type" ColumnName="Company_Type" />
                <ScalarProperty Name="Company_Active" ColumnName="Company_Active" />
                <ScalarProperty Name="Company_LogoName" ColumnName="Company_LogoName" />
                <ScalarProperty Name="Company_Parent_ID" ColumnName="Company_Parent_ID" />
                <ScalarProperty Name="Remarks" ColumnName="Remarks" />
                <ScalarProperty Name="DefaultGridSize" ColumnName="DefaultGridSize" />
                <ScalarProperty Name="JobCardCushionHours" ColumnName="JobCardCushionHours" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="CompanyTheme_ID" ColumnName="CompanyTheme_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_CompanyEmployee">
            <EntityTypeMapping TypeName="AMMSModel.WF_CompanyEmployee">
              <MappingFragment StoreEntitySet="GNM_CompanyEmployee">
                <ScalarProperty Name="JobID" ColumnName="JobID" />
                <ScalarProperty Name="IsOnJob" ColumnName="IsOnJob" />
                <ScalarProperty Name="ExemptionHours" ColumnName="ExemptionHours" />
                <ScalarProperty Name="IsEligibleForOT" ColumnName="IsEligibleForOT" />
                <ScalarProperty Name="Region_ID" ColumnName="Region_ID" />
                <ScalarProperty Name="HourlyRate" ColumnName="HourlyRate" />
                <ScalarProperty Name="Company_Employee_ID" ColumnName="Company_Employee_ID" />
                <ScalarProperty Name="Employee_ID" ColumnName="Employee_ID" />
                <ScalarProperty Name="Company_Employee_Name" ColumnName="Company_Employee_Name" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Country_ID" ColumnName="Country_ID" />
                <ScalarProperty Name="State_ID" ColumnName="State_ID" />
                <ScalarProperty Name="Company_Employee_MobileNumber" ColumnName="Company_Employee_MobileNumber" />
                <ScalarProperty Name="Company_Employee_Landline_Number" ColumnName="Company_Employee_Landline_Number" />
                <ScalarProperty Name="Company_Employee_ZipCode" ColumnName="Company_Employee_ZipCode" />
                <ScalarProperty Name="Company_Employee_ActiveFrom" ColumnName="Company_Employee_ActiveFrom" />
                <ScalarProperty Name="Company_Employee_ValidateUpTo" ColumnName="Company_Employee_ValidateUpTo" />
                <ScalarProperty Name="Company_Employee_Active" ColumnName="Company_Employee_Active" />
                <ScalarProperty Name="Company_Employee_Manager_ID" ColumnName="Company_Employee_Manager_ID" />
                <ScalarProperty Name="Company_Employee_Address" ColumnName="Company_Employee_Address" />
                <ScalarProperty Name="Company_Employee_Location" ColumnName="Company_Employee_Location" />
                <ScalarProperty Name="Company_Employee_Email" ColumnName="Company_Employee_Email" />
                <ScalarProperty Name="Company_Employee_Department_ID" ColumnName="Company_Employee_Department_ID" />
                <ScalarProperty Name="Company_Employee_Designation_ID" ColumnName="Company_Employee_Designation_ID" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Email">
            <EntityTypeMapping TypeName="AMMSModel.WF_Email">
              <MappingFragment StoreEntitySet="GNM_Email">
                <ScalarProperty Name="NoOfAttempts" ColumnName="NoOfAttempts" />
                <ScalarProperty Name="Email_IsError" ColumnName="Email_IsError" />
                <ScalarProperty Name="Email_Attachments" ColumnName="Email_Attachments" />
                <ScalarProperty Name="Email_ID" ColumnName="Email_ID" />
                <ScalarProperty Name="Email_Subject" ColumnName="Email_Subject" />
                <ScalarProperty Name="Email_Body" ColumnName="Email_Body" />
                <ScalarProperty Name="Email_To" ColumnName="Email_To" />
                <ScalarProperty Name="Email_cc" ColumnName="Email_cc" />
                <ScalarProperty Name="Email_Bcc" ColumnName="Email_Bcc" />
                <ScalarProperty Name="Email_Queue_Date" ColumnName="Email_Queue_Date" />
                <ScalarProperty Name="Email_Sent_Date" ColumnName="Email_Sent_Date" />
                <ScalarProperty Name="Email_SentStatus" ColumnName="Email_SentStatus" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Menu">
            <EntityTypeMapping TypeName="AMMSModel.WF_Menu">
              <MappingFragment StoreEntitySet="GNM_Menu">
                <ScalarProperty Name="Menu_IconName" ColumnName="Menu_IconName" />
                <ScalarProperty Name="Menu_ID" ColumnName="Menu_ID" />
                <ScalarProperty Name="Module_ID" ColumnName="Module_ID" />
                <ScalarProperty Name="Menu_Description" ColumnName="Menu_Description" />
                <ScalarProperty Name="Parentmenu_ID" ColumnName="Parentmenu_ID" />
                <ScalarProperty Name="Object_ID" ColumnName="Object_ID" />
                <ScalarProperty Name="Menu_Path" ColumnName="Menu_Path" />
                <ScalarProperty Name="Menu_SortOrder" ColumnName="Menu_SortOrder" />
                <ScalarProperty Name="Menu_IsActive" ColumnName="Menu_IsActive" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_MenuLocale">
            <EntityTypeMapping TypeName="AMMSModel.WF_MenuLocale">
              <MappingFragment StoreEntitySet="GNM_MenuLocale">
                <ScalarProperty Name="MenuLocale_ID" ColumnName="MenuLocale_ID" />
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="Menu_ID" ColumnName="Menu_ID" />
                <ScalarProperty Name="Menu_Description" ColumnName="Menu_Description" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Module">
            <EntityTypeMapping TypeName="AMMSModel.WF_Module">
              <MappingFragment StoreEntitySet="GNM_Module">
                <ScalarProperty Name="Module_IconName" ColumnName="Module_IconName" />
                <ScalarProperty Name="Module_ID" ColumnName="Module_ID" />
                <ScalarProperty Name="Module_Description" ColumnName="Module_Description" />
                <ScalarProperty Name="Module_IsActive" ColumnName="Module_IsActive" />
                <ScalarProperty Name="Module_SortOrder" ColumnName="Module_SortOrder" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_ModuleLocale">
            <EntityTypeMapping TypeName="AMMSModel.WF_ModuleLocale">
              <MappingFragment StoreEntitySet="GNM_ModuleLocale">
                <ScalarProperty Name="Module_IconName" ColumnName="Module_IconName" />
                <ScalarProperty Name="ModuleLocale_ID" ColumnName="ModuleLocale_ID" />
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="Module_ID" ColumnName="Module_ID" />
                <ScalarProperty Name="Module_Description" ColumnName="Module_Description" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Object">
            <EntityTypeMapping TypeName="AMMSModel.WF_Object">
              <MappingFragment StoreEntitySet="GNM_Object">
                <ScalarProperty Name="Object_ID" ColumnName="Object_ID" />
                <ScalarProperty Name="Object_Name" ColumnName="Object_Name" />
                <ScalarProperty Name="Read_Action" ColumnName="Read_Action" />
                <ScalarProperty Name="Create_Action" ColumnName="Create_Action" />
                <ScalarProperty Name="Update_Action" ColumnName="Update_Action" />
                <ScalarProperty Name="Delete_Action" ColumnName="Delete_Action" />
                <ScalarProperty Name="Export_Action" ColumnName="Export_Action" />
                <ScalarProperty Name="Print_Action" ColumnName="Print_Action" />
                <ScalarProperty Name="Object_IsActive" ColumnName="Object_IsActive" />
                <ScalarProperty Name="Object_Description" ColumnName="Object_Description" />
                <ScalarProperty Name="Import_Action" ColumnName="Import_Action" />
                <ScalarProperty Name="Object_Type" ColumnName="Object_Type" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_PartyBranchAssociation">
            <EntityTypeMapping TypeName="AMMSModel.WF_PartyBranchAssociation">
              <MappingFragment StoreEntitySet="GNM_PartyBranchAssociation">
                <ScalarProperty Name="PartyBranch_ID" ColumnName="PartyBranch_ID" />
                <ScalarProperty Name="Party_ID" ColumnName="Party_ID" />
                <ScalarProperty Name="Branch_ID" ColumnName="Branch_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_PrefixSuffix">
            <EntityTypeMapping TypeName="AMMSModel.WF_PrefixSuffix">
              <MappingFragment StoreEntitySet="GNM_PrefixSuffix">
                <ScalarProperty Name="PrefixSuffix_ID" ColumnName="PrefixSuffix_ID" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Branch_ID" ColumnName="Branch_ID" />
                <ScalarProperty Name="Object_ID" ColumnName="Object_ID" />
                <ScalarProperty Name="Start_Number" ColumnName="Start_Number" />
                <ScalarProperty Name="Prefix" ColumnName="Prefix" />
                <ScalarProperty Name="Suffix" ColumnName="Suffix" />
                <ScalarProperty Name="FromDate" ColumnName="FromDate" />
                <ScalarProperty Name="ToDate" ColumnName="ToDate" />
                <ScalarProperty Name="ModifiedBY" ColumnName="ModifiedBY" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="FinancialYear" ColumnName="FinancialYear" />
                <ScalarProperty Name="Company_FinancialYear_ID" ColumnName="Company_FinancialYear_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_RefMasterDetail">
            <EntityTypeMapping TypeName="AMMSModel.WF_RefMasterDetail">
              <MappingFragment StoreEntitySet="GNM_RefMasterDetail">
                <ScalarProperty Name="RefMasterDetail_IsDefault" ColumnName="RefMasterDetail_IsDefault" />
                <ScalarProperty Name="RefMasterDetail_ID" ColumnName="RefMasterDetail_ID" />
                <ScalarProperty Name="RefMasterDetail_IsActive" ColumnName="RefMasterDetail_IsActive" />
                <ScalarProperty Name="RefMasterDetail_Short_Name" ColumnName="RefMasterDetail_Short_Name" />
                <ScalarProperty Name="RefMasterDetail_Name" ColumnName="RefMasterDetail_Name" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="RefMaster_ID" ColumnName="RefMaster_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Role">
            <EntityTypeMapping TypeName="AMMSModel.WF_Role">
              <MappingFragment StoreEntitySet="GNM_Role">
                <ScalarProperty Name="Role_ID" ColumnName="Role_ID" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Role_Name" ColumnName="Role_Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_RoleObject">
            <EntityTypeMapping TypeName="AMMSModel.WF_RoleObject">
              <MappingFragment StoreEntitySet="GNM_RoleObject">
                <ScalarProperty Name="RoleObject_ID" ColumnName="RoleObject_ID" />
                <ScalarProperty Name="Role_ID" ColumnName="Role_ID" />
                <ScalarProperty Name="Object_ID" ColumnName="Object_ID" />
                <ScalarProperty Name="RoleObject_Create" ColumnName="RoleObject_Create" />
                <ScalarProperty Name="RoleObject_Read" ColumnName="RoleObject_Read" />
                <ScalarProperty Name="RoleObject_Update" ColumnName="RoleObject_Update" />
                <ScalarProperty Name="RoleObject_Delete" ColumnName="RoleObject_Delete" />
                <ScalarProperty Name="RoleObject_Print" ColumnName="RoleObject_Print" />
                <ScalarProperty Name="RoleObject_Export" ColumnName="RoleObject_Export" />
                <ScalarProperty Name="RoleObject_Import" ColumnName="RoleObject_Import" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Sms">
            <EntityTypeMapping TypeName="AMMSModel.WF_Sms">
              <MappingFragment StoreEntitySet="GNM_Sms">
                <ScalarProperty Name="Parameter4_value" ColumnName="Parameter4_value" />
                <ScalarProperty Name="Parameter3_value" ColumnName="Parameter3_value" />
                <ScalarProperty Name="Parameter2_value" ColumnName="Parameter2_value" />
                <ScalarProperty Name="Parameter1_value" ColumnName="Parameter1_value" />
                <ScalarProperty Name="Template_ID" ColumnName="Template_ID" />
                <ScalarProperty Name="Sms_ID" ColumnName="Sms_ID" />
                <ScalarProperty Name="Sms_Text" ColumnName="Sms_Text" />
                <ScalarProperty Name="Sms_Mobile_Number" ColumnName="Sms_Mobile_Number" />
                <ScalarProperty Name="Sms_Queue_Date" ColumnName="Sms_Queue_Date" />
                <ScalarProperty Name="Sms_Sent_Date" ColumnName="Sms_Sent_Date" />
                <ScalarProperty Name="Sms_SentStatus" ColumnName="Sms_SentStatus" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_User">
            <EntityTypeMapping TypeName="AMMSModel.WF_User">
              <MappingFragment StoreEntitySet="GNM_User">
                <ScalarProperty Name="ReleaseVersionPopup" ColumnName="ReleaseVersionPopup" />
                <ScalarProperty Name="WareHouse_ID" ColumnName="WareHouse_ID" />
                <ScalarProperty Name="User_IPAddress" ColumnName="User_IPAddress" />
                <ScalarProperty Name="LandingPage" ColumnName="LandingPage" />
                <ScalarProperty Name="User_ID" ColumnName="User_ID" />
                <ScalarProperty Name="User_Name" ColumnName="User_Name" />
                <ScalarProperty Name="User_LoginID" ColumnName="User_LoginID" />
                <ScalarProperty Name="User_Password" ColumnName="User_Password" />
                <ScalarProperty Name="User_IsActive" ColumnName="User_IsActive" />
                <ScalarProperty Name="User_Locked" ColumnName="User_Locked" />
                <ScalarProperty Name="User_LoginCount" ColumnName="User_LoginCount" />
                <ScalarProperty Name="User_FailedCount" ColumnName="User_FailedCount" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="User_Type_ID" ColumnName="User_Type_ID" />
                <ScalarProperty Name="Employee_ID" ColumnName="Employee_ID" />
                <ScalarProperty Name="Partner_ID" ColumnName="Partner_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_UserRole">
            <EntityTypeMapping TypeName="AMMSModel.WF_UserRole">
              <MappingFragment StoreEntitySet="GNM_UserRole">
                <ScalarProperty Name="UserRole_ID" ColumnName="UserRole_ID" />
                <ScalarProperty Name="User_ID" ColumnName="User_ID" />
                <ScalarProperty Name="Role_ID" ColumnName="Role_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_EmployeeBranch">
            <EntityTypeMapping TypeName="AMMSModel.WF_EmployeeBranch">
              <MappingFragment StoreEntitySet="GNM_EmployeeBranch">
                <ScalarProperty Name="IsDefault" ColumnName="IsDefault" />
                <ScalarProperty Name="Branch_ID" ColumnName="Branch_ID" />
                <ScalarProperty Name="CompanyEmployee_ID" ColumnName="CompanyEmployee_ID" />
                <ScalarProperty Name="EmployeeBranch_ID" ColumnName="EmployeeBranch_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_UserLocale">
            <EntityTypeMapping TypeName="AMMSModel.WF_UserLocale">
              <MappingFragment StoreEntitySet="GNM_UserLocale">
                <ScalarProperty Name="User_Name" ColumnName="User_Name" />
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="User_ID" ColumnName="User_ID" />
                <ScalarProperty Name="User_Locale_ID" ColumnName="User_Locale_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="True" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>