﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Rpt_CallLogController : ApiController
    {
        #region LoadBranchDropdown vinay n 
        /// <summary>
        /// LoadBranchDropdown
        /// </summary>
        /// <param name="LoadBranchDropdownWareHouseListObj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_Rpt_CallLog/InitialSetUp")]
        [HttpPost]
        [JwtTokenValidationFilter]

        public IHttpActionResult InitialSetUp([FromBody] InitialSetUpList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_Rpt_CallLogServices.InitialSetUp(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectCustomerFeedbackReport vinay n 22/11/24 :::
        /// <summary>
        /// SelectCustomerFeedbackReport
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/HelpDesk_Rpt_CallLog/Select")]
        [System.Web.Http.HttpPost]
        public IHttpActionResult Select([FromBody] SelectHelpDesk_Rpt_CallLogList Obj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_CallLogServices.Select(Obj, connstring, LogException, sidx, sord, page, rows, _search, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

        #region Export vinay
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_Rpt_CallLog/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportHelpDesk_Rpt_CallLogList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





            try
            {


                Object Response = await HelpDesk_Rpt_CallLogServices.Export(ExportObj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion
    }
}