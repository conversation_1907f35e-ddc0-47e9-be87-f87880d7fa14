﻿using Microsoft.AspNetCore.Mvc;
using PBC.Core_SharedAPIClass.Models;
using PBC.Core_SharedAPIClass.Services;
using PBC.CoreService.Services;

namespace PBC.Core_SharedAPIClass.Controllers
{

    [ApiController]
    [Route("api/[controller]")]
    public class CoreEmailTemplateController : Controller
    {
        private readonly ICoreEmailTemplateService _coreEmailTemplateService;
        private readonly ILogger<CoreEmailTemplateController> _logger;
        private readonly IConfiguration _configuration;

        public CoreEmailTemplateController(ICoreEmailTemplateService coreEmailTemplateService, ILogger<CoreEmailTemplateController> logger, IConfiguration configuration)
        {
            _coreEmailTemplateService = coreEmailTemplateService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Check if old password matches the stored password
        /// </summary>
        /// <param name="request">CommonMethodForEmailandSMS object</param>
        /// <returns>JsonResult</returns>
        /// <response code="200">Returns the password check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("Common-Method-ForEmailandSMS")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CommonMethodForEmailandSMS([FromBody] CommonMethodForEmailandSMSList request)
        {
            try
            {
                _logger.LogInformation("POST /api/CoreEmailTemplate/CommonMethodForEmailandSMS");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use connection string and log exception from gateway request
                var CommonMethodForEmailandSMSListData = new CommonMethodForEmailandSMSList
                {
                    TemplateCode = request.TemplateCode,
                    CompanyId = request.CompanyId,
                    LanguageCode = request.LanguageCode,
                    BranchId = request.BranchId,
                    p1 = request.p1,
                    p2 = request.p2,
                    p3 = request.p3,
                    p4 = request.p4,
                    p5 = request.p5,
                    p6 = request.p6,
                    p7 = request.p7,
                    p8 = request.p8,
                    p9 = request.p9,
                    p10 = request.p10,
                    p11 = request.p11,
                    p12 = request.p12,
                    p13 = request.p13,
                    p14 = request.p14,
                    p15 = request.p15,
                    p16 = request.p16,
                    p17 = request.p17,
                    p18 = request.p18,
                    p19 = request.p19,
                    p20 = request.p20
                };

                var result = await _coreEmailTemplateService.CommonMethodForEmailandSMS(CommonMethodForEmailandSMSListData, request.ConnString, request.LoggedINDateTime);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking old password");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking old password");
            }
        }

    }
}
