﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_CR_ResolutionTimeWithTimeSlotsController : ApiController
    {
        #region GetData vinay n 3/12/24
        /// <summary>
        /// GetData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetData")]
        [HttpPost]
        [JwtTokenValidationFilter]

        public IHttpActionResult GetData([FromBody] GetDataHelpDesk_CR_ResolutionTimeWithTimeSlotsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetYearData vinay n 3/12/24
        /// <summary>
        /// GetYearData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetYearData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetYearData([FromBody] GetYearDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetYearData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetYearWiseBarChartData vinay n 3/12/24
        /// <summary>
        /// GetYearData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetYearWiseBarChartData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetYearWiseBarChartData([FromBody] GetYearWiseBarChartDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetYearWiseBarChartData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetMonthData vinay n 3/12/24
        /// <summary>
        /// GetMonthData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetMonthData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetMonthData([FromBody] GetMonthDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetMonthData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetDatewiseData vinay n 3/12/24
        /// <summary>
        /// GetDatewiseData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetDatewiseData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetDatewiseData([FromBody] GetDatewiseDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetDatewiseData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetMonthWiseBarChartData vinay n 3/12/24
        /// <summary>
        /// GetMonthWiseBarChartData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetMonthWiseBarChartData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetMonthWiseBarChartData([FromBody] GetMonthWiseBarChartDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetMonthWiseBarChartData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetDateWiseBarChartData vinay n 3/12/24
        /// <summary>
        /// GetDateWiseBarChartData
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetDateWiseBarChartData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetDateWiseBarChartData([FromBody] GetDateWiseBarChartDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetDateWiseBarChartData(Obj, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Export vinay n 3/12/24
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportHelpDesk_CR_ResolutionTimeWithTimeSlotsList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));


            try
            {

                Response = await HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.Export(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response);
        }
        #endregion
        #region LoadYearGrid vinay n  3/12/24
        /// <summary>
        /// LoadYearGrid
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>   
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/LoadYearGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public void LoadYearGrid(LoadYearGridList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                //Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.LoadYearGrid(Obj, sidx,sord, page, rows);
                HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.LoadYearGrid(Obj, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            // return Ok(Response.Value);
        }
        #endregion
        #region LoadMonthGrid vinay n  3/12/24
        /// <summary>
        /// LoadMonthGrid
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>   
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/LoadMonthGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public void LoadMonthGrid(LoadMonthGridList Obj)
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                //Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.LoadYearGrid(Obj, sidx,sord, page, rows);
                HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.LoadMonthGrid(Obj, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            // return Ok(Response.Value);
        }
        #endregion
        #region LoadGrid vinay n  3/12/24
        /// <summary>
        /// LoadGrid
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>   
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/LoadGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadGrid()
        {
            var Response = default(dynamic);

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                //Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.LoadYearGrid(Obj, sidx,sord, page, rows);
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.LoadGrid(sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region GetTimeSlots vinay n 3/12/24
        /// <summary>
        /// GetTimeSlots
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetTimeSlots")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTimeSlots([FromBody] GetTimeSlotsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetTimeSlots(Obj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetTimeSlotsYearWise vinay n 3/12/24
        /// <summary>
        /// GetTimeSlots
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetTimeSlotsYearWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTimeSlotsYearWise([FromBody] GetTimeSlotsYearWiseList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetTimeSlotsYearWise(Obj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetTimeSlotsMonthWise vinay n 3/12/24
        /// <summary>
        /// GetTimeSlots
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetTimeSlotsMonthWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTimeSlotsMonthWise([FromBody] GetTimeSlotsMonthWiseList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetTimeSlotsMonthWise(Obj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region GetTimeSlotsDateWise vinay n 3/12/24
        /// <summary>
        /// GetTimeSlotsDateWise
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDesk_CR_ResolutionTimeWithTimeSlots/GetTimeSlotsDateWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTimeSlotsDateWise([FromBody] GetTimeSlotsDateWiseList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.GetTimeSlotsDateWise(Obj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}