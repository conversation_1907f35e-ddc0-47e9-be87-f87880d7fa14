﻿using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Services;
using PBC.HelpdeskService.Services;

namespace PBC.HelpdeskService.Controllers
{

    /// <summary>
    /// Controller for Help Desk Request Page operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HelpDeskServiceRequestController : Controller
    {
        private readonly IHelpDeskUserLandingPageServices _helpDeskUserLandingPageServices;
        private readonly IConfiguration _configuration;
        private readonly ILogger<HelpDeskServiceRequestController> _logger;

        public HelpDeskServiceRequestController(
            IHelpDeskUserLandingPageServices helpDeskUserLandingPageServices,
            IConfiguration configuration,
            ILogger<HelpDeskServiceRequestController> logger)
        {
            _helpDeskUserLandingPageServices = helpDeskUserLandingPageServices;
            _configuration = configuration;
            _logger = logger;
        }





    }
}
