using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Utilities;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for LogSheetExporter operations
    /// Provides HTTP endpoints for logging functionality
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class LogSheetExporterController : ControllerBase
    {
        private readonly ILogger<LogSheetExporterController> _logger;

        public LogSheetExporterController(ILogger<LogSheetExporterController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Logs an error to the text file using LogSheetExporter
        /// </summary>
        /// <param name="request">The log request containing error details</param>
        /// <returns>Success status</returns>
        /// <response code="200">Log entry created successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("log")]
        [ProducesResponseType(typeof(LogResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public ActionResult<LogResponse> LogToTextFile([FromBody] LogRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/logsheetexporter/log - Logging error with ID: {ExId}", request.ExId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Call the static LogSheetExporter method
                LogSheetExporter.LogToTextFile(request.ExId, request.ExMessage, request.ExDetails, request.ExStackTrace);

                return Ok(new LogResponse
                {
                    Success = true,
                    Message = "Log entry created successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging to text file");
                return StatusCode(StatusCodes.Status500InternalServerError, new LogResponse
                {
                    Success = false,
                    Message = "An error occurred while logging"
                });
            }
        }
    }
}
