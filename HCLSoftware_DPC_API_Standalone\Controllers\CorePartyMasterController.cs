﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CorePartyMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CorePartyMasterController : ApiController
    {

        #region :::SelectPartyMaster   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectPartyMaster 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyMaster([FromBody] SelectPartyMasterList SelectPartyMasterobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"] ?? "Party_Name";
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"] ?? "10");
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"] ?? "1");
            string sord = HttpContext.Current.Request.Params["sord"] ?? "asc";
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyMaster(connstring, SelectPartyMasterobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To Export
        /// </summary> 
        /// 
        [Route("api/CorePartyMaster/PartyExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> PartyExport([FromBody] PartyExportList PartyExportobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = PartyExportobj.sidx;
            string sord = PartyExportobj.sord;
            string filter = PartyExportobj.filter;
            string advnceFilter = PartyExportobj.advanceFilter;

            try
            {


                object Response = await CorePartyMasterServices.PartyExport(PartyExportobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion



        #region :::SelectMasters   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectMasters 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectMasters")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMasters([FromBody] SelectMastersCorePartyMasterList SelectMastersCorePartyMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectMasters(connString, SelectMastersCorePartyMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectMastersLocale   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectMastersLocale 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectMastersLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMastersLocale([FromBody] SelectMastersLocaleCorePartyMasterList SelectMastersLocaleCorePartyMasterListobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectMastersLocale(connString, SelectMastersLocaleCorePartyMasterListobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectProductType   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectProductType 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductType([FromBody] SelectProductTypeLista SelectProductTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectProductType(connString, SelectProductTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectModel   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectModel 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] SelectModelTypeList SelectModelTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectModel(connString, SelectModelTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectSecondarySegment   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectSecondarySegment 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectSecondarySegment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSecondarySegment([FromBody] SelectSecondarySegmentlist SelectSecondarySegmentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int prtyID = Convert.ToInt32(HttpContext.Current.Request.Params["partyId"]);
                int segID = Convert.ToInt32(HttpContext.Current.Request.Params["segId"]);
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectSecondarySegment(connString, SelectSecondarySegmentobj, prtyID, segID);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectPartyMasterLocale   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectPartyMasterLocale 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyMasterLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyMasterLocale([FromBody] SelectPartyMasterLocaleList SelectPartyMasterLocaleobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"] ?? "Party_Name";
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"] ?? "10");
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"] ?? "1");
            string sord = HttpContext.Current.Request.Params["sord"] ?? "asc";
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyMasterLocale(connstring, SelectPartyMasterLocaleobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectParticularParty   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectParticularParty 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectParticularParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularParty([FromBody] SelectParticularPartyList SelectParticularPartyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectParticularParty(connString, SelectParticularPartyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectBranchTaxDetails Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// to select all the branch tax details
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyTaxStructureDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyTaxStructureDetails([FromBody] SelectPartyTaxStructureDetailsList SelectPartyTaxStructureDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"] ?? "PartyTaxStructure_ID";  // Default to "PartyTaxStructure_ID" if sidx is null
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"] ?? "20");  // Default to 20 if rows is null
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"] ?? "1");  // Default to 1 if page is null
            string sord = HttpContext.Current.Request.Params["sord"] ?? "asc";  // Default to "asc" if sord is null
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyTaxStructureDetails(connstring, SelectPartyTaxStructureDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SavePartyTaxStructureDetail Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// to update branch tax 
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyTaxStructureDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyTaxStructureDetail([FromBody] SavePartyTaxStructureDetailList SavePartyTaxStructureDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyTaxStructureDetail(connString, SavePartyTaxStructureDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeletePartyTaxStructureDetails Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to Delete the Branch tax details
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyTaxStructureDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyTaxStructureDetails([FromBody] DeletePartyTaxStructureDetailsList DeletePartyTaxStructureDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyTaxStructureDetails(connString, DeletePartyTaxStructureDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckBranchTaxDetails  Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to check if the Tax detail has already been selected for the branch
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/CheckBranchTaxDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckBranchTaxDetails([FromBody] CheckBranchTaxDetailsList CheckBranchTaxDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.CheckBranchTaxDetails(connString, CheckBranchTaxDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartyContactDetails Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ////to Select Particular Contact Details Party based on ID Passed
        /// </summary>   
        /// 

        [Route("api/CorePartyMaster/SelectPartyContactDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyContactDetails([FromBody] SelectPartyContactDetailsList SelectPartyContactDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyContactDetails(connstring, SelectPartyContactDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartyTaxDetails Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ///SelectPartyTaxDetails
        ///
        [Route("api/CorePartyMaster/SelectPartyTaxDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyTaxDetails([FromBody] SelectPartyTaxDetailsList SelectPartyTaxDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyTaxDetails(connstring, SelectPartyTaxDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyMasterData Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ///PartyMasterData
        /// </summary> 
        /// 
        [Route("api/CorePartyMaster/PartyMasterData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyMasterData([FromBody] PartyMasterDataList PartyMasterDataobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyMasterData(connString, PartyMasterDataobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyMasterDataLocale Uday Kumar J B 21-08-2024 Ajax not Found:::
        /// <summary>
        ///PartyMasterDataLocale
        /// </summary>   
        /// 
        [Route("api/CorePartyMaster/PartyMasterDataLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyMasterDataLocale()
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyMasterDataLocale(connString);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::StateMasterData Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///StateMasterData
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/StateMasterData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult StateMasterData([FromBody] StateMasterDataList StateMasterDataobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.StateMasterData(connString, StateMasterDataobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::StateMasterDataLocale Uday Kumar J B 21-08-2024 Ajax not Found :::
        /// <summary>
        ///StateMasterData
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/StateMasterDataLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult StateMasterDataLocale([FromBody] StateMasterDataLocaleList StateMasterDataLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.StateMasterDataLocale(connString, StateMasterDataLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyMasterSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save the Party Master
        /// </summary> 
        /// 
        [Route("api/CorePartyMaster/PartyMasterSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyMasterSave([FromBody] PartyMasterSaveList PartyMasterSaveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyMasterSave(connString, PartyMasterSaveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyMasterSaveLocale Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save the Party Master
        /// </summary>   
        ///  
        [Route("api/CorePartyMaster/PartyMasterSaveLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyMasterSaveLocale([FromBody] PartyMasterSaveLocaleList PartyMasterSaveLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyMasterSaveLocale(connString, PartyMasterSaveLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyContactPersonSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save Party Contact Person Details
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyContactPersonSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyContactPersonSave([FromBody] PartyContactPersonSaveList PartyContactPersonSaveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyContactPersonSave(connString, PartyContactPersonSaveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyTaxSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save Party Tax
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyTaxSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyTaxSave([FromBody] PartyTaxSaveList PartyTaxSaveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyTaxSave(connString, PartyTaxSaveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartsMasterDelete Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///to delete PartsMaster row
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyMasterDelete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyMasterDelete([FromBody] PartyMasterDeletelist PartyMasterDeleteobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyMasterDelete(connString, PartyMasterDeleteobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeletePartyContactDetail Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///to delete PartsMaster row
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyContactDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyContactDetail([FromBody] DeletePartyContactDetailList DeletePartyContactDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyContactDetail(connString, DeletePartyContactDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeletePartyTaxDetail Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///to delete PartsMaster row
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyTaxDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyTaxDetail([FromBody] DeletePartyTaxDetailList DeletePartyTaxDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyTaxDetail(connString, DeletePartyTaxDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyTaxDiscountSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save Party Tax
        /// </summary> 
        /// 
        [Route("api/CorePartyMaster/PartyTaxDiscountSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyTaxDiscountSave([FromBody] PartyTaxDiscountSaveList PartyTaxDiscountSaveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyTaxDiscountSave(connString, PartyTaxDiscountSaveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartyTaxDiscountDetails Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ///SelectPartyTaxDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyTaxDiscountDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyTaxDiscountDetails([FromBody] SelectPartyTaxDiscountDetailsList SelectPartyTaxDiscountDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyTaxDiscountDetails(connstring, SelectPartyTaxDiscountDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeletePartyTaxDiscount Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to Delete the Branch Tax Codes
        /// </summary>  
        /// 
        [Route("api/CorePartyMaster/DeletePartyTaxDiscount")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyTaxDiscount([FromBody] DeletePartyTaxDiscountList DeletePartyTaxDiscountobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyTaxDiscount(connString, DeletePartyTaxDiscountobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: PartySegmentDetails Uday Kumar J B 21-08-2024  :::
        /// <summary>
        ///PartySegmentDetails  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartySegmentDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartySegmentDetail([FromBody] SelectPartySegmentDetailList SelectPartySegmentDetailobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartySegmentDetail(connstring, SelectPartySegmentDetailobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region::: PartySegmentSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartySegmentSave  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartySegmentSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartySegmentSave([FromBody] PartySegmentSaveList PartySegmentSaveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartySegmentSave(connString, PartySegmentSaveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: PartySegmentDelete Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartySegmentDelete  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartySegmentDelete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartySegmentDelete([FromBody] PartySegmentDeleteList PartySegmentDeleteobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartySegmentDelete(connString, PartySegmentDeleteobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: SelectPartySkillSet Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ///SelectPartySkillSet  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartySkillSet")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartySkillSet([FromBody] SelectPartySkillSetList SelectPartySkillSetobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartySkillSet(connstring, SelectPartySkillSetobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region::: SavePartySkillSet Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SavePartySkillSet  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartySkillSet")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartySkillSet([FromBody] SavePartySkillSetList SavePartySkillSetobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartySkillSet(connString, SavePartySkillSetobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: SavePartyContractDtl Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SavePartyContractDtl  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyContractDtl")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyContractDtl([FromBody] SavePartyContractDtlList SavePartyContractDtlobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyContractDtl(connString, SavePartyContractDtlobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: DeletePartySkillSet Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///DeletePartySkillSet  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartySkillSet")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartySkillSet([FromBody] DeletePartySkillSetList DeletePartySkillSetobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartySkillSet(connString, DeletePartySkillSetobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: DeletePartyContractDtl Uday Kumar J B 22-08-2024:::
        /// <summary>
        ///DeletePartyContractDtl  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyContractDtl")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyContractDtl([FromBody] DeletePartyContractDtlList DeletePartyContractDtlobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyContractDtl(connString, DeletePartyContractDtlobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: SelectPartyBranch Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelectPartyBranch  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyBranch([FromBody] SelectPartyBranchList SelectPartyBranchobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];



            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyBranch(connstring, SelectPartyBranchobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region::: SavePartyBranch Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyBranch  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyBranch([FromBody] SavePartyBranchList SavePartyBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyBranch(connString, SavePartyBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: DeletePartyBranch Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyBranch  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyBranch([FromBody] DeletePartyBranchList DeletePartyBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyBranch(connString, DeletePartyBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: SelectPartyProductAssociation Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelectPartyProductAssociation  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyProductAssociation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyProductAssociation([FromBody] SelectPartyProductAssociationList SelectPartyProductAssociationobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyProductAssociation(connstring, SelectPartyProductAssociationobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region::: SelectPartyProductAssociation Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyProductAssociation  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyProductAssociation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyProductAssociation([FromBody] SavePartyProductAssociationList SavePartyProductAssociationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyProductAssociation(connString, SavePartyProductAssociationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: DeletePartyProductAssociation Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyProductAssociation  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyProductAssociation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyProductAssociation([FromBody] DeletePartyProductAssociationList DeletePartyProductAssociationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyProductAssociation(connString, DeletePartyProductAssociationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: SelectPartyServiceSchedule Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelectPartyServiceSchedule  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyServiceSchedule")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyServiceSchedule([FromBody] SelectPartyServiceScheduleList SelectPartyServiceScheduleobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyServiceSchedule(connstring, SelectPartyServiceScheduleobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region::: SavePartyServiceSchedule Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyServiceSchedule  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyServiceSchedule")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyServiceSchedule([FromBody] SavePartyServiceScheduleList SavePartyServiceScheduleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyServiceSchedule(connString, SavePartyServiceScheduleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: DeletePartyServiceSchedule Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyServiceSchedule  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyServiceSchedule")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyServiceSchedule([FromBody] DeletePartyServiceScheduleList DeletePartyServiceScheduleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyServiceSchedule(connString, DeletePartyServiceScheduleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: SelectPartyProductDetails Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelectPartyProductDetails  
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyProductDetails([FromBody] SelectPartyProductDetailsList SelectPartyProductDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyProductDetails(connstring, SelectPartyProductDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelLockLogDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// to select Lock LogDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelLockLogDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelLockLogDetails([FromBody] SelLockLogDetailsList SelLockLogDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelLockLogDetails(connstring, SelLockLogDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelCreditLimitLogDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelCreditLimitLogDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelCreditLimitLogDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelCreditLimitLogDetails([FromBody] SelCreditLimitLogDetailsList SelCreditLimitLogDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelCreditLimitLogDetails(connstring, SelCreditLimitLogDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelPartyContractDetails Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelPartyContractDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelPartyContractDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelPartyContractDetails([FromBody] SelPartyContractDetailsList SelPartyContractDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelPartyContractDetails(connstring, SelPartyContractDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectFieldSearchJobCard Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelectFieldSearchJobCard
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectFieldSearchJobCard")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchJobCard([FromBody] SelectFieldSearchJobCardList SelectFieldSearchJobCardobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectFieldSearchJobCard(connString, SelectFieldSearchJobCardobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartySegmentExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartySegmentExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartySegmentExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartySegmentExits([FromBody] PartySegmentExitsList PartySegmentExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartySegmentExits(connString, PartySegmentExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyProdExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyProdExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyProdExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyProdExits([FromBody] PartyProdExitsList PartyProdExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyProdExits(connString, PartyProdExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartySkillSetExits Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// PartySkillSetExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartySkillSetExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartySkillSetExits([FromBody] PartySkillSetExitsList PartySkillSetExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartySkillSetExits(connString, PartySkillSetExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyAgreementNumberExits Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// PartyAgreementNumberExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyAgreementNumberExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyAgreementNumberExits([FromBody] PartyAgreementNumberExitsList PartyAgreementNumberExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyAgreementNumberExits(connString, PartyAgreementNumberExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyBranchExits Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// PartyBranchExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyBranchExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyBranchExits([FromBody] PartyBranchExitsList PartyBranchExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyBranchExits(connString, PartyBranchExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyPartyTax_TaxCodeExits Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// PartyPartyTax_TaxCodeExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyPartyTax_TaxCodeExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyPartyTax_TaxCodeExits([FromBody] PartyPartyTax_TaxCodeExitsList PartyPartyTax_TaxCodeExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyPartyTax_TaxCodeExits(connString, PartyPartyTax_TaxCodeExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyServiceExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyServiceExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyServiceExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyServiceExits([FromBody] PartyServiceExitsList PartyServiceExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyServiceExits(connString, PartyServiceExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyServiceExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyServiceExits
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/CheckPartyExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartyExists([FromBody] CheckPartyExistsList CheckPartyExistsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.CheckPartyExists(connString, CheckPartyExistsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartyLocaleExists Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// CheckPartyLocaleExists
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/CheckPartyLocaleExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartyLocaleExists([FromBody] CheckPartyLocaleExistsList CheckPartyLocaleExistsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.CheckPartyLocaleExists(connString, CheckPartyLocaleExistsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCompany Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectCompany
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompany([FromBody] SelectCompanyList SelectCompanyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectCompany(connString, SelectCompanyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParentCompany Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectParentCompany
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectParentCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParentCompany([FromBody] SelectParentCompanyList SelectParentCompanyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectParentCompany(connString, SelectParentCompanyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectDealers Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectDealers
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectDealers")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDealers([FromBody] SelectDealersList SelectDealersobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectDealers(connString, SelectDealersobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectAllConsignee Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectAllConsignee
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectAllConsignee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllConsignee([FromBody] SelectAllConsigneeList SelectAllConsigneeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectAllConsignee(connString, SelectAllConsigneeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectAllConsignee Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectAllConsignee
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyAddressdetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyAddressdetails([FromBody] SavePartyAddressdetailsList SavePartyAddressdetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyAddressdetails(connString, SavePartyAddressdetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectAllCountry Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectAllCountry
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectAllCountry")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllCountry()
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectAllCountry(connString);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectState Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectState
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectState")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectState([FromBody] SelectStateList SelectStateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectState(connString, SelectStateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllPartyAddress Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelAllPartyAddress
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelAllPartyAddress")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllPartyAddress([FromBody] SelAllPartyAddressList SelAllPartyAddressobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelAllPartyAddress(connstring, SelAllPartyAddressobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectState Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectState
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectSpecificPartyAddressDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificPartyAddressDetails([FromBody] SelectSpecificPartyAddressDetailsList SelectSpecificPartyAddressDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectSpecificPartyAddressDetails(connString, SelectSpecificPartyAddressDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllPartyAddressLocale Uday Kumar J B 22-08-2024  :::
        /// <summary>
        /// SelAllPartyAddressLocale
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelAllPartyAddressLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllPartyAddressLocale([FromBody] SelAllPartyAddressLocaleList SelAllPartyAddressLocaleobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelAllPartyAddressLocale(connstring, SelAllPartyAddressLocaleobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSpecificPartyAddressLocaleDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectSpecificPartyAddressLocaleDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectSpecificPartyAddressLocaleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificPartyAddressLocaleDetails([FromBody] SelectSpecificPartyAddressLocaleDetailsList SelectSpecificPartyAddressLocaleDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectSpecificPartyAddressLocaleDetails(connString, SelectSpecificPartyAddressLocaleDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSpecificPartyContactLocaleDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectSpecificPartyContactLocaleDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectSpecificPartyContactLocaleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificPartyContactLocaleDetails([FromBody] SelectSpecificPartyContactLocaleDetailsList SelectSpecificPartyContactLocaleDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectSpecificPartyContactLocaleDetails(connString, SelectSpecificPartyContactLocaleDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SavePartyLocaleAddressdetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyLocaleAddressdetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyLocaleAddressdetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyLocaleAddressdetails([FromBody] SavePartyLocaleAddressdetailsList SavePartyLocaleAddressdetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyLocaleAddressdetails(connString, SavePartyLocaleAddressdetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SavePartyLocaleContactdetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyLocaleContactdetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SavePartyLocaleContactdetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartyLocaleContactdetails([FromBody] SavePartyLocaleContactdetailsList SavePartyLocaleContactdetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SavePartyLocaleContactdetails(connString, SavePartyLocaleContactdetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeletePartyAddressDetail Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyAddressDetail
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/DeletePartyAddressDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartyAddressDetail([FromBody] DeletePartyAddressDetailList DeletePartyAddressDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.DeletePartyAddressDetail(connString, DeletePartyAddressDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCurrency Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectCurrency
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectCurrency")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCurrency([FromBody] SelectCurrencyList SelectCurrencyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectCurrency(connString, SelectCurrencyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectLanguage Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectLanguage
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectLanguage")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectLanguage([FromBody] SelectLanguageList SelectLanguageobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectLanguage(connString, SelectLanguageobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckContactPerson Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// CheckContactPerson
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/CheckContactPerson")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckContactPerson([FromBody] CheckContactPersonList CheckContactPersonobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.CheckContactPerson(connString, CheckContactPersonobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: getContract Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// getContract
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/getContract")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getContract([FromBody] getContractList getContractobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.getContract(connString, getContractobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSpecificPartyContactDetails Uday Kumar J B 22-08-2024 Ajax not Found :::
        /// <summary>
        /// SelectSpecificPartyContactDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectSpecificPartyContactDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificPartyContactDetails([FromBody] SelectSpecificPartyContactDetailsList SelectSpecificPartyContactDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.SelectSpecificPartyContactDetails(connString, SelectSpecificPartyContactDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateContactPersonMaster Uday Kumar J B 22-08-2024 Ajax not Found:::
        /// <summary>
        /// UpdateContactPersonMaster
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/UpdateContactPersonMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateContactPersonMaster([FromBody] UpdateContactPersonMasterList UpdateContactPersonMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.UpdateContactPersonMaster(connString, UpdateContactPersonMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartyCreditDetails Uday Kumar J B 22-08-2024 :::
        /// <summary>
        /// SelectPartyCreditDetails
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/SelectPartyCreditDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyCreditDetails([FromBody] SelectPartyCreditDetailsList SelectPartyCreditDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartyMasterServices.SelectPartyCreditDetails(connstring, SelectPartyCreditDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyCreditSave Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyCreditSave
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyCreditSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyCreditSave([FromBody] PartyCreditSaveList PartyCreditSaveobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyCreditSave(connString, PartyCreditSaveobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: PartyCreditDelete Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyCreditDelete
        /// </summary>
        /// 
        [Route("api/CorePartyMaster/PartyCreditDelete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyCreditDelete([FromBody] PartyCreditDeleteList PartyCreditDeleteonbj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePartyMasterServices.PartyCreditDelete(connString, PartyCreditDeleteonbj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}


