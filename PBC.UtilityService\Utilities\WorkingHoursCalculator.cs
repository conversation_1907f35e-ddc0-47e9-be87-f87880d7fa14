using PBC.UtilityService.Utilities.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace PBC.UtilityService.Utilities
{
    /// <summary>
    /// Helper class for complex working hours calculations
    /// </summary>
    public static class WorkingHoursCalculator
    {
        #region getWorkingHoursForNonNightShift vinay n
        public static double getWorkingHoursForNonNightShift(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            string s = string.Empty;
            try
            {
                DateTime? ActualCallDate = Calldate;
                CallDateDetail detailForCallDate = new CallDateDetail();
                detailForCallDate = CommonFunctionalities.GetDetailsForDate(Calldate, companyID, connString, LogException);
                double final = detailForCallDate.WorkHours;
                var startTime = detailForCallDate.startTime;
                var endTime = detailForCallDate.endTime;
                double STM = detailForCallDate.startTime.TotalMinutes;
                double ETM = detailForCallDate.endTime.TotalMinutes;
                var BreakstartTime = detailForCallDate.BreakstartTime;
                var BreakEndTime = detailForCallDate.BreakEndTime;
                double BSTM = detailForCallDate.BreakstartTime.TotalMinutes;
                double BETM = detailForCallDate.BreakEndTime.TotalMinutes;
                double TotalBreakTime = BETM - BSTM;
                double timeFromCallDate = 0.0;
                double timeFromToday = 0.0;

                if (Calldate.Value.Date == DateTime.Now.Date && Calldate.Value.TimeOfDay < endTime && DateTime.Now.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
                {
                    if (DateTime.Now.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakstartTime && DateTime.Now.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = BSTM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - BETM);
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes);
                    }
                }
                else if (Calldate.Value.Date == DateTime.Now.Date && Calldate.Value.TimeOfDay < endTime && DateTime.Now.TimeOfDay > endTime && Calldate.Value.TimeOfDay > startTime)
                {
                    if (Calldate.Value.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                    }
                    else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = (ETM - BETM);
                    }
                    else if (Calldate.Value.TimeOfDay > BreakEndTime)
                    {
                        timeFromToday = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                }
                else if (Calldate.Value.Date == DateTime.Now.Date && DateTime.Now.TimeOfDay < endTime && Calldate.Value.TimeOfDay < startTime)
                {
                    if (DateTime.Now.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - STM;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakstartTime && DateTime.Now.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = BSTM - STM;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - STM) - (BETM - BSTM);
                    }
                }
                else if (Calldate.Value.Date == DateTime.Now.Date && DateTime.Now.TimeOfDay > endTime && Calldate.Value.TimeOfDay < startTime)
                {
                    timeFromToday = final;
                }
                if (Calldate.Value.Date < DateTime.Now.Date)
                {
                    if (Calldate.Value.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
                    {
                        if (Calldate.Value.TimeOfDay < BreakstartTime)
                        {
                            timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                        }
                        else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                        {
                            timeFromCallDate = ETM - BETM;
                        }
                        else if (Calldate.Value.TimeOfDay > BreakEndTime)
                        {
                            timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                        }
                        Calldate = Calldate.Value.AddDays(1);
                    }
                    else if (Calldate.Value.TimeOfDay < startTime)
                    {
                        timeFromCallDate = final;
                        Calldate = Calldate.Value.AddDays(1);
                    }
                    else if (Calldate.Value.TimeOfDay > endTime)
                    {
                        Calldate = Calldate.Value.AddDays(1);
                    }
                }
                double TotalWorkingHours = 0.0;
                int yearChng = Calldate.Value.Year;
                CallDateDetail detail = new CallDateDetail();
                detail = CommonFunctionalities.GetDetailsForDate(Calldate, companyID, connString, LogException);
                while (Calldate.Value.Date < DateTime.Now.Date)
                {
                    string day = Calldate.Value.DayOfWeek.ToString();
                    int year = Calldate.Value.Year;
                    if (Calldate.Value.Year != yearChng)
                    {
                        detail = CommonFunctionalities.GetDetailsForDate(Calldate, companyID, connString, LogException);
                    }
                    if (detail.WorkDays.Contains(day))
                    {
                        TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                    }
                    Calldate = Calldate.Value.AddDays(1);
                    yearChng = Calldate.Value.Year;
                }
                CallDateDetail todaysDetail = new CallDateDetail();
                todaysDetail = CommonFunctionalities.GetDetailsForDate(DateTime.Now, companyID, connString, LogException);
                if (DateTime.Now.TimeOfDay > todaysDetail.startTime && DateTime.Now.TimeOfDay < todaysDetail.endTime && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    if (DateTime.Now.TimeOfDay < todaysDetail.BreakstartTime)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > todaysDetail.BreakstartTime && DateTime.Now.TimeOfDay < todaysDetail.BreakEndTime)
                    {
                        timeFromToday = todaysDetail.BreakstartTime.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > todaysDetail.BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes) - (todaysDetail.BreakEndTime.TotalMinutes - todaysDetail.BreakstartTime.TotalMinutes);
                    }
                }
                if (DateTime.Now.TimeOfDay > todaysDetail.endTime && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    timeFromToday = todaysDetail.WorkHours;
                }

                double HDtime = 0.0;
                int holidayYear = 0;
                double holidayWhours = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT b.CompanyCalenderHoliday_Date
                        FROM GNM_CompanyCalender a
                        INNER JOIN GNM_CompanyCalenderHolidays b ON a.CompanyCalender_ID = b.CompanyCalender_ID
                        WHERE a.Company_ID = @CompanyID 
                        AND b.CompanyCalenderHoliday_Date >= @ActualCallDate
                        AND b.CompanyCalenderHoliday_Date <= @EndDate
                        AND a.IsGeneralShift = 1
                    ";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ActualCallDate", ActualCallDate);
                            cmd.Parameters.AddWithValue("@EndDate", DateTime.Now);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    CallDateDetail holidayDetail = new CallDateDetail();
                                    var holidayDate = reader.GetDateTime(reader.GetOrdinal("CompanyCalenderHoliday_Date"));
                                    if (holidayYear == 0 || holidayDate.Year != holidayYear)
                                    {
                                        holidayDetail = CommonFunctionalities.GetDetailsForDate(holidayDate, companyID, connString, LogException);
                                    }
                                    holidayWhours = holidayDetail.WorkHours;
                                    HDtime = HDtime + holidayWhours;
                                    holidayYear = holidayDate.Year;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        cmd?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                s = Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) / 60).ToString() + "." + ((Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString().Length == 1 ? "0" + Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString() : Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString()).ToString());
            }
            catch (Exception e) { }
            return Convert.ToDouble(s);
        }
        #endregion

        #region getWorkingHoursForNightShift vinay n
        public static double getWorkingHoursForNightShift(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            string s = string.Empty;
            try
            {
                DateTime? ActualCallDate = Calldate;
                CallDateDetail detailForCallDate = new CallDateDetail();
                detailForCallDate = CommonFunctionalities.GetDetailsForDate(Calldate, companyID, connString, LogException);
                double final = detailForCallDate.WorkHours;
                var startTime = detailForCallDate.startTime;
                var endTime = detailForCallDate.endTime;
                double STM = detailForCallDate.startTimeMinutes;
                double ETM = detailForCallDate.endTimeMinutes;
                var BreakstartTime = detailForCallDate.BreakstartTime;
                var BreakEndTime = detailForCallDate.BreakEndTime;
                double BSTM = detailForCallDate.BreakstartTimeMinutes;
                double BETM = detailForCallDate.BreakEndTimeMinute;
                double TotalBreakTime = BETM - BSTM;
                double timeFromCallDate = 0.0;
                double timeFromToday = 0.0;

                if (Calldate.Value.Date == DateTime.Now.Date)
                {
                    if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (DateTime.Now.TimeOfDay.TotalMinutes + 1440) < ETM)
                    {
                        if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BSTM)
                        {
                            timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                        }
                        else if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BETM)
                        {
                            timeFromToday = ((DateTime.Now.TimeOfDay.TotalMinutes + 1440) - BETM);
                        }
                        else if (Calldate.Value.TimeOfDay.TotalMinutes + 1440 > BETM)
                        {
                            timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                        }
                    }
                    else if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (DateTime.Now.TimeOfDay.TotalMinutes + 1440) > ETM)
                    {
                        timeFromToday = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                    }
                    else if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes) < STM && (DateTime.Now.TimeOfDay.TotalMinutes) > STM)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - STM;
                    }
                    else if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes) > STM && (DateTime.Now.TimeOfDay.TotalMinutes) > STM)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                }

                if (Calldate.Value.Date < DateTime.Now.Date)
                {
                    if (Calldate.Value.TimeOfDay.TotalMinutes < ETM && (Calldate.Value.TimeOfDay.TotalMinutes) > STM)
                    {
                        if (Calldate.Value.TimeOfDay.TotalMinutes < BSTM)
                        {
                            timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                        }
                        else if (Calldate.Value.TimeOfDay.TotalMinutes > BSTM && Calldate.Value.TimeOfDay.TotalMinutes < BETM)
                        {
                            timeFromCallDate = ETM - BETM;
                        }
                        else if (Calldate.Value.TimeOfDay.TotalMinutes > BETM)
                        {
                            timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                        }
                        Calldate = Calldate.Value.AddDays(1);
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes < STM)
                    {
                        if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM)
                        {
                            if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BSTM)
                            {
                                timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                            }
                            else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BETM)
                            {
                                timeFromCallDate = ETM - (BETM);
                            }
                            else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BETM)
                            {
                                timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                            }
                        }
                        else
                        {
                            timeFromCallDate = final;
                            Calldate = Calldate.Value.AddDays(1);
                        }
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes > ETM)
                    {
                        Calldate = Calldate.Value.AddDays(1);
                    }
                }
                double TotalWorkingHours = 0.0;
                int yearChng = Calldate.Value.Year;
                CallDateDetail detail = new CallDateDetail();
                detail = CommonFunctionalities.GetDetailsForDate(Calldate, companyID, connString, LogException);
                double temp = 0.0;
                while (Calldate.Value.Date < DateTime.Now.Date)
                {
                    string day = Calldate.Value.DayOfWeek.ToString();
                    int year = Calldate.Value.Year;
                    if (Calldate.Value.Year != yearChng)
                    {
                        detail = CommonFunctionalities.GetDetailsForDate(Calldate, companyID, connString, LogException);
                    }
                    if (detail.WorkDays.Contains(day))
                    {
                        TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                        temp = detail.WorkHours;
                    }
                    Calldate = Calldate.Value.AddDays(1);
                    yearChng = Calldate.Value.Year;
                }
                CallDateDetail todaysDetail = new CallDateDetail();
                todaysDetail = CommonFunctionalities.GetDetailsForDate(DateTime.Now, companyID, connString, LogException);
                if (DateTime.Now.TimeOfDay.TotalMinutes > todaysDetail.startTimeMinutes && DateTime.Now.TimeOfDay.TotalMinutes < todaysDetail.endTimeMinutes && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    if (DateTime.Now.TimeOfDay.TotalMinutes < todaysDetail.BreakstartTimeMinutes)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay.TotalMinutes > todaysDetail.BreakstartTimeMinutes && DateTime.Now.TimeOfDay.TotalMinutes < todaysDetail.BreakEndTimeMinute)
                    {
                        timeFromToday = todaysDetail.BreakstartTimeMinutes - todaysDetail.startTimeMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay.TotalMinutes > todaysDetail.BreakEndTimeMinute)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes);
                    }
                }
                else if ((DateTime.Now.TimeOfDay.TotalMinutes + 1440) > todaysDetail.startTimeMinutes && (DateTime.Now.TimeOfDay.TotalMinutes + 1440) < todaysDetail.endTimeMinutes && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    timeFromToday = ((DateTime.Now.TimeOfDay.TotalMinutes + 1440) - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes) - temp;
                    DateTime Tday = ActualCallDate.Value.AddDays(1);
                    if (Tday.Date == DateTime.Now.Date)
                    {
                        timeFromToday = timeFromToday - todaysDetail.WorkHours;
                    }
                }

                double HDtime = 0.0;
                int holidayYear = 0;
                double holidayWhours = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT b.CompanyCalenderHoliday_Date
                        FROM GNM_CompanyCalender a
                        INNER JOIN GNM_CompanyCalenderHolidays b ON a.CompanyCalender_ID = b.CompanyCalender_ID
                        WHERE a.Company_ID = @CompanyID
                        AND b.CompanyCalenderHoliday_Date >= @ActualCallDate
                        AND b.CompanyCalenderHoliday_Date <= @EndDate
                        AND a.IsGeneralShift = 1
                    ";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ActualCallDate", ActualCallDate);
                            cmd.Parameters.AddWithValue("@EndDate", DateTime.Now);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    CallDateDetail holidayDetail = new CallDateDetail();
                                    var holidayDate = reader.GetDateTime(reader.GetOrdinal("CompanyCalenderHoliday_Date"));
                                    if (holidayYear == 0 || holidayDate.Year != holidayYear)
                                    {
                                        holidayDetail = CommonFunctionalities.GetDetailsForDate(holidayDate, companyID, connString, LogException);
                                    }
                                    holidayWhours = holidayDetail.WorkHours;
                                    HDtime = HDtime + holidayWhours;
                                    holidayYear = holidayDate.Year;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        cmd?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                s = Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) / 60).ToString() + "." + ((Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString().Length == 1 ? "0" + Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString() : Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString()).ToString());
            }
            catch (Exception e) { }
            return Convert.ToDouble(s.Contains('-') ? "0" : s);
        }
        #endregion
    }
}
