using PBC.WorkflowService.Models;

namespace PBC.WorkflowService.Services
{
    /// <summary>
    /// Interface for WorkflowCommon utility functions
    /// </summary>
    public interface IWorkflowCommonService
    {
        /// <summary>
        /// Check if prefix/suffix configuration exists for the given parameters
        /// </summary>
        /// <param name="companyID">Company ID</param>
        /// <param name="branchID">Branch ID</param>
        /// <param name="objectName">Object name to check</param>
        /// <param name="dbName">Database name</param>
        /// <param name="connectionString">Database connection string</param>
        /// <returns>True if prefix/suffix configuration exists, false otherwise</returns>
        Task<bool> CheckPrefixSuffixAsync(int companyID, int branchID, string objectName, string dbName, string connectionString);

        /// <summary>
        /// Get object ID by object name
        /// </summary>
        /// <param name="name">Object name</param>
        /// <param name="dbName">Database name</param>
        /// <param name="connectionString">Database connection string</param>
        /// <returns>Object ID if found, 0 otherwise</returns>
        Task<int> GetObjectIDAsync(string name, string dbName, string connectionString);
    }
}
