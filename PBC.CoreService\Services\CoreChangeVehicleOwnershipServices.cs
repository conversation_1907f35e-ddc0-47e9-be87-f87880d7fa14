using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Services
{
    public class CoreChangeVehicleOwnershipServices : ICoreChangeVehicleOwnershipServices
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CoreChangeVehicleOwnershipServices> _logger;
        private readonly IConfiguration _configuration;

        public CoreChangeVehicleOwnershipServices(HttpClient httpClient, ILogger<CoreChangeVehicleOwnershipServices> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
        }

        #region Helper Methods for PBC.UtilityService Calls

        /// <summary>
        /// Get resource string using PBC.UtilityService
        /// </summary>
        private async Task<string> GetResourceStringAsync(string cultureValue, string resourceKey)
        {
            try
            {
                var request = new
                {
                    CultureValue = cultureValue,
                    ResourceKey = resourceKey
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:7003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/commonfunctionalities/resource-string", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return responseContent.Trim('"') ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for GetResourceString");
                return string.Empty;
            }
        }

        /// <summary>
        /// Get global resource object using PBC.UtilityService
        /// </summary>
        private async Task<IActionResult> GetGlobalResourceObjectAsync(string cultureValue, string resourceKey)
        {
            try
            {
                var request = new
                {
                    CultureValue = cultureValue,
                    ResourceKey = resourceKey
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:7003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/commonfunctionalities/resource", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return new JsonResult(responseContent);
                }

                return new JsonResult(string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for GetGlobalResourceObject");
                return new JsonResult(string.Empty);
            }
        }

        /// <summary>
        /// Decrypt string using PBC.UtilityService
        /// </summary>
        private async Task<string> DecryptStringAsync(string encryptedString)
        {
            try
            {
                var request = new
                {
                    EncryptedString = encryptedString
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:7003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/decrypt-string", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<string>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? string.Empty : string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for DecryptString");
                return string.Empty;
            }
        }

        /// <summary>
        /// Filter search using PBC.UtilityService
        /// </summary>
        private async Task<T[]> FilterSearchAsync<T>(T[] data, object filters)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    Filters = filters
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:7003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/filter-search", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for FilterSearch");
                return data;
            }
        }

        /// <summary>
        /// Advance search using PBC.UtilityService
        /// </summary>
        private async Task<T[]> AdvanceSearchAsync<T>(T[] data, object advanceFilter)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    AdvanceFilter = advanceFilter
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:7003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/advance-search", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for AdvanceSearch");
                return data;
            }
        }

        /// <summary>
        /// Order by field using PBC.UtilityService
        /// </summary>
        private async Task<T[]> OrderByFieldAsync<T>(T[] data, string sortField, string sortDirection)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    SortField = sortField,
                    SortDirection = sortDirection
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:7003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/order-by-field", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for OrderByField");
                return data;
            }
        }

        #endregion

        #region ::: Select Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To select All product
        /// </summary>

        public async Task<IActionResult> SelectAsync(string connString, SelectCoreChangeVehicleOwnershipList SelectCoreChangeVehicleOwnershipobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(_configuration["LogError"] ?? "1");
            var jsonData = default(dynamic);
            string wherecondition = "";
            try
            {
                string YesE = await GetResourceStringAsync(SelectCoreChangeVehicleOwnershipobj.GeneralCulture.ToString(), "Yes");
                string NoE = await GetResourceStringAsync(SelectCoreChangeVehicleOwnershipobj.GeneralCulture.ToString(), "No");
                string YesL = await GetResourceStringAsync(SelectCoreChangeVehicleOwnershipobj.UserCulture.ToString(), "Yes");
                string NoL = await GetResourceStringAsync(SelectCoreChangeVehicleOwnershipobj.UserCulture.ToString(), "No");

                int Company_ID = Convert.ToInt32(SelectCoreChangeVehicleOwnershipobj.Company_ID);
                List<ChangeVehicleOwnershipLog> IEProductList = new List<ChangeVehicleOwnershipLog>();
                List<ChangeVehicleOwnershipLog> IEProductMasterArray = new List<ChangeVehicleOwnershipLog>();
                GNM_Company companydetail = new GNM_Company();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    // Get Company Details
                    using (SqlCommand cmd = new SqlCommand("GetCompanyDetailsCoreChangeVehicleOwnership", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                companydetail.Company_Name = reader["Company_Name"].ToString();
                            }
                        }
                    }

                    // Session["Company_Name"] = companydetail.Company_Name;
                    wherecondition = "WHERE pc.ProductCustomer_ToDate is null";

                    // Get Change Vehicle Ownership Logs
                    using (SqlCommand cmd = new SqlCommand("Up_SEl_AM_ERP_GetChangeVehicleOwnershipLogs", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var updatedDate = reader["UpdatedDate"] as DateTime?;
                                var log = new ChangeVehicleOwnershipLog
                                {
                                    ChangeVehicleOwnership_ID = Convert.ToInt32(reader["ChangeVehicleOwnership_ID"]),
                                    Product_ID = Convert.ToInt32(reader["Product_ID"]),
                                    Product_SerialNumber = reader["Product_SerialNumber"].ToString(),
                                    Model_Name = reader["Model_Name"].ToString(),
                                    Brand = reader["Brand"].ToString(),
                                    ProductType_Name = reader["ProductType_Name"].ToString(),
                                    PreviousParty_ID = Convert.ToInt32(reader["PreviousParty_ID"]),
                                    PreviousParty_Code = reader["PreviousParty_Code"].ToString(),
                                    PreviousParty_Name = reader["PreviousParty_Name"].ToString(),
                                    CurrentParty_ID = Convert.ToInt32(reader["CurrentParty_ID"]),
                                    CurrentParty_Code = reader["CurrentParty_Code"].ToString(),
                                    CurrentParty_Name = reader["CurrentParty_Name"].ToString(),
                                    UpdatedBy = reader["UpdatedBy"].ToString(),
                                    UpdatedDate = updatedDate ?? DateTime.MinValue,
                                    UpdatedDateStr = updatedDate.HasValue ? updatedDate.Value.ToString("dd-MMM-yyyy") : ""
                                };
                                IEProductList.Add(log);
                            }
                        }
                    }
                }

                // Session["Export"] = wherecondition + "Order By " + sidx + " " + sord;
                // Session["ExpLangID"] = LanguageID.ToString();

                if (SelectCoreChangeVehicleOwnershipobj.LanguageID == Convert.ToInt32(SelectCoreChangeVehicleOwnershipobj.GeneralLanguageID))
                {
                    IEProductMasterArray = IEProductList.Select(a => new ChangeVehicleOwnershipLog
                    {
                        Product_ID = a.Product_ID,
                        Brand = a.Brand,
                        Model_Name = a.Model_Name,
                        PreviousParty_ID = a.PreviousParty_ID,
                        PreviousParty_Code = a.PreviousParty_Code,
                        PreviousParty_Name = a.PreviousParty_Name,
                        CurrentParty_ID = a.CurrentParty_ID,
                        CurrentParty_Code = a.CurrentParty_Code,
                        CurrentParty_Name = a.CurrentParty_Name,
                        ChangeVehicleOwnership_ID = a.ChangeVehicleOwnership_ID,
                        Product_SerialNumber = a.Product_SerialNumber,
                        ProductType_Name = a.ProductType_Name,
                        UpdatedBy = a.UpdatedBy,
                        UpdatedDate = a.UpdatedDate,
                        UpdatedDateStr = a.UpdatedDate == DateTime.MinValue ? "" : a.UpdatedDate.ToString("dd-MMM-yyyy")
                    }).ToList();
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();
                        var localeDict = new Dictionary<int, string>();

                        // Get RefMasterDetailLocale
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailLocaleCoreChangeVehicleOwnership", conn))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    int id = Convert.ToInt32(reader["RefMasterDetail_ID"]);
                                    string name = reader["RefMasterDetail_Name"].ToString();
                                    localeDict[id] = name;
                                }
                            }
                        }

                        IEProductMasterArray = IEProductList.Select(a => new ChangeVehicleOwnershipLog
                        {
                            Product_ID = a.Product_ID,
                            Brand = localeDict.ContainsKey(a.Brand_ID) ? localeDict[a.Brand_ID] : a.Brand,
                            Model_Name = a.Model_Name,
                            PreviousParty_ID = a.PreviousParty_ID,
                            PreviousParty_Code = a.PreviousParty_Code,
                            PreviousParty_Name = a.PreviousParty_Name,
                            CurrentParty_ID = a.CurrentParty_ID,
                            CurrentParty_Code = a.CurrentParty_Code,
                            CurrentParty_Name = a.CurrentParty_Name,
                            ChangeVehicleOwnership_ID = a.ChangeVehicleOwnership_ID,
                            Product_SerialNumber = a.Product_SerialNumber,
                            ProductType_Name = a.ProductType_Name,
                            UpdatedBy = a.UpdatedBy,
                            UpdatedDate = a.UpdatedDate,
                            UpdatedDateStr = a.UpdatedDate == DateTime.MinValue ? "" : a.UpdatedDate.ToString("dd-MMM-yyyy")
                        }).ToList();
                    }
                }

                var IEProductMasterArrayData = IEProductMasterArray.ToArray();

                if (_search)
                {
                    string decryptedFilters = await DecryptStringAsync(Uri.UnescapeDataString(filters));
                    var filterObj = JObject.Parse(decryptedFilters).ToObject<object>();

                    // Apply filter search using PBC.UtilityService
                    IEProductMasterArrayData = await FilterSearchAsync(IEProductMasterArrayData, filterObj);
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    var advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<object>();

                    // Apply advance search using PBC.UtilityService
                    IEProductMasterArrayData = await AdvanceSearchAsync(IEProductMasterArrayData, advnfilter);
                    page = 1;
                }

                // Apply ordering using PBC.UtilityService
                IEProductMasterArrayData = await OrderByFieldAsync(IEProductMasterArrayData, sidx, sord);
                var IQProudctMaster = IEProductMasterArrayData.AsQueryable();
                int Count = IQProudctMaster.Count();
                int Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQProudctMaster.AsEnumerable()
                            select new
                            {
                                edit = "<a title='View' href='#' style='font-size: 13px;' id='" + a.ChangeVehicleOwnership_ID + "' key='" + a.ChangeVehicleOwnership_ID + "' PreviousParty_Name='" + a.PreviousParty_Name + "' SerialNumber='" + a.Product_SerialNumber + "' UpdatedBy='" + a.UpdatedBy + "' Brand='" + a.Brand + "' Model_Name='" + a.Model_Name + "' ProductType_Name='" + a.ProductType_Name + "' UpdatedDate='" + a.UpdatedDateStr + "' Product_ID='" + a.Product_ID + "' CurrentParty_Name='" + a.CurrentParty_Name + "' CurrentParty_Code='" + a.CurrentParty_Code + "' PreviousParty_Code='" + a.PreviousParty_Code + "'  class='EditChangeVehicleOwner' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                id = a.ChangeVehicleOwnership_ID,
                                Product_ID = a.Product_ID,
                                ChangeVehicleOwnership_ID = a.ChangeVehicleOwnership_ID,
                                Product_SerialNumber = a.Product_SerialNumber,
                                Model_Name = a.Model_Name,
                                Brand = a.Brand,
                                ProductType_Name = a.ProductType_Name,
                                PreviousParty_Name = a.PreviousParty_Name,
                                PreviousParty_Code = a.PreviousParty_Code,
                                CurrentParty_Name = a.CurrentParty_Name,
                                CurrentParty_Code = a.CurrentParty_Code,
                                UpdatedBy = a.UpdatedBy,
                                UpdatedDate = a.UpdatedDateStr
                            }).Skip((page - 1) * rows).Take(rows).ToArray(),
                    records = Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in SelectAsync method");
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: SaveProductCustomerDetails Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To Save Product Customer Details
        /// </summary>
        ///

        public async Task<IActionResult> SaveProductCustomerDetailsAsync(string connString, SaveProductCustomerDetailsCoreChangeVehicleOwnershipList SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj)
        {
            string Msg = string.Empty;
            int LogException = Convert.ToInt32(_configuration["LogError"] ?? "1");
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    int Company_ID = Convert.ToInt32(SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj.Company_ID);
                    int User_ID = Convert.ToInt32(SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj.User_ID);
                    int Branch_ID = Convert.ToInt32(SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj.Branch);
                    // DateTime localTime = Common.LocalTime(Branch_ID, DateTime.Now);
                    DateTime localTime = DateTime.Now;
                    JObject jObj1 = JObject.Parse(SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj.CustomerData);
                    var customerData = jObj1["rows1"].ToList();

                    foreach (var item in customerData)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_INS_UPD_AM_ERP_SaveProductCustomerDetails", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                            cmd.Parameters.AddWithValue("@UserID", User_ID);
                            cmd.Parameters.AddWithValue("@BranchID", Branch_ID);
                            cmd.Parameters.AddWithValue("@LocalTime", localTime);
                            cmd.Parameters.AddWithValue("@Product_ID", Convert.ToInt32(item["Product_ID"]));
                            cmd.Parameters.AddWithValue("@Party_ID", Convert.ToInt32(item["Party_ID"]));
                            cmd.Parameters.AddWithValue("@ProductCustomer_ID", Convert.ToInt32(item["ProductCustomer_ID"]));

                            Msg = cmd.ExecuteScalar().ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in SaveProductCustomerDetailsAsync method");
                }
                Msg = string.Empty;
            }

            return new JsonResult(Msg);
        }
        #endregion


        #region ::: SelectFieldSearchSerialNumber Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// To Select Field Search SerialNumber
        /// </summary>

        public async Task<IActionResult> SelectFieldSearchSerialNumberAsync(string connString, SelectFieldSearchSerialNumberList SelectFieldSearchSerialNumberobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            string values = (SelectFieldSearchSerialNumberobj.Value?.Trim()) ?? string.Empty;
            string value = await DecryptStringAsync(values);
            int Company_ID = Convert.ToInt32(SelectFieldSearchSerialNumberobj.Company_ID);
            int LangID = Convert.ToInt32(SelectFieldSearchSerialNumberobj.LanguageID);
            string GenLangCode = SelectFieldSearchSerialNumberobj.GeneralLanguageCode.ToString();
            string UserLangCode = SelectFieldSearchSerialNumberobj.UserLanguageCode.ToString();

            // Setting up ADO.NET
            SqlConnection conn = new SqlConnection(connString);
            SqlCommand cmd = new SqlCommand();
            cmd.Connection = conn;

            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();

            if (GenLangCode == UserLangCode)
            {
                // Call SP for general language matching case
                cmd.CommandText = "sp_GetProductDetails_GeneralLang";
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                cmd.Parameters.AddWithValue("@Value", string.IsNullOrEmpty(value) ? (object)DBNull.Value : value);
            }
            else
            {
                // Call SP for locale language case
                cmd.CommandText = "sp_GetProductDetails_LocaleLang";
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                cmd.Parameters.AddWithValue("@LangID", LangID);
                cmd.Parameters.AddWithValue("@Value", string.IsNullOrEmpty(value) ? (object)DBNull.Value : value);
            }

            // Execute the command
            da.SelectCommand = cmd;
            da.Fill(dt);

            // Sort the data
            IQueryable<MyDataClass> IQParty = dt.AsEnumerable()
                    .Select(r => new MyDataClass
                    {
                        ID = r.Field<int>("ID"),
                        Name = r.Field<string>("Name"),
                        Model = r.Field<string>("Model"),
                        Party_Code = r.Field<string>("Party_Code"),
                        CustomerName = r.Field<string>("CustomerName"),
                        EngineSerialNumber = r.Field<string>("EngineSerialNumber")
                    }).AsQueryable();

            // Convert to array for PBC.UtilityService calls
            var partyData = IQParty.ToArray();

            // Apply search or filters
            if (_search && !string.IsNullOrEmpty(filters))
            {
                // Parse and decrypt filters
                string decryptedFilters = await DecryptStringAsync(Uri.UnescapeDataString(filters));
                var filtersObj = JObject.Parse(decryptedFilters).ToObject<object>();

                // Apply search filters using PBC.UtilityService
                partyData = await FilterSearchAsync(partyData, filtersObj);
            }
            else if (advnce && !string.IsNullOrEmpty(advnceFilters))
            {
                // Parse advance filters
                var advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<object>();

                // Apply advanced filters using PBC.UtilityService
                partyData = await AdvanceSearchAsync(partyData, advnfilter);

                // Reset page to 1 for advanced search
                page = 1;
            }

            // Apply sorting using PBC.UtilityService
            partyData = await OrderByFieldAsync(partyData, sidx, sord);
            IQParty = partyData.AsQueryable();

            // Pagination logic
            int count = IQParty.Count(); // Count the filtered records
            int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

            // Adjust the page number
            if (count < (rows * page) && count != 0)
            {
                page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
            }

            // Paginate the result
            var paginatedData = IQParty
                .Skip((page - 1) * rows)
                .Take(rows)
                .Select(r => new
                {
                    r.ID,
                    r.Name,
                    r.Model,
                    r.Party_Code,
                    r.CustomerName,
                    r.EngineSerialNumber,
                    Select = $"<a title='Select' href='#' style='font-size: 13px;' id='{r.ID}' class='FieldSrch' style='cursor:pointer'><i class='fa fa-check'></i></a>"
                }).ToList();

            // Return the data in JSON format
            var jsonData = new
            {
                total = total,       // Total pages
                page = page,         // Current page
                records = count,     // Total records
                rows = paginatedData // Data rows
            };

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Method To Get Products For Serial Uday Kumar 16-08-2024 :::
        /// <summary>
        /// Method To Get Products For Serial
        /// </summary>
        ///

        public async Task<IActionResult> GetProductForSerialAsync(string connString, GetProductForSerialList GetProductForSerialobj)
        {
            var JsonData = default(dynamic);
            List<PartyProductDetails> PartyProductDetailsList = new List<PartyProductDetails>();
            int LogException = Convert.ToInt32(_configuration["LogError"] ?? "1");
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("Up_Get_AM_ERP_GetProductForSerial", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@SerialNumber", GetProductForSerialobj.SerialNumber);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        PartyProductDetails product = new PartyProductDetails();
                        product.PRODUCT_ID = Convert.ToInt32(reader["PRODUCT_ID"]);
                        product.SERIALNUMBER = reader["SERIALNUMBER"].ToString();
                        product.BRAND_ID = Convert.ToInt32(reader["BRAND_ID"]);
                        product.BRAND = reader["BRAND"].ToString();
                        product.MODEL_ID = Convert.ToInt32(reader["MODEL_ID"]);
                        product.MODEL_NAME = reader["MODEL_NAME"].ToString();
                        product.PRODUCTTYPE_ID = Convert.ToInt32(reader["PRODUCTTYPE_ID"]);
                        product.PRODUCTTYPE_NAME = reader["PRODUCTTYPE_NAME"].ToString();
                        product.PreviousParty_NAME = reader["PreviousParty_NAME"] != DBNull.Value ? reader["PreviousParty_NAME"].ToString() : null;
                        product.IsActive = (bool)reader["IsActive"];
                        product.Party_Code = reader["Party_Code"].ToString();

                        PartyProductDetailsList.Add(product);
                    }

                    JsonData = new
                    {
                        data = PartyProductDetailsList.Select(a => new
                        {
                            a.PRODUCT_ID,
                            a.SERIALNUMBER,
                            a.BRAND_ID,
                            a.BRAND,
                            a.MODEL_ID,
                            a.MODEL_NAME,
                            a.PRODUCTTYPE_ID,
                            a.PRODUCTTYPE_NAME,
                            a.PreviousParty_NAME,
                            a.IsActive,
                            a.Party_Code
                        })
                    };

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in GetProductForSerialAsync method");
                }
            }

            return new JsonResult(JsonData);
        }
        #endregion


        #region ::: Get Party Details Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 

        public async Task<IActionResult> GetPartyDetailsAsync(string connString, GetPartyDetailsCoreChangeVehicleOwnershipList GetPartyDetailsCoreChangeVehicleOwnershipobj)
        {
            var jsonResult = default(dynamic);
            int LogException = Convert.ToInt32(_configuration["LogError"] ?? "1");
            try
            {

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command
                    SqlCommand command = new SqlCommand("Up_Get_AM_ERP_GetPartyDetails", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    // Add parameters
                    command.Parameters.AddWithValue("@PartyName", Uri.UnescapeDataString(GetPartyDetailsCoreChangeVehicleOwnershipobj.PartyName));

                    // Execute reader
                    SqlDataReader reader = command.ExecuteReader();

                    // Process results
                    if (reader.Read())
                    {
                        jsonResult = new
                        {
                            Result = "1",
                            PartyName = Uri.UnescapeDataString(GetPartyDetailsCoreChangeVehicleOwnershipobj.PartyName),
                            Party_Id = reader["Party_ID"].ToString(),
                            IsActive = (bool)reader["Party_IsActive"],
                            Party_Code = Uri.UnescapeDataString(reader["Party_Code"].ToString())
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = reader.HasRows ? "2" : "0"
                        };
                    }

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0" // Not Exists or error occurred
                };

                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in GetPartyDetailsAsync method");
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: Get Party Detail Grid Uday Kumar J B 16-08-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        public async Task<IActionResult> SelectPartyDetailGridAsync(string connString, SelectPartyDetailGridCList SelectPartyDetailGridobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonobj = default(dynamic); // Initialize with your default value or appropriate structure
            List<dynamic> arr = new List<dynamic>(); // Initialize list to hold result rows
            int LogException = Convert.ToInt32(_configuration["LogError"] ?? "1");
            try
            {

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command and set stored procedure
                    SqlCommand command = new SqlCommand("Up_SEl_AM_ERP_SelectPartyDetailGrid", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@PartyName", SelectPartyDetailGridobj.PartyName);
                    command.Parameters.AddWithValue("@sidx", sidx);
                    command.Parameters.AddWithValue("@sord", sord);
                    command.Parameters.AddWithValue("@page", page);
                    command.Parameters.AddWithValue("@rows", rows);

                    // Additional parameters based on your application context
                    command.Parameters.AddWithValue("@GenLangCode", SelectPartyDetailGridobj.GeneralLanguageCode.ToString());
                    command.Parameters.AddWithValue("@UserLangCode", SelectPartyDetailGridobj.UserLanguageCode.ToString());

                    // Get select label using PBC.UtilityService
                    var selectLabelResult = await GetGlobalResourceObjectAsync(SelectPartyDetailGridobj.UserCulture.ToString(), "select");
                    string selectLabel = selectLabelResult is JsonResult jsonResult ? jsonResult.Value?.ToString() ?? "Select" : "Select";
                    command.Parameters.AddWithValue("@selectLab", selectLabel);

                    // Fetch data
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            // Create dynamic object to hold each row
                            dynamic obj = new ExpandoObject();
                            obj.Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"));
                            obj.Select = reader.GetString(reader.GetOrdinal("Select"));
                            obj.Party_Code = reader.GetString(reader.GetOrdinal("Party_Code"));
                            obj.Party_Name = reader.GetString(reader.GetOrdinal("Party_Name"));
                            obj.Party_Location = reader.GetString(reader.GetOrdinal("Party_Location"));
                            obj.Party_Address = reader.GetString(reader.GetOrdinal("Party_Address"));

                            // Add to list
                            arr.Add(obj);
                        }
                    }
                }

                // Convert to array for PBC.UtilityService calls
                var arrData = arr.ToArray();

                if (_search && !string.IsNullOrEmpty(filters))
                {
                    string decryptedFilters = await DecryptStringAsync(Uri.UnescapeDataString(filters));
                    var filtersObj = JObject.Parse(decryptedFilters).ToObject<object>();
                    arrData = await FilterSearchAsync(arrData, filtersObj);
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    var advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<object>();
                    arrData = await AdvanceSearchAsync(arrData, advnfilter);
                    page = 1;
                }

                // Apply ordering using PBC.UtilityService
                arrData = await OrderByFieldAsync(arrData, sidx, sord);
                IQueryable<dynamic> IQArr = arrData.AsQueryable();
                int Count = IQArr.Count();
                int Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                // Prepare JSON object to return
                jsonobj = new
                {
                    TotalPages = Total,
                    PageNo = page,
                    RecordCount = Count,
                    rows = IQArr
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in SelectPartyDetailGridAsync method");
                }
                // Handle exceptions here
                jsonobj = new
                {
                    error = ex.Message // You can customize this error handling as per your application's needs
                };
            }

            return new JsonResult(jsonobj);
        }
        #endregion


        #region ::: Get Customer Search Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To get Customer Search 
        /// </summary>
        /// <returns>...</returns>
        ///

        public async Task<IActionResult> SelectFieldSearchPartyAsync(string connString, SelectFieldSearchPartyList SelectFieldSearchPartyobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            string values = (SelectFieldSearchPartyobj.Value)?.Trim() ?? string.Empty;
            string value = await DecryptStringAsync(values);
            int Count = 0;
            int Total = 0;
            int Company_ID = Convert.ToInt32(SelectFieldSearchPartyobj.Company_ID);
            int Branch_ID = Convert.ToInt32(SelectFieldSearchPartyobj.Branch);
            int LangID = Convert.ToInt32(SelectFieldSearchPartyobj.LanguageID);
            string GenLangCode = SelectFieldSearchPartyobj.GeneralLanguageCode.ToString();
            string UserLangCode = SelectFieldSearchPartyobj.UserLanguageCode.ToString();

            // Step 1: Determine if filtering is based on the company
            bool FilterPartyBasedonCompany = false;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("sp_GetFilterPartyBasedOnCompany", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                    var result = cmd.ExecuteScalar();
                    FilterPartyBasedonCompany = Convert.ToBoolean(result);
                }
            }

            // Step 2: Fetch data for Party or Branch
            List<FieldSearch> flSrch = new List<FieldSearch>();

            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand("sp_SelectFieldSearchParty", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                    cmd.Parameters.AddWithValue("@LangID", LangID);
                    cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                    cmd.Parameters.AddWithValue("@Type", SelectFieldSearchPartyobj.Type);
                    cmd.Parameters.AddWithValue("@GenLangCode", GenLangCode);
                    cmd.Parameters.AddWithValue("@UserLangCode", UserLangCode);
                    cmd.Parameters.AddWithValue("@FilterPartyBasedonCompany", FilterPartyBasedonCompany);
                    cmd.Parameters.AddWithValue("@SearchValue", value);

                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        flSrch.Add(new FieldSearch
                        {
                            ID = Convert.ToInt32(reader["ID"]),
                            Name = reader["Name"].ToString(),
                            Location = reader["Location"].ToString(),
                            MobileNumber = reader["MobileNumber"].ToString(),
                            Email = reader["Email"].ToString(),
                            Party_Code = reader["Party_Code"].ToString(),
                            PartyAddress_Address = reader["PartyAddress_Address"].ToString()
                        });
                    }
                }
            }

            // Convert to array for PBC.UtilityService calls
            var fieldSearchData = flSrch.ToArray();

            if (_search && !string.IsNullOrEmpty(filters))
            {
                string decryptedFilters = await DecryptStringAsync(Uri.UnescapeDataString(filters));
                var filtersObj = JObject.Parse(decryptedFilters).ToObject<object>();
                fieldSearchData = await FilterSearchAsync(fieldSearchData, filtersObj);
            }
            else if (advnce && !string.IsNullOrEmpty(advnceFilters))
            {
                var advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<object>();
                fieldSearchData = await AdvanceSearchAsync(fieldSearchData, advnfilter);
                page = 1;
            }

            // Apply ordering using PBC.UtilityService
            fieldSearchData = await OrderByFieldAsync(fieldSearchData, sidx, sord);
            IQueryable<FieldSearch> IQFieldSearch = fieldSearchData.AsQueryable();

            // Step 4: Pagination and Total Count
            Count = IQFieldSearch.Count();
            Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

            if (Count < (rows * page) && Count != 0)
            {
                page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
            }

            // Step 5: Prepare the JSON result
            string Select = await GetResourceStringAsync(SelectFieldSearchPartyobj.UserCulture.ToString(), "select");
            var jsonData = new
            {
                total = Total,
                page = page,
                records = Count,
                rows = IQFieldSearch.Skip((page - 1) * rows).Take(rows).Select(q => new
                {
                    ID = q.ID,
                    Name = q.Name,
                    Location = q.Location,
                    MobileNumber = q.MobileNumber,
                    Email = q.Email,
                    Select = $"<a title='{Select}' href='#' style='font-size: 13px;' id='{q.ID}' class='FieldSrch' style='cursor:pointer'><i class='fa fa-check'></i></a>",
                    Party_Code = q.Party_Code,
                    PartyAddress_Address = q.PartyAddress_Address
                }).ToList()
            };

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Get Party Details by ID Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 

        public async Task<IActionResult> GetPartyDetailsbyIDAsync(string connString, GetPartyDetailsbyIDaList GetPartyDetailsbyIDobj)
        {
            var jsonResult = default(dynamic); // Replace with your default value
            int LogException = Convert.ToInt32(_configuration["LogError"] ?? "1");
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command
                    SqlCommand command = new SqlCommand("Up_Get_AM_ERP_GetPartyDetailsByID", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@PartyID", GetPartyDetailsbyIDobj.PartyID);

                    // Execute reader
                    SqlDataReader reader = command.ExecuteReader();

                    // Process results
                    if (reader.Read())
                    {
                        jsonResult = new
                        {
                            Result = "1",
                            PartyName = reader["Party_Name"].ToString(),
                            Party_Id = GetPartyDetailsbyIDobj.PartyID,
                            Party_IsActive = (bool)reader["Party_IsActive"],
                            Party_Code = reader["Party_Code"].ToString()
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "0" // Not Exists
                        };
                    }

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0" // Not Exists or error occurred
                };

                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in GetPartyDetailsbyIDAsync method");
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion
    }
}
