//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_WFCase_Progress
    {
        public int WFCaseProgress_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int Transaction_ID { get; set; }
        public int WFSteps_ID { get; set; }
        public Nullable<int> Addresse_ID { get; set; }
        public byte Addresse_Flag { get; set; }
        public System.DateTime Received_Time { get; set; }
        public Nullable<int> Actioned_By { get; set; }
        public Nullable<System.DateTime> Action_Time { get; set; }
        public Nullable<int> Action_Chosen { get; set; }
        public string Action_Remarks { get; set; }
        public Nullable<bool> Locked_Ind { get; set; }
        public Nullable<int> WFNextStep_ID { get; set; }
    }
}
