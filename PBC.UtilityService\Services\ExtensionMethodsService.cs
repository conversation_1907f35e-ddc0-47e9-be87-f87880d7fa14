using PBC.UtilityService.Utilities.DTOs;
using PBC.UtilityService.Utilities.Models;
using PBC.UtilityService.Utilities;
using System.Text.Json;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Service implementation for ExtensionMethods operations
    /// </summary>
    public class ExtensionMethodsService : IExtensionMethodsService
    {
        private readonly ILogger<ExtensionMethodsService> _logger;

        public ExtensionMethodsService(ILogger<ExtensionMethodsService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<object[]>> OrderByFieldAsync(OrderByFieldRequest request)
        {
            try
            {
                _logger.LogInformation("Ordering data by field: {SortField}, direction: {SortDirection}", 
                    request.SortField, request.SortDirection);

                await Task.Delay(1); // Simulate async operation

                // Convert data to IQueryable for processing
                var queryableData = request.Data.AsQueryable();
                
                // Apply ordering using the extension method
                var orderedData = queryableData.OrderByField(request.SortField, request.SortDirection);
                
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = true,
                    Data = orderedData.ToArray(),
                    TotalCount = orderedData.Count()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ordering data by field: {SortField}", request.SortField);
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<object[]>> FilterSearchAsync(FilterSearchRequest request)
        {
            try
            {
                _logger.LogInformation("Filtering data with {RuleCount} filter rules", 
                    request.Filters.rules.Count);

                await Task.Delay(1); // Simulate async operation

                // Convert data to IQueryable for processing
                var queryableData = request.Data.AsQueryable();
                
                // Apply filtering using the extension method
                var filteredData = queryableData.FilterSearch(request.Filters);
                
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = true,
                    Data = filteredData.ToArray(),
                    TotalCount = filteredData.Count()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error filtering data with simple filters");
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<object[]>> AdvanceSearchAsync(AdvanceSearchRequest request)
        {
            try
            {
                _logger.LogInformation("Filtering data with {RuleCount} advanced filter rules", 
                    request.AdvanceFilter.Rules.Count);

                await Task.Delay(1); // Simulate async operation

                // Convert data to IQueryable for processing
                var queryableData = request.Data.AsQueryable();
                
                // Apply advanced filtering using the extension method
                var filteredData = queryableData.AdvanceSearch(request.AdvanceFilter);
                
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = true,
                    Data = filteredData.ToArray(),
                    TotalCount = filteredData.Count()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error filtering data with advanced filters");
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<PaginationResponse<object>> PaginateAsync(PaginateRequest request)
        {
            try
            {
                _logger.LogInformation("Paginating data - Page: {Page}, Rows: {Rows}", 
                    request.Page, request.Rows);

                await Task.Delay(1); // Simulate async operation

                // Convert data to List for processing
                var dataList = request.Data.ToList();
                
                // Apply pagination using the extension method
                var paginatedData = dataList.Paginate(request.Page, request.Rows);
                
                var totalRecords = dataList.Count;
                var totalPages = (int)Math.Ceiling((double)totalRecords / request.Rows);
                
                return new PaginationResponse<object>
                {
                    Data = paginatedData,
                    CurrentPage = request.Page,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    RowsPerPage = request.Rows,
                    HasNextPage = request.Page < totalPages,
                    HasPreviousPage = request.Page > 1
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error paginating data");
                return new PaginationResponse<object>
                {
                    Data = new List<object>()
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<object>> ConvertToTypeAsync(ConvertToTypeRequest request)
        {
            try
            {
                _logger.LogInformation("Converting object to type: {TargetType}", request.TargetType);

                await Task.Delay(1); // Simulate async operation

                // Get the target type
                Type targetType = Type.GetType(request.TargetType) ?? 
                                 Type.GetType($"System.{request.TargetType}") ??
                                 throw new ArgumentException($"Unknown type: {request.TargetType}");
                
                // Apply conversion using the extension method
                var convertedValue = request.Value.ConvertToType(targetType);
                
                return new ExtensionMethodsResponse<object>
                {
                    Success = true,
                    Data = convertedValue
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting object to type: {TargetType}", request.TargetType);
                return new ExtensionMethodsResponse<object>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<string>> DecryptStringAsync(DecryptStringRequest request)
        {
            try
            {
                _logger.LogInformation("Decrypting string");

                await Task.Delay(1); // Simulate async operation

                // Apply decryption using the extension method
                var decryptedString = ExtensionMethods.DecryptString_CE(request.EncryptedString);
                
                return new ExtensionMethodsResponse<string>
                {
                    Success = true,
                    Data = decryptedString
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting string");
                return new ExtensionMethodsResponse<string>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<bool>> LikeAsync(string field, string value)
        {
            try
            {
                await Task.Delay(1); // Simulate async operation
                var result = ExtensionMethods.Like(field, value);
                
                return new ExtensionMethodsResponse<bool>
                {
                    Success = true,
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Like comparison");
                return new ExtensionMethodsResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<bool>> LikeIntegerAsync(int field, int value)
        {
            try
            {
                await Task.Delay(1); // Simulate async operation
                var result = ExtensionMethods.LikeInteger(field, value);
                
                return new ExtensionMethodsResponse<bool>
                {
                    Success = true,
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeInteger comparison");
                return new ExtensionMethodsResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<bool>> LikeDecimalAsync(decimal field, decimal value)
        {
            try
            {
                await Task.Delay(1); // Simulate async operation
                var result = ExtensionMethods.LikeDecimal(field, value);
                
                return new ExtensionMethodsResponse<bool>
                {
                    Success = true,
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeDecimal comparison");
                return new ExtensionMethodsResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<bool>> LikeDateAsync(DateTime field, string value)
        {
            try
            {
                await Task.Delay(1); // Simulate async operation
                var result = ExtensionMethods.LikeDate(field, value);
                
                return new ExtensionMethodsResponse<bool>
                {
                    Success = true,
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeDate comparison");
                return new ExtensionMethodsResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<bool>> LikeNullableBoolAsync(bool? field, bool value)
        {
            try
            {
                await Task.Delay(1); // Simulate async operation
                var result = ExtensionMethods.LikeNullableBool(field, value);
                
                return new ExtensionMethodsResponse<bool>
                {
                    Success = true,
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeNullableBool comparison");
                return new ExtensionMethodsResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<bool>> LikeBoolAsync(bool field, bool value)
        {
            try
            {
                await Task.Delay(1); // Simulate async operation
                var result = ExtensionMethods.LikeBool(field, value);
                
                return new ExtensionMethodsResponse<bool>
                {
                    Success = true,
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeBool comparison");
                return new ExtensionMethodsResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
    }
}
