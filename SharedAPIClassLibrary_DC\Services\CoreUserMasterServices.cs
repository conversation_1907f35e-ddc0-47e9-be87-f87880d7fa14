﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreUserMasterServices
    {
        static string AppPath = string.Empty;

        #region ::: To select User Role details. / Mithun:::
        /// <summary>
        /// To select user details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectUserDetail(SelectUserDetailList SelectUserDetailObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonobj = new object();

            List<Role> allRoles = new List<Role>();
            List<UserRole> userRoles = new List<UserRole>();
            string rolesDropdown = "0:--------------Select----------------;";
            int totalRecords = 0;
            double totalPages = 0;
            string username = string.Empty;
            int branchID = Convert.ToInt32(SelectUserDetailObj.Branch);

            using (SqlConnection conn = new SqlConnection(constring))
            {
                using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetUserDetails", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserID", SelectUserDetailObj.Userid);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // Read user roles for the specified user
                        while (reader.Read())
                        {
                            userRoles.Add(new UserRole
                            {
                                UserRole_ID = reader.GetInt32(0),
                                Role_Name = reader.GetString(1)
                            });
                        }

                        // Read all roles
                        if (reader.NextResult())
                        {
                            while (reader.Read())
                            {
                                allRoles.Add(new Role
                                {
                                    Role_ID = reader.GetInt32(0),
                                    Role_Name = reader.GetString(1)
                                });
                            }
                        }

                        // Read the user's name
                        if (reader.NextResult() && reader.Read())
                        {
                            username = reader.GetString(0);
                        }
                    }
                }
            }

            try
            {
                foreach (var role in allRoles)
                {
                    rolesDropdown += $"{role.Role_ID}:{role.Role_Name};";
                }
                rolesDropdown = rolesDropdown.TrimEnd(';');

                if (SelectUserDetailObj.Userid != 0)
                {
                    totalRecords = userRoles.Count;
                    totalPages = Math.Ceiling(totalRecords / (double)rows);

                    var paginatedUserRoles = userRoles.Skip((page - 1) * rows).Take(rows).ToList();

                    jsonobj = new
                    {
                        TotalPages = totalPages,
                        PageNo = page,
                        RecordCount = totalRecords,
                        ddlRoles = rolesDropdown,
                        rows = paginatedUserRoles.Select(ur => new
                        {
                            UserRole_id = ur.UserRole_ID,
                            Edit = $"<a title='Edit' href='#' key='{ur.UserRole_ID}' mode='Read' class='edtClick font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = $"<input key='{ur.UserRole_ID}' type='checkbox' class='chkClick' />",
                            RoleName = ur.Role_Name
                        }).ToList()
                    };

                    // Insert GPS Details (assuming the method is available and correct)
                    //   gbl.InsertGPSDetails(Convert.ToInt32(SelectUserDetailObj.Company_ID.ToString()), branchID, Convert.ToInt32(SelectUserDetailObj.User_ID), Common.GetObjectID("CoreUserMaster",constring), SelectUserDetailObj.Userid, 0, 0, $"Viewed User {username}", false, Convert.ToInt32(SelectUserDetailObj.MenuID), Convert.ToDateTime(SelectUserDetailObj.LoggedINDateTime));
                }
                else
                {
                    jsonobj = new
                    {
                        TotalPages = totalPages,
                        PageNo = page,
                        RecordCount = totalRecords,
                        ddlRoles = rolesDropdown
                    };
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // RedirectToAction("Error");
            }

            //return Json(jsonobj, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonobj);
        }

        // Helper classes for mapping the data
        public class Role
        {
            public int Role_ID { get; set; }
            public string Role_Name { get; set; }
        }

        public class UserRole
        {
            public int UserRole_ID { get; set; }
            public string Role_Name { get; set; }
        }


        #endregion

        #region::: To insert User details /Mithun:::
        /// <summary>
        /// To insert User details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Insert(InsertUserMasterList InsertObj, string constring, int LogException)
        {
            dynamic jsonResult;
            string Email = "";
            int newUserID = 0;

            try
            {
                string jsonData = InsertObj.Data;
                JObject parsedData = JObject.Parse(jsonData);
                GNM_User gUser = parsedData.ToObject<GNM_User>();

                string LoginID = Uri.UnescapeDataString(gUser.User_LoginID);
                int Language_ID = Convert.ToInt32(InsertObj.UserLanguageID);
                string UserName = Uri.UnescapeDataString(gUser.User_Name);
                string Password = Uri.UnescapeDataString(gUser.User_Password);
                string LandingPage = Uri.UnescapeDataString(gUser.LandingPage);
                int? EmployeeID = (gUser.Employee_ID == 0) ? null : gUser.Employee_ID;
                int? PartnerID = (gUser.Partner_ID == 0) ? null : gUser.Partner_ID;
                int? WareHouseID = (gUser.WareHouse_ID == 0) ? null : gUser.WareHouse_ID;
                int CompanyID = Convert.ToInt32(InsertObj.Company_ID);
                int BranchID = Convert.ToInt32(InsertObj.Branch);
                bool IsActive = true; // Set IsActive as needed

                // Get User Details from InsertObj
                GNM_User User = InsertObj.UserDetails.FirstOrDefault();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlTransaction tran = conn.BeginTransaction())
                    {
                        try
                        {
                            // Insert the user into the database
                            using (SqlCommand cmd = new SqlCommand("UP_INS_InsertUser", conn, tran))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@User_LoginID", LoginID);
                                cmd.Parameters.AddWithValue("@User_Name", UserName);
                                cmd.Parameters.AddWithValue("@User_Password", Password);
                                cmd.Parameters.AddWithValue("@LandingPage", LandingPage);
                                cmd.Parameters.AddWithValue("@User_IsActive", IsActive);
                                cmd.Parameters.AddWithValue("@Employee_ID", (object)EmployeeID ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Partner_ID", (object)PartnerID ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@WareHouse_ID", (object)WareHouseID ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                                cmd.Parameters.Add("@Language_ID", SqlDbType.Int).Value = Language_ID;
                                cmd.Parameters.Add("@User_Type_ID", SqlDbType.Int).Value = gUser.User_Type_ID;
                                cmd.Parameters.Add("@User_ID", SqlDbType.Int).Direction = ParameterDirection.Output;

                                cmd.ExecuteNonQuery();

                                newUserID = Convert.ToInt32(cmd.Parameters["@User_ID"].Value);
                            }

                            // Retrieve the Username of the inserted user
                            string Username;
                            using (SqlCommand cmd = new SqlCommand("SELECT User_Name FROM GNM_User WHERE User_ID = @User_ID", conn, tran))
                            {
                                cmd.Parameters.AddWithValue("@User_ID", newUserID);
                                Username = (string)cmd.ExecuteScalar();
                            }

                            // Commit the transaction if everything is successful
                            tran.Commit();

                            // Insert GPS details (This is just an example, make sure the actual method is called)
                            // gbl.InsertGPSDetails(CompanyID, BranchID, User.User_ID, Common.GetObjectID("CoreUserMaster", constring), newUserID, 0, 0, "Inserted User " + Username, false, Convert.ToInt32(InsertObj.MenuID), Convert.ToDateTime(InsertObj.LoggedINDateTime));

                            // Prepare JSON result
                            jsonResult = new
                            {
                                Result = "Success",
                                id = newUserID.ToString()
                            };

                            // Send email if necessary
                            if (newUserID > 0)
                            {
                                if (gUser.User_Type_ID == 1)
                                {
                                    string selectEmployeeEmailQuery = "SELECT Company_Employee_Email FROM GNM_CompanyEmployee WHERE Company_Employee_ID = @Employee_ID";
                                    using (SqlCommand cmd = new SqlCommand(selectEmployeeEmailQuery, conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Employee_ID", gUser.Employee_ID);
                                        Email = (string)cmd.ExecuteScalar();
                                    }
                                }
                                else
                                {
                                    string selectPartnerEmailQuery = "SELECT Party_Email FROM GNM_Party WHERE Party_ID = @Partner_ID";
                                    using (SqlCommand cmd = new SqlCommand(selectPartnerEmailQuery, conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Partner_ID", gUser.Partner_ID);
                                        Email = (string)cmd.ExecuteScalar();
                                    }
                                }

                                // Uncomment and adjust this block if you want to send an email
                                /*
                                if (!string.IsNullOrEmpty(Email))
                                {
                                    string ApplicationPath = InsertObj.IsSecureConnection
                                        ? "https://" + InsertObj.Host + InsertObj.ApplicationPath
                                        : "http://" + InsertObj.Host + InsertObj.ApplicationPath;

                                    ApplicationPath = $"<a href='{ApplicationPath}'>{ApplicationPath}</a>";

                                    WF_Email email = new WF_Email();
                                    string Admin = ConfigurationManager.AppSettings["Admin"].ToString();
                                    string HelpLineEmail = ConfigurationManager.AppSettings["HelpLineEmail"].ToString();
                                    email.Email_Body = $"Congratulations! You have been registered successfully!!!<br/>Your Login ID:<b>{gUser.User_LoginID}</b><br/>Your Password:<b>{Password}</b><br/>(*After login we recommend you to change the password)<br/><br/>Make sure you keep this information in a safe place..<br/>Login to Website through this link {ApplicationPath} <br/>*** This message is intended only for the person or entity to which it is addressed and may contain confidential and/or privileged information. If you have received this message in error, please notify the sender immediately and delete this message from your system ***";
                                    email.Email_Subject = "AMERP Access Details";
                                    email.Email_To = Email;
                                    email.Email_cc = Admin + "," + HelpLineEmail;
                                    email.Email_Queue_Date = DateTime.Now;
                                    email.Email_SentStatus = false;
                                    workflowclient.WF_Email.Add(email);
                                    workflowclient.SaveChanges();
                                }
                                */
                            }
                        }
                        catch (Exception ex)
                        {
                            // Rollback the transaction if an error occurs
                            tran.Rollback();
                            throw ex;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                jsonResult = new
                {
                    Result = "Insertion Failed",
                    id = 0
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "Insertion Failed",
                    id = 0
                };
            }

            return new JsonResult(jsonResult);
        }
        #endregion

        #region::: To select all user /Mithun:::
        /// <summary>
        /// To select all user names
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectUser(SelectUserList SelectUserObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonobj = new object();

            List<GNMUser> usersWithCompany = new List<GNMUser>();
            string sear = "Norm";
            int count = 0;
            int total = 0;

            using (SqlConnection conn = new SqlConnection(constring))
            {
                using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetUserDetailsWithCompany", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            usersWithCompany.Add(new GNMUser
                            {
                                User_ID = reader["User_ID"] != DBNull.Value ? Convert.ToInt32(reader["User_ID"]) : 0,
                                User_Name = reader["User_Name"].ToString(),
                                LoginID = reader["User_LoginID"] != DBNull.Value ? reader["User_LoginID"].ToString() : string.Empty,
                                Company_ID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0,
                                Company = reader["Company_Name"].ToString(),
                                IsActive = reader["User_IsActive"] != DBNull.Value && Convert.ToBoolean(reader["User_IsActive"]) ? "Yes" : "No",
                                Pwd = reader["User_Password"].ToString()
                            });
                        }

                    }
                }
            }

            try
            {
                if (_search)
                {
                    // Double decrypt the filters
                    string decryptedFilters = Common.DecryptString(Common.DecryptString(filters));

                    // Parse the decrypted filters string into a Filters object
                    Filters filtersobj = JObject.Parse(decryptedFilters).ToObject<Filters>();

                    // Apply filtering using the filters object
                    usersWithCompany = usersWithCompany.AsQueryable()
                                                       .FilterSearch<GNMUser>(filtersobj)
                                                       .ToList();

                    sear = "Filter";
                }

                usersWithCompany = usersWithCompany.AsQueryable().OrderByField<GNMUser>(sidx, sord).ToList();
                count = usersWithCompany.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var paginatedUsers = usersWithCompany.Skip((page - 1) * rows).Take(rows).ToList();

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    //   filter = Request.Params["filters"],
                    rows = paginatedUsers.Select(a => new
                    {
                        a.User_ID,
                        Edit = $"<a title='View' href='#' key='{a.User_ID}' mode='Read' class='edtUserClick font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = SelectUserObj.IsDelete ? $"<input key='{a.User_ID}' type='checkbox' defaultchecked='' class='chkClick' />" : $"<input key='{a.User_ID}' type='checkbox' defaultchecked='' class='chkClick' disabled={true} />",
                        a.User_Name,
                        a.LoginID,
                        a.Company,
                        a.IsActive,
                        a.Pwd,
                        Locale = $"<a key='{a.User_ID}' src='{AppPath}/Content/local.png' class='UserLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>",
                    }).ToList().Paginate(page, rows),
                    Sear = sear
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonobj, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonobj);
        }

        #endregion

        #region::: To edit user details  /Mithun:::
        /// <summary>
        /// To edit user details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Edit(EditUserMasterList EditObj, string constring, int LogException)
        {
            dynamic dummy = null;
            var jsonResult = dummy;
            string Pwd = string.Empty;
            int BranchID = Convert.ToInt32(EditObj.Branch);
            //GNM_User User = (GNM_User)Session["UserDetails"];
            GNM_User User = EditObj.UserDetails.FirstOrDefault();

            try
            {
                JObject jObj = JObject.Parse(EditObj.Data);

                int UserID = jObj["User_ID"].Value<int>();
                string UserName = Uri.UnescapeDataString(jObj["User_Name"].ToString());
                string LandingPage = Uri.UnescapeDataString(jObj["LandingPage"].ToString());
                string UserLoginID = Uri.UnescapeDataString(jObj["User_LoginID"].ToString());
                Pwd = Uri.UnescapeDataString(jObj["User_Password"].ToString());

                bool UserIsActive = Convert.ToBoolean(jObj["User_IsActive"]);
                int CompanyID = jObj["Company_ID"].Value<int>();
                int LanguageID = jObj["Language_ID"].Value<int>();
                byte UserTypeID = jObj["User_Type_ID"].Value<byte>();

                int? EmployeeID = jObj["Employee_ID"].ToObject<int?>() == 0 ? (int?)null : jObj["Employee_ID"].ToObject<int?>();
                int? PartnerID = jObj["Partner_ID"].ToObject<int?>() == 0 ? (int?)null : jObj["Partner_ID"].ToObject<int?>();
                int? WareHouseID = jObj["WareHouse_ID"].ToObject<int?>() == 0 ? (int?)null : jObj["WareHouse_ID"].ToObject<int?>();

                string UserPassword = Pwd != string.Empty ? Common.GenerateDMSPassword(Pwd) : null;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Upd_Am_Erp_UpdateUserDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@User_ID", UserID);
                        cmd.Parameters.AddWithValue("@User_Name", UserName);
                        cmd.Parameters.AddWithValue("@LandingPage", LandingPage);
                        cmd.Parameters.AddWithValue("@User_LoginID", UserLoginID);
                        cmd.Parameters.AddWithValue("@User_Password", (object)UserPassword ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@User_IsActive", UserIsActive);
                        cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                        cmd.Parameters.AddWithValue("@Language_ID", LanguageID);
                        cmd.Parameters.AddWithValue("@User_Type_ID", UserTypeID);
                        cmd.Parameters.AddWithValue("@Employee_ID", (object)EmployeeID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Partner_ID", (object)PartnerID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@WareHouse_ID", (object)WareHouseID ?? DBNull.Value);

                        cmd.ExecuteNonQuery();
                    }
                }

                // gbl.InsertGPSDetails(Convert.ToInt32(EditObj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("CoreUserMaster",constring), UserID, 0, 0, "Updated User " + UserName, false, Convert.ToInt32(EditObj.MenuID), Convert.ToDateTime(EditObj.LoggedINDateTime));

                jsonResult = new
                {
                    Result = "Success",
                    id = UserID
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                jsonResult = new
                {
                    Result = "Fail",
                    id = 0
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "Fail",
                    id = 0
                };
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region::: To Save User Role details  /Mithun:::
        /// <summary>
        /// To edit user details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SaveUserRole(SaveUserRoleList SaveUserRoleObj, string constring, int LogException)
        {
            var jsonResult = new { Result = "Fail", id = 0 };

            try
            {
                string jsonData = SaveUserRoleObj.Data;
                JObject jObj = JObject.Parse(jsonData);
                int rowCount = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        GNM_UserRole gUserRole = jObj["rows"][i].ToObject<GNM_UserRole>();

                        if (gUserRole.UserRole_ID == 0)
                        {
                            // Insert new record
                            SqlCommand cmdAdd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveUserRole", connection);
                            cmdAdd.CommandType = CommandType.StoredProcedure;
                            cmdAdd.Parameters.AddWithValue("@User_ID", gUserRole.User_ID);
                            cmdAdd.Parameters.AddWithValue("@Role_ID", gUserRole.Role_ID);

                            // Execute the stored procedure for insertion
                            int insertedUserRoleID = Convert.ToInt32(cmdAdd.ExecuteScalar());
                            // gbl.InsertGPSDetails(Convert.ToInt32(SaveUserRoleObj.Company_ID), Convert.ToInt32(SaveUserRoleObj.Branch), Convert.ToInt32(SaveUserRoleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreUserMaster",constring)), gUserRole.User_ID, 0, 0, "Update", false, Convert.ToInt32(SaveUserRoleObj.MenuID), Convert.ToDateTime(SaveUserRoleObj.LoggedINDateTime));


                        }
                        else
                        {
                            // Update existing record
                            SqlCommand cmdEdit = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveUserRole", connection);
                            cmdEdit.CommandType = CommandType.StoredProcedure;
                            cmdEdit.Parameters.AddWithValue("@UserRole_ID", gUserRole.UserRole_ID);
                            cmdEdit.Parameters.AddWithValue("@User_ID", gUserRole.User_ID);
                            cmdEdit.Parameters.AddWithValue("@Role_ID", gUserRole.Role_ID);

                            // Execute the stored procedure for update
                            int updatedUserRoleID = Convert.ToInt32(cmdEdit.ExecuteScalar());
                            // gbl.InsertGPSDetails(Convert.ToInt32(SaveUserRoleObj.Company_ID), Convert.ToInt32(SaveUserRoleObj.Branch), Convert.ToInt32(SaveUserRoleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreUserMaster",constring)), gUserRole.User_ID, 0, 0, "Update", false, Convert.ToInt32(SaveUserRoleObj.MenuID), Convert.ToDateTime(SaveUserRoleObj.LoggedINDateTime));

                        }
                    }

                    jsonResult = new { Result = "Success", id = 0 };
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                jsonResult = new { Result = "Fail", id = 0 };
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);

        }


        #endregion

        #region::: To Save User Company details  /Mihtun:::
        /// <summary>
        /// To edit user details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SaveUserCompany(SaveUserCompanyList SaveUserCompanyObj, string constring, int LogException)
        {
            dynamic jsonResult = null;

            try
            {
                JObject jObj = JObject.Parse(SaveUserCompanyObj.Data);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        GNM_UserCompany gUserComp = jObj["rows"][i].ToObject<GNM_UserCompany>();

                        // Execute stored procedure for both add and edit operations
                        SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveUserCompany", connection);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserCompanyDet_ID", gUserComp.UserCompanyDet_ID);
                        cmd.Parameters.AddWithValue("@User_ID", gUserComp.User_ID);
                        cmd.Parameters.AddWithValue("@Company_ID", gUserComp.Company_ID);

                        // Execute the stored procedure
                        cmd.ExecuteNonQuery();
                        //   gbl.InsertGPSDetails(Convert.ToInt32(SaveUserCompanyObj.Company_ID), Convert.ToInt32(SaveUserCompanyObj.Branch), Convert.ToInt32(SaveUserCompanyObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreUserMaster",constring)), gUserComp.User_ID, 0, 0, "Update", false, Convert.ToInt32(SaveUserCompanyObj.MenuID), Convert.ToDateTime(SaveUserCompanyObj.LoggedINDateTime));

                    }
                }

                jsonResult = new
                {
                    Result = "Success"
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                jsonResult = new
                {
                    Result = "Fail",
                    id = 0
                };

            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);

        }
        #endregion

        #region::: To delete User Role  /Mithun:::
        /// <summary>
        /// To delete user roles
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult DeleteUserRole(DeleteUserRoleList DeleteUserRoleObj, string constring, int LogException)
        {
            string Result = string.Empty;

            try
            {
                JObject jObj = JObject.Parse(DeleteUserRoleObj.key);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        int id = jObj["rows"][i]["id"].ToObject<int>();

                        // Construct the SQL query
                        string sqlQuery = "DELETE FROM GNM_UserRole WHERE UserRole_ID = @UserRole_ID";

                        // Create SqlCommand with parameters
                        SqlCommand cmd = new SqlCommand(sqlQuery, connection);
                        cmd.Parameters.AddWithValue("@UserRole_ID", id);

                        // Execute the query
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            Result = "Deleted successfully";
                        }
                        else
                        {
                            Result = "Dependency found, cannot delete this record";
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                Result = "Dependency found, cannot delete this record";

            }

            //return Result;
            return new JsonResult(Result);
        }

        #endregion

        #region::: To delete User Company  /Mithun:::
        /// <summary>
        /// To delete user Company
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult DeleteUserCompany(DeleteUserCompanyList DeleteUserCompanyObj, string constring, int LogException)
        {
            string Result = string.Empty;
            var Culture = "Resource_" + DeleteUserCompanyObj.Lang;
            try
            {
                JObject jObj = JObject.Parse(DeleteUserCompanyObj.key);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        int id = jObj["rows"][i]["id"].ToObject<int>();

                        // Construct the SQL query
                        string sqlQuery = "DELETE FROM GNM_UserCompany WHERE UserCompanyDet_ID = @UserCompanyDet_ID";

                        // Create SqlCommand with parameters
                        SqlCommand cmd = new SqlCommand(sqlQuery, connection);
                        cmd.Parameters.AddWithValue("@UserCompanyDet_ID", id);

                        // Execute the query
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            Result = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                        }
                        else
                        {
                            Result = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                Result = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();

            }

            //return Result;
            return new JsonResult(Result);
        }

        #endregion

        #region::: To delete user  /Mithun:::
        /// <summary>
        ///To delete user
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult DeleteUser(DeleteUserList DeleteUserObj, string constring, int LogException)
        {
            string Result = string.Empty;
            int BranchID = Convert.ToInt32(DeleteUserObj.Branch);
            //GNM_User User = (GNM_User)Session["UserDetails"];
            GNM_User User = DeleteUserObj.UserDetails.FirstOrDefault();

            try
            {
                JObject jObj = JObject.Parse(DeleteUserObj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        id = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"]);

                        // Fetch user details
                        GNM_User gUser = null;
                        string selectQuery = "SELECT * FROM GNM_User WHERE User_ID = @UserId";
                        using (SqlCommand selectCommand = new SqlCommand(selectQuery, connection))
                        {
                            selectCommand.Parameters.AddWithValue("@UserId", id);
                            using (SqlDataReader reader = selectCommand.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    gUser = new GNM_User
                                    {
                                        User_ID = (int)reader["User_ID"],
                                        User_Name = reader["User_Name"].ToString()

                                    };
                                }
                            }
                        }

                        if (gUser != null)
                        {
                            // Delete the user
                            string deleteQuery = "DELETE FROM GNM_User WHERE User_ID = @UserId";
                            using (SqlCommand deleteCommand = new SqlCommand(deleteQuery, connection))
                            {
                                deleteCommand.Parameters.AddWithValue("@UserId", id);
                                deleteCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }

                Result = "Deleted successfully";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                Result = "Dependency Found, Cannot delete this Record";
            }
            catch (Exception ex)
            {
                if (ex.InnerException?.InnerException?.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint") == true)
                {
                    Result = "Dependency Found, Cannot delete this Record";
                }
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Result;
            return new JsonResult(Result);
        }

        #endregion

        #region:::  To validate selected roles  /Mithun:::
        /// <summary>
        ///To validate selected roles 
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult ValidateUserRole(ValidateUserRoleList ValidateUserRoleObj, string constring, int LogException)
        {
            bool isDuplicate = false;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_Chk_Am_Erp_ValidateUserRole", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserID", ValidateUserRoleObj.userID);
                        cmd.Parameters.AddWithValue("@RoleID", ValidateUserRoleObj.roleID);
                        cmd.Parameters.Add("@IsDuplicate", SqlDbType.Bit).Direction = ParameterDirection.Output;

                        cmd.ExecuteNonQuery();

                        isDuplicate = Convert.ToBoolean(cmd.Parameters["@IsDuplicate"].Value);
                    }
                }
            }
            catch (Exception ex)
            {
                isDuplicate = false;
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return isDuplicate;
            return new JsonResult(isDuplicate);
        }

        #endregion

        #region:::  To validate selected Company /Mithun:::
        /// <summary>
        ///To validate selected roles 
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult validateUserCompany(validateUserCompanyList validateUserCompanyObj, string constring, int LogException)
        {
            bool isDuplicate = false;

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create SqlCommand for executing stored procedure
                    SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_ValidateUserCompany", connection);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserID", validateUserCompanyObj.UserID);
                    cmd.Parameters.AddWithValue("@CompanyID", validateUserCompanyObj.CompanyID);

                    // Execute the stored procedure
                    object result = cmd.ExecuteScalar();

                    // Check if result is not null and convert to boolean
                    if (result != null && result != DBNull.Value)
                    {
                        isDuplicate = Convert.ToBoolean(result);
                    }
                }
            }
            catch (Exception ex)
            {
                isDuplicate = false;
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return isDuplicate;
            return new JsonResult(isDuplicate);
        }

        #endregion

        #region:::  To get UserData  /Mithun:::
        /// <summary>
        /// To get UserData 
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult UserData(UserDataList UserDataObj, string constring, int LogException)
        {
            dynamic jsonData = null;
            // CommonFunctionalities CFC = new CommonFunctionalitiesController();

            try
            {
                int LanguageID = 0;
                int Branch_ID = Convert.ToInt32(UserDataObj.Branch.ToString());

                // Retrieve LanguageID using stored procedure
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetLanguageID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        LanguageID = (int)cmd.ExecuteScalar();
                    }
                }

                // Retrieve language data using stored procedure
                List<dynamic> languageList = new List<dynamic>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetLanguageDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                languageList.Add(new
                                {
                                    ID = reader["RefMasterDetail_ID"],
                                    Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }
                }

                // Retrieve LandingPages data using stored procedure
                List<dynamic> landingPagesList = new List<dynamic>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetLandingPages", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                landingPagesList.Add(new
                                {
                                    ID = reader["Object_ID"],
                                    Name = reader["Object_Description"]
                                });
                            }
                        }
                    }
                }

                // Retrieve HeaderWarehouse data using stored procedure
                List<dynamic> headerWarehouseList = new List<dynamic>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_UserDataLoadWarehouse", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                headerWarehouseList.Add(new
                                {
                                    ID = reader["WareHouse_ID"],
                                    Name = reader["WareHouseName"]
                                });
                            }
                        }
                    }
                }

                // Combine the data into jsonData
                jsonData = new
                {
                    CompanyMaster = (from comp in CommonFunctionalities.LoadCompany(constring, LogException, "")
                                     orderby comp.Company_Name
                                     select new
                                     {
                                         ID = comp.Company_ID,
                                         Name = comp.Company_Name
                                     }),
                    landingPageMaster = landingPagesList,
                    LanguageMaster = languageList,
                    HeaderWarehouse = headerWarehouseList
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }
        #endregion

        #region ::: To select all Employee /Mithun:::
        /// <summary>
        /// To select all Employee
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult EmployeeData(EmployeeDataList EmployeeDataObj, string constring, int LogException)
        {
            dynamic dummy = null;
            var jsonData = dummy;

            try
            {
                List<dynamic> employees = new List<dynamic>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetEmployeeData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompID", EmployeeDataObj.CompID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                employees.Add(new
                                {
                                    ID = (int)reader["Company_Employee_ID"],
                                    Name = (string)reader["Employee_Name"]
                                });
                            }
                        }
                    }
                }

                jsonData = new
                {
                    EmployeeMaster = employees
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                // RedirectToAction("Error");
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region :::To select all partner /Mithun:::
        /// <summary>
        /// To select all partner
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult PartnerData(PartnerDataList PartnerDataObj, string constring, int LogException)
        {
            dynamic dummy = null;
            var jsonData = dummy;

            try
            {
                List<dynamic> partners = new List<dynamic>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetCompanyPartners", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompID", PartnerDataObj.CompID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                partners.Add(new
                                {
                                    ID = (int)reader["Party_ID"],
                                    Name = (string)reader["Party_Name"]
                                });
                            }
                        }
                    }
                }

                jsonData = new
                {
                    PartnerMaster = partners
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: To select all user names /Mithun:::
        /// <summary>
        /// To select all user names
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectUserforEdit(SelectUserforEditList SelectUserforEditObj, string constring, int LogException)
        {

            var jsonobj = default(dynamic);

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetUserAndHeaderWarehouse", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserID", SelectUserforEditObj.userid);

                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        int companyID = reader["Company_ID"] != DBNull.Value ? (int)reader["Company_ID"] : 0;
                        int employeeID = reader["Employee_ID"] != DBNull.Value ? (int)reader["Employee_ID"] : 0;
                        int languageID = reader["Language_ID"] != DBNull.Value ? (int)reader["Language_ID"] : 0;
                        int partnerID = reader["Partner_ID"] != DBNull.Value ? (int)reader["Partner_ID"] : 0;
                        int userID = reader["User_ID"] != DBNull.Value ? (int)reader["User_ID"] : 0;
                        bool userIsActive = reader["User_IsActive"] != DBNull.Value ? (bool)reader["User_IsActive"] : false;
                        string userLoginID = reader["User_LoginID"] != DBNull.Value ? (string)reader["User_LoginID"] : string.Empty;
                        string userName = reader["User_Name"] != DBNull.Value ? (string)reader["User_Name"] : string.Empty;
                        string userPassword = reader["User_Password"] != DBNull.Value ? (string)reader["User_Password"] : string.Empty;
                        byte userTypeID = reader["User_Type_ID"] != DBNull.Value ? (byte)reader["User_Type_ID"] : (byte)0;
                        string landingPage = reader["LandingPage"] != DBNull.Value ? (string)reader["LandingPage"] : string.Empty;
                        int warehouseID = reader["WareHouse_ID"] != DBNull.Value ? (int)reader["WareHouse_ID"] : 0;

                        reader.NextResult(); // Move to the next result set for HeaderWarehouse

                        var headerWarehouse = new List<object>();

                        while (reader.Read())
                        {
                            headerWarehouse.Add(new
                            {
                                ID = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                                Name = reader["Name"] != DBNull.Value ? (string)reader["Name"] : string.Empty
                            });
                        }

                        // Create the JSON object
                        jsonobj = new
                        {
                            Company_ID = companyID,
                            Employee_ID = employeeID,
                            Language_ID = languageID,
                            Partner_ID = partnerID,
                            User_ID = userID,
                            User_IsActive = userIsActive,
                            User_LoginID = userLoginID,
                            User_Name = userName,
                            User_Password = userPassword,
                            User_Type_ID = userTypeID,
                            LandingPage = landingPage,
                            WareHouse_ID = warehouseID,
                            HeaderWarehouse = headerWarehouse
                        };
                    }
                    reader.Close();
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return Json(jsonobj, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonobj);
        }



        public class WarehouseInfo
        {
            public int ID { get; set; }
            public string Name { get; set; }
        }


        #endregion

        #region ::: To Check for duplicate Roles /Mithun:::
        /// <summary>
        /// To delete Roles
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult ChkDuplicateLogin(ChkDuplicateLoginList ChkDuplicateLoginObj, string constring, int LogException)
        {
            bool isDuplicate = false;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckDuplicateLoginID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LoginID", ChkDuplicateLoginObj.LoginID);
                        cmd.Parameters.AddWithValue("@CompanyID", ChkDuplicateLoginObj.companyid);

                        int duplicateCount = (int)cmd.ExecuteScalar();
                        isDuplicate = duplicateCount > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                isDuplicate = false;
            }

            //return isDuplicate;
            return new JsonResult(isDuplicate);
        }


        #endregion

        #region ::: To select User Company details. /Mithun:::
        /// <summary>
        /// To select user details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectUserCompany(SelectUserCompanyList SelectUserCompanyObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            //GNM_User User = (GNM_User)Session["UserDetails"];
            //  GNM_User User = SelectUserCompanyObj.UserDetails.FirstOrDefault();
            dynamic dummy = null;
            var jsonobj = dummy;

            List<GNM_Company> CompObj = new List<GNM_Company>();
            List<GNM_UserCompany> UserCompObj = new List<GNM_UserCompany>();
            double total = 0.00;
            string Roles = "0:--------------Select----------------;";

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get list of companies
                    string companyQuery = "SELECT Company_ID, Company_Name FROM GNM_Company WHERE Company_Parent_ID = @Company_Parent_ID ORDER BY Company_Name";
                    using (SqlCommand cmd = new SqlCommand(companyQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_Parent_ID", SelectUserCompanyObj.Company_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var company = new GNM_Company
                                {
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Company_Name = reader.GetString(reader.GetOrdinal("Company_Name"))
                                };
                                CompObj.Add(company);

                                Roles += company.Company_ID + ":" + company.Company_Name + ";";
                            }
                        }
                    }

                    Roles = Roles.TrimEnd(new char[] { ';' });

                    if (SelectUserCompanyObj.Userid != 0)
                    {
                        // Get list of user companies
                        string userCompanyQuery = "SELECT UserCompanyDet_ID, Company_ID FROM GNM_UserCompany WHERE User_ID = @User_ID";
                        using (SqlCommand cmd = new SqlCommand(userCompanyQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@User_ID", SelectUserCompanyObj.Userid);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var userCompany = new GNM_UserCompany
                                    {
                                        UserCompanyDet_ID = reader.GetInt32(reader.GetOrdinal("UserCompanyDet_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                    };
                                    UserCompObj.Add(userCompany);
                                }
                            }
                        }

                        total = Math.Ceiling(Convert.ToDouble(UserCompObj.Count) / rows);

                        var userCompanyDetails = (
                            from userComp in UserCompObj
                            join company in CompObj on userComp.Company_ID equals company.Company_ID
                            select new
                            {
                                UserCompanyDet_ID = userComp.UserCompanyDet_ID,
                                Edit = "<img key='" + userComp.UserCompanyDet_ID + "' alt='Edit' src='" + AppPath + "/Content/Images/edit.gif' mode='Read' class='edtClick' />",
                                delete = "<input key='" + userComp.UserCompanyDet_ID + "' type='checkbox' class='chkClick' />",
                                CompanyName = company.Company_Name
                            }).ToList().Paginate(page, rows);

                        jsonobj = new
                        {
                            TotalPages = total,
                            PageNo = page,
                            RecordCount = UserCompObj.Count,
                            ddlCompanys = Roles,
                            rows = userCompanyDetails
                        };
                    }
                    else
                    {
                        jsonobj = new
                        {
                            TotalPages = total,
                            PageNo = page,
                            RecordCount = 0,
                            ddlCompanys = Roles
                        };
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // RedirectToAction("Error");
            }

            //return Json(jsonobj, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonobj);
        }

        #endregion

        #region:::  To get warehouse data  /Mithun:::
        /// <summary>
        /// To get  warehouse data 
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult WarehouseData(WarehouseDataList WarehouseDataObj, string constring, int LogException)
        {
            var jsonData = new { HeaderWarehouse = new List<object>() };

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetWarehouseData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", WarehouseDataObj.CompanyID);
                        cmd.Parameters.AddWithValue("@EmployeeID", WarehouseDataObj.EmployeeID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var headerWarehouseList = new List<object>();

                            while (reader.Read())
                            {
                                headerWarehouseList.Add(new
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }

                            jsonData = new
                            {
                                HeaderWarehouse = headerWarehouseList
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Particular User Locale /Mithun:::
        /// <summary>
        /// To Select particular user locale when click on view
        /// </summary> 
        public static IActionResult SelectParticularUserMasterLocale(SelectParticularUserMasterLocaleList SelectParticularUserMasterLocaleObj, string constring, int LogException)
        {
            try
            {
                var JsonResult = new
                {
                    User_ID = 0,
                    User_Name = "",
                    User_LoginID = "",
                    UserCompany = "",
                    UserType = 0,
                    Employee = "",
                    OutsideAgencyName = "",
                    LandngPage = "",
                    Warehouse = "",
                    User_Locale_ID = "",
                    UserNameL = "",
                    User_IsActive = false,
                    UserLanguage = ""
                };

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectUserDetailsLocale", conn);
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.Parameters.AddWithValue("@User_ID", SelectParticularUserMasterLocaleObj.UserG_ID);

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        JsonResult = new
                        {
                            User_ID = Convert.ToInt32(reader["User_ID"]),
                            User_Name = reader["User_Name"].ToString(),
                            User_LoginID = reader["User_LoginID"].ToString(),
                            UserCompany = reader["UserCompany"].ToString(),
                            UserType = Convert.ToInt32(reader["UserType"]),
                            Employee = reader["Employee"].ToString(),
                            OutsideAgencyName = reader["OutsideAgencyName"].ToString(),
                            LandngPage = reader["LandngPage"].ToString(),
                            Warehouse = reader["Warehouse"].ToString(),
                            User_Locale_ID = reader["User_Locale_ID"].ToString(),
                            UserNameL = reader["UserNameL"].ToString(),
                            User_IsActive = Convert.ToBoolean(reader["User_IsActive"]),
                            UserLanguage = reader["UserLanguage"].ToString()
                        };
                    }

                    reader.Close();
                }

                //return Json(JsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(JsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        #endregion

        #region ::: Insert User Locale /Mithun:::
        /// <summary>
        ///  Method to Insert User Locale
        /// </summary> 
        public static IActionResult InsertUserMasterLocale(InsertUserMasterLocaleList InsertUserMasterLocaleObj, string constring, int LogException)
        {
            try
            {
                JObject jObj = JObject.Parse(InsertUserMasterLocaleObj.key);

                int UserLocaleID = Convert.ToInt32(jObj["User_Locale_ID"].ToString());
                int User_ID = Convert.ToInt32(jObj["User_ID"].ToString());
                string Name = jObj["User_Name"].ToString();

                // Assuming constring is your connection string
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    SqlCommand cmd = new SqlCommand("Up_Ins_Am_Erp_InsertUserDetailsLocale", conn);
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.Parameters.AddWithValue("@User_ID", User_ID);
                    cmd.Parameters.AddWithValue("@User_Name", Common.DecryptString(Name));
                    cmd.Parameters.AddWithValue("@Language_ID", Convert.ToInt32(InsertUserMasterLocaleObj.UserLanguageID));

                    conn.Open();
                    cmd.ExecuteNonQuery();
                }
                return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle exception as needed
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion



        #region ::: Data for Export :::

        public static List<GNMUser> GetUsersWithCompanyDetails(SelectUserList ExportObj, string constring)
        {
            List<GNMUser> usersWithCompany = new List<GNMUser>();

            using (SqlConnection conn = new SqlConnection(constring))
            {
                using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetUserDetailsWithCompany", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            usersWithCompany.Add(new GNMUser
                            {
                                User_ID = reader["User_ID"] != DBNull.Value ? Convert.ToInt32(reader["User_ID"]) : 0,
                                User_Name = reader["User_Name"].ToString(),
                                LoginID = reader["User_LoginID"] != DBNull.Value ? reader["User_LoginID"].ToString() : string.Empty,
                                Company_ID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0,
                                Company = reader["Company_Name"].ToString(),
                                IsActive = reader["User_IsActive"] != DBNull.Value && Convert.ToBoolean(reader["User_IsActive"]) ? "Yes" : "No",
                                Pwd = reader["User_Password"].ToString()
                            });
                        }
                    }
                }
            }

            return usersWithCompany;
        }

        #endregion


        #region ::: Export :::
        public static async Task<object> Export(SelectUserList ExportObj, string constring, int LogException, string filters, string sidx, string sord)
        {
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            List<GNMUser> usersWithCompany = GetUsersWithCompanyDetails(ExportObj, constring);  // Fetch data using new method

            int BranchID = Convert.ToInt32(ExportObj.Branch);
            //GNM_User User1 = (GNM_User)Session["UserDetails"];

            try
            {
                // Apply filters if searching is enabled
                if (filters != "null" && filters != "undefined")
                {
                    string decryptedFilters = Common.DecryptString(Common.DecryptString(filters)); // Double decryption
                    Filters filtersobj = JObject.Parse(decryptedFilters).ToObject<Filters>();     // Parse filters

                    // Apply filtering using the filters object
                    usersWithCompany = usersWithCompany.AsQueryable()
                                                       .FilterSearch<GNMUser>(filtersobj)  // Apply filter logic
                                                       .ToList();
                }

                // Apply sorting based on the provided field (sidx) and order (sord)
                usersWithCompany = usersWithCompany.AsQueryable()
                                                   .OrderByField<GNMUser>(sidx, sord)  // Sorting logic
                                                   .ToList();

                int cnt = usersWithCompany.Count;

                // Prepare Alignment table for export
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "UserName").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "LoginID").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "CompanyName").ToString());
                dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());
                dt1.Rows.Add(0, 0, 0, 1);  // Alignment row

                // Populate DataTable for actual data export
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "UserName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "LoginID").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "CompanyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                // Fill the DataTable with the filtered and sorted user data
                foreach (var user in usersWithCompany)
                {
                    dt.Rows.Add(user.User_Name, user.LoginID, user.Company, user.IsActive);
                }

                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportObj.Branch,
                    dt1 = dt1,


                    dt = dt,

                    FileName = "MovementTypeDefinition", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "MovementTypeDefinition").ToString(), // Set a default or dynamic header
                    exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };


                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;
                // Call export method to export the data
                //DocumentExport.Export(exprtType, dt, dt1, "User", "User");

                // Log export activity
                //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), BranchID, ExportObj.User_ID,
                //                     Common.GetObjectID("CoreUserMaster",constring), 0, 0, 0, "User-Export", false,
                //                     Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle exception, redirect if needed
                // RedirectToAction("Error");
            }
            return null;
        }


        #endregion




        public class InsertUserMasterLocaleList
        {
            public int UserLanguageID { get; set; }
            public string key { get; set; }
        }
        public class SelectParticularUserMasterLocaleList
        {
            public int UserG_ID { get; set; }
        }
        public class WarehouseDataList
        {
            public int CompanyID { get; set; }
            public string EmployeeID { get; set; }
        }
        public class SelectUserCompanyList
        {
            public int Userid { get; set; }
            public int Company_ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class ChkDuplicateLoginList
        {
            public int companyid { get; set; }
            public string LoginID { get; set; }
        }

        public class SelectUserforEditList
        {
            public int userid { get; set; }
        }
        public class PartnerDataList
        {
            public int CompID { get; set; }
        }
        public class EmployeeDataList
        {
            public int CompID { get; set; }
        }

        public class UserDataList
        {
            public int Branch { get; set; }
        }
        public class validateUserCompanyList
        {
            public int UserID { get; set; }
            public int CompanyID { get; set; }
        }
        public class ValidateUserRoleList
        {
            public int userID { get; set; }
            public int roleID { get; set; }
        }
        public class DeleteUserList
        {
            public string key { get; set; }
            public string Lang { get; set; }
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class DeleteUserCompanyList
        {
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class DeleteUserRoleList
        {
            public string key { get; set; }
        }
        public class SaveUserCompanyList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string Data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SaveUserRoleList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string Data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class EditUserMasterList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string Data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectUserList
        {
            public bool IsEdit { get; set; }
            public bool IsDelete { get; set; }
            public int Branch { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int exprtType { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filters { get; set; }
        }

        public class GNMUser
        {
            public int User_ID { set; get; }
            public int Company_ID { set; get; }
            public string User_Name { set; get; }
            public string LoginID { set; get; }
            public string Company { set; get; }
            public string IsActive { set; get; }
            public string Pwd { set; get; }
            public string Locale { get; set; }
        }
        public class InsertUserMasterList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string Data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class WF_Email
        {
            public int Email_ID { get; set; }

            public string Email_Subject { get; set; }

            public string Email_Body { get; set; }

            public string Email_To { get; set; }

            public string Email_cc { get; set; }

            public string Email_Bcc { get; set; }

            public DateTime? Email_Queue_Date { get; set; }

            public DateTime? Email_Sent_Date { get; set; }

            public bool Email_SentStatus { get; set; }

            public string Email_Attachments { get; set; }

            public bool? Email_IsError { get; set; }

            public byte? NoOfAttempts { get; set; }
        }
        public class SelectUserDetailList
        {
            public int Userid { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }


    }
}
