﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreHeaderFooterPrintServices
    {
        static string AppPath = string.Empty;

        #region ::: GetTransaction /Mithun:::
        /// <summary>
        /// To GetTransaction
        /// </summary> 

        public static IActionResult GetTransactions(GetTransactionsList GetTransactionsObj, string constring, int LogException)
        {
            var transactions = default(dynamic);
            List<HeaderFooterNameID> workflowList = new List<HeaderFooterNameID>();

            try
            {
                int companyID = Convert.ToInt32(GetTransactionsObj.Company_ID);
                string query = "SELECT WorkFlow_ID AS ID, WorkFlow_Name AS Name FROM GNM_WorkFlow";

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                workflowList.Add(new HeaderFooterNameID
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }
                        }
                    }
                }

                transactions = from a in workflowList
                               orderby a.Name
                               select new
                               {
                                   a.ID,
                                   a.Name
                               };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            var jsonResult = new
            {
                Transactions = transactions
            };

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: save /Mithun:::
        /// <summary>
        ///  save Header Footer Print
        /// </summary>

        public static IActionResult Save(SaveHeaderFooterList SaveObj, string constring, int LogException)
        {
            string result = string.Empty;
            try
            {
                int ID = Int32.Parse(SaveObj.ID);
                int Company_ID = Convert.ToInt32(SaveObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SaveObj.Branch);
                string header = SaveObj.Header.Trim() == "" ? null : Common.DecryptString(SaveObj.Header);
                string footer = SaveObj.Footer.Trim() == "" ? null : Common.DecryptString(SaveObj.Footer);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = conn;
                        if (ID == 0)
                        {
                            cmd.CommandText = "Up_Ins_Am_Erp_InsertHeaderFooterPrint";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            cmd.Parameters.AddWithValue("@Header", (object)header ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Footer", (object)footer ?? DBNull.Value);

                            result = cmd.ExecuteScalar().ToString();
                        }
                        else
                        {
                            cmd.CommandText = "Up_Upd_Am_Erp_UpdateHeaderFooterPrint";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ID", ID);
                            cmd.Parameters.AddWithValue("@Header", (object)header ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Footer", (object)footer ?? DBNull.Value);

                            result = cmd.ExecuteScalar().ToString();
                        }
                    }

                    if (result == "Success")
                    {
                        int userID = Convert.ToInt32(SaveObj.User_ID);
                        int menuID = Convert.ToInt32(SaveObj.MenuID);
                        DateTime loggedInDateTime = Convert.ToDateTime(SaveObj.LoggedINDateTime);

                        //  gbl.InsertGPSDetails(Company_ID, Branch_ID, userID, Convert.ToInt32(Common.GetObjectID("CoreHeaderFooterPrint",constring)), ID, 0, 0, ID == 0 ? "Inserted" : "Updated", false, menuID, loggedInDateTime);
                    }
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return result;
            return new JsonResult(result);
        }


        #endregion

        #region ::: Updatelocale /Mithun:::
        /// <summary>
        ///  to Update Locale
        /// </summary>
        public static IActionResult UpdateLocale(UpdateLocaleHeaderFooterList UpdateLocaleObj, string constring, int LogException)
        {
            string result = string.Empty;
            try
            {
                int userLanguageID = Convert.ToInt32(UpdateLocaleObj.UserLanguageID);
                int companyID = Convert.ToInt32(UpdateLocaleObj.Company_ID);
                int branchID = Convert.ToInt32(UpdateLocaleObj.Branch);
                int userID = Convert.ToInt32(UpdateLocaleObj.User_ID);
                int menuID = Convert.ToInt32(UpdateLocaleObj.MenuID);
                DateTime loggedInDateTime = Convert.ToDateTime(UpdateLocaleObj.LoggedINDateTime);

                int ID = Int32.Parse(UpdateLocaleObj.ID);
                string header = UpdateLocaleObj.Header.Trim() == "" ? null : Common.DecryptString(UpdateLocaleObj.Header);
                string footer = UpdateLocaleObj.Footer.Trim() == "" ? null : Common.DecryptString(UpdateLocaleObj.Footer);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_InsertUpdateHeaderFooterDataLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@HeaderFooterPrintID", ID);
                        cmd.Parameters.AddWithValue("@Header", (object)header ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Footer", (object)footer ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                        SqlParameter outParam = new SqlParameter("@HeaderFooterPrintLocaleID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outParam);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                result = reader["Result"].ToString();
                            }
                        }

                        int headerFooterPrintLocaleID = (int)outParam.Value;

                        if (result == "Success")
                        {
                            //  gbl.InsertGPSDetails(companyID, branchID, userID, Convert.ToInt32(Common.GetObjectID("CoreHeaderFooterPrint",constring)), headerFooterPrintLocaleID, 0, 0, headerFooterPrintLocaleID == 0 ? "Inserted" : "Updated", false, menuID, loggedInDateTime);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return result;
            return new JsonResult(result);
        }


        #endregion

        #region :::SelectHeaderFooterGrid /Mithun:::
        /// <summary>
        /// To select Header Footer Grid
        /// </summary>
        public static IActionResult SelectHeaderFooterGrid(SelectHeaderFooterGridList SelectHeaderFooterGridObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;

                int companyID = Convert.ToInt32(SelectHeaderFooterGridObj.Company_ID);
                int branchID = Convert.ToInt32(SelectHeaderFooterGridObj.Branch);
                int userLanguageID = Convert.ToInt32(SelectHeaderFooterGridObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectHeaderFooterGridObj.GeneralLanguageID);

                List<HeaderFooterGrid> headerFooterGridList = new List<HeaderFooterGrid>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetHeaderFooterGrid", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@BranchID", branchID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                headerFooterGridList.Add(new HeaderFooterGrid
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("HEADERFOOTERPRINT_ID")),
                                    IsHeaderAvailable = reader.IsDBNull(reader.GetOrdinal("HEADERTEMPLATE")) ? "No" : "Yes",
                                    IsFooterAvailable = reader.IsDBNull(reader.GetOrdinal("FOOTERTEMPLATE")) ? "No" : "Yes"
                                });
                            }
                        }
                    }
                }
                var headerFooterGridQueryable = headerFooterGridList.AsQueryable();

                if (_search)
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersobj.rules.Count() > 0)
                        headerFooterGridQueryable = headerFooterGridQueryable.FilterSearch<HeaderFooterGrid>(filtersobj);
                }
                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse((Query)).ToObject<AdvanceFilter>();
                    headerFooterGridQueryable = headerFooterGridQueryable.AdvanceSearch<HeaderFooterGrid>(advnfilter);
                }
                headerFooterGridQueryable = headerFooterGridQueryable.OrderByField(sidx, sord);

                count = headerFooterGridQueryable.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                string refresh = CommonFunctionalities.GetResourceString(SelectHeaderFooterGridObj.UserCulture.ToString(), "Refresh").ToString();
                string lblAdvanceSearch = CommonFunctionalities.GetResourceString(SelectHeaderFooterGridObj.UserCulture.ToString(), "advancesearch").ToString();

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in headerFooterGridQueryable.Skip((page - 1) * rows).Take(rows)
                            select new
                            {
                                ID = a.ID,
                                Edit = $"<a title={CommonFunctionalities.GetResourceString(SelectHeaderFooterGridObj.UserCulture.ToString(), "view").ToString()} href='#' style='font-size: 13px;' id='{a.ID}' key='{a.ID}' editmode='false' class='editHeaderFooter' Tname='{a.Transaction}'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = $"<input type='checkbox' key='{a.ID}' id='chk{a.ID}' class='chkHeaderFooterDelete'/>",
                                IsHeaderAvailable = a.IsHeaderAvailable,
                                IsFooterAvailable = a.IsFooterAvailable,
                                Local = userLanguageID != generalLanguageID ? $"<a key='{a.ID}' src='{AppPath}/Content/Images/local.png' class='HeaderFooterLocale' alt='Localize' width='20' height='20' title='Localize'><i class='fa fa-globe'></i></a>" : "",
                            }).ToList(),
                    Refresh = refresh,
                    Lbl_AdvanceSearch = lblAdvanceSearch
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(null);
            }
        }

        #endregion

        #region :::GetHeaderFooter /Mithun:::
        /// <summary>
        /// to Get Header Footer
        /// </summary>
        public static IActionResult GetHeaderFooter(GetHeaderFooterList GetHeaderFooterObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            //GNM_User user = (GNM_User)Session["UserDetails"];
            //  GNM_User user = GetHeaderFooterObj.UserDetails.FirstOrDefault();
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetHeaderFooter", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@HeaderFooterPrintID", GetHeaderFooterObj.ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                jsonResult = new
                                {
                                    Header = reader.IsDBNull(reader.GetOrdinal("HEADERTEMPLATE")) ? "" : reader.GetString(reader.GetOrdinal("HEADERTEMPLATE")),
                                    Footer = reader.IsDBNull(reader.GetOrdinal("FOOTERTEMPLATE")) ? "" : reader.GetString(reader.GetOrdinal("FOOTERTEMPLATE"))
                                };
                            }
                        }
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(GetHeaderFooterObj.Company_ID),
                //    Convert.ToInt32(GetHeaderFooterObj.Branch),
                //    GetHeaderFooterObj.User_ID,
                //    Common.GetObjectID("CoreHeaderFooterPrint",constring),
                //    ID,
                //    0,
                //    0,
                //    "Viewed",
                //    false,
                //    Convert.ToInt32(GetHeaderFooterObj.MenuID),
                //    Convert.ToDateTime(GetHeaderFooterObj.LoggedINDateTime)
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(
                        ex.HResult,
                        ex.GetType().FullName + ":" + ex.Message,
                        ex.TargetSite.ToString(),
                        ex.StackTrace
                    );
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region :::GetHeaderFooterLocale/Mithun:::
        /// <summary>
        /// to Get Header Footer Locale
        /// </summary>
        public static IActionResult GetHeaderFooterLocale(GetHeaderFooterLocaleList GetHeaderFooterLocaleObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            //GNM_User user = (GNM_User)Session["UserDetails"];
            GNM_User user = GetHeaderFooterLocaleObj.UserDetails.FirstOrDefault();
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetHeaderFooterLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@HeaderFooterPrintID", GetHeaderFooterLocaleObj.ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                jsonResult = new
                                {
                                    Header = reader.IsDBNull(reader.GetOrdinal("HEADERTEMPLATE")) ? "" : reader.GetString(reader.GetOrdinal("HEADERTEMPLATE")),
                                    Footer = reader.IsDBNull(reader.GetOrdinal("FOOTERTEMPLATE")) ? "" : reader.GetString(reader.GetOrdinal("FOOTERTEMPLATE"))
                                };
                            }
                            else
                            {
                                jsonResult = new
                                {
                                    Header = "",
                                    Footer = ""
                                };
                            }
                        }
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(GetHeaderFooterLocaleObj.Company_ID),
                //    Convert.ToInt32(GetHeaderFooterLocaleObj.Branch),
                //    user.User_ID,
                //    Common.GetObjectID("CoreHeaderFooterPrint",constring),
                //    ID,
                //    0,
                //    0,
                //    "Viewed",
                //    false,
                //    Convert.ToInt32(GetHeaderFooterLocaleObj.MenuID),
                //    Convert.ToDateTime(GetHeaderFooterLocaleObj.LoggedINDateTime)
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(
                        ex.HResult,
                        ex.GetType().FullName + ":" + ex.Message,
                        ex.TargetSite.ToString(),
                        ex.StackTrace
                    );
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region :::Delete /Mithun:::
        /// <summary>
        /// Delete
        /// </summary>
        public static IActionResult Delete(DeleteHeaderFooterList DeleteObj, string constring, int LogException)
        {
            string errorMsg = string.Empty;
            JObject jObj = null;
            int count = 0;
            int id = 0;
            var Culture = "Resource_" + DeleteObj.Lang;
            try
            {
                jObj = JObject.Parse(DeleteObj.key);
                count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < count; i++)
                            {
                                id = jObj["rows"].ElementAt(i)["id"].ToObject<int>();

                                // Delete dependent records in GNM_HEADERFOOTERPRINTLOCALE first
                                using (SqlCommand cmd = new SqlCommand("DELETE FROM GNM_HEADERFOOTERPRINTLOCALE WHERE HEADERFOOTERPRINT_ID = @ID", conn, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@ID", id);
                                    cmd.ExecuteNonQuery();
                                }

                                // Delete the record in GNM_HEADERFOOTERPRINT
                                using (SqlCommand cmd = new SqlCommand("DELETE FROM GNM_HEADERFOOTERPRINT WHERE HEADERFOOTERPRINT_ID = @ID", conn, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@ID", id);
                                    cmd.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();

                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(DeleteObj.Company_ID),
                            //    Convert.ToInt32(DeleteObj.Branch),
                            //    Convert.ToInt32(DeleteObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreHeaderFooterPrint",constring)),
                            //    id,
                            //    0,
                            //    0,
                            //    "Delete",
                            //    false,
                            //    Convert.ToInt32(DeleteObj.MenuID),
                            //    Convert.ToDateTime(DeleteObj.LoggedINDateTime)
                            //);

                            errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMsg = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Error").ToString();
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }


        #endregion











        public class DeleteHeaderFooterList
        {
            public string key { get; set; }
            public string Lang { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class GetHeaderFooterLocaleList
        {
            public int UserLanguageID { get; set; }
            public int ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class GetHeaderFooterList
        {
            public int UserLanguageID { get; set; }
            public int ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectHeaderFooterGridList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public string UserCulture { get; set; }
        }

        class HeaderFooterGrid
        {
            public int ID { get; set; }
            public string Transaction { get; set; }
            public string IsHeaderAvailable { get; set; }
            public string IsFooterAvailable { get; set; }
        }
        public class UpdateLocaleHeaderFooterList
        {
            public int UserLanguageID { get; set; }
            public string ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string Header { get; set; }
            public string Footer { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class SaveHeaderFooterList
        {
            public string ID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string Header { get; set; }
            public string Footer { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class GetTransactionsList
        {
            public int Company_ID { get; set; }
        }
        class HeaderFooterNameID
        {
            public int ID { get; set; }
            public string Name { get; set; }
        }
    }
}
