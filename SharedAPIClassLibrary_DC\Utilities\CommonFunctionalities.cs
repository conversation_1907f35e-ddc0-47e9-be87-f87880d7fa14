﻿using Microsoft.AspNetCore.Mvc;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Resources;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class CommonFunctionalities
    {

        #region :::Global Declaration  :::
        /// <summary>
        /// Declaration 
        /// </summary>
        ///
        public static List<DateDetails> DateDetailsContainer = null;
        #endregion

        #region :::GetGlobalResourceObject   Uday Kumar J B 11-07-2024 :::
        /// <summary>
        /// To Get GlobalResourceObject Uday Kumar J B 11-07-2024 15:13
        /// </summary>
        /// 
        public static IActionResult GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                return new JsonResult(resourceValue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                return new JsonResult(string.Empty);
            }
        }
        public static string GetResourceString(string cultureValue, string resourceKey)
        {
            var actionResult = GetGlobalResourceObject(cultureValue, resourceKey);
            if (actionResult is JsonResult jsonResult)
            {
                return jsonResult.Value?.ToString() ?? string.Empty;
            }
            return string.Empty;
        }
        #endregion


        #region ::: SaveGPSDetails Uday Kumar J B 02-09-2024 :::
        /// <summary>
        /// To Insert Product Reading Details
        /// </summary>
        public static JsonResult InsertGPSDetails(int Company_ID, int BranchID, int USER_ID, int OBJECT_ID, int RECORD_ID, double LATITUDE, double LONGITUDE, string ActionName, bool IsFromMobile, int Menu_ID, DateTime? LoggedINDate = null, DateTime? LoggedDateTime = null)
        {
            var jsondata = default(dynamic);
            string connectionString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("InsertGPSDetails_SP", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@BranchID", BranchID);
                        cmd.Parameters.AddWithValue("@USER_ID", USER_ID);
                        cmd.Parameters.AddWithValue("@OBJECT_ID", OBJECT_ID);
                        cmd.Parameters.AddWithValue("@RECORD_ID", RECORD_ID);
                        cmd.Parameters.AddWithValue("@LATITUDE", LATITUDE);
                        cmd.Parameters.AddWithValue("@LONGITUDE", LONGITUDE);
                        cmd.Parameters.AddWithValue("@ActionName", ActionName);
                        cmd.Parameters.AddWithValue("@IsFromMobile", IsFromMobile);
                        cmd.Parameters.AddWithValue("@Menu_ID", Menu_ID);

                        if (LoggedINDate.HasValue)
                        {
                            cmd.Parameters.AddWithValue("@LoggedINDate", LocalTime(BranchID, LoggedINDate.Value));
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@LoggedINDate", DBNull.Value);
                        }

                        if (LoggedDateTime.HasValue)
                        {
                            cmd.Parameters.AddWithValue("@LoggedDateTime", LocalTime(BranchID, LoggedDateTime.Value));
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@LoggedDateTime", DBNull.Value);
                        }

                        string strHostName = Dns.GetHostName();
                        // string UserIP = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] ?? HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];

                        cmd.Parameters.AddWithValue("@HostName", strHostName);
                        // cmd.Parameters.AddWithValue("@AddressList", UserIP);
                        // cmd.Parameters.AddWithValue("@AddressFamily", UserIP);

                        if (ActionName == "Logged In")
                        {
                            string urlIP = "http://checkip.dyndns.org";
                            using (WebClient client = new WebClient())
                            {
                                string response = client.DownloadString(urlIP);
                                string[] a = response.Split(':');
                                string[] a3 = a[1].Substring(1).Split('<');
                                string a4 = a3[0];
                                cmd.Parameters.AddWithValue("@LocalIPAddress", a4);
                            }
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@LocalIPAddress", DBNull.Value);
                        }

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }

                jsondata = new { x = 1 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsondata);
        }

        public static DateTime LocalTime(int Branch_ID, DateTime servertime)
        {
            string standardTimeZone = "India Standard Time";
            string connectionString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand("GetLocalTime_SP", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                    cmd.Parameters.AddWithValue("@ServerTime", servertime);

                    SqlParameter outputParam = new SqlParameter("@StandardTimeZone", SqlDbType.NVarChar, 50)
                    {
                        Direction = ParameterDirection.Output
                    };
                    cmd.Parameters.Add(outputParam);

                    conn.Open();
                    cmd.ExecuteNonQuery();

                    standardTimeZone = outputParam.Value.ToString();
                }
            }

            DateTime localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, standardTimeZone);
            return localtime;
        }

        #endregion

        public static DateTime LocalTimes(int Branch_ID, DateTime servertime)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;

            int Timezoneid = 0;
            string StandardTimeZone = null;

            // Step 1: Get TimeZoneID for the Branch_ID using the stored procedure
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand("GetTimeZoneIDByBranchID", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);

                    conn.Open();
                    Timezoneid = Convert.ToInt32(cmd.ExecuteScalar());
                    conn.Close();
                }
            }

            // Step 2: Get TimeZone name using TimeZoneID with the stored procedure
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand("GetTimeZoneNameByRefMasterDetailID", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@RefMasterDetail_ID", Timezoneid);

                    conn.Open();
                    StandardTimeZone = cmd.ExecuteScalar() as string;
                    conn.Close();
                }
            }

            // Step 3: Fallback to "India Standard Time" if no result from the database
            StandardTimeZone = StandardTimeZone ?? "India Standard Time";

            // Step 4: Convert the server time to local time based on the retrieved time zone
            DateTime localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);

            return localtime;
        }
        #region ::: To Load Company /Mithun:::
        /// <summary>
        /// To Load Company
        /// </summary>
        public static IEnumerable<GNM_Company> LoadCompany(string constring, int LogException, string CompanyType)
        {
            List<GNM_Company> companies = new List<GNM_Company>();

            string query = @"
                        SELECT Company_ID, Company_Name, Company_Type, Company_Active
                        FROM GNM_Company
                        WHERE Company_Active = 1 AND LOWER(Company_Type) != LOWER(@CompanyType)
                        ORDER BY Company_Name";

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@CompanyType", CompanyType);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            GNM_Company company = new GNM_Company
                            {
                                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                Company_Name = reader.GetString(reader.GetOrdinal("Company_Name")),
                                Company_Type = reader.GetString(reader.GetOrdinal("Company_Type")),
                                Company_Active = reader.GetBoolean(reader.GetOrdinal("Company_Active"))
                            };
                            companies.Add(company);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return companies;
        }
        #endregion

        #region ::: LoadBranch /Mithun :::
        public static IEnumerable<GNM_Branch> LoadBranch(string constring, int LogException, bool Active, int CompanyID)
        {
            List<GNM_Branch> branches = new List<GNM_Branch>();

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Prepare SQL query
                    string query = Active
                        ? "SELECT * FROM GNM_Branch WHERE Branch_Active = 1 AND Company_ID = @CompanyID ORDER BY Branch_Name"
                        : "SELECT * FROM GNM_Branch WHERE Company_ID = @CompanyID ORDER BY Branch_Name";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);

                        // Execute query
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_Branch branch = new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                    Branch_Active = reader.GetBoolean(reader.GetOrdinal("Branch_Active")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                    // Add other fields if necessary
                                };
                                branches.Add(branch);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return branches;
        }

        #endregion
        #region::: LoadBranchLocale /Mithun:::

        public static IEnumerable<GNM_BranchLocale> LoadBranchLocale(string constring, int LogException, bool Active, int CompanyID, int UserLanguageID)
        {
            List<GNM_BranchLocale> branchLocales = new List<GNM_BranchLocale>();

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Prepare SQL query based on the 'Active' flag
                    string query = Active
                        ? @"SELECT 
                        bl.BranchLocale_ID, 
                        bl.Branch_ID, 
                        bl.Language_ID, 
                        bl.Branch_Name, 
                        b.Branch_Active, 
                        b.Company_ID 
                    FROM GNM_BranchLocale bl
                    INNER JOIN GNM_Branch b ON bl.Branch_ID = b.Branch_ID
                    WHERE b.Company_ID = @CompanyID 
                    AND b.Branch_Active = 1 
                    AND bl.Language_ID = @LanguageID
                    ORDER BY bl.Branch_Name"
                        : @"SELECT 
                        bl.BranchLocale_ID, 
                        bl.Branch_ID, 
                        bl.Language_ID, 
                        bl.Branch_Name, 
                        b.Branch_Active, 
                        b.Company_ID 
                    FROM GNM_BranchLocale bl
                    INNER JOIN GNM_Branch b ON bl.Branch_ID = b.Branch_ID
                    WHERE b.Company_ID = @CompanyID 
                    AND bl.Language_ID = @LanguageID
                    ORDER BY bl.Branch_Name";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@LanguageID", UserLanguageID);

                        // Execute the query
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_BranchLocale branchLocale = new GNM_BranchLocale
                                {
                                    Branch_Locale_ID = reader.GetInt32(reader.GetOrdinal("Branch_Locale_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                    Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                    // Optionally retrieve and assign other fields if needed
                                };

                                branchLocales.Add(branchLocale);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return branchLocales;
        }

        #endregion


        #region::: DeleteAttachments /Mithun :::
        public static string DeleteAttachments(Attachements[] dsObj, string SMP, string constring)
        {
            string Msg = string.Empty;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int i = 0; i < dsObj.Length; i++)
                    {
                        int ID = dsObj[i].ATTACHMENTDETAIL_ID;
                        if (ID != 0)
                        {
                            // Step 1: Delete the attachment record from the database
                            string query = "DELETE FROM GNM_ATTACHMENTDETAIL WHERE ATTACHMENTDETAIL_ID = @AttachmentDetailID";

                            using (SqlCommand cmd = new SqlCommand(query, conn))
                            {
                                cmd.Parameters.AddWithValue("@AttachmentDetailID", ID);
                                cmd.ExecuteNonQuery(); // Execute the delete command
                            }

                            // Step 2: Delete the file from the file system
                            string filePath = Path.Combine(SMP, dsObj[i].OBJECTID + "-" + dsObj[i].TransactionID + "-" + Common.DecryptString(dsObj[i].FILE_NAME));
                            if (File.Exists(filePath))
                            {
                                File.Delete(filePath);
                            }
                        }
                    }
                }

                Msg = "Deleted";
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Msg;
        }
        #endregion


        #region::: GetAttachmentCount /Mithun:::
        public static int GetAttachmentCount(int ObjectID, int TransactionID, int DetailID, string constring)
        {
            int AttachmentCount = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    string query = @"SELECT COUNT(*) FROM GNM_ATTACHMENTDETAIL 
                             WHERE OBJECT_ID = @ObjectID AND TRANSACTION_ID = @TransactionID AND DETAIL_ID = @DetailID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Adding parameters to avoid SQL injection
                        cmd.Parameters.AddWithValue("@ObjectID", ObjectID);
                        cmd.Parameters.AddWithValue("@TransactionID", TransactionID);
                        cmd.Parameters.AddWithValue("@DetailID", DetailID);

                        // Execute the scalar query to get the count
                        AttachmentCount = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return AttachmentCount;
        }

        #endregion



        #region::: Upload Attachment :::

        public static List<Attachements> UploadAttachment(Attachements[] ds, int TransactionID, int User_ID, int Company_ID, int DetailID, string constring)
        {
            List<Attachements> ds1 = new List<Attachements>();

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int k = 0; k < ds.Length; k++)
                    {
                        if (ds[k].FILE_NAME != null && ds[k].ATTACHMENTDETAIL_ID == 0)
                        {
                            // Decrypt filename and description
                            string decryptedFileName = Common.DecryptString(ds[k].FILE_NAME);
                            string decryptedFileDescription = Common.DecryptString(ds[k].FILEDESCRIPTION);

                            using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO GNM_ATTACHMENTDETAIL 
                        (FILENAME, FILEDESCRIPTION, UPLOADBY, UPLOADDATE, REMARKS, COMPANY_ID, OBJECT_ID, TRANSACTION_ID, DETAIL_ID, TABLE_NAME) 
                        VALUES 
                        (@FileName, @FileDescription, @UploadBy, @UploadDate, @Remarks, @CompanyID, @ObjectID, @TransactionID, @DetailID, @TableName)", conn))
                            {
                                // Define parameters
                                cmd.Parameters.AddWithValue("@FileName", decryptedFileName);
                                cmd.Parameters.AddWithValue("@FileDescription", decryptedFileDescription ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@UploadBy", User_ID);
                                cmd.Parameters.AddWithValue("@UploadDate", ds[k].UPLOADDATE);
                                cmd.Parameters.AddWithValue("@Remarks", ds[k].Remarks ?? (object)DBNull.Value); // Assuming Remarks is a property in Attachements
                                cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                                cmd.Parameters.AddWithValue("@ObjectID", ds[k].OBJECTID);
                                cmd.Parameters.AddWithValue("@TransactionID", TransactionID);
                                cmd.Parameters.AddWithValue("@DetailID", DetailID == 0 ? (object)DBNull.Value : DetailID);
                                cmd.Parameters.AddWithValue("@TableName", ds[k].Tablename ?? (object)DBNull.Value);

                                // Execute the command
                                cmd.ExecuteNonQuery();
                            }

                            // Optionally, add the processed attachment to ds1 list if needed
                            ds1.Add(ds[k]);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                return null;
            }

            return ds1;
        }


        #endregion


        #region ConvertToHours
        public static string ConvertToHours(int Minutes)
        {
            double a = Convert.ToDouble(Minutes);
            string InTimeFormat = Math.Floor(a / 60).ToString() + ":" + (Math.Floor(a % 60).ToString().Length == 1 ? "0" + Math.Floor(a % 60).ToString() : Math.Floor(a % 60).ToString());
            return InTimeFormat;
        }
        #endregion


        #region getGrpQincondition vinay n
        public static string getGrpQincondition(int userid, string connString, int LogException)
        {
            string inCondition = "";
            List<int> GroleID = new List<int>();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = @"SELECT WFRole_ID 
                    FROM GNM_WFRoleUser 
                    WHERE UserID = @UserID 
                    ORDER BY WFRoleUser_ID DESC";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@UserID", userid);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {

                                GroleID.Add(reader.GetInt32(reader.GetOrdinal("WFRole_ID")));

                            }

                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }

            for (int i = 0; i < GroleID.Count; i++)
            {
                if (i != (GroleID.Count - 1))
                {
                    inCondition += GroleID[i] + ",";
                }
                else
                {
                    inCondition += GroleID[i];
                }
            }
            return inCondition;
        }
        #endregion


        #region getWorkingHours vinay n
        public static double getWorkingHours(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            CallDateDetail detailForCallDate = new CallDateDetail();
            detailForCallDate = GetDetailsForDate(Calldate, companyID, connString, LogException);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTimeMinutes;
            double ETM = detailForCallDate.endTimeMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTimeMinutes;
            double BETM = detailForCallDate.BreakEndTimeMinute;
            double TotalBreakTime = BETM - BSTM;
            if (startTime < endTime)
            {
                return getWorkingHoursForNonNightShift(Calldate, companyID, connString, LogException);
            }
            else
            {
                return getWorkingHoursForNightShift(Calldate, companyID, connString, LogException);
            }
        }
        #endregion

        #region getWorkingHoursForNonNightShift vinay n
        public static double getWorkingHoursForNonNightShift(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            string s = string.Empty;
            try
            {
                DateTime? ActualCallDate = Calldate;
                CallDateDetail detailForCallDate = new CallDateDetail();
                detailForCallDate = GetDetailsForDate(Calldate, companyID, connString, LogException);
                double final = detailForCallDate.WorkHours;
                var startTime = detailForCallDate.startTime;
                var endTime = detailForCallDate.endTime;
                double STM = detailForCallDate.startTime.TotalMinutes;
                double ETM = detailForCallDate.endTime.TotalMinutes;
                var BreakstartTime = detailForCallDate.BreakstartTime;
                var BreakEndTime = detailForCallDate.BreakEndTime;
                double BSTM = detailForCallDate.BreakstartTime.TotalMinutes;
                double BETM = detailForCallDate.BreakEndTime.TotalMinutes;
                double TotalBreakTime = BETM - BSTM;
                double timeFromCallDate = 0.0;
                double timeFromToday = 0.0;

                if (Calldate.Value.Date == DateTime.Now.Date && Calldate.Value.TimeOfDay < endTime && DateTime.Now.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
                {
                    if (DateTime.Now.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakstartTime && DateTime.Now.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = BSTM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - BETM);
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes);
                    }
                }
                else if (Calldate.Value.Date == DateTime.Now.Date && Calldate.Value.TimeOfDay < endTime && DateTime.Now.TimeOfDay > endTime && Calldate.Value.TimeOfDay > startTime)
                {
                    if (Calldate.Value.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                    }
                    else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = (ETM - BETM);
                    }
                    else if (Calldate.Value.TimeOfDay > BreakEndTime)
                    {
                        timeFromToday = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                }
                else if (Calldate.Value.Date == DateTime.Now.Date && DateTime.Now.TimeOfDay < endTime && Calldate.Value.TimeOfDay < startTime)
                {
                    if (DateTime.Now.TimeOfDay < BreakstartTime)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - STM;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakstartTime && DateTime.Now.TimeOfDay < BreakEndTime)
                    {
                        timeFromToday = BSTM - STM;
                    }
                    else if (DateTime.Now.TimeOfDay > BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - STM) - (BETM - BSTM);
                    }
                }
                else if (Calldate.Value.Date == DateTime.Now.Date && DateTime.Now.TimeOfDay > endTime && Calldate.Value.TimeOfDay < startTime)
                {
                    timeFromToday = final;
                }
                if (Calldate.Value.Date < DateTime.Now.Date)
                {
                    if (Calldate.Value.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
                    {
                        if (Calldate.Value.TimeOfDay < BreakstartTime)
                        {
                            timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                        }
                        else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                        {
                            timeFromCallDate = ETM - BETM;
                        }
                        else if (Calldate.Value.TimeOfDay > BreakEndTime)
                        {
                            timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                        }
                        Calldate = Calldate.Value.AddDays(1);
                    }
                    else if (Calldate.Value.TimeOfDay < startTime)
                    {
                        timeFromCallDate = final;
                        Calldate = Calldate.Value.AddDays(1);
                    }
                    else if (Calldate.Value.TimeOfDay > endTime)
                    {
                        Calldate = Calldate.Value.AddDays(1);
                    }
                }
                double TotalWorkingHours = 0.0;
                int yearChng = Calldate.Value.Year;
                CallDateDetail detail = new CallDateDetail();
                detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
                while (Calldate.Value.Date < DateTime.Now.Date)
                {
                    string day = Calldate.Value.DayOfWeek.ToString();
                    int year = Calldate.Value.Year;
                    if (Calldate.Value.Year != yearChng)
                    {
                        detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
                    }
                    if (detail.WorkDays.Contains(day))
                    {
                        TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                    }
                    Calldate = Calldate.Value.AddDays(1);
                    yearChng = Calldate.Value.Year;
                }
                CallDateDetail todaysDetail = new CallDateDetail();
                todaysDetail = GetDetailsForDate(DateTime.Now, companyID, connString, LogException);
                if (DateTime.Now.TimeOfDay > todaysDetail.startTime && DateTime.Now.TimeOfDay < todaysDetail.endTime && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    if (DateTime.Now.TimeOfDay < todaysDetail.BreakstartTime)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > todaysDetail.BreakstartTime && DateTime.Now.TimeOfDay < todaysDetail.BreakEndTime)
                    {
                        timeFromToday = todaysDetail.BreakstartTime.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay > todaysDetail.BreakEndTime)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes) - (todaysDetail.BreakEndTime.TotalMinutes - todaysDetail.BreakstartTime.TotalMinutes);
                    }
                }
                if (DateTime.Now.TimeOfDay > todaysDetail.endTime && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    timeFromToday = todaysDetail.WorkHours;
                }

                double HDtime = 0.0;
                int holidayYear = 0;
                double holidayWhours = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT b.CompanyCalenderHoliday_Date
                        FROM GNM_CompanyCalender a
                        INNER JOIN GNM_CompanyCalenderHolidays b ON a.CompanyCalender_ID = b.CompanyCalender_ID
                        WHERE a.Company_ID = @CompanyID 
                        AND b.CompanyCalenderHoliday_Date >= @ActualCallDate
                        AND b.CompanyCalenderHoliday_Date <= @EndDate
                        AND a.IsGeneralShift = 1
                    ";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ActualCallDate", ActualCallDate);
                            cmd.Parameters.AddWithValue("@EndDate", DateTime.Now);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {

                                    CallDateDetail holidayDetail = new CallDateDetail();
                                    var holidayDate = reader.GetDateTime(reader.GetOrdinal("CompanyCalenderHoliday_Date"));
                                    if (holidayYear == 0 || holidayDate.Year != holidayYear)
                                    {

                                        holidayDetail = GetDetailsForDate(holidayDate, companyID, connString, LogException);
                                    }
                                    holidayWhours = holidayDetail.WorkHours;
                                    HDtime = HDtime + holidayWhours;
                                    holidayYear = holidayDate.Year;

                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }



                s = Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) / 60).ToString() + "." + ((Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString().Length == 1 ? "0" + Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString() : Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString()).ToString());
            }
            catch (Exception e) { }
            return Convert.ToDouble(s);

        }
        #endregion
        #region GetDetailsForDate vinay n
        public static CallDateDetail GetDetailsForDate(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            CallDateDetail detail = null;
            try
            {
                DateDetails Data = null;
                DateDetails DataForYear = null;
                List<DateDetails> DateDetailsList = new List<DateDetails>();

                if (DateDetailsContainer == null)
                {
                    Data = new DateDetails();
                    Data.Details = GetDetails(Calldate, companyID, connString, LogException);
                    Data.Year = Calldate.Value.Year;

                    DateDetailsList.Add(Data);
                    DateDetailsContainer = DateDetailsList;
                }
                else
                {
                    DateDetailsList.Clear();
                    DateDetailsList = (List<DateDetails>)DateDetailsContainer;
                    DataForYear = DateDetailsList.Where(Y => Y.Year == Calldate.Value.Year).FirstOrDefault();
                    if (DataForYear == null)//Details not found for a given Year
                    {
                        Data = new DateDetails();
                        Data.Details = GetDetails(Calldate, companyID, connString, LogException);
                        Data.Year = Calldate.Value.Year;

                        DateDetailsList.Add(Data);
                        DateDetailsContainer = DateDetailsList;
                    }
                }

                DataForYear = DateDetailsList.Where(Y => Y.Year == Calldate.Value.Year).FirstOrDefault();
                detail = DataForYear.Details;
            }
            catch (Exception e) { }
            return detail;
        }
        #endregion
        #region GetDetails vinay n
        public static CallDateDetail GetDetails(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            CallDateDetail detail = new CallDateDetail();
            GNM_CompanyCalender Calender = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
            SELECT *
            FROM GNM_CompanyCalender
            WHERE Company_ID = @CompanyID 
            AND IsGeneralShift = 1 
            AND CompanyCalender_Year = @Year ";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@Year", Calldate.Value.Year);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    Calender = new GNM_CompanyCalender
                                    {
                                        CompanyCalender_ID = reader.GetInt32(reader.GetOrdinal("CompanyCalender_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        CompanyCalender_Year = reader.GetInt32(reader.GetOrdinal("CompanyCalender_Year")),
                                        Shift_ID = reader.GetInt32(reader.GetOrdinal("Shift_ID")),
                                        CompanyCalender_StartTime = reader.GetTimeSpan(reader.GetOrdinal("CompanyCalender_StartTime")),
                                        CompanyCalender_EndTime = reader.GetTimeSpan(reader.GetOrdinal("CompanyCalender_EndTime")),
                                        CompanyCalender_WorkingDays = reader.GetString(reader.GetOrdinal("CompanyCalender_WorkingDays")),
                                        ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                        ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                        IsGeneralShift = reader.GetBoolean(reader.GetOrdinal("IsGeneralShift")),
                                        Break_StartTime = reader.GetTimeSpan(reader.GetOrdinal("Break_StartTime")),
                                        Break_EndTime = reader.GetTimeSpan(reader.GetOrdinal("Break_EndTime")),
                                        Branch_ID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                        ShiftHours = reader.IsDBNull(reader.GetOrdinal("ShiftHours")) ? (TimeSpan?)null : reader.GetTimeSpan(reader.GetOrdinal("ShiftHours")),
                                        DoubleTimeApplicableDays = reader.GetString(reader.GetOrdinal("DoubleTimeApplicableDays")),
                                        OverTimeMinutes = reader.IsDBNull(reader.GetOrdinal("OverTimeMinutes")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("OverTimeMinutes")),
                                        DoubleTimeMinutes = reader.IsDBNull(reader.GetOrdinal("DoubleTimeMinutes")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("DoubleTimeMinutes")),
                                        ShiftType_ID = reader.IsDBNull(reader.GetOrdinal("ShiftType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ShiftType_ID")),
                                        ShiftDays = reader.IsDBNull(reader.GetOrdinal("ShiftDays")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ShiftDays")),
                                        CompanyCalendarActive = reader.IsDBNull(reader.GetOrdinal("CompanyCalendarActive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("CompanyCalendarActive")),
                                        IsByDate = reader.GetBoolean(reader.GetOrdinal("IsByDate"))
                                    };
                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                string WDays = Calender.CompanyCalender_WorkingDays; //calenderClient.GNM_CompanyCalender.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.CompanyCalender_WorkingDays).FirstOrDefault();
                var startTime = Calender.CompanyCalender_StartTime; //calenderClient.GNM_CompanyCalender.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.CompanyCalender_StartTime).FirstOrDefault();
                double STM = startTime.TotalMinutes;
                var endTime = Calender.CompanyCalender_EndTime; //calenderClient.GNM_CompanyCalender.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.CompanyCalender_EndTime).FirstOrDefault();
                double ETM = (STM < endTime.TotalMinutes) ? endTime.TotalMinutes : (1440 + endTime.TotalMinutes);
                var BreakstartTime = Calender.Break_StartTime; //calenderClient.GNM_CompanyCalender.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.Break_StartTime).FirstOrDefault();
                double BSTM = (BreakstartTime.TotalMinutes < STM) ? (1440 + BreakstartTime.TotalMinutes) : BreakstartTime.TotalMinutes;
                var BreakEndTime = Calender.Break_EndTime; //calenderClient.GNM_CompanyCalender.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.Break_EndTime).FirstOrDefault();
                double BETM = (BreakEndTime.TotalMinutes < STM) ? (1440 + BreakEndTime.TotalMinutes) : BreakEndTime.TotalMinutes;
                double TotalBreakTime = BETM - BSTM;
                double WHours = (ETM - STM) - (BETM - BSTM);
                int CallDateYear = Convert.ToInt32(Calldate.Value.Year);
                int Count = 0;
                List<string> WDlist = new List<string>();
                foreach (string a in WDays.Split(','))
                {
                    Count++;
                    if (a == "1")
                    {
                        switch (Count)
                        {
                            case 1: WDlist.Add("Monday"); break;
                            case 2: WDlist.Add("Tuesday"); break;
                            case 3: WDlist.Add("Wednesday"); break;
                            case 4: WDlist.Add("Thursday"); break;
                            case 5: WDlist.Add("Friday"); break;
                            case 6: WDlist.Add("Saturday"); break;
                            case 7: WDlist.Add("Sunday"); break;
                        }
                    }
                };
                detail.startTime = startTime;
                detail.endTime = endTime;
                detail.BreakstartTime = BreakstartTime;
                detail.BreakEndTime = BreakEndTime;
                detail.startTimeMinutes = STM;
                detail.endTimeMinutes = ETM; ;
                detail.BreakstartTimeMinutes = BSTM;
                detail.BreakEndTimeMinute = BETM;
                detail.WorkDays = WDlist;
                detail.WorkHours = WHours;
            }
            catch (Exception e) { }
            return detail;
        }
        #endregion
        #region getWorkingHoursForNightShift vinay n
        public static double getWorkingHoursForNightShift(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            string s = string.Empty;
            try
            {
                DateTime? ActualCallDate = Calldate;
                CallDateDetail detailForCallDate = new CallDateDetail();
                detailForCallDate = GetDetailsForDate(Calldate, companyID, connString, LogException);
                double final = detailForCallDate.WorkHours;
                var startTime = detailForCallDate.startTime;
                var endTime = detailForCallDate.endTime;
                double STM = detailForCallDate.startTimeMinutes;
                double ETM = detailForCallDate.endTimeMinutes;
                var BreakstartTime = detailForCallDate.BreakstartTime;
                var BreakEndTime = detailForCallDate.BreakEndTime;
                double BSTM = detailForCallDate.BreakstartTimeMinutes;
                double BETM = detailForCallDate.BreakEndTimeMinute;
                double TotalBreakTime = BETM - BSTM;
                double timeFromCallDate = 0.0;
                double timeFromToday = 0.0;

                if (Calldate.Value.Date == DateTime.Now.Date)
                {
                    if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (DateTime.Now.TimeOfDay.TotalMinutes + 1440) < ETM)
                    {
                        if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BSTM)
                        {
                            timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                        }
                        else if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BETM)
                        {
                            timeFromToday = ((DateTime.Now.TimeOfDay.TotalMinutes + 1440) - BETM);
                        }
                        else if (Calldate.Value.TimeOfDay.TotalMinutes + 1440 > BETM)
                        {
                            timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                        }
                    }
                    else if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (DateTime.Now.TimeOfDay.TotalMinutes + 1440) > ETM)
                    {
                        timeFromToday = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                    }
                    else if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes) < STM && (DateTime.Now.TimeOfDay.TotalMinutes) > STM)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - STM;
                    }
                    else if (Calldate.Value.Date == DateTime.Now.Date && (Calldate.Value.TimeOfDay.TotalMinutes) > STM && (DateTime.Now.TimeOfDay.TotalMinutes) > STM)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                }
                if (Calldate.Value.Date < DateTime.Now.Date)
                {
                    if (Calldate.Value.TimeOfDay.TotalMinutes < ETM && (Calldate.Value.TimeOfDay.TotalMinutes) > STM)
                    {
                        if (Calldate.Value.TimeOfDay.TotalMinutes < BSTM)
                        {
                            timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                        }
                        else if (Calldate.Value.TimeOfDay.TotalMinutes > BSTM && Calldate.Value.TimeOfDay.TotalMinutes < BETM)
                        {
                            timeFromCallDate = ETM - BETM;
                        }
                        else if (Calldate.Value.TimeOfDay.TotalMinutes > BETM)
                        {
                            timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                        }
                        Calldate = Calldate.Value.AddDays(1);
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes < STM)
                    {
                        if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM)
                        {
                            if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BSTM)
                            {
                                timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                            }
                            else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BETM)
                            {
                                timeFromCallDate = ETM - (BETM);
                            }
                            else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BETM)
                            {
                                timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                            }
                            //Calldate = Calldate.Value.AddDays(1);
                        }
                        else
                        {
                            timeFromCallDate = final;
                            Calldate = Calldate.Value.AddDays(1);
                        }
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes > ETM)
                    {
                        Calldate = Calldate.Value.AddDays(1);
                    }
                }
                double TotalWorkingHours = 0.0;
                int yearChng = Calldate.Value.Year;
                CallDateDetail detail = new CallDateDetail();
                detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
                double temp = 0.0;
                while (Calldate.Value.Date < DateTime.Now.Date)
                {
                    string day = Calldate.Value.DayOfWeek.ToString();
                    int year = Calldate.Value.Year;
                    if (Calldate.Value.Year != yearChng)
                    {
                        detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
                    }
                    if (detail.WorkDays.Contains(day))
                    {
                        TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                        temp = detail.WorkHours;
                    }
                    Calldate = Calldate.Value.AddDays(1);
                    yearChng = Calldate.Value.Year;
                }
                CallDateDetail todaysDetail = new CallDateDetail();
                todaysDetail = GetDetailsForDate(DateTime.Now, companyID, connString, LogException);
                if (DateTime.Now.TimeOfDay.TotalMinutes > todaysDetail.startTimeMinutes && DateTime.Now.TimeOfDay.TotalMinutes < todaysDetail.endTimeMinutes && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    if (DateTime.Now.TimeOfDay.TotalMinutes < todaysDetail.BreakstartTimeMinutes)
                    {
                        timeFromToday = DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay.TotalMinutes > todaysDetail.BreakstartTimeMinutes && DateTime.Now.TimeOfDay.TotalMinutes < todaysDetail.BreakEndTimeMinute)
                    {
                        timeFromToday = todaysDetail.BreakstartTimeMinutes - todaysDetail.startTimeMinutes;
                    }
                    else if (DateTime.Now.TimeOfDay.TotalMinutes > todaysDetail.BreakEndTimeMinute)
                    {
                        timeFromToday = (DateTime.Now.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes);
                    }
                }
                else if ((DateTime.Now.TimeOfDay.TotalMinutes + 1440) > todaysDetail.startTimeMinutes && (DateTime.Now.TimeOfDay.TotalMinutes + 1440) < todaysDetail.endTimeMinutes && DateTime.Now.Date != ActualCallDate.Value.Date)
                {
                    timeFromToday = ((DateTime.Now.TimeOfDay.TotalMinutes + 1440) - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes) - temp;
                    DateTime Tday = ActualCallDate.Value.AddDays(1);
                    if (Tday.Date == DateTime.Now.Date)
                    {
                        timeFromToday = timeFromToday - todaysDetail.WorkHours;
                    }
                }
                double HDtime = 0.0;
                int holidayYear = 0;
                double holidayWhours = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT b.CompanyCalenderHoliday_Date
                        FROM GNM_CompanyCalender a
                        INNER JOIN GNM_CompanyCalenderHolidays b ON a.CompanyCalender_ID = b.CompanyCalender_ID
                        WHERE a.Company_ID = @CompanyID 
                        AND b.CompanyCalenderHoliday_Date >= @ActualCallDate
                        AND b.CompanyCalenderHoliday_Date <= @EndDate
                        AND a.IsGeneralShift = 1
                    ";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ActualCallDate", ActualCallDate);
                            cmd.Parameters.AddWithValue("@EndDate", DateTime.Now);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {

                                    CallDateDetail holidayDetail = new CallDateDetail();
                                    var holidayDate = reader.GetDateTime(reader.GetOrdinal("CompanyCalenderHoliday_Date"));
                                    if (holidayYear == 0 || holidayDate.Year != holidayYear)
                                    {

                                        holidayDetail = GetDetailsForDate(holidayDate, companyID, connString, LogException);
                                    }
                                    holidayWhours = holidayDetail.WorkHours;
                                    HDtime = HDtime + holidayWhours;
                                    holidayYear = holidayDate.Year;

                                }

                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }




                s = Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) / 60).ToString() + "." + ((Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString().Length == 1 ? "0" + Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString() : Math.Floor((timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime) % 60).ToString()).ToString());
            }
            catch (Exception e) { }
            return Convert.ToDouble(s.Contains('-') ? "0" : s);
        }
        #endregion

        #region
        public static string getStatusIDs(int StatusID, int WorkFlowID, string connString, int LogException)
        {
            CoreProductMasterServices.WF_WFStepStatus StepStutus = null;
            IEnumerable<WF_WFSteps> Steps = null;
            List<WF_WFSteps> StepsList = new List<WF_WFSteps>();
            string statusIds = string.Empty;

            List<int> CloseStepID = new List<int>();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "SELECT * FROM GNM_WFStepStatus WHERE WFStepStatus_ID=@WFStepStatus_ID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("WFStepStatus_ID", StatusID);



                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                StepStutus = new CoreProductMasterServices.WF_WFStepStatus
                                {
                                    WFStepStatus_ID = reader.GetInt32(0),
                                    WFStepStatus_Nm = reader.GetString(1),
                                    StepStatusCode = reader.GetString(2)
                                };


                            }

                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = @"SELECT *
                        FROM GNM_WFSteps
                        WHERE [WFStepStatus_ID] = @StepStatusID AND [WorkFlow_ID] = @WorkFlowID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@StepStatusID", StepStutus.WFStepStatus_ID);
                        cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);



                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                WF_WFSteps step = new WF_WFSteps
                                {
                                    WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                    WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                    WFStep_Name = reader.GetString(reader.GetOrdinal("WFStep_Name")),
                                    WFStepType_ID = reader.GetInt32(reader.GetOrdinal("WFStepType_ID")),
                                    WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                                    WFStep_IsActive = reader.GetBoolean(reader.GetOrdinal("WFStep_IsActive")),
                                    BranchCode = reader.IsDBNull(reader.GetOrdinal("BranchCode")) ? null : reader.GetString(reader.GetOrdinal("BranchCode"))
                                };
                                StepsList.Add(step);

                            }
                            Steps = StepsList.AsEnumerable();
                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }


            bool closedInd = false;
            int EndStepTypeID = 0;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string endStepTypeQuery = @"SELECT [WFStepType_ID] 
                                   FROM GNM_WFStepType
                                   WHERE UPPER([WFStepType_Nm]) = 'END'";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(endStepTypeQuery, conn))
                    {
                        cmd.CommandType = CommandType.Text;




                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        object result = cmd.ExecuteScalar();
                        EndStepTypeID = result != null ? Convert.ToInt32(result) : 0;
                        conn.Close();
                        if (EndStepTypeID > 0)
                        {
                            string closeStepQuery = @"SELECT [WFSteps_ID] 
                                     FROM GNM_WFSteps
                                     WHERE [WFStepType_ID] = @EndStepTypeID AND [WorkFlow_ID] = @WorkFlowID";
                            using (SqlCommand command = new SqlCommand(closeStepQuery, conn))
                            {
                                command.Parameters.AddWithValue("@EndStepTypeID", EndStepTypeID);
                                command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        CloseStepID.Add(reader.GetInt32(reader.GetOrdinal("WFSteps_ID")));
                                    }
                                }
                            }
                        }


                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }

            List<int> CloseStepIDStore = new List<int>();
            CloseStepIDStore.AddRange(CloseStepID);

            int ID = 0;
            for (int step = 0; step < Steps.Count(); step++)
            {
                int x = 0;
                while (x < CloseStepID.Count())
                {
                    if (Steps.ElementAt(step).WFSteps_ID == CloseStepID[x])
                    {
                        ID = CloseStepID[x];
                        statusIds = statusIds + " " + ID + ",";
                        CloseStepID.RemoveAt(0);
                        x = 0;
                        closedInd = true;
                    }
                    else
                    {
                        statusIds = statusIds + " " + Steps.ElementAt(step).WFSteps_ID + ",";
                        break;
                    }
                }
            }

            if (closedInd)
            {
                statusIds = " WFNextStep_ID in(" + statusIds + "0)";
            }
            else
            {
                statusIds = " WFSteps_ID in(" + statusIds + "0) and Action_Chosen is null";
            }

            return statusIds;
        }
        #endregion


        #region CheckPartySpecific vinay n
        public static bool CheckPartySpecific(int Party_ID, int CallComplexity_ID, int? CallPriority_ID, int CompanyID, string connString, int LogException)
        {
            List<HD_ServiceLevelAgreement> Agreement = new List<HD_ServiceLevelAgreement>();
            string query = @"
            SELECT *
            FROM HD_ServiceLevelAgreement
            WHERE Company_ID = @CompanyID 
                AND Party_ID = @PartyID
                AND CallComplexity_ID = @CallComplexityID
                AND CallPriority_ID = @CallPriorityID";
            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;




                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                HD_ServiceLevelAgreement agreement = new HD_ServiceLevelAgreement
                                {
                                    ServiceLevelAgreement_ID = reader.GetInt32(reader.GetOrdinal("ServiceLevelAgreement_ID")),
                                    CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                    CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                    ServiceLevelAgreement_Hours = reader.GetDecimal(reader.GetOrdinal("ServiceLevelAgreement_Hours")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    ServiceLevelAgreementHours_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceLevelAgreementHours_IsActive")),
                                    Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                };
                                Agreement.Add(agreement);
                            }

                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }

            if (Agreement.Count() != 0) return true; else return false;
        }
        #endregion

        #region ::: GetObjectID :::
        public static int GetObjectID(string name, string constring, int LogException)
        {
            int ObjectID = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Create the SQL query to retrieve the Object_ID based on the Object_Name
                    string query = "SELECT Object_ID FROM GNM_Object WHERE UPPER(Object_Name) = @name";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Add parameter to prevent SQL injection
                        cmd.Parameters.AddWithValue("@name", name.ToUpper());

                        // Execute the query and get the Object_ID (or default value if not found)
                        var result = cmd.ExecuteScalar();

                        if (result != null)
                        {
                            ObjectID = Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return ObjectID;
        }
        #endregion

        #region ::: getRegionName :::
        public static string getRegionName(int userLanguageID, int generalLanguageID, int? Branch_ID, string connString, int LogException)

        {

            string RegionName = "";

            try

            {

                List<GNM_Branch> BranchList = new List<GNM_Branch>();

                using (var conn = new SqlConnection(connString))

                {

                    conn.Open();

                    string query = "SELECT * FROM GNM_Branch";

                    using (var cmd = new SqlCommand(query, conn))

                    {

                        using (var reader = cmd.ExecuteReader())

                        {

                            while (reader.Read())

                            {

                                var refMasterDetailObj = new GNM_Branch

                                {

                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),

                                    Region_ID = reader["Region_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Region_ID"]),

                                };

                                BranchList.Add(refMasterDetailObj);

                            }

                        }

                    }

                }

                List<GNM_RefMasterDetail> RefMasterDetailList = new List<GNM_RefMasterDetail>();

                using (var conn = new SqlConnection(connString))

                {

                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMasterDetail";

                    using (var cmd = new SqlCommand(query, conn))

                    {

                        using (var reader = cmd.ExecuteReader())

                        {

                            while (reader.Read())

                            {

                                var refMasterDetailObj = new GNM_RefMasterDetail

                                {

                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),

                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),

                                };

                                RefMasterDetailList.Add(refMasterDetailObj);

                            }

                        }

                    }

                }

                List<GNM_RefMasterDetailLocale> RefMasterDetailLocaleList = new List<GNM_RefMasterDetailLocale>();

                using (var conn = new SqlConnection(connString))

                {

                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMasterDetailLocale";

                    using (var cmd = new SqlCommand(query, conn))

                    {

                        using (var reader = cmd.ExecuteReader())

                        {

                            while (reader.Read())

                            {

                                var refMasterDetailObj = new GNM_RefMasterDetailLocale

                                {

                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),

                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),

                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),

                                };

                                RefMasterDetailLocaleList.Add(refMasterDetailObj);

                            }

                        }

                    }

                }

                int? RegionID = BranchList.Where(b => b.Branch_ID == Branch_ID).Select(b => b.Region_ID).FirstOrDefault();

                RegionName = userLanguageID == generalLanguageID ? RefMasterDetailList.Where(r => r.RefMasterDetail_ID == (RegionID == null ? 0 : RegionID)).Select(r => r.RefMasterDetail_Name).FirstOrDefault() : RefMasterDetailLocaleList.Where(r => r.RefMasterDetail_ID == (RegionID == null ? 0 : RegionID) && r.Language_ID == userLanguageID).Select(r => r.RefMasterDetail_Name).FirstOrDefault();

            }

            catch (Exception ex)

            {

                if (LogException == 1)

                {

                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }

            }

            return RegionName;

        }
        #endregion


    }
}
