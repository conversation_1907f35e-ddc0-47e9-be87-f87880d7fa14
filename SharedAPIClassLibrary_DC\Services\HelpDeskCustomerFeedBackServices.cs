﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using static SharedAPIClassLibrary_AMERP.HelpDeskServiceRequestServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskCustomerFeedBackServices
    {
        public static dynamic Dummy = null;
        static string AppPath = string.Empty;
        #region::: To select all Tickets and Work Order Pending for Customer Feedback:::
        /// <summary>
        /// To select all Tickets and Work Order Pending for Customer Feedback
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectALLTicketsandWOPendingforCustomerFeedBack(SelectALLTicketsandWOPendingforCustomerFeedBackList SelectALLTicketsandWOPendingforCustomerFeedBackObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            int count = 0;
            int total = 0;
            string query = "";
            var x = Dummy;
            IQueryable<CustomerFeedBack> IQCustomerFeedBack = null;
            List<CustomerFeedBack> CustomerFeedBackList = null;
            List<CustomerFeedBack> customerFeedBackList = new List<CustomerFeedBack>();
            try
            {
                int userLanguage_ID = Convert.ToInt32(SelectALLTicketsandWOPendingforCustomerFeedBackObj.UserLanguageID);
                int generalLanguage_ID = Convert.ToInt32(SelectALLTicketsandWOPendingforCustomerFeedBackObj.GeneralLanguageID);
                int Branch_ID = Convert.ToInt32(SelectALLTicketsandWOPendingforCustomerFeedBackObj.Branch);

                query = " EXEC SP_CustomerFeedback " + Branch_ID + "," + (generalLanguage_ID == userLanguage_ID ? 0 : userLanguage_ID) + "";
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CustomerFeedBack feedback = new CustomerFeedBack
                                {
                                    QuotationNumber = reader["QuotationNumber"] != DBNull.Value ? reader["QuotationNumber"].ToString() : string.Empty,
                                    JobNumber = reader["JobNumber"] != DBNull.Value ? reader["JobNumber"].ToString() : string.Empty,

                                    ServiceRequest_ID = reader["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceRequest_ID"]) : 0,
                                    RequestNumber = reader["RequestNumber"] != DBNull.Value ? reader["RequestNumber"].ToString() : string.Empty,
                                    ServiceRequestDate = reader["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(reader["ServiceRequestDate"]) : (DateTime?)null,

                                    Model = reader["Model"] != DBNull.Value ? reader["Model"].ToString() : string.Empty,
                                    SerialNumber = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : string.Empty,

                                    PartyName = reader["PartyName"] != DBNull.Value ? reader["PartyName"].ToString() : string.Empty,
                                    Product_Unique_Number = reader["Product_Unique_Number"] != DBNull.Value ? reader["Product_Unique_Number"].ToString() : string.Empty,
                                    PartyContactPerson_Mobile = reader["PartyContactPerson_Mobile"] != DBNull.Value ? reader["PartyContactPerson_Mobile"].ToString() : string.Empty,

                                    JobCard_ID = reader["JobCard_ID"] != DBNull.Value ? Convert.ToInt32(reader["JobCard_ID"]) : 0,
                                    WorkOrderDate = reader["WorkOrderDate"] != DBNull.Value ? Convert.ToDateTime(reader["WorkOrderDate"]) : (DateTime?)null,
                                    QuoteDate = reader["QuoteDate"] != DBNull.Value ? Convert.ToDateTime(reader["QuoteDate"]) : (DateTime?)null,

                                };

                                customerFeedBackList.Add(feedback);
                            }
                        }
                    }
                    var arr = from SR in customerFeedBackList
                              select new CustomerFeedBack()
                              {
                                  ServiceRequest_ID = SR.ServiceRequest_ID,
                                  SerialNumber = (SR.SerialNumber == null ? "" : SR.SerialNumber),
                                  PartyName = SR.PartyName,
                                  PartyContactPerson_Mobile = SR.PartyContactPerson_Mobile,
                                  QuotationNumber = SR.QuotationNumber,
                                  JobNumber = SR.JobNumber,
                                  ServiceRequestDate = SR.ServiceRequestDate,
                                  RequestNumber = SR.RequestNumber,
                                  Model = SR.Model,
                                  Product_Unique_Number = SR.Product_Unique_Number,
                                  JobCard_ID = SR.JobCard_ID,
                                  QuoteDate = SR.QuoteDate,
                              };

                    IQCustomerFeedBack = arr.AsQueryable<CustomerFeedBack>();
                    if (_search)
                    {
                        // First decryption
                        string firstDecryptedString = Common.DecryptString(filters);

                        // Second decryption (assuming you need to decrypt the result of the first decryption)
                        string secondDecryptedString = Common.DecryptString(firstDecryptedString);

                        // Parse the second decrypted string
                        Filters filtersObj = JObject.Parse(secondDecryptedString).ToObject<Filters>();

                        // Apply filters if any rules exist
                        if (filtersObj.rules.Count > 0)
                        {
                            IQCustomerFeedBack = IQCustomerFeedBack.FilterSearch<CustomerFeedBack>(filtersObj);
                        }
                    }
                    IQCustomerFeedBack = IQCustomerFeedBack.OrderByField<CustomerFeedBack>(sidx, sord);
                    //Session["LandingPageServiceRequestExport"] = IQCustomerFeedBack;
                    count = IQCustomerFeedBack.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }
                    x = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = count,
                        rows = (from SR in IQCustomerFeedBack
                                select new
                                {
                                    ServiceRequest_ID = SR.ServiceRequest_ID,
                                    Edit = "<a title=" + CommonFunctionalities.GetResourceString(SelectALLTicketsandWOPendingforCustomerFeedBackObj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px; cursor:pointer;' key='" + SR.ServiceRequest_ID + "' key1='" + SR.JobCard_ID + "' key2='" + SR.RequestNumber + "' key3='" + SR.JobNumber + "' class='edtCBClick'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    RequestNumber = (SR.RequestNumber),
                                    QuotationNumber = SR.QuotationNumber,
                                    ServiceRequestDate = SR.ServiceRequestDate,
                                    ServiceRequestDateSTR = SR.ServiceRequestDateSTR,
                                    JobNumber = SR.JobNumber,
                                    PartyName = (SR.PartyName),
                                    Model = (SR.Model),
                                    SerialNumber = (SR.SerialNumber == null || SR.SerialNumber == "") ? "" : (SR.SerialNumber),
                                    SR.Product_Unique_Number,
                                    SR.PartyContactPerson_Mobile,
                                    SR.QuoteDate,
                                    SR.JobCard_ID,
                                    SR.JobCardDate,
                                    SR.QuoteDatestr,
                                    SR.JobCardDateStr
                                }).ToList().Paginate(page, rows)
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }
        #endregion

        #region ::: SavecustomerFeedbackDetails :::
        /// <summary>
        /// To Save Customer Feedback Details
        /// </summary>

        public static IActionResult SavecustomerFeedbackDetails(SavecustomerFeedbackDetailsList Obj, string constring, int LogException)
        {
            string Msg = string.Empty;
            SqlConnection connection = new SqlConnection(constring); // Your connection string here
            SqlTransaction transaction = null;
            SqlDataReader reader = null;
            try
            {
                connection.Open();
                transaction = connection.BeginTransaction();

                int BranchID = Convert.ToInt32(Obj.Branch.ToString());
                //GNM_User User = (GNM_User)Session["UserDetails"];
                string GenLangCode = Obj.GeneralLanguageCode.ToString();
                string UserLangCode = Obj.UserLanguageCode.ToString();

                SqlCommand command = new SqlCommand();
                command.Connection = connection;
                command.Transaction = transaction;

                if (Obj.ServiceRequestID > 0)
                {
                    string sqlQuery = "SELECT * FROM HD_ServiceRequest WHERE ServiceRequest_ID = @ServiceRequestID";
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        cmd.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);

                        using (reader = cmd.ExecuteReader())  // Use 'reader' here for the first time
                        {
                            if (reader.Read())
                            {
                                // Populate feedbackupdate object from the SQL result
                                HD_ServiceRequest feedbackupdate = new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID")),
                                    ServiceRequestNumber = reader.GetString(reader.GetOrdinal("ServiceRequestNumber")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber")),
                                    CustomerRating = reader.GetByte(reader.GetOrdinal("CustomerRating")),
                                    InformationCollected = reader.GetString(reader.GetOrdinal("InformationCollected")),
                                    IsNegetiveFeedback = reader.GetBoolean(reader.GetOrdinal("IsNegetiveFeedback")),
                                };

                                // Update feedbackupdate with the new values
                                feedbackupdate.CustomerRating = (byte)Obj.CustomerRating;
                                feedbackupdate.InformationCollected = Common.DecryptString(Obj.Feedback);
                                feedbackupdate.CustomerFeedbackDate = Common.LocalTime(BranchID, Convert.ToDateTime(DateTime.Now), constring);
                                feedbackupdate.IsNegetiveFeedback = Obj.IsNegetiveFeedback;

                                // Update Service Request Feedback
                                command.CommandText = "UPDATE HD_ServiceRequest SET CustomerRating = @CustomerRating, InformationCollected = @InformationCollected, CustomerFeedbackDate = @CustomerFeedbackDate, IsNegetiveFeedback = @IsNegetiveFeedback WHERE ServiceRequest_ID = @ServiceRequestID";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@CustomerRating", Obj.CustomerRating);
                                command.Parameters.AddWithValue("@InformationCollected", Common.DecryptString(Obj.Feedback));
                                command.Parameters.AddWithValue("@CustomerFeedbackDate", Common.LocalTime(BranchID, DateTime.Now, constring));
                                command.Parameters.AddWithValue("@IsNegetiveFeedback", Obj.IsNegetiveFeedback);
                                command.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);
                                command.ExecuteNonQuery();

                                // Fetch Party information
                                command.CommandText = "SELECT Party_ID FROM HD_ServiceRequest WHERE ServiceRequest_ID = @ServiceRequestID";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);
                                int PartyID = Convert.ToInt32(command.ExecuteScalar());

                                // Fetch Party Contact Person and Locale data
                                command.CommandText = "SELECT Party_Name FROM GNM_Party WHERE Party_ID = @PartyID";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@PartyID", PartyID);
                                string PartyName = command.ExecuteScalar().ToString();

                                // Get Party Locale information (assuming it exists in the same table)
                                command.CommandText = "SELECT Party_Name FROM GNM_PartyLocale WHERE Party_ID = @PartyID";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@PartyID", PartyID);
                                string PartyLocaleName = command.ExecuteScalar().ToString();

                                // Fetch Party Contact Person details
                                command.CommandText = "SELECT PartyContactPerson_Name, PartyContactPerson_Mobile FROM GNM_PartyContactPersonDetails WHERE PartyContactPerson_ID = (SELECT PartyContactPerson_ID FROM HD_ServiceRequest WHERE ServiceRequest_ID = @ServiceRequestID)";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@ServiceRequestID", Obj.ServiceRequestID);

                                string PartyContactPersonName = string.Empty;
                                string PartyContactPersonMobile = string.Empty;
                                using (reader = command.ExecuteReader())  // Reusing 'reader' here
                                {

                                    if (reader.Read())
                                    {
                                        PartyContactPersonName = reader["PartyContactPerson_Name"].ToString();
                                        PartyContactPersonMobile = reader["PartyContactPerson_Mobile"].ToString();
                                    }
                                }

                                // Use the SRcontroller to send the email (same as the original code)
                                InsertServiceRequestNegativeFeedbackEmailsList feedbackEmailData = new InsertServiceRequestNegativeFeedbackEmailsList
                                {
                                    RequestNumber = feedbackupdate.ServiceRequestNumber,
                                    Company_ID = feedbackupdate.Company_ID,
                                    Branch_ID = feedbackupdate.Branch_ID,
                                    Customer = (GenLangCode == UserLangCode ? PartyName : PartyLocaleName),
                                    Model_Id = Convert.ToInt32(feedbackupdate.Model_ID),
                                    SerialNumber = feedbackupdate.SerialNumber,
                                    ContactPerson = PartyContactPersonName,
                                    MobileNumber = PartyContactPersonMobile,
                                    CallDescription = feedbackupdate.CallDescription,
                                    CustomerRating = feedbackupdate.CustomerRating.ToString(),
                                    Feedback = Common.DecryptString(Obj.Feedback)
                                };

                                HelpDeskServiceRequestServices.InsertServiceRequestNegativeFeedbackEmails(feedbackEmailData, constring, LogException);
                            }
                        }
                    }
                }
                else
                {
                    // Update JobCard feedback
                    command.CommandText = @"
                    UPDATE SRT_JobCardHeader 
                    SET CustomerRating = @CustomerRating, 
                        CustomerFeedBack = @CustomerFeedBack, 
                        CustomerFeedBackDate = @CustomerFeedBackDate, 
                        IsNegetiveFeedBack = @IsNegetiveFeedBack 
                    WHERE JobCard_ID = @JobCardID";

                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@CustomerRating", Obj.CustomerRating);
                    command.Parameters.AddWithValue("@CustomerFeedBack", Common.DecryptString(Obj.Feedback));
                    command.Parameters.AddWithValue("@CustomerFeedBackDate", Common.LocalTime(BranchID, DateTime.Now, constring));
                    command.Parameters.AddWithValue("@IsNegetiveFeedBack", Obj.IsNegetiveFeedback);
                    command.Parameters.AddWithValue("@JobCardID", Obj.JobCardID);
                    command.ExecuteNonQuery();

                    // Fetch JobCard details
                    int? PartyID = null;
                    int? CompanyID = null;
                    int? ModelID = null;
                    string JobCardNumber = string.Empty;
                    string SerialNumber = string.Empty;
                    string CustomerComplaint = string.Empty;

                    command.CommandText = @"
                    SELECT Party_ID, Company_ID, Branch_ID, Model_ID, JobCardNumber, SerialNumber, CustomerComplaint 
                    FROM FSMCore_SRT_JobCardHeader 
                    WHERE JobCard_ID = @JobCardID";

                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@JobCardID", Obj.JobCardID);

                    reader = command.ExecuteReader();
                    if (reader.Read())
                    {
                        PartyID = reader["Party_ID"] as int?;
                        CompanyID = reader["Company_ID"] as int?;
                        BranchID = (int)(reader["Branch_ID"] as int?);
                        ModelID = reader["Model_ID"] as int?;
                        JobCardNumber = reader["JobCardNumber"].ToString();
                        SerialNumber = reader["SerialNumber"].ToString();
                        CustomerComplaint = reader["CustomerComplaint"].ToString();
                    }
                    reader.Close();

                    // Fetch Party Name and Locale
                    string PartyName = string.Empty;
                    string PartyLocaleName = string.Empty;

                    if (PartyID.HasValue)
                    {
                        command.CommandText = "SELECT Party_Name FROM GNM_Party WHERE Party_ID = @PartyID";
                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@PartyID", PartyID.Value);
                        PartyName = command.ExecuteScalar()?.ToString() ?? string.Empty;

                        command.CommandText = "SELECT Party_Name FROM GNM_PartyLocale WHERE Party_ID = @PartyID";
                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@PartyID", PartyID.Value);
                        PartyLocaleName = command.ExecuteScalar()?.ToString() ?? PartyName;
                    }

                    // Fetch Party Contact Person Details
                    int? PartyContactPersonID = null;
                    string PartyContactPersonName = string.Empty;
                    string PartyContactPersonMobile = string.Empty;

                    command.CommandText = "SELECT PartyContactPersonDetails_ID FROM FSMCore_SRT_JobCardHeader WHERE JobCard_ID = @JobCardID";
                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@JobCardID", Obj.JobCardID);
                    PartyContactPersonID = command.ExecuteScalar() as int?;

                    if (PartyContactPersonID.HasValue)
                    {
                        command.CommandText = @"
                        SELECT PartyContactPerson_Name, PartyContactPerson_Mobile 
                        FROM GNM_PartyContactPersonDetails 
                        WHERE PartyContactPerson_ID = @PartyContactPersonID";
                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@PartyContactPersonID", PartyContactPersonID.Value);

                        reader = command.ExecuteReader();
                        if (reader.Read())
                        {
                            PartyContactPersonName = reader["PartyContactPerson_Name"].ToString();
                            PartyContactPersonMobile = reader["PartyContactPerson_Mobile"].ToString();
                        }
                        reader.Close();
                    }

                    // Prepare object for the InsertServiceRequestNegativeFeedbackEmailsList class
                    var feedbackEmailObj = new InsertServiceRequestNegativeFeedbackEmailsList
                    {
                        RequestNumber = JobCardNumber,
                        Company_ID = CompanyID ?? 0,
                        Branch_ID = BranchID,
                        Customer = (GenLangCode == UserLangCode) ? PartyName : PartyLocaleName,
                        Model_Id = ModelID ?? 0,
                        ProductType = string.Empty, // If you have ProductType data, update here
                        SerialNumber = SerialNumber,
                        ContactPerson = PartyContactPersonName,
                        MobileNumber = PartyContactPersonMobile,
                        CallDescription = CustomerComplaint,
                        CustomerRating = Obj.CustomerRating.ToString(),
                        Feedback = Common.DecryptString(Obj.Feedback)
                    };

                    // Send email using the new method
                    var response = HelpDeskServiceRequestServices.InsertServiceRequestNegativeFeedbackEmails(
                        feedbackEmailObj,
                        constring,
                        LogException
                    );
                }




                // Commit transaction if everything is successful
                transaction.Commit();
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                // Rollback the transaction in case of an error
                if (transaction != null)
                {
                    transaction.Rollback();
                }

                // Log exception if necessary
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                Msg = string.Empty;
            }
            finally
            {
                connection.Close();
            }

            return new JsonResult(Msg);
        }

        #endregion

        #region ::: To Export:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        public static async Task<object> Export(SelectALLTicketsandWOPendingforCustomerFeedBackList ExportObj, string connString, int LogException, string filter, string advanceFilter, string sidx, string sord)
        {
            DataTable dt = new DataTable();
            int cnt = 0;
            SqlConnection connection = new SqlConnection(connString);
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                int userid = ExportObj.User_ID;
                int CompanyID = ExportObj.Company_ID;

                string GenLangCode = ExportObj.GeneralLanguageCode.ToString();
                string UserLangCode = ExportObj.UserLanguageCode.ToString();
                // List<ServiceRequest> SRequestReport = new List<ServiceRequest>();
                List<CustomerFeedBack> CustomerFeedBackList = new List<CustomerFeedBack>();
                //SRequestReport = WorkFlowClient.Database.SqlQuery(typeof(ServiceRequest), finalQuery).Cast<ServiceRequest>().ToList();   
                int userLanguage_ID = Convert.ToInt32(ExportObj.UserLanguageID);
                int generalLanguage_ID = Convert.ToInt32(ExportObj.GeneralLanguageID);
                int Branch_ID = Convert.ToInt32(ExportObj.Branch);

                string query = " EXEC SP_CustomerFeedback " + Branch_ID + "," + (generalLanguage_ID == userLanguage_ID ? 0 : userLanguage_ID) + "";
                //CustomerFeedBackList = ServiceRequestClient.Database.SqlQuery(typeof(CustomerFeedBack), query).Cast<CustomerFeedBack>().ToList();
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.CommandType = CommandType.Text;
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CustomerFeedBack feedback = new CustomerFeedBack
                            {
                                QuotationNumber = reader["QuotationNumber"]?.ToString(),
                                JobNumber = reader["JobNumber"]?.ToString(),
                                JobCardDate = reader["JobCardDate"] != DBNull.Value ? Convert.ToDateTime(reader["JobCardDate"]) : (DateTime?)null,
                                JobCardDateStr = reader["JobCardDateStr"]?.ToString(),
                                ServiceRequest_ID = reader["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceRequest_ID"]) : 0,
                                RequestNumber = reader["RequestNumber"]?.ToString(),
                                ServiceRequestDate = reader["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(reader["ServiceRequestDate"]) : (DateTime?)null,
                                ServiceRequestDateSTR = reader["ServiceRequestDateSTR"]?.ToString(),
                                Model = reader["Model"]?.ToString(),
                                SerialNumber = reader["SerialNumber"]?.ToString(),
                                PartyName = reader["PartyName"]?.ToString(),
                                Product_Unique_Number = reader["Product_Unique_Number"]?.ToString(),
                                PartyContactPerson_Mobile = reader["PartyContactPerson_Mobile"]?.ToString(),
                                JobCard_ID = reader["JobCard_ID"] != DBNull.Value ? Convert.ToInt32(reader["JobCard_ID"]) : 0,
                                WorkOrderDate = reader["WorkOrderDate"] != DBNull.Value ? Convert.ToDateTime(reader["WorkOrderDate"]) : (DateTime?)null,
                                QuoteDate = reader["QuoteDate"] != DBNull.Value ? Convert.ToDateTime(reader["QuoteDate"]) : (DateTime?)null,
                                WorkOrderDatestr = reader["WorkOrderDatestr"]?.ToString(),
                                QuoteDatestr = reader["QuoteDatestr"]?.ToString()
                            };

                            CustomerFeedBackList.Add(feedback);
                        }
                    }
                }
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "ServiceRequestNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuotationNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "CustomerQuotationDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "JobCardNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "JobCardDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "PartyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "PartyPhone").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "model").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "serialnumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "LicenseNo").ToString());

                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("ServiceRequestNumber");        //1      
                dtAlignment.Columns.Add("Date");              //2 
                dtAlignment.Columns.Add("QuotationNumber");//3               
                dtAlignment.Columns.Add("CustomerQuotationDate");//4
                dtAlignment.Columns.Add("JobCardNumber");//5
                dtAlignment.Columns.Add("JobCardDate");//6
                dtAlignment.Columns.Add("PartyName");  //7
                dtAlignment.Columns.Add("PartyPhone");  //8
                dtAlignment.Columns.Add("model");  //9
                dtAlignment.Columns.Add("serialnumber");  //10
                dtAlignment.Columns.Add("LicenseNo");  //11

                dtAlignment.Rows.Add(0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                cnt = CustomerFeedBackList.Count();
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(CustomerFeedBackList.ElementAt(i).RequestNumber, (CustomerFeedBackList.ElementAt(i).ServiceRequestDate == null) ? "" : Convert.ToDateTime(CustomerFeedBackList.ElementAt(i).ServiceRequestDate).ToString("dd-MMM-yyyy"), CustomerFeedBackList.ElementAt(i).QuotationNumber, CustomerFeedBackList.ElementAt(i).QuoteDate == null ? "" : Convert.ToDateTime(CustomerFeedBackList.ElementAt(i).QuoteDate).ToString("dd-MMM-yyyy"), CustomerFeedBackList.ElementAt(i).JobNumber, CustomerFeedBackList.ElementAt(i).JobCardDate == null ? "" : Convert.ToDateTime(CustomerFeedBackList.ElementAt(i).JobCardDate).ToString("dd-MMM-yyyy"), CustomerFeedBackList.ElementAt(i).PartyName, CustomerFeedBackList.ElementAt(i).PartyContactPerson_Mobile, CustomerFeedBackList.ElementAt(i).Model, CustomerFeedBackList.ElementAt(i).SerialNumber, CustomerFeedBackList.ElementAt(i).Product_Unique_Number);
                }

                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportObj.Company_ID,
                    Branch = ExportObj.Branch,
                    dt1 = dt, // Exporting the main DataTable
                    dt = dt,      // Criteria (if needed)
                    FileName = "CustomerFeedBack",  // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "CustomerFeedBack").ToString(),
                    exprtType = ExportObj.exprtType,  // Assuming 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };

                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;

                //DocumentExport method
                //DocumentExport.Export(exprtType, dt, dtAlignment, HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CustomerFeedBack1").ToString(), HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CustomerFeedBack").ToString());
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), Convert.ToInt32(Session["Branch"]), User.User_ID, Common.GetObjectID("CustomerFeedback"), 0, 0, 0, "Customer FeedBack-Export", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return null;
        }
        #endregion





        #region ::: Select Case Registration:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectServiceRequest(SelectServiceRequestaList Obj, string constring, int LogException, string HelpDesk, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query) //1--My Que 2--Group 3--All    4--From Summary
        {
            var jsonobj = Dummy;
            int count = 0;
            int total = 0;
            bool IsAllocate = false;
            int WorkFlowID = WorkFlowCommon.GetWorkFlowID("Case Registration", HelpDesk, constring, LogException);
            IEnumerable<WF_WFStepStatus> StepStatus = null;
            List<int> TransactionIDList = new List<int>();
            IQueryable<ServiceRequest> iQServiceReq = null;
            IEnumerable<ServiceRequest> ServiceReq = null;
            IEnumerable<ServiceRequest> ServiceReqAll = null;
            string query = string.Empty;
            string finalquery = string.Empty;
            string wherecondition = string.Empty;
            string inCondition = string.Empty;
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                int userid = Obj.User_ID;
                int Company_ID = Obj.Company_ID;
                string CompanyID = Convert.ToString(Obj.CompanyIDs);
                if (CompanyID != "") { CompanyID = Convert.ToString(Obj.CompanyIDs); } else { CompanyID = Convert.ToString(Obj.Company_ID); }
                string BranchID = Convert.ToString(Obj.BranchIDs);
                if (BranchID != "") { BranchID = Convert.ToString(Obj.BranchIDs); } else { BranchID = Convert.ToString(Convert.ToInt32(Obj.Branch)); }


                int LangID = Obj.Language_ID;
                string GenLangCode = Obj.GeneralLanguageCode.ToString();
                string UserLangCode = Obj.UserLanguageCode.ToString();
                bool IsAdmin = WorkFlowCommon.chkforAdmin(userid, "Case Registration", HelpDesk, constring, LogException);

                List<WF_WFStepStatus> StepStatusL = new List<WF_WFStepStatus>();

                string stepquery = "SELECT WFStepStatus_ID, WFStepStatus_Nm, StepStatusCode FROM WF_WFStepStatus";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(stepquery, connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFStepStatus status = new WF_WFStepStatus
                                {
                                    WFStepStatus_ID = reader["WFStepStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFStepStatus_ID"]),
                                    WFStepStatus_Nm = reader["WFStepStatus_Nm"] == DBNull.Value ? null : reader["WFStepStatus_Nm"].ToString(),
                                    StepStatusCode = reader["StepStatusCode"] == DBNull.Value ? null : reader["StepStatusCode"].ToString(),
                                };
                                StepStatusL.Add(status);
                            }
                        }
                    }
                }
                StepStatus = StepStatusL.AsEnumerable();

                int EndStepTypeID = 0;
                string endquery = @"
                SELECT TOP 1 WFStepType_ID 
                FROM [GNM_WFStepType] 
                WHERE UPPER(WFStepType_Nm) = 'END'";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(endquery, connection))
                    {
                        object result = command.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            EndStepTypeID = Convert.ToInt32(result);
                        }
                    }
                }

                List<int> CloseStepID = new List<int>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Add parameters to prevent SQL injection
                        command.Parameters.AddWithValue("@EndStepTypeID", EndStepTypeID);
                        command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // Read WFSteps_ID and add to the list
                                CloseStepID.Add(reader.GetInt32(reader.GetOrdinal("WFSteps_ID")));
                            }
                        }
                    }
                }
                int ClosedStatusID = HelpDeskCommon.GetEndStepStatusID(WorkFlowCommon.GetWorkFlowID("Case Registration", HelpDesk, constring, LogException), constring, LogException);
                List<int> CloseStepIDStore = new List<int>();
                CloseStepIDStore.AddRange(CloseStepID);
                //Session["IsClosed"] = (ClosedStatusID == StatusID ? "true" : "false");

                bool isBranchSpecific = false; // Default value
                string isBranchSpecificquery = "SELECT TOP 1 AllQueue_Filter_IsBranch FROM [GNM_WorkFlow] WHERE WorkFlow_ID = @WorkFlowID";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(isBranchSpecificquery, connection))
                    {
                        // Add the parameter to prevent SQL injection
                        command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);

                        // Execute the query and retrieve the result
                        object result = command.ExecuteScalar();

                        // Convert the result to a boolean, if not null
                        if (result != null && result != DBNull.Value)
                        {
                            isBranchSpecific = Convert.ToBoolean(result);
                        }
                    }
                }
                string statusIds = string.Empty;

                string assign = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Assign").ToString();
                string lockv = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Lock").ToString();
                string unlock = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "UnLock").ToString();
                string myqueue = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "MyQue").ToString();
                string grpQueue = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GroupQue").ToString();
                string closed = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Closed").ToString();
                string others = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Others").ToString();

                List<WF_WFRoleUser> WFRoleUserList = new List<WF_WFRoleUser>();

                using (var conn = new SqlConnection(constring))
                {
                    conn.Open();

                    string rolequery = "SELECT * FROM [GNM_WFRoleUser]";

                    using (var cmd = new SqlCommand(rolequery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var rObj = new WF_WFRoleUser
                                {
                                    WFRoleUser_ID = reader["WFRoleUser_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFRoleUser_ID"]),
                                    WFRole_ID = reader["WFRole_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFRole_ID"]),
                                    UserID = reader["UserID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["UserID"]),
                                    ApprovalLimit = reader["ApprovalLimit"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ApprovalLimit"]),

                                };

                                WFRoleUserList.Add(rObj);
                            }
                        }
                    }
                }

                List<WF_WFRole> WFRoleList = new List<WF_WFRole>();

                using (var conn = new SqlConnection(constring))
                {
                    conn.Open();

                    string rolequery = "SELECT * FROM GNM_WFRole";

                    using (var cmd = new SqlCommand(rolequery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var rObj = new WF_WFRole
                                {
                                    WFRole_ID = reader["WFRole_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFRole_ID"]),
                                    WorkFlow_ID = reader["WorkFlow_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WorkFlow_ID"]),
                                    WFRole_Name = reader["WFRole_Name"] == DBNull.Value ? null : reader["WFRole_Name"].ToString(),
                                    WfRole_IsAdmin = reader["WfRole_IsAdmin"] != DBNull.Value && Convert.ToBoolean(reader["WfRole_IsAdmin"]),
                                    WfRole_AutoAllocationAllowed = reader["WfRole_AutoAllocationAllowed"] != DBNull.Value && Convert.ToBoolean(reader["WfRole_AutoAllocationAllowed"]),
                                    WFRole_IsRoleExternal = reader["WFRole_IsRoleExternal"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["WFRole_IsRoleExternal"]),
                                    WFRole_ExternalCompany_ID = reader["WFRole_ExternalCompany_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["WFRole_ExternalCompany_ID"]),
                                };

                                WFRoleList.Add(rObj);
                            }
                        }
                    }
                }



                int roleID = (from user in WFRoleUserList
                              join role in WFRoleList on user.WFRole_ID equals role.WFRole_ID
                              where role.WorkFlow_ID == WorkFlowID && user.UserID == userid
                              select new
                              {
                                  user.WFRole_ID
                              }).ToArray().Select(w => w.WFRole_ID).FirstOrDefault();
                statusIds = CommonFunctionalities.getStatusIDs(ClosedStatusID, WorkFlowID, constring, LogException);
                wherecondition = " (Action_Chosen is null or" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " and sr.Company_ID in (" + CompanyID + ") AND st.WFStepStatus_Nm Like '%closed%' and sr.CustomerRating=-1 and sr.ParentIssue_ID is null ";

                if (_search)
                {
                    Filters filter = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filter.rules.Count() > 0)
                    {
                        for (int i = 0; i < filter.rules.Count(); i++)
                        {
                            if (filter.rules.ElementAt(i).field == "RequestNumber")
                            {
                                filter.rules.ElementAt(i).field = "sr.ServiceRequestNumber";
                            }
                            else if (filter.rules.ElementAt(i).field == "ServiceRequestDate")
                            {
                                filter.rules.ElementAt(i).field = "REPLACE(CONVERT(varchar(25),ServiceRequestDate,106),' ','-')";
                            }
                            else if (filter.rules.ElementAt(i).field == "IssueArea")
                            {
                                filter.rules.ElementAt(i).field = "issuearea.RefMasterDetail_Name";
                            }
                            else if (filter.rules.ElementAt(i).field == "IssueSubArea")
                            {
                                filter.rules.ElementAt(i).field = "IssueSubArea.IssueSubArea_Description";
                            }
                            else if (filter.rules.ElementAt(i).field == "Model")
                            {
                                filter.rules.ElementAt(i).field = "model.Model_Name";
                            }
                            else if (filter.rules.ElementAt(i).field == "Status")
                            {
                                filter.rules.ElementAt(i).field = "st.WFStepStatus_Nm";
                            }
                            else if (filter.rules.ElementAt(i).field == "PartyName")
                            {
                                filter.rules.ElementAt(i).field = "p.Party_Name";
                            }
                            wherecondition = wherecondition + " AND " + filter.rules.ElementAt(i).field + " like " + "'%" + filter.rules.ElementAt(i).data + "%'";
                        }
                    }
                }

                //Advance Search
                if (advnce)
                {
                    string op = "";
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    if (advnfilter.rules.Count() > 0)
                    {
                        for (int i = 0; i < advnfilter.rules.Count(); i++)
                        {
                            if (advnfilter.rules.ElementAt(i).Field == "RequestNumber")
                            {
                                advnfilter.rules.ElementAt(i).Field = "sr.ServiceRequestNumber";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "ServiceRequestDate")
                            {
                                advnfilter.rules.ElementAt(i).Field = "REPLACE(CONVERT(varchar(25),ServiceRequestDate,106),' ','-')";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "IssueArea")
                            {
                                advnfilter.rules.ElementAt(i).Field = "issuearea.RefMasterDetail_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "IssueSubArea")
                            {
                                advnfilter.rules.ElementAt(i).Field = "IssueSubArea.IssueSubArea_Description";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Model")
                            {
                                advnfilter.rules.ElementAt(i).Field = "model.Model_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Status")
                            {
                                advnfilter.rules.ElementAt(i).Field = "st.WFStepStatus_Nm";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "PartyName")
                            {
                                advnfilter.rules.ElementAt(i).Field = "p.Party_Name";
                            }
                            op = Common.getoperator(advnfilter.rules.ElementAt(i).Operator);

                            if (i == 0)
                            {
                                if (op == "like")
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                }
                            }
                            else
                            {
                                if (op == "like")
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                }
                            }
                        }
                    }
                }

                if (isBranchSpecific)
                {
                    //wherecondition = wherecondition + " AND sr.Branch_ID=" + Convert.ToInt32(Session["Branch"]);
                    wherecondition = wherecondition + " AND sr.Branch_ID in (" + BranchID + ")";

                }
                if (GenLangCode == UserLangCode)
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID";
                    query = query + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";
                    query = query + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                    query = query + " left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID   left outer join GNM_RefMasterDetail brand on sr.Brand_ID=brand.RefMasterDetail_ID left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID left outer join GNM_WFStepStatus jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID";
                    query = query + " where" + wherecondition;
                }
                else
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID ";
                    query = query + " left outer join GNM_PartyLocale p on sr.Party_ID=p.Party_ID and p.Language_ID=" + LangID + " left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID and issuearea.Language_ID=" + LangID;
                    query = query + " left outer join GNM_RefMasterDetail brand on sr.Brand_ID=brand.RefMasterDetail_ID left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID and model.Language_ID=" + LangID + " left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                    query = query + " left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID  left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID left outer join GNM_WFStepStatus jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID";
                    query = query + " where" + wherecondition;
                }

                //----To get total record count with respect to given filter and search
                finalquery = string.Empty;
                finalquery = "select count(sr.ServiceRequest_ID)" + query;
                IEnumerable<int> rcount;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(finalquery, connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            List<int> results = new List<int>();
                            while (reader.Read())
                            {
                                results.Add(reader.GetInt32(0)); // Assuming the query returns a single integer column.
                            }
                            rcount = results;
                        }
                    }
                }
                count = rcount.ElementAt(0);
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                //---To get page wise queue data with respect to given filter and search----
                finalquery = string.Empty;
                //finalquery = ";WITH NewT AS(select sr.ServiceRequest_ID,ServiceRequestNumber as RequestNumber,convert(varchar(4),sr.FinancialYear) as FinancialYear,ServiceRequestDate,Party_Name as PartyName,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,";
                //finalquery = finalquery + "Model_Name as Model,sr.SerialNumber,st.WFStepStatus_Nm as Status,";
                //finalquery = finalquery + "Locked_Ind,Addresse_ID,Addresse_Flag,WFNextStep_ID, ROW_NUMBER() OVER(ORDER BY Transaction_ID desc) as row_number";

                string Qry = GetServiceRequestQuery(constring, LogException, HelpDesk, LangID, GenLangCode, UserLangCode);
                finalquery = finalquery + Qry + " where" + wherecondition;
                finalquery = finalquery + ") SELECT * FROM NewT WHERE row_number BETWEEN " + (((page - 1) * (rows)) + 1) + " AND " + (page * rows);
                // ServiceReq = ServiceRequestClient.Database.SqlQuery(typeof(ServiceRequest), finalquery).Cast<ServiceRequest>().ToList();

                List<ServiceRequest> serviceRequests = new List<ServiceRequest>();
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(finalquery, connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequest serviceRequest = new ServiceRequest
                                {
                                    row_number = reader["row_number"] != DBNull.Value ? Convert.ToInt64(reader["row_number"]) : 0,
                                    IsDropin = reader["IsDropin"] != DBNull.Value ? reader["IsDropin"].ToString() : null,
                                    IsEscaltedString = reader["IsEscaltedString"] != DBNull.Value ? reader["IsEscaltedString"].ToString() : null,
                                    IsEscalted = reader["IsEscalted"] != DBNull.Value ? (bool?)Convert.ToBoolean(reader["IsEscalted"]) : null,
                                    EscalatedLevel = reader["EscalatedLevel"] != DBNull.Value ? (int?)Convert.ToInt32(reader["EscalatedLevel"]) : null,
                                    SubGroupName = reader["SubGroupName"] != DBNull.Value ? reader["SubGroupName"].ToString() : null,
                                    IsDropinString = reader["IsDropinString"] != DBNull.Value ? reader["IsDropinString"].ToString() : null,
                                    IsDealer = reader["IsDealer"] != DBNull.Value ? (bool?)Convert.ToBoolean(reader["IsDealer"]) : null,
                                    SiteAddress = reader["SiteAddress"] != DBNull.Value ? reader["SiteAddress"].ToString() : null,
                                    Brand_Name = reader["Brand_Name"] != DBNull.Value ? reader["Brand_Name"].ToString() : null,
                                    JobCardStatus = reader["JobCardStatus"] != DBNull.Value ? reader["JobCardStatus"].ToString() : null,
                                    JobCardActivity = reader["JobCardActivity"] != DBNull.Value ? reader["JobCardActivity"].ToString() : null,
                                    QuotationNumber = reader["QuotationNumber"] != DBNull.Value ? reader["QuotationNumber"].ToString() : null,
                                    JobNumber = reader["JobNumber"] != DBNull.Value ? reader["JobNumber"].ToString() : null,
                                    JobCardDate = reader["JobCardDate"] != DBNull.Value ? reader["JobCardDate"].ToString() : null,
                                    FinancialYear = reader["FinancialYear"] != DBNull.Value ? reader["FinancialYear"].ToString() : null,
                                    ServiceRequest_ID = reader["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceRequest_ID"]) : 0,
                                    RequestNumber = reader["RequestNumber"] != DBNull.Value ? reader["RequestNumber"].ToString() : null,
                                    ServiceRequestDate = reader["ServiceRequestDate"] != DBNull.Value ? Convert.ToDateTime(reader["ServiceRequestDate"]) : DateTime.MinValue,
                                    ServiceRequestDateSTR = reader["ServiceRequestDateSTR"] != DBNull.Value ? reader["ServiceRequestDateSTR"].ToString() : null,
                                    Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null,
                                    Model = reader["Model"] != DBNull.Value ? reader["Model"].ToString() : null,
                                    SerialNumber = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : null,
                                    IssueArea = reader["IssueArea"] != DBNull.Value ? reader["IssueArea"].ToString() : null,
                                    IssueSubArea = reader["IssueSubArea"] != DBNull.Value ? reader["IssueSubArea"].ToString() : null,
                                    Status = reader["Status"] != DBNull.Value ? reader["Status"].ToString() : null,
                                    Party_Code = reader["Party_Code"] != DBNull.Value ? reader["Party_Code"].ToString() : null,
                                    PartyName = reader["PartyName"] != DBNull.Value ? reader["PartyName"].ToString() : null,
                                    Assign = reader["Assign"] != DBNull.Value ? reader["Assign"].ToString() : null,
                                    Locked_Ind = reader["Locked_Ind"] != DBNull.Value ? (bool?)Convert.ToBoolean(reader["Locked_Ind"]) : null,
                                    Lock = reader["Lock"] != DBNull.Value ? reader["Lock"].ToString() : null,
                                    Addresse_ID = reader["Addresse_ID"] != DBNull.Value ? Convert.ToInt32(reader["Addresse_ID"]) : 0,
                                    Addresse_Flag = reader["Addresse_Flag"] != DBNull.Value ? Convert.ToByte(reader["Addresse_Flag"]) : (byte)0,
                                    WFNextStep_ID = reader["WFNextStep_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["WFNextStep_ID"]) : null,
                                    IndicatorType = reader["IndicatorType"] != DBNull.Value ? Convert.ToInt32(reader["IndicatorType"]) : 0,
                                    Brand_ID = reader["Brand_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["Brand_ID"]) : null,
                                    Brand = reader["Brand"] != DBNull.Value ? reader["Brand"].ToString() : null,
                                    Product_Unique_Number = reader["Product_Unique_Number"] != DBNull.Value ? reader["Product_Unique_Number"].ToString() : null,
                                    ProductType_ID = reader["ProductType_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["ProductType_ID"]) : null,
                                    ProductType = reader["ProductType"] != DBNull.Value ? reader["ProductType"].ToString() : null,
                                    PartyContactPerson_ID = reader["PartyContactPerson_ID"] != DBNull.Value ? Convert.ToInt32(reader["PartyContactPerson_ID"]) : 0,
                                    ContactPerson = reader["ContactPerson"] != DBNull.Value ? reader["ContactPerson"].ToString() : null,
                                    ProductReading = reader["ProductReading"] != DBNull.Value ? (int?)Convert.ToInt32(reader["ProductReading"]) : null,
                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : null,
                                    Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                                    CallModeName = reader["CallModeName"] != DBNull.Value ? reader["CallModeName"].ToString() : null,
                                    CallPriorityName = reader["CallPriorityName"] != DBNull.Value ? reader["CallPriorityName"].ToString() : null,
                                    CallComplexityName = reader["CallComplexityName"] != DBNull.Value ? reader["CallComplexityName"].ToString() : null,
                                    CallMode_ID = reader["CallMode_ID"] != DBNull.Value ? Convert.ToInt32(reader["CallMode_ID"]) : 0,
                                    CallPriority_ID = reader["CallPriority_ID"] != DBNull.Value ? Convert.ToInt32(reader["CallPriority_ID"]) : 0,
                                    CallComplexity_ID = reader["CallComplexity_ID"] != DBNull.Value ? Convert.ToInt32(reader["CallComplexity_ID"]) : 0,
                                    CallDateAndTime = reader["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(reader["CallDateAndTime"]) : DateTime.MinValue,
                                    PromisedCompletionDate = reader["PromisedCompletionDate"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["PromisedCompletionDate"]) : null,
                                    IsUnderWarranty = reader["IsUnderWarranty"] != DBNull.Value ? Convert.ToBoolean(reader["IsUnderWarranty"]) : false,
                                    IsUnderBreakDown = reader["IsUnderBreakDown"] != DBNull.Value ? Convert.ToBoolean(reader["IsUnderBreakDown"]) : false,
                                    UnderWarrenty = reader["UnderWarrenty"] != DBNull.Value ? reader["UnderWarrenty"].ToString() : null,
                                    UnderBreakdown = reader["UnderBreakdown"] != DBNull.Value ? reader["UnderBreakdown"].ToString() : null,
                                    CallDescription = reader["CallDescription"] != DBNull.Value ? reader["CallDescription"].ToString() : null,
                                    CallClosureDateAndTime = reader["CallClosureDateAndTime"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["CallClosureDateAndTime"]) : null,
                                    CustomerRating = reader["CustomerRating"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CustomerRating"]) : null,
                                    CustomerRatingExport = reader["CustomerRatingExport"] != DBNull.Value ? reader["CustomerRatingExport"].ToString() : null,
                                    CallOwner_ID = reader["CallOwner_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CallOwner_ID"]) : null,
                                    CallOwner = reader["CallOwner"] != DBNull.Value ? reader["CallOwner"].ToString() : null,
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["FunctionGroup_ID"]) : null,
                                    FunctionGroupName = reader["FunctionGroupName"] != DBNull.Value ? reader["FunctionGroupName"].ToString() : null,
                                    ClosureType_ID = reader["ClosureType_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["ClosureType_ID"]) : null,
                                    ClosureTypeName = reader["ClosureTypeName"] != DBNull.Value ? reader["ClosureTypeName"].ToString() : null,
                                    InformationCollected = reader["InformationCollected"] != DBNull.Value ? reader["InformationCollected"].ToString() : null,
                                    CaseType_ID = reader["CaseType_ID"] != DBNull.Value ? Convert.ToInt32(reader["CaseType_ID"]) : 0,
                                    EnquiryType = reader["EnquiryType"] != DBNull.Value ? reader["EnquiryType"].ToString() : null,
                                };

                                serviceRequests.Add(serviceRequest);
                            }
                        }
                    }
                }

                //----------To store Transaction ID of respective Queue for Navigation---//
                finalquery = string.Empty;
                finalquery = "select sr.ServiceRequest_ID,jst.WFStepStatus_Nm as JobCardStatus,sr.ServiceRequestNumber as RequestNumber,ServiceRequestDate,Party_Name as PartyName,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,";
                finalquery = finalquery + "Model_Name as Model,brand.RefMasterDetail_Name as Brand_Name,'JobCardActivity'=(select top 1 RefMasterDetail_Name from SRT_JobCardActivityDetail as jca inner join GNM_RefMasterDetail as ja on jca.Status_ID=ja.RefMasterDetail_ID where jca.JobCard_ID=job.JobCard_ID order by jca.JobCardActivityDetails_ID desc),job.JobCardNumber as JobNumber,sr.SerialNumber,st.WFStepStatus_Nm as Status,quot.QuotationNumber as QuotationNumber";
                finalquery = finalquery + query;

                //IEnumerable<ServiceRequest> RowInds = ServiceRequestClient.Database.SqlQuery(typeof(ServiceRequest), finalquery).Cast<ServiceRequest>();
                //Session["ServiceRequestRowInd"] = RowInds.ToList();
                //Session["ServiceRequestRowInd"] = finalquery.ToString();

                //----To store filtered data for Export feature---
                finalquery = string.Empty;
                finalquery = "select sr.ServiceRequest_ID,jst.WFStepStatus_Nm as JobCardStatus,sr.ServiceRequestNumber as RequestNumber,ServiceRequestDate,Party_Name as PartyName,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description as IssueSubArea,";
                finalquery = finalquery + "Model_Name as Model,brand.RefMasterDetail_Name as Brand_Name,sr.SerialNumber,'JobCardActivity'=(select top 1 RefMasterDetail_Name from SRT_JobCardActivityDetail as jca inner join GNM_RefMasterDetail as ja on jca.Status_ID=ja.RefMasterDetail_ID where jca.JobCard_ID=job.JobCard_ID order by jca.JobCardActivityDetails_ID desc),convert(varchar(4),sr.FinancialYear) as FinancialYear,quot.QuotationNumber as QuotationNumber,st.WFStepStatus_Nm as Status,job.JobCardNumber as JobNumber,Locked_Ind,Addresse_ID,Addresse_Flag,WFNextStep_ID,sr.Brand_ID,sr.Product_Unique_Number,sr.ProductType_ID,sr.PartyContactPerson_ID,sr.SerialNumber,sr.ProductReading,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.CallDateAndTime,sr.PromisedCompletionDate,sr.CallDescription,sr.IsUnderWarranty,sr.IsUnderBreakDown,sr.CustomerRating,sr.CallClosureDateAndTime,sr.CallOwner_ID,sr.FunctionGroup_ID,sr.ClosureType_ID,sr.CaseType_ID,sr.InformationCollected,sr.Region_ID,sr.ClosingDescription,sr.Party_ID";
                finalquery = finalquery + query + " order by " + sidx + " " + sord;

                //ServiceReqAll = ServiceRequestClient.Database.SqlQuery(typeof(ServiceRequest), finalquery).Cast<ServiceRequest>().ToList();

                ServiceReq = from SR in ServiceReq
                             select new ServiceRequest()
                             {
                                 ServiceRequest_ID = SR.ServiceRequest_ID,
                                 SerialNumber = (SR.SerialNumber == null ? "" : SR.SerialNumber),
                                 PartyName = SR.PartyName,
                                 IssueArea = SR.IssueArea,
                                 Brand_Name = SR.Brand_Name,
                                 IssueSubArea = SR.IssueSubArea,
                                 Status = SR.Status,
                                 QuotationNumber = SR.QuotationNumber,
                                 JobNumber = SR.JobNumber,
                                 FinancialYear = (SR.FinancialYear == null) ? "" : (SR.FinancialYear == "0") ? "" : (SR.FinancialYear).ToString(),
                                 //Modified By Puneeth M On 10-Jan-2015 for QA Issue -->Exported report should be displayed as same as GUI
                                 ServiceRequestDate = SR.ServiceRequestDate,
                                 ServiceRequestDateSTR = SR.ServiceRequestDate.ToString("dd-MMM-yyyy"),
                                 //
                                 JobCardStatus = SR.JobCardStatus,

                                 JobCardActivity = SR.JobCardActivity,
                                 RequestNumber = SR.RequestNumber,
                                 Model = SR.Model,
                                 Lock = ((Obj.mode == 1) ? ("") : (Obj.mode == 2) ? ("") : (CloseStepIDStore.Contains((SR.WFNextStep_ID.HasValue ? SR.WFNextStep_ID.Value : 0)) ? closed : (SR.Addresse_ID == userid && SR.Addresse_Flag == 1) ? myqueue : ((SR.Addresse_ID == roleID && SR.Addresse_Flag == 0) ? grpQueue : others))).ToString(),
                                 Locked_Ind = Convert.ToBoolean(SR.Locked_Ind),
                                 IndicatorType = (CloseStepIDStore.Contains((SR.WFNextStep_ID.HasValue ? SR.WFNextStep_ID.Value : 0)) ? 3 : (SR.Addresse_ID == userid && SR.Addresse_Flag == 1) ? 1 : ((SR.Addresse_ID == roleID && SR.Addresse_Flag == 0) ? 2 : 4)),
                                 Attachmentco = SR.Attachmentco
                             };


                iQServiceReq = ServiceReq.AsQueryable<ServiceRequest>();

                //Sorting 
                iQServiceReq = iQServiceReq.OrderByField<ServiceRequest>(sidx, sord);
                //Session["LandingPageServiceRequestExport"] = finalquery.ToString();

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    Allocate = IsAllocate,
                    rows = (from SR in iQServiceReq
                            select new
                            {
                                ServiceRequest_ID = SR.ServiceRequest_ID,
                                //Edit = "<img key='" + SR.ServiceRequest_ID + "' key1='"+SR.RequestNumber+"' src='" + AppPath + "/Content/images/edit.gif' mode='Read' " + ((mode == 1) ? " Rmode='MyQ'" : "Rmode='GroupQ'") + "  class='edtCBClick' style='cursor:pointer'/>",
                                Edit = "<a title=" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px; cursor:pointer;' key='" + SR.ServiceRequest_ID + "' key1='" + SR.RequestNumber + "' mode='Read' " + ((Obj.mode == 1) ? " Rmode='MyQ'" : "Rmode='GroupQ'") + "  class='edtCBClick'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                RequestNumber = (SR.RequestNumber),
                                QuotationNumber = SR.QuotationNumber,
                                //Modified By Puneeth M On 10-Jan-2015 for QA Issue -->Exported report should be displayed as same as GUI
                                ServiceRequestDate = SR.ServiceRequestDate.ToString("dd-MMM-yyyy"),
                                ServiceRequestDateSTR = SR.ServiceRequestDateSTR,
                                //
                                Brand_Name = SR.Brand_Name,
                                JobCardActivity = SR.JobCardActivity,
                                JobCardStatus = SR.JobCardStatus,
                                FinancialYear = SR.FinancialYear,
                                JobNumber = SR.JobNumber,
                                PartyName = (SR.PartyName),
                                IssueArea = (SR.IssueArea),
                                Model = (SR.Model),
                                IssueSubArea = (SR.IssueSubArea),
                                SerialNumber = (SR.SerialNumber == null || SR.SerialNumber == "") ? "" : (SR.SerialNumber),
                                Assign = (Obj.mode == 1) ? ((Convert.ToBoolean(SR.Locked_Ind)) ? "<input type='button' class='LockRow' width='50' key='" + SR.ServiceRequest_ID + "' value='" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Unlock") + "' >" : "") : (Obj.mode == 2) ? ("<input type='button' class='LockRow' width='50' key='" + SR.ServiceRequest_ID + "' value='" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Lock") + "' >") : ((IsAdmin) ? ((SR.IndicatorType == 2 || SR.IndicatorType == 4) ? "<input type='button' class='AssigntoMyQ' width='50' key='" + SR.ServiceRequest_ID + "' value='" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Assign") + "' >" : "") : ""),
                                Lock = (Obj.mode == 1) ? ("" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "MyQueue") + "") : (Obj.mode == 2) ? ("" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Group") + "") : ((SR.IndicatorType == 1) ? "" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "MyQueue") + "" : (SR.IndicatorType == 2) ? "" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Group") + "" : (SR.IndicatorType == 3) ? "" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Closed") + "" : "" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Others") + ""),
                                Indicator = "",
                                Status = SR.Status,
                                Attachmentcount = Convert.ToInt32(SR.Attachmentco) > 0 ? "<img id='" + SR.ServiceRequest_ID + "' alt='Attachment' src='" + AppPath + "/Images/Attchmnt1616.png' />(" + SR.Attachmentco.ToString() + ")" : "",
                            }).ToList(),
                    Mode = Obj.Mode,
                    IsClosed = ClosedStatusID == Obj.StatusID ? "true" : "false",
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonobj);
        }
        #endregion


        public static string GetServiceRequestQuery(string constring, int LogException, string HelpDesk, int LangID, string GenLangCode = "EN", string UserLangCode = "EN", int Mode = 0, int User_ID = 0, int Company_ID = 0, int Branch_ID = 0, string sidx = "", string sord = "")
        {
            StringBuilder Query = new StringBuilder();
            string query = string.Empty;
            string finalquery = string.Empty;
            string statusIds = string.Empty;
            int ClosedStatusID = HelpDeskCommon.GetEndStepStatusID(WorkFlowCommon.GetWorkFlowID("Case Registration", HelpDesk, constring, LogException), constring, LogException);
            try
            {
                string AssignedTo = "";
                if (GenLangCode == UserLangCode)
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                    query = query + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";//left outer join GNM_PartyContactPersonDetails PCP on sr.PartyContactPerson_ID=PCP.PartyContactPerson_ID left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID
                    query = query + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID left outer join GNM_ProductType PT on sr.ProductType_ID=PT.ProductType_ID  left outer join GNM_RefMasterDetail brand on sr.Brand_ID=brand.RefMasterDetail_ID join GNM_RefMasterDetail Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID left outer join GNM_WFStepStatus st on sr.CallStatus_ID = st.WFStepStatus_ID";
                    //query = query + " left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID  left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID left outer join GNM_WFStepStatus jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID";
                    AssignedTo = "case when Addresse_Flag=1 then (select User_Name from GNM_User where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRole where WFRole_ID=Addresse_ID) end as AssignedTo";
                }
                else
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID  left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                    query = query + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_PartyLocale pa on p.Party_ID=pa.Party_ID and pa.Language_ID=" + LangID + " join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";//left outer join GNM_PartyContactPersonDetails PCP on sr.PartyContactPerson_ID=PCP.PartyContactPerson_ID  left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID
                    query = query + " left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID and model.Language_ID=" + LangID + " left outer join GNM_ProductTypeLocale PT on sr.ProductType_ID=PT.ProductType_ID and PT.Language_ID=" + LangID + " left outer join GNM_RefMasterDetailLocale brand on sr.Brand_ID=brand.RefMasterDetail_ID and brand.Language_ID=" + LangID + " left join GNM_RefMasterDetailLocale Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID and Dropin.Language_ID=" + LangID + "  left outer join GNM_WFStepStatusLocale st on sr.CallStatus_ID = st.WFStepStatus_ID";
                    //query = query + "  left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID  left outer join GNM_WFStepStatusLocale jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID";
                    AssignedTo = "case when Addresse_Flag=1 then (select User_Name from GNM_UserLocale where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRoleLocale where WFRole_ID=Addresse_ID) end as AssignedTo";
                }

                finalquery = string.Empty;
                if (GenLangCode == UserLangCode)
                {
                    finalquery = ";WITH NewT AS(select branch.Branch_Name as BranchName, sr.Branch_ID ,sr.IsDealer,NULL AS IsEscalted,NULL AS EscalatedLevel, sr.CaseType_ID,EnquiryType=(case when sr.CaseType_ID=1 then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ),sr.ServiceRequest_ID,sr.ServiceRequestNumber as RequestNumber,sr.CallDescription, sr.ChildTicket_Sequence_ID,ProductReading,ISNULL(PRD.Product_UniqueNo,'') AS Product_Unique_Number,sr.Party_ID,sr.PartyContactPerson_ID,sr.Model_ID,sr.Brand_ID,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.IssueSubArea_ID,sr.CallOwner_ID,sr.Region_ID,PT.ProductType_Name as ProductType,convert(varchar(4),sr.FinancialYear) as FinancialYear,sr.AttachmentCount as Attachmentco,ServiceRequestDate,p.Party_Name as PartyName,p.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,sr.Product_ID,sr.IssueArea_ID,Dropin.RefMasterDetail_Name as IsDropin ,";//,PCP.PartyContactPerson_Email,PCP.PartyContactPerson_Mobile,PCP.PartyContactPerson_Name,job.JobCard_ID,case when  sr.IsDealer=1 then branchdata.Branch_Name else  p.Party_Name end as PartyName,IssueSubArea.IssueSubArea_Description as IssueSubArea
                }
                else
                {
                    finalquery = ";WITH NewT AS(select branch.Branch_Name as BranchName, sr.Branch_ID ,sr.IsDealer,NULL AS IsEscalted,NULL AS EscalatedLevel, sr.CaseType_ID,EnquiryType=(case when sr.CaseType_ID=1 then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ),sr.ServiceRequest_ID,sr.ServiceRequestNumber as RequestNumber,sr.CallDescription, sr.ChildTicket_Sequence_ID,ProductReading,ISNULL(PRD.Product_UniqueNo,'') AS Product_Unique_Number,sr.Party_ID,sr.PartyContactPerson_ID,sr.Model_ID,sr.Brand_ID,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.IssueSubArea_ID,sr.CallOwner_ID,sr.Region_ID,PT.ProductType_Name as ProductType,convert(varchar(4),sr.FinancialYear) as FinancialYear,sr.AttachmentCount as Attachmentco,ServiceRequestDate, pa.Party_Name as PartyName,p.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,sr.Product_ID,sr.IssueArea_ID,Dropin.RefMasterDetail_Name as IsDropin,";//,PCP.PartyContactPerson_Email,PCP.PartyContactPerson_Mobile,PCP.PartyContactPerson_Name,job.JobCard_ID,case when  sr.IsDealer=1 then branchdata.Branch_Name else  pa.Party_Name end as PartyName,IssueSubArea.IssueSubArea_Description as IssueSubArea
                }
                finalquery = finalquery + "Model_Name as Model,sr.SerialNumber,brand.RefMasterDetail_Name as Brand_Name,st.WFStepStatus_Nm as Status,st.StepStatusCode, ";//,jst.WFStepStatus_Nm as JobCardStatus,'JobCardActivity'=(select top 1 RefMasterDetail_Name from SRT_JobCardActivityDetail as jca inner join GNM_RefMasterDetail as ja on jca.Status_ID=ja.RefMasterDetail_ID where jca.JobCard_ID=job.JobCard_ID order by jca.JobCardActivityDetails_ID desc),quot.QuotationNumber as QuotationNumber,job.JobCardNumber as JobNumber
                finalquery = finalquery + "Locked_Ind,Addresse_ID,Addresse_Flag,WFNextStep_ID,WFSteps_ID,Action_Chosen," + AssignedTo + ", ROW_NUMBER() OVER(ORDER BY " + sidx + " " + sord + ") as row_number";//ROW_NUMBER() OVER(ORDER BY COALESCE(sr.ParentIssue_ID,sr.ServiceRequest_ID),sr.ChildTicket_Sequence_ID) as row_number"
                finalquery = finalquery + query;


                if (Mode != 0)//Called from WEB API
                {
                    int WorkFlowID = WorkFlowCommon.GetWorkFlowID("Case Registration", HelpDesk, constring, LogException);
                    if (Mode == 1)//My-Q
                    {
                        finalquery = finalquery + " WHERE Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND Addresse_ID =" + User_ID + " AND Addresse_Flag = 1 AND sr.Company_ID=" + Company_ID;
                    }
                    else if (Mode == 2)//Group-Q
                    {
                        string inCondition = CommonFunctionalities.getGrpQincondition(User_ID, constring, LogException);
                        finalquery = finalquery + " WHERE Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND (Addresse_ID in (" + inCondition + ") AND Addresse_Flag = 0) AND sr.Company_ID=" + Company_ID;
                    }
                    else if (Mode == 3)//All-Q
                    {
                        statusIds = CommonFunctionalities.getStatusIDs(ClosedStatusID, WorkFlowID, constring, LogException);
                        finalquery = finalquery + " WHERE (Action_Chosen is null or" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " and sr.Company_ID=" + Company_ID;
                    }
                    if (Branch_ID != 0) Query.Append(" AND sr.Branch_ID=" + Branch_ID);
                    Query.Append(") SELECT * FROM NewT");
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return finalquery.ToString();
        }


















        #region::: Classes :::

        public partial class SelectServiceRequestaList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int StatusID { get; set; }
            public int Language_ID { get; set; }
            public int mode { get; set; }
            public string Mode { get; set; }
            public string UserCulture { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
        }
        public partial class HD_SRFollowUpInviteDetails
        {
            public int SRFollowUpInviteDetails_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int SRFollowUpDetails_ID { get; set; }
            public bool Invitee_Type { get; set; }
            public Nullable<int> Employee_ID { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public bool IsAttended { get; set; }

            public virtual HD_SRFollowUpDetails HD_SRFollowUpDetails { get; set; }
            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_SRFollowUpDetails
        {
            public HD_SRFollowUpDetails()
            {
                this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
            }

            public int SRFollowUpDetails_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public string FollowUpDescription { get; set; }
            public System.DateTime StartDateandTime { get; set; }
            public Nullable<System.DateTime> EndDateandTime { get; set; }
            public int FollowUpMode_ID { get; set; }
            public int FollowUpStatus_ID { get; set; }
            public string Remarks { get; set; }

            public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_ServiceRequestNotesDetail
        {
            public int ServiceRequestNotes_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int CreatedBy { get; set; }
            public System.DateTime CreatedDateAndTime { get; set; }
            public string NotesDescription { get; set; }
            public string Department { get; set; }
            public string Name { get; set; }
            public Nullable<bool> IsFollowUp { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_CRERCRepository
        {
            public int CRERCRepository_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public string RequestPartNumber { get; set; }
            public string RequestPartDescription { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Parts_ID { get; set; }
            public bool IsEPC { get; set; }
            public bool IsPricedInSAP { get; set; }
            public Nullable<decimal> Rate { get; set; }
            public bool IsNLS { get; set; }
            public bool IsIPN { get; set; }
            public Nullable<System.DateTime> LRDDate { get; set; }
            public bool IsMailAttachment { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_ServiceRequestAttachmentInfo
        {
            public int ServiceRequestAttachmentInfo_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public string FileName { get; set; }
            public int FileSize { get; set; }
            public string FileDescription { get; set; }
            public System.DateTime FileUploadDate { get; set; }
            public int FileUploadedByEmployee_ID { get; set; }
            public bool IsMailAttachment { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_ServiceRequestPartsList
        {
            public int ServiceRequestPartsList_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int Parts_ID { get; set; }
            public int UnitOfMeasurement { get; set; }
            public decimal Quantity { get; set; }
            public string Remarks { get; set; }
            public decimal Rate { get; set; }
            public Nullable<decimal> MRP { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_SRPRODUCTDETAILSALLOCATION
        {
            public int SRPRODUCTDETAILSALLOCATION_ID { get; set; }
            public int SRPRODUCTDETAILS_ID { get; set; }
            public int PRODUCT_ID { get; set; }

            public virtual HD_SRProductDetails HD_SRProductDetails { get; set; }
        }
        public partial class HD_SRProductDetails
        {
            public HD_SRProductDetails()
            {
                this.HD_SRPRODUCTDETAILSALLOCATION = new HashSet<HD_SRPRODUCTDETAILSALLOCATION>();
            }

            public int SRProductDetails_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int Model_ID { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public decimal Quantity { get; set; }
            public Nullable<decimal> ActiveQuantity { get; set; }
            public Nullable<decimal> WonQuantity { get; set; }
            public string LostSaleReasons { get; set; }
            public Nullable<int> Competitor_ID { get; set; }
            public Nullable<int> CompetitorModel_ID { get; set; }
            public Nullable<int> CompetitorBrand_ID { get; set; }
            public Nullable<int> CompetitorProductType_ID { get; set; }
            public Nullable<decimal> CompetitorPrice { get; set; }
            public Nullable<int> PrimarySegment_ID { get; set; }
            public Nullable<int> SecondarySegment_ID { get; set; }
            public string Remarks { get; set; }
            public Nullable<decimal> Rate { get; set; }
            public Nullable<int> PACKINGTYPE_ID { get; set; }
            public string ReferenceDetails { get; set; }

            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
            public virtual ICollection<HD_SRPRODUCTDETAILSALLOCATION> HD_SRPRODUCTDETAILSALLOCATION { get; set; }
        }
        public partial class HD_CustomerFeedbackQuestion
        {
            public HD_CustomerFeedbackQuestion()
            {
                this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
            }

            public int Question_ID { get; set; }
            public string Question { get; set; }
            public string CaseType_IDs { get; set; }
            public string IssueArea_IDs { get; set; }
            public string IssueSubArea_IDs { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsRating { get; set; }
            public bool IsFeedback { get; set; }
            public bool IsActive { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> QuestionCode { get; set; }

            public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
        }
        public partial class HD_SRCustomerQuestionFeedBack
        {
            public int QuestionFeedBack_ID { get; set; }
            public int ServiceRequest_ID { get; set; }
            public int Question_ID { get; set; }
            public System.DateTime FeedBackDate { get; set; }
            public string FeedBack { get; set; }
            public Nullable<byte> Rating { get; set; }

            public virtual HD_CustomerFeedbackQuestion HD_CustomerFeedbackQuestion { get; set; }
            public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        }
        public partial class HD_ServiceRequest
        {
            public HD_ServiceRequest()
            {
                this.HD_ServiceRequestNotesDetail = new HashSet<HD_ServiceRequestNotesDetail>();
                this.HD_SRFollowUpDetails = new HashSet<HD_SRFollowUpDetails>();
                this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
                this.HD_CRERCRepository = new HashSet<HD_CRERCRepository>();
                this.HD_ServiceRequestAttachmentInfo = new HashSet<HD_ServiceRequestAttachmentInfo>();
                this.HD_ServiceRequestPartsList = new HashSet<HD_ServiceRequestPartsList>();
                this.HD_SRProductDetails = new HashSet<HD_SRProductDetails>();
                this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
            }

            public int ServiceRequest_ID { get; set; }
            public string ServiceRequestNumber { get; set; }
            public System.DateTime ServiceRequestDate { get; set; }
            public Nullable<int> Quotation_ID { get; set; }
            public string QuotationNumber { get; set; }
            public Nullable<int> JobCard_ID { get; set; }
            public string JobCardNumber { get; set; }
            public int CallStatus_ID { get; set; }
            public Nullable<int> ParentIssue_ID { get; set; }
            public string Product_Unique_Number { get; set; }
            public int Party_ID { get; set; }
            public int PartyContactPerson_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public string SerialNumber { get; set; }
            public Nullable<int> ProductReading { get; set; }
            public bool IsUnderWarranty { get; set; }
            public int CallMode_ID { get; set; }
            public Nullable<int> CallPriority_ID { get; set; }
            public int CallComplexity_ID { get; set; }
            public System.DateTime CallDateAndTime { get; set; }
            public Nullable<System.DateTime> PromisedCompletionDate { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string CallDescription { get; set; }
            public Nullable<int> IssueArea_ID { get; set; }
            public Nullable<int> IssueSubArea_ID { get; set; }
            public Nullable<int> FunctionGroup_ID { get; set; }
            public bool IsUnderBreakDown { get; set; }
            public Nullable<int> QuestionaryLevel1_ID { get; set; }
            public Nullable<int> QuestionaryLevel2_ID { get; set; }
            public Nullable<int> QuestionaryLevel3_ID { get; set; }
            public Nullable<int> DefectGroup_ID { get; set; }
            public Nullable<int> DefectName_ID { get; set; }
            public string RootCause { get; set; }
            public string InformationCollected { get; set; }
            public Nullable<System.DateTime> CallClosureDateAndTime { get; set; }
            public Nullable<int> ClosureType_ID { get; set; }
            public string ClosingDescription { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }
            public Nullable<int> ModifiedBy_ID { get; set; }
            public Nullable<int> Document_no { get; set; }
            public int CaseType_ID { get; set; }
            public string ActionRemarks { get; set; }
            public Nullable<int> Product_ID { get; set; }
            public Nullable<int> CustomerRating { get; set; }
            public Nullable<int> FinancialYear { get; set; }
            public Nullable<bool> IsCallBlocked { get; set; }
            public Nullable<int> StockBlocking_ID { get; set; }
            public Nullable<int> EnquiryStage_ID { get; set; }
            public Nullable<int> SalesQuotation_ID { get; set; }
            public string SalesQuotationNumber { get; set; }
            public Nullable<int> SalesOrder_ID { get; set; }
            public string SalesOrderNumber { get; set; }
            public Nullable<System.DateTime> SalesOrderDate { get; set; }
            public Nullable<int> CallOwner_ID { get; set; }
            public string Flexi1 { get; set; }
            public string Flexi2 { get; set; }
            public string ResolutionTime { get; set; }
            public string ResponseTime { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public Nullable<System.DateTime> CustomerFeedbackDate { get; set; }
            public Nullable<bool> IsNegetiveFeedback { get; set; }
            public Nullable<bool> IsDealer { get; set; }
            public Nullable<byte> IsDealerList { get; set; }
            public Nullable<byte> ProductRateType { get; set; }
            public Nullable<int> ChildTicket_Sequence_ID { get; set; }
            public string ResponseTime24 { get; set; }
            public string ResolutionTime24 { get; set; }
            public Nullable<int> Current_AssignTo { get; set; }
            public Nullable<int> ContractorID { get; set; }
            public Nullable<int> ContractorContactPerson_ID { get; set; }
            public Nullable<int> ScheduledType_ID { get; set; }
            public Nullable<System.DateTime> ExpectedArrivalDateTime { get; set; }
            public Nullable<System.DateTime> ActualArrivalDateTime { get; set; }
            public Nullable<System.DateTime> ExpectedDepartureDateTime { get; set; }
            public Nullable<System.DateTime> ActualDepartureDateTime { get; set; }
            public Nullable<int> NoofTechs { get; set; }
            public Nullable<int> ShiftHours { get; set; }
            public Nullable<bool> IsWIPBay { get; set; }

            public virtual ICollection<HD_ServiceRequestNotesDetail> HD_ServiceRequestNotesDetail { get; set; }
            public virtual ICollection<HD_SRFollowUpDetails> HD_SRFollowUpDetails { get; set; }
            public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
            public virtual ICollection<HD_CRERCRepository> HD_CRERCRepository { get; set; }
            public virtual ICollection<HD_ServiceRequestAttachmentInfo> HD_ServiceRequestAttachmentInfo { get; set; }
            public virtual ICollection<HD_ServiceRequestPartsList> HD_ServiceRequestPartsList { get; set; }
            public virtual ICollection<HD_SRProductDetails> HD_SRProductDetails { get; set; }
            public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
        }

        public class SavecustomerFeedbackDetailsList
        {
            public int Branch { get; set; }
            public int ServiceRequestID { get; set; }
            public int JobCardID { get; set; }
            public int CustomerRating { get; set; }
            public string Feedback { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public bool IsNegetiveFeedback { get; set; }
            public string Date { get; set; }
        }
        public class SelectALLTicketsandWOPendingforCustomerFeedBackList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string Query { get; set; }
            public string filters { get; set; }
        }
        public class CustomerFeedBack
        {
            public string QuotationNumber
            {
                get;
                set;
            }

            public string JobNumber
            {
                get;
                set;
            }

            public DateTime? JobCardDate
            {
                get;
                set;
            }
            public string JobCardDateStr
            {
                get;
                set;
            }

            public int ServiceRequest_ID
            {
                get;
                set;
            }
            public string RequestNumber
            {
                get;
                set;
            }
            public DateTime? ServiceRequestDate
            {
                get;
                set;
            }
            public string ServiceRequestDateSTR
            {
                get;
                set;
            }
            public string Model
            {
                get;
                set;
            }
            public string SerialNumber
            {
                get;
                set;
            }

            public string PartyName
            {
                get;
                set;
            }
            public string Product_Unique_Number
            {
                get;
                set;
            }

            public string PartyContactPerson_Mobile
            {
                get;
                set;
            }

            public int JobCard_ID
            {
                get;
                set;
            }
            public DateTime? WorkOrderDate
            {
                get;
                set;
            }
            public DateTime? QuoteDate
            {
                get;
                set;
            }
            public string WorkOrderDatestr
            {
                get;
                set;
            }
            public string QuoteDatestr
            {
                get;
                set;
            }
        }


        #endregion
    }
}
