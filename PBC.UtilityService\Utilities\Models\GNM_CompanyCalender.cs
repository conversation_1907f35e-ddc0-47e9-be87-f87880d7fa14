using System;
using System.Collections.Generic;

namespace PBC.UtilityService.Utilities.Models
{
    public partial class GNM_CompanyCalender
    {
        public int CompanyCalender_ID { get; set; }
        public int Company_ID { get; set; }
        public int CompanyCalender_Year { get; set; }
        public int Shift_ID { get; set; }
        public TimeSpan CompanyCalender_StartTime { get; set; }
        public TimeSpan CompanyCalender_EndTime { get; set; }
        public string CompanyCalender_WorkingDays { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public bool IsGeneralShift { get; set; }
        public TimeSpan Break_StartTime { get; set; }
        public TimeSpan Break_EndTime { get; set; }
        public int? Branch_ID { get; set; }
        public TimeSpan? ShiftHours { get; set; }
        public string DoubleTimeApplicableDays { get; set; }
        public int? OverTimeMinutes { get; set; }
        public int? DoubleTimeMinutes { get; set; }
        public int? ShiftType_ID { get; set; }
        public int? ShiftDays { get; set; }
        public bool? CompanyCalendarActive { get; set; }
        public bool IsByDate { get; set; }
    }
}
