﻿

using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using static SharedAPIClassLibrary_AMERP.CoreProductTypeMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;






namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskUnregisteredServiceRequestServices
    {
        #region ::: GetPermissions vinay n 11/11/24:::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>   
        public static IActionResult GetPermissions(GetPermissionsList Obj, string connString, int LogException)
        {
            AMMSCore.Utilities.Utilities util = new AMMSCore.Utilities.Utilities();
            try
            {
                JsonResult jr = Common.InitialSetup(Obj.ObjectID, Obj.User_ID, connString, LogException);
                return new JsonResult(jr);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
        }
        #endregion
        #region GetUnRegisteredSeviceRequest vinay n 11/11/24
        /// <summary>
        /// GetUnRegisteredSeviceRequest
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        private static IQueryable<UnRegisteredServiceObject> GetUnRegisteredSeviceRequest(GetUnRegisteredSeviceRequest2List Obj, string connString, int LogException)
        {
            List<HD_UnregisteredServiceRequest> liUnRegistered = new List<HD_UnregisteredServiceRequest>();
            IQueryable<UnRegisteredServiceObject> iQUnServiceReq = null;
            IEnumerable<UnRegisteredServiceObject> UnServiceReq = null;
            List<UnRegisteredServiceObject> res = new List<UnRegisteredServiceObject>();
            try
            {

                string Pending = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Pending").ToString();
                string locked = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "locked").ToString();
                string Registered = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Registered").ToString();
                string Abandoned = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Abandoned").ToString();
                string frmdate = Obj.frmdate;
                string todate = Obj.todate;


                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand("UP_GET_HelpDesk_GetUnRegisteredSeviceRequest_HelpDeskUnregisteredServiceRequest", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@FrmDate", frmdate);
                            cmd.Parameters.AddWithValue("@ToDate", todate);
                            cmd.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                            cmd.Parameters.AddWithValue("@UserLanguageID", Obj.UserLanguageID);
                            cmd.Parameters.AddWithValue("@GeneralLanguageID", Obj.GeneralLanguageID);
                            cmd.Parameters.AddWithValue("@Language_ID", Obj.Language_ID);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    UnRegisteredServiceObject serviceObj = new UnRegisteredServiceObject
                                    {
                                        ServiceRequest_id = reader.GetInt32(reader.GetOrdinal("ServiceRequest_id")),
                                        PartyName = reader.GetString(reader.GetOrdinal("PartyName")),
                                        RequestDate = reader.GetDateTime(reader.GetOrdinal("RequestDate")),
                                        ReqDate = reader.GetString(reader.GetOrdinal("ReqDate")),
                                        BrandName = reader.GetString(reader.GetOrdinal("BrandName")),
                                        ModelName = reader.GetString(reader.GetOrdinal("ModelName")),
                                        SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber")),
                                        Req_Mobile = reader.GetString(reader.GetOrdinal("Req_Mobile")),
                                        Req_Status = reader.GetString(reader.GetOrdinal("Req_Status"))
                                    };
                                    res.Add(serviceObj);
                                }
                                UnServiceReq = res.AsEnumerable();


                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                iQUnServiceReq = UnServiceReq.AsQueryable<UnRegisteredServiceObject>();

            }
            catch (Exception e)
            {
                throw e;
            }
            return iQUnServiceReq;
        }
        #endregion
        #region ::: SelectUnRegisteredSeviceRequest  vinay n 11/11/24:::
        /// <summary>
        /// to select the UnRegistered Sevice Request  records
        /// </summary>   

        public static IActionResult SelectUnRegisteredSeviceRequest(SelectUnRegisteredSeviceRequestList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            int count = 0;
            int total = 0;
            var TaxStruct = default(dynamic);
            List<UnRegisteredServiceObject> res = new List<UnRegisteredServiceObject>();
            List<HD_UnregisteredServiceRequest> liUnRegistered = new List<HD_UnregisteredServiceRequest>();
            IQueryable<UnRegisteredServiceObject> iQUnServiceReq = null;
            IEnumerable<UnRegisteredServiceObject> UnServiceReq = null;
            string Pending = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Pending").ToString();
            string locked = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "locked").ToString();
            string Registered = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Registered").ToString();
            string Abandoned = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Abandoned").ToString();
            try
            {

                string frmdate = (Obj.FromDate == null) ? (string.Empty) : (Obj.FromDate);
                string todate = (Obj.ToDate == null) ? (string.Empty) : (Obj.ToDate);
                string query = string.Empty;
                if (!string.IsNullOrEmpty(frmdate) && !string.IsNullOrEmpty(todate))
                {

                    query = @"
                    SELECT * 
                    FROM HD_UnregisteredServiceRequest
                    WHERE Date >= @FromDate AND Date <= @ToDate AND Company_ID = @CompanyID";
                }
                else if (!string.IsNullOrEmpty(frmdate) && string.IsNullOrEmpty(todate))
                {
                    query = @"
                        SELECT * 
                        FROM HD_UnregisteredServiceRequest
                        WHERE Date >= @FromDate AND Company_ID = @CompanyID";
                }
                else if (string.IsNullOrEmpty(frmdate) && !string.IsNullOrEmpty(todate))
                {

                    query = @"
                    SELECT * 
                    FROM HD_UnregisteredServiceRequest
                    WHERE Date <= @ToDate AND Company_ID = @CompanyID";
                }
                else
                {
                    query = @"
                    SELECT * 
                    FROM HD_UnregisteredServiceRequest
                    WHERE Company_ID = @CompanyID";
                }
                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);
                            if (!string.IsNullOrEmpty(frmdate))
                            {
                                cmd.Parameters.AddWithValue("@FromDate", Convert.ToDateTime(frmdate));
                            }
                            if (!string.IsNullOrEmpty(todate))
                            {
                                cmd.Parameters.AddWithValue("@ToDate", Convert.ToDateTime(todate).AddDays(1));
                            }



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    HD_UnregisteredServiceRequest serviceObj = new HD_UnregisteredServiceRequest
                                    {
                                        UnregisteredServiceRequest_ID = Convert.ToInt32(reader["UnregisteredServiceRequest_ID"]),
                                        Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                        Product_Unique_Number = reader["Product_Unique_Number"] != DBNull.Value ? reader["Product_Unique_Number"].ToString() : null,
                                        Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                        RequestDescription = reader["RequestDescription"] != DBNull.Value ? reader["RequestDescription"].ToString() : null,
                                        Brand_ID = reader["Brand_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["Brand_ID"]) : null,
                                        ProductType_ID = reader["ProductType_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["ProductType_ID"]) : null,
                                        Model_ID = reader["Model_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["Model_ID"]) : null,
                                        SerialNumber = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : null,
                                        Email_ID = reader["Email_ID"] != DBNull.Value ? reader["Email_ID"].ToString() : null,
                                        Mobile = reader["Mobile"] != DBNull.Value ? reader["Mobile"].ToString() : null,
                                        Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                                        Date = Convert.ToDateTime(reader["Date"]),
                                        ServiceRequest_ID = reader["ServiceRequest_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["ServiceRequest_ID"]) : null,
                                        Remarks = reader["Remarks"] != DBNull.Value ? reader["Remarks"].ToString() : null,
                                        Status = Convert.ToByte(reader["Status"]),
                                        Locked_by_User_ID = reader["Locked_by_User_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["Locked_by_User_ID"]) : null,
                                        Coordinate_Latitude = reader["Coordinate_Latitude"] != DBNull.Value ? (decimal?)Convert.ToDecimal(reader["Coordinate_Latitude"]) : null,
                                        Coordinate_Longitude = reader["Coordinate_Longitude"] != DBNull.Value ? (decimal?)Convert.ToDecimal(reader["Coordinate_Longitude"]) : null,
                                        LatLongAddress = reader["LatLongAddress"] != DBNull.Value ? reader["LatLongAddress"].ToString() : null
                                    };
                                    liUnRegistered.Add(serviceObj);
                                }



                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                GetUnRegisteredSeviceRequest2List requestObj = new GetUnRegisteredSeviceRequest2List
                {
                    UserCulture = Obj.UserCulture,      // Example culture (English - United States)
                    frmdate = Obj.FromDate,     // Example from date (start date of the range)
                    todate = Obj.ToDate,      // Example to date (end date of the range)
                    Company_ID = Obj.Company_ID,             // Example company ID
                    UserLanguageID = Obj.UserLanguageID,         // Example user language ID
                    GeneralLanguageID = Obj.GeneralLanguageID,      // Example general language ID
                    Language_ID = Obj.LanguageID           // Example language ID
                };

                iQUnServiceReq = GetUnRegisteredSeviceRequest(requestObj, constring, LogException);

                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                    {
                        iQUnServiceReq = iQUnServiceReq.FilterSearch<UnRegisteredServiceObject>(filtersObj);
                    }
                    else
                    {
                        iQUnServiceReq = iQUnServiceReq.AsQueryable<UnRegisteredServiceObject>().Where(a => a.Req_Status == Pending || a.Req_Status == locked);
                    }
                }//Advance Search
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                    iQUnServiceReq = iQUnServiceReq.AdvanceSearch<UnRegisteredServiceObject>(advnfilter);
                }
                else
                {
                    iQUnServiceReq = iQUnServiceReq.AsQueryable<UnRegisteredServiceObject>().Where(a => a.Req_Status == Pending || a.Req_Status == locked);
                }
                //Sorting 
                //Session["iQUnServiceReq"] = iQUnServiceReq;

                iQUnServiceReq = iQUnServiceReq.OrderByField<UnRegisteredServiceObject>(sidx, sord);
                count = iQUnServiceReq.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                TaxStruct = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from UnSR in iQUnServiceReq
                            join a in liUnRegistered on UnSR.ServiceRequest_id equals a.UnregisteredServiceRequest_ID
                            select new
                            {
                                edit = "<a title='View' href='#' style='font-size: 13px;' id='" + a.UnregisteredServiceRequest_ID + "' key='" + a.UnregisteredServiceRequest_ID + "' class='editUnRegistered' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                ServiceRequest_id = UnSR.ServiceRequest_id,
                                PartyName = UnSR.PartyName,
                                ReqDate = UnSR.ReqDate,
                                BrandName = UnSR.BrandName,
                                ModelName = UnSR.ModelName,
                                SerialNumber = UnSR.SerialNumber,
                                Req_Mobile = UnSR.Req_Mobile,
                                Req_Status = UnSR.Req_Status
                            }).ToList().Paginate(page, rows),
                    filter = filters,
                    advanceFilter = Query,
                    frmdate,
                    todate
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(TaxStruct);
        }
        #endregion

        #region ::: Get Party Details by Product Unique no:::
        /// <summary>
        /// To get Party Details by Product Unique no
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetPartyDetailsByProductUniqueNo(GetPartyDetailsByProductUniqueNoList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            GNM_Product Prod = null;
            GNM_ProductCustomer PCustomer = null;
            GNM_Party Party = null;
            GNM_PartyLocale PartyLocale = null;
            GNM_RefMasterDetail Brand = null;
            GNM_RefMasterDetailLocale BrandLocale = null;
            GNM_ProductType ProdType = null;
            GNM_ProductTypeLocale ProdTypeLocale = null;
            CoreModelMasterServices.GNM_Model Model = null;
            GNM_ModelLocale ModelLocale = null;
            IEnumerable<GNM_PartyContactPersonDetails> PartyContactDetails = null;
            string PUNumber = (Common.DecryptString(Obj.UniqueNo)).Replace("&pl", "+");
            string GenLangCode = "";
            string UserLangCode = "";
            try
            {

                int LangID = Obj.Language_ID;
                int CompanyID = Obj.Company_ID;
                GenLangCode = Obj.GeneralLanguageCode.ToString();
                UserLangCode = Obj.UserLanguageCode.ToString();
                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                string Query = ";WITH ParentComapany([Company_ID],[Company_Name],[Company_Parent_ID]) as (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM GNM_Company WHERE [Company_ID] = " + CompanyID + " UNION ALL SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] FROM GNM_Company AS child JOIN ParentComapany ON child.[Company_ID] = ParentComapany.[Company_Parent_ID])SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM ParentComapany;";

                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(Query, conn))
                        {
                            cmd.CommandType = CommandType.Text;





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ParentCompanyObject obj = new ParentCompanyObject
                                    {
                                        Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Company_Name = reader.IsDBNull(reader.GetOrdinal("Company_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_Name")),
                                        Company_Parent_ID = reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_Parent_ID"))
                                    };
                                    ParentCompanyDetails.Add(obj);
                                }



                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                string ParentCompanyIDs = "";
                for (int i = 0; i < ParentCompanyDetails.Count(); i++)
                {
                    ParentCompanyIDs += ParentCompanyDetails.ElementAt(i).Company_ID.ToString() + ",";
                }
                ParentCompanyIDs = ParentCompanyIDs.TrimEnd(new char[] { ',' });
                string QueryPUI = "SELECT * FROM  GNM_PRODUCT WHERE Product_UniqueNo='" + PUNumber + "' and IsActive=1 and Company_ID in (" + ParentCompanyIDs + ")";
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(QueryPUI, conn))
                        {
                            cmd.CommandType = CommandType.Text;





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    Prod = new GNM_Product
                                    {
                                        Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Product_UniqueNo = reader.GetString(reader.GetOrdinal("Product_UniqueNo")),
                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                        ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        Product_SerialNumber = reader.GetString(reader.GetOrdinal("Product_SerialNumber")),
                                        PrimarySegment_ID = reader.IsDBNull(reader.GetOrdinal("PrimarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PrimarySegment_ID")),
                                        SecondarySegment_ID = reader.IsDBNull(reader.GetOrdinal("SecondarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SecondarySegment_ID")),
                                        ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                        ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                        IsComponent = reader.IsDBNull(reader.GetOrdinal("IsComponent")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsComponent")),
                                        Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                        NextServiceType_ID = reader.IsDBNull(reader.GetOrdinal("NextServiceType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("NextServiceType_ID")),
                                        NextServiceDate = reader.IsDBNull(reader.GetOrdinal("NextServiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("NextServiceDate")),
                                        MachineStatus_ID = reader.IsDBNull(reader.GetOrdinal("MachineStatus_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("MachineStatus_ID")),
                                        AverageReadingPerDay = reader.IsDBNull(reader.GetOrdinal("AverageReadingPerDay")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("AverageReadingPerDay")),
                                        CommissioningDate = reader.IsDBNull(reader.GetOrdinal("CommissioningDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CommissioningDate")),
                                        IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                        Warehouse_ID = reader.IsDBNull(reader.GetOrdinal("Warehouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Warehouse_ID")),
                                        Parts_ID = reader.IsDBNull(reader.GetOrdinal("Parts_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                        SerialStatus = reader.IsDBNull(reader.GetOrdinal("SerialStatus")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SerialStatus")),
                                        DateOfManufacture = reader.IsDBNull(reader.GetOrdinal("DateOfManufacture")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfManufacture")),
                                        Product_EngineSerialNumber = reader.IsDBNull(reader.GetOrdinal("Product_EngineSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("Product_EngineSerialNumber")),
                                        ServiceCompany = reader.IsDBNull(reader.GetOrdinal("ServiceCompany")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceCompany")),
                                        InvoiceDate = reader.IsDBNull(reader.GetOrdinal("InvoiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("InvoiceDate")),
                                        LandingCost = reader.IsDBNull(reader.GetOrdinal("LandingCost")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("LandingCost")),
                                        WarrantyEndDate = reader.IsDBNull(reader.GetOrdinal("WarrantyEndDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("WarrantyEndDate")),
                                        DateOfSale = reader.IsDBNull(reader.GetOrdinal("DateOfSale")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfSale")),
                                        AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                        WholeSaleDealerid = reader.IsDBNull(reader.GetOrdinal("WholeSaleDealerid")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WholeSaleDealerid")),
                                        IsWholeSaleUser = reader.IsDBNull(reader.GetOrdinal("IsWholeSaleUser")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsWholeSaleUser")),
                                        ServiceEngineer_ID = reader.IsDBNull(reader.GetOrdinal("ServiceEngineer_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceEngineer_ID")),
                                        Reading_Unit = reader.IsDBNull(reader.GetOrdinal("Reading_Unit")) ? null : reader.GetString(reader.GetOrdinal("Reading_Unit")),
                                        LastServiceBranch = reader.IsDBNull(reader.GetOrdinal("LastServiceBranch")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("LastServiceBranch")),
                                        IsPCPApplicable = reader.IsDBNull(reader.GetOrdinal("IsPCPApplicable")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsPCPApplicable")),
                                        PCPFrequency = reader.IsDBNull(reader.GetOrdinal("PCPFrequency")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPFrequency")),
                                        PCPUsedCount = reader.IsDBNull(reader.GetOrdinal("PCPUsedCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPUsedCount")),
                                        IsClaasMachine = reader.IsDBNull(reader.GetOrdinal("IsClaasMachine")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsClaasMachine")),
                                        Series = reader.IsDBNull(reader.GetOrdinal("Series")) ? null : reader.GetString(reader.GetOrdinal("Series")),
                                        isSeriesAttachmentAdded = reader.IsDBNull(reader.GetOrdinal("isSeriesAttachmentAdded")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("isSeriesAttachmentAdded"))
                                    };

                                }



                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                //Prod = ProductClient.GNM_Product.Where(p => p.Product_UniqueNo == PUNumber && p.Company_ID == CompanyID && p.IsActive == true).FirstOrDefault();
                if (Prod != null)
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand("UP_GET_HelpDesk_GetPartyDetailsByProductUniqueNo_HelpDeskUnregisteredServiceRequest", conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ProductID", Prod.Product_ID);
                                command.Parameters.AddWithValue("@LangID", LangID);
                                command.Parameters.AddWithValue("@GenLangCode", GenLangCode);
                                command.Parameters.AddWithValue("@UserLangCode", UserLangCode);
                                command.Parameters.AddWithValue("@Brand_ID", Prod.Brand_ID);
                                command.Parameters.AddWithValue("@ProductType_ID", Prod.ProductType_ID);
                                command.Parameters.AddWithValue("@Model_ID", Prod.Model_ID);





                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.HasRows)
                                    {
                                        while (reader.Read())
                                        {
                                            Party = new GNM_Party
                                            {
                                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                                Party_Mobile = reader.GetString(reader.GetOrdinal("Party_Mobile")),
                                                Party_Email = reader.GetString(reader.GetOrdinal("Party_Email")),
                                                Party_IsActive = reader.GetBoolean(reader.GetOrdinal("Party_IsActive")),
                                                PartyType = reader.IsDBNull(reader.GetOrdinal("PartyType")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("PartyType"))
                                            };

                                        }
                                    }
                                    reader.NextResult();
                                    if (reader.HasRows)
                                    {
                                        while (reader.Read())
                                        {
                                            Brand = new GNM_RefMasterDetail
                                            {
                                                RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                                RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                            };

                                        }
                                    }
                                    reader.NextResult();
                                    if (reader.HasRows)
                                    {
                                        while (reader.Read())
                                        {
                                            ProdType = new GNM_ProductType
                                            {
                                                ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                                ProductType_Name = reader.GetString(reader.GetOrdinal("ProductType_Name"))
                                            };

                                        }
                                    }
                                    reader.NextResult();
                                    if (reader.HasRows)
                                    {
                                        while (reader.Read())
                                        {
                                            Model = new CoreModelMasterServices.GNM_Model
                                            {
                                                Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                Model_Name = reader.GetString(reader.GetOrdinal("Model_Name"))
                                            };

                                        }
                                    }
                                    if (GenLangCode != UserLangCode)
                                    {
                                        reader.NextResult();
                                        if (reader.HasRows)
                                        {
                                            while (reader.Read())
                                            {
                                                PartyLocale = new GNM_PartyLocale
                                                {
                                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                    Party_Name = reader.GetString(reader.GetOrdinal("Party_Name"))
                                                };

                                            }
                                        }
                                        reader.NextResult();
                                        if (reader.HasRows)
                                        {
                                            while (reader.Read())
                                            {
                                                BrandLocale = new GNM_RefMasterDetailLocale
                                                {
                                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                                    RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                                };

                                            }
                                        }
                                        reader.NextResult();
                                        if (reader.HasRows)
                                        {
                                            while (reader.Read())
                                            {
                                                ProdTypeLocale = new GNM_ProductTypeLocale
                                                {
                                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                                    ProductType_Name = reader.GetString(reader.GetOrdinal("ProductType_Name"))
                                                };

                                            }
                                        }

                                        // Process ModelLocale
                                        reader.NextResult();
                                        if (reader.HasRows)
                                        {
                                            while (reader.Read())
                                            {
                                                ModelLocale = new GNM_ModelLocale
                                                {
                                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                    Model_Name = reader.GetString(reader.GetOrdinal("Model_Name"))
                                                };

                                            }
                                        }
                                    }
                                    reader.NextResult();
                                    if (reader.HasRows)
                                    {
                                        while (reader.Read())
                                        {
                                            PCustomer = new GNM_ProductCustomer
                                            {
                                                ProductCustomer_ID = reader.GetInt32(reader.GetOrdinal("ProductCustomer_ID")),

                                            };

                                        }
                                    }

                                }
                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                    if (PCustomer != null)
                    {


                        jsonResult = new
                        {
                            Result = "1",//Exists
                            Party_ID = Party.Party_ID,
                            PartyName = (GenLangCode == UserLangCode) ? Party.Party_Name : (PartyLocale == null) ? "" : PartyLocale.Party_Name,
                            Party_IsActive = Party.Party_IsActive,
                            PartyPhone = Party.Party_Mobile,
                            PartyEmail = Party.Party_Email,
                            PartyType = (Party.PartyType == 1) ? "Customer" : "Prospect",
                            BrandName = (GenLangCode == UserLangCode) ? Brand.RefMasterDetail_Name : (BrandLocale == null) ? "" : BrandLocale.RefMasterDetail_Name,
                            BrandID = Prod.Brand_ID,
                            ProductTypeName = (GenLangCode == UserLangCode) ? ProdType.ProductType_Name : (ProdTypeLocale == null) ? "" : ProdTypeLocale.ProductType_Name,
                            ProductTypeID = Prod.ProductType_ID,
                            ModelName = (GenLangCode == UserLangCode) ? Model.Model_Name : (ModelLocale == null) ? "" : Model.Model_Name,
                            ModelTypeID = Prod.Model_ID,
                            SerialNumber = Prod.Product_SerialNumber,
                            Unique_Number = Prod.Product_UniqueNo,
                            Product_ID = Prod.Product_ID
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "2",//Not associated with any customer                  
                            Party_ID = 0
                        };
                    }
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "0",//Not Exists                 
                        Party_ID = 0
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0"//Not Exists         
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion
        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // If the result type is a collection (e.g., List<T>)
                        if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                        {

                            var list = Activator.CreateInstance<T>();
                            var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();
                            while (reader.Read())
                            {
                                var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }

                              ((IList)list).Add(item);
                            }
                            result = (T)list;
                        }
                        // If the result is a scalar type (like string, bool, int)
                        else if (typeof(T) == typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        // Handle cases for custom classes/complex objects
                        else if (typeof(T).IsClass && typeof(T) != typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = Activator.CreateInstance<T>(); // Create an instance of the class
                                var columnPropertyMapping = typeof(T)
                                  .GetProperties()
                                  .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                               .Select(reader.GetName)
                                                               .Contains(property.Name))
                                  .Select(property => new
                                  {
                                      Property = property,
                                      Ordinal = reader.GetOrdinal(property.Name),
                                      TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                  })
                                  .ToList();

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }
        #region :::Customer Details By ByPhone vinay n 11/11/24:::
        /// <summary>
        /// To get Customer Details By Phone Number
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult getCustomerDetailsByPhone(getCustomerDetailsByPhoneList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            IEnumerable<GNM_Party> Party = null;
            try
            {

                int UserLang = Convert.ToInt32(Obj.UserLanguageID);
                int GenLang = Convert.ToInt32(Obj.GeneralLanguageID);
                bool FilterPartyBasedonCompany = false;
                string filterPartyBasedOnCompanyQuery = @"
                    SELECT Param_value
                    FROM GNM_CompParam
                    WHERE Company_ID = @CompanyID
                    AND Param_Name = 'FilterPartyBasedonCompany'";

                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(filterPartyBasedOnCompanyQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            var result = cmd.ExecuteScalar();
                            if (result != null && result.ToString().ToUpper() == "TRUE")
                            {
                                FilterPartyBasedonCompany = true;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                string partyQuery;
                List<GNM_Party> res = new List<GNM_Party>();
                if (FilterPartyBasedonCompany)
                {

                    partyQuery = @"
            SELECT Party_ID, Party_Name, Party_Mobile, Party_Email, Party_IsActive, PartyType
            FROM GNM_Party
            WHERE Party_IsActive = 1
            AND Party_Mobile = @PhoneNo
            AND (PartyType = 1 OR PartyType = 2)
            AND Company_ID = @CompanyID";
                }
                else
                {

                    partyQuery = @"
            SELECT Party_ID, Party_Name, Party_Mobile, Party_Email, Party_IsActive, PartyType
            FROM GNM_Party
            WHERE Party_IsActive = 1
            AND Party_Mobile = @PhoneNo
            AND (PartyType = 1 OR PartyType = 2)";
                }
                string partyName = "";

                List<SqlParameter> sqlParameters = new List<SqlParameter>
                {
                    new SqlParameter("@PhoneNo", Obj.phoneNo)
                };

                if (FilterPartyBasedonCompany)
                {
                    sqlParameters.Add(new SqlParameter("@CompanyID", Obj.Company_ID));
                }
                res = GetValueFromDB<List<GNM_Party>>(partyQuery, sqlParameters, connString);

                Party = res.AsEnumerable();
                if (Party.Count() == 1)
                {
                    GNM_Party GParty = Party.FirstOrDefault();
                    string query = @"
                    SELECT TOP 1 Party_Name
                    FROM GNM_PartyLocale
                    WHERE Party_ID = @Party_ID AND Language_ID = @Language_ID";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@Party_ID", GParty.Party_ID);
                                cmd.Parameters.AddWithValue("@Language_ID", Obj.Language_ID);





                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                var result = cmd.ExecuteScalar();
                                if (result != null)
                                {
                                    partyName = result.ToString();
                                }
                                else
                                {
                                    partyName = ""; // If null, set to empty string
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    jsonResult = new
                    {
                        Result = "1",
                        Party_ID = GParty.Party_ID,
                        Party_IsLocked = GParty.Party_IsLocked,
                        Party_Name = UserLang == GenLang ? GParty.Party_Name : partyName,
                        Party_Email = GParty.Party_Email,
                        Party_Phone = GParty.Party_Mobile,
                        PartyType = GParty.PartyType == 1 ? "Customer" : "Prospect",
                        Party_IsActive = GParty.Party_IsActive
                    };
                }
                else if (Party.Count() > 1)
                {
                    jsonResult = new
                    {
                        Result = "2"
                    };
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "0"
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
            }

            return new JsonResult(jsonResult);
        }
        #endregion

        #region :::Customer Details By Email vinay n 12/11/24:::
        /// <summary>
        /// To get Customer Details By Email
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult getCustomerDetailsByEmail(getCustomerDetailsByEmailList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            List<GNM_Party> res = new List<GNM_Party>();
            IEnumerable<GNM_Party> Party = null;
            try
            {

                int UserLang = Convert.ToInt32(Obj.UserLanguageID);
                int GenLang = Convert.ToInt32(Obj.GeneralLanguageID);
                bool FilterPartyBasedonCompany = false;
                string filterPartyBasedOnCompanyQuery = @"
                    SELECT Param_value
                    FROM GNM_CompParam
                    WHERE Company_ID = @CompanyID
                    AND Param_Name = 'FilterPartyBasedonCompany'";

                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(filterPartyBasedOnCompanyQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            var result = cmd.ExecuteScalar();
                            if (result != null && result.ToString().ToUpper() == "TRUE")
                            {
                                FilterPartyBasedonCompany = true;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                var partyQuery = "";
                if (FilterPartyBasedonCompany)
                {

                    partyQuery = @"
        SELECT Party_ID, Party_Name, Party_Mobile, Party_Email, Party_IsActive, PartyType
        FROM GNM_Party
        WHERE Party_Email = @Email
        AND (PartyType = 1 OR PartyType = 2)
        AND Company_ID = @CompanyID;";
                }
                else
                {

                    partyQuery = @"
            SELECT Party_ID, Party_Name, Party_Mobile, Party_Email, Party_IsActive, PartyType
                FROM GNM_Party
                WHERE Party_Email = @Email
                AND (PartyType = 1 OR PartyType = 2)";
                }
                string partyName = "";

                List<SqlParameter> sqlParameters = new List<SqlParameter>
                {
                    new SqlParameter("@Email", Obj.EMail)
                };

                if (FilterPartyBasedonCompany)
                {
                    sqlParameters.Add(new SqlParameter("@CompanyID", Obj.Company_ID));
                }
                res = GetValueFromDB<List<GNM_Party>>(partyQuery, sqlParameters, connString);
                Party = res.AsEnumerable();
                if (Party.Count() == 1)
                {
                    GNM_Party GParty = Party.FirstOrDefault();
                    string query = @"
                    SELECT TOP 1 Party_Name
                    FROM GNM_PartyLocale
                    WHERE Party_ID = @Party_ID AND Language_ID = @Language_ID";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@Party_ID", GParty.Party_ID);
                                cmd.Parameters.AddWithValue("@Language_ID", Obj.Language_ID);





                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                var result = cmd.ExecuteScalar();
                                if (result != null)
                                {
                                    partyName = result.ToString();
                                }
                                else
                                {
                                    partyName = ""; // If null, set to empty string
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    jsonResult = new
                    {
                        Result = "1",
                        Party_ID = GParty.Party_ID,
                        Party_IsLocked = GParty.Party_IsLocked,
                        Party_Name = UserLang == GenLang ? GParty.Party_Name : partyName,
                        Party_Email = GParty.Party_Email,
                        Party_Phone = GParty.Party_Mobile,
                        PartyType = GParty.PartyType == 1 ? "Customer" : "Prospect",
                        Party_IsActive = GParty.Party_IsActive
                    };
                }
                else if (Party.Count() > 1)
                {
                    jsonResult = new
                    {
                        Result = "2"
                    };
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "0"
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
            }

            return new JsonResult(jsonResult);
        }
        #endregion

        #region :::Customer Details By Name vinay n 12/11/24:::
        /// <summary>
        /// To get Customer Details By Name
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult getCustomerDetailsByName(getCustomerDetailsByNameList Obj, string connString, int LogException)
        {
            bool FilterPartyBasedonCompany = false;
            var partyQuery = "";
            GNM_Party lipartyLocaleObj = new GNM_Party();
            List<GNM_Party> lipartyLocaleList = new List<GNM_Party>();
            var jsonResult = default(dynamic);
            IEnumerable<GNM_PartyLocale> PartyLocale = null;
            IEnumerable<GNM_Party> Party = null;
            int Company_ID = Convert.ToInt32(Obj.Company_ID);
            Obj.Name = Obj.Name != null ? Uri.UnescapeDataString(Obj.Name) : null;


            try
            {

                int LangID = Obj.Language_ID;
                string filterPartyBasedOnCompanyQuery = @"
                    SELECT Param_value
                    FROM GNM_CompParam
                    WHERE Company_ID = @CompanyID
                    AND Param_Name = 'FilterPartyBasedonCompany'";

                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(filterPartyBasedOnCompanyQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            var result = cmd.ExecuteScalar();
                            if (result != null && result.ToString().ToUpper() == "TRUE")
                            {
                                FilterPartyBasedonCompany = true;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                if (Convert.ToInt32(Obj.UserLanguageID) == Convert.ToInt32(Obj.GeneralLanguageID))
                {
                    List<GNM_Party> res = new List<GNM_Party>();
                    if (FilterPartyBasedonCompany)
                    {

                        partyQuery = @"
           SELECT Party_ID, Party_Name, Party_IsLocked, Party_Email, Party_Mobile, PartyType, Party_IsActive
            FROM GNM_Party
            WHERE Party_Name = @Name
            AND (PartyType = 1 OR PartyType = 2)
            AND Company_ID = @CompanyID";
                    }
                    else
                    {

                        partyQuery = @"
            SELECT Party_ID, Party_Name, Party_IsLocked, Party_Email, Party_Mobile, PartyType, Party_IsActive
            FROM GNM_Party
            WHERE Party_Name = @Name
            AND (PartyType = 1 OR PartyType = 2)";
                    }
                    string partyName = "";
                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                        {
                            new SqlParameter("@Name", Obj.Name)
                        };

                    if (FilterPartyBasedonCompany)
                    {
                        sqlParameters.Add(new SqlParameter("@CompanyID", Obj.Company_ID));
                    }
                    res = GetValueFromDB<List<GNM_Party>>(partyQuery, sqlParameters, connString);
                    Party = res.AsEnumerable();
                    if (Party.Count() == 1)
                    {
                        GNM_Party GParty = Party.FirstOrDefault();
                        jsonResult = new
                        {
                            Result = "1",
                            Party_ID = GParty.Party_ID,
                            Party_IsLocked = GParty.Party_IsLocked,
                            Party_Name = GParty.Party_Name,
                            Party_Email = GParty.Party_Email,
                            Party_Phone = GParty.Party_Mobile,
                            PartyType = GParty.PartyType == 1 ? "Customer" : "Prospect",
                            Party_IsActive = GParty.Party_IsActive
                        };
                    }
                    else if (Party.Count() > 1)
                    {
                        jsonResult = new
                        {
                            Result = "2"
                        };
                    }
                    else if (Party.Count() == 0)
                    {
                        var query = "";
                        if (FilterPartyBasedonCompany)
                        {
                            query = @"
                            SELECT *
                            FROM GNM_Party
                            WHERE Party_Name LIKE '%' + @Name + '%'
                            AND (PartyType = 1 OR PartyType = 2)
                            AND Company_ID = @CompanyID;";
                        }
                        else
                        {
                            query = @"
                    SELECT *
                   FROM GNM_Party
                    WHERE Party_Name LIKE '%' + @Name + '%'
                    AND (PartyType = 1 OR PartyType = 2);";
                        }

                        List<SqlParameter> sqlParameters1 = new List<SqlParameter>
                        {
                            new SqlParameter("@Name", Obj.Name)
                        };

                        if (FilterPartyBasedonCompany)
                        {
                            sqlParameters.Add(new SqlParameter("@CompanyID", Obj.Company_ID));
                        }
                        res = GetValueFromDB<List<GNM_Party>>(partyQuery, sqlParameters1, connString);
                        Party = res.AsEnumerable();
                        if (Party.Count() > 0)
                        {
                            jsonResult = new
                            {
                                Result = "2"
                            };
                        }
                        else
                        {
                            jsonResult = new
                            {
                                Result = "0"
                            };
                        }
                    }
                }
                else
                {
                    var partyLocaleQuery = @"
                        SELECT 
                            a.Party_ID,
                            b.Party_IsLocked,
                            a.Party_Name,
                            b.Party_Email,
                            b.Party_Mobile,
                            b.PartyType,
                            b.Party_IsActive
                        FROM [HCLSoftware_AMP_MicroService_GUI].[dbo].[GNM_PartyLocale] a
                        JOIN [HCLSoftware_AMP_MicroService_GUI].[dbo].[GNM_Party] b ON a.Party_ID = b.Party_ID
                        WHERE a.Party_Name = @Name
                        AND a.Language_ID = @LanguageID
                        AND b.Company_ID = @CompanyID;";
                    List<SqlParameter> sqlParameterspartyLocale = new List<SqlParameter>
                    {
                        new SqlParameter("@Name", Obj.Name),
                        new SqlParameter("@LanguageID", Obj.Language_ID),
                        new SqlParameter("@CompanyID", Obj.Company_ID)
                    };



                    lipartyLocaleList = GetValueFromDB<List<GNM_Party>>(partyLocaleQuery, sqlParameterspartyLocale, connString);



                    var lipartyLocale = (from a in lipartyLocaleList

                                         select new
                                         {
                                             a.Party_ID,
                                             a.Party_IsLocked,
                                             a.Party_Name,
                                             a.Party_Email,
                                             a.Party_Mobile,
                                             a.PartyType,
                                             a.Party_IsActive
                                         });

                    if (lipartyLocale.Count() == 1)
                    {
                        var GParty = lipartyLocale.FirstOrDefault();
                        jsonResult = new
                        {
                            Result = "1",
                            Party_ID = GParty.Party_ID,
                            Party_IsLocked = GParty.Party_IsLocked,
                            Party_Name = GParty.Party_Name,
                            Party_Email = GParty.Party_Email,
                            Party_Phone = GParty.Party_Mobile,
                            PartyType = GParty.PartyType == 1 ? "Customer" : "Prospect",
                            Party_IsActive = GParty.Party_IsActive
                        };
                    }
                    else if (lipartyLocale.Count() > 1)
                    {
                        jsonResult = new
                        {
                            Result = "2"
                        };
                    }
                    else if (lipartyLocale.Count() == 0)
                    {
                        int count = 0;
                        using (SqlConnection conn = new SqlConnection(connString))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(@"
                                SELECT COUNT(*) 
                                FROM GNM_PartyLocale pa
                                INNER JOIN GNM_Party b ON pa.Party_ID = b.Party_ID
                                WHERE pa.Party_Name LIKE @PartyName
                                AND b.Company_ID = @CompanyID", conn))
                                {
                                    cmd.CommandType = CommandType.Text;
                                    cmd.Parameters.AddWithValue("@PartyName", "%" + Obj.Name + "%");
                                    cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);




                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    count = (int)cmd.ExecuteScalar();

                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }

                        if (count > 0)
                        {
                            jsonResult = new
                            {
                                Result = "2"
                            };
                        }
                        else
                        {
                            jsonResult = new
                            {
                                Result = "0"
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
            }

            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: InsertUnregisteredServiceRequest vinay n 12/11/24:::
        /// <summary>
        /// Inser tUnregistered Service Request Record
        /// </summary>   
        public static IActionResult InsertUnregisteredServiceRequest(InsertUnregisteredServiceRequestList Obj, string connString, int LogException)
        {

            int BranchID = Convert.ToInt32(Obj.Branch.ToString());
            int userID = Obj.User_ID;
            int CompanyID = Obj.Company_ID;
            var x = default(dynamic);
            int UnregisteredServiceRequest_ID = -1;
            int? id = null;
            try
            {
                JTokenReader jsonReader = null;
                JObject jObj = JObject.Parse(Obj.data);

                jsonReader = new JTokenReader(jObj["PartyID"]);
                jsonReader.Read();
                int PartyID = Convert.ToInt32(jsonReader.Value);

                jsonReader = new JTokenReader(jObj["phone"]);
                jsonReader.Read();
                string phone = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["ReqDesc"]);
                jsonReader.Read();
                string ReqDesc = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["ProductUniqueNumber"]);
                jsonReader.Read();
                string ProductUniqueNumber = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["Status"]);
                jsonReader.Read();
                Byte Status = Convert.ToByte(jsonReader.Value);

                jsonReader = new JTokenReader(jObj["Email"]);
                jsonReader.Read();
                string Email = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["ServiceRequestID"]);
                jsonReader.Read();
                string ServiceRequestID = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["BrandID"]);
                jsonReader.Read();
                string BrandID = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["ModelID"]);
                jsonReader.Read();
                string ModelID = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["ProductTypeID"]);
                jsonReader.Read();
                string ProductTypeID = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["SerialNumber"]);
                jsonReader.Read();
                string SerialNumber = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["Remarks"]);
                jsonReader.Read();
                string Remarks = jsonReader.Value.ToString();



                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string insertQuery = @"
                   DECLARE @InsertedID TABLE (UnregisteredServiceRequest_ID INT);
                INSERT INTO HD_UnregisteredServiceRequest 
                    (Company_ID, Product_Unique_Number, Party_ID, RequestDescription, Brand_ID, Model_ID, 
                     ProductType_ID, SerialNumber, Email_ID, Mobile, Date, ServiceRequest_ID, Remarks, Status) 
                    OUTPUT INSERTED.UnregisteredServiceRequest_ID INTO @InsertedID
                VALUES 
                    (@Company_ID, @Product_Unique_Number, @Party_ID, @RequestDescription, @Brand_ID, @Model_ID, 
                     @ProductType_ID, @SerialNumber, @Email_ID, @Mobile, @Date, @ServiceRequest_ID, @Remarks, @Status)
                     SELECT TOP 1 UnregisteredServiceRequest_ID FROM @InsertedID;";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(insertQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                            cmd.Parameters.AddWithValue("@Product_Unique_Number", Common.DecryptString(ProductUniqueNumber));
                            cmd.Parameters.AddWithValue("@Party_ID", PartyID);
                            cmd.Parameters.AddWithValue("@RequestDescription", Common.DecryptString(ReqDesc));
                            cmd.Parameters.AddWithValue("@Brand_ID", BrandID == "" ? id : Convert.ToInt32(BrandID));
                            cmd.Parameters.AddWithValue("@Model_ID", ModelID == "0" ? id : Convert.ToInt32(ModelID));
                            cmd.Parameters.AddWithValue("@ProductType_ID", ProductTypeID == "" ? id : Convert.ToInt32(ProductTypeID));
                            cmd.Parameters.AddWithValue("@SerialNumber", SerialNumber == "0" ? null : Common.DecryptString(SerialNumber));
                            cmd.Parameters.AddWithValue("@Email_ID", Common.DecryptString(Email));
                            cmd.Parameters.AddWithValue("@Mobile", phone);
                            cmd.Parameters.AddWithValue("@Date", Common.LocalTimeBasedOnBranch(BranchID, Convert.ToDateTime(DateTime.Now), connString));
                            cmd.Parameters.AddWithValue("@ServiceRequest_ID",
                           string.IsNullOrEmpty(ServiceRequestID) ? (object)DBNull.Value : Convert.ToInt32(ServiceRequestID));
                            cmd.Parameters.AddWithValue("@Remarks", Common.DecryptString(Remarks));
                            cmd.Parameters.AddWithValue("@Status", Status);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }


                            object result = cmd.ExecuteScalar();
                            if (result != null)
                            {
                                UnregisteredServiceRequest_ID = Convert.ToInt32(result);
                            }


                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("HelpDeskUnregisteredServiceRequest")), InsRow.UnregisteredServiceRequest_ID, 0, 0, "Insert", false, Convert.ToInt32(Obj.MenuID));
                x = new
                {
                    UnregisteredServiceRequest_ID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                x = new
                {
                    UnregisteredServiceRequest_ID = 0
                };
            }
            return new JsonResult(x);
        }
        #endregion

        #region ::: SelectSingleUnRegisteredSeviceRequest vinay n 12/11/24:::
        /// <summary>
        /// to Select Single UnRegistered Sevice Request
        /// </summary>   
        public static IActionResult SelectSingleUnRegisteredSeviceRequest(SelectSingleUnRegisteredSeviceRequestList Obj, string connString, int LogException)
        {


            var jsonResult = default(dynamic);
            int ProductID = 0;
            GNM_Product liProduct = null;
            List<UnRegisteredServiceRequest> UnReg = new List<UnRegisteredServiceRequest>();
            try
            {

                string Pending = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Pending").ToString();
                string locked = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "locked").ToString();
                string Registered = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Registered").ToString();
                string Abandoned = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Abandoned").ToString();
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand("UP_SELECT_HelpDesk_SelectSingleUnRegisteredSeviceRequest_HelpDeskUnregisteredServiceRequest", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@UserLanguageID", Convert.ToInt32(Obj.UserLanguageID));
                            cmd.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(Obj.GeneralLanguageID));

                            cmd.Parameters.AddWithValue("@UnregisteredServiceRequest_ID", Obj.id);
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (var reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    while (reader.Read())
                                    {
                                        var unRegObj = new UnRegisteredServiceRequest();
                                        unRegObj.ServiceRequest_id = reader.IsDBNull(reader.GetOrdinal("ServiceRequest_id"))
                                        ? (int?)null
                                        : reader.GetInt32(reader.GetOrdinal("ServiceRequest_id"));


                                        unRegObj.Product_Unique_Number = reader.IsDBNull(reader.GetOrdinal("Product_Unique_Number"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Product_Unique_Number"));


                                        unRegObj.Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("Party_ID"));


                                        unRegObj.Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Party_Name"));


                                        unRegObj.RequestDescription = reader.IsDBNull(reader.GetOrdinal("RequestDescription"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("RequestDescription"));


                                        unRegObj.Brand_ID = reader.IsDBNull(reader.GetOrdinal("Brand_ID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("Brand_ID"));


                                        unRegObj.RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"));


                                        unRegObj.ProductType_ID = reader.IsDBNull(reader.GetOrdinal("ProductType_ID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("ProductType_ID"));


                                        unRegObj.ProductType_Name = reader.IsDBNull(reader.GetOrdinal("ProductType_Name"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("ProductType_Name"));


                                        unRegObj.Model_ID = reader.IsDBNull(reader.GetOrdinal("Model_ID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("Model_ID"));


                                        unRegObj.Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Model_Name"));


                                        unRegObj.SerialNumber = reader.IsDBNull(reader.GetOrdinal("SerialNumber"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("SerialNumber"));


                                        unRegObj.Email_ID = reader.IsDBNull(reader.GetOrdinal("Email_ID"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Email_ID"));


                                        unRegObj.Mobile = reader.IsDBNull(reader.GetOrdinal("Mobile"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Mobile"));


                                        unRegObj.Date = reader.IsDBNull(reader.GetOrdinal("Date"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Date"));




                                        unRegObj.ServiceRequest_ID = reader.IsDBNull(reader.GetOrdinal("ServiceRequest_ID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID"));


                                        unRegObj.ServiceRequestNumber = reader.IsDBNull(reader.GetOrdinal("ServiceRequestNumber"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("ServiceRequestNumber"));


                                        unRegObj.ServiceRequestDate = reader.IsDBNull(reader.GetOrdinal("ServiceRequestDate"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("ServiceRequestDate"));


                                        unRegObj.StatusID = reader.IsDBNull(reader.GetOrdinal("StatusID"))
                                            ? (int?)null
                                            : reader.GetInt32(reader.GetOrdinal("StatusID"));


                                        unRegObj.Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks"))
                                            ? null
                                            : reader.GetString(reader.GetOrdinal("Remarks"));
                                        unRegObj.Status = unRegObj.StatusID == 1 ? Pending :
                                      unRegObj.StatusID == 2 ? locked :
                                      unRegObj.StatusID == 3 ? Registered : Abandoned;


                                        UnReg.Add(unRegObj);
                                    }
                                }
                                reader.NextResult();
                                if (reader.HasRows)
                                {
                                    while (reader.Read())
                                    {
                                        ProductID = reader.GetInt32(reader.GetOrdinal("ProductID"));
                                    }
                                }

                            }





                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }






                //UnReg = from a in _UnRegList
                //        select new
                //        {
                //            ServiceRequest_id = a.ServiceRequest_id, // Corrected field name
                //            Product_unique_no = a.Product_Unique_Number,
                //            a.Party_ID,
                //            Party_Name = a.Party_Name,
                //            RequestDescription = a.RequestDescription,
                //            a.Brand_ID,
                //            RefMasterDetail_Name = a.RefMasterDetail_Name,
                //            a.ProductType_ID,
                //            ProductType_Name = a.ProductType_Name,
                //            a.Model_ID,
                //            Model_Name = a.Model_Name,
                //            a.SerialNumber,
                //            Email_ID = a.Email_ID,
                //            a.Mobile,
                //            Date = a.Date != null ? Convert.ToDateTime(a.Date).ToString("dd-MMM-yyyy") : null, // Handle null Date
                //            a.ServiceRequest_ID,
                //            a.ServiceRequestNumber,
                //            ServiceRequestDate = a.ServiceRequestDate != null ? Convert.ToDateTime(a.ServiceRequestDate).ToString("dd-MMM-yyyy") : null, // Handle null ServiceRequestDate
                //            a.Remarks,
                //            StatusID = a.StatusID,
                //            Status = a.StatusID == 1 ? "Pending" :
                //                     a.StatusID == 2 ? "Locked" :
                //                     a.StatusID == 3 ? "Registered" : "Abandoned"
                //        };



                jsonResult = new
                {
                    UnReg,
                    ProductID
                };


                if (UnReg.First().StatusID == 3 || UnReg.First().StatusID == 4) { }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand("UPDATE HD_UnregisteredServiceRequest SET Status = @Status WHERE UnregisteredServiceRequest_ID = @UnregisteredServiceRequest_ID", conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@Status", 2);
                                cmd.Parameters.AddWithValue("@UnregisteredServiceRequest_ID", Obj.id);
                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                cmd.ExecuteNonQuery();





                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: Get Object ID vinay n 12/11/24:::
        /// <summary>
        /// To  Get ObjectID
        /// </summary>        
        public static int GetObjectID(GetObjectIDList Obj, string connString, int LogException)
        {
            int ObjectID = 0;
            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string query = "SELECT TOP 1 Object_ID FROM GNM_Object WHERE LTRIM(RTRIM(Object_Name)) = @Object_Name";
                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@Object_Name", Obj.name);
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            var result = cmd.ExecuteScalar();

                            if (result != DBNull.Value && result != null)
                            {
                                ObjectID = Convert.ToInt32(result);
                            }



                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return ObjectID;
        }
        #endregion


        #region ::: Get Brand Product Type vinay n 12/11/24:::
        /// <summary>
        /// To  Get Brand and Product Type
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult getBrandProductType(getBrandProductTypeList Obj, string ConnString, int LogException)
        {
            var jsonResult = default(dynamic);
            GNM_RefMasterDetail Brand = null;
            GNM_RefMasterDetailLocale BrandLocale = null;
            GNM_ProductType ProdType = null;
            GNM_ProductTypeLocale ProdTypeLocale = null;
            CoreModelMasterServices.GNM_Model Model = null;
            try
            {
                string GenLangCode = Obj.GeneralLanguageCode.ToString();
                string UserLangCode = Obj.UserLanguageCode.ToString();

                string modelQuery = "SELECT * FROM GNM_Model WHERE Model_ID = @ModelID";

                List<SqlParameter> modelParams = new List<SqlParameter>
                {
                    new SqlParameter("@ModelID", Obj.ModelID)
                };
                Model = GetValueFromDB<CoreModelMasterServices.GNM_Model>(modelQuery, modelParams, ConnString);

                if (Model != null)
                {
                    string brandQuery = "SELECT * FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @BrandID";
                    List<SqlParameter> brandParams = new List<SqlParameter>
                    {
                        new SqlParameter("@BrandID", Model.Brand_ID)
                    };
                    Brand = GetValueFromDB<GNM_RefMasterDetail>(brandQuery, brandParams, ConnString);
                    string prodTypeQuery = "SELECT * FROM GNM_ProductType WHERE ProductType_ID = @ProductTypeID";

                    List<SqlParameter> prodTypeParams = new List<SqlParameter>
                    {
                        new SqlParameter("@ProductTypeID", Model.ProductType_ID)
                    };
                    ProdType = GetValueFromDB<GNM_ProductType>(prodTypeQuery, prodTypeParams, ConnString);
                    if (GenLangCode != UserLangCode)
                    {
                        string brandLocaleQuery = "SELECT * FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID = @BrandID AND Language_ID = @LanguageID";
                        List<SqlParameter> brandLocaleParams = new List<SqlParameter>
                        {
                            new SqlParameter("@BrandID", Model.Brand_ID),
                            new SqlParameter("@LanguageID", Obj.Language_ID)
                        };
                        BrandLocale = GetValueFromDB<GNM_RefMasterDetailLocale>(brandLocaleQuery, brandLocaleParams, ConnString);
                        string prodTypeLocaleQuery = "SELECT * FROM GNM_ProductTypeLocale WHERE ProductType_ID = @ProductTypeID AND Language_ID = @LanguageID";
                        List<SqlParameter> prodTypeLocaleParams = new List<SqlParameter>
                        {
                            new SqlParameter("@ProductTypeID", Model.ProductType_ID),
                            new SqlParameter("@LanguageID", Obj.Language_ID)
                        };

                        ProdTypeLocale = GetValueFromDB<GNM_ProductTypeLocale>(prodTypeLocaleQuery, prodTypeLocaleParams, ConnString);


                    }


                }



                if (Model != null)
                {




                    jsonResult = new
                    {
                        Result = "1",// Exists     
                        BrandID = Brand.RefMasterDetail_ID,
                        BrandName = (GenLangCode == UserLangCode) ? Brand.RefMasterDetail_Name : (BrandLocale == null) ? "" : BrandLocale.RefMasterDetail_Name,
                        ProductTypeID = ProdType.ProductType_ID,
                        ProdTypeName = (GenLangCode == UserLangCode) ? ProdType.ProductType_Name : (ProdTypeLocale == null) ? "" : ProdTypeLocale.ProductType_Name
                    };
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "0"//Not Exists                 
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: UpdateUnregisteredServiceRequest vinay n 12/11/24:::
        /// <summary>
        /// UpdateUnregisteredServiceRequest
        /// </summary>   
        public static IActionResult UpdateUnregisteredServiceRequest(UpdateUnregisteredServiceRequestList Obj, string connString, int LogException)
        {
            var x = default(dynamic);
            int currentStatus = -1;
            try
            {
                string checkStatusQuery = @"
                SELECT Status 
                FROM HD_UnregisteredServiceRequest 
                WHERE UnregisteredServiceRequest_ID = @UnregisteredServiceRequestID";
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(checkStatusQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@UnregisteredServiceRequestID", Obj.Unred_ID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            object result = cmd.ExecuteScalar();
                            if (result != null)
                            {
                                currentStatus = Convert.ToInt32(result);
                            }





                        }

                        if (currentStatus != 3 && currentStatus != 4)
                        {
                            string updateQuery = @"
                            UPDATE HD_UnregisteredServiceRequest 
                            SET Status = @NewStatus 
                            WHERE UnregisteredServiceRequest_ID = @UnregisteredServiceRequestID";
                            using (SqlCommand updateCmd = new SqlCommand(updateQuery, conn))
                            {
                                updateCmd.Parameters.AddWithValue("@NewStatus", Obj.status);
                                updateCmd.Parameters.AddWithValue("@UnregisteredServiceRequestID", Obj.Unred_ID);
                                updateCmd.ExecuteNonQuery();
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }


                x = new
                {
                    Obj.Unred_ID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion
        #region ::: UnRegisteredServiceExport vinay n 12/11/24 :::
        /// <summary>
        /// Exporting Company Grid
        /// </summary>       
        public static async Task<object> UnRegisteredServiceExport(SelectUnRegisteredSeviceRequestList Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            DataTable dt = new DataTable();
            string Pending = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Pending").ToString();
            string locked = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "locked").ToString();
            try
            {

                //List<UnRegisteredServiceObject> arrUnRegistered = ((IEnumerable<UnRegisteredServiceObject>)Session["iQUnServiceReq"]).ToList();
                List<UnRegisteredServiceObject> arrUnRegistered = new List<UnRegisteredServiceObject>();
                GetUnRegisteredSeviceRequest2List requestObj = new GetUnRegisteredSeviceRequest2List
                {
                    UserCulture = Obj.UserCulture,
                    frmdate = Obj.FromDate,
                    todate = Obj.ToDate,
                    Company_ID = Obj.Company_ID,
                    UserLanguageID = Obj.UserLanguageID,
                    GeneralLanguageID = Obj.GeneralLanguageID,
                    Language_ID = Obj.LanguageID
                };
                IQueryable<UnRegisteredServiceObject> Result = GetUnRegisteredSeviceRequest(requestObj, connString, LogException);

                if (filters != null && filters.ToString() != "null")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                    {
                        Result = Result.FilterSearch<UnRegisteredServiceObject>(filtersObj);
                    }
                    else
                    {
                        Result = Result.AsQueryable<UnRegisteredServiceObject>().Where(a => a.Req_Status == Pending || a.Req_Status == locked);
                    }
                }
                if (Query != "null" && Query != null)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    Result = Result.AdvanceSearch<UnRegisteredServiceObject>(advnfilter);
                }
                else
                {
                    Result = Result.AsQueryable<UnRegisteredServiceObject>().Where(a => a.Req_Status == Pending || a.Req_Status == locked);
                }

                Result = Result.OrderByField<UnRegisteredServiceObject>(sidx, sord);
                arrUnRegistered = Result.ToList();
                //Changes Ends

                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PartyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Brand").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "model").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "serialnumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Mobile").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "status").ToString());

                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("Date");
                dtAlignment.Columns.Add("PartyName");
                dtAlignment.Columns.Add("Brand");
                dtAlignment.Columns.Add("model");
                dtAlignment.Columns.Add("serialnumber");
                dtAlignment.Columns.Add("Mobile");
                dtAlignment.Columns.Add("status");
                dtAlignment.Rows.Add(0, 0, 0, 0, 0, 2, 0);

                int cnt = arrUnRegistered.AsEnumerable().Count();
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(arrUnRegistered.ElementAt(i).ReqDate, arrUnRegistered.ElementAt(i).PartyName, arrUnRegistered.ElementAt(i).BrandName, arrUnRegistered.ElementAt(i).ModelName, arrUnRegistered.ElementAt(i).SerialNumber, arrUnRegistered.ElementAt(i).Req_Mobile, arrUnRegistered.ElementAt(i).Req_Status);
                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = Obj.Company_ID,
                    Branch = Obj.Branch,
                    dt1 = dtAlignment,
                    dt = dt,
                    FileName = "VerificationQueue",
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "VerificationQueue").ToString(),
                    exprtType = Obj.exprtType,
                    UserCulture = Obj.UserCulture
                };
                var res = await DocumentExport.Export(reportExportList, connString, LogException);
                return res.Value;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


        #region ::: Get Party Details by ID vinay n 12/11/24:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetPartyDetailsbyID(GetPartyDetailsbyIDList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            GNM_Party Party = null;
            string partyName = "";
            try
            {

                int LangID = Obj.Language_ID;
                int UserLang = Convert.ToInt32(Obj.UserLanguageID);
                int GenLang = Convert.ToInt32(Obj.GeneralLanguageID);
                string partyQuery = @"
                SELECT Party_ID, Party_Name, Party_IsLocked, Party_Email, Party_Mobile, PartyType 
                FROM GNM_Party 
                WHERE Party_ID = @PartyID";
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(partyQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@PartyID", Obj.PartyID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                Party = new GNM_Party();
                                if (reader.Read())
                                {

                                    Party.Party_IsLocked = reader.GetBoolean(reader.GetOrdinal("Party_IsLocked"));
                                    Party.Party_Name = reader.GetString(reader.GetOrdinal("Party_Name"));
                                    Party.Party_Email = reader.GetString(reader.GetOrdinal("Party_Email"));
                                    Party.Party_Mobile = reader.GetString(reader.GetOrdinal("Party_Mobile"));
                                    Party.PartyType = reader.GetByte(reader.GetOrdinal("PartyType"));
                                }
                            }

                            if (UserLang != GenLang)
                            {
                                string partyLocaleQuery = @"
                                SELECT Party_Name 
                                FROM GNM_PartyLocale 
                                WHERE Party_ID = @PartyID AND Language_ID = @UserLanguageID";
                                SqlCommand partyLocaleCmd = new SqlCommand(partyLocaleQuery, conn);
                                partyLocaleCmd.Parameters.AddWithValue("@PartyID", Obj.PartyID);
                                partyLocaleCmd.Parameters.AddWithValue("@UserLanguageID", Obj.Language_ID);
                                using (SqlDataReader localeReader = partyLocaleCmd.ExecuteReader())
                                {
                                    if (localeReader.Read())
                                    {
                                        partyName = localeReader.GetString(localeReader.GetOrdinal("Party_Name"));
                                    }
                                }

                            }


                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                jsonResult = new
                {
                    Result = "1",
                    BelongsTo = "Party",
                    Party_ID = Obj.PartyID,
                    Party_IsLocked = Party.Party_IsLocked,
                    Party_Name = UserLang == GenLang ? Party.Party_Name : partyName,
                    Party_Email = Party.Party_Email,
                    Party_Phone = Party.Party_Mobile,
                    PartyType = (Party.PartyType == 1) ? "Customer" : "Prospect"
                };
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: Product Master Save vinay n 12/11/24:::
        /// <summary>
        /// To save the Product Master
        /// </summary>      
        public static IActionResult ProductMasterSave(ProductMasterSaveObj Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            JTokenReader jr = null;
            int PartyID = 0;
            int BranchID = Convert.ToInt32(Obj.Branch.ToString());
            GNM_Product Prod = null;
            int partyID = 0;
            GNM_Product ProdExists = null;
            int count = 0;
            string PName = string.Empty;
            int ProductCustomerID = 0;
            int productID = 0;
            GNM_ProductCustomer ProdCustomer = new GNM_ProductCustomer();
            try
            {

                int LangID = Obj.Language_ID;
                int CompanyID = Obj.Company_ID;
                var GenLangCode = Obj.GeneralLanguageCode.ToString();
                var UserLangCode = Obj.UserLanguageCode.ToString();

                Prod = JObject.Parse(Obj.Data).ToObject<GNM_Product>();
                JObject jObj = JObject.Parse(Obj.Data);
                jr = new JTokenReader(jObj["Party_ID"]);
                jr.Read();
                PartyID = Convert.ToInt32(jr.Value);
                Prod.Product_SerialNumber = Common.DecryptString(Prod.Product_SerialNumber);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string productCheckQuery = @"
                SELECT COUNT(*) 
                FROM GNM_Product 
                WHERE Model_ID = @ModelID AND Product_SerialNumber = @ProductSerialNumber";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(productCheckQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@ModelID", Prod.Model_ID);
                            cmd.Parameters.AddWithValue("@ProductSerialNumber", Prod.Product_SerialNumber);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            count = (int)cmd.ExecuteScalar();

                            if (count > 0)
                            {
                                string productQuery = @"
                                SELECT Product_ID, IsActive
                                FROM GNM_Product
                                WHERE Model_ID = @ModelID AND Product_SerialNumber = @ProductSerialNumber";
                                SqlCommand productCmd = new SqlCommand(productQuery, conn);
                                cmd.Parameters.AddWithValue("@ModelID", Prod.Model_ID);
                                cmd.Parameters.AddWithValue("@ProductSerialNumber", Prod.Product_SerialNumber);

                                bool isActive = false;
                                using (SqlDataReader productReader = productCmd.ExecuteReader())
                                {
                                    if (productReader.Read())
                                    {
                                        productID = productReader.GetInt32(productReader.GetOrdinal("Product_ID"));
                                        isActive = productReader.GetBoolean(productReader.GetOrdinal("IsActive"));
                                    }
                                }
                                if (isActive)
                                {
                                    string productCustomerQuery = @"
                                        SELECT Party_ID 
                                        FROM GNM_ProductCustomer
                                        WHERE Product_ID = @ProductID AND ProductCustomer_ToDate IS NULL";
                                    SqlCommand productCustomerCmd = new SqlCommand(productCustomerQuery, conn);
                                    productCustomerCmd.Parameters.AddWithValue("@ProductID", productID);

                                    using (SqlDataReader productCustomerReader = productCustomerCmd.ExecuteReader())
                                    {
                                        if (productCustomerReader.Read())
                                        {
                                            partyID = productCustomerReader.GetInt32(productCustomerReader.GetOrdinal("Party_ID"));
                                        }
                                    }
                                    if (partyID > 0)
                                    {
                                        string partyNameQuery = GenLangCode == UserLangCode
                                           ? @"
                                                SELECT Party_Name
                                                FROM GNM_Party
                                                WHERE Party_ID = @PartyID"
                                           : @"
                                                SELECT Party_Name
                                                FROM GNM_PartyLocale
                                                WHERE Party_ID = @PartyID AND Language_ID = @LangID";
                                        SqlCommand partyNameCmd = new SqlCommand(partyNameQuery, conn);
                                        partyNameCmd.Parameters.AddWithValue("@PartyID", partyID);
                                        partyNameCmd.Parameters.AddWithValue("@LangID", Obj.Language_ID);

                                        using (SqlDataReader partyNameReader = partyNameCmd.ExecuteReader())
                                        {
                                            if (partyNameReader.Read())
                                            {
                                                PName = partyNameReader.GetString(partyNameReader.GetOrdinal("Party_Name"));
                                            }
                                        }
                                    }
                                    jsonResult = new
                                    {
                                        Result = "Exists",
                                        Name = PName,
                                        ID = (partyID == 0) ? 0 : partyID
                                    };

                                }
                                else
                                {
                                    jsonResult = new
                                    {
                                        Result = "InActive",
                                        Name = "",
                                        ID = 0
                                    };
                                }
                            }
                            else
                            {
                                string insertProductQuery = @"
                        INSERT INTO GNM_Product (Company_ID, IsActive, ModifiedBy, ModifiedDate)
                        VALUES (@CompanyID, 1, @UserID, @ModifiedDate);
                        SELECT SCOPE_IDENTITY();";
                                using (SqlConnection connnt = new SqlConnection(connString))
                                {

                                    using (SqlTransaction transaction = conn.BeginTransaction())
                                    {
                                        SqlCommand command = null;

                                        try
                                        {
                                            using (command = new SqlCommand(insertProductQuery, conn, transaction))
                                            {
                                                command.CommandType = CommandType.Text;
                                                command.Parameters.AddWithValue("@CompanyID", CompanyID);
                                                command.Parameters.AddWithValue("@UserID", Obj.User_ID);
                                                command.Parameters.AddWithValue("@ModifiedDate", Common.LocalTimeBasedOnBranch(BranchID, Convert.ToDateTime(DateTime.Now), connString));


                                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                                {
                                                    conn.Open();
                                                }
                                                productID = Convert.ToInt32(command.ExecuteScalar());
                                                string insertProductCustomerQuery = @"
                                            INSERT INTO GNM_ProductCustomer ( Party_ID, ProductCustomer_FromDate, ProductCustomer_ToDate)
                                            VALUES ( @PartyID, @FromDate, NULL);
                                            SELECT SCOPE_IDENTITY();"
                                                ;

                                                SqlCommand insertProductCustomerCmd = new SqlCommand(insertProductCustomerQuery, conn, transaction);

                                                insertProductCustomerCmd.Parameters.AddWithValue("@PartyID", PartyID);
                                                insertProductCustomerCmd.Parameters.AddWithValue("@FromDate", Common.LocalTimeBasedOnBranch(BranchID, DateTime.Now, connString));
                                                object result = insertProductCustomerCmd.ExecuteScalar();
                                                if (result != DBNull.Value)
                                                {
                                                    ProductCustomerID = Convert.ToInt32(result);
                                                }



                                                string selectSalesHistoryQuery = @"
                                                SELECT * 
                                                FROM CoreSLT_SALESHISTORY 
                                                WHERE PRODUCT_ID = @ProductID";
                                                SqlCommand selectSalesHistoryCmd = new SqlCommand(selectSalesHistoryQuery, conn, transaction);
                                                selectSalesHistoryCmd.Parameters.AddWithValue("@ProductID", productID);
                                                SqlDataReader reader = selectSalesHistoryCmd.ExecuteReader();
                                                int isWholesale = 2;
                                                if (reader.HasRows)
                                                {
                                                    reader.Close();
                                                    string selectRetailCountQuery = @"
                                                        SELECT * 
                                                        FROM CoreSLT_SALESHISTORY 
                                                        WHERE PRODUCT_ID = @ProductID AND ISWHOLESALE = 2";
                                                    SqlCommand selectRetailCountCmd = new SqlCommand(selectRetailCountQuery, conn, transaction);
                                                    selectRetailCountCmd.Parameters.AddWithValue("@ProductID", productID);
                                                    SqlDataReader retailReader = selectRetailCountCmd.ExecuteReader();
                                                    if (retailReader.HasRows)
                                                    {
                                                        isWholesale = 0;
                                                    }
                                                    retailReader.Close();

                                                }
                                                else
                                                {
                                                    isWholesale = 2;
                                                }
                                                string insertSalesHistoryQuery = @"
                                            INSERT INTO CoreSLT_SALESHISTORY (COMPANY_ID, PRODUCT_ID, ProductCustomer_ID, ISWHOLESALE, SELLINGPRICE, SALESINVOICE_NUMBER)
                                            VALUES (@CompanyID, @ProductID, @ProductCustomerID, @ISWHOLESALE, @SellingPrice, @SalesInvoiceNumber)";
                                                SqlCommand insertSalesHistoryCmd = new SqlCommand(insertSalesHistoryQuery, conn, transaction);
                                                insertSalesHistoryCmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                                                insertSalesHistoryCmd.Parameters.AddWithValue("@ProductID", productID);
                                                insertSalesHistoryCmd.Parameters.AddWithValue("@ProductCustomerID", ProductCustomerID);
                                                insertSalesHistoryCmd.Parameters.AddWithValue("@ISWHOLESALE", isWholesale);
                                                insertSalesHistoryCmd.Parameters.AddWithValue("@SellingPrice", 0.00M);
                                                insertSalesHistoryCmd.Parameters.AddWithValue("@SalesInvoiceNumber", string.Empty);
                                                insertSalesHistoryCmd.ExecuteNonQuery();
                                                transaction.Commit();

                                                jsonResult = new
                                                {
                                                    Result = "Success",
                                                    Product_ID = productID

                                                };
                                            }


                                        }
                                        catch (Exception ex)
                                        {
                                            if (LogException == 1)
                                            {
                                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                            }

                                        }
                                        finally
                                        {
                                            cmd.Dispose();
                                            conn.Close();
                                            conn.Dispose();
                                            SqlConnection.ClearAllPools();
                                        }
                                    }
                                }
                            }


                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }












                //end
                // saving in product Table                   


                // gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreProductMaster")), Prod.Product_ID, 0, 0, "Insert", false, Convert.ToInt32(Session["MenuID"]));



            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: Product Master Save after confirmation vinay n 15/11/24:::
        /// <summary>
        /// To save the Product Master
        /// </summary>      
        public static IActionResult ProductMasterSaveConfirm(ProductMasterSaveConfirmList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            JTokenReader jr = null;
            int BranchID = Convert.ToInt32(Obj.Branch.ToString());
            int PartyID = 0;
            GNM_Product Prod = null;

            try
            {

                int LangID = Obj.Language_ID;
                int CompanyID = Obj.Company_ID;
                Prod = JObject.Parse(Obj.Data).ToObject<GNM_Product>();
                JObject jObj = JObject.Parse(Obj.Data);
                jr = new JTokenReader(jObj["Party_ID"]);
                jr.Read();
                PartyID = Convert.ToInt32(jr.Value);
                Prod.Product_SerialNumber = Common.DecryptString(Prod.Product_SerialNumber);
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand("UP_SAVE_HelpDesk_ProductMasterSaveConfirm_HelpDeskUnregisteredServiceRequest", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Model_ID", Prod.Model_ID);
                            cmd.Parameters.AddWithValue("@Product_SerialNumber", Prod.Product_SerialNumber);
                            cmd.Parameters.AddWithValue("@Party_ID", PartyID);
                            cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                            cmd.Parameters.AddWithValue("@Branch_ID", BranchID);
                            cmd.Parameters.AddWithValue("@CurrentTime", Common.LocalTimeBasedOnBranch(BranchID, Convert.ToDateTime(DateTime.Now), connString));


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }




                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                jsonResult = new
                {
                    Result = "Success"
                };
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: Get Party Detail Grid vinay n 15/11/24:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectPartyDetailGrid(SelectPartyDetailGridList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonobj = default(dynamic);
            IEnumerable<GNM_Party> Party = null;
            IEnumerable<GNM_PartyLocale> PartyLocale = null;
            bool FilterPartyBasedonCompany = false;
            int total = 0;
            try
            {

                string GenLangCode = Obj.GeneralLanguageCode.ToString();
                string UserLangCode = Obj.UserLanguageCode.ToString();
                if (GenLangCode == UserLangCode)
                {
                    string filterPartyBasedOnCompanyQuery = @"
                    SELECT Param_value
                    FROM GNM_CompParam
                    WHERE Company_ID = @CompanyID
                    AND UPPER(Param_Name) = 'FILTERPARTYBASEDONCOMPANY'";

                    using (SqlConnection conn = new SqlConnection(constring))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(filterPartyBasedOnCompanyQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);





                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                var result = cmd.ExecuteScalar();
                                if (result != null && result.ToString().ToUpper() == "TRUE")
                                {
                                    FilterPartyBasedonCompany = true;
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    string partyQuery = "";
                    if (FilterPartyBasedonCompany)
                    {
                        partyQuery = @"SELECT * FROM GNM_Party 
                          WHERE Party_Name LIKE '%' + @PartyName + '%' 
                          AND Company_ID = @Company_ID 
                          AND Party_IsActive = 1 
                          AND (PartyType = 1 OR PartyType = 2)";
                    }
                    else
                    {
                        partyQuery = @"SELECT * FROM GNM_Party 
                          WHERE Party_Name LIKE '%' + @PartyName + '%' 
                          AND Party_IsActive = 1 
                          AND (PartyType = 1 OR PartyType = 2)";
                    }
                    using (SqlConnection conn = new SqlConnection(constring))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(partyQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@PartyName", Obj.PartyName);
                                if (FilterPartyBasedonCompany)
                                {
                                    cmd.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                                }




                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    List<GNM_Party> partyList = new List<GNM_Party>();
                                    while (reader.Read())
                                    {
                                        GNM_Party party = new GNM_Party
                                        {
                                            Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),

                                            Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? null : reader.GetString(reader.GetOrdinal("Party_Name")),
                                            Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? null : reader.GetString(reader.GetOrdinal("Party_Location"))

                                        };
                                        partyList.Add(party);
                                    }
                                    Party = partyList.AsEnumerable();
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                    var list = from PartyDetails in Party
                               select new
                               {
                                   Party_ID = PartyDetails.Party_ID,
                                   Select = "<label  key='" + PartyDetails.Party_ID + "' class='PartySelect' style='color:blue;text-decoration:underline'>Select</label>",
                                   Party_Name = PartyDetails.Party_Name,
                                   Party_Location = PartyDetails.Party_Location
                               };

                    total = Convert.ToInt32(Math.Ceiling(Convert.ToDouble(list.ToList().Count)) / rows);
                    List<dynamic> arr = new List<dynamic>();
                    for (int i = 0; i < list.ToList().Count; i++)
                    {
                        if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                        {
                            arr.Add(list.ToList()[i]);
                        }
                    }
                    jsonobj = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = arr.Count(),
                        rows = arr.ToArray()
                    };
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(constring))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            string queryParty = @"SELECT *
                                   FROM GNM_Party
                                   WHERE [Company_ID] = @CompanyID AND [Party_IsActive] = 1;";
                            using (cmd = new SqlCommand(queryParty, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@CompanyID", Obj.Company_ID);




                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    List<GNM_Party> partyList = new List<GNM_Party>();
                                    while (reader.Read())
                                    {
                                        GNM_Party party = new GNM_Party
                                        {

                                            Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? null : reader.GetString(reader.GetOrdinal("Party_Location"))

                                        };
                                        partyList.Add(party);
                                    }
                                    Party = partyList.AsEnumerable();
                                }

                            }
                            string queryPartyLocale = @"SELECT *
                                        FROM GNM_PartyLocale
                                        WHERE [Party_Name] LIKE '%' + @PartyName + '%' AND [Language_ID] = @LanguageID;";
                            using (SqlCommand commandPartyLocale = new SqlCommand(queryPartyLocale, conn))
                            {
                                commandPartyLocale.Parameters.AddWithValue("@PartyName", Obj.PartyName);
                                commandPartyLocale.Parameters.AddWithValue("@LanguageID", Obj.Language_ID);
                                using (SqlDataReader reader = commandPartyLocale.ExecuteReader())
                                {
                                    List<GNM_PartyLocale> partyLocaleList = new List<GNM_PartyLocale>();

                                    while (reader.Read())
                                    {
                                        GNM_PartyLocale gNM_PartsLocale = new GNM_PartyLocale();

                                        gNM_PartsLocale.Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"));
                                        gNM_PartsLocale.Party_Name = reader.GetString(reader.GetOrdinal("Party_Name"));

                                        partyLocaleList.Add(gNM_PartsLocale);
                                    }
                                    PartyLocale = partyLocaleList.AsEnumerable();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                    var list = from PartyLocDetails in PartyLocale
                               join PartyDetail in Party on PartyLocDetails.Party_ID equals PartyDetail.Party_ID
                               select new
                               {
                                   Party_ID = PartyLocDetails.Party_ID,
                                   Select = "<label  key='" + PartyLocDetails.Party_ID + "' class='PartySelect' style='color:blue;text-decoration:underline'>Select</label>",
                                   Party_Name = PartyLocDetails.Party_Name,
                                   Party_Location = PartyDetail.Party_Location
                               };

                    total = Convert.ToInt32(Math.Ceiling(Convert.ToDouble(list.ToList().Count)) / rows);
                    List<dynamic> arr = new List<dynamic>();
                    for (int i = 0; i < list.ToList().Count; i++)
                    {
                        if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                        {
                            arr.Add(list.ToList()[i]);
                        }
                    }
                    jsonobj = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = arr.Count(),
                        rows = arr.ToArray()
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion

        #region ::: Get Product Unique Number on change of serial number  vinay n 15/11/24:::
        /// <summary>
        /// To get product details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult getProductUniqueNumber(getProductUniqueNumberList Obj, string constring, int LogException)
        {
            string ProdUnique = string.Empty;
            GNM_Product Prod = null;
            try
            {

                int CompanyID = Obj.Company_ID;
                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                string Query = ";WITH ParentComapany([Company_ID],[Company_Name],[Company_Parent_ID]) as (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM GNM_Company WHERE [Company_ID] = " + CompanyID + " UNION ALL SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] FROM GNM_Company AS child JOIN ParentComapany ON child.[Company_ID] = ParentComapany.[Company_Parent_ID])SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM ParentComapany;";
                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(Query, conn))
                        {
                            cmd.CommandType = CommandType.Text;





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ParentCompanyObject parentCompanyObject = new ParentCompanyObject
                                    {
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Company_Name = reader.GetString(reader.GetOrdinal("Company_Name")),
                                        Company_Parent_ID = reader.GetInt32(reader.GetOrdinal("Company_Parent_ID"))
                                    };
                                    ParentCompanyDetails.Add(parentCompanyObject);
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                string ParentCompanyIDs = "";
                for (int i = 0; i < ParentCompanyDetails.Count(); i++)
                {
                    ParentCompanyIDs += ParentCompanyDetails.ElementAt(i).Company_ID.ToString() + ",";
                }
                ParentCompanyIDs = ParentCompanyIDs.TrimEnd(new char[] { ',' });
                string QueryPUI = "SELECT * FROM  GNM_PRODUCT WHERE Model_ID=" + Obj.ModelID + " and Product_SerialNumber='" + Obj.SerialNumber + "' and IsActive=1 and Company_ID in (" + ParentCompanyIDs + ")";
                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(QueryPUI, conn))
                        {
                            cmd.CommandType = CommandType.Text;





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    Prod = new GNM_Product
                                    {
                                        Product_UniqueNo = reader.GetOrdinal("Product_UniqueNo").ToString(),

                                    };

                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                //Prod = ProductClient.GNM_Product.Where(pd => pd.Company_ID == CompanyID && pd.Model_ID == ModelID && pd.Product_SerialNumber == SerialNumber).FirstOrDefault();
                if (Prod != null)
                {
                    if (Prod.Product_UniqueNo != null && Prod.Product_UniqueNo != "")
                        ProdUnique = Prod.Product_UniqueNo.ToString();
                    else
                        ProdUnique = "0";
                }
                else
                    ProdUnique = "0";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(ProdUnique);
        }
        #endregion

        #region ::: Get SerialNumber fao selected Party and Model vinay n 15/11/24:::
        /// <summary>
        /// To  Get  SerialNumber fao selected Party and Model
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult getSerialNumberForModel(getSerialNumberForModelList Obj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            IEnumerable<GNM_ProductCustomer> PCustomer = null;
            GNM_Product Prod = null;
            List<GNM_Product> ProdList = new List<GNM_Product>();
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;
                    string query = "SELECT Product_ID FROM GNM_ProductCustomer WHERE Party_ID = @PartyID AND ProductCustomer_ToDate IS NULL";
                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.Add(new SqlParameter("@PartyID", SqlDbType.Int) { Value = Obj.PartyID });





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                List<GNM_ProductCustomer> pCustList = new List<GNM_ProductCustomer>();
                                while (reader.Read())
                                {
                                    GNM_ProductCustomer pCust = new GNM_ProductCustomer
                                    {
                                        Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID"))

                                    };
                                    pCustList.Add(pCust);
                                }
                                PCustomer = pCustList.AsEnumerable();
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                // Added new code by Ravi on 09-Jul-2015
                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                string Query = ";WITH ParentComapany([Company_ID],[Company_Name],[Company_Parent_ID]) as (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM GNM_Company WHERE [Company_ID] = " + Company_ID + " UNION ALL SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] FROM GNM_Company AS child JOIN ParentComapany ON child.[Company_ID] = ParentComapany.[Company_Parent_ID])SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM ParentComapany;";
                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(Query, conn))
                        {
                            cmd.CommandType = CommandType.Text;





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ParentCompanyObject parentCompanyObject = new ParentCompanyObject
                                    {
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Company_Name = reader.GetString(reader.GetOrdinal("Company_Name")),
                                        Company_Parent_ID = reader.GetInt32(reader.GetOrdinal("Company_Parent_ID"))
                                    };
                                    ParentCompanyDetails.Add(parentCompanyObject);
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                string ParentCompanyIDs = "";
                for (int i = 0; i < ParentCompanyDetails.Count(); i++)
                {
                    ParentCompanyIDs += ParentCompanyDetails.ElementAt(i).Company_ID.ToString() + ",";
                }
                ParentCompanyIDs = ParentCompanyIDs.TrimEnd(new char[] { ',' });
                //---End
                if (PCustomer != null || PCustomer.Count() > 0)
                {
                    foreach (var cust in PCustomer)
                    {
                        //Prod = ProductClient.GNM_Product.Where(pd => pd.Product_ID == cust.Product_ID && pd.Model_ID == ModelID && pd.Company_ID == Company_ID && pd.IsActive == true).FirstOrDefault();
                        //if (Prod != null)
                        //{
                        //    ProdList.Add(Prod);
                        //}
                        List<GNM_Product> PList = new List<GNM_Product>();
                        string ProductQuery = "SELECT * FROM GNM_PRODUCT P WHERE P.PRODUCT_ID=" + cust.Product_ID + " AND P.MODEL_ID=" + Obj.ModelID + " AND P.ISACTIVE=1 AND P.COMPANY_ID IN (" + ParentCompanyIDs + ")";
                        using (SqlConnection conn = new SqlConnection(constring))
                        {


                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(ProductQuery, conn))
                                {
                                    cmd.CommandType = CommandType.Text;





                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_Product prodObject = new GNM_Product
                                            {
                                                Product_SerialNumber = reader.GetOrdinal("Company_ID").ToString()

                                            };
                                            PList.Add(prodObject);
                                        }
                                    }

                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                cmd.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }

                        if (PList.Count > 0) ProdList.Add(PList[0]);
                    }
                    if (ProdList.Count() > 0)
                    {
                        jsonResult = new
                        {
                            Result = "1",// Exists  
                            SerialNumbers = (from Ser in ProdList
                                             orderby Ser.Product_SerialNumber
                                             select new
                                             {
                                                 Ser.Product_SerialNumber
                                             }).Distinct().ToArray()
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "0"//Not Exists                 
                        };
                    }
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "0"//Not Exists                 
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: Get Product Details vinay n 15/11/24:::
        /// <summary>
        /// To get product details
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetProductDetails(GetProductDetailsList Obj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            IEnumerable<GNM_ProductCustomer> PCustomer = null;
            int ProdID = 0;
            GNM_Product Prod = null;
            GNM_RefMasterDetail Brand = null;
            GNM_RefMasterDetailLocale BrandLocale = null;
            GNM_ProductType ProdType = null;
            GNM_ProductTypeLocale ProdTypeLocale = null;
            CoreModelMasterServices.GNM_Model Model = null;
            GNM_ModelLocale ModelLocale = null;
            List<CoreModelMasterServices.GNM_Model> ModelList = new List<CoreModelMasterServices.GNM_Model>();
            List<GNM_ModelLocale> ModelLocaleList = new List<GNM_ModelLocale>();
            List<GNM_Product> ProdList = new List<GNM_Product>();
            bool FilterPartyBasedonCompany = false;

            try
            {

                int LangID = Obj.Language_ID;
                int CompanyID = Obj.Company_ID;
                string GenLangCode = Obj.GeneralLanguageCode.ToString();
                string UserLangCode = Obj.UserLanguageCode.ToString();
                string filterPartyBasedOnCompanyQuery = @"
                    SELECT Param_value
                    FROM GNM_CompParam
                    WHERE Company_ID = @CompanyID
                    AND UPPER(Param_Name) = 'FILTERPARTYBASEDONCOMPANY'";

                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(filterPartyBasedOnCompanyQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.Add(new SqlParameter("@CompanyID", SqlDbType.Int) { Value = Obj.Company_ID });





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            var result = cmd.ExecuteScalar();
                            if (result != null && result.ToString().ToUpper() == "TRUE")
                            {
                                FilterPartyBasedonCompany = true;
                            }
                            else
                            {
                                FilterPartyBasedonCompany = false;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                using (SqlConnection conn = new SqlConnection(constring))
                {


                    SqlCommand cmd = null;
                    string query = "SELECT Product_ID FROM GNM_ProductCustomer WHERE Party_ID = @PartyID AND ProductCustomer_ToDate IS NULL";
                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.Add(new SqlParameter("@PartyID", SqlDbType.Int) { Value = Obj.PartyID });





                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                List<GNM_ProductCustomer> pCustList = new List<GNM_ProductCustomer>();
                                while (reader.Read())
                                {
                                    GNM_ProductCustomer pCust = new GNM_ProductCustomer
                                    {
                                        Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID"))

                                    };
                                    pCustList.Add(pCust);
                                }
                                PCustomer = pCustList.AsEnumerable();
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                if (PCustomer != null)
                {
                    if (PCustomer.Count() == 1)
                    {
                        ProdID = Convert.ToInt32(PCustomer.ElementAt(0).Product_ID);
                        string prodQuery = FilterPartyBasedonCompany
                   ? "SELECT TOP 1 * FROM GNM_Product WHERE Product_ID = @ProdID AND IsActive = 1 AND Company_ID = @CompanyID"
                   : "SELECT TOP 1 * FROM GNM_Product WHERE Product_ID = @ProdID AND IsActive = 1";
                        var parameters = new List<SqlParameter>
                        {
                            new SqlParameter("@ProdID", ProdID)
                        };
                        if (FilterPartyBasedonCompany)
                        {
                            parameters.Add(new SqlParameter("@CompanyID", CompanyID));
                        }
                        Prod = GetValueFromDB<GNM_Product>(prodQuery, parameters, constring);


                        if (Prod != null)
                        {
                            using (SqlConnection conn = new SqlConnection(constring))
                            {
                                string brandQuery = "SELECT TOP 1 * FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @BrandID AND RefMasterDetail_IsActive = 1";


                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(brandQuery, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@BrandID", Prod.Brand_ID);





                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                Brand = new GNM_RefMasterDetail();
                                                Brand.RefMasterDetail_Name = reader.GetOrdinal("RefMasterDetail_Name").ToString();
                                            }

                                        }

                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }
                            using (SqlConnection conn = new SqlConnection(constring))
                            {
                                string prodTypeQuery = "SELECT TOP 1 * FROM GNM_ProductType WHERE ProductType_ID = @ProductTypeID AND ProductType_IsActive = 1";


                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(prodTypeQuery, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@ProductTypeID", Prod.ProductType_ID);





                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                ProdType = new GNM_ProductType();
                                                ProdType.ProductType_Name = reader.GetOrdinal("ProductType_Name").ToString();
                                            }

                                        }

                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }
                            using (SqlConnection conn = new SqlConnection(constring))
                            {
                                string modelQuery = "SELECT TOP 1 * FROM GNM_Model WHERE Model_ID = @ModelID AND Model_IsActive = 1";


                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(modelQuery, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@ModelID", Prod.Model_ID);





                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                Model = new CoreModelMasterServices.GNM_Model();
                                                Model.Model_Name = reader.GetOrdinal("Model_Name").ToString();
                                            }

                                        }

                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }




                            if (GenLangCode != UserLangCode)
                            {
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string brandLocaleQuery = "SELECT TOP 1 * FROM GNM_RefMasterDetailLocale loc JOIN GNM_RefMasterDetail det on loc.RefMasterDetail_ID=DET.RefMasterDetail_ID  WHERE loc.RefMasterDetail_ID = @BrandID AND Language_ID = @LangID AND det.RefMasterDetail_IsActive=1";


                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(brandLocaleQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@BrandID", Prod.Brand_ID);
                                            cmd.Parameters.AddWithValue("@LangID", LangID);





                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                if (reader.Read())
                                                {
                                                    BrandLocale = new GNM_RefMasterDetailLocale();
                                                    BrandLocale.RefMasterDetail_Name = reader.GetOrdinal("RefMasterDetail_Name").ToString();
                                                }

                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string prodTypeLocaleQuery = "SELECT TOP 1 * FROM GNM_ProductTypeLocale loc join GNM_ProductType prod on loc.ProductType_ID=prod.ProductType_ID WHERE loc.ProductType_ID = @ProductTypeID AND Language_ID = @LangID AND ProductType_IsActive=1";


                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(prodTypeLocaleQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@ProductTypeID", Prod.ProductType_ID);
                                            cmd.Parameters.AddWithValue("@LangID", LangID);





                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                if (reader.Read())
                                                {
                                                    ProdTypeLocale = new GNM_ProductTypeLocale();
                                                    ProdTypeLocale.ProductType_Name = reader.GetOrdinal("ProductType_Name").ToString();
                                                }

                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string modelLocaleQuery = "SELECT TOP 1 * FROM GNM_ModelLocale loc join GNM_Model mod on loc.Model_ID=mod.Model_ID WHERE loc.Model_ID = @ModelID AND Language_ID = @LangID and Model_IsActive=1";


                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(modelLocaleQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@ModelID", Prod.Model_ID);
                                            cmd.Parameters.AddWithValue("@LangID", LangID);





                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                if (reader.Read())
                                                {
                                                    ModelLocale = new GNM_ModelLocale();
                                                    ModelLocale.Model_Name = reader.GetOrdinal("Model_Name").ToString();
                                                }

                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }



                            }

                            jsonResult = new
                            {
                                Result = "1",//Exists
                                BrandName = (GenLangCode == UserLangCode) ? Brand.RefMasterDetail_Name : (BrandLocale == null) ? "" : BrandLocale.RefMasterDetail_Name,
                                BrandID = Prod.Brand_ID,
                                ProductTypeName = (GenLangCode == UserLangCode) ? ProdType.ProductType_Name : (ProdTypeLocale == null) ? "" : ProdTypeLocale.ProductType_Name,
                                ProductTypeID = Prod.ProductType_ID,
                                ModelName = (GenLangCode == UserLangCode) ? Model.Model_Name : (ModelLocale == null) ? "" : ModelLocale.Model_Name,
                                ModelTypeID = Prod.Model_ID,
                                SerialNumber = Prod.Product_SerialNumber,
                                Unique_Number = Prod.Product_UniqueNo,
                                Product_ID = Prod.Product_ID
                            };
                        }
                        else
                        {
                            jsonResult = new
                            {
                                Result = "0"//Not Exists                 
                            };
                        }
                    }
                    else if (PCustomer.Count() == 0)
                    {
                        jsonResult = new
                        {
                            Result = "0"//Not Exists                 
                        };
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            foreach (var cust in PCustomer)
                            {
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string prodQuery = FilterPartyBasedonCompany
                              ? "SELECT TOP 1 * FROM GNM_Product WHERE Product_ID = @ProdID AND IsActive = 1 AND Company_ID = @CompanyID"
                              : "SELECT TOP 1 * FROM GNM_Product WHERE Product_ID = @ProdID AND IsActive = 1";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(prodQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@ProdID", ProdID);
                                            if (FilterPartyBasedonCompany)
                                                cmd.Parameters.AddWithValue("@CompanyID", CompanyID);





                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                Prod = new GNM_Product
                                                {
                                                    Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                                    Product_UniqueNo = reader.GetString(reader.GetOrdinal("Product_UniqueNo")),
                                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                    Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                                    Product_SerialNumber = reader.GetString(reader.GetOrdinal("Product_SerialNumber")),
                                                    PrimarySegment_ID = reader.IsDBNull(reader.GetOrdinal("PrimarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PrimarySegment_ID")),
                                                    SecondarySegment_ID = reader.IsDBNull(reader.GetOrdinal("SecondarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SecondarySegment_ID")),
                                                    ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                                    ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                                    IsComponent = reader.IsDBNull(reader.GetOrdinal("IsComponent")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsComponent")),
                                                    Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                    NextServiceType_ID = reader.IsDBNull(reader.GetOrdinal("NextServiceType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("NextServiceType_ID")),
                                                    NextServiceDate = reader.IsDBNull(reader.GetOrdinal("NextServiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("NextServiceDate")),
                                                    MachineStatus_ID = reader.IsDBNull(reader.GetOrdinal("MachineStatus_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("MachineStatus_ID")),
                                                    AverageReadingPerDay = reader.IsDBNull(reader.GetOrdinal("AverageReadingPerDay")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("AverageReadingPerDay")),
                                                    CommissioningDate = reader.IsDBNull(reader.GetOrdinal("CommissioningDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CommissioningDate")),
                                                    IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                                    Warehouse_ID = reader.IsDBNull(reader.GetOrdinal("Warehouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Warehouse_ID")),
                                                    Parts_ID = reader.IsDBNull(reader.GetOrdinal("Parts_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                                    SerialStatus = reader.IsDBNull(reader.GetOrdinal("SerialStatus")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SerialStatus")),
                                                    DateOfManufacture = reader.IsDBNull(reader.GetOrdinal("DateOfManufacture")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfManufacture")),
                                                    Product_EngineSerialNumber = reader.IsDBNull(reader.GetOrdinal("Product_EngineSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("Product_EngineSerialNumber")),
                                                    ServiceCompany = reader.IsDBNull(reader.GetOrdinal("ServiceCompany")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceCompany")),
                                                    InvoiceDate = reader.IsDBNull(reader.GetOrdinal("InvoiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("InvoiceDate")),
                                                    LandingCost = reader.IsDBNull(reader.GetOrdinal("LandingCost")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("LandingCost")),
                                                    WarrantyEndDate = reader.IsDBNull(reader.GetOrdinal("WarrantyEndDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("WarrantyEndDate")),
                                                    DateOfSale = reader.IsDBNull(reader.GetOrdinal("DateOfSale")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfSale")),
                                                    AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                                    WholeSaleDealerid = reader.IsDBNull(reader.GetOrdinal("WholeSaleDealerid")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WholeSaleDealerid")),
                                                    IsWholeSaleUser = reader.IsDBNull(reader.GetOrdinal("IsWholeSaleUser")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsWholeSaleUser")),
                                                    ServiceEngineer_ID = reader.IsDBNull(reader.GetOrdinal("ServiceEngineer_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceEngineer_ID")),
                                                    Reading_Unit = reader.IsDBNull(reader.GetOrdinal("Reading_Unit")) ? null : reader.GetString(reader.GetOrdinal("Reading_Unit")),
                                                    LastServiceBranch = reader.IsDBNull(reader.GetOrdinal("LastServiceBranch")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("LastServiceBranch")),
                                                    IsPCPApplicable = reader.IsDBNull(reader.GetOrdinal("IsPCPApplicable")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsPCPApplicable")),
                                                    PCPFrequency = reader.IsDBNull(reader.GetOrdinal("PCPFrequency")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPFrequency")),
                                                    PCPUsedCount = reader.IsDBNull(reader.GetOrdinal("PCPUsedCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPUsedCount")),
                                                    IsClaasMachine = reader.IsDBNull(reader.GetOrdinal("IsClaasMachine")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsClaasMachine")),
                                                    Series = reader.IsDBNull(reader.GetOrdinal("Series")) ? null : reader.GetString(reader.GetOrdinal("Series")),
                                                    isSeriesAttachmentAdded = reader.IsDBNull(reader.GetOrdinal("isSeriesAttachmentAdded")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("isSeriesAttachmentAdded"))
                                                };

                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }

                                if (Prod != null)
                                {
                                    using (SqlConnection conn = new SqlConnection(constring))
                                    {
                                        string modelQuery = "SELECT TOP 1 * FROM GNM_Model WHERE Model_ID = @ModelID AND Model_IsActive = 1";


                                        SqlCommand cmd = null;

                                        try
                                        {
                                            using (cmd = new SqlCommand(modelQuery, conn))
                                            {
                                                cmd.CommandType = CommandType.Text;
                                                cmd.Parameters.AddWithValue("@ModelID", Prod.Model_ID);





                                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                                {
                                                    conn.Open();
                                                }
                                                using (SqlDataReader reader = cmd.ExecuteReader())
                                                {
                                                    if (reader.Read())
                                                    {
                                                        Model = new CoreModelMasterServices.GNM_Model();
                                                        Model.Model_Name = reader.GetOrdinal("Model_Name").ToString();
                                                        Model.Model_ID = Convert.ToInt32(reader.GetOrdinal("Model_ID").ToString());
                                                        Model.Brand_ID = Convert.ToInt32(reader.GetOrdinal("Brand_ID").ToString());
                                                    }

                                                }

                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            if (LogException == 1)
                                            {
                                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                            }

                                        }
                                        finally
                                        {
                                            cmd.Dispose();
                                            conn.Close();
                                            conn.Dispose();
                                            SqlConnection.ClearAllPools();
                                        }
                                    }

                                    if (Model != null)
                                    {
                                        ModelList.Add(Model);
                                        ProdList.Add(Prod);
                                    }
                                }
                            }
                            List<dynamic> VariableList = new List<dynamic>();
                            using (SqlConnection conn = new SqlConnection(constring))
                            {
                                string modelQuery = "SELECT  * GNM_CompanyBrands";


                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(modelQuery, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;






                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                dynamic Variable = null;
                                                Variable.Model_Name = reader.GetOrdinal("Model_Name").ToString();
                                                Variable.Model_ID = Convert.ToInt32(reader.GetOrdinal("Model_ID").ToString());
                                                Variable.Brand_ID = Convert.ToInt32(reader.GetOrdinal("Brand_ID").ToString());
                                                VariableList.Add(Variable);
                                            }

                                        }

                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }
                            jsonResult = new
                            {
                                Result = "2",//Multiple Exists  
                                ModelList = ((
                                from Models in ModelList
                                join cb in VariableList on Models.Brand_ID equals cb.Brand_ID
                                where cb.Company_ID == Obj.Company_ID
                                orderby Models.Model_Name//added by kavitha -sorting
                                select new
                                {
                                    Models.Model_ID,
                                    Models.Model_Name
                                }
                                ).Distinct()).ToArray(),

                            };
                        }
                        else
                        {
                            foreach (var cust in PCustomer)
                            {
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string prodQuery = FilterPartyBasedonCompany
                              ? "SELECT TOP 1 * FROM GNM_Product WHERE Product_ID = @ProdID AND IsActive = 1 AND Company_ID = @CompanyID"
                              : "SELECT TOP 1 * FROM GNM_Product WHERE Product_ID = @ProdID AND IsActive = 1";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(prodQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@ProdID", cust.Product_ID);
                                            if (FilterPartyBasedonCompany)
                                                cmd.Parameters.AddWithValue("@CompanyID", CompanyID);





                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                Prod = new GNM_Product
                                                {
                                                    Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                                    Product_UniqueNo = reader.GetString(reader.GetOrdinal("Product_UniqueNo")),
                                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                    Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                                    Product_SerialNumber = reader.GetString(reader.GetOrdinal("Product_SerialNumber")),
                                                    PrimarySegment_ID = reader.IsDBNull(reader.GetOrdinal("PrimarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PrimarySegment_ID")),
                                                    SecondarySegment_ID = reader.IsDBNull(reader.GetOrdinal("SecondarySegment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SecondarySegment_ID")),
                                                    ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                                    ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                                    IsComponent = reader.IsDBNull(reader.GetOrdinal("IsComponent")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsComponent")),
                                                    Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                                    NextServiceType_ID = reader.IsDBNull(reader.GetOrdinal("NextServiceType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("NextServiceType_ID")),
                                                    NextServiceDate = reader.IsDBNull(reader.GetOrdinal("NextServiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("NextServiceDate")),
                                                    MachineStatus_ID = reader.IsDBNull(reader.GetOrdinal("MachineStatus_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("MachineStatus_ID")),
                                                    AverageReadingPerDay = reader.IsDBNull(reader.GetOrdinal("AverageReadingPerDay")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("AverageReadingPerDay")),
                                                    CommissioningDate = reader.IsDBNull(reader.GetOrdinal("CommissioningDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("CommissioningDate")),
                                                    IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                                    Warehouse_ID = reader.IsDBNull(reader.GetOrdinal("Warehouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Warehouse_ID")),
                                                    Parts_ID = reader.IsDBNull(reader.GetOrdinal("Parts_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                                    SerialStatus = reader.IsDBNull(reader.GetOrdinal("SerialStatus")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SerialStatus")),
                                                    DateOfManufacture = reader.IsDBNull(reader.GetOrdinal("DateOfManufacture")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfManufacture")),
                                                    Product_EngineSerialNumber = reader.IsDBNull(reader.GetOrdinal("Product_EngineSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("Product_EngineSerialNumber")),
                                                    ServiceCompany = reader.IsDBNull(reader.GetOrdinal("ServiceCompany")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceCompany")),
                                                    InvoiceDate = reader.IsDBNull(reader.GetOrdinal("InvoiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("InvoiceDate")),
                                                    LandingCost = reader.IsDBNull(reader.GetOrdinal("LandingCost")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("LandingCost")),
                                                    WarrantyEndDate = reader.IsDBNull(reader.GetOrdinal("WarrantyEndDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("WarrantyEndDate")),
                                                    DateOfSale = reader.IsDBNull(reader.GetOrdinal("DateOfSale")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfSale")),
                                                    AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                                    WholeSaleDealerid = reader.IsDBNull(reader.GetOrdinal("WholeSaleDealerid")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WholeSaleDealerid")),
                                                    IsWholeSaleUser = reader.IsDBNull(reader.GetOrdinal("IsWholeSaleUser")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsWholeSaleUser")),
                                                    ServiceEngineer_ID = reader.IsDBNull(reader.GetOrdinal("ServiceEngineer_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceEngineer_ID")),
                                                    Reading_Unit = reader.IsDBNull(reader.GetOrdinal("Reading_Unit")) ? null : reader.GetString(reader.GetOrdinal("Reading_Unit")),
                                                    LastServiceBranch = reader.IsDBNull(reader.GetOrdinal("LastServiceBranch")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("LastServiceBranch")),
                                                    IsPCPApplicable = reader.IsDBNull(reader.GetOrdinal("IsPCPApplicable")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsPCPApplicable")),
                                                    PCPFrequency = reader.IsDBNull(reader.GetOrdinal("PCPFrequency")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPFrequency")),
                                                    PCPUsedCount = reader.IsDBNull(reader.GetOrdinal("PCPUsedCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("PCPUsedCount")),
                                                    IsClaasMachine = reader.IsDBNull(reader.GetOrdinal("IsClaasMachine")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsClaasMachine")),
                                                    Series = reader.IsDBNull(reader.GetOrdinal("Series")) ? null : reader.GetString(reader.GetOrdinal("Series")),
                                                    isSeriesAttachmentAdded = reader.IsDBNull(reader.GetOrdinal("isSeriesAttachmentAdded")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("isSeriesAttachmentAdded"))
                                                };

                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }

                                if (Prod != null)
                                {
                                    using (SqlConnection conn = new SqlConnection(constring))
                                    {
                                        string query = @"
                                        SELECT TOP 1 ml.*
                                        FROM GNM_ModelLocale ml
                                        INNER JOIN GNM_Model m ON ml.Model_ID = m.Model_ID
                                        WHERE ml.Model_ID = @ModelID
                                        AND ml.Language_ID = @LangID
                                        AND m.Model_IsActive = 1";

                                        SqlCommand cmd = null;

                                        try
                                        {
                                            using (cmd = new SqlCommand(query, conn))
                                            {
                                                cmd.CommandType = CommandType.Text;
                                                cmd.Parameters.AddWithValue("@ModelID", Prod.Model_ID);
                                                cmd.Parameters.AddWithValue("@LangID", LangID);





                                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                                {
                                                    conn.Open();
                                                }
                                                using (SqlDataReader reader = cmd.ExecuteReader())
                                                {
                                                    ModelLocale = new GNM_ModelLocale
                                                    {
                                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                        Model_Name = reader.GetOrdinal("Model_Name").ToString()

                                                    };

                                                }

                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            if (LogException == 1)
                                            {
                                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                            }

                                        }
                                        finally
                                        {
                                            cmd.Dispose();
                                            conn.Close();
                                            conn.Dispose();
                                            SqlConnection.ClearAllPools();
                                        }
                                    }

                                    if (ModelLocale != null)
                                    {
                                        ModelLocaleList.Add(ModelLocale);
                                    }
                                    ProdList.Add(Prod);
                                }
                            }

                            if (FilterPartyBasedonCompany)
                            {
                                List<CoreModelMasterServices.GNM_Model> modList = new List<CoreModelMasterServices.GNM_Model>();
                                List<GNM_CompanyBrands> compBrList = new List<GNM_CompanyBrands>();
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string query = @"
                                        SELECT *
                                        FROM GNM_Model ";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(query, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;






                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    CoreModelMasterServices.GNM_Model Mod = new CoreModelMasterServices.GNM_Model
                                                    {
                                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                        Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                                        Model_Name = reader.GetOrdinal("Model_Name").ToString()

                                                    };
                                                    modList.Add(Mod);
                                                }


                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string query = @"
                                        SELECT *
                                        FROM GNM_CompanyBrands ";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(query, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;






                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    GNM_CompanyBrands compBr = new GNM_CompanyBrands
                                                    {
                                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                                        Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID"))

                                                    };
                                                    compBrList.Add(compBr);
                                                }


                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }
                                jsonResult = new
                                {
                                    Result = "2",//Multiple Exists  

                                    ModelList = ((
                                    from Models in ModelLocaleList
                                    join model in modList on Models.Model_ID equals model.Model_ID
                                    join cb in compBrList on model.Brand_ID equals cb.Brand_ID
                                    where cb.Company_ID == Obj.Company_ID
                                    select new
                                    {
                                        Models.Model_ID,
                                        Models.Model_Name
                                    }
                                    ).Distinct()).ToArray(),

                                };
                            }
                            else
                            {
                                List<CoreModelMasterServices.GNM_Model> modList = new List<CoreModelMasterServices.GNM_Model>();
                                List<GNM_CompanyBrands> compBrList = new List<GNM_CompanyBrands>();
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string query = @"
                                        SELECT *
                                        FROM GNM_Model ";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(query, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;






                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    CoreModelMasterServices.GNM_Model Mod = new CoreModelMasterServices.GNM_Model
                                                    {
                                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                                        Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                                        Model_Name = reader.GetOrdinal("Model_Name").ToString()

                                                    };
                                                    modList.Add(Mod);
                                                }


                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }
                                using (SqlConnection conn = new SqlConnection(constring))
                                {
                                    string query = @"
                                        SELECT *
                                        FROM GNM_CompanyBrands ";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(query, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;






                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    GNM_CompanyBrands compBr = new GNM_CompanyBrands
                                                    {
                                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                                        Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID"))

                                                    };
                                                    compBrList.Add(compBr);
                                                }


                                            }

                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }
                                }
                                jsonResult = new
                                {
                                    Result = "2",//Multiple Exists  

                                    ModelList = ((
                                    from Models in ModelLocaleList
                                    join model in modList on Models.Model_ID equals model.Model_ID
                                    join cb in compBrList on model.Brand_ID equals cb.Brand_ID
                                    select new
                                    {
                                        Models.Model_ID,
                                        Models.Model_Name
                                    }
                                    ).Distinct()).ToArray(),

                                };
                            }
                        }
                    }
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "0"//Not Exists                 
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0"//Not Exists 
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion


    }
    #region elpDeskUnregisteredServiceRequestListsAndObjs Vinay n 11/11/24
    public class SelectUnRegisteredSeviceRequestList
    {
        public string FromDate { get; set; }
        public string UserCulture { get; set; }
        public string ToDate { get; set; }
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int LanguageID { get; set; }

        //UnRegisteredServiceExport
        public int Branch { get; set; }
        public int exprtType { get; set; }
        public string filter { get; set; }
        public string advanceFilter { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public string extraParam1 { get; set; }
        public string extraParam2 { get; set; }


    }
    public class GetProductDetailsList
    {
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public int PartyID { get; set; }
    }
    public class getSerialNumberForModelList
    {
        public int Company_ID { get; set; }
        public int PartyID { get; set; }
        public int ModelID { get; set; }
    }
    public class getProductUniqueNumberList
    {
        public int Company_ID { get; set; }
        public int ModelID { get; set; }
        public string SerialNumber { get; set; }
    }
    public class SelectPartyDetailGridList
    {
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string PartyName { get; set; }
        public string Company_ID { get; set; }
        public string Language_ID { get; set; }
    }
    public class ProductMasterSaveConfirmList
    {
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public string Data { get; set; }
    }
    public class ProductMasterSaveObj
    {
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string Data { get; set; }
        public int User_ID { get; set; }
    }
    public class GetPartyDetailsbyIDList
    {
        public int Language_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int PartyID { get; set; }
    }
    public class UpdateUnregisteredServiceRequestList
    {
        public int Unred_ID { get; set; }
        public byte status { get; set; }
    }


    public class getBrandProductTypeList
    {
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string ModelID { get; set; }
        public string Language_ID { get; set; }
    }


    public class SelectSingleUnRegisteredSeviceRequestList
    {
        public string UserCulture { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int id { get; set; }
    }
    public class InsertUnregisteredServiceRequestList
    {
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string data { get; set; }
    }
    public class getCustomerDetailsByNameList
    {
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string Name { get; set; }
    }
    public class getCustomerDetailsByEmailList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public string EMail { get; set; }
        public int Language_ID { get; set; }
    }


    public class getCustomerDetailsByPhoneList
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Language_ID { get; set; }
        public string phoneNo { get; set; }
    }
    public class GetPartyDetailsByProductUniqueNoList
    {
        public string UniqueNo { get; set; }
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }
    public class GetUnRegisteredSeviceRequest2List
    {
        public string UserCulture { get; set; }
        public string frmdate { get; set; }
        public int? Company_ID { get; set; }
        public int? UserLanguageID { get; set; }
        public int? GeneralLanguageID { get; set; }
        public int? Language_ID { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public int? exprtType { get; set; }
        public int? Branch { get; set; }
        public string todate { get; set; }
        public string filter { get; set; }
        public string advanceFilter { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public string extraParam1 { get; set; }
        public string extraParam2 { get; set; }




    }

    public class GetPermissionsList
    {
        public int ObjectID { get; set; }
        public int User_ID { get; set; }
        public List<ACLProperties> ACL { get; set; }
    }
    #endregion
    #region HelpDeskUnregisteredServiceRequestMasterClasses vinay n 11/11/24
    public class UnRegisteredServiceObject
    {
        public int ServiceRequest_id
        {
            get;
            set;
        }

        public string PartyName
        {
            get;
            set;
        }

        public string ReqDate
        {
            get;
            set;
        }

        public string BrandName
        {
            get;
            set;
        }
        public string ModelName
        {
            get;
            set;
        }
        public string SerialNumber
        {
            get;
            set;
        }
        public string Req_Mobile
        {
            get;
            set;
        }
        public string Req_Status
        {
            get;
            set;
        }
        public DateTime RequestDate
        {
            get;
            set;
        }
    }
    public partial class HD_UnregisteredServiceRequest
    {
        public int UnregisteredServiceRequest_ID { get; set; }
        public int Company_ID { get; set; }
        public string Product_Unique_Number { get; set; }
        public int Party_ID { get; set; }
        public string RequestDescription { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public string SerialNumber { get; set; }
        public string Email_ID { get; set; }
        public string Mobile { get; set; }
        public string Phone { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<int> ServiceRequest_ID { get; set; }
        public string Remarks { get; set; }
        public byte Status { get; set; }
        public Nullable<int> Locked_by_User_ID { get; set; }
        public Nullable<decimal> Coordinate_Latitude { get; set; }
        public Nullable<decimal> Coordinate_Longitude { get; set; }
        public string LatLongAddress { get; set; }
    }
    public class UnRegisteredServiceRequest
    {
        public int? ServiceRequest_id { get; set; }
        public string Product_Unique_Number { get; set; }
        public int? Party_ID { get; set; }
        public string Party_Name { get; set; }
        public string RequestDescription { get; set; }
        public int? Brand_ID { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public int? ProductType_ID { get; set; }
        public string ProductType_Name { get; set; }
        public int? Model_ID { get; set; }
        public string Model_Name { get; set; }
        public string SerialNumber { get; set; }
        public string Email_ID { get; set; }
        public string Mobile { get; set; }
        public string Date { get; set; }
        public int? ServiceRequest_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
        public string ServiceRequestDate { get; set; }
        public int? StatusID { get; set; }
        public string Remarks { get; set; }
        public string Status { get; set; }
    }

    #endregion

}
