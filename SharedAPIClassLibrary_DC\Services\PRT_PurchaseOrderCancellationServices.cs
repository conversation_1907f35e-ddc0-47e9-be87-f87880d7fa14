﻿using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC;
using SharedAPIClassLibrary_DC.Utilities;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
using System.Text;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Security.AccessControl;
using DocumentFormat.OpenXml.Wordprocessing;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Org.BouncyCastle.Cms;
using System.Security.Cryptography.Pkcs;
using Newtonsoft.Json;
using Amazon.Runtime.Internal.Transform;
using static SharedAPIClassLibrary_AMERP.HelpDeskServiceRequestServices;
using static SharedAPIClassLibrary_AMERP.Services.PRT_PurchaseOrderCancellationServices;
using DocumentFormat.OpenXml.Drawing.Diagrams;

namespace SharedAPIClassLibrary_AMERP.Services
{
    public class PRT_PurchaseOrderCancellationServices
    {

        #region :::Save PO Cancellation :::
        /// <summary>
        /// Method to insert the PO Cancellation
        /// </summary>   
        public static IActionResult Save(POCSaveList Obj, string constring, int LogException)
        {
            JObject jObj = JObject.Parse(Obj.Data);

            int branchId = Obj.Branch;

            jObj["Branch_ID"] = branchId;
            var headerColumns = new Dictionary<string, bool>
            {
                { "Branch_ID", true },
                { "PurchaseOrder_ID", true },
                { "ReasonForCancellation", true },

            };
            List<string> invalidColumns;
            bool isHeaderValid = Common.ValidateAndLog(jObj, headerColumns, out invalidColumns);
            if (!isHeaderValid)
            {

                return new JsonResult(new { Response = "Header Validation Failed", Message = "Header contains invalid or missing fields." });
            }
            JArray details = (JArray)jObj["PRT_OrderCancellationDetails"];
            bool allRowsValid = true;
            foreach (var row in details)
            {
                var detailColumns = new Dictionary<string, bool>
                    {
                { "Parts_ID", true },
                { "Rate", true },
                { "CancelledQuantity", true },
                { "Amount", true }
            };
                List<string> invalidColumns2;
                bool isRowValid = Common.ValidateAndLog(row.ToObject<JObject>(), detailColumns, out invalidColumns2);

                if (!isRowValid)
                {
                    allRowsValid = false;

                }
            }
            if (!allRowsValid)
            {

                return new JsonResult(new { Response = "Detailer Validation Failed", Message = "Detailer contains invalid or missing fields." });
            }

            var jsonData = default(dynamic);
            string poCancellationNumber = null;
            using (System.Transactions.TransactionScope scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Suppress))
            {
                try
                {
                    //var user = (GNM_User)Session["UserDetails"];
                    int companyID = Obj.Company_ID;
                    int userID = Obj.User_ID;
                    int branchID = Convert.ToInt32(Obj.Branch);

                    JObject jobj = JObject.Parse(Obj.Data);

                    // Deserialize object
                    PRT_POCancellationH dummyRow = jobj.ToObject<PRT_POCancellationH>();
                    dummyRow.ReasonForCancellation = Common.DecryptString(dummyRow.ReasonForCancellation);
                    dummyRow.PartsOrderCancellationDate = DateTime.Now;
                    dummyRow.Company_ID = companyID;
                    dummyRow.Branch_ID = branchID;
                    dummyRow.ModifiedBy = userID;
                    dummyRow.ModifiedDate = DateTime.Now;

                    PRT_POCancellation headerRow = new PRT_POCancellation
                    {
                        Branch_ID = dummyRow.Branch_ID,
                        Company_ID = dummyRow.Company_ID,
                        ModifiedBy = dummyRow.ModifiedBy,
                        ModifiedDate = dummyRow.ModifiedDate,
                        OrderCancellation_ID = dummyRow.OrderCancellation_ID,
                        OrderCancellation_Type = dummyRow.OrderCancellation_Type,
                        PartsOrder_ID = dummyRow.PartsOrder_ID,
                        PartsOrderCancellationDate = dummyRow.PartsOrderCancellationDate,
                        PurchaseOrder_ID = dummyRow.PurchaseOrder_ID,
                        ReasonForCancellation = dummyRow.ReasonForCancellation,
                        TotalOrderCancellationAmount = dummyRow.TotalOrderCancellationAmount,
                        POCancellationNumber = ""
                    };

                    foreach (var detail in dummyRow.PRT_OrderCancellationDetails)
                    {
                        headerRow.PRT_OrderCancellationDetails.Add(new PRT_OrderCancellationDetails
                        {
                            OrderCancellation_ID = detail.OrderCancellation_ID,
                            Parts_ID = detail.Parts_ID,
                            Rate = detail.Rate,
                            PickedQuantity = detail.PickedQuantity,
                            OrderedQuantity = detail.OrderedQuantity,
                            InvoicedQuantity = detail.InvoicedQuantity,
                            CancelledQuantity = detail.CancelledQuantity,
                            Amount = detail.Amount,
                            WareHouse_ID = detail.WareHouse_ID,
                            OrderCancellationDetail_ID = detail.OrderCancellationDetail_ID,
                        });
                    }

                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();

                        using (var transaction = connection.BeginTransaction())
                        {
                            try
                            {
                                if (headerRow.OrderCancellation_ID == 0)
                                {

                                    // Check prefix and suffix
                                    if (Common.CheckPreffixSuffix(companyID, branchID, "PRT_PurchaseOrderCancellation", constring, LogException))
                                    {
                                        var headerCommand = new SqlCommand(@"
                                            DECLARE @OrderCancellation_ID INT;
                                            DECLARE @POCancellationNumber NVARCHAR(50);
                                            -- Perform the INSERT to the table
                                            INSERT INTO PRT_POCancellation 
                                            (
                                                PartsOrder_ID, OrderCancellation_Type, PartsOrderCancellationDate, 
                                                ReasonForCancellation, TotalOrderCancellationAmount, Company_ID, 
                                                Branch_ID, ModifiedBy, ModifiedDate, POCancellationNumber, FinancialYear
                                            )
                                            VALUES 
                                            (
                                                @PartsOrder_ID, @OrderCancellation_Type, @PartsOrderCancellationDate, 
                                                @ReasonForCancellation, @TotalOrderCancellationAmount, @Company_ID, 
                                                @Branch_ID, @ModifiedBy, GETDATE(), '', 0
                                            );

                                            -- Retrieve the OrderCancellation_ID of the just inserted record
                                            SELECT @OrderCancellation_ID = SCOPE_IDENTITY(); 

                                            -- Fetch the POCancellationNumber separately using the OrderCancellation_ID
                                            SELECT @POCancellationNumber = POCancellationNumber
                                            FROM PRT_POCancellation
                                            WHERE OrderCancellation_ID = @OrderCancellation_ID;

                                            -- Return both values to the calling application
                                            SELECT @OrderCancellation_ID AS OrderCancellation_ID, @POCancellationNumber AS POCancellationNumber;
                                        ", connection, transaction);

                                        // Add parameters as usual
                                        headerCommand.Parameters.AddWithValue("@PartsOrder_ID", headerRow.PartsOrder_ID.HasValue ? (object)headerRow.PartsOrder_ID.Value : DBNull.Value);
                                        headerCommand.Parameters.AddWithValue("@OrderCancellation_Type", headerRow.OrderCancellation_Type);
                                        headerCommand.Parameters.AddWithValue("@PartsOrderCancellationDate", headerRow.PartsOrderCancellationDate);
                                        headerCommand.Parameters.AddWithValue("@ReasonForCancellation", string.IsNullOrEmpty(headerRow.ReasonForCancellation) ? DBNull.Value : (object)headerRow.ReasonForCancellation);
                                        headerCommand.Parameters.AddWithValue("@TotalOrderCancellationAmount", headerRow.TotalOrderCancellationAmount.HasValue ? (object)headerRow.TotalOrderCancellationAmount.Value : DBNull.Value);
                                        headerCommand.Parameters.AddWithValue("@Company_ID", headerRow.Company_ID);
                                        headerCommand.Parameters.AddWithValue("@Branch_ID", headerRow.Branch_ID);
                                        headerCommand.Parameters.AddWithValue("@ModifiedBy", headerRow.ModifiedBy);

                                        // Execute the command and retrieve both the OrderCancellation_ID and POCancellationNumber
                                        using (var reader = headerCommand.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                // Retrieve and store the values in the headerRow object
                                                headerRow.OrderCancellation_ID = reader.GetInt32(0);
                                                poCancellationNumber = reader.IsDBNull(1) ? null : reader.GetString(1);
                                            }
                                        }





                                        // Insert detail records
                                        foreach (var detail in headerRow.PRT_OrderCancellationDetails)
                                        {
                                            var detailCommand = new SqlCommand(@"
                                            INSERT INTO PRT_OrderCancellationDetails 
                                            (
                                                OrderCancellation_ID, Parts_ID, CancelledQuantity, Rate, 
                                                Amount, WareHouse_ID, OrderedQuantity, InvoicedQuantity, PickedQuantity
                                            )
                                            VALUES 
                                            (
                                                @OrderCancellation_ID, @Parts_ID, @CancelledQuantity, @Rate, 
                                                @Amount, @WareHouse_ID, @OrderedQuantity, @InvoicedQuantity, @PickedQuantity
                                            )", connection, transaction);

                                            // Always set mandatory fields
                                            detailCommand.Parameters.AddWithValue("@OrderCancellation_ID", headerRow.OrderCancellation_ID);
                                            detailCommand.Parameters.AddWithValue("@Parts_ID", detail.Parts_ID);

                                            // Use DBNull.Value if the field can be NULL
                                            detailCommand.Parameters.AddWithValue("@CancelledQuantity", detail.CancelledQuantity.HasValue ? (object)detail.CancelledQuantity.Value : DBNull.Value);
                                            detailCommand.Parameters.AddWithValue("@Rate", detail.Rate);
                                            detailCommand.Parameters.AddWithValue("@Amount", detail.Amount.HasValue ? (object)detail.Amount.Value : DBNull.Value);
                                            detailCommand.Parameters.AddWithValue("@WareHouse_ID", detail.WareHouse_ID);

                                            // OrderedQuantity, InvoicedQuantity, and PickedQuantity may accept nulls too
                                            detailCommand.Parameters.AddWithValue("@OrderedQuantity", detail.OrderedQuantity.HasValue ? (object)detail.OrderedQuantity.Value : DBNull.Value);
                                            detailCommand.Parameters.AddWithValue("@InvoicedQuantity", detail.InvoicedQuantity.HasValue ? (object)detail.InvoicedQuantity.Value : DBNull.Value);
                                            detailCommand.Parameters.AddWithValue("@PickedQuantity", detail.PickedQuantity.HasValue ? (object)detail.PickedQuantity.Value : DBNull.Value);

                                            detailCommand.ExecuteNonQuery();
                                        }

                                        // Update parts stock
                                        foreach (var part in headerRow.PRT_OrderCancellationDetails)
                                        {
                                            var updateStockCommand = new SqlCommand(@"
                            UPDATE GNM_PartsStockDetail 
                            SET PendingPurchaseOrderQuantity = PendingPurchaseOrderQuantity - @CancelledQuantity
                            WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID", connection, transaction);

                                            updateStockCommand.Parameters.AddWithValue("@CancelledQuantity", part.CancelledQuantity);
                                            updateStockCommand.Parameters.AddWithValue("@Parts_ID", part.Parts_ID);
                                            updateStockCommand.Parameters.AddWithValue("@WareHouse_ID", part.WareHouse_ID);

                                            updateStockCommand.ExecuteNonQuery();

                                            var updatePartDetailCommand = new SqlCommand(@"
                            UPDATE PRT_PurchaseOrderPartsDetail 
                            SET BackOrderCancelledQuantity = BackOrderCancelledQuantity + @CancelledQuantity
                            WHERE Parts_ID = @Parts_ID AND PurchaseOrder_ID = @PurchaseOrder_ID", connection, transaction);

                                            updatePartDetailCommand.Parameters.AddWithValue("@CancelledQuantity", part.CancelledQuantity);
                                            updatePartDetailCommand.Parameters.AddWithValue("@Parts_ID", part.Parts_ID);
                                            updatePartDetailCommand.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);

                                            updatePartDetailCommand.ExecuteNonQuery();
                                        }

                                        // Calculate pending quantity
                                        decimal pendingQty = 0;
                                        var pendingQtyCommand = new SqlCommand(@"
                                    SELECT SUM(COALESCE(ApprovedQuantity, 0) - COALESCE(InvoicedQuantity, 0) - COALESCE(BackOrderCancelledQuantity, 0)) 
                                    FROM PRT_PurchaseOrderPartsDetail
                                    WHERE PurchaseOrder_ID = @PurchaseOrder_ID", connection, transaction);

                                        pendingQtyCommand.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);

                                        using (var reader = pendingQtyCommand.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                if (!reader.IsDBNull(0))
                                                {
                                                    pendingQty = reader.GetDecimal(0);
                                                }
                                            }
                                        }


                                        // Get POStatus_ID based on "Closed" or "Partial" status
                                        int poStatusID = 0;
                                        var statusQueryCommand = new SqlCommand(@"
                                        SELECT RefMasterDetail_ID
                                        FROM GNM_RefMasterDetail
                                        join GNM_RefMaster on GNM_RefMasterDetail.RefMaster_ID = GNM_RefMaster.RefMaster_ID
                                        WHERE GNM_RefMaster.RefMaster_Name = 'POSTATUS' 
                                        AND GNM_RefMasterDetail.RefMasterDetail_Short_Name = @StatusType", connection, transaction);

                                        statusQueryCommand.Parameters.AddWithValue("@StatusType", pendingQty <= 0 ? "Closed" : "Partial");

                                        using (var reader = statusQueryCommand.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                poStatusID = reader.GetInt32(0);
                                            }
                                        }

                                        // Update the purchase order status
                                        if (poStatusID > 0)
                                        {
                                            var statusUpdateCommand = new SqlCommand(@"
                                            UPDATE PRT_PurchaseOrder 
                                            SET POStatus_ID = @POStatus_ID
                                            WHERE PurchaseOrder_ID = @PurchaseOrder_ID", connection, transaction);

                                            statusUpdateCommand.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);
                                            statusUpdateCommand.Parameters.AddWithValue("@POStatus_ID", poStatusID);

                                            statusUpdateCommand.ExecuteNonQuery();
                                        }

                                        jsonData = new
                                        {
                                            IsPSExists = true,
                                            IsSuccess = true,
                                            OrderCancellation_ID = poCancellationNumber
                                        };
                                    }
                                    else
                                    {
                                        jsonData = new
                                        {
                                            IsPSExists = false,
                                            IsSuccess = false,
                                            OrderCancellation_ID = 0
                                        };
                                    }
                                }

                                transaction.Commit();
                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }
                                jsonData = new
                                {
                                    IsPSExists = true,
                                    IsSuccess = false,
                                    OrderCancellation_ID = 0
                                };
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    jsonData = new
                    {
                        IsPSExists = true,
                        IsSuccess = false,
                        OrderCancellation_ID = 0
                    };
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


       
        
    }


    #region ::: Classes:::
    public class PurchaseOrderCancellationInterfaceList
    {

        public string PurchaseOrderNumber { get; set; }  // PRT_PurchaseOrder


        public string PartsOrderNumber { get; set; }  // PRT_PartsOrder


        public bool OrderCancellationType { get; set; }  // OrderCancellation_Type


    


        public string ReasonForCancellation { get; set; }  // ReasonForCancellation


        public string BranchShortName { get; set; }  // GNM_Branch


        public string UserLoginID { get; set; }  // GNM_User

        public DateTime ModifiedDate { get; set; }  // ModifiedDate

        public DateTime PartsOrderCancellationDate { get; set; }  // PartsOrderCancellationDate
        public List<PartsOrderPartsDetail> PartsOrderDetails { get; set; }
        public PurchaseOrderCancellationInterfaceList()
        {
            if (OrderCancellationType == null)
                OrderCancellationType = true;

            if (UserLoginID == null)
                UserLoginID = "quest_admin";
            if (PartsOrderNumber == null)
                PartsOrderNumber = "";

            if (ModifiedDate == default(DateTime))
                ModifiedDate = DateTime.Now;

            if (PartsOrderCancellationDate == default(DateTime))
                PartsOrderCancellationDate = DateTime.Now;

            PartsOrderDetails = new List<PartsOrderPartsDetail>();


        }


    }
    public class PartsOrderPartsDetail
    {
        public string PartsPartPrefix { get; set; }  // Parts_PartPrefix

        public string PartsPartsNumber { get; set; }  // Parts_PartsNumber

       

        public decimal PickedQuantity { get; set; }  // PickedQuantity

       

       

        public decimal CancelledQuantity { get; set; }  // CancelledQuantity

      



    

       
        public PartsOrderPartsDetail()
        {
            if (PartsPartPrefix == null)
                PartsPartPrefix = "OEM";
        }
    }
    public class POCSaveList
    {
        public int Branch { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public string Data { get; set; }
    }
    public partial class PRT_OrderCancellationDetails
    {
        public int OrderCancellationDetail_ID { get; set; }
        public int OrderCancellation_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> PickedQuantity { get; set; }
        public Nullable<decimal> OrderedQuantity { get; set; }
        public Nullable<decimal> InvoicedQuantity { get; set; }
        public Nullable<decimal> CancelledQuantity { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public int WareHouse_ID { get; set; }
        public Nullable<decimal> AllocatedQuantity { get; set; }
        public Nullable<decimal> BackOrderQuantity { get; set; }
        public Nullable<decimal> MRP { get; set; }
        public Nullable<int> PartsOrderPartsDetail_ID { get; set; }
        public virtual PRT_POCancellation PRT_POCancellation { get; set; }
    }
    public partial class PRT_POCancellation
    {
        public PRT_POCancellation()
        {
            this.PRT_OrderCancellationDetails = new HashSet<PRT_OrderCancellationDetails>();
        }

        public int OrderCancellation_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public Nullable<int> PartsOrder_ID { get; set; }
        public bool OrderCancellation_Type { get; set; }
        public Nullable<decimal> TotalOrderCancellationAmount { get; set; }
        public string ReasonForCancellation { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public Nullable<int> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public System.DateTime PartsOrderCancellationDate { get; set; }
        public string POCancellationNumber { get; set; }
        public int FinancialYear { get; set; }
        public Nullable<int> DocumentNumber { get; set; }

        public virtual ICollection<PRT_OrderCancellationDetails> PRT_OrderCancellationDetails { get; set; }
    }
    public class PRT_POrderCancellationDetails
    {
        public int OrderCancellationDetail_ID { get; set; }
        public int OrderCancellation_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> PickedQuantity { get; set; }
        public Nullable<decimal> OrderedQuantity { get; set; }
        public Nullable<decimal> InvoicedQuantity { get; set; }
        public Nullable<decimal> CancelledQuantity { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public int WareHouse_ID { get; set; }
        public int PartsOrderID { get; set; }
    }

    public class PRT_POCancellationH
    {
        public PRT_POCancellationH()
        {
            this.PRT_OrderCancellationDetails = new List<PRT_POrderCancellationDetails>();
        }

        public int OrderCancellation_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public Nullable<int> PartsOrder_ID { get; set; }
        public bool OrderCancellation_Type { get; set; }
        public Nullable<decimal> TotalOrderCancellationAmount { get; set; }
        public string ReasonForCancellation { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public Nullable<int> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public System.DateTime PartsOrderCancellationDate { get; set; }

        public List<PRT_POrderCancellationDetails> PRT_OrderCancellationDetails { get; set; }
    }
    #endregion
}
