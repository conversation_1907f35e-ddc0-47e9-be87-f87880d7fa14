﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreFunctionGroupMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreFunctionGroupMasterController : ApiController
    {

        #region ::: Select Function Groups Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/SelAllFunctionGroups")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllFunctionGroups([FromBody] SelAllFunctionGroupsList SelAllFunctionGroupsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreFunctionGroupMasterServices.SelAllFunctionGroups(connstring, SelAllFunctionGroupsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Function Groups Native Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>   
        ///  
        [Route("api/CoreFunctionGroupMaster/SelFunctionGroupsNative")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelFunctionGroupsNative([FromBody] SelFunctionGroupsNativeList SelFunctionGroupsNativeobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreFunctionGroupMasterServices.SelFunctionGroupsNative(connstring, SelFunctionGroupsNativeobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// Exporting 
        /// </summary> 
        /// 
        [Route("api/CoreFunctionGroupMaster/FunctionGroupExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> FunctionGroupExport([FromBody] FunctionGroupExportList FunctionGroupExportobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = FunctionGroupExportobj.sidx;
            string sord = FunctionGroupExportobj.sord;
            string filter = FunctionGroupExportobj.filter;
            string advnceFilter = FunctionGroupExportobj.advanceFilter;

            try
            {


                Object Response = await CoreFunctionGroupMasterServices.FunctionGroupExport(FunctionGroupExportobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: to Select Function Group Operations Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// to Select Function Group Operatins
        /// </summary> 
        ///
        [Route("api/CoreFunctionGroupMaster/SelFunGroupsOperations")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelFunGroupsOperations([FromBody] SelFunGroupsOperationsList SelFunGroupsOperationsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreFunctionGroupMasterServices.SelFunGroupsOperations(connstring, SelFunGroupsOperationsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelParticularFunctionGroup Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// to SelParticularFunctionGroup
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/SelParticularFunctionGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelParticularFunctionGroup([FromBody] SelParticularFunctionGroupList SelParticularFunctionGroupobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.SelParticularFunctionGroup(connString, SelParticularFunctionGroupobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Select FunctionGroup Locale Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// Select FunctionGroup Locale
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/SelFunctionGroupLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelFunctionGroupLocale([FromBody] SelFunctionGroupLocaleList SelFunctionGroupLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.SelFunctionGroupLocale(connString, SelFunctionGroupLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Save Function Group Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Save Function Group
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/SaveFunctionGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveFunctionGroup([FromBody] SaveFunctionGroupList SaveFunctionGroupobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.SaveFunctionGroup(connString, SaveFunctionGroupobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Delete FunctionGroup Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// Delete FunctionGroup
        /// </summary>
        ///  
        [Route("api/CoreFunctionGroupMaster/DeleteFunctionGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteFunctionGroup([FromBody] DeleteFunctionGroupList DeleteFunctionGroupobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.DeleteFunctionGroup(connString, DeleteFunctionGroupobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Insert Function Group Locale Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Insert Function Group Locale
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/InsertFunctionGroupLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertFunctionGroupLocale([FromBody] InsertFunctionGroupLocaleList InsertFunctionGroupLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.InsertFunctionGroupLocale(connString, InsertFunctionGroupLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Update Function Group Locale Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Update Function Group Locale
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/UpdateFunctionGroupLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateFunctionGroupLocale([FromBody] UpdateFunctionGroupLocaleList UpdateFunctionGroupLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.UpdateFunctionGroupLocale(connString, UpdateFunctionGroupLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Get Brand Details Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Get Brand Details
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/GetBrandDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetBrandDetails([FromBody] GetBrandDetailsList GetBrandDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.GetBrandDetails(connString, GetBrandDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  LoadBrandforLanguage Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// LoadBrandforLanguage
        /// </summary>
        ///
        [Route("api/CoreFunctionGroupMaster/LoadBrandforLanguage")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBrandforLanguage([FromBody] GetBrandDetailsList GetBrandDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.LoadBrandforLanguage(connString, GetBrandDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Check If FunctionExits Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// CheckIfFunctionExits
        /// </summary>
        /// 
        [Route("api/CoreFunctionGroupMaster/CheckIfFunctionExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckIfFunctionExits([FromBody] CheckIfFunctionExitsList CheckIfFunctionExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.CheckIfFunctionExits(connString, CheckIfFunctionExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Check If FunctionLocale Exits Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// CheckIfFunctionLocaleExits
        /// </summary>
        ///
        [Route("api/CoreFunctionGroupMaster/CheckIfFunctionLocaleExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckIfFunctionLocaleExits([FromBody] CheckIfFunctionLocaleExitsList CheckIfFunctionLocaleExitsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreFunctionGroupMasterServices.CheckIfFunctionLocaleExits(connString, CheckIfFunctionLocaleExitsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}