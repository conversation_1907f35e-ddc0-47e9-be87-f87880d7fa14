﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreModuleMasterServices
    {
        static string AppPath = string.Empty;

        #region ::: To select menus of respective module :::
        /// <summary>
        /// SelectMenu
        /// </summary>
        /// <param name="connString"></param>
        /// <param name="SelectMenuObj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        public static IActionResult SelectMenu(string connString, SelectMenuList SelectMenuObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, int LogException, string advnceFilters)
        {
            dynamic dummy = null;
            var jsonobj = dummy;
            int count = 0;
            int total = 0;

            IQueryable<MenuObjects> iQMenuList = null;
            IEnumerable<MenuObjects> MenuList = null;
            List<MenuObjects> menuList = new List<MenuObjects>();
            List<GNM_Menu> gMenuList = new List<GNM_Menu>();
            List<GNM_Object> gobjList = new List<GNM_Object>();

            try
            {
                string GenLangCode = SelectMenuObj.GeneralLanguageCode.ToString();
                string UserLangCode = SelectMenuObj.UserLanguageCode.ToString();
                try
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_SELECT_AM_ERP_SelectMenu_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Module_ID", SelectMenuObj.moduleid);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var sortOrderValue = reader.IsDBNull(reader.GetOrdinal("Sort_order")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Sort_order"));
                                        var data = new MenuObjects
                                        {
                                            Menu_ID = reader.GetInt32(reader.GetOrdinal("Menu_ID")),
                                            Menu_Description = reader.IsDBNull(reader.GetOrdinal("Menu_Description")) ? null : reader.GetString(reader.GetOrdinal("Menu_Description")),
                                            ParentMenu = reader.IsDBNull(reader.GetOrdinal("ParentMenu")) ? null : reader.GetString(reader.GetOrdinal("ParentMenu")),
                                            Object = reader.IsDBNull(reader.GetOrdinal("Object")) ? null : reader.GetString(reader.GetOrdinal("Object")),
                                            Menu_path = reader.IsDBNull(reader.GetOrdinal("Menu_Path")) ? null : reader.GetString(reader.GetOrdinal("Menu_Path")),
                                            Sort_order = sortOrderValue.HasValue ? sortOrderValue.Value : 0,
                                            Menu_active = reader.IsDBNull(reader.GetOrdinal("Menu_active")) ? null : reader.GetString(reader.GetOrdinal("Menu_active"))
                                        };
                                        menuList.Add(data);
                                    }
                                    if (reader.NextResult())
                                    {
                                        while (reader.Read())
                                        {
                                            var menu = new GNM_Menu
                                            {
                                                Menu_ID = reader.GetInt32(reader.GetOrdinal("Menu_ID")),
                                                Menu_Description = reader.IsDBNull(reader.GetOrdinal("Menu_Description")) ? null : reader.GetString(reader.GetOrdinal("Menu_Description")),
                                                Parentmenu_ID = reader.IsDBNull(reader.GetOrdinal("Parentmenu_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Parentmenu_ID")),
                                                Object_ID = reader.IsDBNull(reader.GetOrdinal("Object_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Object_ID")),
                                                Menu_Path = reader.IsDBNull(reader.GetOrdinal("Menu_Path")) ? null : reader.GetString(reader.GetOrdinal("Menu_Path")),
                                                Menu_SortOrder = reader.IsDBNull(reader.GetOrdinal("Menu_SortOrder")) ? (byte)0 : (byte)reader.GetInt32(reader.GetOrdinal("Menu_SortOrder")),
                                                Menu_IsActive = reader.GetBoolean(reader.GetOrdinal("Menu_IsActive")),
                                                Module_ID = reader.IsDBNull(reader.GetOrdinal("Module_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Module_ID"))
                                            };

                                            gMenuList.Add(menu);
                                        }
                                    }
                                    if (reader.NextResult())
                                    {
                                        while (reader.Read())
                                        {
                                            var obj = new GNM_Object
                                            {
                                                Object_ID = reader.GetInt32(reader.GetOrdinal("Object_ID")),
                                                Object_Description = reader.IsDBNull(reader.GetOrdinal("Object_Description")) ? null : reader.GetString(reader.GetOrdinal("Object_Description")),
                                                Object_IsActive = reader.GetBoolean(reader.GetOrdinal("Object_IsActive"))
                                            };

                                            gobjList.Add(obj);
                                        }
                                    }


                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }

                string ParentMenus = "0:------------Select--------------;";
                for (int i = 0; i < gMenuList.Count(); i++)
                {
                    ParentMenus = ParentMenus + gMenuList.ElementAt(i).Menu_ID + ":" + gMenuList.ElementAt(i).Menu_Description + ";";
                }

                ParentMenus = ParentMenus.TrimEnd(new char[] { ';' });

                string objects = "0:----------Select------------;";
                for (int i = 0; i < gobjList.Count(); i++)
                {
                    objects = objects + gobjList.ElementAt(i).Object_ID + ":" + gobjList.ElementAt(i).Object_Description + ";";
                }
                objects = objects.TrimEnd(new char[] { ';' });

                //MenuList = from MenuDetails in gMenuNew
                //           join obj in GEClient.GNM_Object on MenuDetails.Object_ID equals obj.Object_ID into objectlist
                //           from objdetails in objectlist.DefaultIfEmpty(new GNM_Object { Object_Description = "" })
                //           join parentmenu in gMenuNew on MenuDetails.Parentmenu_ID equals parentmenu.Menu_ID into MenuNames
                //           from Mdetails in MenuNames.DefaultIfEmpty(new GNM_Menu { Menu_Description = "" })
                //           select new MenuObjects()
                //           {
                //               Menu_ID = MenuDetails.Menu_ID,
                //               Menu_Description = MenuDetails.Menu_Description,
                //               ParentMenu = Mdetails.Menu_Description,
                //               Object = objdetails.Object_Description,
                //               Menu_path = MenuDetails.Menu_Path,
                //               Sort_order = MenuDetails.Menu_SortOrder,
                //               Menu_active = (MenuDetails.Menu_IsActive == true ? "Yes" : "No"),
                //           };



                if (SelectMenuObj.moduleid != 0)
                {
                    iQMenuList = menuList.AsQueryable<MenuObjects>();
                    //FilterToolBar Search

                    if (_search)
                    {
                        string decodedValue = Uri.UnescapeDataString(filters);
                        WorkFlow.Models.Filters filterObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<WorkFlow.Models.Filters>();
                        if (filterObj.rules.Count > 0)
                        {
                            iQMenuList = iQMenuList.FilterSearch<MenuObjects>(filterObj);
                        }
                    }
                    else if (advnce)
                    {
                        string decodedValue = Uri.UnescapeDataString(advnceFilters);
                        AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();

                        iQMenuList = iQMenuList.AdvanceSearch<MenuObjects>(advnfilter);
                    }
                    //Sorting 
                    iQMenuList = iQMenuList.OrderByField<MenuObjects>(sidx, sord);

                    count = iQMenuList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    //Session["Export"] = iQMenuList.ToList();
                    jsonobj = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = count,
                        ddlMenus = ParentMenus,
                        ddlObjects = objects,
                        rows = (from MenuDetails in iQMenuList
                                select new
                                {
                                    Menu_ID = MenuDetails.Menu_ID,
                                    Edit = "<img key='" + MenuDetails.Menu_ID + "' src='" + AppPath + "/Content/images/edit.gif' mode='Read' class='edtClick' />",
                                    delete = "<input key='" + MenuDetails.Menu_ID + "' type='checkbox' defaultchecked='' class='chkClick' />",
                                    Menu_Description = MenuDetails.Menu_Description,
                                    ParentMenu = MenuDetails.ParentMenu,
                                    Object = MenuDetails.Object,
                                    Menu_path = MenuDetails.Menu_path,
                                    Sort_order = MenuDetails.Sort_order,
                                    Menu_active = MenuDetails.Menu_active,
                                    Locale = (GenLangCode == UserLangCode) ? "" : "<img src='" + AppPath + "/Content/images/local.png' height='20' width=20 class='MenuLocale' key='" + MenuDetails.Menu_ID + "' />"
                                }).ToList().Paginate(page, rows)
                    };

                }
                else
                {
                    jsonobj = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = count,
                        ddlMenus = ParentMenus,
                        ddlObjects = objects
                    };
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                //  return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }                   // 
                }
                //  return RedirectToAction("Error");
            }

            return new JsonResult(jsonobj);
        }
        #endregion

        #region ::: To insert Module and respective Menus :::
        /// <summary>
        /// To insert Module and respective Menus
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Insert(InsertList InsertObj, string connectionString, int LogException)
        {
            JTokenReader jr = null;
            GNM_Module gModule = new GNM_Module();
            InsertList insertList = new InsertList();
            List<GNM_Menu> gMenuList = new List<GNM_Menu>();
            dynamic dummy = null;
            var jsonResult = dummy;
            var newModuleId = 0;
            try
            {
                JObject jObj = JObject.Parse(InsertObj.Data);

                jr = new JTokenReader(jObj["ModuleName"]);
                jr.Read();
                gModule.Module_Description = Common.DecryptString(jr.Value.ToString());

                jr = new JTokenReader(jObj["ModuleSortOrder"]);
                jr.Read();
                gModule.Module_SortOrder = Convert.ToByte(jr.Value);

                jr = new JTokenReader(jObj["ModuleActive"]);
                jr.Read();
                gModule.Module_IsActive = Convert.ToBoolean(jr.Value);




                int rowcount = jObj["rows"].Count();

                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Mode"]);
                    jr.Read();
                    string Mode = jr.Value.ToString();

                    GNM_Menu gMenu = new GNM_Menu();

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menuname"]);
                    jr.Read();
                    gMenu.Menu_Description = Common.DecryptString(jr.Value.ToString());

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ParentMenuid"]);
                    jr.Read();
                    gMenu.Parentmenu_ID = Convert.ToInt32(jr.Value);

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Objectid"]);
                    jr.Read();
                    gMenu.Object_ID = Convert.ToInt32(jr.Value);

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menu_path"]);
                    jr.Read();
                    //  gMenu.Menu_Path = Uri.UnescapeDataString(jr.Value.ToString())+"?ObjectID=" + gMenu.Object_ID;
                    gMenu.Menu_Path = (Common.DecryptString(jr.Value.ToString()) == "") ? "#" : (Common.DecryptString(jr.Value.ToString()) == "#") ? "#" : (Common.DecryptString(jr.Value.ToString()) + "?ObjectID=" + gMenu.Object_ID);


                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Sort_order"]);
                    jr.Read();
                    gMenu.Menu_SortOrder = Convert.ToByte(jr.Value);

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IsActive"]);
                    jr.Read();
                    gMenu.Menu_IsActive = Convert.ToBoolean(jr.Value.ToString());

                    //gModule.GNM_Menu.Add(gMenu);
                    try
                    {
                        string GenLangCode = InsertObj.GeneralLanguageCode.ToString();
                        string UserLangCode = InsertObj.UserLanguageCode.ToString();
                        try
                        {
                            using (SqlConnection conn = new SqlConnection(connectionString))
                            {
                                string query = "UP_INSERT_AM_ERP_Insert_CoreModuleMaster";

                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.StoredProcedure;
                                        command.Parameters.AddWithValue("@Module_Description",
          string.IsNullOrEmpty(gModule.Module_Description) ? (object)DBNull.Value : gModule.Module_Description);

                                        command.Parameters.AddWithValue("@Module_SortOrder",
                                            gModule.Module_SortOrder == null ? (object)DBNull.Value : gModule.Module_SortOrder);

                                        command.Parameters.AddWithValue("@Module_IsActive",
                                            gModule.Module_IsActive == null ? (object)DBNull.Value : gModule.Module_IsActive);

                                        command.Parameters.AddWithValue("@Module_IconName",
                                            string.IsNullOrEmpty(gModule.Module_IconName) ? (object)DBNull.Value : gModule.Module_IconName);

                                        // Add parameters for the menu item
                                        command.Parameters.AddWithValue("@Menu_Description",
                                            string.IsNullOrEmpty(gMenu.Menu_Description) ? (object)DBNull.Value : gMenu.Menu_Description);

                                        command.Parameters.AddWithValue("@Parentmenu_ID",
                                            gMenu.Parentmenu_ID == null ? (object)DBNull.Value : gMenu.Parentmenu_ID);

                                        command.Parameters.AddWithValue("@Object_ID",
                                            gMenu.Object_ID == null ? (object)DBNull.Value : gMenu.Object_ID);

                                        command.Parameters.AddWithValue("@Menu_Path",
                                            string.IsNullOrEmpty(gMenu.Menu_Path) ? (object)DBNull.Value : gMenu.Menu_Path);

                                        command.Parameters.AddWithValue("@Menu_SortOrder",
                                            gMenu.Menu_SortOrder == null ? (object)DBNull.Value : gMenu.Menu_SortOrder);

                                        command.Parameters.AddWithValue("@Menu_IsActive",
                                            gMenu.Menu_IsActive == null ? (object)DBNull.Value : gMenu.Menu_IsActive);

                                        command.Parameters.AddWithValue("@Menu_IconName",
                                            string.IsNullOrEmpty(gMenu.Menu_IconName) ? (object)DBNull.Value : gMenu.Menu_IconName);
                                        command.Parameters.AddWithValue("@count", i);



                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }

                                        if (i == 0)
                                        {
                                            newModuleId = (int)command.ExecuteScalar();
                                        }
                                        else
                                        {

                                            command.Parameters.AddWithValue("@NewModuleID", newModuleId);
                                            command.ExecuteScalar();
                                        }



                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    command.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }



                        }
                        catch (WebException wex)
                        {
                            LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }






                    }
                    catch (WebException wex)
                    {
                        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                        //  return RedirectToAction("Error");
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 0)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }                   // 
                        }
                        //  return RedirectToAction("Error");
                    }

                }
                //GEClient.GNM_Module.Add(gModule);
                //GEClient.SaveChanges();
                //gbl.InsertGPSDetails(Convert.ToInt32(InsertObj.Company_ID), Convert.ToInt32(InsertObj.Branch), Convert.ToInt32(InsertObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), gModule.Module_ID, 0, 0, "Insert", false, Convert.ToInt32(InsertObj.MenuID));
                jsonResult = new
                {
                    Result = "Success",
                    id = newModuleId.ToString()
                };

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                jsonResult = new
                {
                    Result = "Fail",
                    id = 0
                };
                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "Fail",
                    id = 0
                };
                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region::: To select all modules :::
        /// <summary>
        ///To select all modules
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectModule(string connString, SelectModuleList SelectModuleObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, int LogException)
        {
            dynamic dummy = null;
            var jsonobj = dummy;
            IEnumerable<GNM_Module> gModule = null;

            int count = 0;
            int total = 0;

            JTokenReader jTR;
            try
            {
                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    JObject jObj = JObject.Parse(Common.DecryptString(decodedValue));
                    string fields = string.Empty;
                    int rowcount = jObj["rules"].Count();
                    for (int i = 0; i < rowcount; i++)
                    {
                        if (fields != string.Empty)
                        {
                            fields = fields + " and ";
                        }
                        jTR = new JTokenReader(jObj["rules"].ElementAt(i).ToObject<JObject>()["field"]);
                        jTR.Read();
                        fields = fields + jTR.Value;
                        jTR = new JTokenReader(jObj["rules"].ElementAt(i).ToObject<JObject>()["data"]);
                        jTR.Read();
                        fields = fields + " like '%" + jTR.Value + "%'";
                    }
                    //Query = "Select * From GNM_Module where " + fields + " order by " + sidx + " " + sord;
                    //gModule = GEClient.Database.SqlQuery(typeof(GNM_Module), Query).Cast<GNM_Module>();
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = "Select * From GNM_Module where " + fields + " order by " + sidx + " " + sord;

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.Text;



                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        List<GNM_Module> moduleList = new List<GNM_Module>();
                                        while (reader.Read())
                                        {
                                            GNM_Module module = new GNM_Module
                                            {
                                                Module_ID = Convert.ToInt32(reader["Module_ID"]),
                                                Module_Description = reader["Module_Description"].ToString(),
                                                Module_IsActive = Convert.ToBoolean(reader["Module_IsActive"]),
                                                Module_SortOrder = Convert.ToByte(reader["Module_SortOrder"])

                                            };
                                            moduleList.Add(module);

                                        }
                                        gModule = moduleList;




                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }



                    }
                    catch (WebException wex)
                    {
                        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                }
                else
                {

                    List<GNM_Module> moduleList = new List<GNM_Module>();
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = "SELECT * FROM GNM_Module ORDER BY Module_Description";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.Text;



                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            GNM_Module module = new GNM_Module
                                            {
                                                Module_ID = Convert.ToInt32(reader["Module_ID"]),
                                                Module_Description = reader["Module_Description"].ToString(),
                                                Module_IsActive = Convert.ToBoolean(reader["Module_IsActive"]),
                                                Module_SortOrder = Convert.ToByte(reader["Module_SortOrder"])  // Explicit conversion to byte
                                            };

                                            moduleList.Add(module);
                                        }

                                        gModule = moduleList;



                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }



                    }
                    catch (WebException wex)
                    {
                        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }

                    // }
                }
                count = gModule.Count();
                total = Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / rows));

                var list =
                    (from ModuleDetails in gModule
                     select new
                     {
                         Module_id = ModuleDetails.Module_ID,
                         Edit = "<img key='" + ModuleDetails.Module_ID + "' src='" + AppPath + "/Content/images/edit.gif' mode='Read' class='edtModuleClick' />",
                         delete = (SelectModuleObj.IsDelete) ? ("<input key='" + ModuleDetails.Module_ID + "' type='checkbox' defaultchecked='' class='chkClick' />") : ("<input key='" + ModuleDetails.Module_ID + "' type='checkbox' defaultchecked='' class='chkClick' disabled=" + true + " />"),
                         Module_Description = ModuleDetails.Module_Description,
                         Module_IsActive = (ModuleDetails.Module_IsActive == true ? "Yes" : "No"),
                         Module_SortOrder = ModuleDetails.Module_SortOrder
                     }).ToList().Paginate(page, rows);


                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = list
                };

            }

            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonobj);
        }
        #endregion
        #region ::: To modify module and respective menus :::
        /// <summary>
        /// To modify module and respective menus 
        /// </summary>
        /// <param name="EditObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Edit(EditList EditObj, string connectionString, int LogException)
        {
            JTokenReader jr = null;
            GNM_Menu gMenu = null;
            GNM_Module gModule = null;
            dynamic dummy = null;
            var jsonResult = dummy;
            try
            {
                JObject jObj = JObject.Parse(EditObj.Data);

                jr = new JTokenReader(jObj["ModuleID"]);
                jr.Read();
                int ModuleID = Convert.ToInt32(jr.Value);





                int rowcount = jObj["rows"].Count();

                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Mode"]);
                    jr.Read();
                    string Mode = jr.Value.ToString();
                    int Menuid = 0;
                    var Menu_Description = string.Empty;
                    int Parentmenu_ID = 0;
                    int Object_ID = 0;
                    var Menu_Path = string.Empty;
                    byte Menu_SortOrder = 0;
                    bool Menu_IsActive = false;
                    int Module_ID = 0;
                    if (Mode == "Edit")
                    {
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menu_ID"]);
                        jr.Read();
                        Menuid = Convert.ToInt32(jr.Value);


                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menuname"]);
                        jr.Read();
                        Menu_Description = Common.DecryptString(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ParentMenuid"]);
                        jr.Read();
                        Parentmenu_ID = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Objectid"]);
                        jr.Read();
                        Object_ID = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menu_path"]);
                        jr.Read();
                        // gMenu.Menu_Path = Uri.UnescapeDataString(jr.Value.ToString())+"?ObjectID=" + gMenu.Object_ID;
                        Menu_Path = Common.DecryptString(jr.Value.ToString()); //+"?ObjectID=" + gMenu.Object_ID;

                        string[] arr = Menu_Path.Split('?');
                        Menu_Path = arr[0].ToString();
                        Menu_Path = (Menu_Path == "#") ? "#" : Menu_Path + "?ObjectID=" + Object_ID;


                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Sort_order"]);
                        jr.Read();
                        Menu_SortOrder = Convert.ToByte(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IsActive"]);
                        jr.Read();
                        Menu_IsActive = Convert.ToBoolean(jr.Value.ToString());

                        //gbl.InsertGPSDetails(Convert.ToInt32(EditObj.Company_ID), Convert.ToInt32(EditObj.Branch), Convert.ToInt32(EditObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), ModuleID, 0, 0, "Update", false, Convert.ToInt32(EditObj.MenuID));
                    }
                    else if (Mode == "Add")
                    {
                        GNM_Menu gmenuNew = new GNM_Menu();
                        Module_ID = ModuleID;
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menuname"]);
                        jr.Read();
                        Menu_Description = Common.DecryptString(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ParentMenuid"]);
                        jr.Read();
                        Parentmenu_ID = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Objectid"]);
                        jr.Read();
                        Object_ID = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Menu_path"]);
                        jr.Read();
                        // gmenuNew.Menu_Path = Uri.UnescapeDataString(jr.Value.ToString())+"?ObjectID=" + gmenuNew.Object_ID; ;
                        Menu_Path = (Common.DecryptString(jr.Value.ToString()) == "") ? "#" : (Common.DecryptString(jr.Value.ToString()) == "#") ? "#" : Common.DecryptString(jr.Value.ToString()) + "?ObjectID=" + gmenuNew.Object_ID; ;

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Sort_order"]);
                        jr.Read();
                        Menu_SortOrder = Convert.ToByte(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IsActive"]);
                        jr.Read();
                        Menu_IsActive = Convert.ToBoolean(jr.Value.ToString());


                        //gbl.InsertGPSDetails(Convert.ToInt32(EditObj.Company_ID), Convert.ToInt32(EditObj.Branch), Convert.ToInt32(EditObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), ModuleID, 0, 0, "Update", false, Convert.ToInt32(EditObj.MenuID));
                    }
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(connectionString))
                        {
                            string query = "UP_InsertUpdate_AM_ERP_Edit_CoreModuleMaster";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@Mode", Mode); // Edit Mode
                                    command.Parameters.AddWithValue("@MenuID", Menuid);
                                    command.Parameters.AddWithValue("@MenuName", Common.DecryptString(Menu_Description));
                                    command.Parameters.AddWithValue("@ParentMenuID", Parentmenu_ID);
                                    command.Parameters.AddWithValue("@ObjectID", Object_ID);
                                    command.Parameters.AddWithValue("@MenuPath", Common.DecryptString(Menu_Path));
                                    command.Parameters.AddWithValue("@SortOrder", Menu_SortOrder);
                                    command.Parameters.AddWithValue("@IsActive", Menu_IsActive);
                                    command.Parameters.AddWithValue("@ModuleID", Module_ID);


                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    command.ExecuteNonQuery();
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }



                    }
                    catch (WebException wex)
                    {
                        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                    }

                }

                jsonResult = new
                {
                    Result = "Success",
                    id = ModuleID
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


                jsonResult = new
                {
                    Result = "Error occured while saving",
                    id = 0
                };
                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new
                {
                    Result = "Error occured while saving",
                    id = 0
                };
                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: To delete menus :::
        /// <summary>
        ///  To delete menus
        /// </summary>
        /// <param name="DeleteMenuObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        public static IActionResult DeleteMenu(DeleteMenuList DeleteMenuObj, string connectionString, int LogException)
        {
            GNM_Menu gMenu = null;
            IEnumerable<GNM_MenuLocale> gMenuLocale = null;
            JTokenReader jr = null;
            string Result = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(DeleteMenuObj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    //gMenu = GEClient.GNM_Menu.Where(menuid => menuid.Menu_ID == id).First();

                    //gMenuLocale = GEClient.GNM_MenuLocale.Where(mid => mid.Menu_ID == gMenu.Menu_ID);

                    //foreach (var m in gMenuLocale)
                    //{
                    //    GEClient.GNM_MenuLocale.Remove(m);
                    //}

                    //GEClient.GNM_Menu.Remove(gMenu);
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(connectionString))
                        {
                            string query = "UP_DELETE_AM_ERP_DeleteMenu_CoreModuleMaster";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@MenuID", id); // Edit Mode



                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    command.ExecuteNonQuery();
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }



                    }
                    catch (WebException wex)
                    {
                        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                    }
                }
                //GEClient.SaveChanges();
                //gbl.InsertGPSDetails(Convert.ToInt32(DeleteMenuObj.Company_ID), Convert.ToInt32(DeleteMenuObj.Branch), Convert.ToInt32(DeleteMenuObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteMenuObj.MenuID));
                Result = "Deleted successfully";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                Result = "Dependancy Found, Cannot delete this record";
                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Result = CommonFunctionalities.GetResourceString(DeleteMenuObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();// "Dependancy Found, Cannot delete this record";
                //  return RedirectToAction("Error");
            }
            return new JsonResult(Result);
        }
        #endregion
        #region ::: To delete module:::
        /// <summary>
        /// To delete module
        /// </summary>
        /// <param name="DeleteModuleObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult DeleteModule(DeleteModuleList DeleteModuleObj, string connectionString, int LogException)
        {
            GNM_Module gModule = null;
            JTokenReader jr = null;
            string Result = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(DeleteModuleObj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);

                    //gModule = GEClient.GNM_Module.Where(modid => modid.Module_ID == id).First();
                    //GNM_ModuleLocale modLocale = GEClient.GNM_ModuleLocale.Where(a => a.Module_ID == id).FirstOrDefault();
                    //if (modLocale != null)
                    //{
                    //    GEClient.GNM_ModuleLocale.Remove(modLocale);
                    //}
                    //GEClient.GNM_Module.Remove(gModule);
                    try
                    {
                        using (SqlConnection conn = new SqlConnection(connectionString))
                        {
                            string query = "UP_DELETE_AM_ERP_DeleteModule_CoreModuleMaster";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@Module_ID", id);



                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    command.ExecuteNonQuery();
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }



                    }
                    catch (WebException wex)
                    {
                        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                    }
                }
                //gbl.InsertGPSDetails(Convert.ToInt32(DeleteModuleObj.Company_ID), Convert.ToInt32(DeleteModuleObj.Branch), Convert.ToInt32(DeleteModuleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteModuleObj.MenuID));
                //GEClient.SaveChanges();
                Result = "Deleted successfully";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                Result = "Dependency found cannot delete the record";
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    //Result = Tr_Resource.Dependencyfoundcannotdeletetherecords;                    
                }
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //  return RedirectToAction("Error");
            }
            return new JsonResult(Result);
        }
        #endregion
        #region ::: To get Module name in Local language :::
        /// <summary>
        /// To get Module name in Local language
        /// </summary>
        /// <param name="getModuleLocaleObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static JsonResult getModuleLocale(getModuleLocaleList getModuleLocaleObj, string connectionString, int LogException)
        {
            GNM_ModuleLocale gml = null;
            dynamic dummy = null;
            var jsonResult = dummy;
            string modulename = "";
            int moduleid = 0;
            string moduleName = "";
            int moduleLocaleId = 0;
            try
            {

                int langid = getModuleLocaleObj.Language_ID;
                //gml = GEClient.GNM_ModuleLocale.Where(mid => mid.Module_ID == moduleID && mid.Language_ID == langid).FirstOrDefault();
                try
                {
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        string query = "UP_GET_AM_ERP_getModuleLocale_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Module_ID", getModuleLocaleObj.moduleID);
                                command.Parameters.AddWithValue("@Language_ID", langid);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        moduleName = reader["Module_Description"].ToString();
                                        moduleLocaleId = Convert.ToInt32(reader["ModuleLocale_ID"]);
                                    }





                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }

                //if (gml != null)
                //{
                //    modulename = gml.Module_Description;
                //    moduleid = gml.ModuleLocale_ID;
                //}
                jsonResult = new
                {
                    moduledesc = moduleName,
                    id = moduleLocaleId
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: To save module name in local language :::
        /// <summary>
        /// To save module name in local language
        /// </summary>
        /// <param name="ModuleLocaleSaveObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult ModuleLocaleSave(ModuleLocaleSaveList ModuleLocaleSaveObj, string connectionString, int LogException)
        {
            GNM_ModuleLocale gmodl = new GNM_ModuleLocale();
            string result = "";
            try
            {

                int langid = ModuleLocaleSaveObj.Language_ID;
                //if (modlocid > 0)
                //{
                //    gmodl = GEClient.GNM_ModuleLocale.Where(mid => mid.ModuleLocale_ID == modlocid).FirstOrDefault();
                //    gmodl.Language_ID = langid;
                //    gmodl.Module_Description =Common.DecryptString(modulename);
                //    gmodl.Module_ID = moduleid;
                //    GEClient.SaveChanges();
                //    gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), gmodl.Module_ID, 0, 0, "Update", false, Convert.ToInt32(Session["MenuID"]));
                //}
                //else
                //{
                //    gmodl.Language_ID = langid;
                //    gmodl.Module_Description =Common.DecryptString(modulename);
                //    gmodl.Module_ID = moduleid;
                //    GEClient.GNM_ModuleLocale.Add(gmodl);
                //    GEClient.SaveChanges();
                //    gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), gmodl.Module_ID, 0, 0, "Update", false, Convert.ToInt32(Session["MenuID"]));
                //}
                try
                {
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        string query = "UP_Save_AM_ERP_ModuleLocaleSave_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ModuleLocale_ID", ModuleLocaleSaveObj.modlocid);
                                command.Parameters.AddWithValue("@Language_ID", langid);
                                command.Parameters.AddWithValue("@Module_Description", Common.DecryptString(ModuleLocaleSaveObj.modulename));
                                command.Parameters.AddWithValue("@Module_ID", ModuleLocaleSaveObj.moduleid);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();
                                //gbl.InsertGPSDetails(Convert.ToInt32(ModuleLocaleSaveObj.Company_ID), Convert.ToInt32(ModuleLocaleSaveObj.Branch), Convert.ToInt32(ModuleLocaleSaveObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModuleMaster")), gmodl.Module_ID, 0, 0, "Update", false, Convert.ToInt32(ModuleLocaleSaveObj.MenuID));
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }


                result = CommonFunctionalities.GetResourceString(ModuleLocaleSaveObj.UserCulture.ToString(), "SavedSuccessfully").ToString();//"Successfully inserted";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


                result = CommonFunctionalities.GetResourceString(ModuleLocaleSaveObj.UserCulture.ToString(), "Error").ToString();//"Error occured while saving";
                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                result = CommonFunctionalities.GetResourceString(ModuleLocaleSaveObj.UserCulture.ToString(), "Error").ToString();//"Error occured while saving";
                //  return RedirectToAction("Error");
            }

            return new JsonResult(result);
        }
        #endregion
        #region ::: To get menu name in Local language :::
        /// <summary>
        /// To get menu name in Local language 
        /// </summary>
        /// <param name="getMenuLocaleNameObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult getMenuLocaleName(getMenuLocaleNameList getMenuLocaleNameObj, string connectionString, int LogException)
        {
            dynamic dummy = null;
            var jsonResult = dummy;
            GNM_MenuLocale gml = null;
            string Menuname = "";
            int menulocid = 0;
            try
            {

                int langid = getMenuLocaleNameObj.Language_ID;
                //gml= GEClient.GNM_MenuLocale.Where(mid => mid.Menu_ID == menuid && mid.Language_ID == langid).FirstOrDefault();
                try
                {
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        string query = "UP_GET_AM_ERP_getMenuLocaleName_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Menu_ID", getMenuLocaleNameObj.menuid);
                                command.Parameters.AddWithValue("@Language_ID", langid);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        Menuname = reader["Menu_Description"].ToString();
                                        menulocid = Convert.ToInt32(reader["MenuLocale_ID"]);
                                    }





                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }

                //if (gml != null)
                //{
                //    Menuname = gml.Menu_Description;
                //    menulocid = gml.MenuLocale_ID;
                //}

                jsonResult = new
                {
                    menudesc = Menuname,
                    id = menulocid
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: To save menu name in local language :::
        /// <summary>
        /// To save menu name in local language
        /// </summary>
        /// <param name="MenuLocaleSaveObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult MenuLocaleSave(MenuLocaleSaveList MenuLocaleSaveObj, string connectionString, int LogException)
        {
            GNM_MenuLocale gmodl = new GNM_MenuLocale();
            string result = "";
            try
            {

                int langid = MenuLocaleSaveObj.Language_ID;
                //if (menulocalid > 0)
                //{
                //    gmodl = GEClient.GNM_MenuLocale.Where(mid => mid.MenuLocale_ID == menulocalid).FirstOrDefault();
                //    gmodl.Language_ID = langid;
                //    gmodl.Menu_Description = Common.DecryptString(menuname);
                //    gmodl.Menu_ID = menuid;
                //}
                //else
                //{
                //    gmodl.Language_ID = langid;
                //    gmodl.Menu_Description = Common.DecryptString(menuname);
                //    gmodl.Menu_ID = menuid;
                //    GEClient.GNM_MenuLocale.Add(gmodl);
                //}
                //GEClient.SaveChanges();
                try
                {
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        string query = "UP_SAVE_AM_ERP_MenuLocaleSave_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@MenuLocale_ID", MenuLocaleSaveObj.menulocalid);
                                command.Parameters.AddWithValue("@Language_ID", langid);
                                command.Parameters.AddWithValue("@Menu_Description", Common.DecryptString(MenuLocaleSaveObj.menuname));
                                command.Parameters.AddWithValue("@Menu_ID", MenuLocaleSaveObj.menuid);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }
                result = CommonFunctionalities.GetResourceString(MenuLocaleSaveObj.UserCulture.ToString(), "SavedSuccessfully").ToString();//"Successfully inserted";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                result = CommonFunctionalities.GetResourceString(MenuLocaleSaveObj.UserCulture.ToString(), "Error").ToString();//"Error occured while saving";
                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                result = CommonFunctionalities.GetResourceString(MenuLocaleSaveObj.UserCulture.ToString(), "Error").ToString();//"Error occured while saving";
                //  return RedirectToAction("Error");
            }

            return new JsonResult(result);

        }
        #endregion

        #region ::: To Check for duplicate Roles :::
        /// <summary>
        /// To Check for duplicate Roles
        /// </summary>
        /// <param name="ChkDuplicateModulesObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult ChkDuplicateModules(ChkDuplicateModulesList ChkDuplicateModulesObj, string connectionString, int LogException)
        {
            bool isDuplicate = false;
            string isDuplicateStr = "";

            try
            {

                //isDuplicate = (GEClient.GNM_Module.Where(m => m.Module_Description == ModuleName).Count() > 0) ? true : false;
                try
                {
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        string query = "UP_CHK_AM_ERP_ChkDuplicateModules_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Module_Description", ChkDuplicateModulesObj.ModuleName);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        isDuplicate = (bool)reader["isDuplicate"];

                                    }
                                    if (isDuplicate == true)
                                    {
                                        isDuplicateStr = "True";
                                    }
                                    else
                                    {
                                        isDuplicateStr = "False";
                                    }





                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                isDuplicate = false;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                isDuplicate = false;
                // return RedirectToAction("Error");
            }
            return new JsonResult(isDuplicateStr);
        }

        #endregion
        #region CheckModuleLocaleExists
        /// <summary>
        /// CheckModuleLocaleExists
        /// </summary>
        /// <param name="CheckModuleLocaleExistsObj"></param>
        /// <param name="connectionString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckModuleLocaleExists(CheckModuleLocaleExistsList CheckModuleLocaleExistsObj, string connectionString, int LogException)
        {

            int Language_ID = Convert.ToInt32(CheckModuleLocaleExistsObj.LanguageID);
            bool isDuplicate = true;
            string isDuplicateStr = "";
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = "UP_CHK_AM_ERP_CheckModuleLocaleExists_CoreModuleMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@ModuleLocale_ID", CheckModuleLocaleExistsObj.mlid);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);
                            command.Parameters.AddWithValue("@Module_Description", CheckModuleLocaleExistsObj.mname);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    isDuplicate = (bool)reader["isDuplicate"];

                                }
                                if (isDuplicate)
                                {
                                    isDuplicateStr = "true";
                                }
                                else
                                {
                                    isDuplicateStr = "false";
                                }






                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }



            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return new JsonResult(isDuplicateStr.ToString());

            //return  GEClient.GNM_ModuleLocale.Where(mid => mid.ModuleLocale_ID != mlid  && mid.Language_ID == Language_ID && mid.Module_Description == mname).Count()>0?"true":"false";
        }
        #endregion
        public static List<MenuObjects> SelectMenuList(string connString, SelectMenuList SelectMenuObj, string sidx, string sord, int LogException)
        {
            dynamic dummy = null;
            var jsonobj = dummy;
            int count = 0;
            int total = 0;

            IQueryable<MenuObjects> iQMenuList = null;
            IEnumerable<MenuObjects> MenuList = null;
            List<MenuObjects> menuList = new List<MenuObjects>();
            List<GNM_Menu> gMenuList = new List<GNM_Menu>();
            List<GNM_Object> gobjList = new List<GNM_Object>();

            try
            {
                string GenLangCode = SelectMenuObj.GeneralLanguageCode.ToString();
                string UserLangCode = SelectMenuObj.UserLanguageCode.ToString();
                try
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_SELECT_AM_ERP_SelectMenu_CoreModuleMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Module_ID", SelectMenuObj.moduleid);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var sortOrderValue = reader.IsDBNull(reader.GetOrdinal("Sort_order")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Sort_order"));
                                        var data = new MenuObjects
                                        {
                                            Menu_ID = reader.GetInt32(reader.GetOrdinal("Menu_ID")),
                                            Menu_Description = reader.IsDBNull(reader.GetOrdinal("Menu_Description")) ? null : reader.GetString(reader.GetOrdinal("Menu_Description")),
                                            ParentMenu = reader.IsDBNull(reader.GetOrdinal("ParentMenu")) ? null : reader.GetString(reader.GetOrdinal("ParentMenu")),
                                            Object = reader.IsDBNull(reader.GetOrdinal("Object")) ? null : reader.GetString(reader.GetOrdinal("Object")),
                                            Menu_path = reader.IsDBNull(reader.GetOrdinal("Menu_Path")) ? null : reader.GetString(reader.GetOrdinal("Menu_Path")),
                                            Sort_order = sortOrderValue.HasValue ? sortOrderValue.Value : 0,
                                            Menu_active = reader.IsDBNull(reader.GetOrdinal("Menu_active")) ? null : reader.GetString(reader.GetOrdinal("Menu_active"))
                                        };
                                        menuList.Add(data);
                                    }
                                    if (reader.NextResult())
                                    {
                                        while (reader.Read())
                                        {
                                            var menu = new GNM_Menu
                                            {
                                                Menu_ID = reader.GetInt32(reader.GetOrdinal("Menu_ID")),
                                                Menu_Description = reader.IsDBNull(reader.GetOrdinal("Menu_Description")) ? null : reader.GetString(reader.GetOrdinal("Menu_Description")),
                                                Parentmenu_ID = reader.IsDBNull(reader.GetOrdinal("Parentmenu_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Parentmenu_ID")),
                                                Object_ID = reader.IsDBNull(reader.GetOrdinal("Object_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Object_ID")),
                                                Menu_Path = reader.IsDBNull(reader.GetOrdinal("Menu_Path")) ? null : reader.GetString(reader.GetOrdinal("Menu_Path")),
                                                Menu_SortOrder = reader.IsDBNull(reader.GetOrdinal("Menu_SortOrder")) ? (byte)0 : (byte)reader.GetInt32(reader.GetOrdinal("Menu_SortOrder")),
                                                Menu_IsActive = reader.GetBoolean(reader.GetOrdinal("Menu_IsActive")),
                                                Module_ID = reader.IsDBNull(reader.GetOrdinal("Module_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Module_ID"))
                                            };

                                            gMenuList.Add(menu);
                                        }
                                    }
                                    if (reader.NextResult())
                                    {
                                        while (reader.Read())
                                        {
                                            var obj = new GNM_Object
                                            {
                                                Object_ID = reader.GetInt32(reader.GetOrdinal("Object_ID")),
                                                Object_Description = reader.IsDBNull(reader.GetOrdinal("Object_Description")) ? null : reader.GetString(reader.GetOrdinal("Object_Description")),
                                                Object_IsActive = reader.GetBoolean(reader.GetOrdinal("Object_IsActive"))
                                            };

                                            gobjList.Add(obj);
                                        }
                                    }


                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }







                //MenuList = from MenuDetails in gMenuNew
                //           join obj in GEClient.GNM_Object on MenuDetails.Object_ID equals obj.Object_ID into objectlist
                //           from objdetails in objectlist.DefaultIfEmpty(new GNM_Object { Object_Description = "" })
                //           join parentmenu in gMenuNew on MenuDetails.Parentmenu_ID equals parentmenu.Menu_ID into MenuNames
                //           from Mdetails in MenuNames.DefaultIfEmpty(new GNM_Menu { Menu_Description = "" })
                //           select new MenuObjects()
                //           {
                //               Menu_ID = MenuDetails.Menu_ID,
                //               Menu_Description = MenuDetails.Menu_Description,
                //               ParentMenu = Mdetails.Menu_Description,
                //               Object = objdetails.Object_Description,
                //               Menu_path = MenuDetails.Menu_Path,
                //               Sort_order = MenuDetails.Menu_SortOrder,
                //               Menu_active = (MenuDetails.Menu_IsActive == true ? "Yes" : "No"),
                //           };




                //Sorting 
                //iQMenuList = iQMenuList.OrderByField<MenuObjects>(sidx, sord);

                //count = iQMenuList.Count();

                iQMenuList = menuList.AsQueryable<MenuObjects>();
                //FilterToolBar Search


                //Sorting 
                iQMenuList = iQMenuList.OrderByField<MenuObjects>(sidx, sord);



                //Session["Export"] = iQMenuList.ToList();





                //Session["Export"] = iQMenuList.ToList();




            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                //  return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }                   // 
                }
                //  return RedirectToAction("Error");
            }

            return iQMenuList.ToList();
        }
        #region ::: To Export :::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        public static async Task<object> Export(SelectMenuList ExportObj, string connString, int LogException, string filter, string advanceFilter, string sidx, string sord)
        {
            DataTable Dt = new DataTable();
            string Module_Description = "";
            try
            {
                //GNM_Module Module = GEClient.GNM_Module.Where(mod => mod.Module_ID == ModuleID).FirstOrDefault();
                try
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "SELECT TOP 1 * FROM GNM_Module WHERE Module_ID = @ModuleID";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.Text;
                                command.Parameters.AddWithValue("@ModuleID", ExportObj.moduleid);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {



                                        Module_Description = reader.IsDBNull(reader.GetOrdinal("Module_Description")) ? null : reader.GetString(reader.GetOrdinal("Module_Description"));



                                    }



                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                return null;
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }



                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                    return null;

                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        return null;
                    }
                }
                List<MenuObjects> gMenuNew = (List<MenuObjects>)SelectMenuList(connString, ExportObj, sidx, sord, LogException);
                IQueryable<MenuObjects> queryableMenuNew = gMenuNew.AsQueryable();

                if (filter != "null")
                {
                    Filters filters = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                    if (filters.rules.Count > 0)
                        queryableMenuNew = queryableMenuNew.FilterSearch<MenuObjects>(filters);
                }

                if (advanceFilter != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advanceFilter)).ToObject<AdvanceFilter>();
                    queryableMenuNew = queryableMenuNew.AdvanceSearch<MenuObjects>(advnfilter);
                }
                gMenuNew = queryableMenuNew.ToList();
                //List<MenuObjects> gMenuNew = (List<MenuObjects>)Session["Export"];
                int cnt = gMenuNew.Count();

                Dt.Columns.Add("Menu Name");
                Dt.Columns.Add("Parent Menu");
                Dt.Columns.Add("Object Name");
                Dt.Columns.Add("Menu Path");
                Dt.Columns.Add("Sort Order");
                Dt.Columns.Add("Is Active?");


                DataTable Dt2 = new DataTable();
                Dt2.Columns.Add("Menu Name");
                Dt2.Columns.Add("Parent Menu");
                Dt2.Columns.Add("Object Name");
                Dt2.Columns.Add("Menu Path");
                Dt2.Columns.Add("Sort Order");
                Dt2.Columns.Add("Is Active?");
                Dt2.Rows.Add(0, 0, 0, 0, 2, 1);

                if (cnt > 0)
                {
                    for (int i = 0; i < cnt; i++)
                    {
                        Dt.Rows.Add(gMenuNew.ElementAt(i).Menu_Description, gMenuNew.ElementAt(i).ParentMenu, gMenuNew.ElementAt(i).Object, gMenuNew.ElementAt(i).Menu_path, gMenuNew.ElementAt(i).Sort_order, gMenuNew.ElementAt(i).Menu_active);
                    }

                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add("Module");
                    Dt1.Rows.Add(Module_Description);
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = Dt2,
                        FileName = "Module", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Module").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };


                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return null;

                //RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    return null;
                }
                //  RedirectToAction("Error");
            }
            return null;
        }
        #endregion
    }
    #region ModuleListsAndClasses
    public class SelectMenuList
    {
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public int moduleid { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int LanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserCulture { get; set; }
        public int exprtType { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public string filter { get; set; }
        public string advanceFilter { get; set; }
    }
    public class InsertList
    {
        public string Data { get; set; }
        public string GeneralLanguageCode { set; get; }
        public string UserLanguageCode { set; get; }
        public string Company_ID { get; set; }
        public string Branch { get; set; }
        public string User_ID { get; set; }
        public int MenuID { get; set; }
    }
    public class SelectModuleList
    {
        public bool IsDelete { get; set; }
    }
    public class EditList
    {
        public string Data { set; get; }
        public int Company_ID { set; get; }
        public int Branch { get; set; }
        public int User_ID { set; get; }
        public int MenuID { set; get; }

    }
    public class DeleteMenuList
    {
        public string key { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { set; get; }
        public int MenuID { set; get; }
        public string UserCulture { set; get; }
    }
    public class DeleteModuleList
    {
        public string key { set; get; }
        public int Company_ID { set; get; }
        public int Branch { set; get; }
        public int User_ID { set; get; }
        public int MenuID { set; get; }
    }
    public class getModuleLocaleList
    {
        public int Language_ID { set; get; }
        public int moduleID { set; get; }
    }
    public class ModuleLocaleSaveList
    {
        public int Language_ID { get; set; }
        public int modlocid { set; get; }
        public string modulename { set; get; }
        public int moduleid { set; get; }
        public int Company_ID { set; get; }
        public int Branch { set; get; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public string UserCulture { get; set; }

    }
    public class getMenuLocaleNameList
    {
        public int Language_ID { get; set; }
        public int menuid { set; get; }
    }
    public class MenuLocaleSaveList
    {
        public int Language_ID { set; get; }
        public int menulocalid { set; get; }
        public string menuname { set; get; }
        public int menuid { set; get; }
        public string UserCulture { set; get; }
    }
    public class ChkDuplicateModulesList
    {
        public string ModuleName { set; get; }
    }
    public class CheckModuleLocaleExistsList
    {
        public int LanguageID { set; get; }
        public int mlid { set; get; }
        public string mname { set; get; }

    }

    #endregion

    #region :::CoreModuleClasses :::
    /// <summary>
    /// CoreModuleClasses
    /// </summary>
    /// 
    public class MenuObjects
    {
        public int Menu_ID { get; set; }
        public string Menu_Description { get; set; }
        public string ParentMenu { get; set; }
        public string Object { get; set; }
        public string Menu_path { get; set; }
        public int Sort_order { get; set; }
        public string Menu_active { get; set; }
    }
    public partial class GNM_Menu
    {
        public GNM_Menu()
        {
            this.GNM_MenuLocale = new HashSet<GNM_MenuLocale>();
        }

        public int Menu_ID { get; set; }
        public int Module_ID { get; set; }
        public string Menu_Description { get; set; }
        public Nullable<int> Parentmenu_ID { get; set; }
        public Nullable<int> Object_ID { get; set; }
        public string Menu_Path { get; set; }
        public byte Menu_SortOrder { get; set; }
        public bool Menu_IsActive { get; set; }
        public string Menu_IconName { get; set; }

        public virtual GNM_Module GNM_Module { get; set; }
        public virtual ICollection<GNM_MenuLocale> GNM_MenuLocale { get; set; }
    }
    public partial class GNM_MenuLocale
    {
        public int MenuLocale_ID { get; set; }
        public int Language_ID { get; set; }
        public int Menu_ID { get; set; }
        public string Menu_Description { get; set; }

        public virtual GNM_Menu GNM_Menu { get; set; }
    }
    public partial class GNM_Module
    {
        public GNM_Module()
        {
            this.GNM_Menu = new HashSet<GNM_Menu>();
            this.GNM_ModuleLocale = new HashSet<GNM_ModuleLocale>();
        }

        public int Module_ID { get; set; }
        public string Module_Description { get; set; }
        public bool Module_IsActive { get; set; }
        public byte Module_SortOrder { get; set; }
        public string Module_IconName { get; set; }

        public virtual ICollection<GNM_Menu> GNM_Menu { get; set; }
        public virtual ICollection<GNM_ModuleLocale> GNM_ModuleLocale { get; set; }
    }
    public partial class GNM_ModuleLocale
    {
        public int ModuleLocale_ID { get; set; }
        public int Language_ID { get; set; }
        public int Module_ID { get; set; }
        public string Module_Description { get; set; }
        public string Module_IconName { get; set; }

        public virtual GNM_Module GNM_Module { get; set; }
    }
    #endregion

}
