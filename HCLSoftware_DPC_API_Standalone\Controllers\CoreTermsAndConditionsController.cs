﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreTermsAndConditionsServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreTermsAndConditionsController : ApiController
    {
        #region ::: Save /Mithun:::
        /// <summary>
        /// To Save
        /// </summary>
        [Route("api/CoreTermsAndConditions/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveTermsConditionList SaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.Save(SaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Update /Mithun:::
        /// <summary>
        /// To Update
        /// </summary>    
        [Route("api/CoreTermsAndConditions/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateTermsConditionList UpdateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.Update(UpdateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::UpdateLocale /Mithun:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        [Route("api/CoreTermsAndConditions/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleTermsConditionList UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectTermsConditionsGrid /Mithun:::
        /// <summary>
        /// SelectTermsConditionsGrid
        /// </summary>
        [Route("api/CoreTermsAndConditions/SelectTermsConditionsGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectTermsConditionsGrid([FromBody] SelectTermsConditionsGridList SelectTermsConditionsGridObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreTermsAndConditionsServices.SelectTermsConditionsGrid(SelectTermsConditionsGridObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: LoadObjectDropdown /Mithun:::
        /// <summary>
        /// to Load Object Dropdowns
        /// </summary>
        [Route("api/CoreTermsAndConditions/LoadObjectDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadObjectDropdown([FromBody] LoadObjectDropdownList LoadObjectDropdownObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.LoadObjectDropdown(LoadObjectDropdownObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelTaxForPopUp /Mithun:::
        /// <summary>
        /// SelTaxForPopUp
        /// </summary>
        [Route("api/CoreTermsAndConditions/LoadTermsDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadTermsDropdown([FromBody] LoadTermsDropdownList LoadTermsDropdownObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.LoadTermsDropdown(LoadTermsDropdownObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// Delete
        /// </summary>
        [Route("api/CoreTermsAndConditions/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteTermsConditionList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: GetTermsConditions /Mithun:::
        /// <summary>
        /// GetTermsConditions
        /// </summary>
        [Route("api/CoreTermsAndConditions/GetTermsConditions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTermsConditions([FromBody] GetTermsConditionsList GetTermsConditionsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.GetTermsConditions(GetTermsConditionsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: GetTermsConditionsLocale /Mithun:::
        /// <summary>
        /// GetTermsConditionsLocale
        /// </summary>
        [Route("api/CoreTermsAndConditions/GetTermsConditionsLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTermsConditionsLocale([FromBody] GetTermsConditionsLocaleList GetTermsConditionsLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTermsAndConditionsServices.GetTermsConditionsLocale(GetTermsConditionsLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: Export /Mithun :::

        [Route("api/CoreTermsAndConditions/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectTermsConditionsGridList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            int page = ExportObj.page;
            int rows = ExportObj.rows;
            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            //string Query = ExportObj.Query;
            try
            {


                Object Response = await CoreTermsAndConditionsServices.Export(ExportObj, connstring, LogException, filters, sidx, sord, page, rows);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion


    }
}
