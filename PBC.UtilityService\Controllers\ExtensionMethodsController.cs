using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for ExtensionMethods operations
    /// Provides HTTP endpoints for LINQ operations, filtering, and data manipulation
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class ExtensionMethodsController : ControllerBase
    {
        private readonly IExtensionMethodsService _extensionMethodsService;
        private readonly ILogger<ExtensionMethodsController> _logger;

        public ExtensionMethodsController(
            IExtensionMethodsService extensionMethodsService,
            ILogger<ExtensionMethodsController> logger)
        {
            _extensionMethodsService = extensionMethodsService;
            _logger = logger;
        }

        /// <summary>
        /// Orders data by a specified field and sort direction
        /// </summary>
        /// <param name="request">The order by field request</param>
        /// <returns>Ordered data</returns>
        /// <response code="200">Returns the ordered data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("order-by-field")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<object[]>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<object[]>>> OrderByField([FromBody] OrderByFieldRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/extensionmethods/order-by-field - Ordering by field: {SortField}", request.SortField);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _extensionMethodsService.OrderByFieldAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ordering data by field: {SortField}", request.SortField);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while ordering data");
            }
        }

        /// <summary>
        /// Filters data using simple filter rules
        /// </summary>
        /// <param name="request">The filter search request</param>
        /// <returns>Filtered data</returns>
        /// <response code="200">Returns the filtered data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("filter-search")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<object[]>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<object[]>>> FilterSearch([FromBody] FilterSearchRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/extensionmethods/filter-search - Filtering with {RuleCount} rules", 
                    request.Filters.rules.Count);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _extensionMethodsService.FilterSearchAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error filtering data with simple filters");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while filtering data");
            }
        }

        /// <summary>
        /// Filters data using advanced filter rules with operators and conditions
        /// </summary>
        /// <param name="request">The advance search request</param>
        /// <returns>Filtered data</returns>
        /// <response code="200">Returns the filtered data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("advance-search")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<object[]>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<object[]>>> AdvanceSearch([FromBody] AdvanceSearchRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/extensionmethods/advance-search - Advanced filtering with {RuleCount} rules", 
                    request.AdvanceFilter.Rules.Count);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _extensionMethodsService.AdvanceSearchAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error filtering data with advanced filters");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while filtering data");
            }
        }

        /// <summary>
        /// Paginates data by returning a specific page of results
        /// </summary>
        /// <param name="request">The paginate request</param>
        /// <returns>Paginated data</returns>
        /// <response code="200">Returns the paginated data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("paginate")]
        [ProducesResponseType(typeof(PaginationResponse<object>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PaginationResponse<object>>> Paginate([FromBody] PaginateRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/extensionmethods/paginate - Page: {Page}, Rows: {Rows}", 
                    request.Page, request.Rows);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _extensionMethodsService.PaginateAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error paginating data");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while paginating data");
            }
        }

        /// <summary>
        /// Converts an object to a specified type with custom conversion logic
        /// </summary>
        /// <param name="request">The convert to type request</param>
        /// <returns>Converted object</returns>
        /// <response code="200">Returns the converted object</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("convert-to-type")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<object>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<object>>> ConvertToType([FromBody] ConvertToTypeRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/extensionmethods/convert-to-type - Target type: {TargetType}", 
                    request.TargetType);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _extensionMethodsService.ConvertToTypeAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting object to type: {TargetType}", request.TargetType);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while converting object");
            }
        }

        /// <summary>
        /// Decrypts and unescapes a URL-encoded string
        /// </summary>
        /// <param name="request">The decrypt string request</param>
        /// <returns>Decrypted string</returns>
        /// <response code="200">Returns the decrypted string</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("decrypt-string")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<string>>> DecryptString([FromBody] DecryptStringRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/extensionmethods/decrypt-string");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _extensionMethodsService.DecryptStringAsync(request);
                
                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting string");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while decrypting string");
            }
        }

        /// <summary>
        /// Performs a case-insensitive "like" comparison for strings
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if field contains value</returns>
        /// <response code="200">Returns the comparison result</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("like")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<bool>>> Like([FromQuery] string field, [FromQuery] string value)
        {
            try
            {
                _logger.LogInformation("GET /api/extensionmethods/like");

                var result = await _extensionMethodsService.LikeAsync(field, value);

                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Like comparison");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred during Like comparison");
            }
        }

        /// <summary>
        /// Performs a "like" comparison for integers by converting to string
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if field contains value</returns>
        /// <response code="200">Returns the comparison result</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("like-integer")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<bool>>> LikeInteger([FromQuery] int field, [FromQuery] int value)
        {
            try
            {
                _logger.LogInformation("GET /api/extensionmethods/like-integer");

                var result = await _extensionMethodsService.LikeIntegerAsync(field, value);

                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeInteger comparison");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred during LikeInteger comparison");
            }
        }

        /// <summary>
        /// Performs a "like" comparison for decimals by converting to string
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if field contains value</returns>
        /// <response code="200">Returns the comparison result</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("like-decimal")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<bool>>> LikeDecimal([FromQuery] decimal field, [FromQuery] decimal value)
        {
            try
            {
                _logger.LogInformation("GET /api/extensionmethods/like-decimal");

                var result = await _extensionMethodsService.LikeDecimalAsync(field, value);

                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeDecimal comparison");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred during LikeDecimal comparison");
            }
        }

        /// <summary>
        /// Performs a "like" comparison for DateTime values by formatting and comparing strings
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to search for</param>
        /// <returns>True if formatted date contains value</returns>
        /// <response code="200">Returns the comparison result</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("like-date")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<bool>>> LikeDate([FromQuery] DateTime field, [FromQuery] string value)
        {
            try
            {
                _logger.LogInformation("GET /api/extensionmethods/like-date");

                var result = await _extensionMethodsService.LikeDateAsync(field, value);

                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeDate comparison");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred during LikeDate comparison");
            }
        }

        /// <summary>
        /// Performs equality comparison for nullable boolean values
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to compare</param>
        /// <returns>True if values are equal</returns>
        /// <response code="200">Returns the comparison result</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("like-nullable-bool")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<bool>>> LikeNullableBool([FromQuery] bool? field, [FromQuery] bool value)
        {
            try
            {
                _logger.LogInformation("GET /api/extensionmethods/like-nullable-bool");

                var result = await _extensionMethodsService.LikeNullableBoolAsync(field, value);

                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeNullableBool comparison");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred during LikeNullableBool comparison");
            }
        }

        /// <summary>
        /// Performs equality comparison for boolean values
        /// </summary>
        /// <param name="field">The field value</param>
        /// <param name="value">The value to compare</param>
        /// <returns>True if values are equal</returns>
        /// <response code="200">Returns the comparison result</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("like-bool")]
        [ProducesResponseType(typeof(ExtensionMethodsResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ExtensionMethodsResponse<bool>>> LikeBool([FromQuery] bool field, [FromQuery] bool value)
        {
            try
            {
                _logger.LogInformation("GET /api/extensionmethods/like-bool");

                var result = await _extensionMethodsService.LikeBoolAsync(field, value);

                if (!result.Success)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LikeBool comparison");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred during LikeBool comparison");
            }
        }
    }
}
