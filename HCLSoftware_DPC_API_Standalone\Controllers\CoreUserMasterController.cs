﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreUserMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreUserMasterController : ApiController
    {
        #region ::: To select User Role details. / Mithun:::
        /// <summary>
        /// To select user details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/SelectUserDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUserDetail([FromBody] SelectUserDetailList SelectUserDetailObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreUserMasterServices.SelectUserDetail(SelectUserDetailObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region::: To insert User details /Mithun:::
        /// <summary>
        /// To insert User details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertUserMasterList InsertObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.Insert(InsertObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: To select all user /Mithun:::
        /// <summary>
        /// To select all user names
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/SelectUser")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUser([FromBody] SelectUserList SelectUserObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreUserMasterServices.SelectUser(SelectUserObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region::: To edit user details  /Mithun:::
        /// <summary>
        /// To edit user details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/Edit")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Edit([FromBody] EditUserMasterList EditObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.Edit(EditObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: To Save User Role details  /Mithun:::
        /// <summary>
        /// To edit user details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/SaveUserRole")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveUserRole([FromBody] SaveUserRoleList SaveUserRoleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.SaveUserRole(SaveUserRoleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: To Save User Company details  /Mihtun:::
        /// <summary>
        /// To edit user details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/SaveUserCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveUserCompany([FromBody] SaveUserCompanyList SaveUserCompanyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.SaveUserCompany(SaveUserCompanyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: To delete User Role  /Mithun:::
        /// <summary>
        /// To delete user roles
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/DeleteUserRole")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteUserRole([FromBody] DeleteUserRoleList DeleteUserRoleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.DeleteUserRole(DeleteUserRoleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: To delete User Company  /Mithun:::
        /// <summary>
        /// To delete user Company
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/DeleteUserCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteUserCompany([FromBody] DeleteUserCompanyList DeleteUserCompanyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.DeleteUserCompany(DeleteUserCompanyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: To delete user  /Mithun:::
        /// <summary>
        ///To delete user
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/DeleteUser")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteUser([FromBody] DeleteUserList DeleteUserObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.DeleteUser(DeleteUserObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region:::  To validate selected roles  /Mithun:::
        /// <summary>
        ///To validate selected roles 
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/ValidateUserRole")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateUserRole([FromBody] ValidateUserRoleList ValidateUserRoleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.ValidateUserRole(ValidateUserRoleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region:::  To validate selected Company /Mithun:::
        /// <summary>
        ///To validate selected roles 
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/validateUserCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult validateUserCompany([FromBody] validateUserCompanyList validateUserCompanyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.validateUserCompany(validateUserCompanyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region:::  To get UserData  /Mithun:::
        /// <summary>
        /// To get UserData 
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/UserData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UserData([FromBody] UserDataList UserDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.UserData(UserDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: To select all Employee /Mithun:::
        /// <summary>
        /// To select all Employee
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/EmployeeData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult EmployeeData([FromBody] EmployeeDataList EmployeeDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.EmployeeData(EmployeeDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::To select all partner /Mithun:::
        /// <summary>
        /// To select all partner
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/PartnerData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartnerData([FromBody] PartnerDataList PartnerDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.PartnerData(PartnerDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: To select all user names:::
        /// <summary>
        /// To select all user names
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/SelectUserforEdit")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUserforEdit([FromBody] SelectUserforEditList SelectUserforEditObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.SelectUserforEdit(SelectUserforEditObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: To Check for duplicate Roles /Mithun:::
        /// <summary>
        /// To delete Roles
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/ChkDuplicateLogin")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ChkDuplicateLogin([FromBody] ChkDuplicateLoginList ChkDuplicateLoginObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.ChkDuplicateLogin(ChkDuplicateLoginObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: To select User Company details. /Mithun:::
        /// <summary>
        /// To select user details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/SelectUserCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUserCompany([FromBody] SelectUserCompanyList SelectUserCompanyObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreUserMasterServices.SelectUserCompany(SelectUserCompanyObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region:::  To get warehouse data  /Mithun:::
        /// <summary>
        /// To get  warehouse data 
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreUserMaster/WarehouseData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WarehouseData([FromBody] WarehouseDataList WarehouseDataObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.WarehouseData(WarehouseDataObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Particular User Locale /Mithun:::
        /// <summary>
        /// To Select particular user locale when click on view
        /// </summary> 
        [Route("api/CoreUserMaster/SelectParticularUserMasterLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularUserMasterLocale([FromBody] SelectParticularUserMasterLocaleList SelectParticularUserMasterLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.SelectParticularUserMasterLocale(SelectParticularUserMasterLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert User Locale /Mithun:::
        /// <summary>
        ///  Method to Insert User Locale
        /// </summary> 
        [Route("api/CoreUserMaster/InsertUserMasterLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertUserMasterLocale([FromBody] InsertUserMasterLocaleList InsertUserMasterLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUserMasterServices.InsertUserMasterLocale(InsertUserMasterLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: Export /Mithun :::

        [Route("api/CoreUserMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectUserList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            try
            {


                object Response = await CoreUserMasterServices.Export(ExportObj, connstring, LogException, filters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion



    }
}
