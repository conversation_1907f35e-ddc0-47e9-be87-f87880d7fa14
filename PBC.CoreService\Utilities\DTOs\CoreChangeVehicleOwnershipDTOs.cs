using System;
using System.Collections.Generic;

namespace PBC.CoreService.Utilities.DTOs
{
    #region Request DTOs

    public class SelectFieldSearchPartyList
    {
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public int LanguageID { get; set; }
        public int Branch { get; set; }
        public int Company_ID { get; set; }
        public string Value { get; set; }
        public int? Type { get; set; }
        public string UserCulture { get; set; }
    }

    public class SelectPartyDetailGridCList
    {
        public string PartyName { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
    }

    public class GetPartyDetailsbyIDaList
    {
        public int PartyID { get; set; }
    }

    public class GetPartyDetailsCoreChangeVehicleOwnershipList
    {
        public string PartyName { get; set; }
    }

    public class GetProductForSerialList
    {
        public string SerialNumber { get; set; }
    }

    public class SelectFieldSearchSerialNumberList
    {
        public string Value { get; set; }
        public int Company_ID { get; set; }
        public int LanguageID { get; set; }
        public string UserCulture { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }

    public class SaveProductCustomerDetailsCoreChangeVehicleOwnershipList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public string CustomerData { get; set; }
    }

    public class SelectCoreChangeVehicleOwnershipList
    {
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public int Company_ID { get; set; }
        public int LanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }

    #endregion

    #region Response DTOs

    public class MyDataClass
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Model { get; set; }
        public string Party_Code { get; set; }
        public string CustomerName { get; set; }
        public string EngineSerialNumber { get; set; }
    }

    public class PartyProductDetails
    {
        public string PRODUCT_UNIQUENO { get; set; }
        public int PRODUCT_ID { get; set; }
        public string SERIALNUMBER { get; set; }
        public int BRAND_ID { get; set; }
        public string BRAND { get; set; }
        public int MODEL_ID { get; set; }
        public string MODEL_NAME { get; set; }
        public int PRODUCTTYPE_ID { get; set; }
        public string PRODUCTTYPE_NAME { get; set; }
        public string PreviousParty_NAME { get; set; }
        public bool IsActive { get; set; }
        public string Party_Code { get; set; }
    }

    public class ChangeVehicleOwnershipLog
    {
        public int ChangeVehicleOwnership_ID { get; set; }
        public int Brand_ID { get; set; }
        public int Product_ID { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public string UpdatedDateStr { get; set; }
        public string Brand { get; set; }
        public string ProductType_Name { get; set; }
        public string Model_Name { get; set; }
        public string Product_SerialNumber { get; set; }
        public int Company_ID { get; set; }
        public string PreviousParty_Code { get; set; }
        public string CurrentParty_Code { get; set; }
        public string PreviousParty_Name { get; set; }
        public string CurrentParty_Name { get; set; }
        public int PreviousParty_ID { get; set; }
        public int CurrentParty_ID { get; set; }
    }

    public class GNM_Company
    {
        public string Company_Name { get; set; }
    }

    public class FieldSearch
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public string MobileNumber { get; set; }
        public string Email { get; set; }
        public string Party_Code { get; set; }
        public string PartyAddress_Address { get; set; }
    }

    #endregion

    #region HTTP Response DTOs (Local to PBC.CoreService)
    /// <summary>
    /// Local DTO for deserializing Extension Methods service responses from PBC.UtilityService
    /// This is a local copy to maintain microservice isolation
    /// </summary>
    /// <typeparam name="T">Type of data returned</typeparam>
    public class ExtensionMethodsResponse<T>
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// The result data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
    #endregion

}
