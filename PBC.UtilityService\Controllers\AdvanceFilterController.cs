using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for AdvanceFilter operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class AdvanceFilterController : ControllerBase
    {
        private readonly IAdvanceFilterService _advanceFilterService;
        private readonly ILogger<AdvanceFilterController> _logger;

        public AdvanceFilterController(
            IAdvanceFilterService advanceFilterService,
            ILogger<AdvanceFilterController> logger)
        {
            _advanceFilterService = advanceFilterService;
            _logger = logger;
        }

        /// <summary>
        /// Gets all advance filters
        /// </summary>
        /// <returns>Collection of advance filters</returns>
        /// <response code="200">Returns the list of advance filters</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<AdvanceFilterDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<AdvanceFilterDto>>> GetAll()
        {
            try
            {
                _logger.LogInformation("GET /api/advancefilter - Getting all advance filters");
                var result = await _advanceFilterService.GetAllAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all advance filters");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving advance filters");
            }
        }

        /// <summary>
        /// Gets advance filter by ID
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>Advance filter if found</returns>
        /// <response code="200">Returns the advance filter</response>
        /// <response code="404">Advance filter not found</response>
        /// <response code="400">Invalid filter ID</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("{id:int}")]
        [ProducesResponseType(typeof(AdvanceFilterDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdvanceFilterDto>> GetById([Range(1, int.MaxValue)] int id)
        {
            try
            {
                _logger.LogInformation("GET /api/advancefilter/{Id} - Getting advance filter", id);
                
                var result = await _advanceFilterService.GetByIdAsync(id);
                if (result == null)
                {
                    _logger.LogWarning("Advance filter not found for ID: {Id}", id);
                    return NotFound($"Advance filter not found for ID: {id}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advance filter for ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving advance filter");
            }
        }

        /// <summary>
        /// Gets advance filters by name (partial match)
        /// </summary>
        /// <param name="name">The filter name to search</param>
        /// <returns>Collection of matching advance filters</returns>
        /// <response code="200">Returns matching advance filters</response>
        /// <response code="400">Invalid name parameter</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("search")]
        [ProducesResponseType(typeof(IEnumerable<AdvanceFilterDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<AdvanceFilterDto>>> GetByName([FromQuery, Required] string name)
        {
            try
            {
                _logger.LogInformation("GET /api/advancefilter/search?name={Name} - Searching advance filters", name);
                
                if (string.IsNullOrWhiteSpace(name))
                {
                    return BadRequest("Name parameter is required and cannot be empty");
                }

                var result = await _advanceFilterService.GetByNameAsync(name);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching advance filters by name: {Name}", name);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while searching advance filters");
            }
        }

        /// <summary>
        /// Gets only active advance filters
        /// </summary>
        /// <returns>Collection of active advance filters</returns>
        /// <response code="200">Returns active advance filters</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("active")]
        [ProducesResponseType(typeof(IEnumerable<AdvanceFilterDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<AdvanceFilterDto>>> GetActiveFilters()
        {
            try
            {
                _logger.LogInformation("GET /api/advancefilter/active - Getting active advance filters");
                var result = await _advanceFilterService.GetActiveFiltersAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active advance filters");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving active advance filters");
            }
        }

        /// <summary>
        /// Creates new advance filter
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Created advance filter</returns>
        /// <response code="201">Advance filter created successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost]
        [ProducesResponseType(typeof(AdvanceFilterDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdvanceFilterDto>> Create([FromBody] CreateAdvanceFilterRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/advancefilter - Creating advance filter with name: {Name}", request.Name);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _advanceFilterService.CreateAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating advance filter with name: {Name}", request.Name);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while creating advance filter");
            }
        }

        /// <summary>
        /// Updates existing advance filter
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <param name="request">Update request</param>
        /// <returns>Updated advance filter</returns>
        /// <response code="200">Advance filter updated successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="404">Advance filter not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPut("{id:int}")]
        [ProducesResponseType(typeof(AdvanceFilterDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdvanceFilterDto>> Update([Range(1, int.MaxValue)] int id, [FromBody] UpdateAdvanceFilterRequest request)
        {
            try
            {
                _logger.LogInformation("PUT /api/advancefilter/{Id} - Updating advance filter", id);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _advanceFilterService.UpdateAsync(id, request);
                if (result == null)
                {
                    return NotFound($"Advance filter not found for ID: {id}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating advance filter for ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating advance filter");
            }
        }

        /// <summary>
        /// Deletes advance filter by ID
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">Advance filter deleted successfully</response>
        /// <response code="400">Invalid filter ID</response>
        /// <response code="404">Advance filter not found</response>
        /// <response code="500">Internal server error</response>
        [HttpDelete("{id:int}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> Delete([Range(1, int.MaxValue)] int id)
        {
            try
            {
                _logger.LogInformation("DELETE /api/advancefilter/{Id} - Deleting advance filter", id);
                
                var result = await _advanceFilterService.DeleteAsync(id);
                if (!result)
                {
                    return NotFound($"Advance filter not found for ID: {id}");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting advance filter for ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting advance filter");
            }
        }

        /// <summary>
        /// Checks if advance filter exists for the given ID
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>Boolean indicating existence</returns>
        /// <response code="200">Returns existence status</response>
        /// <response code="400">Invalid filter ID</response>
        /// <response code="500">Internal server error</response>
        [HttpHead("{id:int}")]
        [HttpGet("{id:int}/exists")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> Exists([Range(1, int.MaxValue)] int id)
        {
            try
            {
                _logger.LogInformation("HEAD/GET /api/advancefilter/{Id}/exists - Checking advance filter existence", id);
                
                var result = await _advanceFilterService.ExistsAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking advance filter existence for ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking advance filter existence");
            }
        }

        /// <summary>
        /// Validates a filter and generates SQL WHERE clause
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>Validation response with SQL generation</returns>
        /// <response code="200">Returns validation result and SQL</response>
        /// <response code="400">Invalid filter ID</response>
        /// <response code="404">Advance filter not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("{id:int}/validate")]
        [ProducesResponseType(typeof(FilterValidationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<FilterValidationResponse>> ValidateAndGenerateSql([Range(1, int.MaxValue)] int id)
        {
            try
            {
                _logger.LogInformation("POST /api/advancefilter/{Id}/validate - Validating and generating SQL", id);
                
                var result = await _advanceFilterService.ValidateAndGenerateSqlAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating advance filter for ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while validating advance filter");
            }
        }

        /// <summary>
        /// Validates filter rules and generates SQL WHERE clause
        /// </summary>
        /// <param name="request">Filter request to validate</param>
        /// <returns>Validation response with SQL generation</returns>
        /// <response code="200">Returns validation result and SQL</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("validate")]
        [ProducesResponseType(typeof(FilterValidationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<FilterValidationResponse>> ValidateAndGenerateSql([FromBody] CreateAdvanceFilterRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/advancefilter/validate - Validating filter request");
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _advanceFilterService.ValidateAndGenerateSqlAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating advance filter request");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while validating advance filter");
            }
        }

        /// <summary>
        /// Duplicates an existing filter with a new name
        /// </summary>
        /// <param name="id">The filter ID to duplicate</param>
        /// <param name="newName">New name for the duplicated filter</param>
        /// <returns>Duplicated advance filter</returns>
        /// <response code="201">Filter duplicated successfully</response>
        /// <response code="400">Invalid parameters</response>
        /// <response code="404">Original filter not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("{id:int}/duplicate")]
        [ProducesResponseType(typeof(AdvanceFilterDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdvanceFilterDto>> Duplicate([Range(1, int.MaxValue)] int id, [FromQuery, Required] string newName)
        {
            try
            {
                _logger.LogInformation("POST /api/advancefilter/{Id}/duplicate - Duplicating filter with new name: {NewName}", id, newName);
                
                if (string.IsNullOrWhiteSpace(newName))
                {
                    return BadRequest("New name is required and cannot be empty");
                }

                var result = await _advanceFilterService.DuplicateAsync(id, newName);
                if (result == null)
                {
                    return NotFound($"Advance filter not found for ID: {id}");
                }

                return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating advance filter for ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while duplicating advance filter");
            }
        }

        /// <summary>
        /// Gets rules for a specific filter
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>Collection of rules for the filter</returns>
        /// <response code="200">Returns rules for the filter</response>
        /// <response code="400">Invalid filter ID</response>
        /// <response code="404">Filter not found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("{id:int}/rules")]
        [ProducesResponseType(typeof(IEnumerable<RulesDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<RulesDto>>> GetRules([Range(1, int.MaxValue)] int id)
        {
            try
            {
                _logger.LogInformation("GET /api/advancefilter/{Id}/rules - Getting rules for filter", id);
                
                var result = await _advanceFilterService.GetRulesByFilterIdAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting rules for filter ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving filter rules");
            }
        }

        /// <summary>
        /// Adds a rule to an existing filter
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <param name="rule">Rule to add</param>
        /// <returns>Updated advance filter</returns>
        /// <response code="200">Rule added successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="404">Filter not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("{id:int}/rules")]
        [ProducesResponseType(typeof(AdvanceFilterDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdvanceFilterDto>> AddRule([Range(1, int.MaxValue)] int id, [FromBody] CreateRulesRequest rule)
        {
            try
            {
                _logger.LogInformation("POST /api/advancefilter/{Id}/rules - Adding rule to filter", id);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _advanceFilterService.AddRuleAsync(id, rule);
                if (result == null)
                {
                    return NotFound($"Advance filter not found for ID: {id}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding rule to filter ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while adding rule to filter");
            }
        }

        /// <summary>
        /// Removes a rule from a filter
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <param name="ruleId">The rule ID to remove</param>
        /// <returns>Updated advance filter</returns>
        /// <response code="200">Rule removed successfully</response>
        /// <response code="400">Invalid parameters</response>
        /// <response code="404">Filter or rule not found</response>
        /// <response code="500">Internal server error</response>
        [HttpDelete("{id:int}/rules/{ruleId:int}")]
        [ProducesResponseType(typeof(AdvanceFilterDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdvanceFilterDto>> RemoveRule([Range(1, int.MaxValue)] int id, [Range(1, int.MaxValue)] int ruleId)
        {
            try
            {
                _logger.LogInformation("DELETE /api/advancefilter/{Id}/rules/{RuleId} - Removing rule from filter", id, ruleId);
                
                var result = await _advanceFilterService.RemoveRuleAsync(id, ruleId);
                if (result == null)
                {
                    return NotFound($"Advance filter not found for ID: {id} or rule not found for ID: {ruleId}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing rule ID: {RuleId} from filter ID: {Id}", ruleId, id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while removing rule from filter");
            }
        }
    }
}
