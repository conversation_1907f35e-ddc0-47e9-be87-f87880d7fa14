﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.ReferenceDocumentServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class ReferenceDocumentController : ApiController
    {


        #region ::: To SaveFileToServer Uday Kumar J B 04-10-2024:::
        /// <summary>
        /// To SaveFileToServer
        /// </summary>
        [Route("api/ReferenceDocument/SaveFileToServer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> SaveFileToServer(SaveFileToServerList SaveFileToServerobj)
        {
            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                string User_ID = null;
                string Branch = null;
                string User_Name = null;
                string Object_ID = null;
                string IsActive = null;
                string TransactionID = null;
                string FileDescription = null;
                string DetailID = null;
                string ISDuplicateAtta = null;
                string Company_ID = null;
                IFormFile postedFile = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "User_ID":
                                User_ID = value;
                                break;
                            case "Branch":
                                Branch = value;
                                break;
                            case "User_Name":
                                User_Name = value;
                                break;
                            case "Object_ID":
                                Object_ID = value;
                                break;
                            case "TransactionID":
                                TransactionID = value;
                                break;
                            case "FileDescription":
                                FileDescription = value;
                                break;
                            case "DetailID":
                                DetailID = value;
                                break;
                            case "ISDuplicateAtta":
                                ISDuplicateAtta = value;
                                break;
                            case "IsActive":
                                IsActive = value;
                                break;
                            case "Company_ID":
                                Company_ID = value;
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            postedFile = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = ReferenceDocumentServices.SaveFileToServer(postedFile, connString, SaveFileToServerobj);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }
        #endregion



        #region ::: To SelectAttachmentDetailsAddMode Uday Kumar J B 04-10-2024:::
        /// <summary>
        /// To SelectAttachmentDetailsAddMode
        /// </summary>
        [Route("api/ReferenceDocument/SelectAttachmentDetailsAddMode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAttachmentDetailsAddMode([FromBody] SelectAttachmentDetailsAddModeList SelectAttachmentDetailsAddModeobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = ReferenceDocumentServices.SelectAttachmentDetailsAddMode(connstring, SelectAttachmentDetailsAddModeobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveAttachment Refernce Document Uday kumar J B 04-10-2024:::
        /// <summary>
        /// Save Sales history Attachment
        /// </summary>
        /// <returns>...</returns>
        [Route("api/ReferenceDocument/SaveAttachmentReferenceDocument")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveAttachmentReferenceDocument([FromBody] SaveAttachmentReferenceDocumentList SaveAttachmentReferenceDocumentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = ReferenceDocumentServices.SaveAttachmentReferenceDocument(SaveAttachmentReferenceDocumentobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelAllAttachment Uday kumar J B 04-10-2024:::
        /// <summary>
        /// SelAllAttachment
        /// </summary>
        /// <returns>...</returns>
        [Route("api/ReferenceDocument/SelAllAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllAttachment([FromBody] SelAllAttachmentList SelAllAttachmentobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = ReferenceDocumentServices.SelAllAttachment(connstring, SelAllAttachmentobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region :::CheckAttachment Uday kumar J B 04-10-2024:::
        /// <summary>
        /// CheckAttachment
        /// </summary>
        /// <returns>...</returns>
        [Route("api/ReferenceDocument/CheckAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckAttachment([FromBody] CheckAttachmentList CheckAttachmentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = ReferenceDocumentServices.CheckAttachment(connString, CheckAttachmentobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}