﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;

public class JwtTokenValidationFilterAttribute : AuthorizationFilterAttribute
{
    
    public bool ValidateToken(string token, string secretKey, string issuer, string audience)
    {        
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = issuer,
            ValidateAudience = true,
            ValidAudience = audience,
            ValidateLifetime = true,
            IssuerSigningKey = new SymmetricSecurityKey(Convert.FromBase64String(secretKey)),
            //ValidateIssuerSigningKey = true, // Ensure issuer signing key is valid
            //RequireSignedTokens = true, // Require tokens to be signed
        };

        try
        {
            SecurityToken validatedToken;
            ClaimsPrincipal claimsPrincipal = new JwtSecurityTokenHandler().ValidateToken(token, tokenValidationParameters, out validatedToken);          
            return true;
        }
        catch (Exception ex)
        {
            // Token validation failed
            return false;
        }
    }

    #region Validate JWT token
    /// <summary>
    /// DK - 07-NOV-2023 to Vlidate JWT token sent from CLIENT - Customer Filter added since 4.7 .net fraework has not auto validation of JWT like .net core
    /// </summary>
    /// <param name="actionContext"></param>
    public override void OnAuthorization(HttpActionContext actionContext)
    {
        try
        {
            // Check if the action or controller is marked as [AllowAnonymous]
            bool isAllowAnonymous =
                actionContext.ActionDescriptor.GetCustomAttributes<AllowAnonymousAttribute>().Any() ||
                actionContext.ControllerContext.ControllerDescriptor.GetCustomAttributes<AllowAnonymousAttribute>().Any();

            if (!isAllowAnonymous)
            {
                // Retrieve the HTTP request
                HttpRequestMessage request = actionContext.Request;

                // Get the Authorization header
                if (request.Headers.Authorization == null)
                {
                    actionContext.Response = new HttpResponseMessage(HttpStatusCode.Unauthorized)
                    {
                        Content = new StringContent("Missing Authorization header"),
                    };
                    return;
                }
                // Extract the JWT token
                string token = request.Headers.Authorization.Parameter;

                // Retrieve the expected issuer and audience values from configuration
                string SecretKey = Convert.ToString(ConfigurationManager.AppSettings.Get("DCAPIKEY")); // Adding from web.config 
                string issuer = Convert.ToString(ConfigurationManager.AppSettings.Get("DCIssuerKEY")); // Adding from web.config 
                string audience = Convert.ToString(ConfigurationManager.AppSettings.Get("DCParty")); // Adding from web.config 

                // Retrieve your security key (secret key) used for token signing
                // Define the validation parameters, including the security key
                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = issuer,
                    ValidateAudience = true,
                    ValidAudience = audience,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(SecretKey)), // Set your security key
                    ClockSkew = TimeSpan.Zero,
                    RequireSignedTokens = true,
                    // Other validation parameters
                };

                // Create a token handler
                var tokenHandler = new JwtSecurityTokenHandler();

                // Validate the token
                SecurityToken validatedToken;
                ClaimsPrincipal claimsPrincipal = tokenHandler.ValidateToken(token, tokenValidationParameters, out validatedToken);
            }
        }
        catch (SecurityTokenValidationException ex)
        {
            // Token validation failed
            actionContext.Response = new HttpResponseMessage(HttpStatusCode.Unauthorized)
            {
                Content = new StringContent("Token validation failed: " + ex.Message),
            };
        }
        catch (Exception ex)
        {
            // Other exceptions
            actionContext.Response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
            {
                Content = new StringContent(ex.Message),
            };
        }
    }
    #endregion


}
