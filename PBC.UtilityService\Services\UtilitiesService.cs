using Microsoft.AspNetCore.Mvc;

namespace PBC.UtilityService.Services
{
    public class UtilitiesService : IUtilitiesService
    {
        private readonly ILogger<UtilitiesService> _logger;

        public UtilitiesService(ILogger<UtilitiesService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<int> CalculateTotalPagesAsync(long numberOfRecords, int pageSize)
        {
            try
            {
                _logger.LogInformation("Calculating total pages for {NumberOfRecords} records with page size {PageSize}", numberOfRecords, pageSize);
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.CalculateTotalPages(numberOfRecords, pageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total pages");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsDateAsync(string date)
        {
            try
            {
                _logger.LogInformation("Checking if '{Date}' is a valid date", date);
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.IsDate(date);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if string is date");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsNumericAsync(object entity)
        {
            try
            {
                _logger.LogInformation("Checking if object is numeric");
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.IsNumeric(entity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if object is numeric");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> IsDoubleAsync(object entity)
        {
            try
            {
                _logger.LogInformation("Checking if object is double");
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.IsDouble(entity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if object is double");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<List<string>> MessageAsync(string message)
        {
            try
            {
                _logger.LogInformation("Creating message list with message: {Message}", message);
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.Message(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating message list");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> CalculateMD5HashAsync(string input)
        {
            try
            {
                _logger.LogInformation("Calculating MD5 hash for input");
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.CalculateMD5Hash(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating MD5 hash");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> GenerateDMSPasswordAsync(string userPassword)
        {
            try
            {
                _logger.LogInformation("Generating DMS password hash");
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.GenerateDMSPassword(userPassword);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating DMS password");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<IActionResult> GetGlobalResourceObjectAsync(string cultureValue, string resourceKey)
        {
            try
            {
                _logger.LogInformation("Getting global resource object for culture: {Culture}, key: {Key}", cultureValue, resourceKey);
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.GetGlobalResourceObject(cultureValue, resourceKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting global resource object");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> GetMonthNameAsync(int id, string culture)
        {
            try
            {
                _logger.LogInformation("Getting month name for ID: {Id}, culture: {Culture}", id, culture);
                
                await Task.Delay(1); // Simulate async operation
                
                return Utilities.Utilities.GetMonthName(id, culture);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting month name");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> GetPriorityAsync(byte id, string culture)
        {
            try
            {
                _logger.LogInformation("Getting priority for ID: {Id}, culture: {Culture}", id, culture);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Utilities.GetPariority(id, culture);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting priority");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<JsonResult> GetInitialSetupAsync(int objectId, int userId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Getting initial setup for Object ID: {ObjectId}, User ID: {UserId}", objectId, userId);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.InitialSetup(objectId, userId, connectionString, logException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting initial setup");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<JsonResult> ValidateCalldateandPCDAsync(DateTime pcd, DateTime calldate)
        {
            try
            {
                _logger.LogInformation("Validating call date and PCD: PCD={PCD}, CallDate={CallDate}", pcd, calldate);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.ValidateCalldateandPCD(pcd, calldate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating call date and PCD");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<JsonResult> CheckBayWorkshopAvailabilityAsync(DateTime expectedArrivalDate, DateTime expectedDepartureDate, bool isWIPBay, int bookingMinutes, int serviceRequestId, int quotationId, int branch, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Checking bay workshop availability for Branch: {Branch}, ServiceRequest: {ServiceRequestId}", branch, serviceRequestId);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.CheckBayWorkshopAvailability(expectedArrivalDate, expectedDepartureDate, isWIPBay, bookingMinutes, serviceRequestId, quotationId, branch, connectionString, logException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking bay workshop availability");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> CheckAutoAllocationAsync(int companyID, int workFlowID, int userID, string connString, int logException)
        {
            try
            {
                _logger.LogInformation("Checking auto allocation for Company: {CompanyID}, Workflow: {WorkFlowID}, User: {UserID}", companyID, workFlowID, userID);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.CheckAutoAllocation(companyID, workFlowID, userID, connString, logException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking auto allocation");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<object> GetAutoAllocationStepDetailsAsync(int workFlowID, int companyID, string connString, int logException)
        {
            try
            {
                _logger.LogInformation("Getting auto allocation step details for Workflow: {WorkFlowID}, Company: {CompanyID}", workFlowID, companyID);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.GetAutoAllocationStepDetails(workFlowID, companyID, connString, logException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto allocation step details");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> LockRecordAsync(string connString, int logException, string userCulture, int quotationID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0)
        {
            try
            {
                _logger.LogInformation("Locking record for Quotation: {QuotationID}, User: {UserID}, Workflow: {WorkFlowName}", quotationID, userID, workFlowName);

                await Task.Delay(1); // Simulate async operation

                return Utilities.WorkFlowCommon.LockRecord(connString, logException, userCulture, quotationID, userID, companyID, workFlowName, dbName, branchID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error locking record for Quotation: {QuotationID}", quotationID);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> UnLockRecordAsync(string connString, int logException, string userCulture, int jobcardID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0)
        {
            try
            {
                _logger.LogInformation("Unlocking record for Jobcard: {JobcardID}, User: {UserID}, Workflow: {WorkFlowName}", jobcardID, userID, workFlowName);

                await Task.Delay(1); // Simulate async operation

                return Utilities.WorkFlowCommon.UnLockRecord(connString, logException, userCulture, jobcardID, userID, companyID, workFlowName, dbName, branchID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unlocking record for Jobcard: {JobcardID}", jobcardID);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<DateTime> LocalTimeBasedOnBranchAsync(int branchID, DateTime serverTime, string connString)
        {
            try
            {
                _logger.LogInformation("Converting server time to local time for Branch: {BranchID}, ServerTime: {ServerTime}", branchID, serverTime);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.LocalTimeBasedOnBranch(branchID, serverTime, connString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting server time to local time for Branch: {BranchID}", branchID);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<DateTime> LocalTimeAsync(int userID, DateTime serverTime, string connString)
        {
            try
            {
                _logger.LogInformation("Converting server time to local time for User: {UserID}, ServerTime: {ServerTime}", userID, serverTime);

                await Task.Delay(1); // Simulate async operation

                return Utilities.Common.LocalTime(userID, serverTime, connString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting server time to local time for User: {UserID}", userID);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<string> GetEndStepStatusNameAsync(int workflowID, string connString, int logException)
        {
            try
            {
                _logger.LogInformation("Getting end step status name for Workflow: {WorkflowID}", workflowID);

                await Task.Delay(1); // Simulate async operation

                return Utilities.HelpDeskCommon.GetEndStepStatusName(workflowID, connString, logException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting end step status name for Workflow: {WorkflowID}", workflowID);
                throw;
            }
        }
    }
}
