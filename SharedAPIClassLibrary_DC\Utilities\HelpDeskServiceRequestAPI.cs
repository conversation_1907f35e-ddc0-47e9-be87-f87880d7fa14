﻿using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Configuration;
using System.Text;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class HelpDeskServiceRequestAPI
    {
        #region  GetServiceRequestQuery vinay n 14/11/24
        /// <summary>
        /// GetServiceRequestQuery
        /// </summary>
        /// <param name="LangID"></param>
        /// <param name="GenLangCode"></param>
        /// <param name="UserLangCode"></param>
        /// <param name="Mode"></param>
        /// <param name="User_ID"></param>
        /// <param name="Company_ID"></param>
        /// <param name="Branch_ID"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <returns></returns>
        public static string GetServiceRequestQuery(string connString, int LogException, int LangID, string GenLangCode = "en", string UserLangCode = "en", int Mode = 0, int User_ID = 0, int Company_ID = 0, int Branch_ID = 0, string sidx = "", string sord = "")
        {
            StringBuilder Query = new StringBuilder();
            string query = string.Empty;
            string finalquery = string.Empty;
            string statusIds = string.Empty;
            int ClosedStatusID = HelpDeskCommon.GetEndStepStatusID(Common.GetWorkFlowID("Case Registration", ConfigurationManager.AppSettings.Get("DbName"), connString, LogException), connString, LogException);
            try
            {
                string AssignedTo = "";
                if (GenLangCode == UserLangCode)
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                    query = query + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetail issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";//left outer join GNM_PartyContactPersonDetails PCP on sr.PartyContactPerson_ID=PCP.PartyContactPerson_ID left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID
                    query = query + " left outer join GNM_Model model on sr.Model_ID=model.Model_ID left outer join GNM_ProductType PT on sr.ProductType_ID=PT.ProductType_ID  left outer join GNM_RefMasterDetail brand on sr.Brand_ID=brand.RefMasterDetail_ID LEFT JOIN PST_QuotationHeader quot ON sr.Quotation_ID = quot.Quotation_ID LEFT OUTER JOIN SRT_JobCardHeader job ON sr.JobCard_ID = job.JobCard_ID LEFT OUTER JOIN GNM_WFStepStatus jst ON job.JobCardStatus_ID = jst.WFStepStatus_ID LEFT OUTER JOIN GNM_WFStepStatus st ON sr.CallStatus_ID = st.WFStepStatus_ID LEFT OUTER JOIN HD_IssueSubArea IssueSubArea ON sr.IssueSubArea_ID = IssueSubArea.IssueSubArea_ID join GNM_RefMasterDetail Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID LEFT JOIN GNM_RefMasterDetail MainGroup ON sr.FunctionGroup_ID = MainGroup.RefMasterDetail_ID LEFT OUTER JOIN GNM_RefMasterDetail SubGroup ON sr.SubGroup_ID = SubGroup.RefMasterDetail_ID";
                    //query = query + " left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID  left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID left outer join GNM_WFStepStatus jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join HD_IssueSubArea IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID";
                    AssignedTo = "case when Addresse_Flag=1 then (select User_Name from GNM_User where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRole where WFRole_ID=Addresse_ID) end as AssignedTo";
                }
                else
                {
                    //---------Common From Condition----
                    query = " from GNM_WFCase_Progress inner join HD_ServiceRequest sr on sr.ServiceRequest_ID=Transaction_ID  left outer join GNM_Product PRD on sr.Product_ID=PRD.Product_ID";
                    query = query + " left outer join GNM_Party p on sr.Party_ID=p.Party_ID left outer join GNM_PartyLocale pa on p.Party_ID=pa.Party_ID and pa.Language_ID=" + LangID + " join GNM_Branch branch on sr.Branch_ID=branch.Branch_ID left outer join GNM_RefMasterDetailLocale issuearea on sr.IssueArea_ID=issuearea.RefMasterDetail_ID";//left outer join GNM_PartyContactPersonDetails PCP on sr.PartyContactPerson_ID=PCP.PartyContactPerson_ID  left outer join GNM_Branch branchdata on branchdata.Branch_ID=sr.Party_ID
                    query = query + " left outer join GNM_ModelLocale model on sr.Model_ID=model.Model_ID and model.Language_ID=" + LangID + " left outer join GNM_ProductTypeLocale PT on sr.ProductType_ID=PT.ProductType_ID and PT.Language_ID=" + LangID + " left outer join GNM_RefMasterDetailLocale brand on sr.Brand_ID=brand.RefMasterDetail_ID and brand.Language_ID=" + LangID + " left join GNM_RefMasterDetailLocale Dropin on Dropin.RefMasterDetail_ID=sr.ScheduledType_ID and Dropin.Language_ID=" + LangID + "  left outer join GNM_WFStepStatusLocale st on sr.CallStatus_ID = st.WFStepStatus_ID LEFT OUTER JOIN HD_IssueSubAreaLocale IssueSubArea ON sr.IssueSubArea_ID = IssueSubArea.IssueSubArea_ID  LEFT OUTER JOIN GNM_RefMasterDetailLocale MainGroup ON sr.FunctionGroup_ID = MainGroup.RefMasterDetail_ID LEFT OUTER JOIN GNM_RefMasterDetailLocale SubGroup ON sr.SubGroup_ID = SubGroup.RefMasterDetail_ID LEFT OUTER JOIN SRT_JobCardHeader job ON sr.JobCard_ID = job.JobCard_ID LEFT OUTER JOIN GNM_WFStepStatusLocale jst ON job.JobCardStatus_ID = jst.WFStepStatus_ID LEFT OUTER JOIN PST_QuotationHeader quot ON sr.Quotation_ID = quot.Quotation_ID";
                    //query = query + "  left outer join PST_QuotationHeader quot on sr.Quotation_ID=quot.Quotation_ID left outer join SRT_JobCardHeader job on sr.JobCard_ID=job.JobCard_ID  left outer join GNM_WFStepStatusLocale jst on job.JobCardStatus_ID=jst.WFStepStatus_ID left outer join HD_IssueSubAreaLocale IssueSubArea on sr.IssueSubArea_ID=IssueSubArea.IssueSubArea_ID";
                    AssignedTo = "case when Addresse_Flag=1 then (select User_Name from GNM_UserLocale where User_ID=Addresse_ID) else (select WFRole_Name from GNM_WFRoleLocale where WFRole_ID=Addresse_ID) end as AssignedTo";
                }

                finalquery = string.Empty;
                if (GenLangCode == UserLangCode)
                {
                    finalquery = ";WITH NewT AS(select branch.Branch_Name as BranchName, sr.Branch_ID ,sr.IsDealer, sr.IsEscalted,sr.EscalatedLevel,  sr.CaseType_ID,EnquiryType=(case when sr.CaseType_ID=1 then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ),sr.ServiceRequest_ID,sr.ServiceRequestNumber as RequestNumber,sr.CallDescription, sr.ChildTicket_Sequence_ID,ProductReading,sr.Party_ID,sr.PartyContactPerson_ID,sr.Model_ID,sr.Brand_ID,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.IssueSubArea_ID,sr.CallOwner_ID,sr.Region_ID,PT.ProductType_Name as ProductType,convert(varchar(4),sr.FinancialYear) as FinancialYear,sr.AttachmentCount as Attachmentco,ServiceRequestDate,p.Party_Name as PartyName,p.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description AS IssueSubArea,MainGroup.RefMasterDetail_Name AS FunctionGroupName, SubGroup.RefMasterDetail_Name AS SubGroupName,jst.WFStepStatus_Nm AS JobCardStatus,sr.Product_ID,sr.IssueArea_ID,Dropin.RefMasterDetail_Name as IsDropin ,";//,PCP.PartyContactPerson_Email,PCP.PartyContactPerson_Mobile,PCP.PartyContactPerson_Name,job.JobCard_ID,case when  sr.IsDealer=1 then branchdata.Branch_Name else  p.Party_Name end as PartyName,IssueSubArea.IssueSubArea_Description as IssueSubArea
                }
                else
                {
                    finalquery = ";WITH NewT AS(select branch.Branch_Name as BranchName, sr.Branch_ID ,sr.IsDealer, sr.IsEscalted,sr.EscalatedLevel,  sr.CaseType_ID,EnquiryType=(case when sr.CaseType_ID=1 then 'Support'  when sr.CaseType_ID=2 then 'Part' when sr.CaseType_ID=3 then 'Service' else 'Sales' end ),sr.ServiceRequest_ID,sr.ServiceRequestNumber as RequestNumber,sr.CallDescription, sr.ChildTicket_Sequence_ID,ProductReading,sr.Party_ID,sr.PartyContactPerson_ID,sr.Model_ID,sr.Brand_ID,sr.CallMode_ID,sr.CallPriority_ID,sr.CallComplexity_ID,sr.IssueSubArea_ID,sr.CallOwner_ID,sr.Region_ID,PT.ProductType_Name as ProductType,convert(varchar(4),sr.FinancialYear) as FinancialYear,sr.AttachmentCount as Attachmentco,ServiceRequestDate, pa.Party_Name as PartyName,p.Party_Code,issuearea.RefMasterDetail_Name as IssueArea,IssueSubArea.IssueSubArea_Description AS IssueSubArea,MainGroup.RefMasterDetail_Name AS FunctionGroupName, SubGroup.RefMasterDetail_Name AS SubGroupName,jst.WFStepStatus_Nm AS JobCardStatus,sr.Product_ID,sr.IssueArea_ID,Dropin.RefMasterDetail_Name as IsDropin,";//,PCP.PartyContactPerson_Email,PCP.PartyContactPerson_Mobile,PCP.PartyContactPerson_Name,job.JobCard_ID,case when  sr.IsDealer=1 then branchdata.Branch_Name else  pa.Party_Name end as PartyName,IssueSubArea.IssueSubArea_Description as IssueSubArea
                }
                finalquery = finalquery + "Model_Name as Model,sr.SerialNumber,ISNULL(PRD.Product_UniqueNo,'') AS Product_Unique_Number,brand.RefMasterDetail_Name as Brand_Name,st.WFStepStatus_Nm as Status,st.StepStatusCode, ";
                finalquery = finalquery + "Locked_Ind,Addresse_ID,Addresse_Flag,WFNextStep_ID,WFSteps_ID,Action_Chosen," + AssignedTo + ",'JobCardActivity' = ( SELECT TOP 1 RefMasterDetail_Name     FROM SRT_JobCardActivityDetail AS jca   INNER JOIN GNM_RefMasterDetail AS ja ON jca.Status_ID = ja.RefMasterDetail_ID      ), quot.QuotationNumber,job.JobNumber, ROW_NUMBER() OVER(ORDER BY " + sidx + " " + sord + ") as row_number";
                finalquery = finalquery + query;


                if (Mode != 0)//Called from WEB API
                {
                    int WorkFlowID = Common.GetWorkFlowID("Case Registration", ConfigurationManager.AppSettings.Get("DbName"), connString, LogException);
                    if (Mode == 1)//My-Q
                    {
                        finalquery = finalquery + " WHERE Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND Addresse_ID =" + User_ID + " AND Addresse_Flag = 1 AND sr.Company_ID=" + Company_ID;
                    }
                    else if (Mode == 2)//Group-Q
                    {
                        string inCondition = CommonFunctionalities.getGrpQincondition(User_ID, connString, LogException);
                        finalquery = finalquery + " WHERE Action_Chosen is null AND WorkFlow_ID =" + WorkFlowID + " AND (Addresse_ID in (" + inCondition + ") AND Addresse_Flag = 0) AND sr.Company_ID=" + Company_ID;
                    }
                    else if (Mode == 3)//All-Q
                    {
                        statusIds = CommonFunctionalities.getStatusIDs(ClosedStatusID, WorkFlowID, connString, LogException);
                        finalquery = finalquery + " WHERE (Action_Chosen is null or" + statusIds + ") and  WorkFlow_ID=" + WorkFlowID + " and sr.Company_ID=" + Company_ID;
                    }
                    if (Branch_ID != 0) Query.Append(" AND sr.Branch_ID=" + Branch_ID);
                    Query.Append(") SELECT * FROM NewT");
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return finalquery.ToString();
        }

        #endregion
    }
}
