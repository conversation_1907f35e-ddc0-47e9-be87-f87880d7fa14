﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreModelMasterServices
    {

        static string AppPath = string.Empty;

        #region ::: Select Model Landomg grid record without sorting and feltering /Mithun:::
        public static IQueryable<ModelMaster> getLandingGridData(int BrandID, int ProductTypeID, int LanguageID, string GeneralCulture, string UserCulture, int Company_ID, string constring, int LogException)
        {
            List<ModelMaster> modelMasterList = new List<ModelMaster>();
            SelectModelList selectModelObj = new SelectModelList();
            try
            {

                bool IsSalesModule = false;
                string YesE = CommonFunctionalities.GetResourceString(GeneralCulture, "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(GeneralCulture, "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(UserCulture, "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(UserCulture, "no").ToString();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Check IsSalesModule
                    string paramValueQuery = "SELECT Param_value FROM GNM_CompParam WHERE Company_ID = @Company_ID AND UPPER(Param_Name) = 'HELPDESKOPTIONS'";
                    using (SqlCommand cmd = new SqlCommand(paramValueQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        var result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            IsSalesModule = result.ToString().Contains("4");
                        }
                    }

                    // Fetch Parent Company Details
                    List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                    string parentCompanyQuery = @"
                        DECLARE @Results TABLE ([Company_ID] INT, [Company_Name] NVARCHAR(255), [Company_Parent_ID] INT, [RecursionLevel] INT);
                        DECLARE @RecursionLevel INT = 1;  -- Insert the base company

                        -- Insert the base company
                        INSERT INTO @Results
                        SELECT 
                            [Company_ID], 
                            [Company_Name], 
                            [Company_Parent_ID], 
                            @RecursionLevel
                        FROM 
                            GNM_Company
                        WHERE 
                            [Company_ID] = @Company_ID;

                        WHILE (@RecursionLevel < 100)  -- Limiting recursion depth
                        BEGIN
                            INSERT INTO @Results
                            SELECT 
                                child.[Company_ID], 
                                child.[Company_Name], 
                                child.[Company_Parent_ID], 
                                @RecursionLevel + 1
                            FROM 
                                GNM_Company AS child
                            INNER JOIN 
                                @Results AS pc ON child.[Company_Parent_ID] = pc.[Company_ID]
                            WHERE 
                                child.[Company_ID] <> pc.[Company_ID]
                                AND NOT EXISTS (
                                    SELECT 1 
                                    FROM @Results AS r 
                                    WHERE r.[Company_ID] = child.[Company_ID]
                                );

                            SET @RecursionLevel = @RecursionLevel + 1;
                        END

                        -- Final result: Return the companies processed
                        SELECT * FROM @Results;
                        ";
                    using (SqlCommand cmd = new SqlCommand(parentCompanyQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParentCompanyDetails.Add(new ParentCompanyObject
                                {
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"])
                                });
                            }
                        }
                    }

                    // Fetch GNM_Model and GNM_ModelLocale based on BrandID, ProductTypeID
                    List<ModelMaster> modelList = new List<ModelMaster>();
                    string modelQuery = @"SELECT m.Model_ID, m.Model_Name, m.ServiceFrequency, m.Model_IsActive, 
                                     m.ProductType_ID, m.Brand_ID, m.Model_Description, m.AttachmentCount
                                  FROM GNM_Model m
                                  WHERE m.Brand_ID = @BrandID AND m.ProductType_ID = @ProductTypeID";
                    using (SqlCommand cmd = new SqlCommand(modelQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@BrandID", BrandID);
                        cmd.Parameters.AddWithValue("@ProductTypeID", ProductTypeID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ModelMaster model = new ModelMaster
                                {
                                    Model_ID = reader["Model_ID"] != DBNull.Value ? Convert.ToInt32(reader["Model_ID"]) : 0, // Default to 0 if null
                                    Model_Name = reader["Model_Name"] != DBNull.Value ? reader["Model_Name"].ToString() : string.Empty, // Default to empty string if null
                                    ServiceFrequency = reader["ServiceFrequency"] != DBNull.Value ? Convert.ToInt32(reader["ServiceFrequency"]) : 0, // Default to 0 if null
                                    Model_IsActive = reader["Model_IsActive"] != DBNull.Value ? (Convert.ToBoolean(reader["Model_IsActive"]) ? YesE : NoE) : NoE, // Handle null and convert
                                    ProductType_ID = reader["ProductType_ID"] != DBNull.Value ? Convert.ToInt32(reader["ProductType_ID"]) : 0, // Default to 0 if null
                                    Brand_ID = reader["Brand_ID"] != DBNull.Value ? Convert.ToInt32(reader["Brand_ID"]) : 0, // Default to 0 if null
                                    Model_Description = reader["Model_Description"] != DBNull.Value ? reader["Model_Description"].ToString() : string.Empty, // Default to empty string if null
                                    Attachmentcount = reader["AttachmentCount"] != DBNull.Value ? (byte?)Convert.ToByte(reader["AttachmentCount"]) : (byte?)null // Nullable byte, default to null if DBNull
                                };

                                modelList.Add(model);
                            }
                        }
                    }

                    // Fetch GNM_ModelLocale for Localization (if LanguageID is different from GeneralLanguageID)
                    if (LanguageID != Convert.ToInt32(selectModelObj.GeneralLanguageID))
                    {
                        string localeQuery = @"SELECT ml.Model_ID, ml.Model_Name
                                       FROM GNM_ModelLocale ml
                                       INNER JOIN GNM_Model m ON m.Model_ID = ml.Model_ID
                                       WHERE ml.Language_ID = @LanguageID AND m.Brand_ID = @BrandID AND m.ProductType_ID = @ProductTypeID";
                        using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", LanguageID);
                            cmd.Parameters.AddWithValue("@BrandID", BrandID);
                            cmd.Parameters.AddWithValue("@ProductTypeID", ProductTypeID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var model = modelList.FirstOrDefault(m => m.Model_ID == Convert.ToInt32(reader["Model_ID"]));
                                    if (model != null)
                                    {
                                        model.Model_Name = reader["Model_Name"].ToString(); // Localized model name
                                    }
                                }
                            }
                        }
                    }

                    // Fetch GNM_ServiceType and map with models (left join)
                    List<GNM_ServiceType> serviceTypes = new List<GNM_ServiceType>();
                    string serviceTypeQuery = "SELECT ServiceType_ID, ServiceType_Name FROM GNM_ServiceType";
                    using (SqlCommand cmd = new SqlCommand(serviceTypeQuery, conn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceTypes.Add(new GNM_ServiceType
                                {
                                    ServiceType_ID = reader["ServiceType_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceType_ID"]) : 0, // Default to 0 if null
                                    ServiceType_Name = reader["ServiceType_Name"] != DBNull.Value ? reader["ServiceType_Name"].ToString() : string.Empty // Default to empty string if null
                                });

                            }
                        }
                    }

                    foreach (var model in modelList)
                    {
                        model.ServiceType_Name = serviceTypes.FirstOrDefault(st => st.ServiceType_ID == model.ServiceType_ID)?.ServiceType_Name ?? string.Empty;
                        if (IsSalesModule)
                        {
                            model.AverageSellingPrice = getavesellprice(model.Model_ID, model.Brand_ID, model.ProductType_ID, Company_ID, constring, LogException);
                            model.AveragePurchasePrice = getavepurprice(model.Model_ID, model.Brand_ID, model.ProductType_ID, Company_ID, constring, LogException);
                        }
                        else
                        {
                            model.AverageSellingPrice = 0.00M;
                            model.AveragePurchasePrice = 0.00M;
                        }
                        model.AverageSellingPriceStr = model.AverageSellingPrice.ToString("0.00");
                        model.AveragePurchasePriceStr = model.AveragePurchasePrice.ToString("0.00");
                        model.ServiceFrequencySort = model.ServiceFrequency.ToString();
                        // Additional properties like AvailableStock, GITStock, etc., can be fetched similarly.
                    }

                    modelMasterList = modelList;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return (IActionResult)modelMasterList.AsQueryable();
            return modelMasterList.AsQueryable();
        }

        #endregion

        #region ::: getavesellprice /Mithun:::
        private static decimal getavesellprice(int modelid, int brandid, int producttypeid, int Company_ID, string constring, int LogException)
        {
            decimal avesellprice = 0.00M;

            // Define your connection string

            // Define the SQL query
            string query = @"
                SELECT AVG(COALESCE(n.SELLINGPRICE, 0.00)) AS AverageSellingPrice
                FROM GNM_Product AS m
                INNER JOIN CoreSLT_SALESHISTORY AS n
                ON m.Product_ID = n.PRODUCT_ID
                WHERE m.Model_ID = @ModelID
                  AND m.Brand_ID = @BrandID
                  AND m.ProductType_ID = @ProductTypeID
                  AND n.COMPANY_ID = @CompanyID";

            try
            {
                // Create and open a connection
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create a SqlCommand
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Add parameters
                        command.Parameters.AddWithValue("@ModelID", modelid);
                        command.Parameters.AddWithValue("@BrandID", brandid);
                        command.Parameters.AddWithValue("@ProductTypeID", producttypeid);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        // Execute the query and get the result
                        object result = command.ExecuteScalar();

                        // Check if result is not null and convert to decimal
                        if (result != DBNull.Value)
                        {
                            avesellprice = Convert.ToDecimal(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return avesellprice;
        }

        #endregion

        #region ::: getavepurprice /Mithun::: 
        private static decimal getavepurprice(int modelid, int brandid, int producttypeid, int Company_ID, string constring, int LogException)
        {
            decimal avepurprice = 0.00M;

            // Define the SQL query
            string query = @"
        SELECT AVG(COALESCE(y.LandingCost, 0.00)) AS AveragePurchasePrice
        FROM GNM_Product AS x
        INNER JOIN GNM_ProductInvoiceDetail AS y
        ON x.Product_ID = y.Product_ID
        WHERE x.Model_ID = @ModelID
          AND x.Brand_ID = @BrandID
          AND x.ProductType_ID = @ProductTypeID
          AND y.Company_ID = @CompanyID";

            try
            {
                // Create and open a connection
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create a SqlCommand
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Add parameters
                        command.Parameters.AddWithValue("@ModelID", modelid);
                        command.Parameters.AddWithValue("@BrandID", brandid);
                        command.Parameters.AddWithValue("@ProductTypeID", producttypeid);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        // Execute the query and get the result
                        object result = command.ExecuteScalar();

                        // Check if result is not null and convert to decimal
                        if (result != DBNull.Value)
                        {
                            avepurprice = Convert.ToDecimal(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return avepurprice;
        }


        #endregion

        #region ::: Select Model Landing grid /Mithun:::
        /// <summary>
        /// To select Model for Brand and Product Type
        /// </summary>
        public static IActionResult Select(SelectModelList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool _advnce, string filters, string Query)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                // Modified by Manjunatha P                 
                IQueryable<ModelMaster> IQModelMaster = null;
                IQModelMaster = (IQueryable<ModelMaster>)getLandingGridData(SelectObj.BrandID, SelectObj.ProductTypeID, SelectObj.LanguageID, SelectObj.GeneralCulture, SelectObj.UserCulture, SelectObj.Company_ID, constring, LogException);


                //if (Request.Params["_search"] == "true")
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                //    if (filters.rules.Count() > 0)
                //    {
                //        IQModelMaster = IQModelMaster.FilterSearch<ModelMaster>(filters);
                //    }
                //}
                //else if (Request.Params["advnce"] == "true")
                //{
                //    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();

                //    if (advnfilter.rules.Count() > 0)
                //    {
                //        IQModelMaster = IQModelMaster.AdvanceSearch<ModelMaster>(advnfilter);
                //    }
                //}
                sidx = sidx == "ServiceFrequencySort" ? "ServiceFrequency" : sidx;
                IQModelMaster = IQModelMaster.OrderByField<ModelMaster>(sidx, sord);

                Count = IQModelMaster.ToList().Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)//1st iteration QA correction
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQModelMaster.AsEnumerable()
                            select new
                            {
                                ID = a.Model_ID,
                                //Attachmentcount = Convert.ToInt32(a.Attachmentcount) > 0 ? "<img id='" + a.Model_ID + "' alt='Attachment' src='" + AppPath + "/Images/Attchmnt1616.png' />(" + a.Attachmentcount.ToString() + ")" : "",
                                Attachmentcount = Convert.ToInt32(a.Attachmentcount) > 0 ? "<a title='Attachment' href='#' id='" + a.Model_ID + "'  ><i class='fa fa-paperclip'></i></a>(" + a.Attachmentcount.ToString() + ")" : "",

                                //edit = "<img id='" + a.Model_ID + "' ModelName='" + a.Model_Name + "' src='" + AppPath + "/Content/edit.gif' key='" + a.Model_ID + "' class='EditModel' editmode='false'/>",
                                edit = "<a title=" + CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "view").ToString() + " href='#' id='" + a.Model_ID + "' ModelName='" + a.Model_Name + "' key='" + a.Model_ID + "' class='EditModel font-icon-class' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.Model_ID + "' defaultchecked=''  id='chk" + a.Model_ID + "' class='ModelDelete'/>",
                                Model_Name = (a.Model_Name),
                                ServiceType_Name = a.ServiceType_Name,
                                //Modified By Puneeth M to make Service Type optional on 25-Jan-2015
                                ServiceFrequency = a.ServiceFrequency == 0 ? string.Empty : a.ServiceFrequency.ToString(),
                                ServiceFrequencySort = a.ServiceFrequencySort == "0" ? string.Empty : a.ServiceFrequencySort.ToString(),
                                //
                                AverageSellingPrice = a.AverageSellingPrice.ToString("0.00"),
                                AveragePurchasePrice = a.AveragePurchasePrice.ToString("0.00"),
                                AverageSellingPriceStr = a.AverageSellingPriceStr,
                                AveragePurchasePriceStr = a.AveragePurchasePriceStr,
                                Model_IsActive = a.Model_IsActive,
                                //Locale = "<img key='" + a.Model_ID + "' src='" + AppPath + "/Content/local.png' class='ModelLocale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                //<i class='fa fa-globe'></i>fa fa-plus
                                Locale = "<a key='" + a.Model_ID + "' src='" + AppPath + "/Content/local.png' class='ModelLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>",

                                //View = "<img id='" + a.Model_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.Model_ID + "' class='ViewModelLocale' Model='" + a.Model_Name + "'/>",
                                View = "<a title=" + CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "Add").ToString() + " href='#' id='" + a.Model_ID + "' key='" + a.Model_ID + "' class='ViewModelLocale' Model='" + a.Model_Name + "' ><i class='fa fa-plus'></i></a>",
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    //  filter = Request.Params["filters"],
                    //  advanceFilter = Request.Params["Query"],
                    SelectObj.BrandID,
                    SelectObj.ProductTypeID,
                    SelectObj.LanguageID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Particular Model Header in edit mode /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectParticularModelHeader(SelectParticularModelHeaderList SelectParticularModelHeaderObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    decimal APPrice = 0.00M;
                    decimal ASPrice = 0.00M;
                    int AvailStockCount = 0, PhysicalStockCount = 0, GITStockCount = 0, OWHStockCount = 0, UCLStockCount = 0;
                    int Company_ID = Convert.ToInt32(SelectParticularModelHeaderObj.Company_ID);
                    int Branch_ID = Convert.ToInt32(SelectParticularModelHeaderObj.Branch);
                    //GNM_User User = (GNM_User)Session["UserDetails"];
                    // GNM_User User = SelectParticularModelHeaderObj.UserDetails.FirstOrDefault();

                    bool IsSalesModule = false;
                    using (SqlCommand cmd = new SqlCommand("SELECT Param_value FROM GNM_CompParam WHERE Company_ID = @CompanyID AND Param_Name = 'HELPDESKOPTIONS'", conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        IsSalesModule = cmd.ExecuteScalar().ToString().Contains("4");
                    }

                    GNM_Model Model = null;
                    using (SqlCommand cmd = new SqlCommand("Up_SEL_GetModel", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ModelID", SelectParticularModelHeaderObj.modelID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Model = new GNM_Model
                                {
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name")) ? null : reader.GetString(reader.GetOrdinal("Model_Name")),
                                    Model_Description = reader.IsDBNull(reader.GetOrdinal("Model_Description")) ? null : reader.GetString(reader.GetOrdinal("Model_Description")),
                                    Model_IsActive = reader.GetBoolean(reader.GetOrdinal("Model_IsActive")),
                                    ServiceFrequency = reader.IsDBNull(reader.GetOrdinal("ServiceFrequency")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceFrequency")),
                                    ServiceType_ID = reader.IsDBNull(reader.GetOrdinal("ServiceType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceType_ID"))
                                };

                            }
                        }
                    }

                    GNM_ModelLocale ModelLocale = null;
                    using (SqlCommand cmd = new SqlCommand("Up_SEL_GetModelLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ModelID", SelectParticularModelHeaderObj.modelID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectParticularModelHeaderObj.languageid);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                ModelLocale = new GNM_ModelLocale
                                {
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")), // Assuming Model_ID is not nullable
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")), // Assuming Language_ID is not nullable
                                    Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name")) ? null : reader.GetString(reader.GetOrdinal("Model_Name")),
                                    Model_Description = reader.IsDBNull(reader.GetOrdinal("Model_Description")) ? null : reader.GetString(reader.GetOrdinal("Model_Description"))
                                };

                            }
                        }
                    }

                    int serviceTypeID = Model.ServiceType_ID.HasValue ? Model.ServiceType_ID.Value : 0;
                    GNM_ServiceType ServiceType = null;
                    if (serviceTypeID != 0)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_SEL_GetServiceType", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ServiceTypeID", serviceTypeID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    ServiceType = new GNM_ServiceType
                                    {
                                        ServiceType_ID = reader.GetInt32(reader.GetOrdinal("ServiceType_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                    };
                                }
                            }
                        }
                    }

                    GNM_MODELSERVICETYPEDET modelServiceTypeRow = null;
                    using (SqlCommand cmd = new SqlCommand("Up_SLE_GetModelServiceType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ModelID", Model.Model_ID);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                modelServiceTypeRow = new GNM_MODELSERVICETYPEDET
                                {
                                    SERVICETYPE_ID = reader.GetInt32(reader.GetOrdinal("SERVICETYPE_ID"))
                                };
                            }
                        }
                    }

                    if (modelServiceTypeRow == null)
                    {
                        IEnumerable<ParentCompanyObject> ParentCompanyDetails = null;
                        using (SqlCommand cmd = new SqlCommand("Up_GetParentCompanyDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var parentCompanyList = new List<ParentCompanyObject>();
                                while (reader.Read())
                                {
                                    parentCompanyList.Add(new ParentCompanyObject
                                    {
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Company_Name = reader.GetString(reader.GetOrdinal("Company_Name")),
                                        Company_Parent_ID = reader.GetInt32(reader.GetOrdinal("Company_Parent_ID"))
                                    });
                                }
                                ParentCompanyDetails = parentCompanyList;
                            }
                        }

                        var Servicetype = new List<GNM_MODELSERVICETYPEDET>();
                        using (SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_MODELSERVICETYPEDET", conn))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    Servicetype.Add(new GNM_MODELSERVICETYPEDET
                                    {
                                        MODEL_ID = reader.GetInt32(reader.GetOrdinal("MODEL_ID")),
                                        COMPANY_ID = reader.GetInt32(reader.GetOrdinal("COMPANY_ID")),
                                        SERVICETYPE_ID = reader.GetInt32(reader.GetOrdinal("SERVICETYPE_ID"))
                                    });
                                }
                            }
                        }

                        var modelServiceType = (from a in Servicetype
                                                join b in ParentCompanyDetails
                                                on a.COMPANY_ID equals b.Company_ID
                                                where a.MODEL_ID == SelectParticularModelHeaderObj.modelID
                                                select new GNM_MODELSERVICETYPEDET() { SERVICETYPE_ID = a.SERVICETYPE_ID }).ToList();

                        if (modelServiceType.Count > 0)
                        {
                            modelServiceTypeRow = modelServiceType.FirstOrDefault();
                        }
                        else
                        {
                            modelServiceTypeRow = null;
                        }
                    }

                    if (IsSalesModule)
                    {
                        // Example for Supplier Price
                        using (SqlCommand cmd = new SqlCommand("Up_GetSupplierPriceDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@modelID", SelectParticularModelHeaderObj.modelID);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                            var supplierPrices = new List<decimal>();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    supplierPrices.Add(reader.GetDecimal(reader.GetOrdinal("SupplierPrice")));
                                }
                            }

                            // Example for Selling Price
                            using (SqlCommand cmdSellingPrice = new SqlCommand("Up_GetModelPriceDetails", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@modelID", SelectParticularModelHeaderObj.modelID);
                                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                                var sellingPrices = new List<decimal>();
                                using (SqlDataReader reader = cmdSellingPrice.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        sellingPrices.Add(reader.GetDecimal(reader.GetOrdinal("SellingPrice")));
                                    }
                                }

                                // Example for Stock Counts
                                using (SqlCommand cmdStockCounts = new SqlCommand("Up_GetProductDetailsByStatusAndCompany", conn))
                                {
                                    cmdStockCounts.CommandType = CommandType.StoredProcedure;
                                    cmdStockCounts.Parameters.AddWithValue("@ModelID", SelectParticularModelHeaderObj.modelID);
                                    cmdStockCounts.Parameters.AddWithValue("@BrandID", SelectParticularModelHeaderObj.Brand_ID);
                                    cmdStockCounts.Parameters.AddWithValue("@ProductTypeID", SelectParticularModelHeaderObj.ProductType_ID);

                                    var stockCounts = new List<int>();
                                    using (SqlDataReader reader = cmdStockCounts.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            stockCounts.Add(reader.GetInt32(reader.GetOrdinal("StockCount")));
                                        }
                                    }

                                    // Use the retrieved data for further calculations or processing
                                    if (supplierPrices.Count > 0)
                                    {
                                        decimal averageSupplierPrice = supplierPrices.Average();
                                        // Use averageSupplierPrice in further calculations or output
                                    }

                                    if (sellingPrices.Count > 0)
                                    {
                                        decimal averageSellingPrice = sellingPrices.Average();
                                        // Use averageSellingPrice in further calculations or output
                                    }

                                    if (stockCounts.Count > 0)
                                    {
                                        int totalStockCount = stockCounts.Sum();
                                        // Use totalStockCount in further calculations or output
                                    }
                                }
                            }
                        }
                    }

                    // Construct the JSON result as per the original logic.
                    jsonResult = (new { APPrice, ASPrice, AvailStockCount, PhysicalStockCount, GITStockCount, OWHStockCount, UCLStockCount });
                }
            }
            catch (SqlException sqlEx)
            {
                // Handle SQL-specific exceptions
                LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.GetType().FullName + ":" + sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
                // Log the exception or handle it appropriately
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                // Log the exception or handle it appropriately
            }

            //return jsonResult;
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: Select Company Available stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectAvailableStock(SelectAvailableStockList SelectAvailableStockObj, string constring, int LogException, int page, int rows)
        {
            int Company_ID = Convert.ToInt32(SelectAvailableStockObj.Company_ID);
            try
            {
                int AvailableSerialStatus = 0;
                int GITSerialStatus = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    // Open the connection
                    connection.Open();

                    // Fetch AvailableSerialStatus
                    string availableStatusQuery = @"
                SELECT RefMasterDetail_ID 
                FROM GNM_RefMasterDetail 
                WHERE RefMasterDetail_Short_Name = 'AVL' 
                AND RefMaster_ID = (
                    SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SERIALSTATUS')";
                    using (SqlCommand availableStatusCommand = new SqlCommand(availableStatusQuery, connection))
                    {
                        AvailableSerialStatus = (int)availableStatusCommand.ExecuteScalar();
                    }

                    // Fetch GITSerialStatus
                    string gitStatusQuery = @"
                SELECT RefMasterDetail_ID 
                FROM GNM_RefMasterDetail 
                WHERE RefMasterDetail_Short_Name = 'GIT' 
                AND RefMaster_ID = (
                    SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SERIALSTATUS')";
                    using (SqlCommand gitStatusCommand = new SqlCommand(gitStatusQuery, connection))
                    {
                        GITSerialStatus = (int)gitStatusCommand.ExecuteScalar();
                    }

                    // Main query for Available Stock
                    string AvailableQuery = @"
                SELECT E.COMPANY_ID, E.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN GNM_WAREHOUSE E ON A.WAREHOUSE_ID = E.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL F ON A.SERIALSTATUS = F.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @AvailableSerialStatus 
                AND E.COMPANY_ID = @Company_ID 
                AND E.WAREHOUSETYPE_ID IN (2, 4) 
                AND A.MODEL_ID = @ModelID
                UNION ALL
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN SLT_PI_MACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID 
                    AND E.BRAND_ID = A.BRAND_ID 
                    AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID 
                    AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_PURCHASEINVOICE F ON E.PURCHASEINVOICE_ID = F.PURCHASEINVOICE_ID 
                LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @GITSerialStatus 
                AND F.COMPANY_ID = @Company_ID 
                AND A.MODEL_ID = @ModelID 
                AND E.BONDING_ID IS NULL 
                AND E.PURCHASEGRN_ID IS NULL 
                AND E.GOODSRELEASENOTE_ID IS NULL 
                AND E.BILLOFENTRY_ID IS NULL";

                    // Execute main query and fetch results
                    List<StockDetails> stockDetailsList = new List<StockDetails>();
                    using (SqlCommand command = new SqlCommand(AvailableQuery, connection))
                    {
                        command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                        command.Parameters.AddWithValue("@GITSerialStatus", GITSerialStatus);
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                        command.Parameters.AddWithValue("@ModelID", SelectAvailableStockObj.modelID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                StockDetails stockDetails = new StockDetails
                                {
                                    COMPANY_ID = reader.GetInt32(0),
                                    BRANCH_ID = reader.GetInt32(1),
                                    WAREHOUSE_ID = reader.GetInt32(2),
                                    PRODUCT_SERIALNUMBER = reader.GetString(3),
                                    SERIALSTATUS = reader.GetInt32(4)
                                };
                                stockDetailsList.Add(stockDetails);
                            }
                        }
                    }

                    // Fetch company names based on languageid
                    string companyNameQuery;
                    if (SelectAvailableStockObj.languageid == Convert.ToInt32(SelectAvailableStockObj.GeneralLanguageID))
                    {
                        companyNameQuery = "SELECT Company_Name FROM GNM_Company WHERE Company_ID = @Company_ID";
                    }
                    else
                    {
                        companyNameQuery = "SELECT Company_Name FROM GNM_CompanyLocale WHERE Company_ID = @Company_ID AND Language_ID = @Language_ID";
                    }

                    Dictionary<int, string> companyNames = new Dictionary<int, string>();

                    foreach (var stock in stockDetailsList.GroupBy(p => p.COMPANY_ID))
                    {
                        string companyName = "";
                        using (SqlCommand command = new SqlCommand(companyNameQuery, connection))
                        {
                            command.Parameters.AddWithValue("@Company_ID", stock.Key);
                            if (SelectAvailableStockObj.languageid != Convert.ToInt32(SelectAvailableStockObj.GeneralLanguageID))
                            {
                                command.Parameters.AddWithValue("@Language_ID", SelectAvailableStockObj.languageid);
                            }

                            // Execute the command and get the company name
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    companyName = reader.GetString(0);
                                }
                            }
                        }
                        // Store company name in a dictionary for reuse
                        companyNames[stock.Key] = companyName;
                    }

                    // Pagination calculation
                    int count = stockDetailsList.Count;
                    int total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    // Prepare the final JSON result
                    var jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = stockDetailsList
                            .GroupBy(p => p.COMPANY_ID)
                            .Select(final => new
                            {
                                CompanyID = final.Key,
                                CompanyName = companyNames.ContainsKey(final.Key) ? companyNames[final.Key] : "",
                                CompanyAvailableStock = final.Count()
                            }).ToList()
                    };

                    //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                    return new JsonResult(jsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Branch wise available stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectBranchWise(SelectBranchWiseList SelectBranchWiseObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                // Define connection string
                int Company_ID = Convert.ToInt32(SelectBranchWiseObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectBranchWiseObj.Branch);
                int AvailableSerialStatus = 0;
                int GITSerialStatus = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch AvailableSerialStatus
                    string availableStatusQuery = @"
                SELECT RefMasterDetail_ID 
                FROM GNM_RefMasterDetail 
                WHERE RefMasterDetail_Short_Name = 'AVL' 
                AND RefMaster_ID = (
                    SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SERIALSTATUS')";
                    using (SqlCommand availableStatusCommand = new SqlCommand(availableStatusQuery, connection))
                    {
                        AvailableSerialStatus = (int)availableStatusCommand.ExecuteScalar();
                    }

                    // Fetch GITSerialStatus
                    string gitStatusQuery = @"
                SELECT RefMasterDetail_ID 
                FROM GNM_RefMasterDetail 
                WHERE RefMasterDetail_Short_Name = 'GIT' 
                AND RefMaster_ID = (
                    SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'SERIALSTATUS')";
                    using (SqlCommand gitStatusCommand = new SqlCommand(gitStatusQuery, connection))
                    {
                        GITSerialStatus = (int)gitStatusCommand.ExecuteScalar();
                    }

                    // Main query for Available Stock
                    string availableQuery = @"
                SELECT E.COMPANY_ID, E.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN GNM_WAREHOUSE E ON A.WAREHOUSE_ID = E.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL F ON A.SERIALSTATUS = F.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @AvailableSerialStatus 
                AND E.COMPANY_ID = @Company_ID 
                AND E.WAREHOUSETYPE_ID IN (2, 4) 
                AND A.MODEL_ID = @ModelID
                UNION ALL
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN SLT_PI_MACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID 
                    AND E.BRAND_ID = A.BRAND_ID 
                    AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID 
                    AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_PURCHASEINVOICE F ON E.PURCHASEINVOICE_ID = F.PURCHASEINVOICE_ID 
                LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @GITSerialStatus 
                AND F.COMPANY_ID = @Company_ID 
                AND A.MODEL_ID = @ModelID 
                AND E.BONDING_ID IS NULL 
                AND E.PURCHASEGRN_ID IS NULL 
                AND E.GOODSRELEASENOTE_ID IS NULL 
                AND E.BILLOFENTRY_ID IS NULL";

                    // Execute main query and fetch results
                    List<StockDetails> stockDetailsList = new List<StockDetails>();
                    using (SqlCommand command = new SqlCommand(availableQuery, connection))
                    {
                        command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                        command.Parameters.AddWithValue("@GITSerialStatus", GITSerialStatus);
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                        command.Parameters.AddWithValue("@ModelID", SelectBranchWiseObj.modelID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                StockDetails stockDetails = new StockDetails
                                {
                                    COMPANY_ID = reader.GetInt32(0),
                                    BRANCH_ID = reader.GetInt32(1),
                                    WAREHOUSE_ID = reader.GetInt32(2),
                                    PRODUCT_SERIALNUMBER = reader.GetString(3),
                                    SERIALSTATUS = reader.GetInt32(4)
                                };
                                stockDetailsList.Add(stockDetails);
                            }
                        }
                    }

                    // Fetch Branch details
                    string branchQuery = "SELECT Branch_ID, Branch_Name FROM GNM_Branch WHERE Company_ID = @Company_ID AND Branch_Active = 1";
                    List<GNM_Branch> branchList = new List<GNM_Branch>();
                    using (SqlCommand branchCommand = new SqlCommand(branchQuery, connection))
                    {
                        branchCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                        using (SqlDataReader reader = branchCommand.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(0),
                                    Branch_Name = reader.GetString(1)
                                });
                            }
                        }
                    }

                    // Group and process available stock data
                    IEnumerable<ModelAvailableStock> modelMasterAvailableStock = from a in stockDetailsList
                                                                                 group a by a.PRODUCT_SERIALNUMBER into final
                                                                                 select new ModelAvailableStock
                                                                                 {
                                                                                     Branch_ID = final.FirstOrDefault().BRANCH_ID,
                                                                                     Count = final.Distinct().Count()
                                                                                 };

                    int count = stockDetailsList.Count();
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling((double)count / rows)) : 0;
                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    // Prepare final JSON result
                    var jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = from a in modelMasterAvailableStock
                               join b in branchList on a.Branch_ID equals b.Branch_ID
                               group a by a.Branch_ID into final
                               select new
                               {
                                   BranchID = final.FirstOrDefault().Branch_ID,
                                   BranchName = branchList.FirstOrDefault(br => br.Branch_ID == final.FirstOrDefault().Branch_ID)?.Branch_Name,
                                   BranchAvailableStock = final.Sum(pc => pc.Count)
                               }
                    };

                    //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                    return new JsonResult(jsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 

        public static IActionResult SelectSerialNumber(SelectSerialNumberList SelectSerialNumberObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;
                int Company_ID = Convert.ToInt32(SelectSerialNumberObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectSerialNumberObj.Branch);

                // Initialize ADO.NET objects
                List<StockDetails> IEProductAvailableStock = new List<StockDetails>();
                List<StockDetails> IEModelMasterPhysicalStock = new List<StockDetails>();

                // Get AvailableSerialStatus and GITSerialStatus
                int AvailableSerialStatus, GITSerialStatus;
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    var command = new SqlCommand("SELECT rmd.RefMasterDetail_ID\r\nFROM GNM_RefMasterDetail rmd\r\nJOIN GNM_RefMaster rm ON rmd.RefMaster_ID = rm.RefMaster_ID\r\nWHERE rm.RefMaster_Name = @RefMasterName \r\nAND rmd.RefMasterDetail_Short_Name = @ShortName", connection);
                    command.Parameters.AddWithValue("@RefMasterName", "SERIALSTATUS");
                    command.Parameters.AddWithValue("@ShortName", "AVL");
                    AvailableSerialStatus = (int)command.ExecuteScalar();

                    command.Parameters["@ShortName"].Value = "GIT";
                    GITSerialStatus = (int)command.ExecuteScalar();
                }

                // Query for available stock
                string AvailableQuery = @"
            SELECT E.COMPANY_ID, E.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS
            FROM GNM_PRODUCT A
            JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID
            JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID
            JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID
            JOIN GNM_WAREHOUSE E ON A.WAREHOUSE_ID = E.WAREHOUSE_ID
            JOIN GNM_REFMASTERDETAIL F ON A.SERIALSTATUS = F.REFMASTERDETAIL_ID
            WHERE A.SERIALSTATUS = @AvailableSerialStatus
            AND E.COMPANY_ID = @CompanyID
            AND E.WAREHOUSETYPE_ID IN (2, 4)
            AND A.MODEL_ID = @ModelID
            UNION ALL
            SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS
            FROM GNM_PRODUCT A
            JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID
            JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID
            JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID
            JOIN SLT_PI_MACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID
            AND E.BRAND_ID = A.BRAND_ID
            AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID
            AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER
            JOIN SLT_PURCHASEINVOICE F ON E.PURCHASEINVOICE_ID = F.PURCHASEINVOICE_ID
            LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID
            JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID
            WHERE A.SERIALSTATUS = @GITSerialStatus
            AND F.COMPANY_ID = @CompanyID
            AND A.MODEL_ID = @ModelID
            AND E.BONDING_ID IS NULL
            AND E.PURCHASEGRN_ID IS NULL
            AND E.GOODSRELEASENOTE_ID IS NULL
            AND E.BILLOFENTRY_ID IS NULL";

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    var command = new SqlCommand(AvailableQuery, connection);
                    command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                    command.Parameters.AddWithValue("@GITSerialStatus", GITSerialStatus);
                    command.Parameters.AddWithValue("@CompanyID", Company_ID);
                    command.Parameters.AddWithValue("@ModelID", SelectSerialNumberObj.modelID);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            IEProductAvailableStock.Add(new StockDetails
                            {
                                PRODUCT_ID = reader.GetInt32(0),
                                PRODUCT_SERIALNUMBER = reader.GetString(3),
                                SERIALSTATUS = reader.GetInt32(4),
                                WAREHOUSE_ID = reader.GetInt32(2),
                            });
                        }
                    }
                }

                // Join with Branch data
                List<GNM_Branch> BranchList = new List<GNM_Branch>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    var command = new SqlCommand("SELECT * FROM GNM_Branch WHERE Company_ID = @CompanyID AND Branch_Active = 1 AND Branch_ID = @BranchID", connection);
                    command.Parameters.AddWithValue("@CompanyID", Company_ID);
                    command.Parameters.AddWithValue("@BranchID", SelectSerialNumberObj.BranchID);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            BranchList.Add(new GNM_Branch
                            {
                                Branch_ID = reader.GetInt32(0),
                                Branch_Name = reader.GetString(1),
                            });
                        }
                    }
                }

                // Apply filters
                foreach (var item in IEProductAvailableStock)
                {
                    var branch = BranchList.FirstOrDefault(b => b.Branch_ID == item.BRANCH_ID);
                    if (branch != null)
                    {
                        IEModelMasterPhysicalStock.Add(new StockDetails
                        {
                            PRODUCT_ID = item.PRODUCT_ID,
                            PRODUCT_SERIALNUMBER = item.PRODUCT_SERIALNUMBER,
                            SERIALSTATUS = item.SERIALSTATUS,
                            WAREHOUSE_ID = item.WAREHOUSE_ID,
                        });
                    }
                }

                // Fetch status and warehouse names
                var statusDict = new Dictionary<int, string>();
                var warehouseDict = new Dictionary<int, string>();

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch Status
                    var command = new SqlCommand("SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetail", connection);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            statusDict.Add(reader.GetInt32(0), reader.GetString(1));
                        }
                    }

                    // Fetch Warehouse
                    command.CommandText = "SELECT WareHouse_ID, WareHouseName FROM GNM_WareHouse";
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            warehouseDict.Add(reader.GetInt32(0), reader.GetString(1));
                        }
                    }
                }

                // Apply localization
                if (SelectSerialNumberObj.languageid != Convert.ToInt32(SelectSerialNumberObj.GeneralLanguageID))
                {
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();

                        // Fetch localized Status
                        var command = new SqlCommand("SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE Language_ID = @LanguageID", connection);
                        command.Parameters.AddWithValue("@LanguageID", SelectSerialNumberObj.languageid);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                statusDict[reader.GetInt32(0)] = reader.GetString(1);
                            }
                        }

                        // Fetch localized Warehouse
                        command.CommandText = "SELECT WareHouse_ID, WareHouseName FROM GNM_WareHouseLocale WHERE Language_ID = @LanguageID";
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouseDict[reader.GetInt32(0)] = reader.GetString(1);
                            }
                        }
                    }
                }

                // Prepare the final list
                var result = IEModelMasterPhysicalStock.Select(a => new
                {
                    ProductID = a.PRODUCT_ID,
                    SerialNumber = a.PRODUCT_SERIALNUMBER,
                    Status = statusDict.ContainsKey(a.SERIALSTATUS) ? statusDict[a.SERIALSTATUS] : null,
                    WareHouse = warehouseDict.ContainsKey((int)a.WAREHOUSE_ID) ? warehouseDict[(int)a.WAREHOUSE_ID] : null,
                }).ToList();

                // Paginate and prepare the result
                count = result.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = result.Skip((page - 1) * rows).Take(rows).ToList()
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Select Company Physical stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectPhysicalStock(SelectPhysicalStockList SelectPhysicalStockObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;
                int Company_ID = Convert.ToInt32(SelectPhysicalStockObj.Company_ID);

                // Define the available, allocated, and sold serial status IDs
                int AvailableSerialStatus;
                int AllocatedSerialStatus;
                int SoldSerialStatus;

                // Retrieve serial status IDs
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string serialStatusQuery = @"
                SELECT
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'AVL' THEN RefMasterDetail_ID END) AS AvailableSerialStatus,
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'ALC' THEN RefMasterDetail_ID END) AS AllocatedSerialStatus,
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'SLD' THEN RefMasterDetail_ID END) AS SoldSerialStatus
                FROM GNM_RefMasterDetail
                WHERE GNM_RefMaster.RefMaster_Name = 'SERIALSTATUS'";

                    using (var command = new SqlCommand(serialStatusQuery, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                AvailableSerialStatus = reader.GetInt32(reader.GetOrdinal("AvailableSerialStatus"));
                                AllocatedSerialStatus = reader.GetInt32(reader.GetOrdinal("AllocatedSerialStatus"));
                                SoldSerialStatus = reader.GetInt32(reader.GetOrdinal("SoldSerialStatus"));
                            }
                            else
                            {
                                // Handle case when no rows are returned
                                throw new Exception("Serial status IDs not found.");
                            }
                        }
                    }
                }

                // Define the query to retrieve physical stock data
                string physicalQuery = @"
            SELECT PRODUCT_ID, PRODUCT_SERIALNUMBER, WAREHOUSE_ID, SERIALSTATUS
            FROM GNM_PRODUCT
            WHERE SERIALSTATUS IN (@AvailableSerialStatus, @AllocatedSerialStatus)
            AND WAREHOUSE_ID IS NOT NULL
            AND ISACTIVE = 1
            AND MODEL_ID = @ModelID
            AND BRAND_ID = @BrandID
            AND PRODUCTTYPE_ID = @ProductTypeID";

                // Retrieve physical stock data
                List<StockDetails> stockDetailsList = new List<StockDetails>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var command = new SqlCommand(physicalQuery, connection))
                    {
                        command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                        command.Parameters.AddWithValue("@AllocatedSerialStatus", AllocatedSerialStatus);
                        command.Parameters.AddWithValue("@ModelID", SelectPhysicalStockObj.modelID);
                        command.Parameters.AddWithValue("@BrandID", SelectPhysicalStockObj.Brand_ID);
                        command.Parameters.AddWithValue("@ProductTypeID", SelectPhysicalStockObj.ProductType_ID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stockDetailsList.Add(new StockDetails
                                {
                                    PRODUCT_ID = reader.GetInt32(reader.GetOrdinal("PRODUCT_ID")),
                                    PRODUCT_SERIALNUMBER = reader.GetString(reader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                                    WAREHOUSE_ID = reader.GetInt32(reader.GetOrdinal("WAREHOUSE_ID")),
                                    SERIALSTATUS = reader.GetInt32(reader.GetOrdinal("SERIALSTATUS"))
                                });
                            }
                        }
                    }
                }

                // Retrieve warehouse and company names
                List<GNM_WareHouse> wareHouseList = new List<GNM_WareHouse>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string wareHouseQuery = @"
                SELECT * FROM GNM_WareHouse
                WHERE Company_ID = @CompanyID
                AND IsActive = 1
                AND WareHouseType_ID = 4";

                    using (var command = new SqlCommand(wareHouseQuery, connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                wareHouseList.Add(new GNM_WareHouse
                                {
                                    WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                    WareHouseName = reader.GetString(reader.GetOrdinal("WareHouseName"))
                                });
                            }
                        }
                    }
                }

                // Retrieve company name based on language ID
                string companyName = "";
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string companyNameQuery = @"
                SELECT
                    CASE WHEN @LanguageID = @GeneralLanguageID
                    THEN (SELECT Company_Name FROM GNM_Company WHERE Company_ID = @CompanyID)
                    ELSE (SELECT Company_Name FROM GNM_CompanyLocale WHERE Company_ID = @CompanyID AND Language_ID = @LanguageID)
                    END AS CompanyName";

                    using (var command = new SqlCommand(companyNameQuery, connection))
                    {
                        command.Parameters.AddWithValue("@LanguageID", SelectPhysicalStockObj.languageid);
                        command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(SelectPhysicalStockObj.GeneralLanguageID));
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                companyName = reader.GetString(reader.GetOrdinal("CompanyName"));
                            }
                        }
                    }
                }

                // Calculate count and total pages
                count = stockDetailsList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                // Prepare JSON result
                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from p in stockDetailsList
                            join w in wareHouseList on p.WAREHOUSE_ID equals w.WareHouse_ID
                            group w by Company_ID into final
                            select new
                            {
                                CompanyID = Company_ID,
                                CompanyName = companyName,
                                CompanyPhysicalStock = final.Count()
                            })
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Branch wise physical stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 

        public static IActionResult SelectBranchWisePhysicalStock(SelectBranchWisePhysicalStockList SelectBranchWisePhysicalStockObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;
                int Company_ID = Convert.ToInt32(SelectBranchWisePhysicalStockObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectBranchWisePhysicalStockObj.Branch);

                // Define the serial status IDs
                int AvailableSerialStatus;
                int AllocatedSerialStatus;
                int SoldSerialStatus;

                // Retrieve serial status IDs
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string serialStatusQuery = @"
                SELECT
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'AVL' THEN RefMasterDetail_ID END) AS AvailableSerialStatus,
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'ALC' THEN RefMasterDetail_ID END) AS AllocatedSerialStatus,
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'SLD' THEN RefMasterDetail_ID END) AS SoldSerialStatus
                FROM GNM_RefMasterDetail
                WHERE GNM_RefMaster.RefMaster_Name = 'SERIALSTATUS'";

                    using (var command = new SqlCommand(serialStatusQuery, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                AvailableSerialStatus = reader.GetInt32(reader.GetOrdinal("AvailableSerialStatus"));
                                AllocatedSerialStatus = reader.GetInt32(reader.GetOrdinal("AllocatedSerialStatus"));
                                SoldSerialStatus = reader.GetInt32(reader.GetOrdinal("SoldSerialStatus"));
                            }
                            else
                            {
                                throw new Exception("Serial status IDs not found.");
                            }
                        }
                    }
                }

                // Define the query to retrieve physical stock data
                string physicalQuery = @"
            SELECT PRODUCT_ID, PRODUCT_SERIALNUMBER, WAREHOUSE_ID, SERIALSTATUS
            FROM GNM_PRODUCT
            WHERE SERIALSTATUS IN (@AvailableSerialStatus, @AllocatedSerialStatus)
            AND WAREHOUSE_ID IS NOT NULL
            AND ISACTIVE = 1
            AND MODEL_ID = @ModelID
            AND BRAND_ID = @BrandID
            AND PRODUCTTYPE_ID = @ProductTypeID";

                // Retrieve physical stock data
                List<StockDetails> stockDetailsList = new List<StockDetails>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var command = new SqlCommand(physicalQuery, connection))
                    {
                        command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                        command.Parameters.AddWithValue("@AllocatedSerialStatus", AllocatedSerialStatus);
                        command.Parameters.AddWithValue("@ModelID", SelectBranchWisePhysicalStockObj.modelID);
                        command.Parameters.AddWithValue("@BrandID", SelectBranchWisePhysicalStockObj.Brand_ID);
                        command.Parameters.AddWithValue("@ProductTypeID", SelectBranchWisePhysicalStockObj.ProductType_ID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stockDetailsList.Add(new StockDetails
                                {
                                    PRODUCT_ID = reader.GetInt32(reader.GetOrdinal("PRODUCT_ID")),
                                    PRODUCT_SERIALNUMBER = reader.GetString(reader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                                    WAREHOUSE_ID = reader.GetInt32(reader.GetOrdinal("WAREHOUSE_ID")),
                                    SERIALSTATUS = reader.GetInt32(reader.GetOrdinal("SERIALSTATUS"))
                                });
                            }
                        }
                    }
                }

                // Retrieve warehouse details
                List<GNM_WareHouse> wareHouseList = new List<GNM_WareHouse>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string wareHouseQuery = @"
                SELECT * FROM GNM_WareHouse
                WHERE Company_ID = @CompanyID
                AND IsActive = 1
                AND WareHouseType_ID = 4";

                    using (var command = new SqlCommand(wareHouseQuery, connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                wareHouseList.Add(new GNM_WareHouse
                                {
                                    WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID"))
                                });
                            }
                        }
                    }
                }

                // Retrieve branch details
                List<GNM_Branch> branchList = new List<GNM_Branch>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string branchQuery = @"
                SELECT * FROM GNM_Branch
                WHERE Company_ID = @CompanyID
                AND Branch_Active = 1";

                    using (var command = new SqlCommand(branchQuery, connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name"))
                                });
                            }
                        }
                    }
                }

                // Calculate count and total pages
                var modelMasterPhysicalStockList = new List<ModelAvailableStock>();
                if (SelectBranchWisePhysicalStockObj.languageid == Convert.ToInt32(SelectBranchWisePhysicalStockObj.GeneralLanguageID))
                {
                    modelMasterPhysicalStockList = (from a in stockDetailsList
                                                    join b in wareHouseList on a.WAREHOUSE_ID equals b.WareHouse_ID
                                                    join c in branchList on b.Branch_ID equals c.Branch_ID
                                                    group a by b.WareHouse_ID into final
                                                    select new ModelAvailableStock
                                                    {
                                                        Branch_ID = wareHouseList.Where(w => w.WareHouse_ID == final.FirstOrDefault().WAREHOUSE_ID).Select(br => br.Branch_ID).FirstOrDefault(),
                                                        ProcuctCount = final.Distinct().Count(),
                                                    }).ToList();
                }
                else
                {
                    modelMasterPhysicalStockList = (from a in stockDetailsList
                                                    join b in wareHouseList on a.WAREHOUSE_ID equals b.WareHouse_ID
                                                    join c in branchList on b.Branch_ID equals c.Branch_ID
                                                    group a by b.WareHouse_ID into final
                                                    select new ModelAvailableStock
                                                    {
                                                        Branch_ID = wareHouseList.Where(w => w.WareHouse_ID == final.FirstOrDefault().WAREHOUSE_ID).Select(br => br.Branch_ID).FirstOrDefault(),
                                                        ProcuctCount = final.Distinct().Count(),
                                                    }).ToList();

                    // Retrieve branch locale details if language is not general
                    List<GNM_BranchLocale> branchLocaleList = new List<GNM_BranchLocale>();
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        string branchLocaleQuery = @"
                    SELECT * FROM GNM_BranchLocale
                    WHERE GNM_Branch.Company_ID = @CompanyID
                    AND GNM_Branch.Branch_Active = 1";

                        using (var command = new SqlCommand(branchLocaleQuery, connection))
                        {
                            command.Parameters.AddWithValue("@CompanyID", Company_ID);
                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    branchLocaleList.Add(new GNM_BranchLocale
                                    {
                                        Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                        Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name"))
                                    });
                                }
                            }
                        }
                    }

                    // Prepare JSON result with branch locale names
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = from a in modelMasterPhysicalStockList
                               join b in branchList on a.Branch_ID equals b.Branch_ID
                               group a by a.Branch_ID into final
                               select new
                               {
                                   BranchID = final.FirstOrDefault().Branch_ID,
                                   BranchName = branchLocaleList.Where(br => br.Branch_ID == final.FirstOrDefault().Branch_ID).Select(bn => bn.Branch_Name).FirstOrDefault(),
                                   BranchPhysicalStock = final.Sum(pc => pc.ProcuctCount)
                               }
                    };
                }

                // Calculate count and total pages
                count = modelMasterPhysicalStockList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                // Prepare JSON result
                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = from a in modelMasterPhysicalStockList
                           join b in branchList on a.Branch_ID equals b.Branch_ID
                           group a by a.Branch_ID into final
                           select new
                           {
                               BranchID = final.FirstOrDefault().Branch_ID,
                               BranchName = (SelectBranchWisePhysicalStockObj.languageid == Convert.ToInt32(SelectBranchWisePhysicalStockObj.GeneralLanguageID))
                                            ? branchList.Where(br => br.Branch_ID == final.FirstOrDefault().Branch_ID).Select(bn => bn.Branch_Name).FirstOrDefault()
                                            : branchList.Where(br => br.Branch_ID == final.FirstOrDefault().Branch_ID).Select(bn => bn.Branch_Name).FirstOrDefault(),
                               BranchPhysicalStock = final.Sum(pc => pc.ProcuctCount)
                           }
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Warehouser /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 

        public static IActionResult SelectWarehouseWisePhysicalStock(SelectWarehouseWisePhysicalStockList SelectWarehouseWisePhysicalStockObj, string constring, int LogException, int page, int rows)
        {
            var jsonResult = default(dynamic);
            try
            {
                // Define variables to hold IDs
                int Company_ID = Convert.ToInt32(SelectWarehouseWisePhysicalStockObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectWarehouseWisePhysicalStockObj.Branch);

                // Variables to hold serial status IDs
                int AvailableSerialStatus;
                int AllocatedSerialStatus;
                int SoldSerialStatus;

                // Retrieve serial status IDs
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string serialStatusQuery = @"
                SELECT
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'AVL' THEN RefMasterDetail_ID END) AS AvailableSerialStatus,
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'ALC' THEN RefMasterDetail_ID END) AS AllocatedSerialStatus,
                    MAX(CASE WHEN RefMasterDetail_Short_Name = 'SLD' THEN RefMasterDetail_ID END) AS SoldSerialStatus
                FROM GNM_RefMasterDetail
                WHERE GNM_RefMaster.RefMaster_Name = 'SERIALSTATUS'";

                    using (var command = new SqlCommand(serialStatusQuery, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                AvailableSerialStatus = reader.GetInt32(reader.GetOrdinal("AvailableSerialStatus"));
                                AllocatedSerialStatus = reader.GetInt32(reader.GetOrdinal("AllocatedSerialStatus"));
                                SoldSerialStatus = reader.GetInt32(reader.GetOrdinal("SoldSerialStatus"));
                            }
                            else
                            {
                                throw new Exception("Serial status IDs not found.");
                            }
                        }
                    }
                }

                // Retrieve physical stock data
                List<StockDetails> stockDetailsList = new List<StockDetails>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string physicalQuery = @"
                SELECT PRODUCT_ID, PRODUCT_SERIALNUMBER, WAREHOUSE_ID, SERIALSTATUS
                FROM GNM_PRODUCT
                WHERE SERIALSTATUS IN (@AvailableSerialStatus, @AllocatedSerialStatus)
                AND WAREHOUSE_ID IS NOT NULL
                AND ISACTIVE = 1
                AND MODEL_ID = @ModelID
                AND BRAND_ID = @BrandID
                AND PRODUCTTYPE_ID = @ProductTypeID";

                    using (var command = new SqlCommand(physicalQuery, connection))
                    {
                        command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                        command.Parameters.AddWithValue("@AllocatedSerialStatus", AllocatedSerialStatus);
                        command.Parameters.AddWithValue("@ModelID", SelectWarehouseWisePhysicalStockObj.modelID);
                        command.Parameters.AddWithValue("@BrandID", SelectWarehouseWisePhysicalStockObj.Brand_ID);
                        command.Parameters.AddWithValue("@ProductTypeID", SelectWarehouseWisePhysicalStockObj.ProductType_ID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stockDetailsList.Add(new StockDetails
                                {
                                    PRODUCT_ID = reader.GetInt32(reader.GetOrdinal("PRODUCT_ID")),
                                    PRODUCT_SERIALNUMBER = reader.GetString(reader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                                    WAREHOUSE_ID = reader.GetInt32(reader.GetOrdinal("WAREHOUSE_ID")),
                                    SERIALSTATUS = reader.GetInt32(reader.GetOrdinal("SERIALSTATUS"))
                                });
                            }
                        }
                    }
                }

                // Retrieve warehouse details
                List<GNM_WareHouse> wareHouseList = new List<GNM_WareHouse>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string wareHouseQuery = @"
                SELECT * FROM GNM_WareHouse
                WHERE Company_ID = @CompanyID
                AND IsActive = 1
                AND WareHouseType_ID = 4";

                    using (var command = new SqlCommand(wareHouseQuery, connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                wareHouseList.Add(new GNM_WareHouse
                                {
                                    WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    WareHouseName = reader.GetString(reader.GetOrdinal("WareHouseName"))
                                });
                            }
                        }
                    }
                }

                // Retrieve branch details
                List<GNM_Branch> branchList = new List<GNM_Branch>();
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string branchQuery = @"
                SELECT * FROM GNM_Branch
                WHERE Company_ID = @CompanyID
                AND Branch_Active = 1
                AND Branch_ID = @BranchID";

                    using (var command = new SqlCommand(branchQuery, connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);
                        command.Parameters.AddWithValue("@BranchID", SelectWarehouseWisePhysicalStockObj.BranchID);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name"))
                                });
                            }
                        }
                    }
                }

                // Initialize wareHouseLocaleList
                List<GNM_WareHouseLocale> wareHouseLocaleList = new List<GNM_WareHouseLocale>();

                // Process results based on language
                var modelMasterPhysicalStockList = new List<ModelAvailableStock>();
                if (SelectWarehouseWisePhysicalStockObj.languageid == Convert.ToInt32(SelectWarehouseWisePhysicalStockObj.GeneralLanguageID))
                {
                    modelMasterPhysicalStockList = (from a in stockDetailsList
                                                    join b in wareHouseList on a.WAREHOUSE_ID equals b.WareHouse_ID
                                                    join c in branchList on b.Branch_ID equals c.Branch_ID
                                                    group a by b.WareHouse_ID into final
                                                    select new ModelAvailableStock
                                                    {
                                                        Warehouse_ID = final.FirstOrDefault().WAREHOUSE_ID,
                                                        ProcuctCount = final.Distinct().Count(),
                                                    }).ToList();
                }
                else
                {
                    modelMasterPhysicalStockList = (from a in stockDetailsList
                                                    join b in wareHouseList on a.WAREHOUSE_ID equals b.WareHouse_ID
                                                    join c in branchList on b.Branch_ID equals c.Branch_ID
                                                    group a by b.WareHouse_ID into final
                                                    select new ModelAvailableStock
                                                    {
                                                        Warehouse_ID = final.FirstOrDefault().WAREHOUSE_ID,
                                                        ProcuctCount = final.Distinct().Count(),
                                                    }).ToList();

                    // Retrieve warehouse locale details if language is not general
                    using (var connection = new SqlConnection(constring))
                    {
                        connection.Open();
                        string wareHouseLocaleQuery = @"
                    SELECT * FROM GNM_WareHouseLocale
                    WHERE GNM_WareHouse.Company_ID = @CompanyID
                    AND GNM_WareHouse.IsActive = 1
                    AND GNM_WareHouse.WareHouseType_ID = 4";

                        using (var command = new SqlCommand(wareHouseLocaleQuery, connection))
                        {
                            command.Parameters.AddWithValue("@CompanyID", Company_ID);
                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    wareHouseLocaleList.Add(new GNM_WareHouseLocale
                                    {
                                        WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                        WareHouseName = reader.GetString(reader.GetOrdinal("WareHouseName"))
                                    });
                                }
                            }
                        }
                    }

                    jsonResult = new
                    {
                        data = from a in modelMasterPhysicalStockList
                               join b in wareHouseList on a.Warehouse_ID equals b.WareHouse_ID
                               select new
                               {
                                   WarehouseID = a.Warehouse_ID,
                                   Warehouse = wareHouseLocaleList.Where(wl => wl.WareHouse_ID == a.Warehouse_ID).Select(wln => wln.WareHouseName).FirstOrDefault(),
                                   ProcuctCount = a.ProcuctCount
                               }
                    };
                }

                // Calculate count and total pages
                int count = modelMasterPhysicalStockList.Count;
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = from a in modelMasterPhysicalStockList
                           join b in wareHouseList on a.Warehouse_ID equals b.WareHouse_ID
                           select new
                           {
                               WarehouseID = a.Warehouse_ID,
                               Warehouse = (SelectWarehouseWisePhysicalStockObj.languageid == Convert.ToInt32(SelectWarehouseWisePhysicalStockObj.GeneralLanguageID))
                                            ? b.WareHouseName
                                            : wareHouseLocaleList.Where(wl => wl.WareHouse_ID == a.Warehouse_ID).Select(wln => wln.WareHouseName).FirstOrDefault(),
                               ProcuctCount = a.ProcuctCount
                           }
                };

                // Return Json result
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region::: SelectSerialNumberOfPhysicalStock /Mithun:::
        /// <summary>
        ///  SelectSerialNumberOfPhysicalStock
        /// </summary>

        public static IActionResult SelectSerialNumberOfPhysicalStock(SelectSerialNumberOfPhysicalStockList SelectSerialNumberOfPhysicalStockObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                int AvailableSerialStatus, AllocatedSerialStatus;

                // Fetch status IDs
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch AvailableSerialStatus
                    using (SqlCommand command = new SqlCommand(@"SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE GNM_RefMaster.RefMaster_Name = 'SERIALSTATUS' AND RefMasterDetail_Short_Name = 'AVL'", connection))
                    {
                        AvailableSerialStatus = (int)command.ExecuteScalar();
                    }

                    // Fetch AllocatedSerialStatus
                    using (SqlCommand command = new SqlCommand(@"SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE GNM_RefMaster.RefMaster_Name = 'SERIALSTATUS' AND RefMasterDetail_Short_Name = 'ALC'", connection))
                    {
                        AllocatedSerialStatus = (int)command.ExecuteScalar();
                    }
                }

                // Query to fetch product details
                string query = @"SELECT PRODUCT_ID, PRODUCT_SERIALNUMBER, WAREHOUSE_ID, SERIALSTATUS 
                         FROM GNM_PRODUCT 
                         WHERE SERIALSTATUS IN (@AvailableSerialStatus, @AllocatedSerialStatus) 
                         AND WAREHOUSE_ID = @WareHouseID 
                         AND ISACTIVE = 1 
                         AND MODEL_ID = @modelID 
                         AND BRAND_ID = @Brand_ID 
                         AND PRODUCTTYPE_ID = @ProductType_ID";

                List<StockDetails> stockDetailsList = new List<StockDetails>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                    command.Parameters.AddWithValue("@AllocatedSerialStatus", AllocatedSerialStatus);
                    command.Parameters.AddWithValue("@WareHouseID", SelectSerialNumberOfPhysicalStockObj.WareHouseID);
                    command.Parameters.AddWithValue("@modelID", SelectSerialNumberOfPhysicalStockObj.modelID);
                    command.Parameters.AddWithValue("@Brand_ID", SelectSerialNumberOfPhysicalStockObj.Brand_ID);
                    command.Parameters.AddWithValue("@ProductType_ID", SelectSerialNumberOfPhysicalStockObj.ProductType_ID);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        stockDetailsList.Add(new StockDetails
                        {
                            PRODUCT_ID = reader.GetInt32(0),
                            PRODUCT_SERIALNUMBER = reader.GetString(1),
                            WAREHOUSE_ID = reader.GetInt32(2),
                            SERIALSTATUS = reader.GetInt32(3)
                        });
                    }
                    reader.Close();
                }

                // Convert to IQueryable
                var IQModelMasterPhysicalStock = stockDetailsList.AsQueryable();

                // Fetch localization data
                Dictionary<int, string> statusLocalization = new Dictionary<int, string>();
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand();
                    command.Connection = connection;
                    connection.Open();

                    // Fetch localization for current language
                    command.CommandText = @"SELECT RefMasterDetail_ID, RefMasterDetail_Name 
                                    FROM GNM_RefMasterDetail 
                                    WHERE RefMasterDetail_ID IN 
                                          (SELECT DISTINCT SERIALSTATUS FROM GNM_PRODUCT 
                                           WHERE SERIALSTATUS IN (@AvailableSerialStatus, @AllocatedSerialStatus))";
                    command.Parameters.AddWithValue("@AvailableSerialStatus", AvailableSerialStatus);
                    command.Parameters.AddWithValue("@AllocatedSerialStatus", AllocatedSerialStatus);
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        statusLocalization[reader.GetInt32(0)] = reader.GetString(1);
                    }
                    reader.Close();

                    // Fetch localization for the specified language
                    command.CommandText = @"SELECT RefMasterDetail_ID, RefMasterDetail_Name 
                                    FROM GNM_RefMasterDetailLocale 
                                    WHERE Language_ID = @Language_ID 
                                    AND RefMasterDetail_ID IN 
                                          (SELECT DISTINCT SERIALSTATUS FROM GNM_PRODUCT 
                                           WHERE SERIALSTATUS IN (@AvailableSerialStatus, @AllocatedSerialStatus))";
                    command.Parameters.AddWithValue("@Language_ID", SelectSerialNumberOfPhysicalStockObj.languageid);
                    reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        statusLocalization[reader.GetInt32(0)] = reader.GetString(1);
                    }
                }

                // Apply localization and projection
                var localizedStockDetails = IQModelMasterPhysicalStock.Select(a => new
                {
                    Product_ID = a.PRODUCT_ID,
                    SerialNumber = a.PRODUCT_SERIALNUMBER,
                    Status = statusLocalization.ContainsKey(a.SERIALSTATUS) ? statusLocalization[a.SERIALSTATUS] : "Unknown"
                }).AsQueryable();

                int count = localizedStockDetails.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = localizedStockDetails
                        .ToList()
                        .Paginate(page, rows)
                };

                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Select Company GIT stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 

        public static IActionResult SelectGITStock(SelectGITStockList SelectGITStockObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);

                int Company_ID = Convert.ToInt32(SelectGITStockObj.Company_ID);
                int GITSerialStatus = 0;
                int TransferredSerialStatus = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get Serial Status IDs
                    string statusQuery = @"                
                    SELECT 
                        rmd.RefMasterDetail_ID, 
                        rmd.RefMasterDetail_Short_Name
                        FROM GNM_RefMasterDetail rmd
                        JOIN GNM_RefMaster rm ON rmd.RefMaster_ID = rm.RefMaster_ID
                        WHERE rm.RefMaster_Name = 'SERIALSTATUS'";

                    SqlCommand command = new SqlCommand(statusQuery, connection);
                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        string shortName = reader.GetString(1).ToUpper();
                        int statusID = reader.GetInt32(0);

                        if (shortName == "GIT")
                        {
                            GITSerialStatus = statusID;
                        }
                        else if (shortName == "TRD")
                        {
                            TransferredSerialStatus = statusID;
                        }
                    }
                    reader.Close();

                    // Get GIT and Transferred Stock Data
                    string GITQuery = @"
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN SLT_PI_MACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID AND E.BRAND_ID = A.BRAND_ID AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_PURCHASEINVOICE F ON E.PURCHASEINVOICE_ID = F.PURCHASEINVOICE_ID 
                LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID
                WHERE A.SERIALSTATUS = @GITSerialStatus AND F.COMPANY_ID = @Company_ID AND A.MODEL_ID = @modelID 
                AND E.BONDING_ID IS NULL AND E.PURCHASEGRN_ID IS NULL AND E.GOODSRELEASENOTE_ID IS NULL AND E.BILLOFENTRY_ID IS NULL
                UNION ALL
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                JOIN SLT_MTNMACHINEDETAIL E ON A.MODEL_ID = E.MODEL_ID AND E.BRAND_ID = A.BRAND_ID AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_MACHINETRANSFERNOTE F ON E.MACHINETRANSFERNOTE_ID = F.MACHINETRANSFERNOTE_ID
                WHERE A.SERIALSTATUS = @TransferredSerialStatus AND F.COMPANY_ID = @Company_ID AND A.MODEL_ID = @modelID AND E.MACHINETRANSFERGRN_ID IS NULL";

                    SqlCommand gitCommand = new SqlCommand(GITQuery, connection);
                    gitCommand.Parameters.AddWithValue("@GITSerialStatus", GITSerialStatus);
                    gitCommand.Parameters.AddWithValue("@TransferredSerialStatus", TransferredSerialStatus);
                    gitCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                    gitCommand.Parameters.AddWithValue("@modelID", SelectGITStockObj.modelID);

                    SqlDataReader gitReader = gitCommand.ExecuteReader();
                    List<StockDetails> gitStockDetails = new List<StockDetails>();

                    while (gitReader.Read())
                    {
                        gitStockDetails.Add(new StockDetails
                        {
                            COMPANY_ID = gitReader.GetInt32(gitReader.GetOrdinal("COMPANY_ID")),
                            BRANCH_ID = gitReader.GetInt32(gitReader.GetOrdinal("BRANCH_ID")),
                            WAREHOUSE_ID = (int)(gitReader.IsDBNull(gitReader.GetOrdinal("WAREHOUSE_ID")) ? (int?)null : gitReader.GetInt32(gitReader.GetOrdinal("WAREHOUSE_ID"))),
                            PRODUCT_SERIALNUMBER = gitReader.GetString(gitReader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                            SERIALSTATUS = gitReader.GetInt32(gitReader.GetOrdinal("SERIALSTATUS"))
                        });
                    }
                    gitReader.Close();

                    // Get company name for each entry
                    var companyNames = new Dictionary<int, string>();

                    foreach (var item in gitStockDetails.GroupBy(p => p.COMPANY_ID))
                    {
                        string companyName = string.Empty;

                        string companyQuery = @"
                    SELECT Company_Name 
                    FROM GNM_Company 
                    WHERE Company_ID = @CompanyID";

                        SqlCommand companyCommand = new SqlCommand(companyQuery, connection);
                        companyCommand.Parameters.AddWithValue("@CompanyID", item.Key);

                        companyName = (string)companyCommand.ExecuteScalar();

                        if (string.IsNullOrEmpty(companyName))
                        {
                            companyQuery = @"
                        SELECT Company_Name 
                        FROM GNM_CompanyLocale 
                        WHERE Company_ID = @CompanyID AND Language_ID = @Language_ID";

                            companyCommand = new SqlCommand(companyQuery, connection);
                            companyCommand.Parameters.AddWithValue("@CompanyID", item.Key);
                            companyCommand.Parameters.AddWithValue("@Language_ID", SelectGITStockObj.languageid);

                            companyName = (string)companyCommand.ExecuteScalar();
                        }

                        companyNames[item.Key] = companyName;
                    }

                    // Pagination and formatting
                    var paginatedData = gitStockDetails
                        .GroupBy(p => p.COMPANY_ID)
                        .Select(g => new
                        {
                            CompanyID = g.Key,
                            CompanyName = companyNames.ContainsKey(g.Key) ? companyNames[g.Key] : "Unknown",
                            CompanyGITStock = g.Count()
                        })
                        .Skip((page - 1) * rows)
                        .Take(rows)
                        .ToList();

                    int count = paginatedData.Count;
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = paginatedData
                    };

                    //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                    return new JsonResult(jsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Branch wise GIT stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectGITBranchWise(SelectGITBranchWiseList SelectGITBranchWiseObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);

                int Company_ID = Convert.ToInt32(SelectGITBranchWiseObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectGITBranchWiseObj.Branch);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get Serial Status IDs
                    int GITSerialStatus = 0;
                    int TransferredSerialStatus = 0;

                    string statusQuery = @"
                    SELECT c.RefMasterDetail_ID, c.RefMasterDetail_Short_Name
                    FROM GNM_RefMasterDetail c
                    JOIN GNM_RefMaster r ON c.RefMaster_ID = r.RefMaster_ID
                    WHERE r.RefMaster_Name = 'SERIALSTATUS'
                        ";

                    SqlCommand statusCommand = new SqlCommand(statusQuery, connection);
                    SqlDataReader statusReader = statusCommand.ExecuteReader();

                    while (statusReader.Read())
                    {
                        string shortName = statusReader.GetString(1).ToUpper();
                        int statusID = statusReader.GetInt32(0);

                        if (shortName == "GIT")
                        {
                            GITSerialStatus = statusID;
                        }
                        else if (shortName == "TRD")
                        {
                            TransferredSerialStatus = statusID;
                        }
                    }
                    statusReader.Close();

                    // Get Branch List
                    List<int> branchIDs = new List<int>();
                    string branchQuery = @"
                SELECT Branch_ID
                FROM GNM_Branch
                WHERE Company_ID = @Company_ID AND Branch_Active = 1";

                    SqlCommand branchCommand = new SqlCommand(branchQuery, connection);
                    branchCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                    SqlDataReader branchReader = branchCommand.ExecuteReader();

                    while (branchReader.Read())
                    {
                        branchIDs.Add(branchReader.GetInt32(0));
                    }
                    branchReader.Close();

                    // Get GIT Stock Data
                    string GITQuery = @"
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS
                FROM GNM_PRODUCT A
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID
                JOIN SLT_PI_MACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID AND E.BRAND_ID = A.BRAND_ID AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER
                JOIN SLT_PURCHASEINVOICE F ON E.PURCHASEINVOICE_ID = F.PURCHASEINVOICE_ID
                LEFT JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID
                WHERE A.SERIALSTATUS = @GITSerialStatus AND F.COMPANY_ID = @Company_ID AND A.MODEL_ID = @modelID
                AND E.BONDING_ID IS NULL AND E.PURCHASEGRN_ID IS NULL AND E.GOODSRELEASENOTE_ID IS NULL AND E.BILLOFENTRY_ID IS NULL
                UNION ALL
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS
                FROM GNM_PRODUCT A
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID
                JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID
                JOIN SLT_MTNMACHINEDETAIL E ON A.MODEL_ID = E.MODEL_ID AND E.BRAND_ID = A.BRAND_ID AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER
                JOIN SLT_MACHINETRANSFERNOTE F ON E.MACHINETRANSFERNOTE_ID = F.MACHINETRANSFERNOTE_ID
                WHERE A.SERIALSTATUS = @TransferredSerialStatus AND F.COMPANY_ID = @Company_ID AND A.MODEL_ID = @modelID
                AND E.MACHINETRANSFERGRN_ID IS NULL";

                    SqlCommand gitCommand = new SqlCommand(GITQuery, connection);
                    gitCommand.Parameters.AddWithValue("@GITSerialStatus", GITSerialStatus);
                    gitCommand.Parameters.AddWithValue("@TransferredSerialStatus", TransferredSerialStatus);
                    gitCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                    gitCommand.Parameters.AddWithValue("@modelID", SelectGITBranchWiseObj.modelID);

                    SqlDataReader gitReader = gitCommand.ExecuteReader();
                    var gitStockDetails = new List<StockDetails>();

                    while (gitReader.Read())
                    {
                        gitStockDetails.Add(new StockDetails
                        {
                            COMPANY_ID = gitReader.GetInt32(gitReader.GetOrdinal("COMPANY_ID")),
                            BRANCH_ID = gitReader.GetInt32(gitReader.GetOrdinal("BRANCH_ID")),
                            WAREHOUSE_ID = (int)(gitReader.IsDBNull(gitReader.GetOrdinal("WAREHOUSE_ID")) ? (int?)null : gitReader.GetInt32(gitReader.GetOrdinal("WAREHOUSE_ID"))),
                            PRODUCT_SERIALNUMBER = gitReader.GetString(gitReader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                            SERIALSTATUS = gitReader.GetInt32(gitReader.GetOrdinal("SERIALSTATUS"))
                        });
                    }
                    gitReader.Close();

                    // Count and pagination
                    var gitStockGroupedByBranch = gitStockDetails
                        .Where(s => branchIDs.Contains(s.BRANCH_ID))
                        .GroupBy(s => s.BRANCH_ID)
                        .Select(g => new ModelAvailableStock
                        {
                            Branch_ID = g.Key,
                            ProcuctCount = g.Distinct().Count()
                        })
                        .ToList();

                    int count = gitStockGroupedByBranch.Count;
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    // Get Branch Names
                    var branchLocaleList = new Dictionary<int, string>();
                    if (SelectGITBranchWiseObj.languageid != Convert.ToInt32(SelectGITBranchWiseObj.GeneralLanguageID))
                    {
                        string branchLocaleQuery = @"
                    SELECT Branch_ID, Branch_Name
                    FROM GNM_BranchLocale
                    WHERE Company_ID = @Company_ID AND Branch_Active = 1 AND Language_ID = @Language_ID";

                        SqlCommand branchLocaleCommand = new SqlCommand(branchLocaleQuery, connection);
                        branchLocaleCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                        branchLocaleCommand.Parameters.AddWithValue("@Language_ID", SelectGITBranchWiseObj.languageid);

                        SqlDataReader branchLocaleReader = branchLocaleCommand.ExecuteReader();
                        while (branchLocaleReader.Read())
                        {
                            branchLocaleList[branchLocaleReader.GetInt32(0)] = branchLocaleReader.GetString(1);
                        }
                        branchLocaleReader.Close();
                    }

                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = gitStockGroupedByBranch
                            .Select(a => new
                            {
                                BranchID = a.Branch_ID,
                                BranchName = branchLocaleList.ContainsKey(a.Branch_ID) ? branchLocaleList[a.Branch_ID] : "Unknown",
                                BranchGITStock = a.ProcuctCount
                            })
                            .Skip((page - 1) * rows)
                            .Take(rows)
                            .ToList()
                    };
                }

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: Select GIT Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectSerialNumberAndStatus(SelectSerialNumberAndStatusList SelectSerialNumberAndStatusObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Define parameters
                    SqlParameter[] parameters = {
                new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Convert.ToInt32(SelectSerialNumberAndStatusObj.Company_ID) },
                new SqlParameter("@BranchID", SqlDbType.Int) { Value = SelectSerialNumberAndStatusObj.BranchID },
                new SqlParameter("@ModelID", SqlDbType.Int) { Value = SelectSerialNumberAndStatusObj.modelID },
                new SqlParameter("@Brand_ID", SqlDbType.Int) { Value = SelectSerialNumberAndStatusObj.Brand_ID },
                new SqlParameter("@ProductType_ID", SqlDbType.Int) { Value = SelectSerialNumberAndStatusObj.ProductType_ID },
                new SqlParameter("@LanguageID", SqlDbType.Int) { Value = SelectSerialNumberAndStatusObj.languageid }
            };

                    // Query to fetch product data
                    string query = @"
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN SLT_PI_MACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID 
                    AND E.BRAND_ID = A.BRAND_ID 
                    AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID 
                    AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_PURCHASEINVOICE F ON E.PURCHASEINVOICE_ID = F.PURCHASEINVOICE_ID 
                LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @GITSerialStatus 
                    AND F.COMPANY_ID = @Company_ID 
                    AND A.MODEL_ID = @ModelID 
                    AND E.BONDING_ID IS NULL 
                    AND E.PURCHASEGRN_ID IS NULL 
                    AND E.GOODSRELEASENOTE_ID IS NULL 
                    AND E.BILLOFENTRY_ID IS NULL
                UNION ALL 
                SELECT F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                JOIN SLT_MTNMACHINEDETAIL E ON A.MODEL_ID = E.MODEL_ID 
                    AND E.BRAND_ID = A.BRAND_ID 
                    AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID 
                    AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_MACHINETRANSFERNOTE F ON E.MACHINETRANSFERNOTE_ID = F.MACHINETRANSFERNOTE_ID 
                WHERE A.SERIALSTATUS = @TransferredSerialStatus 
                    AND F.COMPANY_ID = @Company_ID 
                    AND A.MODEL_ID = @ModelID 
                    AND E.MACHINETRANSFERGRN_ID IS NULL";

                    SqlCommand command = new SqlCommand(query, connection);
                    command.Parameters.AddRange(parameters);

                    SqlDataAdapter adapter = new SqlDataAdapter(command);
                    System.Data.DataTable dataTable = new System.Data.DataTable();
                    adapter.Fill(dataTable);

                    // Convert DataTable to IEnumerable<StockDetails>
                    IEnumerable<StockDetails> IEProductGITStock = dataTable.AsEnumerable().Select(row => new StockDetails
                    {
                        COMPANY_ID = row.Field<int>("COMPANY_ID"),
                        BRANCH_ID = row.Field<int>("BRANCH_ID"),
                        WAREHOUSE_ID = row.Field<int>("WAREHOUSE_ID"),
                        PRODUCT_SERIALNUMBER = row.Field<string>("PRODUCT_SERIALNUMBER"),
                        SERIALSTATUS = row.Field<int>("SERIALSTATUS")
                    });

                    // Fetch branch data
                    string branchQuery = "SELECT * FROM GNM_Branch WHERE Company_ID = @Company_ID AND Branch_ID = @BranchID AND Branch_Active = 1";
                    SqlCommand branchCommand = new SqlCommand(branchQuery, connection);
                    branchCommand.Parameters.AddRange(parameters);
                    SqlDataAdapter branchAdapter = new SqlDataAdapter(branchCommand);
                    System.Data.DataTable branchTable = new System.Data.DataTable();
                    branchAdapter.Fill(branchTable);

                    List<GNM_Branch> BranchList = branchTable.AsEnumerable().Select(row => new GNM_Branch
                    {
                        Branch_ID = row.Field<int>("Branch_ID"),
                        Branch_Active = row.Field<bool>("Branch_Active")
                    }).ToList();

                    var filteredStock = from a in IEProductGITStock
                                        join c in BranchList on a.BRANCH_ID equals c.Branch_ID
                                        group a by a.PRODUCT_SERIALNUMBER into final
                                        select new StockDetails
                                        {
                                            PRODUCT_ID = final.FirstOrDefault().PRODUCT_ID,
                                            PRODUCT_SERIALNUMBER = final.FirstOrDefault().PRODUCT_SERIALNUMBER,
                                            SERIALSTATUS = final.FirstOrDefault().SERIALSTATUS
                                        };

                    // Apply search filters
                    //if (Request.Params["_search"] == "true")
                    //{
                    //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                    //    if (filters.rules.Count() > 0)
                    //        filteredStock = filteredStock.FilterSearch<StockDetails>(filters);
                    //}

                    count = filteredStock.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = filteredStock.Skip((page - 1) * rows).Take(rows).Select(a => new
                        {
                            ProductID = a.PRODUCT_ID,
                            SerialNumber = a.PRODUCT_SERIALNUMBER,
                            Status = (SelectSerialNumberAndStatusObj.languageid == Convert.ToInt32(SelectSerialNumberAndStatusObj.GeneralLanguageID))
                                ? GetStatusName(connection, a.SERIALSTATUS)
                                : GetStatusNameLocalized(connection, a.SERIALSTATUS, SelectSerialNumberAndStatusObj.languageid)
                        }).ToList()
                    };
                }

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: GetStatusName :::
        private static string GetStatusName(SqlConnection connection, int serialStatus)
        {
            string statusName = null;
            string query = "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @SerialStatus";

            using (SqlCommand command = new SqlCommand(query, connection))
            {
                command.Parameters.Add(new SqlParameter("@SerialStatus", SqlDbType.Int) { Value = serialStatus });

                object result = command.ExecuteScalar();
                if (result != null)
                {
                    statusName = result.ToString();
                }
            }

            return statusName;
        }
        #endregion

        #region ::: GetStatusNameLocalized :::
        private static string GetStatusNameLocalized(SqlConnection connection, int serialStatus, int languageId)
        {
            string statusName = null;
            string query = @"
        SELECT RefMasterDetail_Name 
        FROM GNM_RefMasterDetailLocale 
        WHERE RefMasterDetail_ID = @SerialStatus AND Language_ID = @LanguageID";

            using (SqlCommand command = new SqlCommand(query, connection))
            {
                command.Parameters.Add(new SqlParameter("@SerialStatus", SqlDbType.Int) { Value = serialStatus });
                command.Parameters.Add(new SqlParameter("@LanguageID", SqlDbType.Int) { Value = languageId });

                object result = command.ExecuteScalar();
                if (result != null)
                {
                    statusName = result.ToString();
                }
            }

            return statusName;
        }
        #endregion

        #region ::: Select Company OWH stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectOWHStock(SelectOWHStockList SelectOWHStockObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int Company_ID = Convert.ToInt32(SelectOWHStockObj.Company_ID);
                int TransitOutSerialStatus;
                IEnumerable<StockDetails> IEProductOWHStock = null;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get TransitOutSerialStatus
                    string serialStatusQuery = @"
                SELECT TOP 1 c.RefMasterDetail_ID
                FROM GNM_RefMasterDetail c
                JOIN GNM_RefMaster r ON c.RefMaster_ID = r.RefMaster_ID
                WHERE UPPER(r.RefMaster_Name) = 'SERIALSTATUS'
                  AND UPPER(c.RefMasterDetail_Short_Name) = 'TO'
                ";

                    using (SqlCommand command = new SqlCommand(serialStatusQuery, connection))
                    {
                        TransitOutSerialStatus = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // Construct OWHQuery
                    string OWHQuery = @"
                SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, C.REFMASTERDETAIL_NAME AS STATUS, 
                       B.WAREHOUSENAME AS WAREHOUSE, B.COMPANY_ID, B.BRANCH_ID 
                FROM GNM_PRODUCT A 
                JOIN GNM_WAREHOUSE B ON A.WAREHOUSE_ID = B.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.SERIALSTATUS = C.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @TransitOutSerialStatus 
                AND B.COMPANY_ID = @Company_ID 
                AND A.MODEL_ID = @ModelID
                UNION ALL 
                SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, D.REFMASTERDETAIL_NAME AS STATUS, 
                       '' AS WAREHOUSE, C.COMPANY_ID, C.BRANCH_ID 
                FROM GNM_PRODUCT A 
                JOIN SLT_DELIVERYNOTEMACHINEDETAILS B ON A.PRODUCT_ID = B.PRODUCT_ID 
                JOIN SLT_DELIVERYNOTE C ON B.DELIVERYNOTE_ID = C.DELIVERYNOTE_ID 
                JOIN GNM_REFMASTERDETAIL D ON A.SERIALSTATUS = D.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @TransitOutSerialStatus 
                AND C.COMPANY_ID = @Company_ID 
                AND A.MODEL_ID = @ModelID 
                AND A.WAREHOUSE_ID IS NULL";

                    using (SqlCommand command = new SqlCommand(OWHQuery, connection))
                    {
                        // Add parameters
                        command.Parameters.Add(new SqlParameter("@TransitOutSerialStatus", SqlDbType.Int) { Value = TransitOutSerialStatus });
                        command.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID });
                        command.Parameters.Add(new SqlParameter("@ModelID", SqlDbType.Int) { Value = SelectOWHStockObj.modelID });

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            List<StockDetails> stockList = new List<StockDetails>();

                            while (reader.Read())
                            {
                                stockList.Add(new StockDetails
                                {
                                    PRODUCT_ID = reader.GetInt32(reader.GetOrdinal("PRODUCT_ID")),
                                    PRODUCT_SERIALNUMBER = reader.GetString(reader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                                    Status = reader.GetString(reader.GetOrdinal("STATUS")),
                                    WareHouse = reader.GetString(reader.GetOrdinal("WAREHOUSE")),
                                    COMPANY_ID = reader.GetInt32(reader.GetOrdinal("COMPANY_ID")),
                                    BRANCH_ID = reader.GetInt32(reader.GetOrdinal("BRANCH_ID"))
                                });
                            }

                            IEProductOWHStock = stockList;
                        }
                    }

                    // Get WareHouseList
                    List<GNM_WareHouse> WareHouseList = new List<GNM_WareHouse>();
                    string warehouseQuery = @"
                SELECT * 
                FROM GNM_WareHouse 
                WHERE Company_ID = @Company_ID 
                AND IsActive = 1 
                AND WareHouseType_ID = 4";

                    using (SqlCommand warehouseCommand = new SqlCommand(warehouseQuery, connection))
                    {
                        warehouseCommand.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID });

                        using (SqlDataReader reader = warehouseCommand.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WareHouseList.Add(new GNM_WareHouse
                                {
                                    WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                    WareHouseName = reader.GetString(reader.GetOrdinal("WareHouseName")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                    WareHouseType_ID = reader.GetInt32(reader.GetOrdinal("WareHouseType_ID"))
                                });
                            }
                        }
                    }

                    // Count total records
                    int count = IEProductOWHStock.Count();
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = (from p in IEProductOWHStock
                                group p by p.COMPANY_ID into final
                                select new
                                {
                                    CompanyID = final.FirstOrDefault().COMPANY_ID,
                                    CompanyName = (SelectOWHStockObj.languageid == Convert.ToInt32(SelectOWHStockObj.GeneralLanguageID))
                                        ? GetCompanyName(connection, Company_ID)
                                        : GetCompanyNameLocalized(connection, Company_ID, SelectOWHStockObj.languageid),
                                    CompanyOWHStock = final.Count()
                                }).ToList()
                    };
                }

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region:::GetCompanyName :::
        private static string GetCompanyName(SqlConnection connection, int companyID)
        {
            string query = "SELECT Company_Name FROM GNM_Company WHERE Company_ID = @Company_ID";
            using (SqlCommand command = new SqlCommand(query, connection))
            {
                command.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = companyID });
                object result = command.ExecuteScalar();
                return result != null ? result.ToString() : null;
            }
        }
        #endregion

        #region:::GetCompanyNameLocalized :::
        private static string GetCompanyNameLocalized(SqlConnection connection, int companyID, int languageID)
        {
            string query = @"
        SELECT Company_Name 
        FROM GNM_CompanyLocale 
        WHERE Company_ID = @Company_ID 
        AND Language_ID = @LanguageID";
            using (SqlCommand command = new SqlCommand(query, connection))
            {
                command.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int) { Value = companyID });
                command.Parameters.Add(new SqlParameter("@LanguageID", SqlDbType.Int) { Value = languageID });
                object result = command.ExecuteScalar();
                return result != null ? result.ToString() : null;
            }
        }

        #endregion

        #region ::: Select Branch wise OWH stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectOWHBranchWise(SelectOWHBranchWiseList SelectOWHBranchWiseObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                // Establish ADO.NET connections
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    var jsonResult = default(dynamic);
                    int Company_ID = Convert.ToInt32(SelectOWHBranchWiseObj.Company_ID);
                    int Branch_ID = Convert.ToInt32(SelectOWHBranchWiseObj.Branch);

                    // Get the TransitOutSerialStatus
                    int TransitOutSerialStatus;
                    using (SqlCommand command = new SqlCommand("SELECT TOP 1 c.RefMasterDetail_ID" +
                                                                "\r\nFROM GNM_RefMasterDetail c\r\n" +
                                                                "JOIN GNM_RefMaster r ON c.RefMaster_ID = r.RefMaster_ID" +
                                                                "\r\nWHERE r.RefMaster_Name = @RefMasterName\r\n  AND c.RefMasterDetail_Short_Name = @ShortName", connection))
                    {
                        command.Parameters.AddWithValue("@RefMasterName", "SERIALSTATUS");
                        command.Parameters.AddWithValue("@ShortName", "TO");

                        TransitOutSerialStatus = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // First part of the OWH query
                    string OWHQuery = "SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, C.REFMASTERDETAIL_NAME AS STATUS, B.WAREHOUSENAME AS WAREHOUSE, " +
                                      "B.COMPANY_ID, B.BRANCH_ID FROM GNM_PRODUCT A " +
                                      "JOIN GNM_WAREHOUSE B ON A.WAREHOUSE_ID = B.WAREHOUSE_ID " +
                                      "JOIN GNM_REFMASTERDETAIL C ON A.SERIALSTATUS = C.REFMASTERDETAIL_ID " +
                                      "WHERE SERIALSTATUS = @TransitOutSerialStatus AND B.COMPANY_ID = @CompanyID AND MODEL_ID = @ModelID ";

                    // Union with the second part of the query
                    OWHQuery += "UNION ALL " +
                                "SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, D.REFMASTERDETAIL_NAME AS STATUS, '' AS WAREHOUSE, C.COMPANY_ID, C.BRANCH_ID " +
                                "FROM GNM_PRODUCT A " +
                                "JOIN SLT_DELIVERYNOTEMACHINEDETAILS B ON A.PRODUCT_ID = B.PRODUCT_ID " +
                                "JOIN SLT_DELIVERYNOTE C ON B.DELIVERYNOTE_ID = C.DELIVERYNOTE_ID " +
                                "JOIN GNM_REFMASTERDETAIL D ON A.SERIALSTATUS = D.REFMASTERDETAIL_ID " +
                                "WHERE SERIALSTATUS = @TransitOutSerialStatus AND C.COMPANY_ID = @CompanyID AND MODEL_ID = @ModelID AND A.WAREHOUSE_ID IS NULL";

                    // Execute the query to retrieve stock details
                    List<StockDetails> IEProductOWHStock = new List<StockDetails>();
                    using (SqlCommand command = new SqlCommand(OWHQuery, connection))
                    {
                        command.Parameters.AddWithValue("@TransitOutSerialStatus", TransitOutSerialStatus);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);
                        command.Parameters.AddWithValue("@ModelID", SelectOWHBranchWiseObj.modelID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                StockDetails stock = new StockDetails
                                {
                                    PRODUCT_ID = Convert.ToInt32(reader["PRODUCT_ID"]),
                                    PRODUCT_SERIALNUMBER = reader["PRODUCT_SERIALNUMBER"].ToString(),
                                    Status = reader["STATUS"].ToString(),
                                    WareHouse = reader["WAREHOUSE"].ToString(),
                                    COMPANY_ID = Convert.ToInt32(reader["COMPANY_ID"]),
                                    BRANCH_ID = Convert.ToInt32(reader["BRANCH_ID"])
                                };
                                IEProductOWHStock.Add(stock);
                            }
                        }
                    }

                    // Get branch list
                    List<GNM_Branch> BranchList = new List<GNM_Branch>();
                    using (SqlCommand command = new SqlCommand("SELECT Branch_ID, Branch_Name FROM GNM_Branch WHERE Company_ID = @CompanyID AND Branch_Active = 1", connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                BranchList.Add(new GNM_Branch
                                {
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                    Branch_Name = reader["Branch_Name"].ToString(),
                                });
                            }
                        }
                    }

                    // Process language-based logic
                    IEnumerable<ModelAvailableStock> IEMOdelMasterGITStock = IEProductOWHStock
                        .GroupBy(a => a.PRODUCT_SERIALNUMBER)
                        .Select(final => new ModelAvailableStock
                        {
                            Branch_ID = final.FirstOrDefault().BRANCH_ID,
                            ProcuctCount = final.Distinct().Count()
                        });

                    int count = IEMOdelMasterGITStock.Count();
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    if (SelectOWHBranchWiseObj.languageid == Convert.ToInt32(SelectOWHBranchWiseObj.GeneralLanguageID))
                    {
                        jsonResult = new
                        {
                            total = total,
                            page = page,
                            records = count,
                            data = IEMOdelMasterGITStock
                                .GroupBy(a => a.Branch_ID)
                                .Select(final => new
                                {
                                    BranchID = final.FirstOrDefault().Branch_ID,
                                    BranchName = BranchList.FirstOrDefault(b => b.Branch_ID == final.FirstOrDefault().Branch_ID)?.Branch_Name,
                                    BranchOWHStock = final.Sum(pc => pc.ProcuctCount)
                                })
                        };
                    }
                    else
                    {
                        // Get localized branch names
                        List<GNM_BranchLocale> BranchLocaleList = new List<GNM_BranchLocale>();
                        using (SqlCommand command = new SqlCommand("SELECT Branch_ID, Branch_Name FROM GNM_BranchLocale WHERE Company_ID = @CompanyID", connection))
                        {
                            command.Parameters.AddWithValue("@CompanyID", Company_ID);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    BranchLocaleList.Add(new GNM_BranchLocale
                                    {
                                        Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                        Branch_Name = reader["Branch_Name"].ToString(),
                                    });
                                }
                            }
                        }

                        jsonResult = new
                        {
                            total = total,
                            page = page,
                            records = count,
                            data = IEMOdelMasterGITStock
                                .GroupBy(a => a.Branch_ID)
                                .Select(final => new
                                {
                                    BranchID = final.FirstOrDefault().Branch_ID,
                                    BranchName = BranchLocaleList.FirstOrDefault(b => b.Branch_ID == final.FirstOrDefault().Branch_ID)?.Branch_Name,
                                    BranchOWHStock = final.Sum(pc => pc.ProcuctCount)
                                })
                        };
                    }

                    //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                    return new JsonResult(jsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }
        #endregion

        #region ::: Select Out of WH Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectOWHSerialNumberAndStatus(SelectOWHSerialNumberAndStatusList SelectOWHSerialNumberAndStatusObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;
                string OWHQuery = string.Empty;
                List<StockDetails> stockDetailsList = new List<StockDetails>();

                int Company_ID = Convert.ToInt32(SelectOWHSerialNumberAndStatusObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectOWHSerialNumberAndStatusObj.Branch);

                // ADO.NET connection and command setup
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get TransitOutSerialStatus
                    int TransitOutSerialStatus = 0;
                    using (SqlCommand cmd = new SqlCommand("SELECT TOP 1 c.RefMasterDetail_ID\r\nFROM GNM_RefMasterDetail c\r\nJOIN GNM_RefMaster r ON c.RefMaster_ID = r.RefMaster_ID\r\nWHERE UPPER(r.RefMaster_Name) = 'SERIALSTATUS'\r\n  AND UPPER(c.RefMasterDetail_Short_Name) = 'TO'\r\n", conn))
                    {
                        TransitOutSerialStatus = (int)cmd.ExecuteScalar();
                    }

                    // Build the query based on language
                    if (SelectOWHSerialNumberAndStatusObj.languageid == Convert.ToInt32(SelectOWHSerialNumberAndStatusObj.GeneralLanguageID))
                    {
                        OWHQuery = @"SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, C.REFMASTERDETAIL_NAME AS STATUS, B.WAREHOUSENAME AS WAREHOUSE, 
                             B.COMPANY_ID, B.BRANCH_ID 
                             FROM GNM_PRODUCT A 
                             JOIN GNM_WAREHOUSE B ON A.WAREHOUSE_ID=B.WAREHOUSE_ID 
                             JOIN GNM_REFMASTERDETAIL C ON A.SERIALSTATUS=C.REFMASTERDETAIL_ID 
                             WHERE SERIALSTATUS=@TransitOutSerialStatus AND B.COMPANY_ID=@CompanyID AND MODEL_ID=@ModelID
                             UNION ALL 
                             SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, D.REFMASTERDETAIL_NAME AS STATUS, '' AS WAREHOUSE, 
                             C.COMPANY_ID, C.BRANCH_ID 
                             FROM GNM_PRODUCT A 
                             JOIN SLT_DELIVERYNOTEMACHINEDETAILS B ON A.PRODUCT_ID=B.PRODUCT_ID 
                             JOIN SLT_DELIVERYNOTE C ON B.DELIVERYNOTE_ID=C.DELIVERYNOTE_ID 
                             JOIN GNM_REFMASTERDETAIL D ON A.SERIALSTATUS=D.REFMASTERDETAIL_ID 
                             WHERE SERIALSTATUS=@TransitOutSerialStatus AND C.COMPANY_ID=@CompanyID AND MODEL_ID=@ModelID AND A.WAREHOUSE_ID IS NULL";
                    }
                    else
                    {
                        OWHQuery = @"SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, E.REFMASTERDETAIL_NAME AS STATUS, D.WAREHOUSENAME AS WAREHOUSE, 
                             B.COMPANY_ID, B.BRANCH_ID 
                             FROM GNM_PRODUCT A 
                             JOIN GNM_WAREHOUSE B ON A.WAREHOUSE_ID=B.WAREHOUSE_ID 
                             JOIN GNM_REFMASTERDETAIL C ON A.SERIALSTATUS=C.REFMASTERDETAIL_ID 
                             LEFT OUTER JOIN GNM_WAREHOUSELOCALE D ON B.WAREHOUSE_ID = D.WAREHOUSE_ID 
                             LEFT OUTER JOIN GNM_REFMASTERDETAILLOCALE E ON C.REFMASTERDETAIL_ID = E.REFMASTERDETAIL_ID 
                             WHERE SERIALSTATUS=@TransitOutSerialStatus AND B.COMPANY_ID=@CompanyID AND MODEL_ID=@ModelID
                             UNION ALL 
                             SELECT A.PRODUCT_ID, A.PRODUCT_SERIALNUMBER, E.REFMASTERDETAIL_NAME AS STATUS, '' AS WAREHOUSE, 
                             C.COMPANY_ID, C.BRANCH_ID 
                             FROM GNM_PRODUCT A 
                             JOIN SLT_DELIVERYNOTEMACHINEDETAILS B ON A.PRODUCT_ID=B.PRODUCT_ID 
                             JOIN SLT_DELIVERYNOTE C ON B.DELIVERYNOTE_ID=C.DELIVERYNOTE_ID 
                             JOIN GNM_REFMASTERDETAIL D ON A.SERIALSTATUS=D.REFMASTERDETAIL_ID 
                             LEFT OUTER JOIN GNM_REFMASTERDETAILLOCALE E ON D.REFMASTERDETAIL_ID = E.REFMASTERDETAIL_ID 
                             WHERE SERIALSTATUS=@TransitOutSerialStatus AND C.COMPANY_ID=@CompanyID AND MODEL_ID=@ModelID AND A.WAREHOUSE_ID IS NULL";
                    }

                    // Execute the query
                    using (SqlCommand cmd = new SqlCommand(OWHQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@TransitOutSerialStatus", TransitOutSerialStatus);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        cmd.Parameters.AddWithValue("@ModelID", SelectOWHSerialNumberAndStatusObj.modelID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                StockDetails stockDetails = new StockDetails
                                {
                                    PRODUCT_ID = Convert.ToInt32(reader["PRODUCT_ID"]),
                                    PRODUCT_SERIALNUMBER = reader["PRODUCT_SERIALNUMBER"].ToString(),
                                    Status = reader["STATUS"].ToString(),
                                    WareHouse = reader["WAREHOUSE"].ToString(),
                                };
                                stockDetailsList.Add(stockDetails);
                            }
                        }
                    }
                }

                // Group by serial number and project into StockDetails
                var IEModelMasterPhysicalStock = stockDetailsList
                    .GroupBy(x => x.PRODUCT_SERIALNUMBER)
                    .Select(fOWH => new StockDetails
                    {
                        PRODUCT_ID = fOWH.FirstOrDefault().PRODUCT_ID,
                        PRODUCT_SERIALNUMBER = fOWH.FirstOrDefault().PRODUCT_SERIALNUMBER,
                        Status = fOWH.FirstOrDefault().Status,
                        WareHouse = fOWH.FirstOrDefault().WareHouse
                    }).ToList();

                var IQModelMasterPhysicalStock = IEModelMasterPhysicalStock.AsQueryable();

                // Search filtering
                //if (Request.Params["_search"] == "true")
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                //    if (filters.rules.Count() > 0)
                //        IQModelMasterPhysicalStock = IQModelMasterPhysicalStock.FilterSearch<StockDetails>(filters);
                //}

                count = IQModelMasterPhysicalStock.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = IQModelMasterPhysicalStock.Select(a => new
                    {
                        ProductID = a.PRODUCT_ID,
                        SerialNumber = a.PRODUCT_SERIALNUMBER,
                        Status = a.Status,
                        WareHouse = a.WareHouse
                    }).ToList().Paginate(page, rows)
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }
        #endregion

        #region ::: Select Company UCL stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectUCLStock(SelectUCLStockList SelectUCLStockObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int Company_ID = Convert.ToInt32(SelectUCLStockObj.Company_ID);
                List<StockDetails> stockDetailsList = new List<StockDetails>();

                // ADO.NET connection and command setup
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get UnderClearenceSerialStatus
                    int UnderClearenceSerialStatus = 0;
                    using (SqlCommand cmd = new SqlCommand("SELECT TOP 1 c.RefMasterDetail_ID\r\nFROM GNM_RefMasterDetail c\r\nJOIN GNM_RefMaster r ON c.RefMaster_ID = r.RefMaster_ID\r\nWHERE r.RefMaster_Name = 'SERIALSTATUS'\r\n  AND c.RefMasterDetail_Short_Name = 'UCL'", conn))
                    {
                        UnderClearenceSerialStatus = (int)cmd.ExecuteScalar();
                    }

                    // Build the query
                    string UCLQuery = @"SELECT P.PRODUCT_ID, W.COMPANY_ID, W.BRANCH_ID, P.WAREHOUSE_ID, P.PRODUCT_SERIALNUMBER, P.SERIALSTATUS 
                                FROM GNM_PRODUCT P 
                                JOIN GNM_WAREHOUSE W ON P.WAREHOUSE_ID = W.WAREHOUSE_ID 
                                WHERE SERIALSTATUS = @UnderClearenceSerialStatus AND W.WAREHOUSETYPE_ID = 2 AND W.COMPANY_ID = @CompanyID AND P.MODEL_ID = @ModelID
                                UNION ALL 
                                SELECT A.PRODUCT_ID, F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                                FROM GNM_PRODUCT A 
                                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                                JOIN SLT_BILLOFENTRYMACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID AND E.BRAND_ID = A.BRAND_ID AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                                JOIN SLT_BILLOFENTRY F ON E.BILLOFENTRY_ID = F.BILLOFENTRY_ID 
                                LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                                WHERE A.SERIALSTATUS = @UnderClearenceSerialStatus AND F.COMPANY_ID = @CompanyID AND A.MODEL_ID = @ModelID";

                    using (SqlCommand cmd = new SqlCommand(UCLQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@UnderClearenceSerialStatus", UnderClearenceSerialStatus);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        cmd.Parameters.AddWithValue("@ModelID", SelectUCLStockObj.modelID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                StockDetails stockDetails = new StockDetails
                                {
                                    PRODUCT_ID = Convert.ToInt32(reader["PRODUCT_ID"]),
                                    PRODUCT_SERIALNUMBER = reader["PRODUCT_SERIALNUMBER"].ToString(),
                                    Status = reader["SERIALSTATUS"].ToString(),
                                    WareHouse = reader["WAREHOUSE_ID"] != DBNull.Value ? reader["WAREHOUSE_ID"].ToString() : string.Empty,
                                    COMPANY_ID = Convert.ToInt32(reader["COMPANY_ID"]),
                                    BRANCH_ID = Convert.ToInt32(reader["BRANCH_ID"])
                                };
                                stockDetailsList.Add(stockDetails);
                            }
                        }
                    }


                    var IEProductUCLStock = stockDetailsList.AsQueryable();
                    int count = IEProductUCLStock.Count();
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = (from p in IEProductUCLStock
                                group p by p.COMPANY_ID into final
                                select new
                                {
                                    CompanyID = final.FirstOrDefault().COMPANY_ID,
                                    CompanyName = (SelectUCLStockObj.languageid == Convert.ToInt32(SelectUCLStockObj.GeneralLanguageID))
                                        ? GetCompanyName(conn, Company_ID)
                                        : GetCompanyNameLocalized(conn, Company_ID, SelectUCLStockObj.languageid),
                                    CompanyUCLStock = final.Count()
                                }).ToList()
                    };

                    return new JsonResult(jsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Branch wise UCL stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 

        public static IActionResult SelectUCLBranchWise(SelectUCLBranchWiseList SelectUCLBranchWiseObj, string constring, int LogException, int page, int rows)
        {
            try
            {

                // Initialize the results
                var jsonResult = default(dynamic);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch Company_ID and Branch_ID from session
                    int Company_ID = Convert.ToInt32(SelectUCLBranchWiseObj.Company_ID);
                    int Branch_ID = Convert.ToInt32(SelectUCLBranchWiseObj.Branch);

                    // Retrieve UnderClearenceSerialStatus
                    int UnderClearenceSerialStatus;
                    string queryStatus = "SELECT TOP 1 RefMasterDetail_ID FROM GNM_RefMasterDetail " +
                                         "JOIN GNM_RefMaster ON GNM_RefMasterDetail.RefMaster_ID = GNM_RefMaster.RefMaster_ID " +
                                         "WHERE UPPER(GNM_RefMaster.RefMaster_Name) = 'SERIALSTATUS' AND UPPER(GNM_RefMasterDetail.RefMasterDetail_Short_Name) = 'UCL'";
                    using (SqlCommand cmd = new SqlCommand(queryStatus, connection))
                    {
                        object result = cmd.ExecuteScalar();
                        UnderClearenceSerialStatus = result != null ? Convert.ToInt32(result) : 0;
                    }

                    // Query to get UCL stock details
                    string queryUCL = @"
                SELECT P.PRODUCT_ID, W.COMPANY_ID, W.BRANCH_ID, P.WAREHOUSE_ID, P.PRODUCT_SERIALNUMBER, P.SERIALSTATUS
                FROM GNM_PRODUCT P
                JOIN GNM_WAREHOUSE W ON P.WAREHOUSE_ID = W.WAREHOUSE_ID
                WHERE P.SERIALSTATUS = @UnderClearenceSerialStatus AND W.WAREHOUSETYPE_ID = 2 AND W.COMPANY_ID = @Company_ID AND P.MODEL_ID = @Model_ID
                UNION ALL
                SELECT A.PRODUCT_ID, F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS
                FROM GNM_PRODUCT A
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID
                JOIN SLT_BILLOFENTRYMACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID AND E.BRAND_ID = A.BRAND_ID AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER
                JOIN SLT_BILLOFENTRY F ON E.BILLOFENTRY_ID = F.BILLOFENTRY_ID
                LEFT JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID
                WHERE A.SERIALSTATUS = @UnderClearenceSerialStatus AND F.COMPANY_ID = @Company_ID AND A.MODEL_ID = @Model_ID";

                    // Execute the query
                    using (SqlCommand cmd = new SqlCommand(queryUCL, connection))
                    {
                        cmd.Parameters.AddWithValue("@UnderClearenceSerialStatus", UnderClearenceSerialStatus);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@Model_ID", SelectUCLBranchWiseObj.modelID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var stockDetailsList = new List<StockDetails>();

                            while (reader.Read())
                            {
                                var stockDetail = new StockDetails
                                {
                                    PRODUCT_ID = reader.GetInt32(reader.GetOrdinal("PRODUCT_ID")),
                                    COMPANY_ID = reader.GetInt32(reader.GetOrdinal("COMPANY_ID")),
                                    BRANCH_ID = reader.GetInt32(reader.GetOrdinal("BRANCH_ID")),
                                    WAREHOUSE_ID = reader.GetInt32(reader.GetOrdinal("WAREHOUSE_ID")),
                                    PRODUCT_SERIALNUMBER = reader.GetString(reader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                                    SERIALSTATUS = reader.GetInt32(reader.GetOrdinal("SERIALSTATUS"))
                                };

                                stockDetailsList.Add(stockDetail);
                            }

                            // Group and process data based on languageid
                            var branchList = new List<GNM_Branch>(); // Fetch from database as needed
                            var modelAvailableStockList = stockDetailsList
                                .GroupBy(a => a.PRODUCT_SERIALNUMBER)
                                .Select(g => new ModelAvailableStock
                                {
                                    Branch_ID = g.FirstOrDefault().BRANCH_ID,
                                    ProcuctCount = g.Distinct().Count()
                                })
                                .ToList();

                            int count = stockDetailsList.Count();
                            int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                            if (count < (rows * page) && count != 0)
                            {
                                page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                            }

                            if (SelectUCLBranchWiseObj.languageid == Convert.ToInt32(SelectUCLBranchWiseObj.GeneralLanguageID))
                            {
                                jsonResult = new
                                {
                                    total = total,
                                    page = page,
                                    records = count,
                                    data = modelAvailableStockList
                                        .Join(branchList, a => a.Branch_ID, b => b.Branch_ID, (a, b) => new
                                        {
                                            BranchID = a.Branch_ID,
                                            BranchName = b.Branch_Name,
                                            BranchUCLStock = a.ProcuctCount
                                        })
                                        .GroupBy(x => x.BranchID)
                                        .Select(g => new
                                        {
                                            BranchID = g.Key,
                                            BranchName = g.FirstOrDefault().BranchName,
                                            BranchUCLStock = g.Sum(x => x.BranchUCLStock)
                                        })
                                };
                            }
                            else
                            {
                                // Handle non-default language case
                            }
                        }
                    }
                }

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        #endregion

        #region ::: Select Out of WH Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        public static IActionResult SelectUCLSerialNumberAndStatus(SelectUCLSerialNumberAndStatusList SelectUCLSerialNumberAndStatusObj, string constring, int LogException, int page, int rows)
        {
            try
            {
                var jsonResult = default(dynamic);
                int count = 0;
                int total = 0;

                // Retrieve session values
                int Company_ID = Convert.ToInt32(SelectUCLSerialNumberAndStatusObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectUCLSerialNumberAndStatusObj.Branch);

                // Initialize connection
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Retrieve UnderClearenceSerialStatus using ADO.NET
                    int UnderClearenceSerialStatus;
                    using (SqlCommand cmd = new SqlCommand(" SELECT TOP 1 rmd.RefMasterDetail_ID\r\nFROM GNM_RefMasterDetail rmd\r\nJOIN GNM_RefMaster rm ON rmd.RefMaster_ID = rm.RefMaster_ID\r\nWHERE UPPER(rm.RefMaster_Name) = 'SERIALSTATUS'\r\nAND UPPER(rmd.RefMasterDetail_Short_Name) = 'UCL'", conn))
                    {
                        UnderClearenceSerialStatus = (int)cmd.ExecuteScalar();
                    }

                    // Define the query
                    string UCLQuery = @"
                SELECT P.PRODUCT_ID, W.COMPANY_ID, W.BRANCH_ID, P.WAREHOUSE_ID, P.PRODUCT_SERIALNUMBER, P.SERIALSTATUS 
                FROM GNM_PRODUCT P 
                JOIN GNM_WAREHOUSE W ON P.WAREHOUSE_ID = W.WAREHOUSE_ID 
                WHERE P.SERIALSTATUS = @SerialStatus 
                AND W.WAREHOUSETYPE_ID = 2 
                AND W.COMPANY_ID = @CompanyID 
                AND P.MODEL_ID = @ModelID

                UNION ALL 

                SELECT A.PRODUCT_ID, F.COMPANY_ID, F.BRANCH_ID, A.WAREHOUSE_ID, A.PRODUCT_SERIALNUMBER, A.SERIALSTATUS 
                FROM GNM_PRODUCT A 
                JOIN GNM_MODEL B ON A.MODEL_ID = B.MODEL_ID 
                JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID = C.REFMASTERDETAIL_ID 
                JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID = D.PRODUCTTYPE_ID 
                JOIN SLT_BILLOFENTRYMACHINEDETAILS E ON A.MODEL_ID = E.MODEL_ID 
                AND E.BRAND_ID = A.BRAND_ID 
                AND E.PRODUCTTYPE_ID = A.PRODUCTTYPE_ID 
                AND E.SERIALNUMBER = A.PRODUCT_SERIALNUMBER 
                JOIN SLT_BILLOFENTRY F ON E.BILLOFENTRY_ID = F.BILLOFENTRY_ID 
                LEFT OUTER JOIN GNM_WAREHOUSE G ON A.WAREHOUSE_ID = G.WAREHOUSE_ID 
                JOIN GNM_REFMASTERDETAIL H ON A.SERIALSTATUS = H.REFMASTERDETAIL_ID 
                WHERE A.SERIALSTATUS = @SerialStatus 
                AND F.COMPANY_ID = @CompanyID 
                AND A.MODEL_ID = @ModelID";

                    // Retrieve stock details
                    List<StockDetails> stockDetailsList = new List<StockDetails>();
                    using (SqlCommand cmd = new SqlCommand(UCLQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@SerialStatus", UnderClearenceSerialStatus);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        cmd.Parameters.AddWithValue("@ModelID", SelectUCLSerialNumberAndStatusObj.modelID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stockDetailsList.Add(new StockDetails
                                {
                                    PRODUCT_ID = reader.GetInt32(reader.GetOrdinal("PRODUCT_ID")),
                                    COMPANY_ID = reader.GetInt32(reader.GetOrdinal("COMPANY_ID")),
                                    BRANCH_ID = reader.GetInt32(reader.GetOrdinal("BRANCH_ID")),
                                    WAREHOUSE_ID = reader.GetInt32(reader.GetOrdinal("WAREHOUSE_ID")),
                                    PRODUCT_SERIALNUMBER = reader.GetString(reader.GetOrdinal("PRODUCT_SERIALNUMBER")),
                                    SERIALSTATUS = reader.GetInt32(reader.GetOrdinal("SERIALSTATUS"))
                                });
                            }
                        }
                    }

                    // Apply search filters if present
                    IQueryable<StockDetails> queryableStockDetails = stockDetailsList.AsQueryable();

                    //if (Request.Params["_search"] == "true")
                    //{
                    //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                    //    if (filters.rules.Count() > 0)
                    //    {
                    //        queryableStockDetails = queryableStockDetails.FilterSearch<StockDetails>(filters);
                    //    }
                    //}

                    // Apply pagination
                    count = queryableStockDetails.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    // Prepare additional data retrieval for status and warehouse names
                    var statusIds = queryableStockDetails.Select(a => a.SERIALSTATUS).Distinct().ToList();
                    var warehouseIds = queryableStockDetails.Select(a => a.WAREHOUSE_ID).Distinct().ToList();

                    // Retrieve status names
                    Dictionary<int, string> statusNames = new Dictionary<int, string>();
                    string statusQuery = SelectUCLSerialNumberAndStatusObj.languageid == Convert.ToInt32(SelectUCLSerialNumberAndStatusObj.GeneralLanguageID)
                        ? "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID IN (@Ids)"
                        : "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID IN (@Ids) AND Language_ID = @LanguageID";

                    using (SqlCommand cmd = new SqlCommand(statusQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Ids", string.Join(",", statusIds));
                        if (SelectUCLSerialNumberAndStatusObj.languageid != Convert.ToInt32(SelectUCLSerialNumberAndStatusObj.GeneralLanguageID))
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", SelectUCLSerialNumberAndStatusObj.languageid);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                statusNames[reader.GetInt32(0)] = reader.GetString(1);
                            }
                        }
                    }

                    // Retrieve warehouse names
                    Dictionary<int, string> warehouseNames = new Dictionary<int, string>();
                    string warehouseQuery = SelectUCLSerialNumberAndStatusObj.languageid == Convert.ToInt32(SelectUCLSerialNumberAndStatusObj.GeneralLanguageID)
                        ? "SELECT WareHouse_ID, WareHouseName FROM GNM_WareHouse WHERE WareHouse_ID IN (@Ids)"
                        : "SELECT WareHouse_ID, WareHouseName FROM GNM_WareHouseLocale WHERE WareHouse_ID IN (@Ids) AND Language_ID = @LanguageID";

                    using (SqlCommand cmd = new SqlCommand(warehouseQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Ids", string.Join(",", warehouseIds));
                        if (SelectUCLSerialNumberAndStatusObj.languageid != Convert.ToInt32(SelectUCLSerialNumberAndStatusObj.GeneralLanguageID))
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", SelectUCLSerialNumberAndStatusObj.languageid);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouseNames[reader.GetInt32(0)] = reader.GetString(1);
                            }
                        }
                    }

                    // Prepare the result
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = queryableStockDetails
                            .Skip((page - 1) * rows)
                            .Take(rows)
                            .Select(a => new
                            {
                                ProductID = a.PRODUCT_ID,
                                SerialNumber = a.PRODUCT_SERIALNUMBER,
                                Status = statusNames.ContainsKey(a.SERIALSTATUS) ? statusNames[a.SERIALSTATUS] : string.Empty,
                                WareHouse = warehouseNames.ContainsKey(a.WAREHOUSE_ID) ? warehouseNames[a.WAREHOUSE_ID] : string.Empty
                            }).ToList()

                    };

                    //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                    return new JsonResult(jsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }


        #endregion

        #region ::: Insert Model Header /Mithun:::
        /// <summary>
        /// To Insert Model Header
        /// </summary>
        public static IActionResult Insert(InsertModelMasterList InsertObj, string constring, int LogException)
        {
            try
            {
                int BranchID = Convert.ToInt32(InsertObj.Branch);
                int Company_ID = Convert.ToInt32(InsertObj.Company_ID);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = InsertObj.UserDetails.FirstOrDefault();
                int UserID = Convert.ToInt32(InsertObj.User_ID);
                DateTime LoggedINDateTime = Convert.ToDateTime(InsertObj.LoggedINDateTime);
                int MenuID = Convert.ToInt32(InsertObj.MenuID);

                var jObj = JObject.Parse(InsertObj.data);

                string ModelName = jObj["ModelName"].ToString();
                string IsActive = jObj["IsActive"].ToString();

                int? ServoceType_ID = (int?)jObj["ServoceType_ID"];
                ServoceType_ID = ServoceType_ID == 0 ? (int?)null : ServoceType_ID;

                int? ServiceFrequency = (int?)jObj["ServiceFrequency"];
                ServiceFrequency = ServiceFrequency == 0 ? (int?)null : ServiceFrequency;

                int Brand_ID = Convert.ToInt32(jObj["Brand_ID"]);
                int ProductType_ID = Convert.ToInt32(jObj["ProductType_ID"]);
                string Description = jObj["Description"].ToString();
                int? AttachmentCount = jObj["AttachmentCount"] != null ? (int?)jObj["AttachmentCount"] : null;
                string Series = jObj["Series"]?.ToString(); // Nullable

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlTransaction transaction = connection.BeginTransaction();

                    try
                    {
                        // Insert into GNM_Model
                        string insertModelQuery = @"
                    INSERT INTO GNM_Model (
                        ProductType_ID, Brand_ID, Model_Name, Model_IsActive, 
                        ModifiedBy, ModifiedDate, ServiceType_ID, ServiceFrequency, 
                        Model_Description, AttachmentCount, Series
                    ) 
                    VALUES (
                        @ProductType_ID, @Brand_ID, @ModelName, @IsActive, 
                        @ModifiedBy, @ModifiedDate, @ServiceType_ID, @ServiceFrequency, 
                        @ModelDescription, @AttachmentCount, @Series
                    );
                    SELECT SCOPE_IDENTITY();";

                        using (SqlCommand cmd = new SqlCommand(insertModelQuery, connection, transaction))
                        {
                            cmd.Parameters.AddWithValue("@ProductType_ID", ProductType_ID);
                            cmd.Parameters.AddWithValue("@Brand_ID", Brand_ID);
                            cmd.Parameters.AddWithValue("@ModelName", Common.DecryptString(ModelName));
                            cmd.Parameters.AddWithValue("@IsActive", IsActive == "checked");
                            cmd.Parameters.AddWithValue("@ModifiedBy", UserID);
                            cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            cmd.Parameters.AddWithValue("@ServiceType_ID", (object)ServoceType_ID ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ServiceFrequency", (object)ServiceFrequency ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ModelDescription", Common.DecryptString(Description) ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@AttachmentCount", (object)AttachmentCount ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Series", (object)Series ?? DBNull.Value);

                            int modelID = Convert.ToInt32(cmd.ExecuteScalar());

                            // Insert into GNM_MODELSERVICETYPEDET if needed
                            if (ServoceType_ID.HasValue)
                            {
                                string insertServiceTypeQuery = @"
                            INSERT INTO GNM_MODELSERVICETYPEDET (MODEL_ID, SERVICETYPE_ID, COMPANY_ID)
                            VALUES (@MODEL_ID, @SERVICETYPE_ID, @COMPANY_ID);";

                                using (SqlCommand serviceCmd = new SqlCommand(insertServiceTypeQuery, connection, transaction))
                                {
                                    serviceCmd.Parameters.AddWithValue("@MODEL_ID", modelID);
                                    serviceCmd.Parameters.AddWithValue("@SERVICETYPE_ID", ServoceType_ID.Value);
                                    serviceCmd.Parameters.AddWithValue("@COMPANY_ID", Company_ID);
                                    serviceCmd.ExecuteNonQuery();
                                }
                            }

                            // Commit transaction
                            transaction.Commit();

                            // Log details
                            //    gbl.InsertGPSDetails(Company_ID, BranchID, UserID, Common.GetObjectID("CoreModelMaster",constring), modelID, 0, 0, "Inserted Model- " + Common.DecryptString(ModelName), false, MenuID, LoggedINDateTime);

                            //return Json(new { Model_ID = modelID }, JsonRequestBehavior.AllowGet);
                            return new JsonResult(new { Model_ID = modelID });
                        }
                    }
                    catch (Exception ex)
                    {
                        // Rollback transaction
                        transaction.Rollback();

                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                        //return RedirectToAction("Error");
                        return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion


        #region ::: Update Model Header /Mithun:::
        /// <summary>
        /// To Update MOdel
        /// </summary>
        public static IActionResult Update(UpdateModelMasterList UpdateModelObj, string constring, int LogException)
        {
            try
            {
                // Initialize the parameters
                string SMP = (AppPath + "/ModelAttachments");
                int ObjectID = Common.GetObjectID("CoreModelMaster");
                int BranchID = Convert.ToInt32(UpdateModelObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = UpdateModelObj.UserDetails.FirstOrDefault();
                int Company_ID = Convert.ToInt32(UpdateModelObj.Company_ID);
                JObject jObj = JObject.Parse(UpdateModelObj.data);
                int Model_ID = Convert.ToInt32(jObj["Model_ID"]);
                string ModelName = jObj["ModelName"].ToString();
                bool IsActive = jObj["IsActive"].ToString() == "checked";
                int? ServiceType_ID = Convert.ToInt32(jObj["ServiceType_ID"]);
                ServiceType_ID = ServiceType_ID == 0 ? null : ServiceType_ID;
                int? ServiceFrequency = Convert.ToInt32(jObj["ServiceFrequency"]);
                ServiceFrequency = ServiceFrequency == 0 ? null : ServiceFrequency;
                int Brand_ID = Convert.ToInt32(jObj["Brand_ID"]);
                int ProductType_ID = Convert.ToInt32(jObj["ProductType_ID"]);
                string Description = jObj["Description"].ToString();

                // Update the Model details using ADO.NET
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        // Update model details
                        string updateModelQuery = @"
                    UPDATE GNM_Model 
                    SET Model_Name = @ModelName, Model_IsActive = @IsActive, 
                        ServiceType_ID = @ServiceType_ID, ServiceFrequency = @ServiceFrequency, 
                        Brand_ID = @Brand_ID, ProductType_ID = @ProductType_ID, 
                        ModifiedBy = @ModifiedBy, ModifiedDate = @ModifiedDate, 
                        Model_Description = @Description
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand cmd = new SqlCommand(updateModelQuery, conn, transaction))
                        {
                            cmd.Parameters.AddWithValue("@ModelName", Common.DecryptString(ModelName));
                            cmd.Parameters.AddWithValue("@IsActive", IsActive);
                            cmd.Parameters.AddWithValue("@ServiceType_ID", ServiceType_ID.HasValue ? (object)ServiceType_ID.Value : DBNull.Value);
                            cmd.Parameters.AddWithValue("@ServiceFrequency", ServiceFrequency.HasValue ? (object)ServiceFrequency.Value : DBNull.Value);
                            cmd.Parameters.AddWithValue("@Brand_ID", Brand_ID);
                            cmd.Parameters.AddWithValue("@ProductType_ID", ProductType_ID);
                            cmd.Parameters.AddWithValue("@ModifiedBy", Convert.ToInt32(UpdateModelObj.User_ID));
                            cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            cmd.Parameters.AddWithValue("@Description", Common.DecryptString(Description));
                            cmd.Parameters.AddWithValue("@Model_ID", Model_ID);
                            cmd.ExecuteNonQuery();
                        }

                        // Update ServiceType details (if needed)
                        string serviceTypeQuery = @"
                    IF EXISTS (SELECT * FROM GNM_MODELSERVICETYPEDET WHERE MODEL_ID = @Model_ID AND COMPANY_ID = @Company_ID)
                    BEGIN
                        UPDATE GNM_MODELSERVICETYPEDET 
                        SET SERVICETYPE_ID = @ServiceType_ID 
                        WHERE MODEL_ID = @Model_ID AND COMPANY_ID = @Company_ID
                    END
                    ELSE
                    BEGIN
                        INSERT INTO GNM_MODELSERVICETYPEDET (MODEL_ID, SERVICETYPE_ID, COMPANY_ID) 
                        VALUES (@Model_ID, @ServiceType_ID, @Company_ID)
                    END";

                        using (SqlCommand cmd = new SqlCommand(serviceTypeQuery, conn, transaction))
                        {
                            cmd.Parameters.AddWithValue("@Model_ID", Model_ID);
                            cmd.Parameters.AddWithValue("@ServiceType_ID", ServiceType_ID.HasValue ? (object)ServiceType_ID.Value : DBNull.Value);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            cmd.ExecuteNonQuery();
                        }

                        // Handle Attachment Upload
                        if (UpdateModelObj.AttachmentData != null)
                        {
                            JObject attachmentData = JObject.Parse(UpdateModelObj.AttachmentsData);
                            int attachmentCount = attachmentData["rows"].Count();
                            Attachements[] ds = new Attachements[attachmentCount];
                            for (int i = 0; i < attachmentCount; i++)
                            {
                                Attachements detail = new Attachements
                                {
                                    ATTACHMENTDETAIL_ID = Convert.ToInt32(attachmentData["rows"][i]["ATTACHMENTDETAIL_ID"]),
                                    FILE_NAME = Common.DecryptString(attachmentData["rows"][i]["FILENAME"].ToString()),
                                    FILEDESCRIPTION = Common.DecryptString(attachmentData["rows"][i]["FILEDESCRIPTION"].ToString()),
                                    Upload = Convert.ToInt32(attachmentData["rows"][i]["Upload"]),
                                    UPLOADDATE = Convert.ToDateTime(attachmentData["rows"][i]["UPLOADDATE"]),
                                    OBJECTID = Convert.ToInt32(attachmentData["rows"][i]["OBJECT_ID"]),
                                    autodisp = attachmentData["rows"][i]["autodisp"].ToString() == "Yes",
                                    Prin = attachmentData["rows"][i]["Prin"].ToString() == "Yes",
                                    DetailID = 0
                                };

                                string dstPath = Path.Combine(SMP, $"{detail.OBJECTID}-{Model_ID}-{Common.DecryptString(detail.FILE_NAME)}");
                                string srcPath = Path.Combine(SMP, $"Temp_{UpdateModelObj.ObjectID}_{UpdateModelObj.User_ID}/{Common.DecryptString(detail.FILE_NAME)}");

                                if (!File.Exists(dstPath))
                                {
                                    File.Move(srcPath, dstPath);
                                }
                            }

                            // Call to a method that handles the actual attachment upload logic
                            UploadAttachment(ds, Model_ID, Convert.ToInt32(UpdateModelObj.User_ID), Company_ID, 0, constring, LogException);

                        }

                        // Handle Attachment Deletion
                        if (UpdateModelObj.ModelAttachmentDelete != null)
                        {
                            JObject attachmentDelete = JObject.Parse(UpdateModelObj.ModelAttachmentDelete);
                            int deleteCount = attachmentDelete["rows"].Count();
                            Attachements[] ds1 = new Attachements[deleteCount];
                            for (int i = 0; i < deleteCount; i++)
                            {
                                Attachements detail = new Attachements
                                {
                                    ATTACHMENTDETAIL_ID = Convert.ToInt32(attachmentDelete["rows"][i]["id"]),
                                    FILE_NAME = Common.DecryptString(attachmentDelete["rows"][i]["FileName"].ToString()),
                                    OBJECTID = Convert.ToInt32(attachmentDelete["rows"][i]["Object_ID"]),
                                    TransactionID = Model_ID
                                };

                                DeleteAttachments(ds1, SMP, constring);  // Implement this method to delete attachment files
                            }
                        }

                        // Update attachment count
                        int attCount = GetAttachmentCountForModel(ObjectID, Model_ID, 0, constring);
                        string updateAttachmentCountQuery = @"
                    UPDATE GNM_Model 
                    SET AttachmentCount = @AttachmentCount 
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand cmd = new SqlCommand(updateAttachmentCountQuery, conn, transaction))
                        {
                            cmd.Parameters.AddWithValue("@AttachmentCount", attCount);
                            cmd.Parameters.AddWithValue("@Model_ID", Model_ID);
                            cmd.ExecuteNonQuery();
                        }

                        transaction.Commit(); // Commit transaction if all succeeds
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback(); // Rollback transaction if an error occurs
                        throw ex; // Log or handle exception

                    }
                }
                return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };

            }
            catch (Exception ex)
            {
                // Log exception if necessary
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion


        public static int GetAttachmentCountForModel(int ObjectID, int TransactionID, int DetailID, string constring)
        {
            int attachmentCount = 0;

            // Define the query
            string query = @"SELECT COUNT(*) 
                     FROM GNM_MODELATTACHMENTDETAIL 
                     WHERE OBJECT_ID = @ObjectID AND MODEL_ID = @TransactionID";

            // Use a SQL connection
            using (SqlConnection conn = new SqlConnection(constring))
            {
                // Create SQL command
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to avoid SQL injection
                    cmd.Parameters.AddWithValue("@ObjectID", ObjectID);
                    cmd.Parameters.AddWithValue("@TransactionID", TransactionID);

                    // Open the connection
                    conn.Open();

                    // Execute the scalar query to get the count
                    attachmentCount = (int)cmd.ExecuteScalar();
                }
            }

            return attachmentCount;
        }


        public static List<Attachements> UploadAttachment(Attachements[] ds, int TransactionID, int User_ID, int Company_ID, int DetailID, string constring, int LogException)
        {
            List<Attachements> ds1 = new List<Attachements>();
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int k = 0; k < ds.Count(); k++)
                    {
                        if (ds[k].FILE_NAME != null && ds[k].ATTACHMENTDETAIL_ID == 0)
                        {
                            // SQL query to insert new attachment
                            string query = @"INSERT INTO GNM_MODELATTACHMENTDETAIL
                                    (MODEL_ID, OBJECT_ID, FILENAME, FILEDESCRIPTION, UPLOADBY, UPLOADDATE, AUTODISPLAY, PRINT)
                                     VALUES (@ModelID, @ObjectID, @Filename, @FileDescription, @UploadBy, @UploadDate, @AutoDisplay, @Print)";

                            using (SqlCommand cmd = new SqlCommand(query, conn))
                            {
                                // Add parameters to avoid SQL injection
                                cmd.Parameters.AddWithValue("@ModelID", TransactionID);
                                cmd.Parameters.AddWithValue("@ObjectID", ds[k].OBJECTID);
                                cmd.Parameters.AddWithValue("@Filename", Common.DecryptString(ds[k].FILE_NAME));
                                cmd.Parameters.AddWithValue("@FileDescription", Common.DecryptString(ds[k].FILEDESCRIPTION));
                                cmd.Parameters.AddWithValue("@UploadBy", User_ID);
                                cmd.Parameters.AddWithValue("@UploadDate", ds[k].UPLOADDATE);
                                cmd.Parameters.AddWithValue("@AutoDisplay", ds[k].autodisp);
                                cmd.Parameters.AddWithValue("@Print", ds[k].Prin);

                                // Execute the query
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                return null;
                throw;
            }

            return ds1;
        }

        public static string DeleteAttachments(Attachements[] dsObj, string SMP, string constring)
        {
            string Msg = string.Empty;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int i = 0; i < dsObj.Length; i++)
                    {
                        int ID = dsObj[i].ATTACHMENTDETAIL_ID;
                        if (ID != 0)
                        {
                            // Delete the row from the GNM_MODELATTACHMENTDETAIL table
                            string query = @"DELETE FROM GNM_MODELATTACHMENTDETAIL WHERE MODELATTACHMENTDETAIL_ID = @ID";

                            using (SqlCommand cmd = new SqlCommand(query, conn))
                            {
                                cmd.Parameters.AddWithValue("@ID", ID);
                                cmd.ExecuteNonQuery();
                            }

                            // Delete the file from the file system
                            string filePath = Path.Combine(SMP, $"{dsObj[i].OBJECTID}-{dsObj[i].TransactionID}-{Common.DecryptString(dsObj[i].FILE_NAME)}");
                            if (File.Exists(filePath))
                            {
                                File.Delete(filePath);
                            }
                        }
                    }

                    Msg = "Deleted";
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Msg;
        }


        #region ::: Select ServiceType in Model Header /Mithun:::
        /// <summary>
        /// To Select Service Type
        /// </summary> 
        public static IActionResult SelectServiceType(SelectServiceTypeList SelectServiceTypeObj, string constring, int LogException)
        {
            var Masterdata = new object();
            try
            {
                int Company_ID = Convert.ToInt32(SelectServiceTypeObj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectServiceTypeObj.UserLanguageID);

                // Define the lists to hold service types and parent company details
                List<GNM_ServiceType> serviceTypes = new List<GNM_ServiceType>();
                List<GNM_ServiceType> currentServiceTypes = new List<GNM_ServiceType>();
                List<ParentCompanyObject> parentCompanyDetails = new List<ParentCompanyObject>();

                // Fetch the service types
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query for fetching active service types
                    string serviceTypeQuery = @"SELECT * FROM GNM_ServiceType 
                                        WHERE ServiceType_Active = 1 
                                        AND IsMandatoryService = 0 
                                        AND IsWarrantyClaimable = 0 
                                        AND IsInsuranceJob = 0";

                    using (SqlCommand cmd = new SqlCommand(serviceTypeQuery, conn))
                    {
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            serviceTypes.Add(new GNM_ServiceType
                            {
                                ServiceType_ID = (int)reader["ServiceType_ID"],
                                ServiceType_Name = reader["ServiceType_Name"].ToString(),
                                Company_ID = (int)reader["Company_ID"]
                            });
                        }
                        reader.Close();
                    }

                    // Query for fetching the current service type
                    if (SelectServiceTypeObj.ServiceTypeID.HasValue)
                    {
                        string currentServiceTypeQuery = @"SELECT * FROM GNM_ServiceType WHERE ServiceType_ID = @ServiceTypeID";
                        using (SqlCommand cmd = new SqlCommand(currentServiceTypeQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ServiceTypeID", SelectServiceTypeObj.ServiceTypeID.Value);
                            SqlDataReader reader = cmd.ExecuteReader();
                            while (reader.Read())
                            {
                                currentServiceTypes.Add(new GNM_ServiceType
                                {
                                    ServiceType_ID = (int)reader["ServiceType_ID"],
                                    ServiceType_Name = reader["ServiceType_Name"].ToString()
                                });
                            }
                            reader.Close();
                        }
                    }

                    // Query for fetching parent company details
                    string parentCompanyQuery = @";WITH ParentCompany([Company_ID],[Company_Name],[Company_Parent_ID]) AS 
                                           (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM GNM_Company WHERE [Company_ID] = @Company_ID 
                                           UNION ALL 
                                           SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] 
                                           FROM GNM_Company AS child 
                                           JOIN ParentCompany ON child.[Company_ID] = ParentCompany.[Company_Parent_ID])
                                           SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM ParentCompany";
                    using (SqlCommand cmd = new SqlCommand(parentCompanyQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            parentCompanyDetails.Add(new ParentCompanyObject
                            {
                                Company_ID = (int)reader["Company_ID"],
                                Company_Name = reader["Company_Name"].ToString(),
                                Company_Parent_ID = (int)reader["Company_Parent_ID"]
                            });
                        }
                        reader.Close();
                    }
                }

                // Filter the service types based on parent company details
                var filteredServiceTypes = serviceTypes
                    .Where(st => parentCompanyDetails.Any(pc => pc.Company_ID == st.Company_ID));

                // Save the current service type name in session

                //Session["CurrentServiceType"] = currentServiceTypes
                //    .Select(ref1 => new { name = ref1.ServiceType_Name });

                // Prepare the response based on the selected language
                if (SelectServiceTypeObj.LanguageID == Convert.ToInt32(SelectServiceTypeObj.GeneralLanguageID))
                {
                    Masterdata = new
                    {
                        ReferenceMasterData = filteredServiceTypes
                            .OrderBy(st => st.ServiceType_Name)
                            .Select(st => new
                            {
                                ID = st.ServiceType_ID,
                                Name = st.ServiceType_Name
                            })
                    };
                }
                else
                {
                    // Fetch localized service type names
                    foreach (var st in filteredServiceTypes)
                    {
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            conn.Open();
                            string localeQuery = @"SELECT ServiceType_Name FROM GNM_ServiceTypeLocale 
                                           WHERE ServiceType_ID = @ServiceTypeID AND Language_ID = @LanguageID";
                            using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@ServiceTypeID", st.ServiceType_ID);
                                cmd.Parameters.AddWithValue("@LanguageID", SelectServiceTypeObj.LanguageID);
                                string localizedName = cmd.ExecuteScalar()?.ToString() ?? string.Empty;

                                Masterdata = new
                                {
                                    ReferenceMasterData = filteredServiceTypes
                                        .OrderBy(sn => st.ServiceType_Name)
                                        .Select(sn => new
                                        {
                                            ID = st.ServiceType_ID,
                                            Name = localizedName
                                        })
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }




        #endregion

        #region ::: Select Brand /Mithun:::
        /// <summary>
        /// To Select Brand
        /// </summary> 
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterModelMasterList SelectReferenceMasterObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic); // Initialize with an empty result
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    int Company_ID = Convert.ToInt32(SelectReferenceMasterObj.Company_ID);
                    int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);
                    string userLanguageCode = SelectReferenceMasterObj.UserLanguageCode.ToString();
                    string generalLanguageCode = SelectReferenceMasterObj.GeneralLanguageCode.ToString();

                    string query;
                    SqlCommand command;

                    if (userLanguageCode == generalLanguageCode)
                    {
                        query = @"
                    SELECT RefMasterDetail_ID AS ID, RefMasterDetail_Name AS Name
                    FROM GNM_RefMasterDetail
                    WHERE GNM_RefMasterDetail.RefMaster_Name = 'BRAND'
                      AND GNM_RefMasterDetail.RefMasterDetail_IsActive = 1
                      AND GNM_RefMasterDetail.Company_ID = @Company_ID  
                    ORDER BY RefMasterDetail_Name";

                        command = new SqlCommand(query, connection);
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    }
                    else
                    {
                        query = @"
                    SELECT RefL.RefMasterDetail_ID AS ID, RefL.RefMasterDetail_Name AS Name
                    FROM GNM_RefMasterDetailLocale AS RefL
                    INNER JOIN GNM_RefMasterDetail AS Ref ON Ref.RefMasterDetail_ID = RefL.RefMasterDetail_ID
                    WHERE Ref.GNM_RefMaster.RefMaster_Name = 'BRAND'
                      AND RefL.Language_ID = @Language_ID
                      AND Ref.RefMasterDetail_IsActive = 1
                      AND Ref.Company_ID = @Company_ID
                    ORDER BY RefL.RefMasterDetail_Name";

                        command = new SqlCommand(query, connection);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    }

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        var referenceMasterData = new List<object>();

                        while (reader.Read())
                        {
                            referenceMasterData.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }

                        Masterdata = new
                        {
                            ReferenceMasterData = referenceMasterData
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion

        #region ::: Select ProductType /Mithun:::
        /// <summary>
        /// To Select Product type
        /// </summary> 
        public static IActionResult SelectProductType(SelectProductTypeModelList SelectProductTypeObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectProductTypeObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command;

                    if (SelectProductTypeObj.UserLanguageCode.ToString() == SelectProductTypeObj.GeneralLanguageCode.ToString())
                    {
                        command = new SqlCommand("Up_SEL_ActiveProductTypes", connection);
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@BrandID", SelectProductTypeObj.BrandID);
                    }
                    else
                    {
                        command = new SqlCommand("Up_SEL_ActiveProductTypesByLanguage", connection);
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@BrandID", SelectProductTypeObj.BrandID);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);
                    }

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        var productTypeData = new List<dynamic>();
                        while (reader.Read())
                        {
                            productTypeData.Add(new
                            {
                                ID = reader["ProductType_ID"],
                                Name = reader["ProductType_Name"].ToString()
                            });
                        }

                        // Perform ordering on the resulting list of dynamic objects
                        var orderedProductTypeData = productTypeData.OrderBy(pt => pt.Name);

                        Masterdata = new { ProductTypeData = orderedProductTypeData };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion

        #region ::: Check Duplicate Servicetype In Mandatory Service details Grid /Mithun:::
        /// <summary>
        /// to check if the Service type is already exists or not
        /// </summary>
        public static IActionResult CheckServiceType(CheckServiceTypeList CheckServiceTypeObj, string constring, int LogException)
        {
            int status = 0;
            try
            {

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    if (CheckServiceTypeObj.EffectiveDate != null)
                    {
                        string query = @"
                    SELECT COUNT(1)
                    FROM GNM_ModelServiceChargeDetail
                    WHERE ServiceType_ID = @ServiceType_ID
                      AND ModelServiceChargeDetail_ID != @ModelServiceChargeDetail_ID
                      AND Model_ID = @ModelID
                      AND EffectiveDate = @EffectiveDate";

                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            // Add parameters to the query
                            command.Parameters.AddWithValue("@ServiceType_ID", CheckServiceTypeObj.ServiceType_ID);
                            command.Parameters.AddWithValue("@ModelServiceChargeDetail_ID", CheckServiceTypeObj.ModelServiceChargeDetail_ID);
                            command.Parameters.AddWithValue("@ModelID", CheckServiceTypeObj.ModelID);
                            command.Parameters.AddWithValue("@EffectiveDate", CheckServiceTypeObj.EffectiveDate);

                            // Execute the query and check if any row exists
                            int count = (int)command.ExecuteScalar();

                            // If the count is greater than 0, set status to 1
                            if (count > 0)
                            {
                                status = 1;
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: Select Mandatory Service details and Load Service Type /Mithun:::
        /// <summary>
        /// To select Mandatory Service details and load service type
        /// </summary>
        public static IActionResult SelectServiceChargedetail(SelectServiceChargedetailList SelectServiceChargedetailObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonData = default(dynamic);
            try
            {
                using (SqlConnection con = new SqlConnection(constring))
                {
                    con.Open();

                    int Count = 0;
                    int Total = 0;
                    int Company_ID = Convert.ToInt32(SelectServiceChargedetailObj.Company_ID);

                    // Fetch Model Service Charge Detail List
                    List<GNM_ModelServiceChargeDetail> IEModelServiceChargeDetailList = new List<GNM_ModelServiceChargeDetail>();
                    string serviceChargeQuery = "SELECT * FROM GNM_ModelServiceChargeDetail WHERE Model_ID = @ModelID";
                    using (SqlCommand cmd = new SqlCommand(serviceChargeQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@ModelID", SelectServiceChargedetailObj.ModelID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_ModelServiceChargeDetail serviceChargeDetail = new GNM_ModelServiceChargeDetail
                                {
                                    ModelServiceChargeDetail_ID = Convert.ToInt32(reader["ModelServiceChargeDetail_ID"]),
                                    Model_ID = Convert.ToInt32(reader["Model_ID"]),
                                    ServiceType_ID = Convert.ToInt32(reader["ServiceType_ID"]),
                                    // Assuming reader is your SqlDataReader instance
                                    ServiceCharge = reader["ServiceCharge"] != DBNull.Value ? (decimal?)Convert.ToDecimal(reader["ServiceCharge"]) : (decimal?)null,
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    EffectiveDate = reader["EffectiveDate"] != DBNull.Value ? Convert.ToDateTime(reader["EffectiveDate"]) : (DateTime?)null
                                };
                                IEModelServiceChargeDetailList.Add(serviceChargeDetail);
                            }
                        }
                    }

                    // Fetch Parent Company Details using recursive CTE
                    List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                    string parentCompanyQuery = @"
                ;WITH ParentCompany([Company_ID], [Company_Name], [Company_Parent_ID]) AS 
                (
                    SELECT [Company_ID], [Company_Name], [Company_Parent_ID] 
                    FROM dbo.GNM_Company 
                    WHERE [Company_ID] = @CompanyID
                    UNION ALL
                    SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] 
                    FROM dbo.GNM_Company AS child 
                    JOIN ParentCompany ON child.[Company_Parent_ID] = ParentCompany.[Company_ID]
                )
                SELECT [Company_ID], [Company_Name], [Company_Parent_ID] FROM ParentCompany;";
                    using (SqlCommand cmd = new SqlCommand(parentCompanyQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParentCompanyObject parentCompany = new ParentCompanyObject
                                {
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = Convert.ToString(reader["Company_Name"]),
                                    Company_Parent_ID = (int)(reader["Company_Parent_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_Parent_ID"]) : (int?)null)
                                };
                                ParentCompanyDetails.Add(parentCompany);
                            }
                        }
                    }

                    // Fetch Service Type List
                    List<GNM_ServiceType> ServiceList = new List<GNM_ServiceType>();
                    string serviceTypeQuery = "SELECT * FROM GNM_ServiceType WHERE ServiceType_Active = 1 AND (IsCommissioning = 1 OR IsMandatoryService = 1)";
                    using (SqlCommand cmd = new SqlCommand(serviceTypeQuery, con))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_ServiceType serviceType = new GNM_ServiceType
                                {
                                    ServiceType_ID = Convert.ToInt32(reader["ServiceType_ID"]),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    ServiceType_Name = Convert.ToString(reader["ServiceType_Name"]),
                                    ServiceType_Active = Convert.ToBoolean(reader["ServiceType_Active"]),
                                    IsMandatoryService = Convert.ToBoolean(reader["IsMandatoryService"]),
                                    ServiceDueHours = reader["ServiceDueHours"] != DBNull.Value ? Convert.ToInt32(reader["ServiceDueHours"]) : (int?)null,
                                    ServiceDueDays = reader["ServiceDueDays"] != DBNull.Value ? Convert.ToInt32(reader["ServiceDueDays"]) : (int?)null,
                                    IsWarrantyClaimable = Convert.ToBoolean(reader["IsWarrantyClaimable"]),
                                    IsDemandDrive = Convert.ToBoolean(reader["IsDemandDrive"]),
                                    IsInsuranceJob = Convert.ToBoolean(reader["IsInsuranceJob"]),
                                    ServiceType_Code = Convert.ToString(reader["ServiceType_Code"]),
                                };
                                ServiceList.Add(serviceType);
                            }
                        }
                    }

                    // Filter ServiceTypeList based on Parent Company Details
                    var ServiceTypeList = (from a in ServiceList
                                           join b in ParentCompanyDetails on a.Company_ID equals b.Company_ID
                                           where a.ServiceType_Active == true
                                           select a).ToList();

                    ServiceTypeList = ServiceTypeList.OrderBy(i => i.ServiceType_Name).ToList();
                    string JsonServiceType = "-1:--" + CommonFunctionalities.GetGlobalResourceObject(SelectServiceChargedetailObj.GeneralCulture.ToString(), "select").ToString() + "--;";

                    if (SelectServiceChargedetailObj.LanguageID == Convert.ToInt32(SelectServiceChargedetailObj.GeneralLanguageID))
                    {
                        for (int i = 0; i < ServiceTypeList.Count(); i++)
                        {
                            JsonServiceType = JsonServiceType + ServiceTypeList[i].ServiceType_ID + ":" + ServiceTypeList[i].ServiceType_Name + ";";
                        }
                    }
                    else
                    {
                        // Fetch ServiceTypeLocale
                        List<dynamic> ServiceTypeLocaleArray = new List<dynamic>();
                        string serviceTypeLocaleQuery = @"
                    SELECT sl.ServiceType_ID, sl.ServiceType_Name 
                    FROM GNM_ServiceTypeLocale sl 
                    JOIN GNM_ServiceType st ON sl.ServiceType_ID = st.ServiceType_ID 
                    WHERE sl.Language_ID = @LanguageID";
                        using (SqlCommand cmd = new SqlCommand(serviceTypeLocaleQuery, con))
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", SelectServiceChargedetailObj.LanguageID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceTypeLocaleArray.Add(new
                                    {
                                        ServiceType_ID = Convert.ToInt32(reader["ServiceType_ID"]),
                                        ServiceType_Name = Convert.ToString(reader["ServiceType_Name"])
                                    });
                                }
                            }
                        }

                        for (int i = 0; i < ServiceTypeLocaleArray.Count(); i++)
                        {
                            JsonServiceType = JsonServiceType + ServiceTypeLocaleArray[i].ServiceType_ID + ":" + ServiceTypeLocaleArray[i].ServiceType_Name + ";";
                        }


                        JsonServiceType = JsonServiceType.TrimEnd(';');

                        // Fetch model service charge details based on language ID
                        IEnumerable<ModelServiceChargeDetail> IEModelServiceChargeDetailArray = null;
                        if (SelectServiceChargedetailObj.LanguageID == Convert.ToInt32(SelectServiceChargedetailObj.GeneralLanguageID))
                        {
                            IEModelServiceChargeDetailArray = from a in IEModelServiceChargeDetailList
                                                              join s in ServiceList on a.ServiceType_ID equals s.ServiceType_ID
                                                              join d in ParentCompanyDetails on a.Company_ID equals d.Company_ID
                                                              select new ModelServiceChargeDetail
                                                              {
                                                                  ModelServiceChargeDetail_ID = Convert.ToInt32(a.ModelServiceChargeDetail_ID),
                                                                  Model_ID = Convert.ToInt32(a.Model_ID),
                                                                  ServiceType_Name = s.ServiceType_Name,
                                                                  ServiceCharge = Convert.ToString(a.ServiceCharge),
                                                                  Company_ID = Convert.ToInt32(a.Company_ID),
                                                                  EffectiveDateSort = a.EffectiveDate != null ? a.EffectiveDate.Value.ToString("dd-MMM-yyyy") : "",
                                                              };
                        }
                        else
                        {
                            IEModelServiceChargeDetailArray = from a in IEModelServiceChargeDetailList
                                                              join s in ServiceList on a.ServiceType_ID equals s.ServiceType_ID
                                                              join sl in ServiceTypeLocaleArray on s.ServiceType_ID equals sl.ServiceType_ID
                                                              join d in ParentCompanyDetails on a.Company_ID equals d.Company_ID
                                                              select new ModelServiceChargeDetail
                                                              {
                                                                  ModelServiceChargeDetail_ID = Convert.ToInt32(a.ModelServiceChargeDetail_ID),
                                                                  Model_ID = Convert.ToInt32(a.Model_ID),
                                                                  ServiceType_Name = sl.ServiceType_Name,
                                                                  ServiceCharge = Convert.ToString(a.ServiceCharge),
                                                                  Company_ID = Convert.ToInt32(a.Company_ID),
                                                                  EffectiveDateSort = a.EffectiveDate != null ? a.EffectiveDate.Value.ToString("dd-MMM-yyyy") : "",
                                                              };
                        }

                        var IQModelServiceChargeDetail = IEModelServiceChargeDetailArray.AsQueryable();

                        // Apply filters and search
                        //if (Request.Params["_search"] == "true")
                        //{
                        //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                        //    IQModelServiceChargeDetail = IQModelServiceChargeDetail.FilterSearch(filters);
                        //}
                        //else if (Request.Params["advnce"] == "true")
                        //{
                        //    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();
                        //    IQModelServiceChargeDetail = IQModelServiceChargeDetail.AdvanceSearch(advnfilter);
                        //}

                        IQModelServiceChargeDetail = IQModelServiceChargeDetail.OrderByField(sidx, sord);

                        //if (LanguageID == Convert.ToInt32(Session["GeneralLanguageID"]))
                        //{
                        //    Session["IQModelServiceChargeDetailList"] = IQModelServiceChargeDetail.ToList();
                        //}
                        //else
                        //{
                        //    Session["IQModelServiceChargeDetailList"] = IQModelServiceChargeDetail.ToList();
                        //}

                        Count = IQModelServiceChargeDetail.Count();
                        Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                        jsonData = new
                        {
                            total = Total,
                            page = page,
                            rows = (from a in IQModelServiceChargeDetail.AsEnumerable()
                                    select new
                                    {
                                        ID = a.ModelServiceChargeDetail_ID,
                                        edit = $"<a title='{CommonFunctionalities.GetGlobalResourceObject(SelectServiceChargedetailObj.UserCulture.ToString(), "edit").ToString()}' href='#' id='{a.ModelServiceChargeDetail_ID}' key='{a.ModelServiceChargeDetail_ID}' class='{(a.Company_ID == Company_ID ? "EditModelServiceChargeDetail font-icon-class" : "NonEditModelServiceChargeDetail font-icon-class")}' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                        delete = $"<input type='checkbox' key='{a.ModelServiceChargeDetail_ID}' defaultchecked='' id='chk{a.ModelServiceChargeDetail_ID}' class='{(a.Company_ID == Company_ID ? "ModelServiceChargeDelete" : "NotModelServiceChargeDelete")}'/>",
                                        ServiceType_Name = a.ServiceType_Name,
                                        ServiceCharge = a.ServiceCharge, // Converts decimal? to string with 2 decimal places
                                        EffectiveDateSort = !string.IsNullOrEmpty(a.EffectiveDateSort) ? Convert.ToDateTime(a.EffectiveDateSort).ToString("dd-MMM-yyyy") : string.Empty
                                    }).ToList(),
                            records = Count,
                            JsonServiceType,
                        };

                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Insert Mandatory service details /Mithun:::
        /// <summary>
        /// To Save Mandatory Service Details
        /// </summary>
        public static IActionResult InsertServiceChargeDetail(InsertServiceChargeDetailList InsertServiceChargeDetailObj, string constring, int LogException)
        {
            string modelMessage = string.Empty;

            try
            {
                int companyID = Convert.ToInt32(InsertServiceChargeDetailObj.Company_ID);
                JObject jObj = JObject.Parse(InsertServiceChargeDetailObj.data);
                int count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlTransaction transaction = connection.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < count; i++)
                        {
                            var mRow = jObj["rows"].ElementAt(i).ToObject<GNM_ModelServiceChargeDetail>();

                            if (mRow.ModelServiceChargeDetail_ID != 0)
                            {
                                // Update existing record
                                string updateQuery = @"
                            UPDATE GNM_ModelServiceChargeDetail
                            SET ServiceType_ID = @ServiceType_ID,
                                ServiceCharge = @ServiceCharge,
                                EffectiveDate = @EffectiveDate,
                                Company_ID = @Company_ID
                            WHERE ModelServiceChargeDetail_ID = @ModelServiceChargeDetail_ID";

                                using (SqlCommand command = new SqlCommand(updateQuery, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@ServiceType_ID", mRow.ServiceType_ID);
                                    command.Parameters.AddWithValue("@ServiceCharge", mRow.ServiceCharge);
                                    command.Parameters.AddWithValue("@EffectiveDate", mRow.EffectiveDate);
                                    command.Parameters.AddWithValue("@Company_ID", companyID);
                                    command.Parameters.AddWithValue("@ModelServiceChargeDetail_ID", mRow.ModelServiceChargeDetail_ID);
                                    command.ExecuteNonQuery();
                                }
                            }
                            else
                            {
                                // Insert new record
                                string insertQuery = @"
                            INSERT INTO GNM_ModelServiceChargeDetail (Model_ID, ServiceType_ID, ServiceCharge, Company_ID, EffectiveDate)
                            VALUES (@Model_ID, @ServiceType_ID, @ServiceCharge, @Company_ID, @EffectiveDate)";

                                using (SqlCommand command = new SqlCommand(insertQuery, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@Model_ID", mRow.Model_ID);
                                    command.Parameters.AddWithValue("@ServiceType_ID", mRow.ServiceType_ID);
                                    command.Parameters.AddWithValue("@ServiceCharge", mRow.ServiceCharge);
                                    command.Parameters.AddWithValue("@Company_ID", companyID);
                                    command.Parameters.AddWithValue("@EffectiveDate", mRow.EffectiveDate);
                                    command.ExecuteNonQuery();
                                }
                            }

                            // Call the InsertGPSDetails method
                            //gbl.InsertGPSDetails(
                            //    companyID,
                            //    Convert.ToInt32(InsertServiceChargeDetailObj.Branch),
                            //    Convert.ToInt32(InsertServiceChargeDetailObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                            //    Convert.ToInt32(mRow.Model_ID),
                            //    0,
                            //    0,
                            //    "Update",
                            //    false,
                            //    Convert.ToInt32(InsertServiceChargeDetailObj.MenuID)
                            //);
                        }

                        transaction.Commit();
                        modelMessage = "Saved";
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                        modelMessage = string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                modelMessage = string.Empty;
            }

            //return modelMessage;
            return new JsonResult(modelMessage);
        }


        #endregion

        #region ::: Select Particular Model when click on globe /Mithun:::
        /// <summary>
        /// To Select  Particular Model when click on globe
        /// </summary>
        public static IActionResult SelectParticularModel(SelectParticularModelList SelectParticularModelObj, string constring, int LogException)
        {
            var result = default(dynamic); // Default empty object

            try
            {
                int companyID = Convert.ToInt32(SelectParticularModelObj.Company_ID);
                int languageID = Convert.ToInt32(SelectParticularModelObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve model data
                    string modelQuery = @"
                SELECT Model_ID, Model_Name, Model_Description, Model_IsActive, ServiceFrequency
                FROM GNM_Model
                WHERE Model_ID = @Model_ID";

                    using (SqlCommand modelCommand = new SqlCommand(modelQuery, connection))
                    {
                        modelCommand.Parameters.AddWithValue("@Model_ID", SelectParticularModelObj.id);
                        using (SqlDataReader modelReader = modelCommand.ExecuteReader())
                        {
                            if (modelReader.Read())
                            {
                                int modelID = modelReader.GetInt32(modelReader.GetOrdinal("Model_ID"));
                                string modelName = modelReader.GetString(modelReader.GetOrdinal("Model_Name"));
                                string modelDescription = modelReader.GetString(modelReader.GetOrdinal("Model_Description"));
                                bool modelIsActive = modelReader.GetBoolean(modelReader.GetOrdinal("Model_IsActive"));
                                int serviceFrequency = modelReader.GetInt32(modelReader.GetOrdinal("ServiceFrequency"));

                                // Retrieve model service type data
                                string serviceTypeQuery = @"
                            SELECT SERVICETYPE_ID
                            FROM GNM_MODELSERVICETYPEDET
                            WHERE MODEL_ID = @Model_ID AND COMPANY_ID = @Company_ID";

                                using (SqlCommand serviceTypeCommand = new SqlCommand(serviceTypeQuery, connection))
                                {
                                    serviceTypeCommand.Parameters.AddWithValue("@Model_ID", modelID);
                                    serviceTypeCommand.Parameters.AddWithValue("@Company_ID", companyID);

                                    int serviceTypeID = 0;
                                    using (SqlDataReader serviceTypeReader = serviceTypeCommand.ExecuteReader())
                                    {
                                        if (serviceTypeReader.Read())
                                        {
                                            serviceTypeID = serviceTypeReader.GetInt32(serviceTypeReader.GetOrdinal("SERVICETYPE_ID"));
                                        }
                                    }

                                    result = new
                                    {
                                        Model_ID = modelID,
                                        Model_Name = modelName,
                                        Model_Description = modelDescription,
                                        Model_IsActive = modelIsActive,
                                        ServiceFrequency = serviceFrequency,
                                        ServiceType_ID = serviceTypeID
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(result, JsonRequestBehavior.AllowGet);
            return new JsonResult(result);
        }
        #endregion

        #region ::: Select Model Locale data when click on globe /Mithun:::
        /// <summary>
        ///  To select the Model Locale data when click on globe
        /// </summary>    
        public static IActionResult SelectSingleModelLocale(SelectSingleModelLocaleList SelectSingleModelLocaleObj, string constring, int LogException)
        {
            var result = default(dynamic); // Default empty object

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve model data
                    string modelQuery = @"
                SELECT Model_ID, Model_Description, ServiceFrequency, ServiceType_ID, Model_IsActive
                FROM GNM_Model
                WHERE Model_ID = @Model_ID";

                    using (SqlCommand modelCommand = new SqlCommand(modelQuery, connection))
                    {
                        modelCommand.Parameters.AddWithValue("@Model_ID", SelectSingleModelLocaleObj.id);

                        // Retrieve model locale data
                        string modelLocaleQuery = @"
                    SELECT Model_Name, Model_Description
                    FROM GNM_ModelLocale
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand modelLocaleCommand = new SqlCommand(modelLocaleQuery, connection))
                        {
                            modelLocaleCommand.Parameters.AddWithValue("@Model_ID", SelectSingleModelLocaleObj.id);

                            using (SqlDataReader modelReader = modelCommand.ExecuteReader())
                            {
                                if (modelReader.Read())
                                {
                                    int modelID = modelReader.GetInt32(modelReader.GetOrdinal("Model_ID"));
                                    string modelDescription = modelReader.GetString(modelReader.GetOrdinal("Model_Description"));
                                    int serviceFrequency = modelReader.GetInt32(modelReader.GetOrdinal("ServiceFrequency"));
                                    int serviceTypeID = modelReader.GetInt32(modelReader.GetOrdinal("ServiceType_ID"));
                                    bool modelIsActive = modelReader.GetBoolean(modelReader.GetOrdinal("Model_IsActive"));

                                    using (SqlDataReader modelLocaleReader = modelLocaleCommand.ExecuteReader())
                                    {
                                        if (modelLocaleReader.HasRows)
                                        {
                                            // Locale data exists
                                            if (modelLocaleReader.Read())
                                            {
                                                result = new
                                                {
                                                    Tools_ID = modelID,
                                                    Model_Name = modelLocaleReader.GetString(modelLocaleReader.GetOrdinal("Model_Name")),
                                                    Model_Description = modelLocaleReader.GetString(modelLocaleReader.GetOrdinal("Model_Description")),
                                                    ServiceFrequency = serviceFrequency,
                                                    ServiceType_ID = serviceTypeID,
                                                    Model_IsActive = modelIsActive
                                                };
                                            }
                                        }
                                        else
                                        {
                                            // No locale data
                                            result = new
                                            {
                                                Tools_ID = modelID,
                                                Model_Name = "",
                                                Model_Description = modelDescription,
                                                ServiceFrequency = serviceFrequency,
                                                ServiceType_ID = serviceTypeID,
                                                Model_IsActive = modelIsActive
                                            };
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle or log the exception as needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(result, JsonRequestBehavior.AllowGet);
            return new JsonResult(result);
        }
        #endregion

        #region ::: Insert Model Locale /Mithun:::
        /// <summary>
        ///  Method to Insert Model Locale
        /// </summary>   
        public static IActionResult InsertModelLocale(InsertModelLocaleList InsertModelLocaleObj, string constring, int LogException)
        {

            try
            {
                JObject jObj = JObject.Parse(InsertModelLocaleObj.key);

                int modelID = Convert.ToInt32(jObj["ModelID"]);
                string name = Common.DecryptString(jObj["Name"].ToString());
                string description = Common.DecryptString(jObj["Description"].ToString());
                int languageID = Convert.ToInt32(InsertModelLocaleObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Check if the record exists
                    string checkQuery = @"
                SELECT COUNT(*)
                FROM GNM_ModelLocale
                WHERE Model_ID = @Model_ID";

                    using (SqlCommand checkCommand = new SqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@Model_ID", modelID);
                        int count = (int)checkCommand.ExecuteScalar();

                        if (count == 0)
                        {
                            // Insert new record
                            string insertQuery = @"
                        INSERT INTO GNM_ModelLocale (Model_ID, Model_Name, Model_Description, Language_ID)
                        VALUES (@Model_ID, @Model_Name, @Model_Description, @Language_ID)";

                            using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@Model_ID", modelID);
                                insertCommand.Parameters.AddWithValue("@Model_Name", name);
                                insertCommand.Parameters.AddWithValue("@Model_Description", description);
                                insertCommand.Parameters.AddWithValue("@Language_ID", languageID);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Update existing record
                            string updateQuery = @"
                        UPDATE GNM_ModelLocale
                        SET Model_Name = @Model_Name,
                            Model_Description = @Model_Description
                        WHERE Model_ID = @Model_ID";

                            using (SqlCommand updateCommand = new SqlCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@Model_ID", modelID);
                                updateCommand.Parameters.AddWithValue("@Model_Name", name);
                                updateCommand.Parameters.AddWithValue("@Model_Description", description);
                                updateCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(InsertModelLocaleObj.Company_ID),
                    //    Convert.ToInt32(InsertModelLocaleObj.Branch),
                    //    Convert.ToInt32(InsertModelLocaleObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                    //    modelID,
                    //    0,
                    //    0,
                    //    "Update",
                    //    false,
                    //    Convert.ToInt32(InsertModelLocaleObj.MenuID)
                    //);
                }
                return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Update Model Locale /Mithun:::
        /// <summary>
        /// To Update Model Locale
        /// </summary>
        public static IActionResult UpdateLocale(UpdateLocaleModelList UpdateLocaleObj, string constring, int LogException)
        {
            int modelLocaleID = 0;
            var result = default(dynamic); // Default empty object

            try
            {
                JObject jObj = JObject.Parse(UpdateLocaleObj.data);

                int modelID = Convert.ToInt32(jObj["Model_ID"]);
                string modelName = Common.DecryptString(jObj["Model_Name"].ToString());
                string modelDescription = Common.DecryptString(jObj["Model_Description"].ToString());
                modelLocaleID = Convert.ToInt32(jObj["ModelLocale_ID"]);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    if (modelLocaleID != 0)
                    {
                        // Update existing record
                        string updateQuery = @"
                    UPDATE GNM_ModelLocale
                    SET Model_Name = @Model_Name,
                        Model_Description = @Model_Description
                    WHERE ModelLocale_ID = @ModelLocale_ID";

                        using (SqlCommand updateCommand = new SqlCommand(updateQuery, connection))
                        {
                            updateCommand.Parameters.AddWithValue("@ModelLocale_ID", modelLocaleID);
                            updateCommand.Parameters.AddWithValue("@Model_Name", modelName);
                            updateCommand.Parameters.AddWithValue("@Model_Description", modelDescription);
                            updateCommand.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        // Insert new record
                        string insertQuery = @"
                    INSERT INTO GNM_ModelLocale (Model_ID, Model_Name, Model_Description, Language_ID)
                    OUTPUT Inserted.ModelLocale_ID
                    VALUES (@Model_ID, @Model_Name, @Model_Description, @Language_ID)";

                        using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection))
                        {
                            insertCommand.Parameters.AddWithValue("@Model_ID", modelID);
                            insertCommand.Parameters.AddWithValue("@Model_Name", modelName);
                            insertCommand.Parameters.AddWithValue("@Model_Description", modelDescription);
                            insertCommand.Parameters.AddWithValue("@Language_ID", Convert.ToInt32(UpdateLocaleObj.UserLanguageID));

                            modelLocaleID = (int)insertCommand.ExecuteScalar();
                        }
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(UpdateLocaleObj.Company_ID),
                    //    Convert.ToInt32(UpdateLocaleObj.Branch),
                    //    Convert.ToInt32(UpdateLocaleObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster", constring)),
                    //    modelID,
                    //    0,
                    //    0,
                    //    "Update",
                    //    false,
                    //    Convert.ToInt32(UpdateLocaleObj.MenuID)
                    //);
                }

                result = new
                {
                    ModelLocale_ID = modelLocaleID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return Json(result, JsonRequestBehavior.AllowGet);
            return new JsonResult(result);
        }
        #endregion

        #region ::: Delete Model /Mithun:::
        /// <summary>
        /// To Delete Model
        /// </summary>
        public static IActionResult Delete(DeleteModelList DeleteObj, string constring, int LogException)
        {
            string ModelMessage = string.Empty;
            int count = 0;
            int modelID = 0;

            try
            {
                JObject jObj = JObject.Parse(DeleteObj.key);
                count = jObj["rows"].Count();

                int BranchID = Convert.ToInt32(DeleteObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = DeleteObj.UserDetails.FirstOrDefault();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < count; i++)
                    {
                        modelID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                        // Remove entries from GNM_ModelLocale
                        string deleteLocaleQuery = @"
                    DELETE FROM GNM_ModelLocale
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand cmdDeleteLocale = new SqlCommand(deleteLocaleQuery, connection))
                        {
                            cmdDeleteLocale.Parameters.AddWithValue("@Model_ID", modelID);
                            cmdDeleteLocale.ExecuteNonQuery();
                        }

                        // Remove entries from GNM_MODELSERVICETYPEDET
                        string deleteServiceTypeQuery = @"
                    DELETE FROM GNM_MODELSERVICETYPEDET
                    WHERE MODEL_ID = @Model_ID";

                        using (SqlCommand cmdDeleteServiceType = new SqlCommand(deleteServiceTypeQuery, connection))
                        {
                            cmdDeleteServiceType.Parameters.AddWithValue("@Model_ID", modelID);
                            cmdDeleteServiceType.ExecuteNonQuery();
                        }

                        // Remove entry from GNM_Model
                        string deleteModelQuery = @"
                    DELETE FROM GNM_Model
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand cmdDeleteModel = new SqlCommand(deleteModelQuery, connection))
                        {
                            cmdDeleteModel.Parameters.AddWithValue("@Model_ID", modelID);
                            cmdDeleteModel.ExecuteNonQuery();
                        }

                        // Insert GPS details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(DeleteObj.Company_ID),
                        //    BranchID,
                        //    User.User_ID,
                        //    Common.GetObjectID("CoreModelMaster",constring),
                        //    modelID,
                        //    0,
                        //    0,
                        //    "Deleted Model with ID: " + modelID,
                        //    false,
                        //    Convert.ToInt32(DeleteObj.MenuID),
                        //    Convert.ToDateTime(DeleteObj.LoggedINDateTime)
                        //);
                    }
                }

                ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            catch (Exception ex)
            {
                // Log other exceptions or handle them as needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return ModelMessage;
            return new JsonResult(ModelMessage);
        }

        #endregion

        #region ::: Delete Mandatory Service Details /Mithun:::
        /// <summary>
        /// To Delete Mandatory Service Details
        /// </summary>
        public static IActionResult DeleteServiceChargeDetail(DeleteServiceChargeDetailList DeleteServiceChargeDetailObj, string constring, int LogException)
        {
            string ModelMessage = string.Empty;
            int count = 0;
            int ID = 0;

            try
            {
                JObject jObj = JObject.Parse(DeleteServiceChargeDetailObj.key);
                count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < count; i++)
                    {
                        ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                        // Check if dependency exists in FSMCore_SRT_JobCardHeader
                        string checkJobCardQuery = @"
                    SELECT TOP 1 * FROM FSMCore_SRT_JobCardHeader
                    WHERE ServiceType_ID = (SELECT ServiceType_ID FROM GNM_ModelServiceChargeDetail WHERE ModelServiceChargeDetail_ID = @ModelServiceChargeDetail_ID)
                    AND Model_ID = @Model_ID";

                        using (SqlCommand checkCmd = new SqlCommand(checkJobCardQuery, connection))
                        {
                            checkCmd.Parameters.AddWithValue("@ModelServiceChargeDetail_ID", ID);
                            checkCmd.Parameters.AddWithValue("@Model_ID", DeleteServiceChargeDetailObj.ModelID);

                            using (SqlDataReader reader = checkCmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteServiceChargeDetailObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    //return ModelMessage;
                                    return new JsonResult(ModelMessage);
                                }
                            }
                        }

                        // Delete from GNM_ModelServiceChargeDetail
                        string deleteQuery = @"
                    DELETE FROM GNM_ModelServiceChargeDetail
                    WHERE ModelServiceChargeDetail_ID = @ModelServiceChargeDetail_ID";

                        using (SqlCommand deleteCmd = new SqlCommand(deleteQuery, connection))
                        {
                            deleteCmd.Parameters.AddWithValue("@ModelServiceChargeDetail_ID", ID);
                            deleteCmd.ExecuteNonQuery();
                        }
                    }

                    // Insert GPS details after the delete operations
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(DeleteServiceChargeDetailObj.Company_ID),
                    //    Convert.ToInt32(DeleteServiceChargeDetailObj.Branch),
                    //    Convert.ToInt32(DeleteServiceChargeDetailObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                    //    DeleteServiceChargeDetailObj.ModelID,
                    //    0,
                    //    0,
                    //    "Delete",
                    //    false,
                    //    Convert.ToInt32(DeleteServiceChargeDetailObj.MenuID)
                    //);

                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteServiceChargeDetailObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteServiceChargeDetailObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return ModelMessage;
            return new JsonResult(ModelMessage);
        }

        #endregion

        #region ::: Delete Quick Checklist Details /Mithun:::
        /// <summary>
        /// To Delete Mandatory Service Details
        /// </summary>
        public static IActionResult DeleteQuickChecklist(DeleteQuickChecklistList DeleteQuickChecklistObj, string constring, int LogException)
        {
            string ModelMessage = string.Empty;
            int count = 0;
            int ID = 0;

            try
            {
                JObject jObj = JObject.Parse(DeleteQuickChecklistObj.key);
                count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < count; i++)
                    {
                        ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                        // Check if dependency exists in FSMCore_SRT_JobCardHeader
                        string checkJobCardQuery = @"
                    SELECT TOP 1 * FROM FSMCore_SRT_JobCardHeader
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand checkCmd = new SqlCommand(checkJobCardQuery, connection))
                        {
                            checkCmd.Parameters.AddWithValue("@Model_ID", DeleteQuickChecklistObj.ModelID);

                            using (SqlDataReader reader = checkCmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteQuickChecklistObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    //return ModelMessage;
                                    return new JsonResult(ModelMessage);
                                }
                            }
                        }

                        // Delete from GNM_ModelQuickListDetails
                        string deleteQuery = @"
                    DELETE FROM GNM_ModelQuickListDetails
                    WHERE ModelQuickChecklist_ID = @ModelQuickChecklist_ID";

                        using (SqlCommand deleteCmd = new SqlCommand(deleteQuery, connection))
                        {
                            deleteCmd.Parameters.AddWithValue("@ModelQuickChecklist_ID", ID);
                            deleteCmd.ExecuteNonQuery();
                        }
                    }

                    // Insert GPS details after the delete operations
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(DeleteQuickChecklistObj.Company_ID),
                    //    Convert.ToInt32(DeleteQuickChecklistObj.Branch),
                    //    Convert.ToInt32(DeleteQuickChecklistObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                    //    ModelID,
                    //    0,
                    //    0,
                    //    "Delete",
                    //    false,
                    //    Convert.ToInt32(DeleteQuickChecklistObj.MenuID)
                    //);

                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteQuickChecklistObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteQuickChecklistObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return ModelMessage;
            return new JsonResult(ModelMessage);
        }

        #endregion

        #region ::: Delete Red Carpet Checklist Details /Mithun:::
        /// <summary>
        /// To Delete Mandatory Service Details
        /// </summary>

        public static IActionResult DeleteRedCarpetChecklist(DeleteRedCarpetChecklistList DeleteRedCarpetChecklistObj, string constring, int LogException)
        {
            string ModelMessage = string.Empty;
            int count = 0;
            int ID = 0;

            try
            {
                JObject jObj = JObject.Parse(DeleteRedCarpetChecklistObj.key);
                count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < count; i++)
                    {
                        ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                        // Check if dependency exists in FSMCore_SRT_JobCardHeader
                        string checkJobCardQuery = @"
                    SELECT TOP 1 * FROM FSMCore_SRT_JobCardHeader
                    WHERE Model_ID = @Model_ID";

                        using (SqlCommand checkCmd = new SqlCommand(checkJobCardQuery, connection))
                        {
                            checkCmd.Parameters.AddWithValue("@Model_ID", DeleteRedCarpetChecklistObj.ModelID);

                            using (SqlDataReader reader = checkCmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteRedCarpetChecklistObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    //return ModelMessage;
                                    return new JsonResult(ModelMessage);
                                }
                            }
                        }

                        // Delete from GNM_ModelRedCarpetListDetails
                        string deleteQuery = @"
                    DELETE FROM GNM_ModelRedCarpetListDetails
                    WHERE ModelRedCarpetChecklist_ID = @ModelRedCarpetChecklist_ID";

                        using (SqlCommand deleteCmd = new SqlCommand(deleteQuery, connection))
                        {
                            deleteCmd.Parameters.AddWithValue("@ModelRedCarpetChecklist_ID", ID);
                            deleteCmd.ExecuteNonQuery();
                        }
                    }

                    // Insert GPS details after the delete operations
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(DeleteRedCarpetChecklistObj.Company_ID),
                    //    Convert.ToInt32(DeleteRedCarpetChecklistObj.Branch),
                    //    Convert.ToInt32(DeleteRedCarpetChecklistObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                    //    ModelID,
                    //    0,
                    //    0,
                    //    "Delete",
                    //    false,
                    //    Convert.ToInt32(DeleteRedCarpetChecklistObj.MenuID)
                    //);

                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteRedCarpetChecklistObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ModelMessage += CommonFunctionalities.GetGlobalResourceObject(DeleteRedCarpetChecklistObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return ModelMessage;
            return new JsonResult(ModelMessage);
        }


        #endregion

        #region ::: Check Duplicate Model Locale Name /Mithun:::
        /// <summary>
        /// To Check if Model name Already exists for the Brand and producttype
        /// </summary>
        public static IActionResult CheckModelNameLocale(CheckModelNameLocaleList CheckModelNameLocaleObj, string constring, int LogException)
        {
            int Count = 0;
            int Language_ID = 0;

            try
            {
                Language_ID = Convert.ToInt32(CheckModelNameLocaleObj.UserLanguageID);
                string ModelName = Common.DecryptString(CheckModelNameLocaleObj.ModelName);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = @"
                SELECT COUNT(*) 
                FROM GNM_ModelLocale ml
                INNER JOIN GNM_Model m ON ml.Model_ID = m.Model_ID
                WHERE m.Brand_ID = @BrandID
                  AND m.ProductType_ID = @ProductTypeID
                  AND ml.Model_Name = @ModelName
                  AND ml.Model_ID != @ModelID
                  AND ml.Language_ID = @LanguageID";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BrandID", CheckModelNameLocaleObj.BrandID);
                        command.Parameters.AddWithValue("@ProductTypeID", CheckModelNameLocaleObj.ProductTypeID);
                        command.Parameters.AddWithValue("@ModelName", ModelName);
                        command.Parameters.AddWithValue("@ModelID", CheckModelNameLocaleObj.ModelID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        Count = (int)command.ExecuteScalar();
                    }

                    // If a matching record is found, set Count to 1
                    if (Count > 0)
                    {
                        Count = 1;
                    }
                }
            }
            catch (SqlException ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region ::: Check Duplicate Model Name /Mithun:::
        /// <summary>
        /// To Check if Model name Already exists for the Brand and producttype
        /// </summary>
        public static IActionResult CheckModelName(CheckModelNameList CheckModelNameObj, string constring, int LogException)
        {
            int Count = 0;

            try
            {
                string ModelName = Common.DecryptString(CheckModelNameObj.ModelName);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = @"
                SELECT COUNT(*) 
                FROM GNM_Model
                WHERE Brand_ID = @BrandID
                  AND ProductType_ID = @ProductTypeID
                  AND Model_Name = @ModelName
                  AND Model_ID != @ModelID";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BrandID", CheckModelNameObj.BrandID);
                        command.Parameters.AddWithValue("@ProductTypeID", CheckModelNameObj.ProductTypeID);
                        command.Parameters.AddWithValue("@ModelName", ModelName);
                        command.Parameters.AddWithValue("@ModelID", CheckModelNameObj.ModelID);

                        Count = (int)command.ExecuteScalar();
                    }

                    // If a matching record is found, set Count to 1
                    if (Count > 0)
                    {
                        Count = 1;
                    }
                }
            }
            catch (SqlException ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Count;
            return new JsonResult(Count);
        }


        #endregion

        #region ::: Select Model Price List details /Mithun:::
        /// <summary>
        /// To select Model Price List details
        /// </summary>
        public static IActionResult SelectPriceList(SelectPriceListList SelectPriceListObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectPriceListObj.Company_ID);
                List<ModelPriceList> modelPriceList = new List<ModelPriceList>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Query to fetch model price details
                    string query = @"
                SELECT ModelPriceDetails_ID, Model_ID, ListPrice, EffectiveFrom
                FROM GNM_ModelPriceDetails
                WHERE Model_ID = @ModelID AND Company_ID = @CompanyID";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ModelID", SelectPriceListObj.ModelID);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ModelPriceList priceListItem = new ModelPriceList
                                {
                                    ModelPriceDetails_ID = reader.GetInt32(reader.GetOrdinal("ModelPriceDetails_ID")),
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    ListPrice = reader.GetDecimal(reader.GetOrdinal("ListPrice")),
                                    EffectiveFrom = reader.GetDateTime(reader.GetOrdinal("EffectiveFrom"))
                                };
                                modelPriceList.Add(priceListItem);
                            }
                        }
                    }
                }

                // Handling if LanguageID is not the general language ID
                if (SelectPriceListObj.LanguageID != Convert.ToInt32(SelectPriceListObj.GeneralLanguageID))
                {
                    modelPriceList = modelPriceList.Select(a => new ModelPriceList
                    {
                        ModelPriceDetails_ID = a.ModelPriceDetails_ID,
                        Model_ID = a.Model_ID,
                        ListPrice = a.ListPrice,
                        EffectiveFrom = a.EffectiveFrom
                    }).ToList();
                }

                // Sorting based on sidx and sord
                var orderedPriceList = modelPriceList.AsQueryable()
                                                     .OrderByField(sidx, sord);

                // Store session if language matches
                //if (LanguageID == Convert.ToInt32(Session["GeneralLanguageID"]))
                //{
                //    Session["IQModelPriceList"] = orderedPriceList.AsEnumerable();
                //}

                Count = orderedPriceList.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                else
                {
                    page = 1;
                }

                // JSON Data
                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in orderedPriceList
                            select new
                            {
                                ID = a.ModelPriceDetails_ID,
                                edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectPriceListObj.UserCulture.ToString(), "edit", null).ToString() + " href='#' id='" + a.ModelPriceDetails_ID + "' key='" + a.ModelPriceDetails_ID + "' class='EditModelPriceList font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.ModelPriceDetails_ID + "' defaultchecked=''  id='chk" + a.ModelPriceDetails_ID + "' class='ModelPriceListDelete'/>",
                                ListPrice = a.ListPrice.ToString("0.00"),
                                EffectiveFrom = a.EffectiveFrom.ToString("dd-MMM-yyyy"),
                            }).ToList(),
                    records = Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: MakeLastRowEditable /Mithun:::
        /// <summary>
        /// MakeLastRowEditable
        /// </summary>
        /// <param name="MakeLastRowEditableObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult MakeLastRowEditable(MakeLastRowEditableModelList MakeLastRowEditableObj, string constring, int LogException)
        {
            GNM_ModelPriceDetails Model = null;
            int Company_ID = Convert.ToInt32(MakeLastRowEditableObj.Company_ID);
            int lastModelPriceDetailsID = 0;

            try
            {

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // SQL Query to get all rows for the given ModelID and Company_ID
                    string query = @"
                SELECT ModelPriceDetails_ID, Model_ID, ListPrice, EffectiveFrom 
                FROM GNM_ModelPriceDetails
                WHERE Model_ID = @ModelID AND Company_ID = @CompanyID
                ORDER BY ModelPriceDetails_ID ASC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ModelID", MakeLastRowEditableObj.ModelID);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            // Loop through all rows to get the last one
                            while (reader.Read())
                            {
                                Model = new GNM_ModelPriceDetails
                                {
                                    ModelPriceDetails_ID = reader.GetInt32(reader.GetOrdinal("ModelPriceDetails_ID")),
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    ListPrice = reader.GetDecimal(reader.GetOrdinal("ListPrice")),
                                    EffectiveFrom = reader.GetDateTime(reader.GetOrdinal("EffectiveFrom"))
                                };
                            }
                        }
                    }

                    // If we retrieved a model, return its ID
                    if (Model != null)
                    {
                        lastModelPriceDetailsID = Model.ModelPriceDetails_ID;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return lastModelPriceDetailsID;
            return new JsonResult(lastModelPriceDetailsID);
        }

        #endregion

        #region ::: Insert Model PriceList details /Mithun:::
        /// <summary>
        /// To Save Model PriceList Details
        /// </summary>
        public static IActionResult InsertModelPriceListDetail(InsertModelPriceListDetailList InsertModelPriceListDetailObj, string constring, int LogException)
        {
            try
            {

                int Company_ID = Convert.ToInt32(InsertModelPriceListDetailObj.Company_ID);
                JObject jObj = JObject.Parse(InsertModelPriceListDetailObj.PriceListdata);
                int Count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        GNM_ModelPriceDetails MRow = jObj["rows"].ElementAt(i).ToObject<GNM_ModelPriceDetails>();

                        if (MRow.ModelPriceDetails_ID != 0)
                        {
                            // Update the existing record
                            string updateQuery = @"
                        UPDATE GNM_ModelPriceDetails
                        SET ListPrice = @ListPrice, EffectiveFrom = @EffectiveFrom, Company_ID = @CompanyID
                        WHERE ModelPriceDetails_ID = @ModelPriceDetails_ID";

                            using (SqlCommand command = new SqlCommand(updateQuery, connection))
                            {
                                command.Parameters.AddWithValue("@ListPrice", Convert.ToDecimal(MRow.ListPrice));
                                command.Parameters.AddWithValue("@EffectiveFrom", Convert.ToDateTime(MRow.EffectiveFrom));
                                command.Parameters.AddWithValue("@CompanyID", Company_ID);
                                command.Parameters.AddWithValue("@ModelPriceDetails_ID", MRow.ModelPriceDetails_ID);

                                command.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert a new record
                            string insertQuery = @"
                        INSERT INTO GNM_ModelPriceDetails (Model_ID, ListPrice, EffectiveFrom, Company_ID)
                        VALUES (@ModelID, @ListPrice, @EffectiveFrom, @CompanyID)";

                            using (SqlCommand command = new SqlCommand(insertQuery, connection))
                            {
                                command.Parameters.AddWithValue("@ModelID", Convert.ToInt32(MRow.Model_ID));
                                command.Parameters.AddWithValue("@ListPrice", Convert.ToDecimal(MRow.ListPrice));
                                command.Parameters.AddWithValue("@EffectiveFrom", Convert.ToDateTime(MRow.EffectiveFrom));
                                command.Parameters.AddWithValue("@CompanyID", Company_ID);

                                command.ExecuteNonQuery();
                            }
                        }

                        // Insert GPS details (you can customize this part as needed)
                        //    gbl.InsertGPSDetails(Convert.ToInt32(InsertModelPriceListDetailObj.Company_ID), Convert.ToInt32(InsertModelPriceListDetailObj.Branch), Convert.ToInt32(InsertModelPriceListDetailObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)), Convert.ToInt32(MRow.Model_ID), 0, 0, "Update", false, Convert.ToInt32(InsertModelPriceListDetailObj.MenuID));
                    }
                    return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: Insert Model Quick Checklist Detail /Mithun:::
        /// <summary>
        /// To Save Model PriceList Details
        /// </summary>
        public static IActionResult InsertModelQuickChecklistDetail(InsertModelQuickChecklistDetailList InsertModelQuickChecklistDetailObj, string constring, int LogException)
        {
            try
            {
                int Company_ID = Convert.ToInt32(InsertModelQuickChecklistDetailObj.Company_ID);
                JObject jObj = JObject.Parse(InsertModelQuickChecklistDetailObj.QuickChecklistData);
                int Count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        var jRow = jObj["rows"].ElementAt(i);
                        int modelQuickChecklistID = Convert.ToInt32(jRow["ModelQuickChecklist_ID"]);
                        int modelID = Convert.ToInt32(jRow["Model_ID"]);
                        string description = Common.DecryptString((string)jRow["Description"]);
                        bool isMandatory = Convert.ToBoolean(jRow["IsMandatory"]);
                        bool isPhotoRequired = Convert.ToBoolean(jRow["IsPhotoRequired"]);
                        bool isDefaultCheck = Convert.ToBoolean(jRow["IsDefaultCheck"]);
                        string minvalue = Common.DecryptString((string)jRow["Minvalue"]);
                        string maxvalue = Common.DecryptString((string)jRow["Maxvalue"]);

                        if (modelQuickChecklistID != 0)
                        {
                            // Update existing record
                            string updateQuery = @"
                        UPDATE GNM_ModelQuickListDetails
                        SET Description = @Description,
                            IsMandatory = @IsMandatory,
                            IsPhotoRequired = @IsPhotoRequired,
                            IsDefaultCheck = @IsDefaultCheck,
                            Minvalue = @Minvalue,
                            Maxvalue = @Maxvalue
                        WHERE ModelQuickChecklist_ID = @ModelQuickChecklist_ID";

                            using (SqlCommand updateCommand = new SqlCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@Description", description);
                                updateCommand.Parameters.AddWithValue("@IsMandatory", isMandatory);
                                updateCommand.Parameters.AddWithValue("@IsPhotoRequired", isPhotoRequired);
                                updateCommand.Parameters.AddWithValue("@IsDefaultCheck", isDefaultCheck);
                                updateCommand.Parameters.AddWithValue("@Minvalue", minvalue);
                                updateCommand.Parameters.AddWithValue("@Maxvalue", maxvalue);
                                updateCommand.Parameters.AddWithValue("@ModelQuickChecklist_ID", modelQuickChecklistID);

                                updateCommand.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert new record
                            string insertQuery = @"
                        INSERT INTO GNM_ModelQuickListDetails (Model_ID, Description, IsMandatory, IsPhotoRequired, IsDefaultCheck, Minvalue, Maxvalue, Company_ID)
                        VALUES (@Model_ID, @Description, @IsMandatory, @IsPhotoRequired, @IsDefaultCheck, @Minvalue, @Maxvalue, @Company_ID)";

                            using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@Model_ID", modelID);
                                insertCommand.Parameters.AddWithValue("@Description", description);
                                insertCommand.Parameters.AddWithValue("@IsMandatory", isMandatory);
                                insertCommand.Parameters.AddWithValue("@IsPhotoRequired", isPhotoRequired);
                                insertCommand.Parameters.AddWithValue("@IsDefaultCheck", isDefaultCheck);
                                insertCommand.Parameters.AddWithValue("@Minvalue", minvalue);
                                insertCommand.Parameters.AddWithValue("@Maxvalue", maxvalue);
                                insertCommand.Parameters.AddWithValue("@Company_ID", Company_ID);

                                insertCommand.ExecuteNonQuery();
                            }
                        }

                        // Call GPS details insertion method
                        //gbl.InsertGPSDetails(
                        //    Company_ID,
                        //    Convert.ToInt32(InsertModelQuickChecklistDetailObj.Branch),
                        //    Convert.ToInt32(InsertModelQuickChecklistDetailObj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                        //    modelID,
                        //    0,
                        //    0,
                        //    "Update",
                        //    false,
                        //    Convert.ToInt32(InsertModelQuickChecklistDetailObj.MenuID)
                        //);

                    }
                    return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Insert Model RedCarpet Checklist Detail /Mithun:::
        /// <summary>
        /// To Save Model PriceList Details
        /// </summary>
        public static IActionResult InsertModelRedCarpetChecklistDetail(InsertModelRedCarpetChecklistDetailList InsertModelRedCarpetChecklistDetailObj, string constring, int LogException)
        {
            try
            {
                int Company_ID = Convert.ToInt32(InsertModelRedCarpetChecklistDetailObj.Company_ID);
                JObject jObj = JObject.Parse(InsertModelRedCarpetChecklistDetailObj.RedCarpetChecklistData);
                int count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < count; i++)
                    {
                        var jRow = jObj["rows"].ElementAt(i);
                        int modelRedCarpetChecklistID = Convert.ToInt32(jRow["ModelRedCarpetChecklist_ID"]);
                        int modelID = Convert.ToInt32(jRow["Model_ID"]);
                        string description = Common.DecryptString((string)jRow["Description"]);
                        bool isMandatory = Convert.ToBoolean(jRow["IsMandatory"]);
                        bool isPhotoRequired = Convert.ToBoolean(jRow["IsPhotoRequired"]);

                        if (modelRedCarpetChecklistID != 0)
                        {
                            // Update existing record
                            string updateQuery = @"
                        UPDATE GNM_ModelRedCarpetListDetails
                        SET Description = @Description,
                            IsMandatory = @IsMandatory,
                            IsPhotoRequired = @IsPhotoRequired
                        WHERE ModelRedCarpetChecklist_ID = @ModelRedCarpetChecklist_ID";

                            using (SqlCommand updateCommand = new SqlCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@Description", description);
                                updateCommand.Parameters.AddWithValue("@IsMandatory", isMandatory);
                                updateCommand.Parameters.AddWithValue("@IsPhotoRequired", isPhotoRequired);
                                updateCommand.Parameters.AddWithValue("@ModelRedCarpetChecklist_ID", modelRedCarpetChecklistID);

                                updateCommand.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert new record
                            string insertQuery = @"
                        INSERT INTO GNM_ModelRedCarpetListDetails (Model_ID, Description, IsMandatory, IsPhotoRequired, Company_ID)
                        VALUES (@Model_ID, @Description, @IsMandatory, @IsPhotoRequired, @Company_ID)";

                            using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@Model_ID", modelID);
                                insertCommand.Parameters.AddWithValue("@Description", description);
                                insertCommand.Parameters.AddWithValue("@IsMandatory", isMandatory);
                                insertCommand.Parameters.AddWithValue("@IsPhotoRequired", isPhotoRequired);
                                insertCommand.Parameters.AddWithValue("@Company_ID", Company_ID);

                                insertCommand.ExecuteNonQuery();
                            }
                        }

                        // Call GPS details insertion method
                        //gbl.InsertGPSDetails(
                        //    Company_ID,
                        //    Convert.ToInt32(InsertModelRedCarpetChecklistDetailObj.Branch),
                        //    Convert.ToInt32(InsertModelRedCarpetChecklistDetailObj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                        //    modelID,
                        //    0,
                        //    0,
                        //    "Update",
                        //    false,
                        //    Convert.ToInt32(InsertModelRedCarpetChecklistDetailObj.MenuID)
                        //);
                    }
                    return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        #endregion

        #region ::: getPreviousDate /Mithun:::
        /// <summary>
        /// To select the All parts 
        /// </summary>    
        public static IActionResult getPreviousDate(getPreviousDateModelList getPreviousDateObj, string constring, int LogException)
        {
            string CurDate = "";
            string Query = "";
            int Company_ID = Convert.ToInt32(getPreviousDateObj.Company_ID);

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    if (string.IsNullOrEmpty(getPreviousDateObj.Currenteditmode))
                    {
                        // Query to get the details when Currenteditmode is empty
                        Query = @"
                    SELECT TOP 1 ModelPriceDetails_ID, EffectiveFrom
                    FROM GNM_ModelPriceDetails
                    WHERE Model_ID = @ModelID AND Company_ID = @Company_ID
                    ORDER BY ModelPriceDetails_ID DESC";

                        using (SqlCommand command = new SqlCommand(Query, connection))
                        {
                            command.Parameters.AddWithValue("@ModelID", getPreviousDateObj.modelID);
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    reader.Read();
                                    DateTime effectiveFrom = Convert.ToDateTime(reader["EffectiveFrom"]);
                                    CurDate = effectiveFrom.ToString("dd-MMM-yyyy");
                                }
                                else
                                {
                                    CurDate = "";
                                }
                            }
                        }
                    }
                    else
                    {
                        // Query for the else part
                        Query = @"
                    SELECT TOP 1 *
                    FROM GNM_ModelPriceDetails
                    WHERE Model_ID = @ModelID AND Company_ID = @Company_ID
                    ORDER BY ModelPriceDetails_ID DESC";

                        using (SqlCommand command = new SqlCommand(Query, connection))
                        {
                            command.Parameters.AddWithValue("@ModelID", getPreviousDateObj.modelID);
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    reader.Read();
                                    DateTime effectiveFrom = Convert.ToDateTime(reader["EffectiveFrom"]);
                                    CurDate = effectiveFrom.ToString("dd-MMM-yyyy");
                                }
                                else
                                {
                                    CurDate = "";
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return CurDate;
            return new JsonResult(CurDate);
        }

        #endregion

        #region ::: Select Model Supplier Price Detail Grid /Mithun:::
        /// <summary>
        ///  Select Model Supplier Price Detail Grid
        /// </summary>
        public static IActionResult SelectModelSupplierDetailGrid(SelectModelSupplierDetailGridList SelectModelSupplierDetailGridObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            List<SupplierPriceDetail> supplierPriceDetails = new List<SupplierPriceDetail>();

            using (SqlConnection con = new SqlConnection(constring))
            {
                con.Open();

                // Query to get ModelSupplierPriceDetails data
                string queryModelSupplierPriceDetails = @"SELECT ModelSupplierPriceDetails_ID, Model_ID, Supplier_ID, SupplierPrice, Currency_ID, EffectiveFrom 
                                                  FROM GNM_ModelSupplierPriceDetails 
                                                  WHERE Model_ID = @ModelID AND Company_ID = @CompanyID";
                using (SqlCommand cmd = new SqlCommand(queryModelSupplierPriceDetails, con))
                {
                    cmd.Parameters.AddWithValue("@ModelID", SelectModelSupplierDetailGridObj.ModelID);
                    cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(SelectModelSupplierDetailGridObj.Company_ID));

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            SupplierPriceDetail detail = new SupplierPriceDetail
                            {
                                Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                ModelSupplierPriceDetails_ID = reader.GetInt32(reader.GetOrdinal("ModelSupplierPriceDetails_ID")),
                                Supplier_ID = reader.GetInt32(reader.GetOrdinal("Supplier_ID")),
                                SupplierPrice = reader.GetDecimal(reader.GetOrdinal("SupplierPrice")),
                                Currency_ID = reader.GetInt32(reader.GetOrdinal("Currency_ID")),
                                EffectiveFrom = reader.GetDateTime(reader.GetOrdinal("EffectiveFrom"))
                            };
                            supplierPriceDetails.Add(detail);
                        }
                    }
                }

                // Query to get Supplier (GNM_Party or GNM_PartyLocale based on language) and Currency information
                string supplierQuery, currencyQuery;
                int languageID = Convert.ToInt32(SelectModelSupplierDetailGridObj.Language_ID);
                int generalLanguageID = Convert.ToInt32(SelectModelSupplierDetailGridObj.GeneralLanguageID);

                if (languageID == generalLanguageID)
                {
                    // Default language query
                    supplierQuery = "SELECT Party_ID, Party_Name FROM GNM_Party WHERE Party_ID = @SupplierID";
                    currencyQuery = "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @CurrencyID";
                }
                else
                {
                    // Locale specific query
                    supplierQuery = "SELECT Party_ID, Party_Name FROM GNM_PartyLocale WHERE Party_ID = @SupplierID AND Language_ID = @LanguageID";
                    currencyQuery = "SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID = @CurrencyID AND Language_ID = @LanguageID";
                }

                foreach (var detail in supplierPriceDetails)
                {
                    // Get Supplier Name
                    using (SqlCommand cmdSupplier = new SqlCommand(supplierQuery, con))
                    {
                        cmdSupplier.Parameters.AddWithValue("@SupplierID", detail.Supplier_ID);
                        if (languageID != generalLanguageID)
                            cmdSupplier.Parameters.AddWithValue("@LanguageID", languageID);

                        detail.Name = Convert.ToString(cmdSupplier.ExecuteScalar());
                    }

                    // Get Currency Name
                    using (SqlCommand cmdCurrency = new SqlCommand(currencyQuery, con))
                    {
                        cmdCurrency.Parameters.AddWithValue("@CurrencyID", detail.Currency_ID);
                        if (languageID != generalLanguageID)
                            cmdCurrency.Parameters.AddWithValue("@LanguageID", languageID);

                        detail.Currency = Convert.ToString(cmdCurrency.ExecuteScalar());
                    }
                }
            }

            // Sorting and Pagination Logic
            if (sord == "asc")
            {
                supplierPriceDetails = supplierPriceDetails.OrderBy(x => x.GetType().GetProperty(sidx).GetValue(x)).ToList();
            }
            else
            {
                supplierPriceDetails = supplierPriceDetails.OrderByDescending(x => x.GetType().GetProperty(sidx).GetValue(x)).ToList();
            }

            int totalRecords = supplierPriceDetails.Count;
            int totalPages = (int)Math.Ceiling((float)totalRecords / rows);
            supplierPriceDetails = supplierPriceDetails.Skip((page - 1) * rows).Take(rows).ToList();

            var jsonresult = new
            {
                total = totalPages,
                page = page,
                records = totalRecords,
                rows = supplierPriceDetails.Select(a => new
                {
                    ID = a.ModelSupplierPriceDetails_ID,
                    edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectModelSupplierDetailGridObj.UserCulture.ToString(), "edit").ToString() + " href='#' id='" + a.ModelSupplierPriceDetails_ID + "' key='" + a.ModelSupplierPriceDetails_ID + "' class='EditSupplierPriceDetails font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                    delete = "<input type='checkbox' key='" + a.ModelSupplierPriceDetails_ID + "' defaultchecked=''  id='chk" + a.ModelSupplierPriceDetails_ID + "' class='SupplierPriceDetailsDelete'/>",
                    Currency = a.Currency,
                    Supplier_ID = a.Supplier_ID,
                    Name = a.Name,
                    SupplierPrice = a.SupplierPrice.ToString("0.00"),
                    EffectiveFrom = a.EffectiveFrom.HasValue ? a.EffectiveFrom.Value.ToString("dd-MMM-yyyy") : string.Empty

                }).ToList(),
            };

            //return Json(jsonresult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonresult);
        }

        #endregion

        #region ::: Select Model Sales Price Detail Grid /Mithun:::
        /// <summary>
        /// Select Model Sales Price Detail Grid 
        /// </summary>
        public static IActionResult SelectModelSalesPriceDetailGrid(SelectModelSalesPriceDetailGridList SelectModelSalesPriceDetailGridObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonresult = default(dynamic);
            int Language_ID = Convert.ToInt32(SelectModelSalesPriceDetailGridObj.Language_ID);
            int Company_ID = Convert.ToInt32(SelectModelSalesPriceDetailGridObj.Company_ID);
            int Total = 0;
            List<SalesPriceDetail> salesPriceDetailsList = new List<SalesPriceDetail>();

            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("SELECT Model_ID, ModelSalesPriceDetails_ID, ListPrice, DealerNetPrice, MRP, EffectiveFrom FROM GNM_ModelSalesPriceDetails WHERE Model_ID = @ModelID", conn))
                {
                    cmd.Parameters.AddWithValue("@ModelID", SelectModelSalesPriceDetailGridObj.ModelID);
                    SqlDataReader reader = cmd.ExecuteReader();

                    while (reader.Read())
                    {
                        salesPriceDetailsList.Add(new SalesPriceDetail
                        {
                            Model_ID = Convert.ToInt32(reader["Model_ID"]),
                            ModelSalesPriceDetails_ID = Convert.ToInt32(reader["ModelSalesPriceDetails_ID"]),
                            ListPrice = (decimal)(reader["ListPrice"] != DBNull.Value ? Convert.ToDecimal(reader["ListPrice"]) : (decimal?)null),
                            DealerNetPrice = (decimal)(reader["DealerNetPrice"] != DBNull.Value ? Convert.ToDecimal(reader["DealerNetPrice"]) : (decimal?)null),
                            MRP = (decimal)(reader["MRP"] != DBNull.Value ? Convert.ToDecimal(reader["MRP"]) : (decimal?)null),
                            EffectiveFrom = reader["EffectiveFrom"] != DBNull.Value ? Convert.ToDateTime(reader["EffectiveFrom"]) : (DateTime?)null
                        });
                    }
                }
            }

            IEnumerable<SalesPriceDetail> IESalesDetailArray = salesPriceDetailsList.AsEnumerable();
            IQueryable<SalesPriceDetail> IQSalesDetail = IESalesDetailArray.AsQueryable();

            // Sorting logic
            IQSalesDetail = IQSalesDetail.OrderByField<SalesPriceDetail>(sidx, sord);

            // Pagination logic
            int Count = IQSalesDetail.Count();
            Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
            if (Count < (rows * page) && Count != 0)
            {
                page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
            }
            else
            {
                page = 1;
            }

            jsonresult = new
            {
                total = Total,
                page = page,
                rows = (from a in IQSalesDetail.AsEnumerable()
                        select new
                        {
                            ID = a.ModelSalesPriceDetails_ID,
                            edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectModelSalesPriceDetailGridObj.UserCulture.ToString(), "view").ToString() + " href='#' id='" + a.ModelSalesPriceDetails_ID + "'key='" + a.ModelSalesPriceDetails_ID + "' class='EditSalesPriceDetails font-icon-class' editmode='false'/><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = "<input type='checkbox' key='" + a.ModelSalesPriceDetails_ID + "' defaultchecked=''  id='chk" + a.ModelSalesPriceDetails_ID + "' class='SalesPriceDetailsDelete'/>",
                            Model_ID = a.Model_ID,
                            ListPrice = a.ListPrice.ToString("0.00"),
                            DealerNetPrice = a.DealerNetPrice.ToString("0.00"),
                            MRP = a.MRP.ToString("0.00"),
                            EffectiveFrom = a.EffectiveFrom.HasValue ? a.EffectiveFrom.Value.ToString("dd-MMM-yyyy") : ""
                        }).ToList(),
                records = Count
            };

            //return Json(jsonresult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonresult);
        }
        #endregion

        #region ::: Select Model Quick Checklist Detail Grid /Mithun:::
        /// <summary>
        /// Select Model Quick Checklist Detail Grid 
        /// </summary>

        public static IActionResult SelectModelQuickChecklistDetail(SelectModelQuickChecklistDetailList SelectModelQuickChecklistDetailObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonresult = new object();
            int Company_ID = Convert.ToInt32(SelectModelQuickChecklistDetailObj.Company_ID);
            int Total = 0;
            int GeneralLanguageID = Convert.ToInt32(SelectModelQuickChecklistDetailObj.GeneralLanguageID);
            int userLanguageID = Convert.ToInt32(SelectModelQuickChecklistDetailObj.UserLanguageID);

            // Fetching global and user-specific resource strings
            string YesE = CommonFunctionalities.GetResourceString(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "yes").ToString();
            string NoE = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "No").ToString();
            string OKE = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "OK").ToString();
            string NOKE = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "NOK").ToString();
            string NAE = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "NotApplicable").ToString();

            string YesL = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.UserCulture.ToString(), "Yes").ToString();
            string NoL = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.UserCulture.ToString(), "No").ToString();
            string OKL = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "OK").ToString();
            string NOKL = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.GeneralCulture.ToString(), "NOK").ToString();
            string NAL = CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.UserCulture.ToString(), "NotApplicable").ToString();

            List<QuickChecklistDetail> quickChecklistDetails = new List<QuickChecklistDetail>();

            using (SqlConnection con = new SqlConnection(constring))
            {
                con.Open();

                // Query for ModelQuickChecklistDetails
                SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_ModelQuickListDetails WHERE Model_ID = @ModelID", con);
                cmd.Parameters.AddWithValue("@ModelID", SelectModelQuickChecklistDetailObj.ModelID);
                SqlDataReader reader = cmd.ExecuteReader();

                while (reader.Read())
                {
                    quickChecklistDetails.Add(new QuickChecklistDetail
                    {
                        Model_ID = reader["Model_ID"] != DBNull.Value ? Convert.ToInt32(reader["Model_ID"]) : 0,
                        ModelQuickListDetails_ID = reader["ModelQuickChecklist_ID"] != DBNull.Value ? Convert.ToInt32(reader["ModelQuickChecklist_ID"]) : 0,
                        Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : string.Empty,
                        IsMandatory = reader["IsMandatory"] != DBNull.Value && Convert.ToBoolean(reader["IsMandatory"]) ? YesE : NoE,
                        IsPhotoRequired = reader["IsPhotoRequired"] != DBNull.Value && Convert.ToBoolean(reader["IsPhotoRequired"]) ? YesE : NoE,
                        DefaultCheck = reader["IsDefaultCheck"] != DBNull.Value && Convert.ToInt32(reader["IsDefaultCheck"]) == 1 ? OKE :
                                      reader["IsDefaultCheck"] != DBNull.Value && Convert.ToInt32(reader["IsDefaultCheck"]) == 2 ? NOKE :
                                      reader["IsDefaultCheck"] != DBNull.Value && Convert.ToInt32(reader["IsDefaultCheck"]) == 3 ? NAE : "",
                        Minvalue = reader["Minvalue"] != DBNull.Value ? reader["Minvalue"].ToString() : string.Empty,
                        Maxvalue = reader["Maxvalue"] != DBNull.Value ? reader["Maxvalue"].ToString() : string.Empty
                    });
                }
                reader.Close();

                // If the language is not the general language, fetch localized descriptions
                if (SelectModelQuickChecklistDetailObj.LanguageID != GeneralLanguageID)
                {
                    SqlCommand cmdLocale = new SqlCommand("SELECT * FROM GNM_ModelQuickListDetailsLocale WHERE Language_ID = @LanguageID", con);
                    cmdLocale.Parameters.AddWithValue("@LanguageID", SelectModelQuickChecklistDetailObj.LanguageID);
                    SqlDataReader localeReader = cmdLocale.ExecuteReader();

                    List<GNM_ModelQuickListDetailsLocale> locales = new List<GNM_ModelQuickListDetailsLocale>();
                    while (localeReader.Read())
                    {
                        locales.Add(new GNM_ModelQuickListDetailsLocale
                        {
                            ModelQuickChecklist_ID = Convert.ToInt32(localeReader["ModelQuickChecklist_ID"]),
                            Description = localeReader["Description"].ToString(),
                            Language_ID = Convert.ToInt32(localeReader["Language_ID"])
                        });
                    }

                    localeReader.Close();

                    // Update descriptions with localized ones
                    quickChecklistDetails = (from a in quickChecklistDetails
                                             join b in locales
                                             on a.ModelQuickListDetails_ID equals b.ModelQuickChecklist_ID
                                             select new QuickChecklistDetail
                                             {
                                                 Model_ID = a.Model_ID,
                                                 ModelQuickListDetails_ID = a.ModelQuickListDetails_ID,
                                                 Description = b.Description,
                                                 IsMandatory = (a.IsMandatory == YesE) ? YesL : NoL,
                                                 IsPhotoRequired = (a.IsPhotoRequired == YesE) ? YesL : NoL,
                                                 DefaultCheck = (a.DefaultCheck == OKE) ? OKL : (a.DefaultCheck == NOKE) ? NOKL : (a.DefaultCheck == NAE) ? NAL : "",
                                                 Minvalue = a.Minvalue,
                                                 Maxvalue = a.Maxvalue
                                             }).ToList();
                }
            }

            // Convert to IQueryable
            IQueryable<QuickChecklistDetail> IQQuickCheckListDetail = quickChecklistDetails.AsQueryable();

            // Apply search filters if needed

            //if (Request.Params["_search"] == "true")
            //{
            //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
            //    if (filters.rules.Count() > 0)
            //    {
            //        IQQuickCheckListDetail = IQQuickCheckListDetail.FilterSearch<QuickChecklistDetail>(filters);
            //    }
            //}

            // Apply sorting
            IQQuickCheckListDetail = IQQuickCheckListDetail.OrderByField<QuickChecklistDetail>(sidx, sord);

            // Pagination logic
            int Count = IQQuickCheckListDetail.Count();
            Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
            if (Count < (rows * page) && Count != 0) { page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1); } else { page = 1; }

            // Prepare JSON result
            jsonresult = new
            {
                total = Total,
                page = page,
                rows = (from a in IQQuickCheckListDetail.Skip((page - 1) * rows).Take(rows).ToList()
                        select new
                        {
                            ModelQuickListDetails_ID = a.ModelQuickListDetails_ID,
                            edit = "<a title='" + CommonFunctionalities.GetGlobalResourceObject(SelectModelQuickChecklistDetailObj.UserCulture.ToString(), "edit").ToString() + "' href='#' style='font-size: 18px;' id='" + a.ModelQuickListDetails_ID + "' key='" + a.ModelQuickListDetails_ID + "' class='EditQuickChecklistDetails' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = "<input type='checkbox' key='" + a.ModelQuickListDetails_ID + "' defaultchecked='' id='chk" + a.ModelQuickListDetails_ID + "' class='QuickChecklistDelete'/>",
                            Model_ID = a.Model_ID,
                            Description = a.Description,
                            IsMandatory = a.IsMandatory,
                            IsPhotoRequired = a.IsPhotoRequired,
                            DefaultCheck = a.DefaultCheck,
                            Locale = "<a title='Localize' href='#' style='font-size: 13px;' width='20' height='20' src='" + AppPath + "/Content/local.png' class='QuickChecklistLocale' key='" + a.ModelQuickListDetails_ID + "'><i class='fa fa-globe'></i></a>",
                            a.Minvalue,
                            a.Maxvalue
                        }).ToList(),
                records = Count
            };

            //return Json(jsonresult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonresult);
        }

        #endregion

        #region ::: SelectParticularQuickChecklistLocale /Mithun:::
        /// <summary>
        /// To Select Particular Product type
        /// </summary>
        public static IActionResult SelectParticularQuickChecklistLocale(SelectParticularQuickChecklistLocaleList SelectParticularQuickChecklistLocaleObj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                // Set up database connection
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    int Language_ID = Convert.ToInt32(SelectParticularQuickChecklistLocaleObj.UserLanguageID);

                    // Prepare SQL for QuickCheckList
                    string sqlQuickCheckList = "SELECT ModelQuickChecklist_ID, Description, IsMandatory, IsPhotoRequired FROM GNM_ModelQuickListDetails WHERE ModelQuickChecklist_ID = @QuickChecklist_ID";
                    SqlCommand cmdQuickCheckList = new SqlCommand(sqlQuickCheckList, conn);
                    cmdQuickCheckList.Parameters.AddWithValue("@QuickChecklist_ID", SelectParticularQuickChecklistLocaleObj.QuickChecklist_ID);

                    // Execute the command for QuickCheckList
                    GNM_ModelQuickListDetails QuickCheckList = null;
                    using (SqlDataReader reader = cmdQuickCheckList.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            QuickCheckList = new GNM_ModelQuickListDetails
                            {
                                ModelQuickChecklist_ID = reader.GetInt32(reader.GetOrdinal("ModelQuickChecklist_ID")),
                                Description = reader.GetString(reader.GetOrdinal("Description")),
                                IsMandatory = reader.GetBoolean(reader.GetOrdinal("IsMandatory")),
                                IsPhotoRequired = reader.GetBoolean(reader.GetOrdinal("IsPhotoRequired"))
                            };
                        }
                    }

                    // Prepare SQL for QuickCheckLocaleList
                    string sqlQuickCheckLocaleList = "SELECT ModelQuickChecklistLocale_ID, Description FROM GNM_ModelQuickListDetailsLocale WHERE ModelQuickChecklist_ID = @QuickChecklist_ID AND Language_ID = @Language_ID";
                    SqlCommand cmdQuickCheckLocaleList = new SqlCommand(sqlQuickCheckLocaleList, conn);
                    cmdQuickCheckLocaleList.Parameters.AddWithValue("@QuickChecklist_ID", SelectParticularQuickChecklistLocaleObj.QuickChecklist_ID);
                    cmdQuickCheckLocaleList.Parameters.AddWithValue("@Language_ID", Language_ID);

                    // Execute the command for QuickCheckLocaleList
                    GNM_ModelQuickListDetailsLocale QuickCheckLocaleList = null;
                    using (SqlDataReader reader = cmdQuickCheckLocaleList.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            QuickCheckLocaleList = new GNM_ModelQuickListDetailsLocale
                            {
                                ModelQuickChecklistLocale_ID = reader.GetInt32(reader.GetOrdinal("ModelQuickChecklistLocale_ID")),
                                Description = reader.GetString(reader.GetOrdinal("Description"))
                            };
                        }
                    }

                    // Check if locale list exists and create the result accordingly
                    if (QuickCheckLocaleList != null)
                    {
                        x = new
                        {
                            QuickCheckList.ModelQuickChecklist_ID,
                            QuickCheckList.Description,
                            QuickCheckList.IsMandatory,
                            QuickCheckList.IsPhotoRequired,
                            DescriptionLocale = QuickCheckLocaleList.Description,
                            QuickChecklistLocaleID = QuickCheckLocaleList.ModelQuickChecklistLocale_ID
                        };
                    }
                    else
                    {
                        x = new
                        {
                            QuickCheckList.ModelQuickChecklist_ID,
                            QuickCheckList.Description,
                            QuickCheckList.IsMandatory,
                            QuickCheckList.IsPhotoRequired,
                            DescriptionLocale = "",
                            QuickChecklistLocaleID = ""
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }
        #endregion

        #region ::: InsertQuickChecklistLocale /Mithun:::
        /// <summary>
        ///  Method to Insert Model Locale
        /// </summary>   
        public static IActionResult InsertQuickChecklistLocale(InsertQuickChecklistLocaleList InsertQuickChecklistLocaleObj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                // Parse the request parameters using JObject and JTokenReader
                JObject jObj = JObject.Parse(InsertQuickChecklistLocaleObj.key);

                int ModelQuickChecklist_ID = Convert.ToInt32(jObj["ModelQuickChecklist_ID"].ToString());
                int ModelQuickChecklistLocale_ID = Convert.ToInt32(jObj["ModelQuickChecklistLocale_ID"].ToString());
                string Description = jObj["Description"].ToString();

                // Decrypt the description
                string decryptedDescription = Common.DecryptString(Description);

                // Set up the connection string
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // If ModelQuickChecklistLocale_ID == 0, insert a new record
                    if (ModelQuickChecklistLocale_ID == 0)
                    {
                        string insertQuery = @"INSERT INTO GNM_ModelQuickListDetailsLocale 
                                        (ModelQuickChecklist_ID, Description, Language_ID) 
                                        VALUES (@ModelQuickChecklist_ID, @Description, @Language_ID)";
                        using (SqlCommand cmd = new SqlCommand(insertQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ModelQuickChecklist_ID", ModelQuickChecklist_ID);
                            cmd.Parameters.AddWithValue("@Description", decryptedDescription);
                            cmd.Parameters.AddWithValue("@Language_ID", Convert.ToInt32(InsertQuickChecklistLocaleObj.UserLanguageID));

                            // Execute the insert command
                            cmd.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        // Update the existing record
                        string updateQuery = @"UPDATE GNM_ModelQuickListDetailsLocale 
                                        SET Description = @Description 
                                        WHERE ModelQuickChecklist_ID = @ModelQuickChecklist_ID";
                        using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ModelQuickChecklist_ID", ModelQuickChecklist_ID);
                            cmd.Parameters.AddWithValue("@Description", decryptedDescription);

                            // Execute the update command
                            cmd.ExecuteNonQuery();
                        }
                    }
                    return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log the exception details
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        #endregion

        #region ::: Select Model RedCarpet Checklist Detail Grid /Mithun:::
        /// <summary>
        /// Select Model RedCarpet Checklist Detail Grid 
        /// </summary>
        public static IActionResult SelectModelRedCarpetChecklistDetail(SelectModelRedCarpetChecklistDetailList SelectModelRedCarpetChecklistDetailObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonresult = default(dynamic);
            List<RedCarpetChecklistDetail> modelRedCarpetChecklistDetailsList = new List<RedCarpetChecklistDetail>();
            List<RedCarpetChecklistDetail> modelRedCarpetChecklistDetailsListLocale = new List<RedCarpetChecklistDetail>();
            int GeneralLanguageID = Convert.ToInt32(SelectModelRedCarpetChecklistDetailObj.GeneralLanguageID);
            int userLanguageID = Convert.ToInt32(SelectModelRedCarpetChecklistDetailObj.UserLanguageID);
            string YesE = CommonFunctionalities.GetGlobalResourceObject(SelectModelRedCarpetChecklistDetailObj.GeneralCulture.ToString(), "Yes").ToString();
            string NoE = CommonFunctionalities.GetGlobalResourceObject(SelectModelRedCarpetChecklistDetailObj.GeneralCulture.ToString(), "No").ToString();
            string YesL = CommonFunctionalities.GetGlobalResourceObject(SelectModelRedCarpetChecklistDetailObj.UserCulture.ToString(), "Yes").ToString();
            string NoL = CommonFunctionalities.GetGlobalResourceObject(SelectModelRedCarpetChecklistDetailObj.UserCulture.ToString(), "No").ToString();
            int total = 0;
            int count = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch data from GNM_ModelRedCarpetListDetails
                    using (SqlCommand cmd = new SqlCommand("SELECT Model_ID, ModelRedCarpetChecklist_ID, Description, IsMandatory, IsPhotoRequired FROM GNM_ModelRedCarpetListDetails WHERE Model_ID = @ModelID", conn))
                    {
                        cmd.Parameters.AddWithValue("@ModelID", SelectModelRedCarpetChecklistDetailObj.ModelID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                modelRedCarpetChecklistDetailsList.Add(new RedCarpetChecklistDetail
                                {
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    ModelRedCarpetChecklist_ID = reader.GetInt32(reader.GetOrdinal("ModelRedCarpetChecklist_ID")),
                                    Description = reader.GetString(reader.GetOrdinal("Description")),
                                    IsMandatory = reader.GetBoolean(reader.GetOrdinal("IsMandatory")) ? YesE : NoE,
                                    IsPhotoRequired = reader.GetBoolean(reader.GetOrdinal("IsPhotoRequired")) ? YesE : NoE
                                });

                            }
                        }
                    }

                    // Fetch data from GNM_ModelRedCarpetListDetailsLocale
                    using (SqlCommand cmd = new SqlCommand("SELECT ModelRedCarpetChecklist_ID, Description, Language_ID FROM GNM_ModelRedCarpetListDetailsLocale", conn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                modelRedCarpetChecklistDetailsListLocale.Add(new RedCarpetChecklistDetail
                                {
                                    ModelRedCarpetChecklist_ID = reader.GetInt32(reader.GetOrdinal("ModelRedCarpetChecklist_ID")),
                                    Description = reader.GetString(reader.GetOrdinal("Description")),
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID"))
                                });

                            }
                        }
                    }
                }

                // Perform in-memory filtering based on the LanguageID
                IEnumerable<RedCarpetChecklistDetail> IERedCarpetCheckListDetailArray;
                if (SelectModelRedCarpetChecklistDetailObj.LanguageID == GeneralLanguageID)
                {
                    IERedCarpetCheckListDetailArray = from a in modelRedCarpetChecklistDetailsList
                                                      select new RedCarpetChecklistDetail
                                                      {
                                                          Model_ID = a.Model_ID,
                                                          ModelRedCarpetChecklist_ID = a.ModelRedCarpetChecklist_ID,
                                                          Description = a.Description,
                                                          IsMandatory = a.IsMandatory,
                                                          IsPhotoRequired = a.IsPhotoRequired
                                                      };
                }
                else
                {
                    IERedCarpetCheckListDetailArray = from a in modelRedCarpetChecklistDetailsList
                                                      join b in modelRedCarpetChecklistDetailsListLocale
                                                      on a.ModelRedCarpetChecklist_ID equals b.ModelRedCarpetChecklist_ID
                                                      where b.Language_ID == SelectModelRedCarpetChecklistDetailObj.LanguageID
                                                      select new RedCarpetChecklistDetail
                                                      {
                                                          Model_ID = a.Model_ID,
                                                          ModelRedCarpetChecklist_ID = a.ModelRedCarpetChecklist_ID,
                                                          Description = b.Description,
                                                          IsMandatory = a.IsMandatory == "Yes" ? YesL : NoL,
                                                          IsPhotoRequired = a.IsPhotoRequired == "Yes" ? YesL : NoL
                                                      };
                }

                // Apply IQueryable
                IQueryable<RedCarpetChecklistDetail> IQRedCarpetCheckListDetail = IERedCarpetCheckListDetailArray.AsQueryable();

                // Apply search filters

                //if (Request.Params["_search"] == "true")
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                //    if (filters.rules.Count() > 0)
                //    {
                //        IQRedCarpetCheckListDetail = IQRedCarpetCheckListDetail.FilterSearch<RedCarpetChecklistDetail>(filters);
                //    }
                //}

                // Apply sorting
                IQRedCarpetCheckListDetail = IQRedCarpetCheckListDetail.OrderByField<RedCarpetChecklistDetail>(sidx, sord);

                // Calculate paging
                count = IQRedCarpetCheckListDetail.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0) { page = (count / rows) + ((count % rows) == 0 ? 0 : 1); } else { page = 1; }

                // Prepare JSON result
                jsonresult = new
                {
                    total = total,
                    page = page,
                    rows = (from a in IQRedCarpetCheckListDetail.Skip((page - 1) * rows).Take(rows).ToList()
                            select new
                            {
                                ModelRedCarpetChecklist_ID = a.ModelRedCarpetChecklist_ID,
                                edit = "<a title='" + CommonFunctionalities.GetGlobalResourceObject(SelectModelRedCarpetChecklistDetailObj.UserCulture.ToString(), "edit").ToString() + "' href='#' style='font-size: 18px;' id='" + a.ModelRedCarpetChecklist_ID + "' key='" + a.ModelRedCarpetChecklist_ID + "' class='EditRedCarpetChecklistDetails' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.ModelRedCarpetChecklist_ID + "' defaultchecked='' id='chk" + a.ModelRedCarpetChecklist_ID + "' class='RedCarpetChecklistDelete'/>",
                                Model_ID = a.Model_ID,
                                Description = a.Description,
                                IsMandatory = a.IsMandatory,
                                IsPhotoRequired = a.IsPhotoRequired,
                                Locale = "<a title='Localize' href='#' style='font-size: 13px;' width='20' height='20' src='" + AppPath + "/Content/local.png' class='RedCarpetChecklistLocale' key='" + a.ModelRedCarpetChecklist_ID + "'><i class='fa fa-globe'></i></a>",
                            }).ToList(),
                    records = count
                };

                //return Json(jsonresult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonresult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log the exception
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: InsertRedCarpetChecklistLocale /Mithun:::
        /// <summary>
        ///  Method to Insert Model Locale
        /// </summary>  
        public static IActionResult InsertRedCarpetChecklistLocale(InsertRedCarpetChecklistLocaleList InsertRedCarpetChecklistLocaleObj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                JObject jObj = JObject.Parse(InsertRedCarpetChecklistLocaleObj.key);

                int ModelQuickChecklist_ID = Convert.ToInt32(jObj["ModelRedCarpetChecklist_ID"]);
                int ModelQuickChecklistLocale_ID = Convert.ToInt32(jObj["ModelRedCarpetChecklistLocale_ID"]);
                string Description = Common.DecryptString(jObj["Description"].ToString());
                int LanguageID = Convert.ToInt32(InsertRedCarpetChecklistLocaleObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    if (ModelQuickChecklistLocale_ID == 0)
                    {
                        string insertQuery = @"INSERT INTO GNM_ModelRedCarpetListDetailsLocale 
                                      (ModelRedCarpetChecklist_ID, Description, Language_ID)
                                      VALUES (@ModelRedCarpetChecklist_ID, @Description, @Language_ID)";

                        using (SqlCommand cmd = new SqlCommand(insertQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ModelRedCarpetChecklist_ID", ModelQuickChecklist_ID);
                            cmd.Parameters.AddWithValue("@Description", Description);
                            cmd.Parameters.AddWithValue("@Language_ID", LanguageID);
                            cmd.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        string updateQuery = @"UPDATE GNM_ModelRedCarpetListDetailsLocale
                                      SET Description = @Description
                                      WHERE ModelRedCarpetChecklist_ID = @ModelRedCarpetChecklist_ID";

                        using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ModelRedCarpetChecklist_ID", ModelQuickChecklist_ID);
                            cmd.Parameters.AddWithValue("@Description", Description);
                            cmd.ExecuteNonQuery();
                        }
                    }
                    return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectParticularRedCarpetChecklistLocale /Mithun:::
        /// <summary>
        /// To Select Particular Product type
        /// </summary>
        public static IActionResult SelectParticularRedCarpetChecklistLocale(SelectParticularRedCarpetChecklistLocaleList SelectParticularRedCarpetChecklistLocaleObj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularRedCarpetChecklistLocaleObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query for GNM_ModelRedCarpetListDetails
                    string selectChecklistQuery = @"SELECT ModelRedCarpetChecklist_ID, ModelRedCarpetChecklist_Description, ModelRedCarpetChecklist_IsMandatory, ModelRedCarpetChecklist_IsPhotoRequired 
                                            FROM GNM_ModelRedCarpetListDetails
                                            WHERE ModelRedCarpetChecklist_ID = @ModelRedCarpetChecklist_ID";

                    GNM_ModelRedCarpetListDetails quickCheckList = null;
                    using (SqlCommand cmd = new SqlCommand(selectChecklistQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@ModelRedCarpetChecklist_ID", SelectParticularRedCarpetChecklistLocaleObj.RedCarpetChecklist_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                quickCheckList = new GNM_ModelRedCarpetListDetails
                                {
                                    ModelRedCarpetChecklist_ID = reader.GetInt32(0),
                                    Description = reader.GetString(1), // Assuming this corresponds to ModelRedCarpetChecklist_Description
                                    IsMandatory = reader.GetBoolean(2), // Assuming this corresponds to ModelRedCarpetChecklist_IsMandatory
                                    IsPhotoRequired = reader.GetBoolean(3) // Assuming this corresponds to ModelRedCarpetChecklist_IsPhotoRequired
                                };
                            }
                        }
                    }

                    // Query for GNM_ModelRedCarpetListDetailsLocale
                    string selectChecklistLocaleQuery = @"SELECT ModelRedCarpetChecklistLocale_ID, ModelRedCarpetChecklistLocale_Description
                                                  FROM GNM_ModelRedCarpetListDetailsLocale
                                                  WHERE ModelRedCarpetChecklist_ID = @ModelRedCarpetChecklist_ID
                                                  AND Language_ID = @Language_ID";

                    GNM_ModelRedCarpetListDetailsLocale quickCheckLocaleList = null;
                    using (SqlCommand cmd = new SqlCommand(selectChecklistLocaleQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@ModelRedCarpetChecklist_ID", SelectParticularRedCarpetChecklistLocaleObj.RedCarpetChecklist_ID);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                quickCheckLocaleList = new GNM_ModelRedCarpetListDetailsLocale
                                {
                                    ModelRedCarpetChecklistLocale_ID = reader.GetInt32(0),
                                    Description = reader.GetString(1) // Assuming this corresponds to ModelRedCarpetChecklistLocale_Description
                                };
                            }
                        }
                    }

                    // Constructing the response
                    if (quickCheckLocaleList != null)
                    {
                        x = new
                        {
                            quickCheckList.ModelRedCarpetChecklist_ID,
                            quickCheckList.Description,
                            quickCheckList.IsMandatory,
                            quickCheckList.IsPhotoRequired,
                            DescriptionLocale = quickCheckLocaleList.Description,
                            QuickChecklistLocaleID = quickCheckLocaleList.ModelRedCarpetChecklistLocale_ID
                        };
                    }
                    else
                    {
                        x = new
                        {
                            quickCheckList.ModelRedCarpetChecklist_ID,
                            quickCheckList.Description,
                            quickCheckList.IsMandatory,
                            quickCheckList.IsPhotoRequired,
                            DescriptionLocale = "",
                            QuickChecklistLocaleID = ""
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }

        #endregion

        #region ::: CheckDuplicateDescription /Mithun:::
        /// <summary>
        /// CheckDuplicateDescription
        /// </summary>
        public static IActionResult CheckDuplicateDescription(CheckDuplicateDescriptionList CheckDuplicateDescriptionObj, string constring, int LogException)
        {
            int Count = 0;
            string Val = CheckDuplicateDescriptionObj.Val.Trim();
            int CompanyId = Convert.ToInt32(CheckDuplicateDescriptionObj.Company_ID);

            try
            {
                Val = Uri.UnescapeDataString(Val);

                if (!string.IsNullOrEmpty(Val))
                {
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = @"SELECT COUNT(1) 
                                 FROM GNM_ModelQuickListDetails 
                                 WHERE LOWER(ModelQuickList_Description) = @Description";

                        using (SqlCommand cmd = new SqlCommand(query, conn))
                        {
                            cmd.Parameters.AddWithValue("@Description", Val.ToLower());

                            int result = Convert.ToInt32(cmd.ExecuteScalar());

                            if (result > 0)
                            {
                                Count = 1;
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Message = "Error Occured" }) { StatusCode = 500 };
            }

            //return Count;
            return new JsonResult(Count);
        }
        #endregion

        #region ::: CheckDuplicateDescriptionRed /Mithun:::
        /// <summary>
        /// CheckDuplicateDescriptionRed
        /// </summary>
        public static IActionResult CheckDuplicateDescriptionRed(CheckDuplicateDescriptionRedList CheckDuplicateDescriptionRedObj, string constring, int LogException)
        {
            int Count = 0;
            string Val = CheckDuplicateDescriptionRedObj.Val.Trim();
            int CompanyId = Convert.ToInt32(CheckDuplicateDescriptionRedObj.Company_ID);

            try
            {
                Val = Uri.UnescapeDataString(Val);

                if (!string.IsNullOrEmpty(Val))
                {
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = @"SELECT COUNT(1) 
                                 FROM GNM_ModelRedCarpetListDetails 
                                 WHERE LOWER(ModelRedCarpetChecklist_Description) = @Description";

                        using (SqlCommand cmd = new SqlCommand(query, conn))
                        {
                            cmd.Parameters.AddWithValue("@Description", Val.ToLower());

                            int result = Convert.ToInt32(cmd.ExecuteScalar());

                            if (result > 0)
                            {
                                Count = 1;
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return Count;
            return new JsonResult(Count);
        }


        #endregion

        #region ::: Insert Model Supplier PriceList details /Mithun:::
        /// <summary>
        /// To Save Model Supplier PriceList Details
        /// </summary>
        public static IActionResult InsertModelSupplierPriceListDetail(InsertModelSupplierPriceListDetailList InsertModelSupplierPriceListDetailObj, string constring, int LogException)
        {
            try
            {
                int Company_ID = Convert.ToInt32(InsertModelSupplierPriceListDetailObj.Company_ID);
                JObject jObjSupplier = JObject.Parse(InsertModelSupplierPriceListDetailObj.SupplierPriceListdata);
                int Count = jObjSupplier["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < Count; i++)
                        {
                            var SupplierRow = jObjSupplier["rows"].ElementAt(i).ToObject<GNM_ModelSupplierPriceDetails>();

                            string query;
                            SqlCommand cmd = new SqlCommand();
                            cmd.Connection = conn;
                            cmd.Transaction = transaction;

                            if (SupplierRow.ModelSupplierPriceDetails_ID != 0)
                            {
                                // Update existing record
                                query = @"UPDATE GNM_ModelSupplierPriceDetails 
                                  SET Supplier_ID = @Supplier_ID,
                                      Currency_ID = @Currency_ID,
                                      SupplierPrice = @SupplierPrice,
                                      EffectiveFrom = @EffectiveFrom,
                                      Company_ID = @Company_ID
                                  WHERE ModelSupplierPriceDetails_ID = @ModelSupplierPriceDetails_ID";

                                cmd.CommandText = query;
                                cmd.Parameters.AddWithValue("@ModelSupplierPriceDetails_ID", SupplierRow.ModelSupplierPriceDetails_ID);
                            }
                            else
                            {
                                // Insert new record
                                query = @"INSERT INTO GNM_ModelSupplierPriceDetails 
                                  (Model_ID, Supplier_ID, Currency_ID, SupplierPrice, EffectiveFrom, Company_ID) 
                                  VALUES (@Model_ID, @Supplier_ID, @Currency_ID, @SupplierPrice, @EffectiveFrom, @Company_ID)";

                                cmd.CommandText = query;
                                cmd.Parameters.AddWithValue("@Model_ID", SupplierRow.Model_ID);
                            }

                            cmd.Parameters.AddWithValue("@Supplier_ID", SupplierRow.Supplier_ID);
                            cmd.Parameters.AddWithValue("@Currency_ID", SupplierRow.Currency_ID);
                            cmd.Parameters.AddWithValue("@SupplierPrice", SupplierRow.SupplierPrice);
                            cmd.Parameters.AddWithValue("@EffectiveFrom", SupplierRow.EffectiveFrom);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                            cmd.ExecuteNonQuery();

                            // Insert GPS details
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(InsertModelSupplierPriceListDetailObj.Company_ID),
                            //    Convert.ToInt32(InsertModelSupplierPriceListDetailObj.Branch),
                            //    Convert.ToInt32(InsertModelSupplierPriceListDetailObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                            //    Convert.ToInt32(SupplierRow.Model_ID),
                            //    0, 0, "Update", false,
                            //    Convert.ToInt32(InsertModelSupplierPriceListDetailObj.MenuID)
                            //);
                        }
                        transaction.Commit();
                        return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Insert Model Sales PriceList details /Mithun:::
        /// <summary>
        /// To Save Model Supplier PriceList Details
        /// </summary>
        public static IActionResult InsertModelSalesPriceListDetail(InsertModelSalesPriceListDetailList InsertModelSalesPriceListDetailObj, string constring, int LogException)
        {
            try
            {
                int Company_ID = Convert.ToInt32(InsertModelSalesPriceListDetailObj.Company_ID);
                JObject jObjSales = JObject.Parse(InsertModelSalesPriceListDetailObj.SalesPriceListdata);
                int Count = jObjSales["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < Count; i++)
                        {
                            var SupplierRow = jObjSales["rows"].ElementAt(i).ToObject<GNM_ModelSalesPriceDetails>();

                            string query;
                            SqlCommand cmd = new SqlCommand();
                            cmd.Connection = conn;
                            cmd.Transaction = transaction;

                            if (SupplierRow.ModelSalesPriceDetails_ID != 0)
                            {
                                // Update existing record
                                query = @"UPDATE GNM_ModelSalesPriceDetails 
                                  SET Model_ID = @Model_ID,
                                      ListPrice = @ListPrice,
                                      DealerNetPrice = @DealerNetPrice,
                                      MRP = @MRP,
                                      EffectiveFrom = @EffectiveFrom
                                  WHERE ModelSalesPriceDetails_ID = @ModelSalesPriceDetails_ID";

                                cmd.CommandText = query;
                                cmd.Parameters.AddWithValue("@ModelSalesPriceDetails_ID", SupplierRow.ModelSalesPriceDetails_ID);
                            }
                            else
                            {
                                // Insert new record
                                query = @"INSERT INTO GNM_ModelSalesPriceDetails 
                                  (Model_ID, ListPrice, DealerNetPrice, MRP, EffectiveFrom) 
                                  VALUES (@Model_ID, @ListPrice, @DealerNetPrice, @MRP, @EffectiveFrom)";

                                cmd.CommandText = query;
                            }

                            cmd.Parameters.AddWithValue("@Model_ID", SupplierRow.Model_ID);
                            cmd.Parameters.AddWithValue("@ListPrice", SupplierRow.ListPrice);
                            cmd.Parameters.AddWithValue("@DealerNetPrice", SupplierRow.DealerNetPrice);
                            cmd.Parameters.AddWithValue("@MRP", SupplierRow.MRP);
                            cmd.Parameters.AddWithValue("@EffectiveFrom", SupplierRow.EffectiveFrom);

                            cmd.ExecuteNonQuery();

                            // Insert GPS details
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(InsertModelSalesPriceListDetailObj.Company_ID),
                            //    Convert.ToInt32(InsertModelSalesPriceListDetailObj.Branch),
                            //    Convert.ToInt32(InsertModelSalesPriceListDetailObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreModelMaster",constring)),
                            //    Convert.ToInt32(SupplierRow.Model_ID),
                            //    0, 0, "Update", false,
                            //    Convert.ToInt32(InsertModelSalesPriceListDetailObj.MenuID)
                            //);
                        }

                        transaction.Commit();
                        return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                        return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }
        #endregion

        #region ::: Delete Model SupplierPrice Detail Mithun:::
        /// <summary>
        ///to Delete Model SupplierPrice Details record
        /// </summary>        
        public static IActionResult DeleteModelSupplierPriceDetailsRows(DeleteModelSupplierPriceDetailsRowsList DeleteModelSupplierPriceDetailsRowsObj, string constring, int LogException)
        {
            string errorMsg = string.Empty;

            try
            {
                JObject jObj = JObject.Parse(DeleteModelSupplierPriceDetailsRowsObj.key);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < rowcount; i++)
                        {
                            int id = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                            string query = "DELETE FROM GNM_ModelSupplierPriceDetails WHERE ModelSupplierPriceDetails_ID = @id";

                            using (SqlCommand cmd = new SqlCommand(query, conn, transaction))
                            {
                                cmd.Parameters.AddWithValue("@id", id);
                                cmd.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                        errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteModelSupplierPriceDetailsRowsObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (SqlException sqlEx)
                    {
                        transaction.Rollback();

                        if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteModelSupplierPriceDetailsRowsObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            // Handle other SQL exceptions if necessary
                            errorMsg = "An error occurred while deleting records.";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception if needed  
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                errorMsg = "An error occurred while processing your request.";
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }



        #endregion

        #region ::: Delete Model SalesPrice Detail /Mithun:::
        /// <summary>
        ///to Delete Model SupplierPrice Details record
        /// </summary>    
        public static IActionResult DeleteModelSalesPriceDetailsRows(DeleteModelSalesPriceDetailsRowsList DeleteModelSalesPriceDetailsRowsObj, string constring, int LogException)
        {
            string errorMsg = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(DeleteModelSalesPriceDetailsRowsObj.key);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < rowcount; i++)
                        {
                            int id = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());

                            string query = "DELETE FROM GNM_ModelSalesPriceDetails WHERE ModelSalesPriceDetails_ID = @id";

                            using (SqlCommand cmd = new SqlCommand(query, conn, transaction))
                            {
                                cmd.Parameters.AddWithValue("@id", id);
                                cmd.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                        errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteModelSalesPriceDetailsRowsObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (SqlException sqlEx)
                    {
                        transaction.Rollback();

                        if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteModelSalesPriceDetailsRowsObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            // Handle other SQL exceptions if necessary
                            errorMsg = "An error occurred while deleting records.";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                errorMsg = "An error occurred while processing your request.";
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: getSupplierPricePreviousDate /Mithun:::
        /// <summary>
        /// To select the All parts 
        /// </summary>      
        public static IActionResult GetSupplierPricePreviousDate(GetSupplierPricePreviousDateList GetSupplierPricePreviousDateObj, string constring, int LogException)
        {
            string curDate = string.Empty;
            string query = string.Empty;

            if (GetSupplierPricePreviousDateObj.currentEditMode == string.Empty)
            {
                query = @"
            SELECT TOP 1 EffectiveFrom
            FROM GNM_ModelSupplierPriceDetails
            WHERE Model_ID = @ModelID AND Company_ID = @CompanyID
            ORDER BY ModelSupplierPriceDetails_ID DESC";
            }
            else
            {
                query = @"
            SELECT TOP 1 EffectiveFrom
            FROM (
                SELECT TOP 2 EffectiveFrom
                FROM GNM_ModelSupplierPriceDetails
                WHERE Model_ID = @ModelID AND Company_ID = @CompanyID
                ORDER BY ModelSupplierPriceDetails_ID DESC
            ) AS SubQuery
            ORDER BY ModelSupplierPriceDetails_ID ASC";
            }

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@ModelID", GetSupplierPricePreviousDateObj.modelID);
                        cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(GetSupplierPricePreviousDateObj.Company_ID));

                        object result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            curDate = Convert.ToDateTime(result).ToString("dd-MMM-yyyy");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                curDate = string.Empty; // Handle the error case
            }

            //return curDate;
            return new JsonResult(curDate);
        }

        #endregion

        #region ::: Select Party /Mithun:::
        /// <summary>
        /// To Select Party when enter Party and press tab
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult ChangePartyName(ChangePartyNameList ChangePartyNameObj, string constring, int LogException)
        {
            try
            {
                var jsonResponse = default(dynamic);
                var data = string.Empty;
                int LangID = Convert.ToInt32(ChangePartyNameObj.LanguageID);
                int Company_ID = Convert.ToInt32(ChangePartyNameObj.Company_ID);
                int id = Convert.ToInt32(ChangePartyNameObj.ModelID);
                string Key = Common.DecryptString(ChangePartyNameObj.Key);

                GNM_PartyLocale partyLocale = null;
                GNM_Party partyname = null;

                // Retrieve party data using ADO.NET
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to fetch party details
                    string queryParty = @"SELECT * FROM GNM_Party 
                                  WHERE Party_Name = @Key 
                                  AND Party_IsActive = 1 
                                  AND Company_ID = @Company_ID 
                                  AND PartyType = 4";

                    using (SqlCommand cmd = new SqlCommand(queryParty, conn))
                    {
                        cmd.Parameters.AddWithValue("@Key", Key);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                partyname = new GNM_Party
                                {
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                    Currency_ID = reader.GetInt32(reader.GetOrdinal("Currency_ID")),
                                    // Add other fields as necessary
                                };
                            }
                        }
                    }

                    // If party is found, fetch the localized name (GNM_PartyLocale)
                    if (partyname != null)
                    {
                        string queryPartyLocale = @"SELECT * FROM GNM_PartyLocale 
                                            WHERE Language_ID = @LangID 
                                            AND Party_ID = @Party_ID";

                        using (SqlCommand cmd = new SqlCommand(queryPartyLocale, conn))
                        {
                            cmd.Parameters.AddWithValue("@LangID", LangID);
                            cmd.Parameters.AddWithValue("@Party_ID", partyname.Party_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    partyLocale = new GNM_PartyLocale
                                    {
                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                    };
                                }
                            }
                        }
                    }

                    // Check if the party is already linked to the model
                    if (partyname != null)
                    {
                        string mode = ChangePartyNameObj.mode;
                        bool IsExists = false;

                        if (mode == "add")
                        {
                            string queryAdd = @"SELECT COUNT(*) FROM GNM_ModelSupplierPriceDetails 
                                        WHERE Model_ID = @Model_ID AND Supplier_ID = @Supplier_ID";
                            using (SqlCommand cmd = new SqlCommand(queryAdd, conn))
                            {
                                cmd.Parameters.AddWithValue("@Model_ID", id);
                                cmd.Parameters.AddWithValue("@Supplier_ID", partyname.Party_ID);

                                IsExists = (int)cmd.ExecuteScalar() > 0;
                            }
                        }
                        else if (mode == "edit")
                        {
                            int PrmID = Convert.ToInt32(ChangePartyNameObj.primID);
                            string queryEdit = @"SELECT COUNT(*) FROM GNM_ModelSupplierPriceDetails 
                                         WHERE Model_ID = @Model_ID 
                                         AND Supplier_ID = @Supplier_ID 
                                         AND ModelSupplierPriceDetails_ID != @PrmID";
                            using (SqlCommand cmd = new SqlCommand(queryEdit, conn))
                            {
                                cmd.Parameters.AddWithValue("@Model_ID", id);
                                cmd.Parameters.AddWithValue("@Supplier_ID", partyname.Party_ID);
                                cmd.Parameters.AddWithValue("@PrmID", PrmID);

                                IsExists = (int)cmd.ExecuteScalar() > 0;
                            }
                        }

                        // Create the response object
                        jsonResponse = new
                        {
                            ID = partyname.Party_ID,
                            Name = partyname.Party_Name,
                            IsExists = IsExists,
                            CurrencyID = partyname.Currency_ID
                        };
                    }
                    else
                    {
                        // No party found, return empty response
                        jsonResponse = new
                        {
                            ID = "",
                            Name = "",
                            IsExists = false
                        };
                    }
                }

                //return Json(jsonResponse, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResponse);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }



        #endregion

        #region ::: Select Field Search for Party /Mithun:::
        /// <summary>
        /// To Select Field Search for Party
        /// </summary>  
        public static IActionResult SelectFieldSearchParty(SelectFieldSearchPartyList SelectFieldSearchPartyObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                string Value = Common.DecryptString(SelectFieldSearchPartyObj.Value);
                int Company_ID = Convert.ToInt32(SelectFieldSearchPartyObj.Company_ID);
                int LangID = Convert.ToInt32(SelectFieldSearchPartyObj.LanguageID);
                int userLanguageID = Convert.ToInt32(SelectFieldSearchPartyObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectFieldSearchPartyObj.GeneralLanguageID);

                IQueryable<PartyFieldSearchObject> flSrch = Enumerable.Empty<PartyFieldSearchObject>().AsQueryable(); // Initialize as IQueryable
                int Count = 0;
                int Total = 0;
                List<GNM_Party> partyList = new List<GNM_Party>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    // Handle filter search

                    //if (Request.Params["_search"] == "true")
                    //{
                    //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                    //    if (filters.rules.Count > 0)
                    //    {
                    //        Value = Common.DecryptString(filters.rules.ElementAt(0).data);
                    //    }
                    //    else
                    //    {
                    //        Value = string.Empty;
                    //    }
                    //}

                    // Base query
                    string query = @"SELECT Party_ID, Party_Name 
                             FROM GNM_Party 
                             WHERE Company_ID = @Company_ID 
                             AND Party_IsActive = 1 
                             AND PartyType = 4";

                    // Append filter condition if Value is not empty
                    if (!string.IsNullOrEmpty(Value))
                    {
                        query += " AND Party_Name LIKE @Value";
                    }

                    // Set parameters
                    cmd.CommandText = query;
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                    if (!string.IsNullOrEmpty(Value))
                    {
                        cmd.Parameters.AddWithValue("@Value", "%" + Value + "%");
                    }

                    // Execute query and populate list
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            partyList.Add(new GNM_Party
                            {
                                Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                Party_Name = reader.GetString(reader.GetOrdinal("Party_Name"))
                            });
                        }
                    }

                    // Convert to IQueryable<PartyFieldSearchObject>
                    flSrch = (from q in partyList
                              select new PartyFieldSearchObject()
                              {
                                  ID = q.Party_ID,
                                  Name = q.Party_Name
                              }).AsQueryable(); // Convert to IQueryable
                }

                // Handle advanced search

                //if (Request.Params["advnce"] == "true")
                //{
                //    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();
                //    flSrch = flSrch.AdvanceSearch<PartyFieldSearchObject>(advnfilter);
                //}

                // Sorting
                flSrch = flSrch.OrderByField<PartyFieldSearchObject>(sidx, sord);

                // Pagination logic
                Count = flSrch.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                // Paginate results
                var paginatedResults = flSrch.Skip((page - 1) * rows).Take(rows).ToList();

                // Create response
                var jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    rows = paginatedResults.Select(q => new
                    {
                        ID = q.ID,
                        Name = q.Name,
                        Select = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectFieldSearchPartyObj.UserCulture.ToString(), "select").ToString() + " href='#' style='font-size: 13px;' id='" + q.ID + "' class='FieldSrch' style='cursor:pointer'><i class='fa fa-check'></i></a>"
                    }).ToList()
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
        }

        #endregion

        #region ::: Check Valid Date Format /Mithun:::
        public static IActionResult CheckValidDateFormat(CheckValidDateFormatList CheckValidDateFormatObj, int LogException)
        {
            var Result = "";
            var jsonresult = default(dynamic);
            try
            {
                DateTime temp = Convert.ToDateTime(CheckValidDateFormatObj.dateformat);
                Result = "Success";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Result = "Fail";
            }
            jsonresult = new
            {
                Result = Result
            };
            //return Json(jsonresult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonresult);
        }
        #endregion

        public static List<Attachements> GetAllAttachemnts(int TransactionID, int ObjectID, int DetailID, string Tablename, string constring, int LogException)
        {
            List<Attachements> ds = new List<Attachements>();

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "";

                    // Ensure the query is set for all conditions
                    if (DetailID != 0 && !string.IsNullOrEmpty(Tablename))
                    {
                        query = "SELECT * FROM GNM_MODELATTACHMENTDETAIL WHERE MODEL_ID = @TransactionID";
                    }
                    else if (TransactionID != 0)
                    {
                        query = "SELECT * FROM GNM_MODELATTACHMENTDETAIL WHERE MODEL_ID = @TransactionID AND OBJECT_ID = @ObjectID";
                    }
                    else
                    {
                        // No valid query conditions are met
                        return null; // Return null if conditions are not met
                    }

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@TransactionID", TransactionID);
                        if (TransactionID != 0)
                        {
                            cmd.Parameters.AddWithValue("@ObjectID", ObjectID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var attachment = new Attachements
                                {
                                    ATTACHMENTDETAIL_ID = !reader.IsDBNull(reader.GetOrdinal("MODELATTACHMENTDETAIL_ID")) ? reader.GetInt32(reader.GetOrdinal("MODELATTACHMENTDETAIL_ID")) : 0,
                                    TransactionID = !reader.IsDBNull(reader.GetOrdinal("MODEL_ID")) ? reader.GetInt32(reader.GetOrdinal("MODEL_ID")) : 0,
                                    FILE_NAME = !reader.IsDBNull(reader.GetOrdinal("FILENAME")) ? reader.GetString(reader.GetOrdinal("FILENAME")) : string.Empty,
                                    FILEDESCRIPTION = !reader.IsDBNull(reader.GetOrdinal("FILEDESCRIPTION")) ? reader.GetString(reader.GetOrdinal("FILEDESCRIPTION")) : string.Empty,
                                    UPLOADDATE = (DateTime)(!reader.IsDBNull(reader.GetOrdinal("UPLOADDATE")) ? reader.GetDateTime(reader.GetOrdinal("UPLOADDATE")) : (DateTime?)null),
                                    UPLOADDATESORT = !reader.IsDBNull(reader.GetOrdinal("UPLOADDATE")) ? reader.GetDateTime(reader.GetOrdinal("UPLOADDATE")).ToString("dd-MMM-yyyy hh:mm tt") : string.Empty,
                                    UPLOADBY = !reader.IsDBNull(reader.GetOrdinal("UPLOADBY")) ? GetUserName(reader.GetInt32(reader.GetOrdinal("UPLOADBY")), conn) : string.Empty,
                                    Upload = !reader.IsDBNull(reader.GetOrdinal("UPLOADBY")) ? reader.GetInt32(reader.GetOrdinal("UPLOADBY")) : 0,
                                    AttachmentIDS = !reader.IsDBNull(reader.GetOrdinal("MODELATTACHMENTDETAIL_ID")) ? reader.GetInt32(reader.GetOrdinal("MODELATTACHMENTDETAIL_ID")).ToString() : string.Empty,
                                    OBJECTID = !reader.IsDBNull(reader.GetOrdinal("OBJECT_ID")) ? reader.GetInt32(reader.GetOrdinal("OBJECT_ID")) : 0,
                                    autodisp = !reader.IsDBNull(reader.GetOrdinal("AUTODISPLAY")) && reader.GetBoolean(reader.GetOrdinal("AUTODISPLAY")),
                                    Prin = !reader.IsDBNull(reader.GetOrdinal("PRINT")) && reader.GetBoolean(reader.GetOrdinal("PRINT"))
                                };

                                ds.Add(attachment);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception (consider using a logging framework or custom logger)
                Console.WriteLine($"Error: {ex.Message}");
                // You might want to log more details or handle the error appropriately
            }

            return ds; // Return the list even if it's empty
        }

        private static string GetUserName(int userId, SqlConnection conn)
        {
            string userName = "";
            string query = "SELECT User_Name FROM GNM_User WHERE User_ID = @UserId";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@UserId", userId);

                object result = cmd.ExecuteScalar();
                if (result != null)
                {
                    userName = result.ToString();
                }
            }

            return userName;
        }

        #region::: SelAllAttachment /Mithun:::
        /// <summary>
        /// SelAllAttachment
        /// </summary>
        /// <param name="SelAllAttachmentObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <returns></returns>
        public static IActionResult SelAllAttachment(SelAllAttachmentList SelAllAttachmentObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonData = default(dynamic);
            try
            {
                if (SelAllAttachmentObj == null)
                {
                    return new JsonResult(new { success = false, message = "Invalid input data." });
                }

                int DetailID = SelAllAttachmentObj.DetailID; // No need to use HasValue
                string ObjectNmae = SelAllAttachmentObj.ObjectNames ?? string.Empty;
                string Tablename = SelAllAttachmentObj.Tablename ?? string.Empty;
                int Language_ID = SelAllAttachmentObj.LangID; // Directly use it if it's int

                List<Attachements> dsToolsDetails = new List<Attachements>();
                int GeneralLanguageID = Convert.ToInt32(SelAllAttachmentObj.GeneralLanguageID);
                int userLanguageID = Convert.ToInt32(SelAllAttachmentObj.UserLanguageID);
                int ObjectID = Common.GetObjectID(ObjectNmae);

                List<Attachements> Attache = GetAllAttachemnts(SelAllAttachmentObj.TransactionID, ObjectID, DetailID, Tablename, constring, LogException).ToList();
                List<Attachements> POAttachmentList = null;

                if (!string.IsNullOrEmpty(SelAllAttachmentObj.AttachmentData))
                {
                    POAttachmentList = JsonConvert.DeserializeObject<List<Attachements>>(SelAllAttachmentObj.AttachmentData);
                }

                dsToolsDetails.AddRange(Attache);
                if (POAttachmentList != null)
                {
                    dsToolsDetails.AddRange(POAttachmentList);
                }
                string View = CommonFunctionalities.GetGlobalResourceObject(SelAllAttachmentObj.UserCulture.ToString(), "view").ToString();
                var arrToolsList = from a in dsToolsDetails
                                   select new Attachements()
                                   {
                                       view = (Convert.ToInt32(a.DetailID) == 0) ? "<a target='_blank' style='color:blue;' href='" + SelAllAttachmentObj.AppPathString.ToString() + " /ModelAttachments/" + a.OBJECTID + "-" + a.TransactionID + "-" + ((a.FILE_NAME)) + "' class='OpenAttachments'>" + ((GeneralLanguageID == Language_ID) ? "View" : View) + "</a>" : "<a target='_blank' style='color:blue;' href='" + SelAllAttachmentObj.AppPathString.ToString() + " /ModelAttachments/" + a.OBJECTID + "-" + a.TransactionID + "-" + a.DetailID + "-" + ((a.FILE_NAME)) + "' class='OpenAttachments'>" + ((GeneralLanguageID == Language_ID) ? "View" : View) + "</a>",
                                       //edit = "<img id='" + a.ATTACHMENTDETAIL_ID + "' src='" + Session["AppPathString"].ToString() + "/Content/Images/edit.gif' key='" + a.ATTACHMENTDETAIL_ID + "' class='' editmode='false'/>",
                                       edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelAllAttachmentObj.UserCulture.ToString(), "view").ToString() + " href='#' id='" + a.ATTACHMENTDETAIL_ID + "' key='" + a.ATTACHMENTDETAIL_ID + "' class='font-icon-class' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                       delete = Convert.ToInt32(a.ATTACHMENTDETAIL_ID) == 0 ? "<input type='checkbox' key='" + a.AttachmentIDS + "' id='chk" + a.AttachmentIDS + "' class='chkToolsAttachmentDelete'/>" : "<input type='checkbox' key='" + a.ATTACHMENTDETAIL_ID + "' id='chk" + a.ATTACHMENTDETAIL_ID + "' class='chkToolsAttachmentDelete'/>",
                                       ATTACHMENTDETAIL_ID = a.ATTACHMENTDETAIL_ID,
                                       FILE_NAME = a.FILE_NAME,
                                       FILEDESCRIPTION = a.FILEDESCRIPTION,
                                       UPLOADBY = (userLanguageID == GeneralLanguageID) ? a.UPLOADBY : GetUploadBy(a.Upload, constring),
                                       UPLOADDATE = a.UPLOADDATE,
                                       UPLOADDATESORT = a.UPLOADDATESORT,
                                       Upload = a.Upload,
                                       OBJECTID = a.OBJECTID,
                                       TransactionID = a.TransactionID,
                                       DetailID = a.DetailID,
                                       AttachmentIDS = a.AttachmentIDS,
                                       ID = a.ATTACHMENTDETAIL_ID,
                                       autodisplay = a.autodisp == true ? CommonFunctionalities.GetGlobalResourceObject(SelAllAttachmentObj.UserCulture.ToString(), "yes").ToString() : CommonFunctionalities.GetGlobalResourceObject(SelAllAttachmentObj.UserCulture.ToString(), "no").ToString(),
                                       Print = a.Prin == true ? CommonFunctionalities.GetGlobalResourceObject(SelAllAttachmentObj.UserCulture.ToString(), "yes").ToString() : CommonFunctionalities.GetGlobalResourceObject(SelAllAttachmentObj.UserCulture.ToString(), "no").ToString(),
                                   };


                var iToosArray = arrToolsList.AsQueryable().OrderByField(sidx, sord);

                int Count = iToosArray.Count();
                int Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = iToosArray.ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                return new JsonResult(new { success = false, message = ex.Message });
            }

            return new JsonResult(jsonData);
        }

        #endregion
        private static string GetUploadBy(int userId, string constring)
        {
            string uploadBy = null;
            using (SqlConnection conn = new SqlConnection(constring))
            {
                string query = "SELECT User_Name FROM GNM_UserLocale WHERE User_ID = @User_ID";
                SqlCommand cmd = new SqlCommand(query, conn);
                cmd.Parameters.AddWithValue("@User_ID", userId);

                conn.Open();
                var result = cmd.ExecuteScalar();
                if (result != null)
                {
                    uploadBy = result.ToString();
                }
            }
            return uploadBy;
        }

        #region::: DeleteAttachments /Mithun:::
        /// <summary>
        /// DeleteAttachments
        /// </summary>
        /// <param name="DeleteAttachmentsObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult DeleteAttachments(DeleteAttachmentsList DeleteAttachmentsObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    for (int i = 0; i < DeleteAttachmentsObj.dsObj.Count(); i++)
                    {
                        int ID = DeleteAttachmentsObj.dsObj[i].ATTACHMENTDETAIL_ID;
                        if (ID != 0)
                        {
                            // Delete from the database
                            string deleteQuery = "DELETE FROM GNM_MODELATTACHMENTDETAIL WHERE MODELATTACHMENTDETAIL_ID = @ID";
                            using (SqlCommand cmd = new SqlCommand(deleteQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@ID", ID);
                                cmd.ExecuteNonQuery();
                            }

                            // Delete the corresponding file from the file system
                            string filePath = Path.Combine(DeleteAttachmentsObj.SMP, DeleteAttachmentsObj.dsObj[i].OBJECTID + "-" + DeleteAttachmentsObj.dsObj[i].TransactionID + "-" + Common.DecryptString(DeleteAttachmentsObj.dsObj[i].FILE_NAME));
                            if (File.Exists(filePath))
                            {
                                File.Delete(filePath);
                            }
                        }
                    }
                }

                Msg = "Deleted";
            }
            catch (Exception ex)
            {
                // Log exception details
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion


        public static string CreateRandomid()
        {

            string _alloedchars = "abcdefghijklmnopqrstuvwxyz01234567890";
            Random randnum = new Random((int)DateTime.Now.Ticks);
            char[] chars = new char[6];
            for (int i = 0; i < 6; i++)
            {
                chars[i] = _alloedchars[randnum.Next(_alloedchars.Length)];

            }

            return new string(chars);
        }





        public class SelectAttachmentDetailsAddModeList
        {
            public int User_ID { get; set; }
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public string Tablename { get; set; }
            public string AppPathString { get; set; }
            public string UserCulture { get; set; }
            public int ObjectID { get; set; }
        }
        public class ParentCompanyObject
        {
            public int Company_ID
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public int Company_Parent_ID
            {
                get;
                set;
            }
        }
        public class DeleteAttachmentsList
        {
            public string SMP { get; set; }

            public Attachements[] dsObj { get; set; }
        }
        public class SelAllAttachmentList
        {
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public int LangID { get; set; }
            public string ObjectNames { get; set; }
            public string Tablename { get; set; }
            public string UserCulture { get; set; }
            public string AttachmentData { get; set; }
            public string AppPathString { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class CheckValidDateFormatList
        {
            public string dateformat { get; set; }
        }
        public class SelectFieldSearchPartyList
        {
            public string Value { get; set; }
            public string UserCulture { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }

        }
        public class PartyFieldSearchObject
        {
            public int ID { get; set; }
            public string Name { get; set; }
            public string Search { get; set; }
        }


        public class ChangePartyNameList
        {
            public string Key { get; set; }
            public string mode { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int ModelID { get; set; }
            public int primID { get; set; }
            public int LanguageID { get; set; }
        }
        public class GetSupplierPricePreviousDateList
        {
            public int Company_ID { get; set; }
            public int modelID { get; set; }
            public string currentEditMode { get; set; }
        }
        public class DeleteModelSalesPriceDetailsRowsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class DeleteModelSupplierPriceDetailsRowsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class InsertModelSalesPriceListDetailList
        {
            public int UserLanguageID { get; set; }
            public string SalesPriceListdata { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class InsertModelSupplierPriceListDetailList
        {
            public int UserLanguageID { get; set; }
            public string SupplierPriceListdata { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class CheckDuplicateDescriptionRedList
        {
            public int Company_ID { get; set; }
            public string Val { get; set; }
        }

        public class CheckDuplicateDescriptionList
        {
            public int Company_ID { get; set; }
            public string Val { get; set; }
        }
        public class SelectParticularRedCarpetChecklistLocaleList
        {
            public int UserLanguageID { get; set; }
            public int RedCarpetChecklist_ID { get; set; }
        }
        public class InsertRedCarpetChecklistLocaleList
        {
            public int UserLanguageID { get; set; }
            public string key { get; set; }
        }
        public class SelectModelRedCarpetChecklistDetailList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int ModelID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class RedCarpetChecklistDetail
        {
            public int ModelRedCarpetChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public int Language_ID { get; set; }
            public string Description { get; set; }
            public string IsMandatory { get; set; }
            public string IsPhotoRequired { get; set; }
        }

        public class InsertQuickChecklistLocaleList
        {
            public int UserLanguageID { get; set; }
            public string key { get; set; }
        }
        public class SelectParticularQuickChecklistLocaleList
        {
            public int UserLanguageID { get; set; }
            public int QuickChecklist_ID { get; set; }
        }
        public class SelectModelQuickChecklistDetailList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int ModelID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class QuickChecklistDetail
        {
            public int ModelQuickListDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public string IsMandatory { get; set; }
            public string IsPhotoRequired { get; set; }
            public string DefaultCheck { get; set; }
            public string Minvalue { get; set; }
            public string Maxvalue { get; set; }
        }
        public class SelectModelSalesPriceDetailGridList
        {
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int ModelID { get; set; }
            public string UserCulture { get; set; }
        }
        public class SalesPriceDetail
        {
            public int ModelSalesPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public decimal DealerNetPrice { get; set; }
            public decimal MRP { get; set; }
            public DateTime? EffectiveFrom { get; set; }
        }
        public class SelectModelSupplierDetailGridList
        {
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int ModelID { get; set; }
            public string UserCulture { get; set; }
        }
        public class SupplierPriceDetail
        {
            public int ModelSupplierPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public int Supplier_ID { get; set; }
            public int Currency_ID { get; set; }
            public DateTime? EffectiveFrom { get; set; }
            public string Currency { get; set; }
            public decimal SupplierPrice { get; set; }
            public string Name { get; set; }
        }
        public partial class GNM_ModelSupplierPriceDetails
        {
            public int ModelSupplierPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public int Supplier_ID { get; set; }
            public int Currency_ID { get; set; }
            public decimal SupplierPrice { get; set; }
            public Nullable<System.DateTime> EffectiveFrom { get; set; }
            public int Company_ID { get; set; }
        }
        public class getPreviousDateModelList
        {
            public int Company_ID { get; set; }
            public int modelID { get; set; }
            public string Currenteditmode { get; set; }
        }
        public class InsertModelRedCarpetChecklistDetailList
        {
            public int Company_ID { get; set; }
            public string RedCarpetChecklistData { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }

        }
        public class InsertModelQuickChecklistDetailList
        {
            public int Company_ID { get; set; }
            public string QuickChecklistData { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }

        }
        public class InsertModelPriceListDetailList
        {
            public int Company_ID { get; set; }
            public string PriceListdata { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }

        }
        public class MakeLastRowEditableModelList
        {
            public int Company_ID { get; set; }
            public int ModelID { get; set; }
        }
        public class SelectPriceListList
        {
            public int ModelID { get; set; }
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserCulture { get; set; }
        }
        public class ModelPriceList
        {
            public int ModelPriceDetails_ID
            {
                get;
                set;
            }

            public int Model_ID
            {
                get;
                set;
            }
            public decimal ListPrice { get; set; }
            public DateTime EffectiveFrom { get; set; }
        }
        public class CheckModelNameList
        {
            public string ModelName { get; set; }
            public int BrandID { get; set; }
            public int ProductTypeID { get; set; }
            public int ModelID { get; set; }
            public int UserLanguageID { get; set; }
        }
        public class CheckModelNameLocaleList
        {
            public string ModelName { get; set; }
            public int BrandID { get; set; }
            public int ProductTypeID { get; set; }
            public int ModelID { get; set; }
            public int UserLanguageID { get; set; }
        }
        public class DeleteRedCarpetChecklistList
        {
            public int ModelID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }

        }
        public class DeleteQuickChecklistList
        {
            public int ModelID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }

        }
        public class DeleteServiceChargeDetailList
        {
            public int ModelID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }

        }

        public class DeleteModelList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int LanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class UpdateLocaleModelList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int LanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }
        }
        public class InsertModelLocaleList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int LanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
        }

        public class SelectSingleModelLocaleList
        {
            public int id { get; set; }
        }
        public class SelectParticularModelList
        {
            public int id { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
        }
        public class InsertServiceChargeDetailList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int LanguageID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }
        }
        public class SelectServiceChargedetailList
        {
            public int Company_ID { get; set; }
            public int ModelID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class ModelServiceChargeDetail
        {
            public int ModelServiceChargeDetail_ID
            {
                get;
                set;
            }

            public int Model_ID
            {
                get;
                set;
            }
            public int ServiceType_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public string ServiceCharge { get; set; }
            public int Company_ID
            {
                get;
                set;
            }
            public string EffectiveDateSort { get; set; }
        }

        public class CheckServiceTypeList
        {
            public int ServiceType_ID { get; set; }
            public int ModelServiceChargeDetail_ID { get; set; }
            public int ModelID { get; set; }
            public DateTime? EffectiveDate { get; set; }
        }
        public class SelectProductTypeModelList
        {
            public int BrandID { get; set; }
            public int UserLanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }
        public class SelectReferenceMasterModelMasterList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }
        public class SelectServiceTypeList
        {
            public int LanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int? ServiceTypeID { get; set; }
        }


        public class UpdateModelMasterList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int ObjectID { get; set; }
            public string Lang { get; set; }
            public string data { get; set; }
            public string AttachmentsData { get; set; }
            public string AttachmentData { get; set; }
            public string ModelAttachmentDelete { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class Attachements
        {
            public int ATTACHMENTDETAIL_ID
            {
                get;
                set;
            }
            public string AttachmentIDS { get; set; }
            public int TransactionID { get; set; }
            public string FILE_NAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public string UPLOADBY { get; set; }
            public DateTime UPLOADDATE { get; set; }
            public string UPLOADDATESORT { get; set; }
            public string delete { get; set; }
            public int Upload { get; set; }
            public string view { get; set; }
            public int OBJECTID { get; set; }
            public string DocumentType { get; set; }
            public int DocumentType_ID { get; set; }
            public int? DetailID { get; set; }
            public int ID { get; set; }
            public string edit { get; set; }
            public string Tablename { get; set; }

            public bool autodisp { get; set; }
            public string Print { get; set; }
            public string autodisplay { get; set; }
            public bool Prin { get; set; }
        }
        public class InsertModelMasterList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }

        public class SelectUCLSerialNumberAndStatusList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }

        }

        public class SelectUCLBranchWiseList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }

        }

        public class SelectUCLStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectOWHSerialNumberAndStatusList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectOWHBranchWiseList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectOWHStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectSerialNumberAndStatusList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectGITBranchWiseList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectGITStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }

        public class SelectSerialNumberOfPhysicalStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int WareHouseID { get; set; }
        }

        public class SelectWarehouseWisePhysicalStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int CompanyID { get; set; }
        }
        public class SelectBranchWisePhysicalStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }

        public class SelectPhysicalStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectSerialNumberList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public int BranchID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class SelectBranchWiseList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int CompanyID { get; set; }
        }
        public class ModelAvailableStock
        {
            public int Model_ID { get; set; }
            public int Procuct_ID { get; set; }
            public int? Warehouse_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public int ProcuctCount { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string SerialNumber { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
            public string WareHouseName { get; set; }
            public int Count { get; set; }
        }

        public class SelectAvailableStockList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
        }
        public class StockDetails
        {
            public int PRODUCT_ID { get; set; }
            public int COMPANY_ID { get; set; }
            public int BRANCH_ID { get; set; }
            public string PRODUCT_SERIALNUMBER { get; set; }
            public string Status { get; set; }
            public int WAREHOUSE_ID { get; set; }
            public string WareHouse { get; set; }
            public int SERIALSTATUS { get; set; }
        }
        public partial class SelectParticularModelHeaderList
        {
            public int modelID { get; set; }
            public int languageid { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public partial class SelectModelList
        {
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int BrandID { get; set; }
            public int ProductTypeID { get; set; }
            public int LanguageID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }
        public partial class GNM_ServiceTypeLocale
        {
            public int ServiceTypeLocale_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ServiceType GNM_ServiceType { get; set; }
        }
        public partial class CoreSRM_Operation
        {
            public CoreSRM_Operation()
            {
                this.GNM_ServiceTypeOperationDetail = new HashSet<GNM_ServiceTypeOperationDetail>();
                this.SRM_OperationBranchDetail = new HashSet<CoreSRM_OperationBranchDetail>();
                this.SRM_OperationCheckListDetail = new HashSet<CoreSRM_OperationCheckListDetail>();
                this.SRM_OperationLocale = new HashSet<CoreSRM_OperationLocale>();
                this.SRM_OperationProductDetail = new HashSet<CoreSRM_OperationProductDetail>();
            }

            public int Operation_ID { get; set; }
            public int Company_ID { get; set; }
            public string Operation_Description { get; set; }
            public string Operation_Code { get; set; }
            public Nullable<int> FunctionGroup_ID { get; set; }
            public Nullable<int> Skill_ID { get; set; }
            public Nullable<byte> Operation_SkillLevel { get; set; }
            public Nullable<decimal> Operation_StandardTime { get; set; }
            public Nullable<decimal> Operation_Time { get; set; }
            public bool Operation_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ServiceTypeOperationDetail> GNM_ServiceTypeOperationDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationBranchDetail> SRM_OperationBranchDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationCheckListDetail> SRM_OperationCheckListDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationLocale> SRM_OperationLocale { get; set; }
            public virtual ICollection<CoreSRM_OperationProductDetail> SRM_OperationProductDetail { get; set; }
        }
        public partial class CoreSRM_OperationBranchDetail
        {
            public int OperationBranchDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Branch_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationCheckListLocaleDetail
        {
            public int OperationCheckListLocaleDetail_ID { get; set; }
            public int OperationCheckListDetail_ID { get; set; }
            public string CheckListLocaleDescription { get; set; }
            public int Language_ID { get; set; }

            public virtual CoreSRM_OperationCheckListDetail SRM_OperationCheckListDetail { get; set; }
        }
        public partial class CoreSRM_OperationCheckListDetail
        {
            public CoreSRM_OperationCheckListDetail()
            {
                this.SRM_OperationCheckListLocaleDetail = new HashSet<CoreSRM_OperationCheckListLocaleDetail>();
            }

            public int OperationCheckListDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public string CheckListDescription { get; set; }
            public Nullable<bool> IsMandatory { get; set; }
            public Nullable<bool> IsSpecialTools { get; set; }
            public Nullable<bool> IsSafetyMeasures { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
            public virtual ICollection<CoreSRM_OperationCheckListLocaleDetail> SRM_OperationCheckListLocaleDetail { get; set; }
        }
        public partial class CoreSRM_OperationLocale
        {
            public int OperationLocale_ID { get; set; }
            public int Operation_ID { get; set; }
            public string Operation_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationProductDetail
        {
            public int OperationProductDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class GNM_ServiceTypeOperationDetail
        {
            public int ServiceTypeOperationDetail_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public int Operation_ID { get; set; }

            public virtual GNM_ServiceType GNM_ServiceType { get; set; }
            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class GNM_ServiceType
        {
            public GNM_ServiceType()
            {
                this.GNM_ServiceTypeLocale = new HashSet<GNM_ServiceTypeLocale>();
                this.GNM_ServiceTypeOperationDetail = new HashSet<GNM_ServiceTypeOperationDetail>();
            }

            public int ServiceType_ID { get; set; }
            public int Company_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public bool ServiceType_Active { get; set; }
            public Nullable<bool> IsMandatoryService { get; set; }
            public Nullable<int> ServiceDueHours { get; set; }
            public Nullable<int> ServiceDueDays { get; set; }
            public Nullable<bool> IsWarrantyClaimable { get; set; }
            public Nullable<bool> IsDemandDrive { get; set; }
            public Nullable<bool> IsInsuranceJob { get; set; }
            public string ServiceType_Code { get; set; }
            public Nullable<byte> ServiceType_Priority { get; set; }
            public Nullable<bool> IsCommissioning { get; set; }
            public Nullable<bool> IsNotificationEligible { get; set; }
            public Nullable<bool> ChargeToTypeInvoice { get; set; }
            public Nullable<bool> ChargeToTypeInternalInvoice { get; set; }
            public Nullable<bool> ChargeToTypeWarranty { get; set; }
            public Nullable<bool> IsUnderStandardWarranty { get; set; }
            public Nullable<bool> IsUnderExtendedWarranty { get; set; }
            public Nullable<bool> IsNotUnderStandardandExtendedWarranty { get; set; }
            public Nullable<bool> IsInvoice { get; set; }
            public Nullable<bool> IsInternalInvoice { get; set; }
            public string PrevostNotifT { get; set; }
            public string NovaNotifT { get; set; }
            public string SystemCondition { get; set; }
            public Nullable<bool> IsStandardText { get; set; }

            public virtual ICollection<GNM_ServiceTypeLocale> GNM_ServiceTypeLocale { get; set; }
            public virtual ICollection<GNM_ServiceTypeOperationDetail> GNM_ServiceTypeOperationDetail { get; set; }
        }
        public partial class GNM_ModelServiceChargeDetail
        {
            public int ModelServiceChargeDetail_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<decimal> ServiceCharge { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<System.DateTime> EffectiveDate { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_MODELSERVICETYPEDET
        {
            public int MODELSERVICETYPEDET_ID { get; set; }
            public int MODEL_ID { get; set; }
            public int SERVICETYPE_ID { get; set; }
            public int COMPANY_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelWarrantyDefinitionDetails
        {
            public int ModelWarrantyDefinitionDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<bool> Applicable_Type { get; set; }
            public int WarrantyMonths { get; set; }
            public int WarrantyHours { get; set; }
            public System.DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelSalesPriceDetails
        {
            public int ModelSalesPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public decimal DealerNetPrice { get; set; }
            public decimal MRP { get; set; }
            public System.DateTime EffectiveFrom { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelRedCarpetListDetailsLocale
        {
            public int ModelRedCarpetChecklistLocale_ID { get; set; }
            public int ModelRedCarpetChecklist_ID { get; set; }
            public string Description { get; set; }
            public Nullable<int> Language_ID { get; set; }

            public virtual GNM_ModelRedCarpetListDetails GNM_ModelRedCarpetListDetails { get; set; }
        }
        public partial class GNM_ModelRedCarpetListDetails
        {
            public GNM_ModelRedCarpetListDetails()
            {
                this.GNM_ModelRedCarpetListDetailsLocale = new HashSet<GNM_ModelRedCarpetListDetailsLocale>();
            }

            public int ModelRedCarpetChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsPhotoRequired { get; set; }

            public virtual ICollection<GNM_ModelRedCarpetListDetailsLocale> GNM_ModelRedCarpetListDetailsLocale { get; set; }
            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelQuickListDetailsLocale
        {
            public int ModelQuickChecklistLocale_ID { get; set; }
            public int ModelQuickChecklist_ID { get; set; }
            public string Description { get; set; }
            public Nullable<int> Language_ID { get; set; }

            public virtual GNM_ModelQuickListDetails GNM_ModelQuickListDetails { get; set; }
        }
        public partial class GNM_ModelQuickListDetails
        {
            public GNM_ModelQuickListDetails()
            {
                this.GNM_ModelQuickListDetailsLocale = new HashSet<GNM_ModelQuickListDetailsLocale>();
            }

            public int ModelQuickChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsPhotoRequired { get; set; }
            public Nullable<byte> IsDefaultCheck { get; set; }
            public string Minvalue { get; set; }
            public string Maxvalue { get; set; }

            public virtual ICollection<GNM_ModelQuickListDetailsLocale> GNM_ModelQuickListDetailsLocale { get; set; }
            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelPriceDetails
        {
            public int ModelPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public System.DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelLocale
        {
            public int ModelLocale_ID { get; set; }
            public int Model_ID { get; set; }
            public string Model_Name { get; set; }
            public int Language_ID { get; set; }
            public string Model_Description { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_Model
        {
            public GNM_Model()
            {
                this.GNM_ModelLocale = new HashSet<GNM_ModelLocale>();
                this.GNM_ModelPriceDetails = new HashSet<GNM_ModelPriceDetails>();
                this.GNM_ModelQuickListDetails = new HashSet<GNM_ModelQuickListDetails>();
                this.GNM_ModelRedCarpetListDetails = new HashSet<GNM_ModelRedCarpetListDetails>();
                this.GNM_ModelSalesPriceDetails = new HashSet<GNM_ModelSalesPriceDetails>();
                this.GNM_ModelServiceChargeDetail = new HashSet<GNM_ModelServiceChargeDetail>();
                this.GNM_MODELSERVICETYPEDET = new HashSet<GNM_MODELSERVICETYPEDET>();
                this.GNM_ModelWarrantyDefinitionDetails = new HashSet<GNM_ModelWarrantyDefinitionDetails>();
            }

            public int Model_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string Model_Name { get; set; }
            public bool Model_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> ServiceFrequency { get; set; }
            public string Model_Description { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public string Series { get; set; }

            public virtual ICollection<GNM_ModelLocale> GNM_ModelLocale { get; set; }
            public virtual ICollection<GNM_ModelPriceDetails> GNM_ModelPriceDetails { get; set; }
            public virtual ICollection<GNM_ModelQuickListDetails> GNM_ModelQuickListDetails { get; set; }
            public virtual ICollection<GNM_ModelRedCarpetListDetails> GNM_ModelRedCarpetListDetails { get; set; }
            public virtual ICollection<GNM_ModelSalesPriceDetails> GNM_ModelSalesPriceDetails { get; set; }
            public virtual ICollection<GNM_ModelServiceChargeDetail> GNM_ModelServiceChargeDetail { get; set; }
            public virtual ICollection<GNM_MODELSERVICETYPEDET> GNM_MODELSERVICETYPEDET { get; set; }
            public virtual ICollection<GNM_ModelWarrantyDefinitionDetails> GNM_ModelWarrantyDefinitionDetails { get; set; }
        }
        public class ModelMaster
        {

            public int ServiceType_ID
            {
                get;
                set;
            }
            public int Model_ID
            {
                get;
                set;
            }
            public int Company_ID
            {
                get;
                set;
            }

            public string Model_Name
            {
                get;
                set;
            }
            public string Model_IsActive { get; set; }
            public string ServiceFrequencySort { get; set; }
            public int ProductType_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public int ServiceFrequency { get; set; }
            public decimal AverageSellingPrice { get; set; }
            public decimal AveragePurchasePrice { get; set; }
            public string AverageSellingPriceStr { get; set; }
            public string AveragePurchasePriceStr { get; set; }
            public int Brand_ID { get; set; }
            public string Model_Description { get; set; }
            public string AvailableStock { get; set; }
            public string physicalStock { get; set; }
            public string GITStock { get; set; }
            public string Outofwarehouse { get; set; }
            public string UnderClearence { get; set; }
            public byte? Attachmentcount { get; set; }
        }

    }
}
