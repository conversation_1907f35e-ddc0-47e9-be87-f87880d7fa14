﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Tr_AverageResolutionTimeServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Tr_AverageResolutionTimeController : ApiController
    {



        #region ::: Select Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To select Year wise Details
        /// </summary> 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResolutionTimeServices.Select(HelpDesk_Tr_AverageResolutionSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectMonthWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Month Wise Avearage Resolution Time
        /// </summary>
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectMonthWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMonthWise([FromBody] HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectMonthWise(HelpDesk_Tr_AverageResolutionSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectDateWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Wise
        /// </summary>
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectDateWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDateWise([FromBody] HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectDateWise(HelpDesk_Tr_AverageResolutionSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: LoadBranchDD  Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        /// 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/LoadBranchDD")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBranchDD([FromBody] HelpDesk_Tr_AverageResolutionLoadBranchDDList HelpDesk_Tr_AverageResolutionLoadBranchDDobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResolutionTimeServices.LoadBranchDD(HelpDesk_Tr_AverageResolutionLoadBranchDDobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectCustomer Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To Select Customer
        /// </summary>
        /// 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCustomer([FromBody] HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectCustomer(HelpDesk_Tr_AverageResolutionSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: SelectModel Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Model
        /// </summary> 
        ///
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectModel(HelpDesk_Tr_AverageResolutionSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectISSUEAREA Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To Select ISSUEAREA
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectISSUEAREA")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectISSUEAREA([FromBody] HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectISSUEAREA(HelpDesk_Tr_AverageResolutionSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCallProirity Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select CallPriority
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectCallPriority")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCallPriority([FromBody] HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectCallPriority(HelpDesk_Tr_AverageResolutionSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectDateDetails  Uday Kmar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Details
        /// </summary>
        /// 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/SelectDateDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDateDetails([FromBody] HelpDesk_Tr_AverageResolutionSelectDateDetailsList HelpDesk_Tr_AverageResolutionSelectDateDetailsobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResolutionTimeServices.SelectDateDetails(HelpDesk_Tr_AverageResolutionSelectDateDetailsobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region :::Export Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_AverageResolutionTime/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Tr_AverageResolutionExportList HelpDesk_Tr_AverageResolutionExportobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HelpDesk_Tr_AverageResolutionExportobj.sidx;
            string sord = HelpDesk_Tr_AverageResolutionExportobj.sord;
            string filter = HelpDesk_Tr_AverageResolutionExportobj.filter;
            string advnceFilter = HelpDesk_Tr_AverageResolutionExportobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Tr_AverageResolutionTimeServices.Export(HelpDesk_Tr_AverageResolutionExportobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


    }
}