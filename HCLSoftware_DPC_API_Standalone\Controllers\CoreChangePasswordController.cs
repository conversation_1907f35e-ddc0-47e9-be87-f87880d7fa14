﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreChangePasswordController : ApiController
    {
        #region ::: CheckOldPassword /Vinay:::
        /// <summary>
        /// CheckOldPassword
        /// </summary>
        /// <param name="DeleteMenuObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreChangePassword/CheckOldPassword")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckOldPassword([FromBody] CheckOldPasswordList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreChangePasswordServices.CheckOldPassword(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region ::: ChangePassword /Vinay:::
        /// <summary>
        /// ChangePassword
        /// </summary>
        /// <param name="DeleteMenuObj"></param>
        /// <returns></returns>
        [System.Web.Http.Route("api/CoreChangePassword/ChangePassword")]
        [System.Web.Http.HttpPost]
        [JwtTokenValidationFilter]
        public void ChangePassword([FromBody] ChangePasswordList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                CoreChangePasswordServices.ChangePassword(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }


        }
        #endregion
    }
}