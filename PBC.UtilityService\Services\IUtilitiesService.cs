using Microsoft.AspNetCore.Mvc;

namespace PBC.UtilityService.Services
{
    public interface IUtilitiesService
    {
        /// <summary>
        /// Calculate total pages based on number of records and page size
        /// </summary>
        /// <param name="numberOfRecords">Total number of records</param>
        /// <param name="pageSize">Number of records per page</param>
        /// <returns>Total number of pages</returns>
        Task<int> CalculateTotalPagesAsync(long numberOfRecords, int pageSize);

        /// <summary>
        /// Check if a string is a valid date
        /// </summary>
        /// <param name="date">Date string to validate</param>
        /// <returns>True if valid date, false otherwise</returns>
        Task<bool> IsDateAsync(string date);

        /// <summary>
        /// Check if an object is numeric
        /// </summary>
        /// <param name="entity">Object to check</param>
        /// <returns>True if numeric, false otherwise</returns>
        Task<bool> IsNumericAsync(object entity);

        /// <summary>
        /// Check if an object is a valid double
        /// </summary>
        /// <param name="entity">Object to check</param>
        /// <returns>True if valid double, false otherwise</returns>
        Task<bool> IsDoubleAsync(object entity);

        /// <summary>
        /// Create a message list with a single message
        /// </summary>
        /// <param name="message">Message to add to list</param>
        /// <returns>List containing the message</returns>
        Task<List<string>> MessageAsync(string message);

        /// <summary>
        /// Calculate MD5 hash of input string
        /// </summary>
        /// <param name="input">String to hash</param>
        /// <returns>MD5 hash as string</returns>
        Task<string> CalculateMD5HashAsync(string input);

        /// <summary>
        /// Generate DMS password hash
        /// </summary>
        /// <param name="userPassword">Plain text password</param>
        /// <returns>Hashed password</returns>
        Task<string> GenerateDMSPasswordAsync(string userPassword);

        /// <summary>
        /// Get global resource object value
        /// </summary>
        /// <param name="cultureValue">Culture value</param>
        /// <param name="resourceKey">Resource key</param>
        /// <returns>Resource value as JSON</returns>
        Task<IActionResult> GetGlobalResourceObjectAsync(string cultureValue, string resourceKey);

        /// <summary>
        /// Get month name based on culture
        /// </summary>
        /// <param name="id">Month ID (1-12)</param>
        /// <param name="culture">Culture string</param>
        /// <returns>Month name in specified culture</returns>
        Task<string> GetMonthNameAsync(int id, string culture);

        /// <summary>
        /// Get priority name based on culture
        /// </summary>
        /// <param name="id">Priority ID (1-4)</param>
        /// <param name="culture">Culture string</param>
        /// <returns>Priority name in specified culture</returns>
        Task<string> GetPriorityAsync(byte id, string culture);

        /// <summary>
        /// Get initial setup data for ACL permissions
        /// </summary>
        /// <param name="objectId">Object ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>Initial setup data as JSON result</returns>
        Task<JsonResult> GetInitialSetupAsync(int objectId, int userId, string connectionString, int logException);

        /// <summary>
        /// Validate call date and PCD (Promised Completion Date)
        /// </summary>
        /// <param name="pcd">Promised Completion Date</param>
        /// <param name="calldate">Call date</param>
        /// <returns>Validation result (1 if PCD is before call date, 0 otherwise)</returns>
        Task<JsonResult> ValidateCalldateandPCDAsync(DateTime pcd, DateTime calldate);

        /// <summary>
        /// Check bay and workshop availability
        /// </summary>
        /// <param name="expectedArrivalDate">Expected arrival date</param>
        /// <param name="expectedDepartureDate">Expected departure date</param>
        /// <param name="isWIPBay">Is WIP bay flag</param>
        /// <param name="bookingMinutes">Booking minutes</param>
        /// <param name="serviceRequestId">Service request ID</param>
        /// <param name="quotationId">Quotation ID</param>
        /// <param name="branch">Branch ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>Availability status as JSON result</returns>
        Task<JsonResult> CheckBayWorkshopAvailabilityAsync(DateTime expectedArrivalDate, DateTime expectedDepartureDate, bool isWIPBay, int bookingMinutes, int serviceRequestId, int quotationId, int branch, string connectionString, int logException);

        /// <summary>
        /// Check if auto allocation is allowed for a workflow
        /// </summary>
        /// <param name="companyID">Company ID</param>
        /// <param name="workFlowID">Workflow ID</param>
        /// <param name="userID">User ID</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>True if auto allocation is allowed, false otherwise</returns>
        Task<bool> CheckAutoAllocationAsync(int companyID, int workFlowID, int userID, string connString, int logException);

        /// <summary>
        /// Get auto allocation step details for a workflow
        /// </summary>
        /// <param name="workFlowID">Workflow ID</param>
        /// <param name="companyID">Company ID</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>Auto allocation step details</returns>
        Task<object> GetAutoAllocationStepDetailsAsync(int workFlowID, int companyID, string connString, int logException);

        /// <summary>
        /// Lock a record in the workflow system
        /// </summary>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <param name="userCulture">User culture for localization</param>
        /// <param name="quotationID">Quotation ID to lock</param>
        /// <param name="userID">User ID performing the lock</param>
        /// <param name="companyID">Company ID</param>
        /// <param name="workFlowName">Workflow name</param>
        /// <param name="dbName">Database name</param>
        /// <param name="branchID">Branch ID (optional)</param>
        /// <returns>Lock result message</returns>
        Task<string> LockRecordAsync(string connString, int logException, string userCulture, int quotationID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0);

        /// <summary>
        /// Unlock a record in the workflow system
        /// </summary>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <param name="userCulture">User culture for localization</param>
        /// <param name="jobcardID">Jobcard ID to unlock</param>
        /// <param name="userID">User ID performing the unlock</param>
        /// <param name="companyID">Company ID</param>
        /// <param name="workFlowName">Workflow name</param>
        /// <param name="dbName">Database name</param>
        /// <param name="branchID">Branch ID (optional)</param>
        /// <returns>Unlock result message</returns>
        Task<string> UnLockRecordAsync(string connString, int logException, string userCulture, int jobcardID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0);

        /// <summary>
        /// Convert server time to local time based on branch timezone
        /// </summary>
        /// <param name="branchID">Branch ID to get timezone for</param>
        /// <param name="serverTime">Server time to convert</param>
        /// <param name="connString">Database connection string</param>
        /// <returns>Local time based on branch timezone</returns>
        Task<DateTime> LocalTimeBasedOnBranchAsync(int branchID, DateTime serverTime, string connString);

        /// <summary>
        /// Convert server time to local time based on user timezone
        /// </summary>
        /// <param name="userID">User ID to get timezone for</param>
        /// <param name="serverTime">Server time to convert</param>
        /// <param name="connString">Database connection string</param>
        /// <returns>Local time based on user timezone</returns>
        Task<DateTime> LocalTimeAsync(int userID, DateTime serverTime, string connString);

        /// <summary>
        /// Get the end step status name for a workflow
        /// </summary>
        /// <param name="workflowID">Workflow ID to get end step status for</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>End step status name</returns>
        Task<string> GetEndStepStatusNameAsync(int workflowID, string connString, int logException);
    }
}
