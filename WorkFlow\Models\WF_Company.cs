//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_Company
    {
        public WF_Company()
        {
            this.GNM_Branch = new HashSet<WF_Branch>();
            this.GNM_RefMasterDetail2 = new HashSet<WF_RefMasterDetail>();
            this.GNM_CompanyEmployee = new HashSet<WF_CompanyEmployee>();
            this.GNM_PrefixSuffix = new HashSet<WF_PrefixSuffix>();
            this.GNM_User = new HashSet<WF_User>();
        }
    
        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public int Currency_ID { get; set; }
        public string Company_Address { get; set; }
        public string Company_Type { get; set; }
        public bool Company_Active { get; set; }
        public string Company_LogoName { get; set; }
        public Nullable<int> Company_Parent_ID { get; set; }
        public string Remarks { get; set; }
        public byte DefaultGridSize { get; set; }
        public Nullable<decimal> JobCardCushionHours { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> CompanyTheme_ID { get; set; }
        public Nullable<int> QuotationValidity { get; set; }
        public string CompanyFont { get; set; }
        public Nullable<decimal> InventoryCarryingFactoy_Percentage { get; set; }
        public Nullable<int> OrderingCost { get; set; }
    
        public virtual ICollection<WF_Branch> GNM_Branch { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail1 { get; set; }
        public virtual ICollection<WF_RefMasterDetail> GNM_RefMasterDetail2 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee { get; set; }
        public virtual ICollection<WF_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<WF_User> GNM_User { get; set; }
    }
}
