﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActionOK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Actiontobetaken" xml:space="preserve">
    <value>Action to be Taken</value>
  </data>
  <data name="ActionYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="AddedSuccessfully" xml:space="preserve">
    <value>Added successfully..!!</value>
  </data>
  <data name="AddMode" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="AddOnlyimagefile!" xml:space="preserve">
    <value>Add Only image file!</value>
  </data>
  <data name="ALPHAINDEX" xml:space="preserve">
    <value>ALPHABETICAL INDEX / PAGES</value>
  </data>
  <data name="AlreadyaUserLoggedinthisBrowser" xml:space="preserve">
    <value>Already a User Logged in this browser</value>
  </data>
  <data name="API" xml:space="preserve">
    <value>Alphabetical Index ?</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply Filter</value>
  </data>
  <data name="Areyousure" xml:space="preserve">
    <value>Are you sure ?</value>
  </data>
  <data name="Areyousure?" xml:space="preserve">
    <value>Are you sure ?</value>
  </data>
  <data name="AreYouSureYouWantToCancel" xml:space="preserve">
    <value>Are you sure you want to cancel ?</value>
  </data>
  <data name="AreYouSureYouWantToClear" xml:space="preserve">
    <value>Are you sure you want to clear?</value>
  </data>
  <data name="AreYouSureYouWantToClose" xml:space="preserve">
    <value>Are you sure you want to close?</value>
  </data>
  <data name="AreYouSureYouWantToDelete" xml:space="preserve">
    <value>Are you sure you want to Delete?</value>
  </data>
  <data name="AreYousureyouwanttoExcelUploadData" xml:space="preserve">
    <value>Are you sure you want to upload excel data</value>
  </data>
  <data name="AreYousureyouwanttoExcelUploadvalidData" xml:space="preserve">
    <value>Are you sure you want to upload excel valid data</value>
  </data>
  <data name="AreYousureyouwanttologout" xml:space="preserve">
    <value>Are you sure you want to Logout ?</value>
  </data>
  <data name="AreYousureyouwanttoOvercopy" xml:space="preserve">
    <value>Are you sure you want to copy</value>
  </data>
  <data name="AreYousureyouwanttoOverwrite" xml:space="preserve">
    <value>Are you sure you want to overwrite</value>
  </data>
  <data name="AreYouSureYouWantToRemove" xml:space="preserve">
    <value>Are you sure you want to remove from cart?</value>
  </data>
  <data name="AreYousureyouwanttoreplace" xml:space="preserve">
    <value>Are you sure you want to replace?</value>
  </data>
  <data name="AreYouSureYouWantToReset" xml:space="preserve">
    <value>Are you sure you want to reset?</value>
  </data>
  <data name="AssemblyNo" xml:space="preserve">
    <value>Assembly #</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="BrandInformationissavedsuccessfully" xml:space="preserve">
    <value>Brand Information is saved successfully</value>
  </data>
  <data name="BuildTree" xml:space="preserve">
    <value>Build Tree</value>
  </data>
  <data name="CannotBuildTreePlsAddMissingMainSubToplevel" xml:space="preserve">
    <value>Please add Missing Main and Sub Sections to Top Level Master</value>
  </data>
  <data name="CannotpublishCheckerrorreportformoreinfo" xml:space="preserve">
    <value>Cannot publish. Check error report for more info</value>
  </data>
  <data name="CatalogueDeletedSuccessfully" xml:space="preserve">
    <value>Deleted Successfully..!</value>
  </data>
  <data name="CatPreview" xml:space="preserve">
    <value>Catalogue Preview</value>
  </data>
  <data name="CheckConnection" xml:space="preserve">
    <value>Check Connection.. Unable to Connect</value>
  </data>
  <data name="CheckConnectionUnabletoConnect" xml:space="preserve">
    <value>Check Connection..Unable to Connect</value>
  </data>
  <data name="Common_Notes" xml:space="preserve">
    <value>Common Notes</value>
  </data>
  <data name="ConfirmOrderDel" xml:space="preserve">
    <value>Please Confirm to Delete from Database Order # - </value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="CurrentVIN#" xml:space="preserve">
    <value>Current VIN#</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerAdd" xml:space="preserve">
    <value>Customer - Add </value>
  </data>
  <data name="CustomerPrint" xml:space="preserve">
    <value>CUSTOMER #</value>
  </data>
  <data name="CustomerSearch" xml:space="preserve">
    <value>Customer Search</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DBAssemblyRemarks" xml:space="preserve">
    <value>Assembly - Remove</value>
  </data>
  <data name="DBConfigurationnotsetPleaseContactAdmin" xml:space="preserve">
    <value>DB Configuration not set. Please Contact Admin</value>
  </data>
  <data name="DBMainSec" xml:space="preserve">
    <value>Database Main Section</value>
  </data>
  <data name="DBMainSectionRemarks" xml:space="preserve">
    <value>Main Section - Remove</value>
  </data>
  <data name="DBORderNo" xml:space="preserve">
    <value>Database Order #</value>
  </data>
  <data name="DBPart_Assembly_No" xml:space="preserve">
    <value>Database Assembly/Section #</value>
  </data>
  <data name="DBSubSec" xml:space="preserve">
    <value>Database Sub Section</value>
  </data>
  <data name="DBSubSectionRemarks" xml:space="preserve">
    <value>Sub Section - Remove</value>
  </data>
  <data name="DelOrder" xml:space="preserve">
    <value>Delete Order</value>
  </data>
  <data name="Dependancyfoundinactiveit" xml:space="preserve">
    <value>Dependency Found..  Request you to do In-Active it, since it cannot be deleted.</value>
  </data>
  <data name="DependencyFoundandrecordsalreadybeenInactivated" xml:space="preserve">
    <value>Dependency Found, and records already been Inactivated</value>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>Dependency found cannot delete the records</value>
  </data>
  <data name="DependencyFoundRequestyoutoIn-Activeitsinceitcannotbedeleted" xml:space="preserve">
    <value>Dependency Found.Request you to In-Active it, since it cannot be deleted</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Dev.</value>
  </data>
  <data name="DownloadPDF" xml:space="preserve">
    <value>Download PDF</value>
  </data>
  <data name="Doyouwanttoaddrecommendedpartsalongwithselectedpart" xml:space="preserve">
    <value>Do you want to add recommended parts along with the selected part</value>
  </data>
  <data name="DoYouWanttoPublishTheOrder" xml:space="preserve">
    <value>Do You Want To Publish The Order ?</value>
  </data>
  <data name="Doyouwanttoremoverecommendedpartalong" xml:space="preserve">
    <value>Do you want to remove recommended parts along with the selected part</value>
  </data>
  <data name="EditMode" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="EditOptions" xml:space="preserve">
    <value>Edit Options</value>
  </data>
  <data name="EndWith" xml:space="preserve">
    <value>End With</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="EnterCustomer" xml:space="preserve">
    <value>Enter Customer #</value>
  </data>
  <data name="EnterCustomerName" xml:space="preserve">
    <value>Enter Customer Name</value>
  </data>
  <data name="Entervaliddata" xml:space="preserve">
    <value>Enter valid data</value>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="FileAssemblyRemarks" xml:space="preserve">
    <value>Varnet  Assembly - Include</value>
  </data>
  <data name="FileMainSectionRemarks" xml:space="preserve">
    <value>Varnet Main Section - Include</value>
  </data>
  <data name="Filesizeislargerthan 5MB!" xml:space="preserve">
    <value>File size is larger than 5MB!</value>
  </data>
  <data name="FileSubSectionRemarks" xml:space="preserve">
    <value>Varnet Sub Section - Include</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="FunctionGroupCode" xml:space="preserve">
    <value>Function Group Code</value>
  </data>
  <data name="FunctionGroupCodeAlreadyPresent" xml:space="preserve">
    <value>Given Function Group Code is already available in function group details</value>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>Function Group Name</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="GreaterThanorEqualto" xml:space="preserve">
    <value>Greater Than or Equal to</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="ImportedDateTime" xml:space="preserve">
    <value>Imported Date</value>
  </data>
  <data name="ImportIgnoreOptions" xml:space="preserve">
    <value>Import Ignore Options</value>
  </data>
  <data name="ImportVarNetFile" xml:space="preserve">
    <value>Import Varnet File</value>
  </data>
  <data name="Import_Status" xml:space="preserve">
    <value>Import Status</value>
  </data>
  <data name="IncludeServiceManual" xml:space="preserve">
    <value>Service Manual ?</value>
  </data>
  <data name="Informationisnotsaved" xml:space="preserve">
    <value>Information is not saved</value>
  </data>
  <data name="InsertedSuccesfully" xml:space="preserve">
    <value>Added successfully..!!</value>
  </data>
  <data name="InvalidCustomer" xml:space="preserve">
    <value>Invalid Customer</value>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>Invalid Date</value>
  </data>
  <data name="Invalidemailid" xml:space="preserve">
    <value>Invalid Email ID</value>
  </data>
  <data name="InvalidLoginID" xml:space="preserve">
    <value>Invalid Login ID</value>
  </data>
  <data name="InvalidOrderNumber" xml:space="preserve">
    <value>Invalid Order Number</value>
  </data>
  <data name="InvalidUserID/Password" xml:space="preserve">
    <value>Invalid UserID / Password</value>
  </data>
  <data name="IsEmpty" xml:space="preserve">
    <value>Is Empty</value>
  </data>
  <data name="IsEqual" xml:space="preserve">
    <value>Is Equal</value>
  </data>
  <data name="IsNull" xml:space="preserve">
    <value>Is Null</value>
  </data>
  <data name="Is_PartSection" xml:space="preserve">
    <value>Is Part Sec.?</value>
  </data>
  <data name="Is_Section" xml:space="preserve">
    <value>Is Main Section?</value>
  </data>
  <data name="Is_ServiceSection" xml:space="preserve">
    <value>Is Ser Sec.?</value>
  </data>
  <data name="Is_TopSection" xml:space="preserve">
    <value>Is Top Level?</value>
  </data>
  <data name="ITEM" xml:space="preserve">
    <value>#ITEM</value>
  </data>
  <data name="ItemNo" xml:space="preserve">
    <value>Item#</value>
  </data>
  <data name="ItemQty" xml:space="preserve">
    <value>Item Qty.</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="LessthanorEqualto" xml:space="preserve">
    <value>Less than or Equal to</value>
  </data>
  <data name="Like" xml:space="preserve">
    <value>Like</value>
  </data>
  <data name="LOCALISATION" xml:space="preserve">
    <value>LOCALISATION</value>
  </data>
  <data name="MainSectionMissingTopLevelMaster" xml:space="preserve">
    <value>Main Section Missing - TopLevelMaster</value>
  </data>
  <data name="Matching_OP" xml:space="preserve">
    <value>Matching Option Codes</value>
  </data>
  <data name="MaximumZoomed-In" xml:space="preserve">
    <value>Maximum Zoomed-In</value>
  </data>
  <data name="MaximumZoomed-Out" xml:space="preserve">
    <value>Maximum Zoomed-Out</value>
  </data>
  <data name="MFG Code" xml:space="preserve">
    <value>MFG Code</value>
  </data>
  <data name="MFGAssociation" xml:space="preserve">
    <value>MFG Association</value>
  </data>
  <data name="MFGCode" xml:space="preserve">
    <value>MFGCode</value>
  </data>
  <data name="MFGCodeAlreadyPresent" xml:space="preserve">
    <value>Given MFG Code is already available in function group details</value>
  </data>
  <data name="MFG_Code" xml:space="preserve">
    <value>MFG Code</value>
  </data>
  <data name="ModelAdd" xml:space="preserve">
    <value>Model Add</value>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>Model Search</value>
  </data>
  <data name="MsgAllowOnlyAlphabetsNumbers" xml:space="preserve">
    <value>Allows Alphabets Numbers . and ( only</value>
  </data>
  <data name="MsgAssemblyAssociationNotFound" xml:space="preserve">
    <value>Associated Assemblies Not Found !!</value>
  </data>
  <data name="MsgBeforeSaveConditionPleaseAddCondition" xml:space="preserve">
    <value>Before save ,please add a condition</value>
  </data>
  <data name="MsgCannotcopypartdetailstoitselfpartNo" xml:space="preserve">
    <value>Cannot copy Part/Assembly # details to same Part/Assembly #</value>
  </data>
  <data name="MsgCopiedsuccessfully" xml:space="preserve">
    <value>Copied successfully !</value>
  </data>
  <data name="MsgCustomerNotFound" xml:space="preserve">
    <value>Customer not found</value>
  </data>
  <data name="MsgCustomerPartAddedSuccessfull" xml:space="preserve">
    <value>Customer Part Added Successfully</value>
  </data>
  <data name="MsgCustomerPartUpdatedSuccessfull" xml:space="preserve">
    <value>Customer Part Updated Successfully</value>
  </data>
  <data name="MsgDataNotPresent" xml:space="preserve">
    <value>Data not found</value>
  </data>
  <data name="MsgDuplicateEntry" xml:space="preserve">
    <value>Duplicate Entry</value>
  </data>
  <data name="MsgDuplicateEntryForAbbrevation" xml:space="preserve">
    <value>Duplicate Entry For Abbreviation</value>
  </data>
  <data name="MsgDuplicateEntryForCartName" xml:space="preserve">
    <value>Duplicate Entry For Cart Name</value>
  </data>
  <data name="MsgDuplicateEntryForCustomerCode" xml:space="preserve">
    <value>Duplicate Entry For Customer Code</value>
  </data>
  <data name="MsgDuplicateEntryForCustomerName" xml:space="preserve">
    <value>Duplicate Entry For Customer Name</value>
  </data>
  <data name="MsgDuplicateEntryForCustomerPharmaCode" xml:space="preserve">
    <value>Duplicate Entry for Parma Code</value>
  </data>
  <data name="MsgDuplicateEntryForMainSectionCode" xml:space="preserve">
    <value>Duplicate Entry For Main Section</value>
  </data>
  <data name="MsgDuplicateEntryForMaskingActionCode" xml:space="preserve">
    <value>Duplicate Entry For Masking Action Code</value>
  </data>
  <data name="MsgDuplicateEntryForMaskingActionDescription" xml:space="preserve">
    <value>Duplicate Entry For Masking Action Description</value>
  </data>
  <data name="MsgDuplicateEntryForOptionCode" xml:space="preserve">
    <value>Duplicate Entry For Option Code</value>
  </data>
  <data name="MsgDuplicateEntryForOptionDescription" xml:space="preserve">
    <value>Duplicate Entry For Option Description</value>
  </data>
  <data name="MsgDuplicateEntryForSectionCode" xml:space="preserve">
    <value>Duplicate Entry For Section</value>
  </data>
  <data name="MsgDuplicateEntryForSubSectionCode" xml:space="preserve">
    <value>Duplicate entry for Subsection Code</value>
  </data>
  <data name="MsgDuplicateEntryForSupersessionRemarks" xml:space="preserve">
    <value>Duplicate Entry For Supersession Remarks</value>
  </data>
  <data name="MsgDuplicateEntryForUserEmployeeCode" xml:space="preserve">
    <value>Duplicate Entry For User Employee Code</value>
  </data>
  <data name="MsgDuplicateEntryForUserLoginID" xml:space="preserve">
    <value>Duplicate Entry For User Login ID</value>
  </data>
  <data name="MsgDuplicateEntryForUserRole" xml:space="preserve">
    <value>Duplicate Entry For User Role</value>
  </data>
  <data name="MsgDuplicateEntryForVendorCode" xml:space="preserve">
    <value>Duplicate Entry For Vendor Code</value>
  </data>
  <data name="MsgDuplicateEntryForVendorName" xml:space="preserve">
    <value>Duplicate Entry For Vendor Name</value>
  </data>
  <data name="MsgDuplicateEntryForVendorParamCode" xml:space="preserve">
    <value>Duplicate Entry For Vendor Param Code</value>
  </data>
  <data name="MsgDuplicateEntryForVINNumber" xml:space="preserve">
    <value>For This VIN Number Road Number Already Changed</value>
  </data>
  <data name="MsgEmailidisnotregistered" xml:space="preserve">
    <value>Email id is not registered</value>
  </data>
  <data name="MsgEmailsentsuccessfully" xml:space="preserve">
    <value>Email sent successfully</value>
  </data>
  <data name="MsgEnteredLevelisNotValid" xml:space="preserve">
    <value>Entered Level is Not Valid</value>
  </data>
  <data name="MsgEnterEmailIDNotValid" xml:space="preserve">
    <value>Entered Email ID is not valid</value>
  </data>
  <data name="MsgEnterMobileNumberNotValid" xml:space="preserve">
    <value>Enter Mobile Number is not valid</value>
  </data>
  <data name="MsgEnterNumberisNotValid" xml:space="preserve">
    <value>Enter Number is Not Valid</value>
  </data>
  <data name="MsgEnterOrderQtyisNotValid" xml:space="preserve">
    <value>Entered Order Qty is Not Valid</value>
  </data>
  <data name="MsgEntervaliddatewithvalidformate" xml:space="preserve">
    <value>Enter valid date with format dd-MMM-yyyy</value>
  </data>
  <data name="MsgError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MsgFaildToAdd" xml:space="preserve">
    <value>Failed To Add</value>
  </data>
  <data name="MsgFaildToAdd1" xml:space="preserve">
    <value>Failed To Add</value>
  </data>
  <data name="MsgFaildToDelete" xml:space="preserve">
    <value>Failed To Delete</value>
  </data>
  <data name="MsgFaildToDelete1" xml:space="preserve">
    <value>Failed To Delete</value>
  </data>
  <data name="MsgFaildToUpdate" xml:space="preserve">
    <value>Failed To Update</value>
  </data>
  <data name="MsgFaildToUpdate1" xml:space="preserve">
    <value>Failed To Update</value>
  </data>
  <data name="MsgFailedToPublish" xml:space="preserve">
    <value>Failed to publish</value>
  </data>
  <data name="MsgFailedToUpdate" xml:space="preserve">
    <value>Failed To Update</value>
  </data>
  <data name="MsgFalse" xml:space="preserve">
    <value>FALSE</value>
  </data>
  <data name="MsgFunctionGroupDeleteSuccessfull" xml:space="preserve">
    <value>Function Group Used Deleted Successfully</value>
  </data>
  <data name="MsgFunctionGroupUsedAddedSuccessfull" xml:space="preserve">
    <value>Function Group Used Added Successfully</value>
  </data>
  <data name="MsgFunctionGroupUsedUpdatedSuccessfull" xml:space="preserve">
    <value>Function Group Used Updated Successfully</value>
  </data>
  <data name="MsgHasLockedThisAssemblyForModification" xml:space="preserve">
    <value>has locked this Assembly for modification</value>
  </data>
  <data name="MsgHighlightedFieldsAreMandatory" xml:space="preserve">
    <value>Highlighted fields are mandatory</value>
  </data>
  <data name="MsgIncorrectPwd" xml:space="preserve">
    <value>Entered Incorrect Password</value>
  </data>
  <data name="MsgInformationalreadyavailable" xml:space="preserve">
    <value>Information already available</value>
  </data>
  <data name="MsgInvalidCurrentRoadNumber" xml:space="preserve">
    <value>Invalid Current Road Number</value>
  </data>
  <data name="MsgInvalidCustomerName" xml:space="preserve">
    <value>Invalid Customer Name</value>
  </data>
  <data name="MsgInvalidModelName" xml:space="preserve">
    <value>Invalid Model Name</value>
  </data>
  <data name="MsgInvalidOrderNumber" xml:space="preserve">
    <value>Invalid Order Number</value>
  </data>
  <data name="MsgInvalidPartNumber" xml:space="preserve">
    <value>Invalid Part Number</value>
  </data>
  <data name="MsgInvalidProductTypeName" xml:space="preserve">
    <value>Invalid Product Type Name</value>
  </data>
  <data name="MsgInvalidVINNumber" xml:space="preserve">
    <value>Invalid VIN Number</value>
  </data>
  <data name="MsgMainSectionNotFound" xml:space="preserve">
    <value>No Main Section Found</value>
  </data>
  <data name="MsgMFGAddedSuccessfull" xml:space="preserve">
    <value> Added successfully..!!</value>
  </data>
  <data name="MsgMFGAddedSuccessfull1" xml:space="preserve">
    <value> Added successfully..!!</value>
  </data>
  <data name="MsgMFGUpdatedSuccessfull" xml:space="preserve">
    <value> Updated Successfully...!</value>
  </data>
  <data name="MsgMFGUpdatedSuccessfull1" xml:space="preserve">
    <value> Updated Successfully...!</value>
  </data>
  <data name="MsgNewPasswordcantbesameasPrevious" xml:space="preserve">
    <value>New Password cant be same as Previous</value>
  </data>
  <data name="MsgNodataFound" xml:space="preserve">
    <value>No data Found</value>
  </data>
  <data name="MsgNoInformationFound" xml:space="preserve">
    <value>No Information Found</value>
  </data>
  <data name="MsgNoLocaleDetailsFound" xml:space="preserve">
    <value>No Locale Details Found</value>
  </data>
  <data name="MsgNoOneFound" xml:space="preserve">
    <value>No One's Found</value>
  </data>
  <data name="MsgNoPartInCartToExport" xml:space="preserve">
    <value>No part in cart to export</value>
  </data>
  <data name="MsgNoPartToRequestQuote" xml:space="preserve">
    <value>Please select part to request for quote</value>
  </data>
  <data name="MsgNoRangeFound" xml:space="preserve">
    <value>No Range Found</value>
  </data>
  <data name="MsgNoRecentsFound" xml:space="preserve">
    <value>No Recents Found</value>
  </data>
  <data name="MsgNoRecord" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="MsgNoRecordsToUpdate" xml:space="preserve">
    <value>No Records to Update</value>
  </data>
  <data name="MsgNoSection" xml:space="preserve">
    <value>No Section Found</value>
  </data>
  <data name="MsgNoSubSection" xml:space="preserve">
    <value>No Subsection found</value>
  </data>
  <data name="MsgNoSuchDataFound" xml:space="preserve">
    <value>No such data found</value>
  </data>
  <data name="MsgNoUserIsAssociated" xml:space="preserve">
    <value>No user is associated with customer</value>
  </data>
  <data name="MsgOrder#NotFound" xml:space="preserve">
    <value>Order # not found</value>
  </data>
  <data name="MsgOrderNumberMandatory" xml:space="preserve">
    <value>Order # Mandatory</value>
  </data>
  <data name="MsgPartsmergedwithselectedcart" xml:space="preserve">
    <value>Merged with selected cart</value>
  </data>
  <data name="MsgPasswordChangedSuccesfully" xml:space="preserve">
    <value>Password Changed Successfully</value>
  </data>
  <data name="MsgPleaseselectatlest2cataloguebeforecompare" xml:space="preserve">
    <value>Please select at least 2 catalogue order # before compare</value>
  </data>
  <data name="MsgPleaseselectCarttogetAttachmentinMail" xml:space="preserve">
    <value>Please select Cart to get Attachment in Mail</value>
  </data>
  <data name="MsgPleaseselectfiletype" xml:space="preserve">
    <value>Please select file type</value>
  </data>
  <data name="MsgPleaseSelectMFGCode" xml:space="preserve">
    <value>Please Select MFG Code</value>
  </data>
  <data name="MsgPleaseSelectModelPartCatalogue" xml:space="preserve">
    <value>Select Part Number,Catalogue,Model</value>
  </data>
  <data name="MsgPleasewait" xml:space="preserve">
    <value>Please wait</value>
  </data>
  <data name="MsgPleaseWaitWhilewereadHotSpots" xml:space="preserve">
    <value>Please Wait ,While We Read And Draw Hotspots</value>
  </data>
  <data name="MsgPlsSelectBrand" xml:space="preserve">
    <value>Please select the brand.</value>
  </data>
  <data name="MsgPlsSelectforToc" xml:space="preserve">
    <value>Please Select Road#,VIN#,Order#,Model , Customer Name</value>
  </data>
  <data name="MsgPublishedAssembly" xml:space="preserve">
    <value>Published successfully</value>
  </data>
  <data name="MsgPwdDidntmatched" xml:space="preserve">
    <value>Password did not match</value>
  </data>
  <data name="MsgRecomAddedSuccessfull" xml:space="preserve">
    <value>Recommended Part Added Successfully</value>
  </data>
  <data name="MsgRecomDeleteSuccessfull" xml:space="preserve">
    <value>Recommended Part Deleted Successfully</value>
  </data>
  <data name="MsgRecomUpdateSuccessfull" xml:space="preserve">
    <value>Recommended Part Updated Successfully</value>
  </data>
  <data name="MsgRecordsDeleteSuccessfull" xml:space="preserve">
    <value>Deleted Successfully..! </value>
  </data>
  <data name="MsgRecordsDeleteSuccessfull1" xml:space="preserve">
    <value>Deleted Successfully..! </value>
  </data>
  <data name="MsgRecordsHaveAlreadybeenInserted" xml:space="preserve">
    <value>Records have already been Inserted</value>
  </data>
  <data name="MsgRecordsHaveBeenDeleted" xml:space="preserve">
    <value>Deleted Successfully..!</value>
  </data>
  <data name="MsgRecordsHaveBeenImoprted" xml:space="preserve">
    <value>Imported successfully..!!</value>
  </data>
  <data name="MsgRecordsHaveBeenImported" xml:space="preserve">
    <value>Imported Successfully...!</value>
  </data>
  <data name="MsgRecordsHaveBeenInserted" xml:space="preserve">
    <value>Added successfully..!!</value>
  </data>
  <data name="MsgRecordsHaveBeenUpdated" xml:space="preserve">
    <value>Updated Successfully...!</value>
  </data>
  <data name="MsgReplacecompletedsuccessfully" xml:space="preserve">
    <value>Replace completed successfully</value>
  </data>
  <data name="MsgResErrorinUploadedExcelFile" xml:space="preserve">
    <value>Error in uploading Excel file</value>
  </data>
  <data name="MsgRoad#NotFound" xml:space="preserve">
    <value>Road # not found</value>
  </data>
  <data name="MsgRoleNameNotPresent" xml:space="preserve">
    <value>Role Name not found</value>
  </data>
  <data name="MsgSameVersionOfAssemblyCannotcompare" xml:space="preserve">
    <value>Same Version of Assembly cannot compare</value>
  </data>
  <data name="MsgSectionNotFound" xml:space="preserve">
    <value>Section Not Found</value>
  </data>
  <data name="MsgSelectCustomer" xml:space="preserve">
    <value>Select Customer</value>
  </data>
  <data name="MsgSelectedParthavingalreadypartdetails" xml:space="preserve">
    <value>Selected Part having already part details</value>
  </data>
  <data name="MsgSelectedPartNoDoesnotcontainBompartdetails" xml:space="preserve">
    <value>Selected Part # does not contain BOM Part details</value>
  </data>
  <data name="MsgSelectOrderNumber" xml:space="preserve">
    <value>Select Catalogue,Model,ProductType,Brand</value>
  </data>
  <data name="MsgSelectPartNumber" xml:space="preserve">
    <value>Select Part Number</value>
  </data>
  <data name="MsgSelectRoadNumber" xml:space="preserve">
    <value>Select Road Number</value>
  </data>
  <data name="MsgSelectVINNumber" xml:space="preserve">
    <value>Select VIN Number</value>
  </data>
  <data name="MsgSentSuccessfully" xml:space="preserve">
    <value>Sent successfully</value>
  </data>
  <data name="MsgSetAddedSuccessfull" xml:space="preserve">
    <value>Set Part Added Successfully</value>
  </data>
  <data name="MsgSetDeleteSuccessfull" xml:space="preserve">
    <value>Set Part Deleted Successfully</value>
  </data>
  <data name="MsgSetUpdateSuccessfull" xml:space="preserve">
    <value>Set Part Updated Successfully</value>
  </data>
  <data name="MsgShiptoaddressselect" xml:space="preserve">
    <value>Select ship to address</value>
  </data>
  <data name="MsgSomethingWentWrongPleasetryAgain" xml:space="preserve">
    <value>Something Went Wrong Please try Again</value>
  </data>
  <data name="MsgSubSectionNotFound" xml:space="preserve">
    <value>Sub Section Not Found</value>
  </data>
  <data name="MsgSuccess" xml:space="preserve">
    <value>Valid Brand</value>
  </data>
  <data name="MsgSupersessiondatanotfound" xml:space="preserve">
    <value>Supersession Not Found</value>
  </data>
  <data name="MsgThereisnopartdetailsareselectedtocopy" xml:space="preserve">
    <value>There is no part details are selected to copy</value>
  </data>
  <data name="MsgTrue" xml:space="preserve">
    <value>TRUE</value>
  </data>
  <data name="MsgVendorAddedSuccessfull" xml:space="preserve">
    <value>Vendor Part Info Added Successfully</value>
  </data>
  <data name="MsgVendorDeleteSuccessfull" xml:space="preserve">
    <value>Vendor Part Info Deleted Successfully</value>
  </data>
  <data name="MsgVendorUpdatedSuccessfull" xml:space="preserve">
    <value>Vendor Part Info Updated Successfully</value>
  </data>
  <data name="MsgVIN#NotFound" xml:space="preserve">
    <value>VIN # not found</value>
  </data>
  <data name="MultipleFileUploadMsg" xml:space="preserve">
    <value>Multiple Files Selected, Click Yes to Build Tree and Generate Logs</value>
  </data>
  <data name="MultipOptionCodes" xml:space="preserve">
    <value>Multiple Option Codes</value>
  </data>
  <data name="NameCustEn" xml:space="preserve">
    <value>Customer Name - English</value>
  </data>
  <data name="NameCustFn" xml:space="preserve">
    <value>Customer Name - French</value>
  </data>
  <data name="NewOldNoChange" xml:space="preserve">
    <value>New/Existing/No Change - Order</value>
  </data>
  <data name="NewOrderVarnetMsg" xml:space="preserve">
    <value>Its a New Order - Continue to Generate Work-Log and Build Tree</value>
  </data>
  <data name="NewPasswordissenttoyouremail" xml:space="preserve">
    <value>New password is sent to your email id</value>
  </data>
  <data name="NewPasswordWillbesenttoemaildoyouwanttocontinue" xml:space="preserve">
    <value>New password will be sent to your email id, do you want to continue?</value>
  </data>
  <data name="NewVIN#" xml:space="preserve">
    <value>New VIN#</value>
  </data>
  <data name="Next Release information" xml:space="preserve">
    <value>Application Announcements</value>
  </data>
  <data name="NextReleaseinformation" xml:space="preserve">
    <value>Application announcements</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="NoAssembliesFound" xml:space="preserve">
    <value>No Assemblies Found</value>
  </data>
  <data name="NoBrandisAssociated" xml:space="preserve">
    <value>No Brand is Associated</value>
  </data>
  <data name="NoBrandisAssociated.PleaseSelectbrand" xml:space="preserve">
    <value>No Brand is Associated. Please Select brand</value>
  </data>
  <data name="NoCatalogueisAssociated" xml:space="preserve">
    <value>No Catalogue is Associated</value>
  </data>
  <data name="NoChangesFoundVarnetFileDB" xml:space="preserve">
    <value>Compare Result - No Changes Found between Varnet File and Database</value>
  </data>
  <data name="NoChildAssembliesFound" xml:space="preserve">
    <value>No Child Assemblies Found</value>
  </data>
  <data name="NoLogFound" xml:space="preserve">
    <value>No Log Found</value>
  </data>
  <data name="NoParentAssembliesFound" xml:space="preserve">
    <value>No Parent Assemblies Found</value>
  </data>
  <data name="NoProductTypeisAssociated" xml:space="preserve">
    <value>No Product Type is Associated</value>
  </data>
  <data name="NoRef" xml:space="preserve">
    <value>No.Ref.</value>
  </data>
  <data name="NoSectionisAssociatedtothisVIN" xml:space="preserve">
    <value>No Section is Associated to this VIN</value>
  </data>
  <data name="NotContains" xml:space="preserve">
    <value>Not Contains</value>
  </data>
  <data name="NotEmpty" xml:space="preserve">
    <value>Not Empty</value>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>Not Equal</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NotinHotspotCopyMode" xml:space="preserve">
    <value>Not in Hotspot copy mode</value>
  </data>
  <data name="NotinHotspotMoveMode" xml:space="preserve">
    <value>Not in Hotspot move mode</value>
  </data>
  <data name="NotNull" xml:space="preserve">
    <value>Not Null</value>
  </data>
  <data name="NotusedinanyAssembly" xml:space="preserve">
    <value>Not used in any Assembly</value>
  </data>
  <data name="NOVABUS" xml:space="preserve">
    <value>#NOVABUS</value>
  </data>
  <data name="NovaOrderList" xml:space="preserve">
    <value>Order/Top Level List</value>
  </data>
  <data name="NoVehicleisAssociated" xml:space="preserve">
    <value>No Vehicle is Associated</value>
  </data>
  <data name="NoVersionsCreated" xml:space="preserve">
    <value>No Versions Created</value>
  </data>
  <data name="NUMERICALINDEX" xml:space="preserve">
    <value>NUMERICALINDEX</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="OnlyAlphabetsandCharactersareAllowedSpecialCharactersarenotAllowed" xml:space="preserve">
    <value>Only Alphabets and Characters are Allowed..Special Characters are not Allowed</value>
  </data>
  <data name="OptionsAdd" xml:space="preserve">
    <value>Options - Add</value>
  </data>
  <data name="Option_NotFound" xml:space="preserve">
    <value>Options Code not Found</value>
  </data>
  <data name="OrderBook" xml:space="preserve">
    <value>Order Book</value>
  </data>
  <data name="OrderCompareList" xml:space="preserve">
    <value>Order Compare List</value>
  </data>
  <data name="OrderDeletedSucess" xml:space="preserve">
    <value>Order Deleted Successfully</value>
  </data>
  <data name="OrderExistsandPublished" xml:space="preserve">
    <value>Order # Already Exists in Database and its Published , Click "YES" To Delete Order and Re-Build Tree/ Click "NO" View Compare List. </value>
  </data>
  <data name="OrderExistsNotPublished" xml:space="preserve">
    <value>Order #  Already Exists in Database and its Not Published, Click "YES" To Delete Order and Re-Build Tree/ Click "NO" View Compare List.</value>
  </data>
  <data name="OrderImportinProgress" xml:space="preserve">
    <value>Selected Order - Import is already in Work in Progress by User Name -</value>
  </data>
  <data name="ordernoalreadypresent" xml:space="preserve">
    <value>Given Order# is already available in vehicle details</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>ORDER #</value>
  </data>
  <data name="OrderNumberMismatchinFile" xml:space="preserve">
    <value>Order # Mismatch - File Content and File Name</value>
  </data>
  <data name="OrderNumberVal" xml:space="preserve">
    <value>ORDER #</value>
  </data>
  <data name="OrderPublished" xml:space="preserve">
    <value>Order Published Successfully</value>
  </data>
  <data name="OrderSearch" xml:space="preserve">
    <value>Order# Search</value>
  </data>
  <data name="PAGETITLE" xml:space="preserve">
    <value>PAGE TITLE</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Part</value>
  </data>
  <data name="Part Assembly No" xml:space="preserve">
    <value>Part Assembly No</value>
  </data>
  <data name="Part Description in English" xml:space="preserve">
    <value>Part Description in English</value>
  </data>
  <data name="Part Description in French" xml:space="preserve">
    <value>Part Description in French</value>
  </data>
  <data name="Part Number" xml:space="preserve">
    <value>Part Number</value>
  </data>
  <data name="PartDesc" xml:space="preserve">
    <value>Part Desc.</value>
  </data>
  <data name="PartDescription" xml:space="preserve">
    <value>Part Description</value>
  </data>
  <data name="PartNumber" xml:space="preserve">
    <value>Part Number</value>
  </data>
  <data name="PartRemovedFromCart" xml:space="preserve">
    <value>Part Removed From Cart</value>
  </data>
  <data name="PartsBookFor" xml:space="preserve">
    <value>Parts Book For</value>
  </data>
  <data name="PartsBookPDFPrintinginProgress" xml:space="preserve">
    <value>Please Click Yes to Start Parts Book PDF Printing</value>
  </data>
  <data name="PartsManual" xml:space="preserve">
    <value>PARTS MANUAL</value>
  </data>
  <data name="PartsManualFileName" xml:space="preserve">
    <value>Parts Manual PDF File Name</value>
  </data>
  <data name="Part_Assembly_AlternateNo" xml:space="preserve">
    <value>Alternate Part #</value>
  </data>
  <data name="Part_Assembly_CreatedDate" xml:space="preserve">
    <value>Created Date</value>
  </data>
  <data name="Part_Assembly_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Part_Assembly_HasVersion" xml:space="preserve">
    <value>Has Version?</value>
  </data>
  <data name="Part_Assembly_IsActive" xml:space="preserve">
    <value>Is Active?</value>
  </data>
  <data name="Part_Assembly_IsAssembly" xml:space="preserve">
    <value>Is Assembly?</value>
  </data>
  <data name="Part_Assembly_IsLock" xml:space="preserve">
    <value>Is Lock?</value>
  </data>
  <data name="Part_Assembly_IsPurchasable" xml:space="preserve">
    <value>Is Purchasable?</value>
  </data>
  <data name="Part_Assembly_IsRecommendedPart" xml:space="preserve">
    <value>Has Recom.?</value>
  </data>
  <data name="Part_Assembly_IsReferred" xml:space="preserve">
    <value>Is Referred?</value>
  </data>
  <data name="Part_Assembly_IsSetMember" xml:space="preserve">
    <value>Has Set Mem.?</value>
  </data>
  <data name="Part_Assembly_IsSuperseeded" xml:space="preserve">
    <value>Is Superseded?</value>
  </data>
  <data name="Part_Assembly_IsSuperseeding" xml:space="preserve">
    <value>Is Superseding?</value>
  </data>
  <data name="Part_Assembly_IsSupersession" xml:space="preserve">
    <value>Is Supersession?</value>
  </data>
  <data name="Part_Assembly_IsVisible" xml:space="preserve">
    <value>Is Visible?</value>
  </data>
  <data name="Part_Assembly_ModifiedDate" xml:space="preserve">
    <value>Modified Date</value>
  </data>
  <data name="Part_Assembly_No" xml:space="preserve">
    <value>Part/Assembly#</value>
  </data>
  <data name="Part_Assembly_ShowVendorPartToCustomer" xml:space="preserve">
    <value>Show Vendor Part to Customer?</value>
  </data>
  <data name="Part_Assembly_Specification" xml:space="preserve">
    <value>Specification</value>
  </data>
  <data name="PCYANDWIWIP" xml:space="preserve">
    <value>Please Click Yes to Build Tree and Generate Work Log</value>
  </data>
  <data name="PDFPRINTLOG" xml:space="preserve">
    <value>PDF Print Log</value>
  </data>
  <data name="PleaseChoosethebrand" xml:space="preserve">
    <value>Please choose the brand</value>
  </data>
  <data name="Pleasecloseallwindowsopenedwithpreviousbrand" xml:space="preserve">
    <value>Please close all windows opened with previous brand</value>
  </data>
  <data name="pleaseenteremailid" xml:space="preserve">
    <value>Please Provide register Email ID</value>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>Please Enter Login ID</value>
  </data>
  <data name="Pleaseentervalidemailid" xml:space="preserve">
    <value>Please enter valid Email Id</value>
  </data>
  <data name="Pleasefillallfields" xml:space="preserve">
    <value>Please fill all fields</value>
  </data>
  <data name="Pleaseselectcheckbox" xml:space="preserve">
    <value>Please select check box</value>
  </data>
  <data name="PleaseSelectCustomerName" xml:space="preserve">
    <value>Please Select Customer Name</value>
  </data>
  <data name="PleaseselectEffectiveFromdateFirst" xml:space="preserve">
    <value>Please select Effective From date First</value>
  </data>
  <data name="Pleaseselectfiletype" xml:space="preserve">
    <value>Please select file type</value>
  </data>
  <data name="PleaseSelectFonttoRunOCR" xml:space="preserve">
    <value>Please Select Font to Run OCR</value>
  </data>
  <data name="PleaseselectMaskingCodefirst" xml:space="preserve">
    <value>Please select Masking code first</value>
  </data>
  <data name="PNI" xml:space="preserve">
    <value>Numeric Index ?</value>
  </data>
  <data name="PrintCompleted" xml:space="preserve">
    <value>PDF Print Completed</value>
  </data>
  <data name="PrintedBy" xml:space="preserve">
    <value>Printed By</value>
  </data>
  <data name="PrintedDate" xml:space="preserve">
    <value>Printed Date</value>
  </data>
  <data name="PrintedStatus" xml:space="preserve">
    <value>Printed Status</value>
  </data>
  <data name="PrintPartsBook" xml:space="preserve">
    <value>Print Parts Book</value>
  </data>
  <data name="ProductTypeCodeduplicatedforSameBrand" xml:space="preserve">
    <value>Product Type Code duplicated for Same Brand</value>
  </data>
  <data name="PWTBFMSSSMAWIP" xml:space="preserve">
    <value>Please Click Yes to Confirm and Please Wait - Tree Build for Missing Section, Sub-Section, Main Assembly &amp; Service Links (Addtion/Removal) - In-Progress</value>
  </data>
  <data name="QTY" xml:space="preserve">
    <value>QTY</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="RecordshavebeenIn-activated" xml:space="preserve">
    <value>Records have been In-activated</value>
  </data>
  <data name="ReEPC" xml:space="preserve">
    <value>Electronic Parts Catalogue</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Release Date</value>
  </data>
  <data name="ReleaseNoteFileName" xml:space="preserve">
    <value>Release Note File Name</value>
  </data>
  <data name="ReleaseNoteInformation" xml:space="preserve">
    <value>Release Note Information</value>
  </data>
  <data name="ReleaseVerion" xml:space="preserve">
    <value>Release Version</value>
  </data>
  <data name="ReleaseVersion" xml:space="preserve">
    <value>Release Version</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>Relogin</value>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>Remarks</value>
  </data>
  <data name="RemoveAllCustomers" xml:space="preserve">
    <value>Remove all customers</value>
  </data>
  <data name="RemovedFromCartSuccessfully" xml:space="preserve">
    <value>Removed From Cart Successfully</value>
  </data>
  <data name="Res*Remarks" xml:space="preserve">
    <value>*Remarks</value>
  </data>
  <data name="ResAbbrevationList" xml:space="preserve">
    <value>Abbreviation List</value>
  </data>
  <data name="ResAbbrevationList1" xml:space="preserve">
    <value>Abbreviation List</value>
  </data>
  <data name="ResAbbreviation" xml:space="preserve">
    <value>View Abbreviation</value>
  </data>
  <data name="ResAbbreviation1" xml:space="preserve">
    <value>Abbreviation</value>
  </data>
  <data name="ResAbbreviationDescription" xml:space="preserve">
    <value>Abbreviation Description</value>
  </data>
  <data name="ResAbbreviationDescription1" xml:space="preserve">
    <value>Abbreviation Description</value>
  </data>
  <data name="ResAbbreviationDescriptionSearchList" xml:space="preserve">
    <value>Abbreviation Description Search List</value>
  </data>
  <data name="ResAbbreviationDescriptionSearchList1" xml:space="preserve">
    <value>Abbreviation Description Search List</value>
  </data>
  <data name="ResAbbreviationDescriptionUpdation" xml:space="preserve">
    <value>Abbreviation Description Updation</value>
  </data>
  <data name="ResAbbreviationDescriptionUpdation1" xml:space="preserve">
    <value>Abbreviation Description Updation</value>
  </data>
  <data name="ResAbbreviationDesUpdation" xml:space="preserve">
    <value>Abbreviation Description Updation</value>
  </data>
  <data name="ResAbbreviationDesUpdation1" xml:space="preserve">
    <value>Abbreviation Description Updation</value>
  </data>
  <data name="ResAbbreviationDetails" xml:space="preserve">
    <value>Abbreviation Details</value>
  </data>
  <data name="ResAbbreviationLanguageList" xml:space="preserve">
    <value>Abbreviation Language List</value>
  </data>
  <data name="ResAbbreviationP" xml:space="preserve">
    <value>ABBREVIATIONS</value>
  </data>
  <data name="ResAbbreviations" xml:space="preserve">
    <value>View Abbreviations</value>
  </data>
  <data name="ResAbbreviations1" xml:space="preserve">
    <value>Abbreviations</value>
  </data>
  <data name="ResAccessRights" xml:space="preserve">
    <value>Access Rights</value>
  </data>
  <data name="ResAction" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="ResActionBy" xml:space="preserve">
    <value>Action By</value>
  </data>
  <data name="ResActionBy1" xml:space="preserve">
    <value>Action By</value>
  </data>
  <data name="ResActionName" xml:space="preserve">
    <value>Action Name</value>
  </data>
  <data name="ResActionType" xml:space="preserve">
    <value>Action Type</value>
  </data>
  <data name="ResActionType1" xml:space="preserve">
    <value>Action Type</value>
  </data>
  <data name="ResActivityName" xml:space="preserve">
    <value>Activity Name</value>
  </data>
  <data name="ResActivityName1" xml:space="preserve">
    <value>Activity Name</value>
  </data>
  <data name="ResAdd" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ResAdd1" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ResAddAbbrevation" xml:space="preserve">
    <value>Add  Abbreviation</value>
  </data>
  <data name="ResAddAbbreviationDescription" xml:space="preserve">
    <value>Add Abbreviation Description</value>
  </data>
  <data name="ResAddAnnotation" xml:space="preserve">
    <value>Add Annotation</value>
  </data>
  <data name="ResAddBlankLine" xml:space="preserve">
    <value>Add Blank Line</value>
  </data>
  <data name="ResAddBOMPart" xml:space="preserve">
    <value>Add BOM Part</value>
  </data>
  <data name="ResAddBookmark" xml:space="preserve">
    <value>Add Bookmark</value>
  </data>
  <data name="ResAddCondition" xml:space="preserve">
    <value>Add Condition</value>
  </data>
  <data name="ResAddCondition1" xml:space="preserve">
    <value>Add Condition</value>
  </data>
  <data name="ResAddCustomer" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="ResAddCustomer1" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="ResAddCustomerPartInfo" xml:space="preserve">
    <value>Add Customer Part Info</value>
  </data>
  <data name="ResAddCustomerPartInfo1" xml:space="preserve">
    <value>Add Customer Part Info</value>
  </data>
  <data name="ResAddDictionary" xml:space="preserve">
    <value>Add Dictionary</value>
  </data>
  <data name="ResAddedSelectedPartandSetMemberSuccessfully" xml:space="preserve">
    <value>Added Selected Part and Set Member(s) Successfully</value>
  </data>
  <data name="ResAddedSuccessfully" xml:space="preserve">
    <value>Added Successfully</value>
  </data>
  <data name="ResAddFilter" xml:space="preserve">
    <value>Add Filter</value>
  </data>
  <data name="ResAddFont" xml:space="preserve">
    <value>Add Font</value>
  </data>
  <data name="ResAddFunctionGroup" xml:space="preserve">
    <value>Add Function Group</value>
  </data>
  <data name="ResAddFunctionGroup1" xml:space="preserve">
    <value>Add Function Group</value>
  </data>
  <data name="ResAddFunctionGroupUsed" xml:space="preserve">
    <value>Add Function Group Used</value>
  </data>
  <data name="ResAddGUIConfig" xml:space="preserve">
    <value>Add GUI Config</value>
  </data>
  <data name="ResAddHighlighter" xml:space="preserve">
    <value>Add Highlighter</value>
  </data>
  <data name="ResAddHiglighter" xml:space="preserve">
    <value>Add Highlighter</value>
  </data>
  <data name="ResAddHotSpot" xml:space="preserve">
    <value>Add Hotspot</value>
  </data>
  <data name="ResAddImage" xml:space="preserve">
    <value>Add Image</value>
  </data>
  <data name="ResAddMaskingAction" xml:space="preserve">
    <value>Add Masking Action</value>
  </data>
  <data name="ResAddNewCustomerPart" xml:space="preserve">
    <value>Add New Customer Part</value>
  </data>
  <data name="ResAddNewMFGCode" xml:space="preserve">
    <value>MFG Code - Add</value>
  </data>
  <data name="ResAddNewMFGCode1" xml:space="preserve">
    <value>Add MFG Code</value>
  </data>
  <data name="ResAddNewModel" xml:space="preserve">
    <value>Add New Modal</value>
  </data>
  <data name="ResAddNewModel1" xml:space="preserve">
    <value>Add New Modal</value>
  </data>
  <data name="ResAddNewOwnership" xml:space="preserve">
    <value>Add New Vehicle Ownership</value>
  </data>
  <data name="ResAddNewVINNumber" xml:space="preserve">
    <value>Add New VIN Number</value>
  </data>
  <data name="ResAddOnlyDescription" xml:space="preserve">
    <value>Add only Description</value>
  </data>
  <data name="ResAddOptions" xml:space="preserve">
    <value>Add Options</value>
  </data>
  <data name="ResAddPartDetails" xml:space="preserve">
    <value>Add Part Details</value>
  </data>
  <data name="ResAddParttoBOM" xml:space="preserve">
    <value>Add Part to BOM</value>
  </data>
  <data name="ResAddParttoBOM1" xml:space="preserve">
    <value>Add Part to BOM</value>
  </data>
  <data name="ResAddRecommendedParts" xml:space="preserve">
    <value>Add Recommended Parts</value>
  </data>
  <data name="ResAddress1" xml:space="preserve">
    <value>Address1</value>
  </data>
  <data name="ResAddress2" xml:space="preserve">
    <value>Address2</value>
  </data>
  <data name="ResAddRoadHistory" xml:space="preserve">
    <value>Add New Road Number</value>
  </data>
  <data name="ResAddSectionItem" xml:space="preserve">
    <value>Add Section Item</value>
  </data>
  <data name="ResAddSectionItem1" xml:space="preserve">
    <value>Add Section Item</value>
  </data>
  <data name="ResAddSetMember" xml:space="preserve">
    <value>Add Set Member</value>
  </data>
  <data name="ResAddSupersedingDetailer" xml:space="preserve">
    <value>Add Superseding Detailer</value>
  </data>
  <data name="ResAddSupersession" xml:space="preserve">
    <value>Add Supersession</value>
  </data>
  <data name="ResAddSupersession1" xml:space="preserve">
    <value>Add Supersession</value>
  </data>
  <data name="ResAddSupersessionDetailer" xml:space="preserve">
    <value>Add Supersession Detailer</value>
  </data>
  <data name="ResAddText" xml:space="preserve">
    <value>Add text</value>
  </data>
  <data name="ResAddToCart" xml:space="preserve">
    <value>Add To Cart</value>
  </data>
  <data name="ResAddToCart1" xml:space="preserve">
    <value>Add To Cart</value>
  </data>
  <data name="ResAddToShoppingCart" xml:space="preserve">
    <value>Add To Shopping Cart</value>
  </data>
  <data name="ResAddUser" xml:space="preserve">
    <value>Add User</value>
  </data>
  <data name="ResAddVendor" xml:space="preserve">
    <value>Add Vendor</value>
  </data>
  <data name="ResAddVendor1" xml:space="preserve">
    <value>Add Vendor</value>
  </data>
  <data name="ResAddVendorInfo" xml:space="preserve">
    <value>Add Vendor Info</value>
  </data>
  <data name="ResAddVendorName" xml:space="preserve">
    <value>Add Vendor Name</value>
  </data>
  <data name="ResAddVendorPartInfo" xml:space="preserve">
    <value>Add Vendor Part Info</value>
  </data>
  <data name="ResAdvanceSearch" xml:space="preserve">
    <value>Advance Search</value>
  </data>
  <data name="ResAdvanceSearch1" xml:space="preserve">
    <value>Advance Search</value>
  </data>
  <data name="ResAdvanceSearchCreateModifyQuery" xml:space="preserve">
    <value>Advance Search - Create/Modify  Query</value>
  </data>
  <data name="ResAdvanceSearchCreateModifyQuery1" xml:space="preserve">
    <value>Advance Search - Create/Modify  Query</value>
  </data>
  <data name="ResAdvanceSearchCreateQuery" xml:space="preserve">
    <value>Advance Search - Create Query</value>
  </data>
  <data name="ResAdvanceSearchCreateQuery1" xml:space="preserve">
    <value>Advance Search - Create Query</value>
  </data>
  <data name="ResAdvanceSearchModifyQuery" xml:space="preserve">
    <value>Advance Search - Modify Query</value>
  </data>
  <data name="ResAdvanceSearchModifyQuery1" xml:space="preserve">
    <value>Advance Search - Modify Query</value>
  </data>
  <data name="ResAdvanceSearchResult" xml:space="preserve">
    <value>Advance Search Result</value>
  </data>
  <data name="ResAdvanceSearchResult1" xml:space="preserve">
    <value>Advance Search Result</value>
  </data>
  <data name="ResAgeing" xml:space="preserve">
    <value>Ageing</value>
  </data>
  <data name="ResAgeing1" xml:space="preserve">
    <value>Ageing</value>
  </data>
  <data name="ResAgeingindays" xml:space="preserve">
    <value>Ageing in days</value>
  </data>
  <data name="ResAlign" xml:space="preserve">
    <value>Align</value>
  </data>
  <data name="ResAll" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="ResAll1" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="ResAllCustomers" xml:space="preserve">
    <value>All Customers</value>
  </data>
  <data name="ResAllDocumnets" xml:space="preserve">
    <value>All Documents</value>
  </data>
  <data name="ResAllocated" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="ResAllocated1" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="ResAllocatedTo" xml:space="preserve">
    <value>Allocated To</value>
  </data>
  <data name="ResAllOrders" xml:space="preserve">
    <value>All Orders</value>
  </data>
  <data name="ResAlternateDescription" xml:space="preserve">
    <value>Alternate Description</value>
  </data>
  <data name="ResAlternateDescription1" xml:space="preserve">
    <value>Alternate Description</value>
  </data>
  <data name="ResAlternatePart#" xml:space="preserve">
    <value>Alternate Part #</value>
  </data>
  <data name="ResAlternatePart#1" xml:space="preserve">
    <value>Alternate Part #</value>
  </data>
  <data name="ResAND" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="ResAND1" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="ResAnewdocument" xml:space="preserve">
    <value>A new document -</value>
  </data>
  <data name="ResAnnotation" xml:space="preserve">
    <value>Add Annotations</value>
  </data>
  <data name="ResAnnotation1" xml:space="preserve">
    <value>Annotation</value>
  </data>
  <data name="ResAnnotationDetails" xml:space="preserve">
    <value>Annotation Details</value>
  </data>
  <data name="ResAnnotationDetails1" xml:space="preserve">
    <value>Annotation Details</value>
  </data>
  <data name="ResAnnotationName" xml:space="preserve">
    <value>Annotation Name</value>
  </data>
  <data name="ResApplicationWillBeDownOn" xml:space="preserve">
    <value>Application will be down on</value>
  </data>
  <data name="ResAreyousureyouwanttoDelete" xml:space="preserve">
    <value>Are you sure you want to Delete?</value>
  </data>
  <data name="ResAreyousureyouwanttoDelete?" xml:space="preserve">
    <value>Are you sure you want to Delete?</value>
  </data>
  <data name="ResAssembliesAlphaIndex" xml:space="preserve">
    <value>ASSEMBLIES ALPHABETICAL INDEX</value>
  </data>
  <data name="ResAssembly" xml:space="preserve">
    <value>Assembly</value>
  </data>
  <data name="ResAssembly#" xml:space="preserve">
    <value>Assembly #</value>
  </data>
  <data name="ResAssembly#1" xml:space="preserve">
    <value>Assembly #</value>
  </data>
  <data name="ResAssembly&amp;Parts" xml:space="preserve">
    <value>Assembly &amp; Parts</value>
  </data>
  <data name="ResAssembly&amp;PartsCustomerDocumentLink" xml:space="preserve">
    <value>Assembly &amp; Parts Customer Document Link</value>
  </data>
  <data name="ResAssembly&amp;PartsCustomerView" xml:space="preserve">
    <value>Assembly &amp; Parts Customer View</value>
  </data>
  <data name="ResAssembly&amp;PartsViewer" xml:space="preserve">
    <value>Assembly &amp; Parts Viewer</value>
  </data>
  <data name="ResAssembly1" xml:space="preserve">
    <value>Assembly</value>
  </data>
  <data name="ResAssemblyAssociation" xml:space="preserve">
    <value>Assembly Association</value>
  </data>
  <data name="ResAssemblyAssociation1" xml:space="preserve">
    <value>Assembly Association</value>
  </data>
  <data name="ResAssemblyAttachments" xml:space="preserve">
    <value>Assembly Attachments</value>
  </data>
  <data name="ResAssemblyAttachments1" xml:space="preserve">
    <value>Assembly Attachments</value>
  </data>
  <data name="ResAssemblyBOMPartDetails" xml:space="preserve">
    <value>BOM Part Details</value>
  </data>
  <data name="ResAssemblyCount" xml:space="preserve">
    <value># of Assy. Used</value>
  </data>
  <data name="ResAssemblyCreatedDate" xml:space="preserve">
    <value>Created Date</value>
  </data>
  <data name="ResAssemblyCustomerLink" xml:space="preserve">
    <value>Assembly Customer Document</value>
  </data>
  <data name="ResAssemblyCustomerView" xml:space="preserve">
    <value>Assembly Customer View</value>
  </data>
  <data name="ResAssemblyCustomerView1" xml:space="preserve">
    <value>Assembly Customer View</value>
  </data>
  <data name="ResAssemblyDescription" xml:space="preserve">
    <value>Assembly Description</value>
  </data>
  <data name="ResAssemblyDescription1" xml:space="preserve">
    <value>Assembly Description</value>
  </data>
  <data name="ResAssemblyDocuments" xml:space="preserve">
    <value>Assembly Documents</value>
  </data>
  <data name="ResAssemblyDocuments1" xml:space="preserve">
    <value>Assembly Documents</value>
  </data>
  <data name="ResAssemblyImages" xml:space="preserve">
    <value>Assembly Images</value>
  </data>
  <data name="ResAssemblyImages1" xml:space="preserve">
    <value>Assembly Images</value>
  </data>
  <data name="ResAssemblyImageUpload" xml:space="preserve">
    <value>Assembly Image Upload</value>
  </data>
  <data name="ResAssemblyImageUpload1" xml:space="preserve">
    <value>Assembly Image Upload</value>
  </data>
  <data name="ResAssemblyImport" xml:space="preserve">
    <value>Assembly Import</value>
  </data>
  <data name="ResAssemblyImport1" xml:space="preserve">
    <value>Assembly Import</value>
  </data>
  <data name="ResAssemblyList" xml:space="preserve">
    <value>Assembly List</value>
  </data>
  <data name="ResAssemblyList1" xml:space="preserve">
    <value>Assembly List</value>
  </data>
  <data name="ResAssemblyMaster" xml:space="preserve">
    <value>Assembly-Master</value>
  </data>
  <data name="ResAssemblyMaster1" xml:space="preserve">
    <value>Assembly-Master</value>
  </data>
  <data name="ResAssemblyModifiedDate" xml:space="preserve">
    <value>Modified Date</value>
  </data>
  <data name="ResAssemblyName" xml:space="preserve">
    <value>Assembly Name</value>
  </data>
  <data name="ResAssemblyName1" xml:space="preserve">
    <value>Assembly Name</value>
  </data>
  <data name="ResAssemblyNo" xml:space="preserve">
    <value>Assembly No</value>
  </data>
  <data name="ResAssemblyPart" xml:space="preserve">
    <value>Assembly Part</value>
  </data>
  <data name="ResAssemblyPartConfigurations" xml:space="preserve">
    <value>Part-Configurations</value>
  </data>
  <data name="ResAssemblyPartDetails" xml:space="preserve">
    <value>Assembly Part-Details</value>
  </data>
  <data name="ResAssemblyPartDetaResAssemblyPartDeta" xml:space="preserve">
    <value />
  </data>
  <data name="ResAssemblyPartsServiceDocumentLink" xml:space="preserve">
    <value>Assembly &amp; Parts Service Document Link</value>
  </data>
  <data name="ResAssemblyServiceDocumentLink" xml:space="preserve">
    <value>Assly Service Document Link</value>
  </data>
  <data name="ResAssemblyServiceDocumentLink1" xml:space="preserve">
    <value>Assembly Service Document Link</value>
  </data>
  <data name="ResAssemblyServiceDocumentLink2" xml:space="preserve">
    <value>Assly Service Document Link</value>
  </data>
  <data name="ResAssemblyServiceLink" xml:space="preserve">
    <value>Assembly Service Link</value>
  </data>
  <data name="ResAssemblyToolbar Confi." xml:space="preserve">
    <value>Assembly Toolbar Config</value>
  </data>
  <data name="ResAssemblyToolbar Confi.1" xml:space="preserve">
    <value>Assembly Toolbar Config</value>
  </data>
  <data name="ResAssemblyToolbarConfig" xml:space="preserve">
    <value>Assembly Toolbar Config.</value>
  </data>
  <data name="ResAssemblyToolbarConfig1" xml:space="preserve">
    <value>Assembly Toolbar Config.</value>
  </data>
  <data name="ResAssemblyToolBarConfiguration" xml:space="preserve">
    <value>Assembly Tool Bar Configuration</value>
  </data>
  <data name="ResAssemblyToolBarConfiguration1" xml:space="preserve">
    <value>Assembly Tool Bar Configuration</value>
  </data>
  <data name="ResAssemblyViewConfig" xml:space="preserve">
    <value>Assembly View Config.</value>
  </data>
  <data name="ResAssemblyViewConfig1" xml:space="preserve">
    <value>Assembly View Config.</value>
  </data>
  <data name="ResAssemblyViewConfiguration-List" xml:space="preserve">
    <value>Assembly View Configuration-List</value>
  </data>
  <data name="ResAssemblyViewConfiguration-List1" xml:space="preserve">
    <value>Assembly View Configuration-List</value>
  </data>
  <data name="ResAssemblyViewConfigure-Edit" xml:space="preserve">
    <value>Assembly View Configure-Edit</value>
  </data>
  <data name="ResAssemblyViewConfigure-Edit1" xml:space="preserve">
    <value>Assembly View Configure-Edit</value>
  </data>
  <data name="ResAssemblyViewDetails" xml:space="preserve">
    <value>Assembly-Viewer Details</value>
  </data>
  <data name="ResAssemblyViewer" xml:space="preserve">
    <value>Assembly-Viewer</value>
  </data>
  <data name="ResAssemblyViewer1" xml:space="preserve">
    <value>Assembly-Viewer</value>
  </data>
  <data name="ResAssociateAssembly" xml:space="preserve">
    <value>Associate Assembly</value>
  </data>
  <data name="ResAssociateAssembly-List" xml:space="preserve">
    <value>Associate Assembly-List</value>
  </data>
  <data name="ResAssociateAssembly-List1" xml:space="preserve">
    <value>Associate Assembly-List</value>
  </data>
  <data name="ResAssociateAssembly1" xml:space="preserve">
    <value>Associate Assembly</value>
  </data>
  <data name="ResAssociateCatalogue" xml:space="preserve">
    <value>Associate Catalogue</value>
  </data>
  <data name="ResAssociateCatalogue1" xml:space="preserve">
    <value>Associate Catalogue</value>
  </data>
  <data name="ResAssociateCatalogueUpdation" xml:space="preserve">
    <value>Associate Catalogue Updation</value>
  </data>
  <data name="ResAssociateCatalogueUpdation1" xml:space="preserve">
    <value>Associate Catalogue Updation</value>
  </data>
  <data name="ResAssociatedAssemblies" xml:space="preserve">
    <value>Associated Assemblies</value>
  </data>
  <data name="ResAssociateDrawings" xml:space="preserve">
    <value>Associate Drawings</value>
  </data>
  <data name="ResAssociateDrawings1" xml:space="preserve">
    <value>Associate Drawings</value>
  </data>
  <data name="ResAssociateDrawingtocatalogue" xml:space="preserve">
    <value>Associate Drawing to catalogue</value>
  </data>
  <data name="ResAssociateDrawingtocatalogue1" xml:space="preserve">
    <value>Associate Drawing to catalogue</value>
  </data>
  <data name="ResAssociatedSections" xml:space="preserve">
    <value>Associated Sections</value>
  </data>
  <data name="ResAssociatedSections1" xml:space="preserve">
    <value>Associated Sections</value>
  </data>
  <data name="ResAssociateOptions" xml:space="preserve">
    <value>Associate Options</value>
  </data>
  <data name="ResAssociateOptions1" xml:space="preserve">
    <value>Associate Options</value>
  </data>
  <data name="ResAssociateParts" xml:space="preserve">
    <value>Associate Parts</value>
  </data>
  <data name="ResAssociateParts1" xml:space="preserve">
    <value>Associate Parts</value>
  </data>
  <data name="ResAssociateSections" xml:space="preserve">
    <value>Associate Sections</value>
  </data>
  <data name="ResAssociateSections1" xml:space="preserve">
    <value>Associate Sections</value>
  </data>
  <data name="ResAssociateServiceSection" xml:space="preserve">
    <value>Associate Service Section</value>
  </data>
  <data name="ResAssociateServiceSection1" xml:space="preserve">
    <value>Associate Service Section</value>
  </data>
  <data name="ResAssyAssociation" xml:space="preserve">
    <value>Assy Association</value>
  </data>
  <data name="ResAssyAssociation1" xml:space="preserve">
    <value>Assy Association</value>
  </data>
  <data name="ResAttach" xml:space="preserve">
    <value>Attach.</value>
  </data>
  <data name="ResAttach1" xml:space="preserve">
    <value>Attach.</value>
  </data>
  <data name="ResAttachDownload" xml:space="preserve">
    <value>Attach / Download</value>
  </data>
  <data name="ResAttachment" xml:space="preserve">
    <value>Attachment</value>
  </data>
  <data name="ResAttachment-Add" xml:space="preserve">
    <value>Attachment-Add</value>
  </data>
  <data name="ResAttachment-Add1" xml:space="preserve">
    <value>Attachment-Add</value>
  </data>
  <data name="ResAttachment-Edit" xml:space="preserve">
    <value>Attachment-Edit</value>
  </data>
  <data name="ResAttachment-Edit1" xml:space="preserve">
    <value>Attachment-Edit</value>
  </data>
  <data name="ResAttachment1" xml:space="preserve">
    <value>Attachment</value>
  </data>
  <data name="ResAttachmentCategory" xml:space="preserve">
    <value>Attachment Category</value>
  </data>
  <data name="ResAttachmentList" xml:space="preserve">
    <value>Attachment List</value>
  </data>
  <data name="ResAttachments" xml:space="preserve">
    <value>Attachments</value>
  </data>
  <data name="ResAttachments1" xml:space="preserve">
    <value>Attachments</value>
  </data>
  <data name="ResAttachmentType" xml:space="preserve">
    <value>Attachment  Type</value>
  </data>
  <data name="ResAttachmentType1" xml:space="preserve">
    <value>Attachment  Type</value>
  </data>
  <data name="ResAttachmentUpload" xml:space="preserve">
    <value>Attachment Upload</value>
  </data>
  <data name="ResAttachmentUpload1" xml:space="preserve">
    <value>Attachment Upload</value>
  </data>
  <data name="ResAuthor" xml:space="preserve">
    <value>Author</value>
  </data>
  <data name="ResAuthorAttachments" xml:space="preserve">
    <value>Author Attachments</value>
  </data>
  <data name="ResAuthorAttachments1" xml:space="preserve">
    <value>Author Attachments</value>
  </data>
  <data name="ResAuthorErrorReportAttachments" xml:space="preserve">
    <value>Author Error Report Attachments</value>
  </data>
  <data name="ResAuthoringActivity" xml:space="preserve">
    <value>Authoring Activity</value>
  </data>
  <data name="ResAuthoringActivity1" xml:space="preserve">
    <value>Authoring Activity</value>
  </data>
  <data name="ResAuthoringActivityDefinition" xml:space="preserve">
    <value>Authoring Activity Definition</value>
  </data>
  <data name="ResAuthoringActivityDefinition1" xml:space="preserve">
    <value>Authoring Activity Definition</value>
  </data>
  <data name="ResAuthoringActivityList" xml:space="preserve">
    <value>Authoring Activity List</value>
  </data>
  <data name="ResAuthoringActivityList1" xml:space="preserve">
    <value>Authoring Activity List</value>
  </data>
  <data name="ResAuthoringErrorLog" xml:space="preserve">
    <value>Authoring Error Log</value>
  </data>
  <data name="ResAuthoringErrorLog1" xml:space="preserve">
    <value>Authoring Error Log</value>
  </data>
  <data name="ResAuthoringErrorLogReport" xml:space="preserve">
    <value>Authoring Error Log Report</value>
  </data>
  <data name="ResAuthoringLog" xml:space="preserve">
    <value>Authoring Log</value>
  </data>
  <data name="ResAuthoringLog-Edit" xml:space="preserve">
    <value>Authoring Log-Edit</value>
  </data>
  <data name="ResAuthoringLog-Edit1" xml:space="preserve">
    <value>Authoring Log-Edit</value>
  </data>
  <data name="ResAuthoringLog1" xml:space="preserve">
    <value>Authoring Log</value>
  </data>
  <data name="ResAuthoringLogDetails" xml:space="preserve">
    <value>Authoring Log Details</value>
  </data>
  <data name="ResAuthoringLogList" xml:space="preserve">
    <value>Authoring Log List</value>
  </data>
  <data name="ResAuthoringLogReport" xml:space="preserve">
    <value>Authoring Log Report</value>
  </data>
  <data name="ResAuthoringLogUpdate" xml:space="preserve">
    <value>Authoring Log Update</value>
  </data>
  <data name="ResAuthoringLogUpdate1" xml:space="preserve">
    <value>Authoring Log Update</value>
  </data>
  <data name="ResAuthoringNotes" xml:space="preserve">
    <value>Authoring Notes</value>
  </data>
  <data name="ResAuthoringNotes1" xml:space="preserve">
    <value>Authoring Notes</value>
  </data>
  <data name="ResAuthoringNotesAdd" xml:space="preserve">
    <value>Authoring Notes-Add</value>
  </data>
  <data name="ResAuthoringNotesAdd1" xml:space="preserve">
    <value>Authoring Notes-Add</value>
  </data>
  <data name="ResAuthoringNotesEdit" xml:space="preserve">
    <value>Authoring Notes-Edit</value>
  </data>
  <data name="ResAuthoringNotesEdit1" xml:space="preserve">
    <value>Authoring Notes-Edit</value>
  </data>
  <data name="ResAuthoringNotesList" xml:space="preserve">
    <value>Authoring Notes Unique List</value>
  </data>
  <data name="ResAuthoringUsers" xml:space="preserve">
    <value>Authoring Users</value>
  </data>
  <data name="ResAuthoringUsers1" xml:space="preserve">
    <value>Authoring Users</value>
  </data>
  <data name="ResAuthorLog" xml:space="preserve">
    <value>Author Log</value>
  </data>
  <data name="ResAuthorLog1" xml:space="preserve">
    <value>Author Log</value>
  </data>
  <data name="ResAuthorNotes" xml:space="preserve">
    <value>Author Notes</value>
  </data>
  <data name="ResAuthorNotes1" xml:space="preserve">
    <value>Author Notes</value>
  </data>
  <data name="ResAuthorNotesAttachments" xml:space="preserve">
    <value>Author Notes Attachments</value>
  </data>
  <data name="ResAuthorNotesAttachments1" xml:space="preserve">
    <value>Author Notes Attachments</value>
  </data>
  <data name="ResAuthorProductionDetails" xml:space="preserve">
    <value>Author Production Details</value>
  </data>
  <data name="ResAuthorTimeSheet" xml:space="preserve">
    <value>Author Time Sheet</value>
  </data>
  <data name="ResAutoHotspot" xml:space="preserve">
    <value>Auto Hotspot</value>
  </data>
  <data name="ResBackToTopLevel" xml:space="preserve">
    <value>Back to top level</value>
  </data>
  <data name="ResBCC" xml:space="preserve">
    <value>BCC</value>
  </data>
  <data name="ResBCC1" xml:space="preserve">
    <value>BCC</value>
  </data>
  <data name="ResBOMAdd" xml:space="preserve">
    <value>BOM Add</value>
  </data>
  <data name="ResBOMAdd1" xml:space="preserve">
    <value>BOM Add</value>
  </data>
  <data name="ResBOMEdit" xml:space="preserve">
    <value>BOM Edit</value>
  </data>
  <data name="ResBOMEdit1" xml:space="preserve">
    <value>BOM Edit</value>
  </data>
  <data name="ResBOMGridConfiguration" xml:space="preserve">
    <value>BOM Grid Configuration</value>
  </data>
  <data name="ResBOMGUIConfigure" xml:space="preserve">
    <value>BOM GUI Configuration</value>
  </data>
  <data name="ResBOMPart" xml:space="preserve">
    <value>BOM Part</value>
  </data>
  <data name="ResBOMPartNotFound" xml:space="preserve">
    <value>BOM Part not found</value>
  </data>
  <data name="ResBOMPrint" xml:space="preserve">
    <value>BOM Print</value>
  </data>
  <data name="ResBOMPrint1" xml:space="preserve">
    <value>BOM Print</value>
  </data>
  <data name="ResBookmark" xml:space="preserve">
    <value>Bookmark</value>
  </data>
  <data name="ResBookMarkDetails" xml:space="preserve">
    <value>Manage Bookmark Details</value>
  </data>
  <data name="ResBookMarkListDetails" xml:space="preserve">
    <value>Bookmark Details</value>
  </data>
  <data name="ResBookmarkName" xml:space="preserve">
    <value>Bookmark Name</value>
  </data>
  <data name="ResBookmarkName1" xml:space="preserve">
    <value>Bookmark Name</value>
  </data>
  <data name="ResBookMarks" xml:space="preserve">
    <value>Add to Bookmark</value>
  </data>
  <data name="ResBookMarks1" xml:space="preserve">
    <value>Book Marks</value>
  </data>
  <data name="ResBoth" xml:space="preserve">
    <value>Both</value>
  </data>
  <data name="ResBoth?" xml:space="preserve">
    <value>Both?</value>
  </data>
  <data name="ResBoth?1" xml:space="preserve">
    <value>Both?</value>
  </data>
  <data name="ResBrand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="ResBrand-Add" xml:space="preserve">
    <value>Brand - Add</value>
  </data>
  <data name="ResBrand-Add1" xml:space="preserve">
    <value>Brand - Add</value>
  </data>
  <data name="ResBrand1" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="ResBrandAssociation" xml:space="preserve">
    <value>Brand Association</value>
  </data>
  <data name="ResBrandAssociation1" xml:space="preserve">
    <value>Brand Association</value>
  </data>
  <data name="ResBrandAttachments" xml:space="preserve">
    <value>Brand Attachments</value>
  </data>
  <data name="ResBrandCode" xml:space="preserve">
    <value>Brand Code</value>
  </data>
  <data name="ResBrandCode1" xml:space="preserve">
    <value>Brand Code</value>
  </data>
  <data name="ResBrandDetails" xml:space="preserve">
    <value>Brand Details</value>
  </data>
  <data name="ResBrandImages" xml:space="preserve">
    <value>Brand Images</value>
  </data>
  <data name="ResBrandImages1" xml:space="preserve">
    <value>Brand Images</value>
  </data>
  <data name="ResBrandList" xml:space="preserve">
    <value>Brand List</value>
  </data>
  <data name="ResBrandList1" xml:space="preserve">
    <value>Brand List</value>
  </data>
  <data name="ResBrandName" xml:space="preserve">
    <value>Brand Name</value>
  </data>
  <data name="ResBrandName1" xml:space="preserve">
    <value>Brand Name</value>
  </data>
  <data name="ResBrandNameSearchList" xml:space="preserve">
    <value>Brand Name Search List</value>
  </data>
  <data name="ResBrandNameSearchList1" xml:space="preserve">
    <value>Brand Name Search List</value>
  </data>
  <data name="ResBrowse" xml:space="preserve">
    <value>Add File</value>
  </data>
  <data name="ResCalendar" xml:space="preserve">
    <value>Calendar</value>
  </data>
  <data name="ResCalendar1" xml:space="preserve">
    <value>Calendar</value>
  </data>
  <data name="ResCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ResCancelQuery" xml:space="preserve">
    <value>Cancel Query</value>
  </data>
  <data name="ResCannotExceedMoreThan255" xml:space="preserve">
    <value>Sequence Must Be Between 1 to 255</value>
  </data>
  <data name="ResCartName" xml:space="preserve">
    <value>Cart Name</value>
  </data>
  <data name="ResCartName1" xml:space="preserve">
    <value>Cart Name</value>
  </data>
  <data name="ResCartUIList" xml:space="preserve">
    <value>Cart UI List</value>
  </data>
  <data name="ResCartUIList1" xml:space="preserve">
    <value>Cart UI List</value>
  </data>
  <data name="ResCassic" xml:space="preserve">
    <value>Cassic</value>
  </data>
  <data name="ResCassic1" xml:space="preserve">
    <value>Cassic</value>
  </data>
  <data name="ResCatalogue" xml:space="preserve">
    <value>Catalogue</value>
  </data>
  <data name="ResCatalogue#" xml:space="preserve">
    <value>Catalogue #</value>
  </data>
  <data name="ResCatalogue#1" xml:space="preserve">
    <value>Catalogue #</value>
  </data>
  <data name="ResCatalogue1" xml:space="preserve">
    <value>Catalogue 1</value>
  </data>
  <data name="ResCatalogue11" xml:space="preserve">
    <value>Catalogue 1</value>
  </data>
  <data name="ResCatalogue2" xml:space="preserve">
    <value>Catalogue 2</value>
  </data>
  <data name="ResCatalogue21" xml:space="preserve">
    <value>Catalogue 2</value>
  </data>
  <data name="ResCatalogue3" xml:space="preserve">
    <value>Catalogue 3</value>
  </data>
  <data name="ResCatalogue31" xml:space="preserve">
    <value>Catalogue 3</value>
  </data>
  <data name="ResCatalogue4" xml:space="preserve">
    <value>Catalogue 4</value>
  </data>
  <data name="ResCatalogue41" xml:space="preserve">
    <value>Catalogue 4</value>
  </data>
  <data name="ResCatalogue5" xml:space="preserve">
    <value>Catalogue 5</value>
  </data>
  <data name="ResCatalogue51" xml:space="preserve">
    <value>Catalogue 5</value>
  </data>
  <data name="ResCatalogue6" xml:space="preserve">
    <value>Catalogue</value>
  </data>
  <data name="ResCatalogueAssociation" xml:space="preserve">
    <value>Catalogue Association</value>
  </data>
  <data name="ResCatalogueAssociation1" xml:space="preserve">
    <value>Catalogue Association</value>
  </data>
  <data name="ResCatalogueCompare" xml:space="preserve">
    <value>Catalogue Compare</value>
  </data>
  <data name="ResCatalogueCompare1" xml:space="preserve">
    <value>Catalogue Compare</value>
  </data>
  <data name="ResCatalogueCompareResult" xml:space="preserve">
    <value>Catalogue Compare Result</value>
  </data>
  <data name="ResCatalogueCompareResult1" xml:space="preserve">
    <value>Catalogue Compare Result</value>
  </data>
  <data name="ResCatalogueCustomerAttachment" xml:space="preserve">
    <value>Catalogue Customer Attachment</value>
  </data>
  <data name="ResCatalogueCustomerLinkDocument" xml:space="preserve">
    <value>Catalogue Customer Link Document</value>
  </data>
  <data name="ResCatalogueDesc" xml:space="preserve">
    <value>Catalogue Desc</value>
  </data>
  <data name="ResCatalogueDesc1" xml:space="preserve">
    <value>Catalogue Desc</value>
  </data>
  <data name="ResCatalogueDescription" xml:space="preserve">
    <value>Catalogue Description</value>
  </data>
  <data name="ResCatalogueDescription1" xml:space="preserve">
    <value>Catalogue Description</value>
  </data>
  <data name="ResCatalogueDetails" xml:space="preserve">
    <value>Catalogue Details</value>
  </data>
  <data name="ResCatalogueError" xml:space="preserve">
    <value>Catalogue Error</value>
  </data>
  <data name="ResCatalogueImages" xml:space="preserve">
    <value>Catalogue Images</value>
  </data>
  <data name="ResCatalogueImages1" xml:space="preserve">
    <value>Catalogue Images</value>
  </data>
  <data name="ResCatalogueLink" xml:space="preserve">
    <value>Catalogue Link</value>
  </data>
  <data name="ResCatalogueLink1" xml:space="preserve">
    <value>Catalogue Link</value>
  </data>
  <data name="ResCatalogueList" xml:space="preserve">
    <value>Catalogue List</value>
  </data>
  <data name="ResCatalogueList1" xml:space="preserve">
    <value>Catalogue List</value>
  </data>
  <data name="ResCatalogueName" xml:space="preserve">
    <value>Catalogue Name</value>
  </data>
  <data name="ResCatalogueName1" xml:space="preserve">
    <value>Catalogue Name</value>
  </data>
  <data name="ResCataloguePublish" xml:space="preserve">
    <value>Catalogue Publish</value>
  </data>
  <data name="ResCataloguePublishNotification" xml:space="preserve">
    <value>Catalogue Publish Notification</value>
  </data>
  <data name="ResCatalogueRevisionDetails" xml:space="preserve">
    <value>Catalogue Revision Details</value>
  </data>
  <data name="ResCatalogueRevisionHistory" xml:space="preserve">
    <value>Catalogue Revision History</value>
  </data>
  <data name="ResCatalogueRevisionHistory1" xml:space="preserve">
    <value>Catalogue Revision History</value>
  </data>
  <data name="ResCatalogueSearchList" xml:space="preserve">
    <value>Catalogue Search List</value>
  </data>
  <data name="ResCatalogueSearchList1" xml:space="preserve">
    <value>Catalogue Search List</value>
  </data>
  <data name="ResCatalogueServiceAttachment" xml:space="preserve">
    <value>Catalogue Service Attachment</value>
  </data>
  <data name="ResCatalogueServiceLinkDocument" xml:space="preserve">
    <value>Catalogue Service Link Document</value>
  </data>
  <data name="ResCatalogueStatusReport" xml:space="preserve">
    <value>Catalogue Status Report</value>
  </data>
  <data name="ResCatalogueViewConfig" xml:space="preserve">
    <value>Catalogue View Config.</value>
  </data>
  <data name="ResCatalogueViewConfig1" xml:space="preserve">
    <value>Catalogue View Config.</value>
  </data>
  <data name="ResCatalogueViewConfiguration-List" xml:space="preserve">
    <value>Catalogue View Configuration-List</value>
  </data>
  <data name="ResCatalogueViewConfiguration-List1" xml:space="preserve">
    <value>Catalogue View Configuration-List</value>
  </data>
  <data name="ResCategory" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="ResCC" xml:space="preserve">
    <value>CC</value>
  </data>
  <data name="ResCC1" xml:space="preserve">
    <value>CC</value>
  </data>
  <data name="ResChange?" xml:space="preserve">
    <value>Change?</value>
  </data>
  <data name="ResChange?1" xml:space="preserve">
    <value>Change?</value>
  </data>
  <data name="ResChangeOwnershipOfaVehilce" xml:space="preserve">
    <value>Change Ownership of a Vehicle</value>
  </data>
  <data name="ResChangeOwnershipOfaVehilce1" xml:space="preserve">
    <value>Change Ownership of a Vehicle</value>
  </data>
  <data name="ResChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="ResChangePassword1" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="ResChangeRoad#ofaVehicle" xml:space="preserve">
    <value>Change Road Number of a Vehicle</value>
  </data>
  <data name="ResChangeRoad#ofaVehicle1" xml:space="preserve">
    <value>Change Road Number of a Vehicle</value>
  </data>
  <data name="ResChangeRoadDetails" xml:space="preserve">
    <value>Change Road Details</value>
  </data>
  <data name="ResChangeRoadDetails1" xml:space="preserve">
    <value>Change Road Details</value>
  </data>
  <data name="ResChangeRoadDetailsAttachments" xml:space="preserve">
    <value>Change Road Details Attachments</value>
  </data>
  <data name="ResChangeRoadNumber" xml:space="preserve">
    <value>Change Road Number</value>
  </data>
  <data name="ResChangeVehicleOwnership" xml:space="preserve">
    <value>Change Vehicle Ownership</value>
  </data>
  <data name="ResChangeVehicleOwnership1" xml:space="preserve">
    <value>Change Vehicle Ownership</value>
  </data>
  <data name="ResChangeVehicleOwnerShipAttachments" xml:space="preserve">
    <value>Change Vehicle Ownership Attachments</value>
  </data>
  <data name="ResChangeVehicleOwnershipList" xml:space="preserve">
    <value>Vehicle Ownership List</value>
  </data>
  <data name="ResChangeVehicleOwnershipList1" xml:space="preserve">
    <value>Vehicle Ownership List</value>
  </data>
  <data name="ResChangeVIN" xml:space="preserve">
    <value>Change VIN</value>
  </data>
  <data name="ResChangeVIN#ofaVehicle" xml:space="preserve">
    <value>Change VIN # of a Vehicle</value>
  </data>
  <data name="ResChangeVIN#ofaVehicle1" xml:space="preserve">
    <value>Change VIN Number of a Vehicle</value>
  </data>
  <data name="ResChangeVINDetails" xml:space="preserve">
    <value>Change VIN Details</value>
  </data>
  <data name="ResChangeVINDetails1" xml:space="preserve">
    <value>Change VIN Details</value>
  </data>
  <data name="ResChangeVINDetailsAttachments" xml:space="preserve">
    <value>Change VIN Details Attachments</value>
  </data>
  <data name="ResCharacterleft" xml:space="preserve">
    <value>Character left</value>
  </data>
  <data name="ResCharacterleft1" xml:space="preserve">
    <value>Character left</value>
  </data>
  <data name="ResChild" xml:space="preserve">
    <value>View Child</value>
  </data>
  <data name="ResChild1" xml:space="preserve">
    <value>View Child</value>
  </data>
  <data name="ResChildDetails" xml:space="preserve">
    <value>Child Details</value>
  </data>
  <data name="ResChildDetails1" xml:space="preserve">
    <value>Child Details</value>
  </data>
  <data name="ResChildPartDetails" xml:space="preserve">
    <value>Child Part Details</value>
  </data>
  <data name="ResChildPartDetails1" xml:space="preserve">
    <value>Child Part Details</value>
  </data>
  <data name="ResChooseFile" xml:space="preserve">
    <value>Choose File</value>
  </data>
  <data name="ResChooseFile1" xml:space="preserve">
    <value>Choose File</value>
  </data>
  <data name="ResCircle" xml:space="preserve">
    <value>Circle</value>
  </data>
  <data name="ResCity" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="ResClassic" xml:space="preserve">
    <value> Classic</value>
  </data>
  <data name="ResClassic1" xml:space="preserve">
    <value> Classic</value>
  </data>
  <data name="ResClear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="ResClickOnHotSpotsToUpdate" xml:space="preserve">
    <value>Click on Hotspot to link.</value>
  </data>
  <data name="ResClicktoSaveHotspot" xml:space="preserve">
    <value>Click to save Hotspot</value>
  </data>
  <data name="ResClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ResClose1" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ResClosed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="ResClosed1" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="ResCode" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ResCode1" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ResCollapse" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="ResCollapseAll" xml:space="preserve">
    <value>Collapse All</value>
  </data>
  <data name="ResColumnName" xml:space="preserve">
    <value>Column Name</value>
  </data>
  <data name="ResColumnName1" xml:space="preserve">
    <value>Column Name</value>
  </data>
  <data name="ResColumnValue" xml:space="preserve">
    <value>Column Value</value>
  </data>
  <data name="ResColumnValue1" xml:space="preserve">
    <value>Column Value</value>
  </data>
  <data name="ResComanyName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="ResComments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="ResComments1" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="ResCommonNotes" xml:space="preserve">
    <value>Common Notes</value>
  </data>
  <data name="ResCommonNotes1" xml:space="preserve">
    <value>Common Notes</value>
  </data>
  <data name="ResCommonNotesHistory" xml:space="preserve">
    <value>Common Notes History</value>
  </data>
  <data name="ResCommonNotesHistory1" xml:space="preserve">
    <value>Common Notes History</value>
  </data>
  <data name="ResCompare" xml:space="preserve">
    <value>Compare </value>
  </data>
  <data name="ResCompare1" xml:space="preserve">
    <value>Compare </value>
  </data>
  <data name="ResCompareAssemblies" xml:space="preserve">
    <value>Compare Assemblies</value>
  </data>
  <data name="ResCompareAssemblies1" xml:space="preserve">
    <value>Compare Assemblies</value>
  </data>
  <data name="ResComparedVersionList" xml:space="preserve">
    <value>Compared Version List</value>
  </data>
  <data name="ResCompareVersion" xml:space="preserve">
    <value>Compare Version </value>
  </data>
  <data name="ResCompareVersion1" xml:space="preserve">
    <value>Compare Version </value>
  </data>
  <data name="ResCondition" xml:space="preserve">
    <value>Condition</value>
  </data>
  <data name="ResCondition1" xml:space="preserve">
    <value>Condition</value>
  </data>
  <data name="ResConfigure" xml:space="preserve">
    <value>Configure</value>
  </data>
  <data name="ResConfigure1" xml:space="preserve">
    <value>Configure</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIAuthor" xml:space="preserve">
    <value>Config Assly BOM UI - Author</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIViewer" xml:space="preserve">
    <value>Config Assly BOM UI - Viewer</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIViewer1" xml:space="preserve">
    <value>Configure Assembly BOM UI - Viewer</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIViewer2" xml:space="preserve">
    <value>Config Assly BOM UI - Viewer</value>
  </data>
  <data name="ResConfigureCartUI" xml:space="preserve">
    <value>Configure</value>
  </data>
  <data name="ResConfigureCartUI1" xml:space="preserve">
    <value>Configure Cart UI</value>
  </data>
  <data name="ResConfigurePartsUI" xml:space="preserve">
    <value>Configure Parts UI</value>
  </data>
  <data name="ResConfigurePartsUI1" xml:space="preserve">
    <value>Configure Parts UI</value>
  </data>
  <data name="ResConfigureToolbar" xml:space="preserve">
    <value>Configure Toolbar</value>
  </data>
  <data name="ResConfigureToolbar-List" xml:space="preserve">
    <value>Configure Toolbar-List</value>
  </data>
  <data name="ResConfigureToolbar-List1" xml:space="preserve">
    <value>Configure Toolbar-List</value>
  </data>
  <data name="ResConfigureToolbar1" xml:space="preserve">
    <value>Configure Toolbar</value>
  </data>
  <data name="ResConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="ResConfirmPassword1" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="ResConsumptionCode" xml:space="preserve">
    <value>Consumption Code</value>
  </data>
  <data name="ResConsumptionCode1" xml:space="preserve">
    <value>Consumption Code</value>
  </data>
  <data name="ResContains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="ResContains1" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="ResCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="ResCopy1" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="ResCopyAllSections" xml:space="preserve">
    <value>Copy All Sections</value>
  </data>
  <data name="ResCopyAllSections1" xml:space="preserve">
    <value>Copy All Sections</value>
  </data>
  <data name="ResCopyBOM" xml:space="preserve">
    <value>Copy BOM</value>
  </data>
  <data name="ResCopyBOM1" xml:space="preserve">
    <value>Copy BOM</value>
  </data>
  <data name="ResCopyCatalogue" xml:space="preserve">
    <value>Copy Catalogue</value>
  </data>
  <data name="ResCopyCatalogue1" xml:space="preserve">
    <value>Copy Catalogue</value>
  </data>
  <data name="ResCopyDetails" xml:space="preserve">
    <value>Copy Details</value>
  </data>
  <data name="ResCopyDetails1" xml:space="preserve">
    <value>Copy Details</value>
  </data>
  <data name="ResCopyLocalRemaksDetsils" xml:space="preserve">
    <value>Copy-Local Remaks Details</value>
  </data>
  <data name="ResCopyLocalRemarksTo" xml:space="preserve">
    <value>Copy Local Remarks To</value>
  </data>
  <data name="ResCopyRoleFrom" xml:space="preserve">
    <value>Copy Role From</value>
  </data>
  <data name="ResCopyRoleFrom1" xml:space="preserve">
    <value>Copy Role From</value>
  </data>
  <data name="ResCopySections" xml:space="preserve">
    <value>Copy Sections</value>
  </data>
  <data name="ResCopySections1" xml:space="preserve">
    <value>Copy Sections</value>
  </data>
  <data name="ResCount" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="ResCountry" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="ResCreate" xml:space="preserve">
    <value>Create </value>
  </data>
  <data name="ResCreate1" xml:space="preserve">
    <value>Create </value>
  </data>
  <data name="ResCreateCrossReference" xml:space="preserve">
    <value>Create a Cross Reference</value>
  </data>
  <data name="ResCreateNew" xml:space="preserve">
    <value>Create New</value>
  </data>
  <data name="ResCreateNew1" xml:space="preserve">
    <value>Create New</value>
  </data>
  <data name="ResCreateNewQuery" xml:space="preserve">
    <value>Create New Query</value>
  </data>
  <data name="ResCreateNewQuery1" xml:space="preserve">
    <value>Create New Query</value>
  </data>
  <data name="ResCurrentOwner" xml:space="preserve">
    <value>Current Owner</value>
  </data>
  <data name="ResCurrentOwner1" xml:space="preserve">
    <value>Current Owner</value>
  </data>
  <data name="ResCurrentOwnerName" xml:space="preserve">
    <value>Current Owner Name</value>
  </data>
  <data name="ResCurrentPassword" xml:space="preserve">
    <value>Current Password</value>
  </data>
  <data name="ResCurrentRoad#" xml:space="preserve">
    <value>Current Road #</value>
  </data>
  <data name="ResCurrentRoad#1" xml:space="preserve">
    <value>Current Road #</value>
  </data>
  <data name="ResCurrentVIN#" xml:space="preserve">
    <value>Current VIN #</value>
  </data>
  <data name="ResCurrentVIN#1" xml:space="preserve">
    <value>Current VIN #</value>
  </data>
  <data name="ResCustomer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration" xml:space="preserve">
    <value>Customer-Assembly View Configuration</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration-List" xml:space="preserve">
    <value>Customer-Assembly View Configuration-List</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration-List1" xml:space="preserve">
    <value>Customer-Assembly View Configuration-List</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration1" xml:space="preserve">
    <value>Customer-Assembly View Configuration</value>
  </data>
  <data name="ResCustomer-CatalogueViewConfiguration-List" xml:space="preserve">
    <value>Customer-Catalogue View Configuration-List</value>
  </data>
  <data name="ResCustomer-CatalogueViewConfiguration-List1" xml:space="preserve">
    <value>Customer-Catalogue View Configuration-List</value>
  </data>
  <data name="ResCustomer-PartsInformation" xml:space="preserve">
    <value>Customer-Parts Information</value>
  </data>
  <data name="ResCustomer-PartsInformation1" xml:space="preserve">
    <value>Customer-Parts Information</value>
  </data>
  <data name="ResCustomer-PartsViewConfiguration-List" xml:space="preserve">
    <value>Customer-Parts View Configuration-List</value>
  </data>
  <data name="ResCustomer-PartsViewConfiguration-List1" xml:space="preserve">
    <value>Customer-Parts View Configuration-List</value>
  </data>
  <data name="ResCustomer-ShoppingCartConfiguration" xml:space="preserve">
    <value>Customer-Shopping Cart Configuration</value>
  </data>
  <data name="ResCustomer-ShoppingCartConfiguration1" xml:space="preserve">
    <value>Customer-Shopping Cart Configuration</value>
  </data>
  <data name="ResCustomer1" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="ResCustomerAccountNumber" xml:space="preserve">
    <value>Customer Account #</value>
  </data>
  <data name="ResCustomerAnnotation" xml:space="preserve">
    <value>Customer Annotation</value>
  </data>
  <data name="ResCustomerAnnotation1" xml:space="preserve">
    <value>Customer Annotation</value>
  </data>
  <data name="ResCustomerAssociation" xml:space="preserve">
    <value>Customer Association</value>
  </data>
  <data name="ResCustomerAssociation1" xml:space="preserve">
    <value>Customer Association</value>
  </data>
  <data name="ResCustomerAttachments" xml:space="preserve">
    <value>Customer Attachments</value>
  </data>
  <data name="ResCustomerAttachments1" xml:space="preserve">
    <value>Customer Attachments</value>
  </data>
  <data name="ResCustomerCode" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="ResCustomerCode1" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="ResCustomerCopy" xml:space="preserve">
    <value>Customer Copy</value>
  </data>
  <data name="ResCustomerDetails" xml:space="preserve">
    <value>Customer Details</value>
  </data>
  <data name="ResCustomerDoesNotExist" xml:space="preserve">
    <value>Customer Does not exist</value>
  </data>
  <data name="ResCustomerErrorLogReport" xml:space="preserve">
    <value>Customer Error Log Report</value>
  </data>
  <data name="ResCustomerErrorReportAttachments" xml:space="preserve">
    <value>Customer Error Report Attachments</value>
  </data>
  <data name="ResCustomerImages" xml:space="preserve">
    <value>Customer Images</value>
  </data>
  <data name="ResCustomerInfo" xml:space="preserve">
    <value>Customer Info</value>
  </data>
  <data name="ResCustomerInfo1" xml:space="preserve">
    <value>Customer Info</value>
  </data>
  <data name="ResCustomerLangaugeList" xml:space="preserve">
    <value>Customer Language List</value>
  </data>
  <data name="ResCustomerLevel" xml:space="preserve">
    <value>Customer Level</value>
  </data>
  <data name="ResCustomerLevel1" xml:space="preserve">
    <value>Customer Level</value>
  </data>
  <data name="ResCustomerList" xml:space="preserve">
    <value>Customer List</value>
  </data>
  <data name="ResCustomerList1" xml:space="preserve">
    <value>Customer List</value>
  </data>
  <data name="ResCustomerLog" xml:space="preserve">
    <value>Customer Log</value>
  </data>
  <data name="ResCustomerLog1" xml:space="preserve">
    <value>Customer Log</value>
  </data>
  <data name="ResCustomerMFGCode" xml:space="preserve">
    <value>Customer MFG Code</value>
  </data>
  <data name="ResCustomerMFGCode1" xml:space="preserve">
    <value>Customer MFG Code</value>
  </data>
  <data name="ResCustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="ResCustomerName1" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="ResCustomerNameSearch" xml:space="preserve">
    <value>Customer Name Search</value>
  </data>
  <data name="ResCustomerNameSearchList" xml:space="preserve">
    <value>Customer Search List</value>
  </data>
  <data name="ResCustomerNameSearchList1" xml:space="preserve">
    <value>Customer Search List</value>
  </data>
  <data name="ResCustomerOrder" xml:space="preserve">
    <value>Customer Order</value>
  </data>
  <data name="ResCustomerOrder/Road#" xml:space="preserve">
    <value>Customer Order / Road #</value>
  </data>
  <data name="ResCustomerOrder/Road#1" xml:space="preserve">
    <value>Customer Order / Road #</value>
  </data>
  <data name="ResCustomerOrder1" xml:space="preserve">
    <value>Customer Order</value>
  </data>
  <data name="ResCustomerOrderDetails" xml:space="preserve">
    <value>Customer Order Details</value>
  </data>
  <data name="ResCustomerOrderList" xml:space="preserve">
    <value>Order Details</value>
  </data>
  <data name="ResCustomerOrderReport" xml:space="preserve">
    <value>Customer Order Report</value>
  </data>
  <data name="ResCustomerOrderRoad#" xml:space="preserve">
    <value>Customer Order / Road #</value>
  </data>
  <data name="ResCustomerOrderRoad#1" xml:space="preserve">
    <value>Customer Order / Road #</value>
  </data>
  <data name="ResCustomerOrderSearchList" xml:space="preserve">
    <value>Customer Order Search List</value>
  </data>
  <data name="ResCustomerOtherAttachments" xml:space="preserve">
    <value>Customer / Other Attachments</value>
  </data>
  <data name="ResCustomerPart#" xml:space="preserve">
    <value>Customer Part #</value>
  </data>
  <data name="ResCustomerPart#1" xml:space="preserve">
    <value>Customer Part #</value>
  </data>
  <data name="ResCustomerPartInfo" xml:space="preserve">
    <value>Customer Part Info</value>
  </data>
  <data name="ResCustomerPartInfo1" xml:space="preserve">
    <value>Customer Part Info</value>
  </data>
  <data name="ResCustomerPartInformationImport" xml:space="preserve">
    <value>Customer Part Information Import</value>
  </data>
  <data name="ResCustomerPartInformationImport1" xml:space="preserve">
    <value>Customer Part Information Import</value>
  </data>
  <data name="ResCustomerPartsInformation" xml:space="preserve">
    <value>Customer-Parts Information</value>
  </data>
  <data name="ResCustomerPartsInformation1" xml:space="preserve">
    <value>Customer-Parts Information</value>
  </data>
  <data name="ResCustomerPrefix" xml:space="preserve">
    <value>Customer Prefix</value>
  </data>
  <data name="ResCustomerPrefix1" xml:space="preserve">
    <value>Customer Prefix</value>
  </data>
  <data name="ResCustomerRef#" xml:space="preserve">
    <value>Customer Ref #</value>
  </data>
  <data name="ResCustomerRef#1" xml:space="preserve">
    <value>Customer Ref #</value>
  </data>
  <data name="ResCustomerReferenceNumer" xml:space="preserve">
    <value>Customer Reference #</value>
  </data>
  <data name="ResCustomers" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="ResCustomerSearch" xml:space="preserve">
    <value>Customer Search</value>
  </data>
  <data name="ResCustomerSerachList" xml:space="preserve">
    <value>Customer Search List</value>
  </data>
  <data name="ResCustomerUsageLog" xml:space="preserve">
    <value>Customer Usage Log</value>
  </data>
  <data name="ResCustomerUsageLog1" xml:space="preserve">
    <value>Customer Usage Log</value>
  </data>
  <data name="ResCustomerUserList" xml:space="preserve">
    <value>Customer User List</value>
  </data>
  <data name="ResCustomerUserRole" xml:space="preserve">
    <value>Customer User Role</value>
  </data>
  <data name="ResCustomerUserRole1" xml:space="preserve">
    <value>Customer User Role</value>
  </data>
  <data name="ResCustomerUsers" xml:space="preserve">
    <value>Customer Users</value>
  </data>
  <data name="ResCustomerUsers1" xml:space="preserve">
    <value>Customer Users</value>
  </data>
  <data name="ResCustomerVehicleDetails" xml:space="preserve">
    <value>Customer Vehicle Details</value>
  </data>
  <data name="ResCustomerVehicleReport" xml:space="preserve">
    <value>Customer Vehicle Report</value>
  </data>
  <data name="ResCustromerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="ResCustromerName1" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="ResDashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="ResDataForOffline" xml:space="preserve">
    <value>Data For Offline</value>
  </data>
  <data name="ResDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ResDate&amp;Time" xml:space="preserve">
    <value>Date &amp; Time</value>
  </data>
  <data name="ResDate&amp;Time1" xml:space="preserve">
    <value>Date &amp; Time</value>
  </data>
  <data name="ResDate1" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ResDear" xml:space="preserve">
    <value>Dear</value>
  </data>
  <data name="ResDearSir/Madam" xml:space="preserve">
    <value>Dear Sir/Madam</value>
  </data>
  <data name="ResDefaultFont" xml:space="preserve">
    <value>Default Font</value>
  </data>
  <data name="ResDefaultFont1" xml:space="preserve">
    <value>Default Font</value>
  </data>
  <data name="ResDefaultName" xml:space="preserve">
    <value>Default Name</value>
  </data>
  <data name="ResDefaultName1" xml:space="preserve">
    <value>Default Name</value>
  </data>
  <data name="ResDefectNotification" xml:space="preserve">
    <value>Defect Notification</value>
  </data>
  <data name="ResDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ResDelete1" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ResDeleteAnnotation" xml:space="preserve">
    <value>Delete Annotation</value>
  </data>
  <data name="ResDeleteBOMPartItem" xml:space="preserve">
    <value>Delete BOM Part Item</value>
  </data>
  <data name="ResDeleteBOMPartItem1" xml:space="preserve">
    <value>Delete BOM Part Item</value>
  </data>
  <data name="ResDeleteHighlighter" xml:space="preserve">
    <value>Delete Highlighter</value>
  </data>
  <data name="ResDeleteHotspot" xml:space="preserve">
    <value>Delete Hotspot</value>
  </data>
  <data name="ResDeleteQuery" xml:space="preserve">
    <value>Delete Query</value>
  </data>
  <data name="ResDeleteQuery1" xml:space="preserve">
    <value>Delete Query</value>
  </data>
  <data name="ResDeleteSectionItem" xml:space="preserve">
    <value>Delete Section Item</value>
  </data>
  <data name="ResDeleteSectionItem1" xml:space="preserve">
    <value>Delete Section Item</value>
  </data>
  <data name="ResDeleteSelected" xml:space="preserve">
    <value>Delete Selected</value>
  </data>
  <data name="ResDepartment" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="ResDepartment1" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="ResDepartmentCode" xml:space="preserve">
    <value>Department Code</value>
  </data>
  <data name="ResDepartmentCode1" xml:space="preserve">
    <value>Department Code</value>
  </data>
  <data name="ResDepartmentLanguage-Add" xml:space="preserve">
    <value>Department Language-Add</value>
  </data>
  <data name="ResDepartmentLanguage-Add1" xml:space="preserve">
    <value>Department Language-Add</value>
  </data>
  <data name="ResDepartmentLanguage-Edit" xml:space="preserve">
    <value>Department Language-Edit</value>
  </data>
  <data name="ResDepartmentLanguage-Edit1" xml:space="preserve">
    <value>Department Language-Edit</value>
  </data>
  <data name="ResDepartmentLanguage-List" xml:space="preserve">
    <value>Department Language-List</value>
  </data>
  <data name="ResDepartmentLanguage-List1" xml:space="preserve">
    <value>Department Language-List</value>
  </data>
  <data name="ResDepartmentList" xml:space="preserve">
    <value>Department List</value>
  </data>
  <data name="ResDepartmentList1" xml:space="preserve">
    <value>Department List</value>
  </data>
  <data name="ResDepartmentName" xml:space="preserve">
    <value>Department Name</value>
  </data>
  <data name="ResDepartmentName1" xml:space="preserve">
    <value>Department Name</value>
  </data>
  <data name="ResDepartmentSearchList" xml:space="preserve">
    <value>Department Search List</value>
  </data>
  <data name="ResDepartmentSearchList1" xml:space="preserve">
    <value>Department Search List</value>
  </data>
  <data name="ResDepartmentUpdation" xml:space="preserve">
    <value>Department Updation</value>
  </data>
  <data name="ResDepartmentUpdation1" xml:space="preserve">
    <value>Department Updation</value>
  </data>
  <data name="ResDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResDescription1" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResDescriptionDoesNotExist" xml:space="preserve">
    <value>Description does not exist</value>
  </data>
  <data name="ResDescriptionLangiageList" xml:space="preserve">
    <value>Description Language List</value>
  </data>
  <data name="ResDesignation" xml:space="preserve">
    <value>Designation</value>
  </data>
  <data name="ResDesignation1" xml:space="preserve">
    <value>Designation</value>
  </data>
  <data name="ResDesignationCode" xml:space="preserve">
    <value>Designation Code</value>
  </data>
  <data name="ResDesignationCode1" xml:space="preserve">
    <value>Designation Code</value>
  </data>
  <data name="ResDesignationLanguage-Add" xml:space="preserve">
    <value>Designation Language-Add</value>
  </data>
  <data name="ResDesignationLanguage-Add1" xml:space="preserve">
    <value>Designation Language-Add</value>
  </data>
  <data name="ResDesignationLanguage-Edit" xml:space="preserve">
    <value>Designation Language-Edit</value>
  </data>
  <data name="ResDesignationLanguage-Edit1" xml:space="preserve">
    <value>Designation Language-Edit</value>
  </data>
  <data name="ResDesignationList" xml:space="preserve">
    <value>Designation List</value>
  </data>
  <data name="ResDesignationList1" xml:space="preserve">
    <value>Designation List</value>
  </data>
  <data name="ResDesignationName" xml:space="preserve">
    <value>Designation Name</value>
  </data>
  <data name="ResDesignationName1" xml:space="preserve">
    <value>Designation Name</value>
  </data>
  <data name="ResDesignationSearchList" xml:space="preserve">
    <value>Designation Search List</value>
  </data>
  <data name="ResDesignationSearchList1" xml:space="preserve">
    <value>Designation Search List</value>
  </data>
  <data name="ResDesignationUpdation" xml:space="preserve">
    <value>Designation Updation</value>
  </data>
  <data name="ResDesignationUpdation1" xml:space="preserve">
    <value>Designation Updation</value>
  </data>
  <data name="ResDictionary" xml:space="preserve">
    <value>Dictionary</value>
  </data>
  <data name="ResDictionary1" xml:space="preserve">
    <value>Dictionary</value>
  </data>
  <data name="ResDictionaryDescriptionUpdation" xml:space="preserve">
    <value>Dictionary Description Updation</value>
  </data>
  <data name="ResDictionaryDescriptionUpdation1" xml:space="preserve">
    <value>Dictionary Description Updation</value>
  </data>
  <data name="ResDictionaryLanguage-List" xml:space="preserve">
    <value>Dictionary Language-List</value>
  </data>
  <data name="ResDictionaryLanguage-List1" xml:space="preserve">
    <value>Dictionary Language-List</value>
  </data>
  <data name="ResDictionaryLanguageList" xml:space="preserve">
    <value>Dictionary Language List</value>
  </data>
  <data name="ResDictionaryList" xml:space="preserve">
    <value>Dictionary List</value>
  </data>
  <data name="ResDictionaryList1" xml:space="preserve">
    <value>Dictionary List</value>
  </data>
  <data name="ResDigitalPartsCatalogue" xml:space="preserve">
    <value>Digital Catalogue</value>
  </data>
  <data name="ResDisplayColumnName" xml:space="preserve">
    <value>Display Column Name</value>
  </data>
  <data name="ResDisplayMode" xml:space="preserve">
    <value>Display</value>
  </data>
  <data name="ResDisplayName" xml:space="preserve">
    <value>Display Name</value>
  </data>
  <data name="ResDisplayName1" xml:space="preserve">
    <value>Display Name</value>
  </data>
  <data name="ResDocumentType" xml:space="preserve">
    <value>Document Type</value>
  </data>
  <data name="ResDownload" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="ResDownloadAsPDF" xml:space="preserve">
    <value>Download as PDF</value>
  </data>
  <data name="ResDownloadAsPDF1" xml:space="preserve">
    <value>Download as PDF</value>
  </data>
  <data name="ResDownLoadReleaseNote" xml:space="preserve">
    <value>Download Release Note</value>
  </data>
  <data name="ResDownoadTemplate" xml:space="preserve">
    <value>Download Template</value>
  </data>
  <data name="ResDrawing" xml:space="preserve">
    <value>Drawing</value>
  </data>
  <data name="ResDrawing#" xml:space="preserve">
    <value>Drawing #</value>
  </data>
  <data name="ResDrawing#1" xml:space="preserve">
    <value>Drawing #</value>
  </data>
  <data name="ResDrawingDelete" xml:space="preserve">
    <value>Drawing Delete</value>
  </data>
  <data name="ResDrawingDelete1" xml:space="preserve">
    <value>Drawing Delete</value>
  </data>
  <data name="ResDrawingList" xml:space="preserve">
    <value>Drawing List</value>
  </data>
  <data name="ResDrawingList1" xml:space="preserve">
    <value>Drawing List</value>
  </data>
  <data name="ResDrawingLog" xml:space="preserve">
    <value>Drawing Log</value>
  </data>
  <data name="ResDrawingLog1" xml:space="preserve">
    <value>Drawing Log</value>
  </data>
  <data name="ResDrawingName" xml:space="preserve">
    <value>Drawing Name</value>
  </data>
  <data name="ResDrawingName1" xml:space="preserve">
    <value>Drawing Name</value>
  </data>
  <data name="ResDrawingToolbar" xml:space="preserve">
    <value>ResDrawingToolBarConfiguration</value>
  </data>
  <data name="ResDrawingToolbar1" xml:space="preserve">
    <value>ResDrawingToolBarConfiguration</value>
  </data>
  <data name="ResDrawingToolbarAttachment" xml:space="preserve">
    <value>Drawing Toolbar Attachment</value>
  </data>
  <data name="ResDrawingToolbarAttachment1" xml:space="preserve">
    <value>Drawing Toolbar Attachment</value>
  </data>
  <data name="ResDrawingToolbarConfig" xml:space="preserve">
    <value>Drawing Toolbar Config.</value>
  </data>
  <data name="ResDrawingToolbarConfig1" xml:space="preserve">
    <value>Drawing Toolbar Config.</value>
  </data>
  <data name="Resduetorelease" xml:space="preserve">
    <value>due to release</value>
  </data>
  <data name="ResDuplicateCartName" xml:space="preserve">
    <value>Duplicate Cart Name</value>
  </data>
  <data name="ResDuplicateCatalogueNo" xml:space="preserve">
    <value>Duplicate Catalogue Number</value>
  </data>
  <data name="ResDuplicateCustomer" xml:space="preserve">
    <value>Duplicate Customer</value>
  </data>
  <data name="ResDuplicateFile" xml:space="preserve">
    <value>Duplicate File</value>
  </data>
  <data name="ResDuplicateOptionEntry" xml:space="preserve">
    <value>Duplicate Option Entry</value>
  </data>
  <data name="ResDuplicatePartRecords" xml:space="preserve">
    <value>Duplicate Part Records</value>
  </data>
  <data name="ResDuplicateQueryName" xml:space="preserve">
    <value>Duplicate Query Name</value>
  </data>
  <data name="ResDuplicateRecords" xml:space="preserve">
    <value>Duplicate Records</value>
  </data>
  <data name="ResDuplicateRoleName" xml:space="preserve">
    <value>Duplicate Role Name</value>
  </data>
  <data name="ResEdit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="ResEdit1" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="ResEditAbbrevation" xml:space="preserve">
    <value>Edit Abbreviation</value>
  </data>
  <data name="ResEditAbbreviationDescription" xml:space="preserve">
    <value>Edit Abbreviation Description</value>
  </data>
  <data name="ResEditable" xml:space="preserve">
    <value>Editable</value>
  </data>
  <data name="ResEditAnnotation" xml:space="preserve">
    <value>Edit Annotation</value>
  </data>
  <data name="ResEditBOMLineItem" xml:space="preserve">
    <value>Edit BOM Line Item</value>
  </data>
  <data name="ResEditBOMLineItem1" xml:space="preserve">
    <value>Edit BOM Line Item</value>
  </data>
  <data name="ResEditCustomer" xml:space="preserve">
    <value>Edit Customer</value>
  </data>
  <data name="ResEditCustomerPartInfo" xml:space="preserve">
    <value>Edit Customer Part Info</value>
  </data>
  <data name="ResEditCustomerPartInfo1" xml:space="preserve">
    <value>Edit Customer Part Info</value>
  </data>
  <data name="ResEditDictionary" xml:space="preserve">
    <value>Edit Dictionary</value>
  </data>
  <data name="ResEditFont" xml:space="preserve">
    <value>Edit Font</value>
  </data>
  <data name="ResEditFunctionGroup" xml:space="preserve">
    <value>Edit Function Group</value>
  </data>
  <data name="ResEditGUIConfig" xml:space="preserve">
    <value>Edit GUI Config</value>
  </data>
  <data name="ResEditImage" xml:space="preserve">
    <value>Edit Image</value>
  </data>
  <data name="ResEditLineItem" xml:space="preserve">
    <value>Edit Line Item</value>
  </data>
  <data name="ResEditLineItem1" xml:space="preserve">
    <value>Edit Line Item</value>
  </data>
  <data name="ResEditMaskingAction" xml:space="preserve">
    <value>Edit-Masking Action</value>
  </data>
  <data name="ResEditMFGCode" xml:space="preserve">
    <value>Edit MFG Code - </value>
  </data>
  <data name="ResEditMFGCode1" xml:space="preserve">
    <value>Edit MFG Code - </value>
  </data>
  <data name="ResEditModel" xml:space="preserve">
    <value>Edit Model - </value>
  </data>
  <data name="ResEditModel1" xml:space="preserve">
    <value>Edit Model - </value>
  </data>
  <data name="ResEditOptions" xml:space="preserve">
    <value>Edit Options</value>
  </data>
  <data name="ResEditRecommendedParts" xml:space="preserve">
    <value>Edit Recommended Parts</value>
  </data>
  <data name="ResEditRoadHistory" xml:space="preserve">
    <value>Edit Road Number</value>
  </data>
  <data name="ResEditSetMember" xml:space="preserve">
    <value>Edit Set Member</value>
  </data>
  <data name="ResEditSupersession" xml:space="preserve">
    <value>Edit Supersession</value>
  </data>
  <data name="ResEditSupersession1" xml:space="preserve">
    <value>Edit Supersession</value>
  </data>
  <data name="ResEditUser" xml:space="preserve">
    <value>Edit User</value>
  </data>
  <data name="ResEditVehicleInfo" xml:space="preserve">
    <value>Edit Vehicle Info</value>
  </data>
  <data name="ResEditVehicleOwnership" xml:space="preserve">
    <value>Edit Vehicle Ownership</value>
  </data>
  <data name="ResEditVendorInfo" xml:space="preserve">
    <value>Edit Vendor Info</value>
  </data>
  <data name="ResEditVendorName" xml:space="preserve">
    <value>Edit Vendor Name</value>
  </data>
  <data name="ResEffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="ResEffectiveDate1" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="ResEffectiveFromDate" xml:space="preserve">
    <value>Effective From Date</value>
  </data>
  <data name="ResEffectiveFromDate1" xml:space="preserve">
    <value>Effective From Date</value>
  </data>
  <data name="ResEffectiveToDate" xml:space="preserve">
    <value>Effective To Date</value>
  </data>
  <data name="ResEffectiveToDate1" xml:space="preserve">
    <value>Effective To Date</value>
  </data>
  <data name="ResElectricalDiagrams" xml:space="preserve">
    <value>Electrical Diagrams</value>
  </data>
  <data name="ResEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ResEmail1" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ResEmailalreadyexistforanotheruser" xml:space="preserve">
    <value>Email already exist for another user</value>
  </data>
  <data name="ResEmailID" xml:space="preserve">
    <value>E-mail:</value>
  </data>
  <data name="ResEmployeeCode" xml:space="preserve">
    <value>Employee Code</value>
  </data>
  <data name="ResEmployeeCode1" xml:space="preserve">
    <value>Employee Code</value>
  </data>
  <data name="ResEndWith" xml:space="preserve">
    <value>End With</value>
  </data>
  <data name="ResEndWith1" xml:space="preserve">
    <value>End With</value>
  </data>
  <data name="ResEnglish" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="ResEnglishDocument" xml:space="preserve">
    <value>English Document Path</value>
  </data>
  <data name="ResEnhanced" xml:space="preserve">
    <value> Enhanced</value>
  </data>
  <data name="ResEnhanced1" xml:space="preserve">
    <value>Enhanced</value>
  </data>
  <data name="ResEnhanced11" xml:space="preserve">
    <value>Enhanced</value>
  </data>
  <data name="ResEnhanced2" xml:space="preserve">
    <value> Enhanced</value>
  </data>
  <data name="ResEnterBrandCode" xml:space="preserve">
    <value>Enter Brand Code</value>
  </data>
  <data name="ResEnterBrandCode1" xml:space="preserve">
    <value>Enter Brand Code</value>
  </data>
  <data name="resEnterbw1to999" xml:space="preserve">
    <value>Enter thickness between [1-999]</value>
  </data>
  <data name="ResEnterCartDate" xml:space="preserve">
    <value>Enter Cart Date</value>
  </data>
  <data name="ResEnterCartDate1" xml:space="preserve">
    <value>Enter Cart Date</value>
  </data>
  <data name="ResEnterCartName" xml:space="preserve">
    <value>Enter Cart Name</value>
  </data>
  <data name="ResEnterCartName1" xml:space="preserve">
    <value>Enter Cart Name</value>
  </data>
  <data name="ResEnterCode" xml:space="preserve">
    <value>Enter Code</value>
  </data>
  <data name="ResEnterCode1" xml:space="preserve">
    <value>Enter Code</value>
  </data>
  <data name="ResEnterDescription" xml:space="preserve">
    <value>Enter Description</value>
  </data>
  <data name="ResEnteredInvalidSequenceNo" xml:space="preserve">
    <value>Entered Invalid Sequence Number</value>
  </data>
  <data name="ResEnteredMaskingDescriptionalreadyexist" xml:space="preserve">
    <value>Entered Masking Description already exist</value>
  </data>
  <data name="ResEnteredOptionCodealreadyexist" xml:space="preserve">
    <value>Entered Option Code already Exist</value>
  </data>
  <data name="ResEnteredOptionDescriptionalreadyexist" xml:space="preserve">
    <value>Entered Option Description already exist</value>
  </data>
  <data name="ResEnteredSequenceNoAlreadyExists" xml:space="preserve">
    <value>Entered Sequence Number Already Exists</value>
  </data>
  <data name="ResEnteredURLIsInvalid" xml:space="preserve">
    <value>Entered URL Is Invalid</value>
  </data>
  <data name="ResEnterItem#" xml:space="preserve">
    <value>Enter Item# </value>
  </data>
  <data name="ResEnterMainSection" xml:space="preserve">
    <value>Enter Main Section</value>
  </data>
  <data name="ResEnterName" xml:space="preserve">
    <value>Enter Name</value>
  </data>
  <data name="ResEnterName1" xml:space="preserve">
    <value>Enter Name</value>
  </data>
  <data name="ResEnterRemarks" xml:space="preserve">
    <value>Enter Remarks</value>
  </data>
  <data name="ResEnterRemarks1" xml:space="preserve">
    <value>Enter Remarks</value>
  </data>
  <data name="ResEnterSection" xml:space="preserve">
    <value>Enter Section</value>
  </data>
  <data name="ResEnterSequence" xml:space="preserve">
    <value>Enter Sequence</value>
  </data>
  <data name="ResEnterSubSection" xml:space="preserve">
    <value>Enter Sub Section</value>
  </data>
  <data name="ResEnterText" xml:space="preserve">
    <value>Enter text</value>
  </data>
  <data name="resEntertheTexttoAdd" xml:space="preserve">
    <value>Enter text to add</value>
  </data>
  <data name="ResEnterThickness" xml:space="preserve">
    <value>Enter thickness</value>
  </data>
  <data name="ResEntervalidquantity" xml:space="preserve">
    <value>Enter valid Quantity</value>
  </data>
  <data name="ResEnterVendorName" xml:space="preserve">
    <value>Enter Vendor Name</value>
  </data>
  <data name="ResEntredDuplicatePartNumber" xml:space="preserve">
    <value>Entered Duplicate Part Number</value>
  </data>
  <data name="ResErase" xml:space="preserve">
    <value>Erase</value>
  </data>
  <data name="ResError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ResErrorAttachments" xml:space="preserve">
    <value>Error Attachments</value>
  </data>
  <data name="ResErrorAttachments1" xml:space="preserve">
    <value>Error Attachments</value>
  </data>
  <data name="ResErrorDate&amp;Time" xml:space="preserve">
    <value>Error Date &amp; Time</value>
  </data>
  <data name="ResErrorDate&amp;Time1" xml:space="preserve">
    <value>Error Date &amp; Time</value>
  </data>
  <data name="ResErrorDateTime" xml:space="preserve">
    <value>Error Date Time</value>
  </data>
  <data name="ResErrorDateTime1" xml:space="preserve">
    <value>Error Date Time</value>
  </data>
  <data name="ResErrorDescription" xml:space="preserve">
    <value>Error Description</value>
  </data>
  <data name="ResErrorDescription1" xml:space="preserve">
    <value>Error Description</value>
  </data>
  <data name="ResErrorinUploadedDataPleaseOpenExcel" xml:space="preserve">
    <value>Error in uploaded file please open Excel</value>
  </data>
  <data name="ResErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>Error in Uploaded Parts Please Open Excel</value>
  </data>
  <data name="ResErrorinUploadedPartsPleaseOpenImportLog" xml:space="preserve">
    <value>Error in Uploaded excel, please open Import Log</value>
  </data>
  <data name="ResErrorList" xml:space="preserve">
    <value>Error List</value>
  </data>
  <data name="ResErrorList1" xml:space="preserve">
    <value>Error List</value>
  </data>
  <data name="ResErrorLog" xml:space="preserve">
    <value>Error Log</value>
  </data>
  <data name="ResErrorLog1" xml:space="preserve">
    <value>Error Log</value>
  </data>
  <data name="ResErrorLogAuthor" xml:space="preserve">
    <value>Error Log Author</value>
  </data>
  <data name="ResErrorLogAuthor1" xml:space="preserve">
    <value>Error Log Author</value>
  </data>
  <data name="ResErrorLogCustomer" xml:space="preserve">
    <value>Error Log Customer</value>
  </data>
  <data name="ResErrorLogCustomer1" xml:space="preserve">
    <value>Error Log Customer</value>
  </data>
  <data name="ResErrorLogCustomerDetails" xml:space="preserve">
    <value>Error Log Customer Details</value>
  </data>
  <data name="ResErrorReport" xml:space="preserve">
    <value>Error Report</value>
  </data>
  <data name="ResErrorReportList" xml:space="preserve">
    <value>Error Report List</value>
  </data>
  <data name="ResErrorType" xml:space="preserve">
    <value>Error Type</value>
  </data>
  <data name="ResErrorType1" xml:space="preserve">
    <value>Error Type</value>
  </data>
  <data name="ResExcel" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="ResExclusion" xml:space="preserve">
    <value>Exclusion</value>
  </data>
  <data name="ResExclusionRange-Options" xml:space="preserve">
    <value>Exclusion Range-Options</value>
  </data>
  <data name="ResExpandAll" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="ResExport" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="ResExport1" xml:space="preserve">
    <value>Export </value>
  </data>
  <data name="ResExport11" xml:space="preserve">
    <value>Export </value>
  </data>
  <data name="ResExport2" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="ResExportMedia" xml:space="preserve">
    <value>Export Media</value>
  </data>
  <data name="ResExporttoExcel" xml:space="preserve">
    <value>Export to Excel</value>
  </data>
  <data name="ResExporttoExcel1" xml:space="preserve">
    <value>Export to Excel</value>
  </data>
  <data name="ResExtension1" xml:space="preserve">
    <value>Ext1</value>
  </data>
  <data name="ResExtension2" xml:space="preserve">
    <value>Ext2</value>
  </data>
  <data name="ResExternal" xml:space="preserve">
    <value>External</value>
  </data>
  <data name="ResExternalType" xml:space="preserve">
    <value>External Type</value>
  </data>
  <data name="ResFailedToSend" xml:space="preserve">
    <value>Failed to send</value>
  </data>
  <data name="ResFGList" xml:space="preserve">
    <value>FG List</value>
  </data>
  <data name="ResFGList1" xml:space="preserve">
    <value>FG List</value>
  </data>
  <data name="ResFGName" xml:space="preserve">
    <value>FG Name</value>
  </data>
  <data name="ResFGNameDoesNotExist" xml:space="preserve">
    <value>Function Group Does not Exist</value>
  </data>
  <data name="ResFGNameOrCodeDoesNotExist" xml:space="preserve">
    <value>Function Group Code Does Not Exist</value>
  </data>
  <data name="ResFGUsed" xml:space="preserve">
    <value>FG Used</value>
  </data>
  <data name="ResFGUsed1" xml:space="preserve">
    <value>FG Used</value>
  </data>
  <data name="ResFGUsedList" xml:space="preserve">
    <value>Function Group  List</value>
  </data>
  <data name="ResFieldName" xml:space="preserve">
    <value>Field Name</value>
  </data>
  <data name="ResFieldName1" xml:space="preserve">
    <value>Field Name</value>
  </data>
  <data name="ResFile" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="ResFileAlreadyExistsDoYouWantToReplace" xml:space="preserve">
    <value>File Already Exists Do You Want To Replace</value>
  </data>
  <data name="ResFileExtensionIcon" xml:space="preserve">
    <value>File Extension Icon</value>
  </data>
  <data name="ResFileName" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="ResFileName1" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="ResFileNameorURL" xml:space="preserve">
    <value>File Name or URL</value>
  </data>
  <data name="ResFileNameorURL1" xml:space="preserve">
    <value>File Name or URL</value>
  </data>
  <data name="ResFileSize" xml:space="preserve">
    <value>File Size</value>
  </data>
  <data name="ResFileType" xml:space="preserve">
    <value>File Type</value>
  </data>
  <data name="ResFileType1" xml:space="preserve">
    <value>File Type</value>
  </data>
  <data name="ResFinalVehicleRecords" xml:space="preserve">
    <value>Final Vehicle Records</value>
  </data>
  <data name="ResFirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="ResFirstName1" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="ResFirstPage" xml:space="preserve">
    <value>First Page</value>
  </data>
  <data name="ResFittowindow" xml:space="preserve">
    <value>Fit Image to window</value>
  </data>
  <data name="ResFittowindow1" xml:space="preserve">
    <value>Fit to window</value>
  </data>
  <data name="ResFitToWindow2" xml:space="preserve">
    <value>Fit To Window</value>
  </data>
  <data name="ResFlag(New)" xml:space="preserve">
    <value>Flag (New)?</value>
  </data>
  <data name="ResFlag(New)?" xml:space="preserve">
    <value>Is Flag(New)?</value>
  </data>
  <data name="ResFlag(No)" xml:space="preserve">
    <value>Flag (No)?</value>
  </data>
  <data name="ResFlag(No)?" xml:space="preserve">
    <value>Is Flag(No)?</value>
  </data>
  <data name="ResFlexi1" xml:space="preserve">
    <value>Flexi 1</value>
  </data>
  <data name="ResFlexi10" xml:space="preserve">
    <value>Flexi 10</value>
  </data>
  <data name="ResFlexi101" xml:space="preserve">
    <value>Flexi 10</value>
  </data>
  <data name="ResFlexi11" xml:space="preserve">
    <value>Flexi 1</value>
  </data>
  <data name="ResFlexi2" xml:space="preserve">
    <value>Flexi 2</value>
  </data>
  <data name="ResFlexi21" xml:space="preserve">
    <value>Flexi 2</value>
  </data>
  <data name="ResFlexi3" xml:space="preserve">
    <value>Flexi 3</value>
  </data>
  <data name="ResFlexi31" xml:space="preserve">
    <value>Flexi 3</value>
  </data>
  <data name="ResFlexi4" xml:space="preserve">
    <value>Flexi 4</value>
  </data>
  <data name="ResFlexi41" xml:space="preserve">
    <value>Flexi 4</value>
  </data>
  <data name="ResFlexi5" xml:space="preserve">
    <value>Flexi 5</value>
  </data>
  <data name="ResFlexi51" xml:space="preserve">
    <value>Flexi 5</value>
  </data>
  <data name="ResFlexi6" xml:space="preserve">
    <value>Flexi 6</value>
  </data>
  <data name="ResFlexi61" xml:space="preserve">
    <value>Flexi 6</value>
  </data>
  <data name="ResFlexi7" xml:space="preserve">
    <value>Flexi 7</value>
  </data>
  <data name="ResFlexi71" xml:space="preserve">
    <value>Flexi 7</value>
  </data>
  <data name="ResFlexi8" xml:space="preserve">
    <value>Flexi 8</value>
  </data>
  <data name="ResFlexi81" xml:space="preserve">
    <value>Flexi 8</value>
  </data>
  <data name="ResFlexi9" xml:space="preserve">
    <value>Flexi 9</value>
  </data>
  <data name="ResFlexi91" xml:space="preserve">
    <value>Flexi 9</value>
  </data>
  <data name="ResFlexiFields" xml:space="preserve">
    <value>Flexi Fields</value>
  </data>
  <data name="ResFont" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="ResFont1" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="ResFontColor" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="ResFontColor1" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="ResFontColour" xml:space="preserve">
    <value>Font Colour</value>
  </data>
  <data name="ResFontColour1" xml:space="preserve">
    <value>Font Colour</value>
  </data>
  <data name="ResFontDetails" xml:space="preserve">
    <value>Font Details</value>
  </data>
  <data name="ResFontDetails1" xml:space="preserve">
    <value>Font Details</value>
  </data>
  <data name="ResFontHistory" xml:space="preserve">
    <value>Font History</value>
  </data>
  <data name="ResFontHistory1" xml:space="preserve">
    <value>Font History</value>
  </data>
  <data name="ResFontHistoryList" xml:space="preserve">
    <value>Font History</value>
  </data>
  <data name="ResFontHistoryList1" xml:space="preserve">
    <value>Font History</value>
  </data>
  <data name="ResFontList" xml:space="preserve">
    <value>Font List</value>
  </data>
  <data name="ResFontList1" xml:space="preserve">
    <value>Font List</value>
  </data>
  <data name="ResFontName" xml:space="preserve">
    <value>FontName</value>
  </data>
  <data name="ResFontName1" xml:space="preserve">
    <value>Font Name</value>
  </data>
  <data name="ResFontNameSearchList" xml:space="preserve">
    <value>Font Name Search List</value>
  </data>
  <data name="ResFontNameSearchList1" xml:space="preserve">
    <value>Font Name Search List</value>
  </data>
  <data name="ResFontPreview" xml:space="preserve">
    <value>Font Preview</value>
  </data>
  <data name="ResFontSize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="ResFontSize1" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="ResFontStyle" xml:space="preserve">
    <value>Font Style</value>
  </data>
  <data name="ResFontStyle1" xml:space="preserve">
    <value>Font Style</value>
  </data>
  <data name="ResFontTemplate" xml:space="preserve">
    <value>Font Template</value>
  </data>
  <data name="ResFontTemplate1" xml:space="preserve">
    <value>Font Template</value>
  </data>
  <data name="ResFontTemplateName" xml:space="preserve">
    <value>Font Template Name</value>
  </data>
  <data name="ResFontTemplates" xml:space="preserve">
    <value>Font Templates</value>
  </data>
  <data name="ResFontTemplates1" xml:space="preserve">
    <value>Font Templates</value>
  </data>
  <data name="ResForgotPassword" xml:space="preserve">
    <value>I forgot my password</value>
  </data>
  <data name="ResFormatter" xml:space="preserve">
    <value>Formatter</value>
  </data>
  <data name="ResFoundInCatalogue" xml:space="preserve">
    <value>Found In Catalogue</value>
  </data>
  <data name="ResFoundInCatalogue1" xml:space="preserve">
    <value>Found In Catalogue</value>
  </data>
  <data name="ResFreeLine" xml:space="preserve">
    <value>Free Line</value>
  </data>
  <data name="ResFrench" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="ResFrenchDocument" xml:space="preserve">
    <value>French Document Path</value>
  </data>
  <data name="ResFrenchLanguage" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="ResFrequentlyPurchasedParts" xml:space="preserve">
    <value>Frequently Purchased Parts</value>
  </data>
  <data name="ResFrequentlyPurchaseHistory" xml:space="preserve">
    <value>Frequently Purchase History</value>
  </data>
  <data name="ResFrom" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="ResFromMFGCode" xml:space="preserve">
    <value>From MFG Code</value>
  </data>
  <data name="ResFromMFGCode1" xml:space="preserve">
    <value>From MFG Code</value>
  </data>
  <data name="ResFromPart#" xml:space="preserve">
    <value>From Part #</value>
  </data>
  <data name="ResFromPart#1" xml:space="preserve">
    <value>From Part #</value>
  </data>
  <data name="ResFromPartNumber" xml:space="preserve">
    <value>From Part Number</value>
  </data>
  <data name="ResFromPartNumber1" xml:space="preserve">
    <value>From Part Number</value>
  </data>
  <data name="ResFromQty" xml:space="preserve">
    <value>From Qty.</value>
  </data>
  <data name="ResFromQty1" xml:space="preserve">
    <value>From Qty.</value>
  </data>
  <data name="ResFromSN" xml:space="preserve">
    <value>From SN</value>
  </data>
  <data name="ResFromSNShortCode" xml:space="preserve">
    <value>From SN Short Code</value>
  </data>
  <data name="ResFromTo" xml:space="preserve">
    <value>From To</value>
  </data>
  <data name="ResFunctionGroup" xml:space="preserve">
    <value>Function Group</value>
  </data>
  <data name="ResFunctionGroup1" xml:space="preserve">
    <value>Function Group</value>
  </data>
  <data name="ResFunctionGroupAttacments" xml:space="preserve">
    <value>Function Group Attachments</value>
  </data>
  <data name="ResFunctionGroupAttacments1" xml:space="preserve">
    <value>Function Group Attachments</value>
  </data>
  <data name="ResFunctionGroupDetails" xml:space="preserve">
    <value>Function Group Details</value>
  </data>
  <data name="ResFunctionGroupImages" xml:space="preserve">
    <value>Function Group Images</value>
  </data>
  <data name="ResFunctionGroupLanguageList" xml:space="preserve">
    <value>Function Group Language List</value>
  </data>
  <data name="ResFunctionGroupList" xml:space="preserve">
    <value>Function Group List</value>
  </data>
  <data name="ResFunctionGroupList1" xml:space="preserve">
    <value>Function Group List</value>
  </data>
  <data name="ResFunctionGroups" xml:space="preserve">
    <value>Function Groups</value>
  </data>
  <data name="ResFunctionGroupUsed" xml:space="preserve">
    <value>Function Group Used</value>
  </data>
  <data name="ResFunctionGroupUsedList" xml:space="preserve">
    <value>Function Group Used List</value>
  </data>
  <data name="ResGenerate" xml:space="preserve">
    <value>Generate</value>
  </data>
  <data name="ResGermanDocument" xml:space="preserve">
    <value>German Document Path</value>
  </data>
  <data name="ResGivenNumberAlreadyAvailableInParts" xml:space="preserve">
    <value>Given Number already available in Parts</value>
  </data>
  <data name="ResGreaterorEqualto" xml:space="preserve">
    <value>Greater or Equal to</value>
  </data>
  <data name="ResGreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="ResGreaterThan1" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="ResGreaterThanorEqualto" xml:space="preserve">
    <value>Greater Than or Equal to</value>
  </data>
  <data name="ResGreaterThanorEqualto1" xml:space="preserve">
    <value>Greater Than or Equal to</value>
  </data>
  <data name="ResGreaterThen" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="ResGUIConfig" xml:space="preserve">
    <value>GUI Config</value>
  </data>
  <data name="ResGUIConfig_ID" xml:space="preserve">
    <value>GUIConfig_ID</value>
  </data>
  <data name="ResHasImageAttachments" xml:space="preserve">
    <value>Has Image Attachments</value>
  </data>
  <data name="ResHasImageAttachments1" xml:space="preserve">
    <value>Has Image Attachments</value>
  </data>
  <data name="ResHasPartAttachments" xml:space="preserve">
    <value>Has Part Attachments</value>
  </data>
  <data name="ResHasPartAttachments1" xml:space="preserve">
    <value>Has Part Attachments</value>
  </data>
  <data name="ResHasRecommendedPart" xml:space="preserve">
    <value>Has Recommended Part</value>
  </data>
  <data name="ResHasRecommendedPart1" xml:space="preserve">
    <value>Has Recommended Part</value>
  </data>
  <data name="ResHasSetMemberParts" xml:space="preserve">
    <value>Has Set Member Parts</value>
  </data>
  <data name="ResHasSetMemberParts1" xml:space="preserve">
    <value>Has Set Member Parts</value>
  </data>
  <data name="ResHasSupersessionornot" xml:space="preserve">
    <value>Has Supersession or not</value>
  </data>
  <data name="ResHasSupersessionornot1" xml:space="preserve">
    <value>Has Supersession or not</value>
  </data>
  <data name="ResHasVersion" xml:space="preserve">
    <value>Has Version?</value>
  </data>
  <data name="ResHexagon" xml:space="preserve">
    <value>Hexagon</value>
  </data>
  <data name="ResHideAssemblyNo" xml:space="preserve">
    <value>Hide Assembly #</value>
  </data>
  <data name="ResHideItem#" xml:space="preserve">
    <value>Hide Item #</value>
  </data>
  <data name="ResHighlighter" xml:space="preserve">
    <value>Highlight Image</value>
  </data>
  <data name="ResHighlighter1" xml:space="preserve">
    <value>Highlighter</value>
  </data>
  <data name="ResHistory" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="ResHistory1" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="ResHome" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="ResHorizontalSplitter" xml:space="preserve">
    <value>Horizontal Splitter</value>
  </data>
  <data name="ResHorizontalSplitter1" xml:space="preserve">
    <value>Horizontal Splitter</value>
  </data>
  <data name="ResHotspot" xml:space="preserve">
    <value>Hotspot</value>
  </data>
  <data name="ResHotspot1" xml:space="preserve">
    <value>Hotspot</value>
  </data>
  <data name="ResHotspot?" xml:space="preserve">
    <value>Is Hotspot?</value>
  </data>
  <data name="ResHotspot?1" xml:space="preserve">
    <value>Is Hotspot?</value>
  </data>
  <data name="ResHotspotDelete" xml:space="preserve">
    <value>Hotspot Delete</value>
  </data>
  <data name="ResHotspotDelete1" xml:space="preserve">
    <value>Hotspot Delete</value>
  </data>
  <data name="ResHotspotdone" xml:space="preserve">
    <value>Hotspot done</value>
  </data>
  <data name="ResHotspotdone1" xml:space="preserve">
    <value>Hotspot done</value>
  </data>
  <data name="ResHotSpotsCopy" xml:space="preserve">
    <value>Copy Hotspot</value>
  </data>
  <data name="ResHotspotShapes" xml:space="preserve">
    <value>Hotspot Shapes</value>
  </data>
  <data name="ResHotspotShapes1" xml:space="preserve">
    <value>Hotspot Shapes</value>
  </data>
  <data name="ResHotSpotsMove" xml:space="preserve">
    <value>Move Hotspot</value>
  </data>
  <data name="ResHotSpotsonoff" xml:space="preserve">
    <value>Hotspots [On/Off]</value>
  </data>
  <data name="ResID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="ResIgnoredBy" xml:space="preserve">
    <value>Ignored By</value>
  </data>
  <data name="ResIgnoredDateTime" xml:space="preserve">
    <value>Ignored Date </value>
  </data>
  <data name="ResIgnoreErrorAndPublish" xml:space="preserve">
    <value>Ignore &amp;  Publish</value>
  </data>
  <data name="ResIgnoreforPartsCatalogue" xml:space="preserve">
    <value>Ignore for Parts Catalogue</value>
  </data>
  <data name="ResIgnoreforPartsCatalogue1" xml:space="preserve">
    <value>Ignore for Parts Catalogue</value>
  </data>
  <data name="ResIgnoreforServiceManuals" xml:space="preserve">
    <value>Ignore for Service Manuals</value>
  </data>
  <data name="ResIgnoreforServiceManuals1" xml:space="preserve">
    <value>Ignore for Service Manuals</value>
  </data>
  <data name="ResIhopethattheinformationsuppliedisadequateIfIcanbeofanyfurtherhelporhavenotmademyselfclearpleasedonthesitatetocommunicatewithmeatyourconvenience" xml:space="preserve">
    <value>I hope that the information supplied is adequate. If I can be of any further help, or have not made myself clear, please don't hesitate to communicate with me at your convenience.</value>
  </data>
  <data name="ResImage" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="ResImg" xml:space="preserve">
    <value>Img</value>
  </data>
  <data name="ResImg1" xml:space="preserve">
    <value>Img</value>
  </data>
  <data name="ResImport" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="ResImport1" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="ResImportAllCustomer" xml:space="preserve">
    <value>Import All Customer</value>
  </data>
  <data name="ResImportAllOrder" xml:space="preserve">
    <value>Import All Order</value>
  </data>
  <data name="ResImportAssembly&amp;Parts" xml:space="preserve">
    <value>Import Assembly &amp; Parts</value>
  </data>
  <data name="ResImportAssemblyFile" xml:space="preserve">
    <value>Import Assembly File</value>
  </data>
  <data name="ResImportAssemblyFile1" xml:space="preserve">
    <value>Import Assembly File</value>
  </data>
  <data name="ResImportAssemblyParts" xml:space="preserve">
    <value>Import Assembly Parts</value>
  </data>
  <data name="ResImportBOM" xml:space="preserve">
    <value>Import BOM</value>
  </data>
  <data name="ResImportBOMfromExcel" xml:space="preserve">
    <value>Import BOM from Excel</value>
  </data>
  <data name="ResImportBOMfromExcel1" xml:space="preserve">
    <value>Import BOM from Excel</value>
  </data>
  <data name="ResImportCustomerPartsFile" xml:space="preserve">
    <value>Import Customer Part File</value>
  </data>
  <data name="ResImportCustomerPartsFile1" xml:space="preserve">
    <value>Import Customer Part File</value>
  </data>
  <data name="ResImportCustomerPartsInfo" xml:space="preserve">
    <value>Import Customer Parts Info</value>
  </data>
  <data name="ResImportCustomerPartsInformation" xml:space="preserve">
    <value>Import Customer Parts Information</value>
  </data>
  <data name="ResimportDrawing" xml:space="preserve">
    <value>Import Drawing</value>
  </data>
  <data name="ResimportDrawing1" xml:space="preserve">
    <value>Import Drawing</value>
  </data>
  <data name="ResImportedBy" xml:space="preserve">
    <value>Imported By</value>
  </data>
  <data name="ResImportedBy1" xml:space="preserve">
    <value>Imported By</value>
  </data>
  <data name="ResImportedCount" xml:space="preserve">
    <value>Imported Count</value>
  </data>
  <data name="ResImportedCount1" xml:space="preserve">
    <value>Imported Count</value>
  </data>
  <data name="ResImportedFileName" xml:space="preserve">
    <value>Imported File Name</value>
  </data>
  <data name="ResImportFile" xml:space="preserve">
    <value>Import File</value>
  </data>
  <data name="ResImportFile1" xml:space="preserve">
    <value>Import File</value>
  </data>
  <data name="ResImportFileFormat" xml:space="preserve">
    <value>Import File Format</value>
  </data>
  <data name="ResImportFileFormat1" xml:space="preserve">
    <value>Import File Format</value>
  </data>
  <data name="ResImportLog" xml:space="preserve">
    <value>Import Log</value>
  </data>
  <data name="ResImportLog1" xml:space="preserve">
    <value>Import Log</value>
  </data>
  <data name="ResImportNewRoad#" xml:space="preserve">
    <value>Import Road#</value>
  </data>
  <data name="ResImportOptions" xml:space="preserve">
    <value>Import Options</value>
  </data>
  <data name="ResImportPartsFile" xml:space="preserve">
    <value>Import Parts File</value>
  </data>
  <data name="ResImportPartsFile1" xml:space="preserve">
    <value>Import Parts File</value>
  </data>
  <data name="ResImportS upersession" xml:space="preserve">
    <value />
  </data>
  <data name="ResImportSectionFile" xml:space="preserve">
    <value>Import Section File</value>
  </data>
  <data name="ResImportSectionFile1" xml:space="preserve">
    <value>Import Section File</value>
  </data>
  <data name="ResImportSectionfromExcel" xml:space="preserve">
    <value>Import Section from Excel</value>
  </data>
  <data name="ResImportSectionfromExcel1" xml:space="preserve">
    <value>Import Section from Excel</value>
  </data>
  <data name="ResImportSections" xml:space="preserve">
    <value>Import Sections</value>
  </data>
  <data name="ResImportSectionsfromExcel" xml:space="preserve">
    <value>Import Sections from Excel</value>
  </data>
  <data name="ResImportSectionsfromExcel1" xml:space="preserve">
    <value>Import Sections from Excel</value>
  </data>
  <data name="ResImportShoppingCart" xml:space="preserve">
    <value>Import Shopping Cart</value>
  </data>
  <data name="ResImportSupersession" xml:space="preserve">
    <value>Import Supersession</value>
  </data>
  <data name="ResImportSupersessionPartFile" xml:space="preserve">
    <value>Import Supersession Parts File</value>
  </data>
  <data name="ResImportSupersessionPartFile1" xml:space="preserve">
    <value>Import Supersession Parts File</value>
  </data>
  <data name="ResImportTemplate" xml:space="preserve">
    <value>Import Template</value>
  </data>
  <data name="ResImportTemplate1" xml:space="preserve">
    <value>Import Template</value>
  </data>
  <data name="ResImportVARNETFile" xml:space="preserve">
    <value>Import VARNET File</value>
  </data>
  <data name="ResImportVendorPartsFile" xml:space="preserve">
    <value>Import Vendor Parts File</value>
  </data>
  <data name="ResImportVendorPartsFile1" xml:space="preserve">
    <value>Import Vendor Parts File</value>
  </data>
  <data name="ResImportVendorPartsInfo" xml:space="preserve">
    <value>Import Vendor Parts Info</value>
  </data>
  <data name="ResImportVendorPartsInformation" xml:space="preserve">
    <value>Import Vendor Parts Information</value>
  </data>
  <data name="ResImportVIN" xml:space="preserve">
    <value>Import VIN #</value>
  </data>
  <data name="ResImportVIN#" xml:space="preserve">
    <value>Import VIN #</value>
  </data>
  <data name="ResImportVIN#1" xml:space="preserve">
    <value>Import VIN #</value>
  </data>
  <data name="ResImportVINDetailsTemplate" xml:space="preserve">
    <value>Import VIN Details Template</value>
  </data>
  <data name="ResImportVINDetailsTemplate1" xml:space="preserve">
    <value>Import VIN Details Template</value>
  </data>
  <data name="ResImportVINfromExcel" xml:space="preserve">
    <value>Import VIN# from Excel</value>
  </data>
  <data name="ResInavlidModelName" xml:space="preserve">
    <value>Invalid Model Name</value>
  </data>
  <data name="ResIncludeetmembersinPartList" xml:space="preserve">
    <value>Include Set members in Part List?</value>
  </data>
  <data name="ResIncludeetmembersinPartList?" xml:space="preserve">
    <value>Include Set Members In Part List?</value>
  </data>
  <data name="ResIncludeInBOM" xml:space="preserve">
    <value>Include In Part List</value>
  </data>
  <data name="ResIncludeSetmemberInPartList" xml:space="preserve">
    <value>Set Parts in BOM?</value>
  </data>
  <data name="ResInclusion" xml:space="preserve">
    <value>Inclusion</value>
  </data>
  <data name="ResInclusionRange-Options" xml:space="preserve">
    <value>Inclusion Range-Options</value>
  </data>
  <data name="ResIndent" xml:space="preserve">
    <value>Indent</value>
  </data>
  <data name="ResIndent1" xml:space="preserve">
    <value>Indent</value>
  </data>
  <data name="ResInProgress" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="ResInProgress1" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="ResInputisNotInCorrectFormat" xml:space="preserve">
    <value>Section is not in correct format</value>
  </data>
  <data name="ResInspectionandTestPlan" xml:space="preserve">
    <value>Inspection and Test Plan</value>
  </data>
  <data name="ResInternal" xml:space="preserve">
    <value>Internal</value>
  </data>
  <data name="ResInternal1" xml:space="preserve">
    <value>Internal</value>
  </data>
  <data name="ResInternalAttachments" xml:space="preserve">
    <value>Internal Attachments</value>
  </data>
  <data name="ResInternalAttachments1" xml:space="preserve">
    <value>Internal Attachments</value>
  </data>
  <data name="ResInternalNo" xml:space="preserve">
    <value>Internal No</value>
  </data>
  <data name="ResInvalidFile" xml:space="preserve">
    <value>Invalid File</value>
  </data>
  <data name="ResInvalidFileType" xml:space="preserve">
    <value>Invalid File Type</value>
  </data>
  <data name="ResInvalidOption" xml:space="preserve">
    <value>Invalid option</value>
  </data>
  <data name="ResInvalidVIN#" xml:space="preserve">
    <value>Invalid VIN #</value>
  </data>
  <data name="ResIsActive?" xml:space="preserve">
    <value>Is Active?</value>
  </data>
  <data name="ResIsActive?1" xml:space="preserve">
    <value>Is Active?</value>
  </data>
  <data name="ResIsanSubAssembly" xml:space="preserve">
    <value>Is an Sub Assembly</value>
  </data>
  <data name="ResIsanSubAssembly1" xml:space="preserve">
    <value>Is an Sub Assembly</value>
  </data>
  <data name="ResIsAss?" xml:space="preserve">
    <value>Is Ass?</value>
  </data>
  <data name="ResIsAss?1" xml:space="preserve">
    <value>Is Ass?</value>
  </data>
  <data name="ResIsAssembly" xml:space="preserve">
    <value>Is Assembly?</value>
  </data>
  <data name="ResIsAssembly?" xml:space="preserve">
    <value>Is Assembly?</value>
  </data>
  <data name="ResIsAssembly?1" xml:space="preserve">
    <value>Is Assembly?</value>
  </data>
  <data name="ResIsAssy" xml:space="preserve">
    <value>Is Assy.?</value>
  </data>
  <data name="ResIsBookmarked" xml:space="preserve">
    <value>Is Bookmarked</value>
  </data>
  <data name="ResIsBookmarked1" xml:space="preserve">
    <value>Is Bookmarked</value>
  </data>
  <data name="ResIsBookMarked?" xml:space="preserve">
    <value>Is Book Marked</value>
  </data>
  <data name="ResIsBookMarked?1" xml:space="preserve">
    <value>Is Book Marked</value>
  </data>
  <data name="ResIsBookmarkForSection?" xml:space="preserve">
    <value>Is Bookmark For Section?</value>
  </data>
  <data name="ResIsBookmarkForSection?1" xml:space="preserve">
    <value>Is Bookmark For Section?</value>
  </data>
  <data name="ResIsDefault?" xml:space="preserve">
    <value>Is Default?</value>
  </data>
  <data name="ResIsDefault?1" xml:space="preserve">
    <value>Is Default?</value>
  </data>
  <data name="ResIsEmpty" xml:space="preserve">
    <value>Is Empty</value>
  </data>
  <data name="ResIsEmpty1" xml:space="preserve">
    <value>Is Empty</value>
  </data>
  <data name="ResIsEqual" xml:space="preserve">
    <value>Is Equal</value>
  </data>
  <data name="ResIsEqual1" xml:space="preserve">
    <value>Is Equal</value>
  </data>
  <data name="ResIsHotspot?" xml:space="preserve">
    <value>Is Hotspot?</value>
  </data>
  <data name="ResIsLock" xml:space="preserve">
    <value>Is Lock?</value>
  </data>
  <data name="ResIsLock?" xml:space="preserve">
    <value>Is Lock?</value>
  </data>
  <data name="ResIsLock?1" xml:space="preserve">
    <value>Is Lock?</value>
  </data>
  <data name="ResIsLocked?" xml:space="preserve">
    <value>Is Locked?</value>
  </data>
  <data name="ResIsLocked?1" xml:space="preserve">
    <value>Is Locked?</value>
  </data>
  <data name="ResIsMandatory" xml:space="preserve">
    <value>Is Mandatory</value>
  </data>
  <data name="ResIsPartSection?" xml:space="preserve">
    <value>Is Part Sec.?</value>
  </data>
  <data name="ResIsPartSection?1" xml:space="preserve">
    <value>Is Part Section?</value>
  </data>
  <data name="ResIsPrecedingPart?" xml:space="preserve">
    <value>Is Preceding Part?</value>
  </data>
  <data name="ResIsPrecedingPart?1" xml:space="preserve">
    <value>Is Preceding Part?</value>
  </data>
  <data name="ResIsPrimary?" xml:space="preserve">
    <value>Is Primary?</value>
  </data>
  <data name="ResIsPrimary?1" xml:space="preserve">
    <value>Is Primary?</value>
  </data>
  <data name="ResIsPublished?" xml:space="preserve">
    <value>Is Published?</value>
  </data>
  <data name="ResIsPublished?1" xml:space="preserve">
    <value>Is Published?</value>
  </data>
  <data name="ResIsPurchasable" xml:space="preserve">
    <value>Is Purchasable?</value>
  </data>
  <data name="ResIsPurchasable?" xml:space="preserve">
    <value>Is Purchasable?</value>
  </data>
  <data name="ResIsPurchasable?1" xml:space="preserve">
    <value>Is Purchasable?</value>
  </data>
  <data name="ResIsRecommendedPart" xml:space="preserve">
    <value>Has Recommended Part?</value>
  </data>
  <data name="ResIsRecommendedPart?" xml:space="preserve">
    <value>Has Recom.?</value>
  </data>
  <data name="ResIsRecommendedPart?1" xml:space="preserve">
    <value>Has Recommended Part?</value>
  </data>
  <data name="ResIsReferred?" xml:space="preserve">
    <value>Is Referred?</value>
  </data>
  <data name="ResIsReferred?1" xml:space="preserve">
    <value>Is Referred?</value>
  </data>
  <data name="ResIsSection" xml:space="preserve">
    <value>Is Section?</value>
  </data>
  <data name="ResIsSection?" xml:space="preserve">
    <value>Is Sec.?</value>
  </data>
  <data name="ResIsSection?1" xml:space="preserve">
    <value>Is Main Section?</value>
  </data>
  <data name="ResIsServiceSection" xml:space="preserve">
    <value>Is Service Section?</value>
  </data>
  <data name="ResIsServiceSection?" xml:space="preserve">
    <value>Is Ser Sec.?</value>
  </data>
  <data name="ResIsServiceSection?1" xml:space="preserve">
    <value>Is Service Section?</value>
  </data>
  <data name="ResIsSetMember" xml:space="preserve">
    <value>Has Set Member?</value>
  </data>
  <data name="ResIsSetMember?" xml:space="preserve">
    <value>Has Set Mem.?</value>
  </data>
  <data name="ResIsSetMember?1" xml:space="preserve">
    <value>Has Set Member?</value>
  </data>
  <data name="ResIsSubSection" xml:space="preserve">
    <value>Is Sub Section?</value>
  </data>
  <data name="ResIsSupersedingPart?" xml:space="preserve">
    <value>Is Superseding Part?</value>
  </data>
  <data name="ResIsSupersedingPart?1" xml:space="preserve">
    <value>Is Superseding Part?</value>
  </data>
  <data name="ResIsSuperseededPart?" xml:space="preserve">
    <value>Is Superseded Part?</value>
  </data>
  <data name="ResIsSupersession?" xml:space="preserve">
    <value> Is Supersession?</value>
  </data>
  <data name="ResIsSupersession?1" xml:space="preserve">
    <value>Is Supersession?</value>
  </data>
  <data name="ResIsSupersession?11" xml:space="preserve">
    <value>Is Supersession?</value>
  </data>
  <data name="ResIsSupersession?2" xml:space="preserve">
    <value> Is Supersession?</value>
  </data>
  <data name="ResIsTopLevel" xml:space="preserve">
    <value>Is Top Level?</value>
  </data>
  <data name="ResIsTopLevel?" xml:space="preserve">
    <value>Is Top Level?</value>
  </data>
  <data name="ResIsTopLevel?1" xml:space="preserve">
    <value>Is Top Level?</value>
  </data>
  <data name="ResIsURL" xml:space="preserve">
    <value>Is URL?</value>
  </data>
  <data name="ResIsVisiable?" xml:space="preserve">
    <value>Is Visible?</value>
  </data>
  <data name="ResIsVisible" xml:space="preserve">
    <value>Is Visible?</value>
  </data>
  <data name="ResIsVisible?" xml:space="preserve">
    <value>Is Visible?</value>
  </data>
  <data name="ResIsVisible?1" xml:space="preserve">
    <value>Is Visible?</value>
  </data>
  <data name="ResIs_PartDuplicate" xml:space="preserve">
    <value>Is Part Duplicate?</value>
  </data>
  <data name="ResIs_Supersession?" xml:space="preserve">
    <value>Is Supersession?</value>
  </data>
  <data name="ResItem" xml:space="preserve">
    <value>Item #</value>
  </data>
  <data name="ResItem#" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="ResItem#1" xml:space="preserve">
    <value>Item #</value>
  </data>
  <data name="ResItem1" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="ResItemNumber" xml:space="preserve">
    <value>Item Number</value>
  </data>
  <data name="ResLanguage" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="ResLanguage1" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="ResLanguageCode" xml:space="preserve">
    <value>Language Code</value>
  </data>
  <data name="ResLanguageCode1" xml:space="preserve">
    <value>Language Code</value>
  </data>
  <data name="ResLanguageList" xml:space="preserve">
    <value>Language List</value>
  </data>
  <data name="ResLanguageList1" xml:space="preserve">
    <value>Language List</value>
  </data>
  <data name="ResLanguageName" xml:space="preserve">
    <value>Language Name</value>
  </data>
  <data name="ResLanguageName1" xml:space="preserve">
    <value>Language Name</value>
  </data>
  <data name="ResLanguageNameSearchList" xml:space="preserve">
    <value>Language Name Search List</value>
  </data>
  <data name="ResLanguageNameSearchList1" xml:space="preserve">
    <value>Language Name Search List</value>
  </data>
  <data name="ResLastModifiedDate" xml:space="preserve">
    <value>Last Modified Date</value>
  </data>
  <data name="ResLastModifiedDate1" xml:space="preserve">
    <value>Last Modified Date</value>
  </data>
  <data name="ResLastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="ResLastName1" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="ResLastPage" xml:space="preserve">
    <value>Last Page</value>
  </data>
  <data name="ResLastPurchase" xml:space="preserve">
    <value>Last Purchase</value>
  </data>
  <data name="ResLastUpdatedBy" xml:space="preserve">
    <value>Last Updated By</value>
  </data>
  <data name="ResLastUpdatedBy1" xml:space="preserve">
    <value>Last Updated By</value>
  </data>
  <data name="ResLastupdateddateLog" xml:space="preserve">
    <value>Last updated date Log</value>
  </data>
  <data name="ResLastupdateddateLog1" xml:space="preserve">
    <value>Last updated date Log</value>
  </data>
  <data name="ResLatestVersion" xml:space="preserve">
    <value>Latest Version</value>
  </data>
  <data name="ResLess" xml:space="preserve">
    <value>Show Less</value>
  </data>
  <data name="ResLess1" xml:space="preserve">
    <value>Less</value>
  </data>
  <data name="ResLessorEqualto" xml:space="preserve">
    <value>Less or Equal to</value>
  </data>
  <data name="ResLessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="ResLessThan1" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="ResLessthanorEqualto" xml:space="preserve">
    <value>Less than or Equal to</value>
  </data>
  <data name="ResLessthanorEqualto1" xml:space="preserve">
    <value>Less than or Equal to</value>
  </data>
  <data name="ResLevel" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="ResLevel1" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="ResLineThickness" xml:space="preserve">
    <value>Line thickness</value>
  </data>
  <data name="ResLinkHotSpots" xml:space="preserve">
    <value>Link Hotspots</value>
  </data>
  <data name="ResLinkingHotspots" xml:space="preserve">
    <value>Linking Hotspots</value>
  </data>
  <data name="ResList" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="ResList1" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="ResListAnnotation" xml:space="preserve">
    <value>List Annotation</value>
  </data>
  <data name="ResListOfAssemblies" xml:space="preserve">
    <value>List Of Assemblies</value>
  </data>
  <data name="ResListOfAssemblies1" xml:space="preserve">
    <value>List Of Assemblies</value>
  </data>
  <data name="ResListofOptions" xml:space="preserve">
    <value>List of Options</value>
  </data>
  <data name="ResListofOptions1" xml:space="preserve">
    <value>List of Options</value>
  </data>
  <data name="ResListOfPartAssemblies" xml:space="preserve">
    <value>List Of Part Assemblies</value>
  </data>
  <data name="ResListofParts" xml:space="preserve">
    <value>List of Parts</value>
  </data>
  <data name="ResListofParts1" xml:space="preserve">
    <value>List of Parts</value>
  </data>
  <data name="ResLoading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="ResLoadPartsFrom" xml:space="preserve">
    <value>Load Parts From</value>
  </data>
  <data name="ResLocalCodeNotFound" xml:space="preserve">
    <value>Local Code not found</value>
  </data>
  <data name="ResLocale" xml:space="preserve">
    <value>Locale</value>
  </data>
  <data name="ResLocalGlobalRemarks" xml:space="preserve">
    <value>Local / Global Remarks</value>
  </data>
  <data name="ResLocalNote" xml:space="preserve">
    <value>Local Note</value>
  </data>
  <data name="ResLocalNote1" xml:space="preserve">
    <value>Local Note</value>
  </data>
  <data name="ResLocalNoteCode" xml:space="preserve">
    <value>Local Note Code</value>
  </data>
  <data name="ResLocalNoteDetails" xml:space="preserve">
    <value>Local Note Details</value>
  </data>
  <data name="ResLocalNoteList" xml:space="preserve">
    <value>Local Note List</value>
  </data>
  <data name="ResLocalNotes" xml:space="preserve">
    <value>Local Notes</value>
  </data>
  <data name="ResLocalNotes1" xml:space="preserve">
    <value>Local Notes</value>
  </data>
  <data name="ResLocalNotesDetails" xml:space="preserve">
    <value>Local Notes Details</value>
  </data>
  <data name="ResLocalRemarks" xml:space="preserve">
    <value>Local Note Remarks</value>
  </data>
  <data name="ResLocalRemarksCopiedSuccessfully" xml:space="preserve">
    <value>Local Remarks copied successfully!!!</value>
  </data>
  <data name="ResLocalRemarksUpdation" xml:space="preserve">
    <value>Local Remarks Updation</value>
  </data>
  <data name="ResLocation" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="ResLock" xml:space="preserve">
    <value>Lock</value>
  </data>
  <data name="ResLock1" xml:space="preserve">
    <value>Lock</value>
  </data>
  <data name="ResLockedBy" xml:space="preserve">
    <value>Locked By</value>
  </data>
  <data name="ResLockedBy1" xml:space="preserve">
    <value>Locked By</value>
  </data>
  <data name="ResLockedDate" xml:space="preserve">
    <value>Locked Date</value>
  </data>
  <data name="ResLockedDate1" xml:space="preserve">
    <value>Locked Date</value>
  </data>
  <data name="ResLockList" xml:space="preserve">
    <value>Lock List</value>
  </data>
  <data name="ResLockList1" xml:space="preserve">
    <value>Lock List</value>
  </data>
  <data name="ResLockLog" xml:space="preserve">
    <value>Lock Log</value>
  </data>
  <data name="ResLockLog1" xml:space="preserve">
    <value>Lock Log</value>
  </data>
  <data name="ResLockLogDetails" xml:space="preserve">
    <value>Lock Log Details</value>
  </data>
  <data name="ResLockUnlock" xml:space="preserve">
    <value>Lock / Unlock</value>
  </data>
  <data name="ResLockUnlock1" xml:space="preserve">
    <value>Lock / Unlock</value>
  </data>
  <data name="ResLockUnlockLog" xml:space="preserve">
    <value>Lock/Unlock Log</value>
  </data>
  <data name="ResLockUnlockLog1" xml:space="preserve">
    <value>Lock/Unlock Log</value>
  </data>
  <data name="ResLogin" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="ResLoginID" xml:space="preserve">
    <value>Login ID / Email - ID</value>
  </data>
  <data name="ResLoginID1" xml:space="preserve">
    <value>Login ID</value>
  </data>
  <data name="ResLogout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="ResMachedCodes" xml:space="preserve">
    <value>Matched Codes</value>
  </data>
  <data name="ResMachedCodes1" xml:space="preserve">
    <value>Matched Codes</value>
  </data>
  <data name="ResMainSection" xml:space="preserve">
    <value>Main Section</value>
  </data>
  <data name="ResMainSectionDetails" xml:space="preserve">
    <value>Main Section Details</value>
  </data>
  <data name="ResMainSectionLanguageList" xml:space="preserve">
    <value>Main Section Language List</value>
  </data>
  <data name="ResMainSectionList" xml:space="preserve">
    <value>Main Section List</value>
  </data>
  <data name="ResMaintenanceManuals" xml:space="preserve">
    <value>Maintenance Manuals</value>
  </data>
  <data name="ResManageBookmarks" xml:space="preserve">
    <value>View and Manage Bookmarks</value>
  </data>
  <data name="ResManageBookmarks1" xml:space="preserve">
    <value>Manage Bookmarks</value>
  </data>
  <data name="ResManageDrawingGUIConfigure" xml:space="preserve">
    <value>Manage Drawing GUI Configuration</value>
  </data>
  <data name="ResManageDrawings" xml:space="preserve">
    <value>Manage Drawings</value>
  </data>
  <data name="ResManageDrawings1" xml:space="preserve">
    <value>Manage Drawings</value>
  </data>
  <data name="ResManageParts" xml:space="preserve">
    <value>Manage Parts</value>
  </data>
  <data name="ResManageParts1" xml:space="preserve">
    <value>Manage Parts</value>
  </data>
  <data name="ResManagePartSearchList" xml:space="preserve">
    <value>Manage Part Search List</value>
  </data>
  <data name="ResManagePartsGUIConfigure" xml:space="preserve">
    <value>Manage Parts GUI Configuration</value>
  </data>
  <data name="ResManageSection-Add" xml:space="preserve">
    <value>Manage Section-Add</value>
  </data>
  <data name="ResManageSection-Add1" xml:space="preserve">
    <value>Manage Section-Add</value>
  </data>
  <data name="ResManageSections" xml:space="preserve">
    <value>Manage Sections</value>
  </data>
  <data name="ResManageSections1" xml:space="preserve">
    <value>Manage Sections</value>
  </data>
  <data name="ResManufacturerCode" xml:space="preserve">
    <value>Manufacturer Code</value>
  </data>
  <data name="ResManufacturingYear" xml:space="preserve">
    <value>MFG Year</value>
  </data>
  <data name="ResManufacturingYear1" xml:space="preserve">
    <value>Manufacturing Year</value>
  </data>
  <data name="Resmanytomany" xml:space="preserve">
    <value>Many to many</value>
  </data>
  <data name="Resmanytoone" xml:space="preserve">
    <value>Many to one</value>
  </data>
  <data name="ResMaskingAction" xml:space="preserve">
    <value>Masking Action</value>
  </data>
  <data name="ResMaskingAction1" xml:space="preserve">
    <value>Masking Action</value>
  </data>
  <data name="ResMaskingActionLangaugeList" xml:space="preserve">
    <value>Masking Action Language List</value>
  </data>
  <data name="ResMaskingActionLanguageList" xml:space="preserve">
    <value>Masking action Language</value>
  </data>
  <data name="ResMaskingActionLanguageList1" xml:space="preserve">
    <value>Masking action Language</value>
  </data>
  <data name="ResMaskingActionList" xml:space="preserve">
    <value>Masking Action List</value>
  </data>
  <data name="ResMaskingActionList1" xml:space="preserve">
    <value>Masking Action List</value>
  </data>
  <data name="ResMaskingActions" xml:space="preserve">
    <value>Masking Actions</value>
  </data>
  <data name="ResMaskingActions1" xml:space="preserve">
    <value>Masking Actions</value>
  </data>
  <data name="ResMaskingActionsList" xml:space="preserve">
    <value>Masking Actions List</value>
  </data>
  <data name="ResMaskingActionsList1" xml:space="preserve">
    <value>Masking Actions List</value>
  </data>
  <data name="ResMaskingCode" xml:space="preserve">
    <value>Masking Code</value>
  </data>
  <data name="ResMaskingCode1" xml:space="preserve">
    <value>Masking Code</value>
  </data>
  <data name="ResMaskingCodeNotFound" xml:space="preserve">
    <value>Masking Code not found</value>
  </data>
  <data name="ResMaskingDescription" xml:space="preserve">
    <value>Masking Description</value>
  </data>
  <data name="ResMaskingDescription1" xml:space="preserve">
    <value>Masking Description</value>
  </data>
  <data name="ResMaskingDescriptionUpdation" xml:space="preserve">
    <value>Masking Description Updation</value>
  </data>
  <data name="ResMaskingDescriptionUpdation1" xml:space="preserve">
    <value>Masking Description Updation</value>
  </data>
  <data name="ResMaskingDetails" xml:space="preserve">
    <value>Masking Details</value>
  </data>
  <data name="ResMaskingDetails1" xml:space="preserve">
    <value>Masking Details</value>
  </data>
  <data name="ResMaskingNotes" xml:space="preserve">
    <value>Masking Notes</value>
  </data>
  <data name="ResMaskingNotes1" xml:space="preserve">
    <value>Masking Notes</value>
  </data>
  <data name="ResMaster" xml:space="preserve">
    <value>Master</value>
  </data>
  <data name="ResMaster1" xml:space="preserve">
    <value>Master</value>
  </data>
  <data name="ResMasterTransaction" xml:space="preserve">
    <value>Master / Transaction</value>
  </data>
  <data name="ResMauroCredali" xml:space="preserve">
    <value>Mauro Credali</value>
  </data>
  <data name="ResMaxZoomIn" xml:space="preserve">
    <value>Maximum Zoomed-In</value>
  </data>
  <data name="ResMaxZoomOut" xml:space="preserve">
    <value>Maximum Zoomed-Out</value>
  </data>
  <data name="ResMegEnterSectionName" xml:space="preserve">
    <value>Enter Section Name</value>
  </data>
  <data name="ResMenu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="ResMenu1" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="ResMFGCode" xml:space="preserve">
    <value>MFG Code</value>
  </data>
  <data name="ResMFGCode1" xml:space="preserve">
    <value>MFG Code</value>
  </data>
  <data name="ResMFGCodecannotbeempty" xml:space="preserve">
    <value>MFG Code cannot be empty</value>
  </data>
  <data name="ResMFGCodeDetails" xml:space="preserve">
    <value>MFG Code Details</value>
  </data>
  <data name="ResMFGCodeNameSearchList" xml:space="preserve">
    <value>MFG Code Name Search List</value>
  </data>
  <data name="ResMFGCodeNameSearchList1" xml:space="preserve">
    <value>MFG Code Name Search List</value>
  </data>
  <data name="ResMFGDetails" xml:space="preserve">
    <value>MFG Details</value>
  </data>
  <data name="ResMFGList" xml:space="preserve">
    <value>MFG List</value>
  </data>
  <data name="ResMFGList1" xml:space="preserve">
    <value>MFG List</value>
  </data>
  <data name="ResMFGName" xml:space="preserve">
    <value>MFG Name</value>
  </data>
  <data name="ResMFGName1" xml:space="preserve">
    <value>MFG Name</value>
  </data>
  <data name="ResMfrer" xml:space="preserve">
    <value>Mfrer</value>
  </data>
  <data name="ResMiddleName" xml:space="preserve">
    <value>Middle Name</value>
  </data>
  <data name="ResMiddleName1" xml:space="preserve">
    <value>Middle Name</value>
  </data>
  <data name="ResMngPartsSetItemNo" xml:space="preserve">
    <value>Set Item #</value>
  </data>
  <data name="ResMobile#" xml:space="preserve">
    <value>Mobile #</value>
  </data>
  <data name="ResMobile#1" xml:space="preserve">
    <value>Mobile #</value>
  </data>
  <data name="ResMobileNumber" xml:space="preserve">
    <value>Mobile Number</value>
  </data>
  <data name="ResMobileNumber1" xml:space="preserve">
    <value>Mobile Number</value>
  </data>
  <data name="ResModalImage" xml:space="preserve">
    <value>Modal Images</value>
  </data>
  <data name="ResModalImage1" xml:space="preserve">
    <value>Modal Images</value>
  </data>
  <data name="ResMode" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="ResMode1" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="ResModel" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="ResModel-Add" xml:space="preserve">
    <value>Model - Add</value>
  </data>
  <data name="ResModel-Add1" xml:space="preserve">
    <value>Model - Add</value>
  </data>
  <data name="ResModel1" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="ResModelAddNewName" xml:space="preserve">
    <value>Add New Modal</value>
  </data>
  <data name="ResModelAddNewName1" xml:space="preserve">
    <value>Add New Modal</value>
  </data>
  <data name="ResModelAttachments" xml:space="preserve">
    <value>Model Attachments</value>
  </data>
  <data name="ResModelCode" xml:space="preserve">
    <value>Model Code</value>
  </data>
  <data name="ResModelCode1" xml:space="preserve">
    <value>Model Code</value>
  </data>
  <data name="ResModelDetils" xml:space="preserve">
    <value>Model Details</value>
  </data>
  <data name="ResModelImages" xml:space="preserve">
    <value>Model Images</value>
  </data>
  <data name="ResModelImages1" xml:space="preserve">
    <value>Model Images</value>
  </data>
  <data name="ResModelList" xml:space="preserve">
    <value>Model List</value>
  </data>
  <data name="ResModelList1" xml:space="preserve">
    <value>Model List</value>
  </data>
  <data name="ResModelName" xml:space="preserve">
    <value>Model Name</value>
  </data>
  <data name="ResModelName1" xml:space="preserve">
    <value>Model Name</value>
  </data>
  <data name="ResModelNameSearchList" xml:space="preserve">
    <value>Model Name Search List</value>
  </data>
  <data name="ResModelNameSearchList1" xml:space="preserve">
    <value>Model Name Search List</value>
  </data>
  <data name="ResModifiedDate" xml:space="preserve">
    <value>Modified Date</value>
  </data>
  <data name="ResModifyQuery" xml:space="preserve">
    <value>Modify Query</value>
  </data>
  <data name="ResModifyQuery1" xml:space="preserve">
    <value>Modify Query</value>
  </data>
  <data name="ResModule" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="ResMore" xml:space="preserve">
    <value>Show More</value>
  </data>
  <data name="ResMore1" xml:space="preserve">
    <value>More</value>
  </data>
  <data name="ResMoreFields" xml:space="preserve">
    <value>More</value>
  </data>
  <data name="ResMostrecent" xml:space="preserve">
    <value>Most recent</value>
  </data>
  <data name="ResMostrecent1" xml:space="preserve">
    <value>Most recent</value>
  </data>
  <data name="ResMoveAnnotation" xml:space="preserve">
    <value>Move Annotation</value>
  </data>
  <data name="ResMsgAbbrevationDescription" xml:space="preserve">
    <value>Enter Abbreviation Description</value>
  </data>
  <data name="ResmsgAssemblyAlreadyBookmarked" xml:space="preserve">
    <value>Remove Bookmark</value>
  </data>
  <data name="ResMsgAssemblyNoCannotBeEmpty" xml:space="preserve">
    <value>Assembly No cannot be empty</value>
  </data>
  <data name="ResmsgbackupDate" xml:space="preserve">
    <value>Back up date </value>
  </data>
  <data name="ResMsgCombinationOfItemNoLvelSeqAlreadyExists" xml:space="preserve">
    <value>Combination of Item #, Level and Sequence already exists</value>
  </data>
  <data name="ResMsgCommonNotesHistory" xml:space="preserve">
    <value>Common Notes History</value>
  </data>
  <data name="ResMsgCommonNotesHistory1" xml:space="preserve">
    <value>Common Notes History</value>
  </data>
  <data name="ResMsgCompareAssembliesOnlyforselectedAssemblyFromAssemblyPartHeader" xml:space="preserve">
    <value>Compare Assemblies only for selected Assembly from Assembly Part Header</value>
  </data>
  <data name="ResMsgCopyRoleFrom" xml:space="preserve">
    <value />
  </data>
  <data name="ResMsgCopyRoleFrom1" xml:space="preserve">
    <value />
  </data>
  <data name="ResMsgCustomerCodeCannotBeEmpty" xml:space="preserve">
    <value>Customer Code cannot be empty</value>
  </data>
  <data name="ResMsgCustomerNameCannotBeEmpty" xml:space="preserve">
    <value>Customer Name cannot be empty</value>
  </data>
  <data name="ResMsgCustomerRerence#" xml:space="preserve">
    <value>Enter customer reference #</value>
  </data>
  <data name="ResMsgDatemustbeBiggerorEqualtotodaydate" xml:space="preserve">
    <value>Date must be bigger or equal to today date</value>
  </data>
  <data name="ResmsgDeletedCannotbeRecoverd" xml:space="preserve">
    <value>Records deleted cannot be recovered.</value>
  </data>
  <data name="ResMsgDependencyFound" xml:space="preserve">
    <value>Dependency Found Cannot Delete</value>
  </data>
  <data name="ResMsgDescriptionShouldbemandatory" xml:space="preserve">
    <value>Description should be mandatory</value>
  </data>
  <data name="ResMsgDetailsAlreadyExists" xml:space="preserve">
    <value>Details are already exists</value>
  </data>
  <data name="ResmsgDoyouwantoremove" xml:space="preserve">
    <value>Do you want to remove.</value>
  </data>
  <data name="ResmsgDoyouwantoreplace" xml:space="preserve">
    <value>Do you want to replace every where?</value>
  </data>
  <data name="ResmsgDuplicateImage" xml:space="preserve">
    <value>Duplicate Image</value>
  </data>
  <data name="ResMsgDuplicateItemNo" xml:space="preserve">
    <value>Duplicate Item #</value>
  </data>
  <data name="ResMsgDuplicateSequenceNo" xml:space="preserve">
    <value>Duplicate Sequence #</value>
  </data>
  <data name="ResMsgEnterAbbreviation" xml:space="preserve">
    <value>Enter Abbreviation</value>
  </data>
  <data name="ResMsgEnterAbbreviationDescription" xml:space="preserve">
    <value>Enter Abbreviation Description</value>
  </data>
  <data name="ResMsgEnterAbbreviationDescription1" xml:space="preserve">
    <value>Enter Abbreviation Description</value>
  </data>
  <data name="ResMsgEnterActivityName" xml:space="preserve">
    <value>Enter Activity Name</value>
  </data>
  <data name="ResMsgEnterActivityName1" xml:space="preserve">
    <value>Enter Activity Name</value>
  </data>
  <data name="ResMsgEnterAddress1" xml:space="preserve">
    <value>Enter Address1</value>
  </data>
  <data name="ResMsgEnterAddress2" xml:space="preserve">
    <value>Enter Address2</value>
  </data>
  <data name="ResMsgEnterAlternateDesc" xml:space="preserve">
    <value>Enter Alternate Description</value>
  </data>
  <data name="ResMsgEnterAlternateDesc1" xml:space="preserve">
    <value>Enter Alternate Description</value>
  </data>
  <data name="ResMsgEnterAlternatePart#" xml:space="preserve">
    <value>Enter Alternate Part #</value>
  </data>
  <data name="ResMsgEnterAlternatePart#1" xml:space="preserve">
    <value>Enter Alternate Part #</value>
  </data>
  <data name="ResMsgEnterAssembly#" xml:space="preserve">
    <value>Enter Assembly #</value>
  </data>
  <data name="ResMsgEnterAssembly#1" xml:space="preserve">
    <value>Enter Assembly #</value>
  </data>
  <data name="ResMsgEnterAssemblyName" xml:space="preserve">
    <value>Enter Assembly Name</value>
  </data>
  <data name="ResMsgEnterAssemblyName1" xml:space="preserve">
    <value>Enter Assembly Name</value>
  </data>
  <data name="ResMsgEnterBrandName" xml:space="preserve">
    <value>Enter Brand Name</value>
  </data>
  <data name="ResMsgEnterBrandName1" xml:space="preserve">
    <value>Enter Brand Name</value>
  </data>
  <data name="ResMsgEnterCartDate" xml:space="preserve">
    <value>Enter Cart Date</value>
  </data>
  <data name="ResMsgEnterCartDate1" xml:space="preserve">
    <value>Enter Cart Date</value>
  </data>
  <data name="ResMsgEnterCatalogueName" xml:space="preserve">
    <value>Enter Catalogue Name</value>
  </data>
  <data name="ResMsgEnterCatalogueName1" xml:space="preserve">
    <value>Enter Catalogue Name</value>
  </data>
  <data name="ResMsgEntercc" xml:space="preserve">
    <value>Enter CC</value>
  </data>
  <data name="ResMsgEnterCity" xml:space="preserve">
    <value>Enter City</value>
  </data>
  <data name="ResMsgEnterCode" xml:space="preserve">
    <value>Enter Code</value>
  </data>
  <data name="ResMsgEnterCode1" xml:space="preserve">
    <value>Enter Code</value>
  </data>
  <data name="ResMsgEnterColumnValue" xml:space="preserve">
    <value>Enter Column Value</value>
  </data>
  <data name="ResMsgEnterColumnValue1" xml:space="preserve">
    <value>Enter Column Value</value>
  </data>
  <data name="ResMsgEnterCommonNotes" xml:space="preserve">
    <value>Enter Common Notes</value>
  </data>
  <data name="ResMsgEnterCommonNotes1" xml:space="preserve">
    <value>Enter Common Notes</value>
  </data>
  <data name="ResMsgEnterConfirmPassword" xml:space="preserve">
    <value>Enter Confirm Password</value>
  </data>
  <data name="ResMsgEnterConfirmPassword1" xml:space="preserve">
    <value>Enter Confirm Password</value>
  </data>
  <data name="ResMsgEnterCurrentOwner" xml:space="preserve">
    <value>Enter Current Owner</value>
  </data>
  <data name="ResMsgEnterCurrentOwner1" xml:space="preserve">
    <value>Enter Current Owner</value>
  </data>
  <data name="ResMsgEnterCurrentPassword" xml:space="preserve">
    <value>Enter Current Password</value>
  </data>
  <data name="ResMsgEnterCurrentPassword1" xml:space="preserve">
    <value>Enter Current Password</value>
  </data>
  <data name="ResMsgEnterCustomer" xml:space="preserve">
    <value>Enter Customer</value>
  </data>
  <data name="ResMsgEnterCustomer1" xml:space="preserve">
    <value>Enter Customer</value>
  </data>
  <data name="ResMsgEnterCustomerCode" xml:space="preserve">
    <value>Enter Customer Code</value>
  </data>
  <data name="ResMsgEnterCustomerCode1" xml:space="preserve">
    <value>Enter Customer Code</value>
  </data>
  <data name="ResMsgEnterCustomerMFGCode" xml:space="preserve">
    <value>Enter Customer MFG Code</value>
  </data>
  <data name="ResMsgEnterCustomerMFGCode1" xml:space="preserve">
    <value>Enter Customer MFG Code</value>
  </data>
  <data name="ResMsgEnterCustomerName" xml:space="preserve">
    <value>Enter Customer Name</value>
  </data>
  <data name="ResMsgEnterCustomerName1" xml:space="preserve">
    <value>Enter Customer Name</value>
  </data>
  <data name="ResMsgEnterCustomerPart#" xml:space="preserve">
    <value>Enter Customer Part#</value>
  </data>
  <data name="ResMsgEnterCustomerPart#1" xml:space="preserve">
    <value>Enter Customer Part#</value>
  </data>
  <data name="ResMsgEnterDefaultFontName" xml:space="preserve">
    <value>Enter Default Font Name</value>
  </data>
  <data name="ResMsgEnterDefaultFontName1" xml:space="preserve">
    <value>Enter Default Font Name</value>
  </data>
  <data name="ResMsgEnterDepartment" xml:space="preserve">
    <value>Enter Department</value>
  </data>
  <data name="ResMsgEnterDepartment1" xml:space="preserve">
    <value>Enter Department</value>
  </data>
  <data name="ResMsgEnterDepartmentCode" xml:space="preserve">
    <value>Enter Department Code</value>
  </data>
  <data name="ResMsgEnterDepartmentCode1" xml:space="preserve">
    <value>Enter Department Code</value>
  </data>
  <data name="ResMsgEnterDepartmentName" xml:space="preserve">
    <value>Enter Department Name</value>
  </data>
  <data name="ResMsgEnterDepartmentName1" xml:space="preserve">
    <value>Enter Department Name</value>
  </data>
  <data name="ResMsgEnterDescription" xml:space="preserve">
    <value>Enter Description</value>
  </data>
  <data name="ResMsgEnterDescription1" xml:space="preserve">
    <value>Enter Description</value>
  </data>
  <data name="ResMsgEnterDesignation" xml:space="preserve">
    <value>Enter Designation</value>
  </data>
  <data name="ResMsgEnterDesignation1" xml:space="preserve">
    <value>Enter Designation</value>
  </data>
  <data name="ResMsgEnterDesignationCode" xml:space="preserve">
    <value>Enter Designation Code</value>
  </data>
  <data name="ResMsgEnterDesignationCode1" xml:space="preserve">
    <value>Enter Designation Code</value>
  </data>
  <data name="ResMsgEnterDesignationName" xml:space="preserve">
    <value>Enter Designation Name</value>
  </data>
  <data name="ResMsgEnterDesignationName1" xml:space="preserve">
    <value>Enter Designation Name</value>
  </data>
  <data name="ResMsgEnterDictionary" xml:space="preserve">
    <value>Enter Dictionary Description</value>
  </data>
  <data name="ResMsgEnterDisplayName" xml:space="preserve">
    <value>Enter Display Name</value>
  </data>
  <data name="ResMsgEnterDisplayName1" xml:space="preserve">
    <value>Enter Display Name</value>
  </data>
  <data name="ResMsgEnterDrawingName" xml:space="preserve">
    <value>Enter Drawing Name</value>
  </data>
  <data name="ResMsgEnterDrawingName1" xml:space="preserve">
    <value>Enter Drawing Name</value>
  </data>
  <data name="ResMsgEnterEffectiveDateFrom" xml:space="preserve">
    <value>Enter Effective Date From</value>
  </data>
  <data name="ResMsgEnterEffectiveDateFrom1" xml:space="preserve">
    <value>Enter Effective Date From</value>
  </data>
  <data name="ResMsgEnterEffectiveDateTo" xml:space="preserve">
    <value>Enter Effective Date To</value>
  </data>
  <data name="ResMsgEnterEffectiveDateTo1" xml:space="preserve">
    <value>Enter Effective Date To</value>
  </data>
  <data name="ResMsgEnterEmail" xml:space="preserve">
    <value>Enter Email</value>
  </data>
  <data name="ResMsgEnterEmail1" xml:space="preserve">
    <value>Enter Email</value>
  </data>
  <data name="ResMsgEnterEmployeeCode" xml:space="preserve">
    <value>Enter Employee Code</value>
  </data>
  <data name="ResMsgEnterEmployeeCode1" xml:space="preserve">
    <value>Enter Employee Code</value>
  </data>
  <data name="ResMsgEnterExtension1" xml:space="preserve">
    <value>Enter Extension1</value>
  </data>
  <data name="ResMsgEnterExtension2" xml:space="preserve">
    <value>Enter Extension2</value>
  </data>
  <data name="ResMsgEnterFGName" xml:space="preserve">
    <value>Enter FG Name</value>
  </data>
  <data name="ResMsgEnterFGName1" xml:space="preserve">
    <value>Enter FG Name</value>
  </data>
  <data name="ResMsgEnterFirstName" xml:space="preserve">
    <value>Enter First Name</value>
  </data>
  <data name="ResMsgEnterFirstName1" xml:space="preserve">
    <value>Enter First Name</value>
  </data>
  <data name="ResMsgEnterFlexi1" xml:space="preserve">
    <value>Enter Flexi 1</value>
  </data>
  <data name="ResMsgEnterFlexi10" xml:space="preserve">
    <value>Enter Flexi 10</value>
  </data>
  <data name="ResMsgEnterFlexi101" xml:space="preserve">
    <value>Enter Flexi 10</value>
  </data>
  <data name="ResMsgEnterFlexi11" xml:space="preserve">
    <value>Enter Flexi 1</value>
  </data>
  <data name="ResMsgEnterFlexi2" xml:space="preserve">
    <value>Enter Flexi 2</value>
  </data>
  <data name="ResMsgEnterFlexi21" xml:space="preserve">
    <value>Enter Flexi 2</value>
  </data>
  <data name="ResMsgEnterFlexi3" xml:space="preserve">
    <value>Enter Flexi 3</value>
  </data>
  <data name="ResMsgEnterFlexi31" xml:space="preserve">
    <value>Enter Flexi 3</value>
  </data>
  <data name="ResMsgEnterFlexi4" xml:space="preserve">
    <value>Enter Flexi 4</value>
  </data>
  <data name="ResMsgEnterFlexi41" xml:space="preserve">
    <value>Enter Flexi 4</value>
  </data>
  <data name="ResMsgEnterFlexi5" xml:space="preserve">
    <value>Enter Flexi 5</value>
  </data>
  <data name="ResMsgEnterFlexi51" xml:space="preserve">
    <value>Enter Flexi 5</value>
  </data>
  <data name="ResMsgEnterFlexi6" xml:space="preserve">
    <value>Enter Flexi 6</value>
  </data>
  <data name="ResMsgEnterFlexi61" xml:space="preserve">
    <value>Enter Flexi 6</value>
  </data>
  <data name="ResMsgEnterFlexi7" xml:space="preserve">
    <value>Enter Flexi 7</value>
  </data>
  <data name="ResMsgEnterFlexi71" xml:space="preserve">
    <value>Enter Flexi 7</value>
  </data>
  <data name="ResMsgEnterFlexi8" xml:space="preserve">
    <value>Enter Flexi 8</value>
  </data>
  <data name="ResMsgEnterFlexi81" xml:space="preserve">
    <value>Enter Flexi 8</value>
  </data>
  <data name="ResMsgEnterFlexi9" xml:space="preserve">
    <value>Enter Flexi 9</value>
  </data>
  <data name="ResMsgEnterFlexi91" xml:space="preserve">
    <value>Enter Flexi 9</value>
  </data>
  <data name="ResMsgEnterFontName" xml:space="preserve">
    <value>Enter Font Name</value>
  </data>
  <data name="ResMsgEnterFontName1" xml:space="preserve">
    <value>Enter Font Name</value>
  </data>
  <data name="ResMsgEnterFromSN" xml:space="preserve">
    <value>Enter From SN</value>
  </data>
  <data name="ResMsgEnterIndent" xml:space="preserve">
    <value>Enter Indent</value>
  </data>
  <data name="ResMsgEnterIndentation" xml:space="preserve">
    <value>Enter Indentation</value>
  </data>
  <data name="ResMsgEnterIndentation1" xml:space="preserve">
    <value>Enter Indentation</value>
  </data>
  <data name="ResMsgEnterItem#" xml:space="preserve">
    <value>Enter Item #</value>
  </data>
  <data name="ResMsgEnterItem#1" xml:space="preserve">
    <value>Enter Item #</value>
  </data>
  <data name="ResMsgEnterLanguageCode" xml:space="preserve">
    <value>Enter Language Code</value>
  </data>
  <data name="ResMsgEnterLanguageCode1" xml:space="preserve">
    <value>Enter Language Code</value>
  </data>
  <data name="ResMsgEnterLanguageName" xml:space="preserve">
    <value>Enter Language Name</value>
  </data>
  <data name="ResMsgEnterLanguageName1" xml:space="preserve">
    <value>Enter Language Name</value>
  </data>
  <data name="ResMsgEnterLastName" xml:space="preserve">
    <value>Enter Last Name</value>
  </data>
  <data name="ResMsgEnterLastName1" xml:space="preserve">
    <value>Enter Last Name</value>
  </data>
  <data name="ResMsgEnterLevel" xml:space="preserve">
    <value>Enter Level</value>
  </data>
  <data name="ResMsgEnterLevel1" xml:space="preserve">
    <value>Enter Level</value>
  </data>
  <data name="ResMsgEnterLocalNoteCode" xml:space="preserve">
    <value>Enter Local Note Code</value>
  </data>
  <data name="ResMsgEnterLocalNotes" xml:space="preserve">
    <value>Enter Local Notes</value>
  </data>
  <data name="ResMsgEnterLocalNotes1" xml:space="preserve">
    <value>Enter Local Notes</value>
  </data>
  <data name="ResMsgEnterLocalRemarks" xml:space="preserve">
    <value>Enter Local Remarks</value>
  </data>
  <data name="ResMsgEnterLocation" xml:space="preserve">
    <value>Enter Location</value>
  </data>
  <data name="ResMsgEnterLoginID" xml:space="preserve">
    <value>Enter Login ID</value>
  </data>
  <data name="ResMsgEnterLoginID1" xml:space="preserve">
    <value>Enter Login ID</value>
  </data>
  <data name="ResMsgEnterManufacturingYear" xml:space="preserve">
    <value>Enter Manufacturing Year</value>
  </data>
  <data name="ResMsgEnterManufacturingYear1" xml:space="preserve">
    <value>Enter Manufacturing Year</value>
  </data>
  <data name="ResMsgEnterMaskingCode" xml:space="preserve">
    <value>Enter Masking Code</value>
  </data>
  <data name="ResMsgEnterMaskingCode1" xml:space="preserve">
    <value>Enter Masking Code</value>
  </data>
  <data name="ResMsgEnterMaskingDescription" xml:space="preserve">
    <value>Enter Masking Description</value>
  </data>
  <data name="ResMsgEnterMaskingDescription1" xml:space="preserve">
    <value>Enter Masking Description</value>
  </data>
  <data name="ResMsgEnterMFGCode" xml:space="preserve">
    <value>Enter MFG Code</value>
  </data>
  <data name="ResMsgEnterMFGCode1" xml:space="preserve">
    <value>Enter MFG Code</value>
  </data>
  <data name="ResMsgEnterMFGName" xml:space="preserve">
    <value>Enter MFG Name</value>
  </data>
  <data name="ResMsgEnterMFGName1" xml:space="preserve">
    <value>Enter MFG Name</value>
  </data>
  <data name="ResMsgEnterMfrer" xml:space="preserve">
    <value>Enter Mfrer</value>
  </data>
  <data name="ResMsgEnterMiddleName" xml:space="preserve">
    <value>Enter Middle Name</value>
  </data>
  <data name="ResMsgEnterMiddleName1" xml:space="preserve">
    <value>Enter Middle Name</value>
  </data>
  <data name="ResMsgEnterMobileNumber" xml:space="preserve">
    <value>Enter Mobile Number</value>
  </data>
  <data name="ResMsgEnterMobileNumber1" xml:space="preserve">
    <value>Enter Mobile Number</value>
  </data>
  <data name="ResMsgEnterModelCode" xml:space="preserve">
    <value>Enter Model Code</value>
  </data>
  <data name="ResMsgEnterModelCode1" xml:space="preserve">
    <value>Enter Model Code</value>
  </data>
  <data name="ResMsgEnterModelName" xml:space="preserve">
    <value>Enter Model Name</value>
  </data>
  <data name="ResMsgEnterModelName1" xml:space="preserve">
    <value>Enter Model Name</value>
  </data>
  <data name="ResMsgEnterNewOwner" xml:space="preserve">
    <value>Enter New Owner</value>
  </data>
  <data name="ResMsgEnterNewOwner1" xml:space="preserve">
    <value>Enter New Owner</value>
  </data>
  <data name="ResMsgEnterNewPassword" xml:space="preserve">
    <value>Enter New Password</value>
  </data>
  <data name="ResMsgEnterNewPassword1" xml:space="preserve">
    <value>Enter New Password</value>
  </data>
  <data name="ResMsgEnterNotes" xml:space="preserve">
    <value>Enter Notes</value>
  </data>
  <data name="ResMsgEnterOptionDescription" xml:space="preserve">
    <value>Enter Option Description</value>
  </data>
  <data name="ResMsgEnterOptionDescription1" xml:space="preserve">
    <value>Enter Option Description</value>
  </data>
  <data name="ResMsgEnterOrder#" xml:space="preserve">
    <value>Enter Order #</value>
  </data>
  <data name="ResMsgEnterOrder#1" xml:space="preserve">
    <value>Enter Order #</value>
  </data>
  <data name="ResMsgEnterOrderDate" xml:space="preserve">
    <value>Enter Order Date</value>
  </data>
  <data name="ResMsgEnterOrderDate1" xml:space="preserve">
    <value>Enter Order Date</value>
  </data>
  <data name="ResMsgEnterOrderTopLevel" xml:space="preserve">
    <value>Enter Order # / Top Level</value>
  </data>
  <data name="ResMsgEnterParmaCode" xml:space="preserve">
    <value>Enter Parma Code</value>
  </data>
  <data name="ResMsgEnterParmaCode1" xml:space="preserve">
    <value>Enter Parma Code</value>
  </data>
  <data name="ResMsgEnterPart#" xml:space="preserve">
    <value>Enter Part #</value>
  </data>
  <data name="ResMsgEnterPart#1" xml:space="preserve">
    <value>Enter Part #</value>
  </data>
  <data name="ResMsgEnterPartDesc" xml:space="preserve">
    <value>Enter Part Description</value>
  </data>
  <data name="ResMsgEnterPartDesc1" xml:space="preserve">
    <value>Enter Part Description</value>
  </data>
  <data name="ResMsgEnterPartDescription" xml:space="preserve">
    <value>Enter Part Description</value>
  </data>
  <data name="ResMsgEnterPartDescription1" xml:space="preserve">
    <value>Enter Part Description</value>
  </data>
  <data name="ResMsgEnterPartStatus" xml:space="preserve">
    <value>Enter Part Status</value>
  </data>
  <data name="ResMsgEnterPassword" xml:space="preserve">
    <value>Enter Password</value>
  </data>
  <data name="ResMsgEnterPassword1" xml:space="preserve">
    <value>Enter Password</value>
  </data>
  <data name="ResMsgEnterPharmaCode" xml:space="preserve">
    <value>Enter Parma Code</value>
  </data>
  <data name="ResMsgEnterPhone1" xml:space="preserve">
    <value>Enter Phone1</value>
  </data>
  <data name="ResMsgEnterPhone2" xml:space="preserve">
    <value>Enter Phone2</value>
  </data>
  <data name="ResMsgEnterProductTypeName" xml:space="preserve">
    <value>Enter Product Type Name</value>
  </data>
  <data name="ResMsgEnterProductTypeName1" xml:space="preserve">
    <value>Enter Product Type Name</value>
  </data>
  <data name="ResMsgEnterQty" xml:space="preserve">
    <value>Enter Qty</value>
  </data>
  <data name="ResMsgEnterQty1" xml:space="preserve">
    <value>Enter Qty</value>
  </data>
  <data name="ResMsgEnterQueryName" xml:space="preserve">
    <value>Enter Query Name</value>
  </data>
  <data name="ResMsgEnterQueryName1" xml:space="preserve">
    <value>Enter Query Name</value>
  </data>
  <data name="ResMsgEnterReferenceAssembly#" xml:space="preserve">
    <value>Enter Reference Assembly #</value>
  </data>
  <data name="ResMsgEnterReferenceAssembly#1" xml:space="preserve">
    <value>Enter Reference Assembly #</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyDescription" xml:space="preserve">
    <value>Enter Reference Assembly Description</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyMFGCode" xml:space="preserve">
    <value>Enter Reference Assembly MFG Code</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyMFGCode1" xml:space="preserve">
    <value>Enter Reference Assembly MFG Code</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyName" xml:space="preserve">
    <value>Enter Reference Assembly Name</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyName1" xml:space="preserve">
    <value>Enter Reference Assembly Name</value>
  </data>
  <data name="ResMsgEnterRemarks" xml:space="preserve">
    <value>Enter Remarks</value>
  </data>
  <data name="ResMsgEnterRemarks1" xml:space="preserve">
    <value>Enter Remarks</value>
  </data>
  <data name="ResMsgEnterRenamePart#as" xml:space="preserve">
    <value>Enter Rename Part # as</value>
  </data>
  <data name="ResMsgEnterRenamePart#as1" xml:space="preserve">
    <value>Enter Rename Part # as</value>
  </data>
  <data name="ResMsgEnterReplacebyPart#" xml:space="preserve">
    <value>Enter Replace by Part #</value>
  </data>
  <data name="ResMsgEnterReplacebyPart#1" xml:space="preserve">
    <value>Enter Replace by Part #</value>
  </data>
  <data name="ResMsgEnterResolution" xml:space="preserve">
    <value>Enter Resolution</value>
  </data>
  <data name="ResMsgEnterResolution1" xml:space="preserve">
    <value>Enter Resolution</value>
  </data>
  <data name="ResMsgEnterRespondedDate&amp;Time" xml:space="preserve">
    <value>Enter Responded Date&amp;Time</value>
  </data>
  <data name="ResMsgEnterRespondedDate&amp;Time1" xml:space="preserve">
    <value>Enter Responded Date&amp;Time</value>
  </data>
  <data name="ResMsgEnterRoad#" xml:space="preserve">
    <value>Enter Road #</value>
  </data>
  <data name="ResMsgEnterRoad#1" xml:space="preserve">
    <value>Enter Road #</value>
  </data>
  <data name="ResMsgEnterRoleName" xml:space="preserve">
    <value>Enter Role Name</value>
  </data>
  <data name="ResMsgEnterRoleName1" xml:space="preserve">
    <value>Enter Role Name</value>
  </data>
  <data name="ResMsgEnterSection#" xml:space="preserve">
    <value>Enter Section #</value>
  </data>
  <data name="ResMsgEnterSection#1" xml:space="preserve">
    <value>Enter Section #</value>
  </data>
  <data name="ResMsgEnterSectionName" xml:space="preserve">
    <value>Enter Section Name</value>
  </data>
  <data name="ResMsgEnterSectionName1" xml:space="preserve">
    <value>Enter Section Name</value>
  </data>
  <data name="ResMsgEnterSequence" xml:space="preserve">
    <value>Enter Sequence</value>
  </data>
  <data name="ResMsgEnterSequence#" xml:space="preserve">
    <value>Enter Sequence #</value>
  </data>
  <data name="ResMsgEnterSequence#1" xml:space="preserve">
    <value>Enter Sequence #</value>
  </data>
  <data name="ResMsgEnterSetItemNoSequence" xml:space="preserve">
    <value>Enter Set Item # Sequence</value>
  </data>
  <data name="ResMsgEnterSpecification" xml:space="preserve">
    <value>Enter Specification</value>
  </data>
  <data name="ResMsgEnterSpecification1" xml:space="preserve">
    <value>Enter Specification</value>
  </data>
  <data name="ResMsgEnterSpecifiction" xml:space="preserve">
    <value>Enter Specification</value>
  </data>
  <data name="ResMsgEnterSpecifiction1" xml:space="preserve">
    <value>Enter Specification</value>
  </data>
  <data name="ResMsgEnterTemplateCode" xml:space="preserve">
    <value>Enter Template Code</value>
  </data>
  <data name="ResMsgEnterTemplateCode1" xml:space="preserve">
    <value>Enter Template Code</value>
  </data>
  <data name="ResMsgEnterTemplateName" xml:space="preserve">
    <value>Enter Template Name</value>
  </data>
  <data name="ResMsgEnterTemplateName1" xml:space="preserve">
    <value>Enter Template Name</value>
  </data>
  <data name="ResMsgEnterTimeTaken" xml:space="preserve">
    <value>Enter Time Taken</value>
  </data>
  <data name="ResMsgEnterTimeTaken1" xml:space="preserve">
    <value>Enter Time Taken</value>
  </data>
  <data name="ResMsgEnterUOMCode" xml:space="preserve">
    <value>Enter UOM Code</value>
  </data>
  <data name="ResMsgEnterUOMCode1" xml:space="preserve">
    <value>Enter UOM Code</value>
  </data>
  <data name="ResMsgEnterUOMDescription" xml:space="preserve">
    <value>Enter UOM Description</value>
  </data>
  <data name="ResMsgEnterUOMDescription1" xml:space="preserve">
    <value>Enter UOM Description</value>
  </data>
  <data name="ResMsgEnterUpToSN" xml:space="preserve">
    <value>Enter Up To SN</value>
  </data>
  <data name="ResMsgEnterValidLocation" xml:space="preserve">
    <value>Invalid Location</value>
  </data>
  <data name="ResMsgEnterVendorCode" xml:space="preserve">
    <value>Enter Vendor Code</value>
  </data>
  <data name="ResMsgEnterVendorCode1" xml:space="preserve">
    <value>Enter Vendor Code</value>
  </data>
  <data name="ResMsgEnterVendorName" xml:space="preserve">
    <value>Enter Vendor Name</value>
  </data>
  <data name="ResMsgEnterVendorName1" xml:space="preserve">
    <value>Enter Vendor Name</value>
  </data>
  <data name="ResMsgEnterVIN#" xml:space="preserve">
    <value>Enter VIN #</value>
  </data>
  <data name="ResMsgEnterVIN#1" xml:space="preserve">
    <value>Enter VIN #</value>
  </data>
  <data name="ResMsgEnterVINshort#" xml:space="preserve">
    <value>Enter VIN Short #</value>
  </data>
  <data name="ResMsgEnterVINShortCode" xml:space="preserve">
    <value>Enter VIN Short Code</value>
  </data>
  <data name="ResMsgEnterYourComments" xml:space="preserve">
    <value>Enter Your Comments</value>
  </data>
  <data name="ResMsgEnterYourComments1" xml:space="preserve">
    <value>Enter Your Comments</value>
  </data>
  <data name="ResMsgEnterZipCode" xml:space="preserve">
    <value>Enter Zip Code</value>
  </data>
  <data name="ResmsgErrorsFound" xml:space="preserve">
    <value>Errors Found</value>
  </data>
  <data name="ResmsgFileAlreadyExist" xml:space="preserve">
    <value>Image already exist</value>
  </data>
  <data name="ResMsgFilenameshouldnotexceed50characters" xml:space="preserve">
    <value>File name should not exceed 50 characters</value>
  </data>
  <data name="ResMsgFilesizeshouldnotexceed2MB" xml:space="preserve">
    <value>File size should not exceed 2MB</value>
  </data>
  <data name="ResMsgFilesizeshouldnotexceed50MB" xml:space="preserve">
    <value>File size should not exceed 50MB</value>
  </data>
  <data name="ResMsgFontColour" xml:space="preserve">
    <value>Select Font Colour</value>
  </data>
  <data name="ResMsgFontColour1" xml:space="preserve">
    <value>Select Font Colour</value>
  </data>
  <data name="ResMsgGivenNumberalreadyavailableinParts" xml:space="preserve">
    <value>Given Number already available in Parts</value>
  </data>
  <data name="ResMsgHighlightedFieldsAreMandatory" xml:space="preserve">
    <value>Highlighted Fields Are Mandatory</value>
  </data>
  <data name="ResMsgHotsSpotsOncceDeletedCantBeRecovered" xml:space="preserve">
    <value>Hotspots Once Deleted Cannot Be Recovered</value>
  </data>
  <data name="ResMsgHotsSpotsSelected" xml:space="preserve">
    <value>Hotspots Selected</value>
  </data>
  <data name="ResMsgIndentLevelmustbeadigitof0to9" xml:space="preserve">
    <value>Indent Level must be a digit of (0to9)</value>
  </data>
  <data name="ResMsgInvalidAssociationCustomerCodeAndCustomerName" xml:space="preserve">
    <value>Invalid Association Customer Code and Customer Name</value>
  </data>
  <data name="ResMsgInvalidAssociationVendorCodeAndVendorName" xml:space="preserve">
    <value>Invalid Association of Vendor Code and Vendor Name</value>
  </data>
  <data name="ResMsgInvalidCustomerCode" xml:space="preserve">
    <value>Invalid Customer Code</value>
  </data>
  <data name="ResMsgInvalidCustomerName" xml:space="preserve">
    <value>Invalid Customer Name</value>
  </data>
  <data name="ResMsgInvalidMaskingCode" xml:space="preserve">
    <value>Invalid Masking Code</value>
  </data>
  <data name="ResMsgInvalidMFGCode" xml:space="preserve">
    <value>Invalid MFG Code</value>
  </data>
  <data name="ResMsgInvalidPartAssemblyNo" xml:space="preserve">
    <value>Invalid Part Assembly No</value>
  </data>
  <data name="ResMsgInvalidPartDescription" xml:space="preserve">
    <value>Invalid Part Description</value>
  </data>
  <data name="ResMsgInvalidPartNO" xml:space="preserve">
    <value>Invalid part # or part not published</value>
  </data>
  <data name="ResMsgInvalidReferencePartNumber" xml:space="preserve">
    <value>Invalid Reference Part Number</value>
  </data>
  <data name="ResMsgInvalidVendorCode" xml:space="preserve">
    <value>Invalid Vendor Code</value>
  </data>
  <data name="ResMsgInvalidVendorName" xml:space="preserve">
    <value>Invalid Vendor Name</value>
  </data>
  <data name="ResMsgItemNoAlreadyExists" xml:space="preserve">
    <value>Item # already exists</value>
  </data>
  <data name="ResMsgItemNoCannotBeEmpty" xml:space="preserve">
    <value>Item # cannot be empty</value>
  </data>
  <data name="ResmsgItemnumberNotFound" xml:space="preserve">
    <value>Item Number Not Found</value>
  </data>
  <data name="ResMsgLoading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="Resmsglocalremarksexisreplace" xml:space="preserve">
    <value>Local remarks already exist do you want to replace ?</value>
  </data>
  <data name="ResMsgMFGCodeCannotBeEmpty" xml:space="preserve">
    <value>MFG Code Cannot be Empty</value>
  </data>
  <data name="resmsgnImageSavedSuccessfully" xml:space="preserve">
    <value>Image Saved Successfully.</value>
  </data>
  <data name="Resmsgnolocalremarks" xml:space="preserve">
    <value>The current assembly has no local remarks</value>
  </data>
  <data name="resmsgnOriginalDatafound" xml:space="preserve">
    <value>No original data found for this assembly</value>
  </data>
  <data name="resmsgnOriginalRestored" xml:space="preserve">
    <value>Original data restored</value>
  </data>
  <data name="ResMsgNotUsedInAnyAssembly" xml:space="preserve">
    <value>Not used in any assembly</value>
  </data>
  <data name="ResmsgnoVinrecordsfound" xml:space="preserve">
    <value>No VIN Records found</value>
  </data>
  <data name="ResMsgOnlyreplaceapartbyapartORanassemblybyanassembly" xml:space="preserve">
    <value>Only replace a part by a part OR an assembly by an assembly</value>
  </data>
  <data name="ResMsgOrderNumber" xml:space="preserve">
    <value>Enter Order Number</value>
  </data>
  <data name="ResmsgOriginalDataFound" xml:space="preserve">
    <value>Original data found </value>
  </data>
  <data name="ResMsgPartNumberCannotBeEmpty" xml:space="preserve">
    <value>Part Number cannot be empty</value>
  </data>
  <data name="ResMsgPartNumberShouldbemandatory" xml:space="preserve">
    <value>Part #should be mandatory</value>
  </data>
  <data name="ResmsgPleaseAddAnotherImage" xml:space="preserve">
    <value>Please try adding another image.</value>
  </data>
  <data name="ResMsgPleaseenterpartno" xml:space="preserve">
    <value>Please enter part/assembly #</value>
  </data>
  <data name="ResMsgPleaseenterpartOrAssemblyno" xml:space="preserve">
    <value>Please enter part/assembly #</value>
  </data>
  <data name="ResMsgPleaseSelectColumn" xml:space="preserve">
    <value>Please select a column</value>
  </data>
  <data name="ResMsgPleaseSelectOperator" xml:space="preserve">
    <value>Please select a operator</value>
  </data>
  <data name="ResMsgPlsFillMandatoryFields" xml:space="preserve">
    <value>Please fill the mandatory fields</value>
  </data>
  <data name="ResMsgProductTypeCode" xml:space="preserve">
    <value>Enter Product Type Code</value>
  </data>
  <data name="ResMsgProductTypeCode1" xml:space="preserve">
    <value>Enter Product Type Code</value>
  </data>
  <data name="ResmsgRecordissafe" xml:space="preserve">
    <value>Record is safe :)</value>
  </data>
  <data name="ResMsgReEnterPassword" xml:space="preserve">
    <value>Re Enter Password</value>
  </data>
  <data name="ResMsgReEnterPassword1" xml:space="preserve">
    <value>Re Enter Password</value>
  </data>
  <data name="ResmsgRemove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="ResmsgReplace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="ResmsgReplaceBookmark" xml:space="preserve">
    <value>Replace Local Remarks</value>
  </data>
  <data name="ResmsgReplaceImage" xml:space="preserve">
    <value>Yes Replace</value>
  </data>
  <data name="ResMsgReplacePartalreadypresentinSameBOM" xml:space="preserve">
    <value>Replace Part already present in same BOM</value>
  </data>
  <data name="ResMsgReplacePartalreadypresentwithintheSameAssembly" xml:space="preserve">
    <value>Replace Part already present within the same Assembly</value>
  </data>
  <data name="ResmsgrestoreOriginal" xml:space="preserve">
    <value>Restore Original</value>
  </data>
  <data name="ResMsgSelectAttachmentType" xml:space="preserve">
    <value>Select Attachment Type</value>
  </data>
  <data name="ResMsgSelectAttachmentType1" xml:space="preserve">
    <value>Select Attachment Type</value>
  </data>
  <data name="ResMsgSelectCountry" xml:space="preserve">
    <value>Select Country</value>
  </data>
  <data name="ResMsgSelectErrorType" xml:space="preserve">
    <value>Select Error Type</value>
  </data>
  <data name="ResMsgSelectErrorType1" xml:space="preserve">
    <value>Select Error Type</value>
  </data>
  <data name="ResMsgSelectFG" xml:space="preserve">
    <value>Select Function Group</value>
  </data>
  <data name="ResMsgSelectFG1" xml:space="preserve">
    <value>Select Function Group</value>
  </data>
  <data name="ResMsgSelectFontSize" xml:space="preserve">
    <value>Select Font Size</value>
  </data>
  <data name="ResMsgSelectFontSize1" xml:space="preserve">
    <value>Select Font Size</value>
  </data>
  <data name="ResMsgSelectFontStyle" xml:space="preserve">
    <value>Select Font Style</value>
  </data>
  <data name="ResMsgSelectFontStyle1" xml:space="preserve">
    <value>Select Font Style</value>
  </data>
  <data name="ResMsgSelectFontTemplate" xml:space="preserve">
    <value>Select Font Template</value>
  </data>
  <data name="ResMsgSelectLanguage" xml:space="preserve">
    <value>Select Language</value>
  </data>
  <data name="ResMsgSelectLanguage1" xml:space="preserve">
    <value>Select Language</value>
  </data>
  <data name="ResMsgSelectMFGCode" xml:space="preserve">
    <value>Select MFG Code</value>
  </data>
  <data name="ResMsgSelectMFGCode1" xml:space="preserve">
    <value>Select MFG Code</value>
  </data>
  <data name="ResMsgSelectQuery" xml:space="preserve">
    <value>Select Query</value>
  </data>
  <data name="ResMsgSelectQuery1" xml:space="preserve">
    <value>Select Query</value>
  </data>
  <data name="ResMsgselectrecordstodelete" xml:space="preserve">
    <value>Please select records to delete</value>
  </data>
  <data name="ResMsgSelectScreen" xml:space="preserve">
    <value>Screen Select</value>
  </data>
  <data name="ResMsgSelectState" xml:space="preserve">
    <value>Select State</value>
  </data>
  <data name="ResMsgSelectToDelete" xml:space="preserve">
    <value>Select records to delete</value>
  </data>
  <data name="ResMsgSelectUOM" xml:space="preserve">
    <value>Select UOM</value>
  </data>
  <data name="ResMsgSelectUOM1" xml:space="preserve">
    <value>Select UOM</value>
  </data>
  <data name="ResMsgSequencealreadyExists" xml:space="preserve">
    <value>Sequence already exists</value>
  </data>
  <data name="ResMsgSequenceShouldnotexceedsMorethan255" xml:space="preserve">
    <value>Sequence should not exceeds more than 255</value>
  </data>
  <data name="ResMsgShipToAddress" xml:space="preserve">
    <value>Enter ship to address</value>
  </data>
  <data name="ResMsgSpecialInstructions" xml:space="preserve">
    <value>Enter special instructions</value>
  </data>
  <data name="ResMsgVendorCodeCannotBeEmpty" xml:space="preserve">
    <value>Vendor Code cannot be empty</value>
  </data>
  <data name="ResmsgVendorDoesNotExist" xml:space="preserve">
    <value>Vendor Does NO</value>
  </data>
  <data name="ResMsgVendorMfgCodeCannotBeEmpty" xml:space="preserve">
    <value>Vendor Mfg Code cannot be empty</value>
  </data>
  <data name="ResMsgVendorNameCannotBeEmpty" xml:space="preserve">
    <value>Vendor Name cannot be empty</value>
  </data>
  <data name="ResMsgVendorParmaCodeCannotBeEmpty" xml:space="preserve">
    <value>Vendor Parma Code cannot be empty</value>
  </data>
  <data name="ResMsgVendorPartNumberCannotBeEmpty" xml:space="preserve">
    <value>Vendor Part Number cannot be empty</value>
  </data>
  <data name="ResMsgViewAssemblyImg" xml:space="preserve">
    <value>View Assembly Image</value>
  </data>
  <data name="ResMsgViewAssemblyImg1" xml:space="preserve">
    <value>View Assembly Image</value>
  </data>
  <data name="ResMsgVIN" xml:space="preserve">
    <value>VIN</value>
  </data>
  <data name="ResMsgYoucannotaddsamepartasreferencepart" xml:space="preserve">
    <value>Cannot add same part as reference part</value>
  </data>
  <data name="ResMultipleDeleteHotspot" xml:space="preserve">
    <value>Multiple Hotspot Delete</value>
  </data>
  <data name="ResMultiplePartsSelection" xml:space="preserve">
    <value>Select multiple parts</value>
  </data>
  <data name="ResMultiplePartsSelection1" xml:space="preserve">
    <value>Multiple Parts Selection</value>
  </data>
  <data name="ResName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="ResName1" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="ResNewCartAddedSuccessfully" xml:space="preserve">
    <value>New cart added successfully</value>
  </data>
  <data name="ResNewCodes" xml:space="preserve">
    <value>New Codes</value>
  </data>
  <data name="ResNewCodes1" xml:space="preserve">
    <value>New Codes</value>
  </data>
  <data name="ResNewDrawing#" xml:space="preserve">
    <value>New Drawing #</value>
  </data>
  <data name="ResNewDrawing#1" xml:space="preserve">
    <value>New Drawing #</value>
  </data>
  <data name="ResNewMFGCode" xml:space="preserve">
    <value>New MFG Code</value>
  </data>
  <data name="ResNewOwner" xml:space="preserve">
    <value>New Owner</value>
  </data>
  <data name="ResNewOwner1" xml:space="preserve">
    <value>New Owner</value>
  </data>
  <data name="ResNewOwnerName" xml:space="preserve">
    <value>New Owner Name</value>
  </data>
  <data name="ResNewPart#" xml:space="preserve">
    <value>New Part #</value>
  </data>
  <data name="ResNewPart#1" xml:space="preserve">
    <value>New Part #</value>
  </data>
  <data name="ResNewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="ResNewPassword1" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="ResNewRoad#" xml:space="preserve">
    <value>New Road #</value>
  </data>
  <data name="ResNewRoad#1" xml:space="preserve">
    <value>New Road #</value>
  </data>
  <data name="ResNewTemplateName" xml:space="preserve">
    <value>New Font Name</value>
  </data>
  <data name="ResNewVIN#" xml:space="preserve">
    <value>New VIN #</value>
  </data>
  <data name="ResNewVIN#1" xml:space="preserve">
    <value>New VIN #</value>
  </data>
  <data name="ResNext" xml:space="preserve">
    <value>Next image</value>
  </data>
  <data name="ResNextAssembly" xml:space="preserve">
    <value>View Next Assembly</value>
  </data>
  <data name="ResNextImage" xml:space="preserve">
    <value>Next Image</value>
  </data>
  <data name="ResNextPage" xml:space="preserve">
    <value>Next Page</value>
  </data>
  <data name="ResNextReleaseInformation" xml:space="preserve">
    <value>Application announcements</value>
  </data>
  <data name="ResNextReleseInformation" xml:space="preserve">
    <value>Application announcements</value>
  </data>
  <data name="ResNextstopbuttonposition" xml:space="preserve">
    <value>Next stop button position</value>
  </data>
  <data name="ResNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="ResNo1" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="ResNoDrawing" xml:space="preserve">
    <value>No Drawing</value>
  </data>
  <data name="ResNoFileChoosen" xml:space="preserve">
    <value>No File Choosen</value>
  </data>
  <data name="ResNoFunctionGroupFound" xml:space="preserve">
    <value>No Function Group found</value>
  </data>
  <data name="ResNoImageAvailable" xml:space="preserve">
    <value>No Image Available</value>
  </data>
  <data name="ResNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ResNone1" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ResNonEditable" xml:space="preserve">
    <value>Non Editable</value>
  </data>
  <data name="ResNoNewAssociationSelected" xml:space="preserve">
    <value>No new association selected to save</value>
  </data>
  <data name="ResNoRecordFound" xml:space="preserve">
    <value>No Record Found</value>
  </data>
  <data name="ResNoRecordFoundtoDelete" xml:space="preserve">
    <value>No Record Found are to Delete</value>
  </data>
  <data name="ResNoRecordsAreFoundToAdd" xml:space="preserve">
    <value>Select Record To Add</value>
  </data>
  <data name="ResNoRecordsAreFoundToDelete" xml:space="preserve">
    <value>No Records Are Found To Delete</value>
  </data>
  <data name="ResNoRecordsAreFoundToLock" xml:space="preserve">
    <value>No Records Are Found To Lock</value>
  </data>
  <data name="ResNoRecordsAreFoundToUnLock" xml:space="preserve">
    <value>No Records Are Found To Unlock</value>
  </data>
  <data name="ResNorecordstoview" xml:space="preserve">
    <value>No records to view</value>
  </data>
  <data name="ResNoRestrictions" xml:space="preserve">
    <value>No Restrictions</value>
  </data>
  <data name="ResNoRestrictions1" xml:space="preserve">
    <value>No Restrictions</value>
  </data>
  <data name="ResNotContains" xml:space="preserve">
    <value>Not Contains</value>
  </data>
  <data name="ResNotContains1" xml:space="preserve">
    <value>Not Contains</value>
  </data>
  <data name="ResNote" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="ResNotEmpty" xml:space="preserve">
    <value>Not Empty</value>
  </data>
  <data name="ResNotEmpty1" xml:space="preserve">
    <value>Not Empty</value>
  </data>
  <data name="ResNotEqual" xml:space="preserve">
    <value>Not Equal</value>
  </data>
  <data name="ResNotEqual1" xml:space="preserve">
    <value>Not Equal</value>
  </data>
  <data name="ResNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ResNotes1" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ResNoteText" xml:space="preserve">
    <value>Note / Text</value>
  </data>
  <data name="ResNotifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="ResNotImportedCount" xml:space="preserve">
    <value>Not Imported Count</value>
  </data>
  <data name="ResNotImportedCount1" xml:space="preserve">
    <value>Not Imported Count</value>
  </data>
  <data name="ResNotInAddMode" xml:space="preserve">
    <value>Not in add Mode</value>
  </data>
  <data name="ResNotinAssembly" xml:space="preserve">
    <value>Not in Assembly</value>
  </data>
  <data name="ResNotinTree" xml:space="preserve">
    <value>Not in Tree</value>
  </data>
  <data name="Resnova" xml:space="preserve">
    <value>Nova</value>
  </data>
  <data name="ResNOVABUS" xml:space="preserve">
    <value>NOVABUS</value>
  </data>
  <data name="ResNOVABUS1" xml:space="preserve">
    <value>NOVABUS</value>
  </data>
  <data name="ResNumberOfVehicles" xml:space="preserve">
    <value>Number Of Vehicles</value>
  </data>
  <data name="ResNUMERICAL" xml:space="preserve">
    <value>NUMERICAL INDEX</value>
  </data>
  <data name="ResObject_ID" xml:space="preserve">
    <value>Object_ID</value>
  </data>
  <data name="ResOCR" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="ResOCR1" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="ResOf" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="ResOK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ResOldDrawing#" xml:space="preserve">
    <value>Old Drawing #</value>
  </data>
  <data name="ResOldDrawing#1" xml:space="preserve">
    <value>Old Drawing #</value>
  </data>
  <data name="ResOldMFGCode" xml:space="preserve">
    <value>Old MFG Code</value>
  </data>
  <data name="ResOldOwnerName" xml:space="preserve">
    <value>Old Owner Name</value>
  </data>
  <data name="ResOldPart#" xml:space="preserve">
    <value>Old Part #</value>
  </data>
  <data name="ResOldPart#1" xml:space="preserve">
    <value>Old Part #</value>
  </data>
  <data name="ResOldPassword" xml:space="preserve">
    <value>Old Password</value>
  </data>
  <data name="ResOldRoad#" xml:space="preserve">
    <value>Old Road #</value>
  </data>
  <data name="ResOldTemplateName" xml:space="preserve">
    <value>Old Font Name</value>
  </data>
  <data name="ResOne" xml:space="preserve">
    <value>One</value>
  </data>
  <data name="ResOne1" xml:space="preserve">
    <value>One1</value>
  </data>
  <data name="ResOne10" xml:space="preserve">
    <value>One10</value>
  </data>
  <data name="ResOne2" xml:space="preserve">
    <value>One2</value>
  </data>
  <data name="ResOne3" xml:space="preserve">
    <value>One3</value>
  </data>
  <data name="ResOne4" xml:space="preserve">
    <value>One4</value>
  </data>
  <data name="ResOne5" xml:space="preserve">
    <value>One5</value>
  </data>
  <data name="ResOne6" xml:space="preserve">
    <value>One6</value>
  </data>
  <data name="ResOne7" xml:space="preserve">
    <value>One7</value>
  </data>
  <data name="ResOne8" xml:space="preserve">
    <value>One8</value>
  </data>
  <data name="ResOne9" xml:space="preserve">
    <value>One9</value>
  </data>
  <data name="Resonetomany" xml:space="preserve">
    <value>One to many</value>
  </data>
  <data name="Resonetoone" xml:space="preserve">
    <value>One to one</value>
  </data>
  <data name="ResOpen" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="ResOpen1" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="ResOperator" xml:space="preserve">
    <value>Operator</value>
  </data>
  <data name="ResOperator1" xml:space="preserve">
    <value>Operator</value>
  </data>
  <data name="ResOperatorsManual" xml:space="preserve">
    <value>Operator's Manual</value>
  </data>
  <data name="ResOptionCode" xml:space="preserve">
    <value>Option Code</value>
  </data>
  <data name="ResOptionCode1" xml:space="preserve">
    <value>Option Code</value>
  </data>
  <data name="ResOptionDescription" xml:space="preserve">
    <value>Option Description</value>
  </data>
  <data name="ResOptionDescription1" xml:space="preserve">
    <value>Option Description</value>
  </data>
  <data name="ResOptionDescriptionLanguage-List" xml:space="preserve">
    <value>Option Description Language-List</value>
  </data>
  <data name="ResOptionDescriptionLanguage-List1" xml:space="preserve">
    <value>Option Description Language-List</value>
  </data>
  <data name="ResOptionDescriptionList" xml:space="preserve">
    <value>Option Description List</value>
  </data>
  <data name="ResOptionDescriptionList1" xml:space="preserve">
    <value>Option Description List</value>
  </data>
  <data name="ResOptionDescriptionUpdation" xml:space="preserve">
    <value>Option Description Updation</value>
  </data>
  <data name="ResOptionDescriptionUpdation1" xml:space="preserve">
    <value>Option Description Updation</value>
  </data>
  <data name="ResOptionDetails" xml:space="preserve">
    <value>Option Details</value>
  </data>
  <data name="ResOptionEntryCantbeblank" xml:space="preserve">
    <value>Option entry can't be blank</value>
  </data>
  <data name="ResOptionIsActive?" xml:space="preserve">
    <value>Option Is Active?</value>
  </data>
  <data name="ResOptionLanguage-Add" xml:space="preserve">
    <value>Option Language-Add</value>
  </data>
  <data name="ResOptionLanguage-Add1" xml:space="preserve">
    <value>Option Language-Add</value>
  </data>
  <data name="ResOptionLanguage-Edit" xml:space="preserve">
    <value>Option Language-Edit</value>
  </data>
  <data name="ResOptionLanguage-Edit1" xml:space="preserve">
    <value>Option Language-Edit</value>
  </data>
  <data name="ResOptionLanguageList" xml:space="preserve">
    <value>Option Language List</value>
  </data>
  <data name="ResOptionList" xml:space="preserve">
    <value>Option List</value>
  </data>
  <data name="ResOptionList1" xml:space="preserve">
    <value>Option List</value>
  </data>
  <data name="ResOptionRemarks" xml:space="preserve">
    <value>Options Remarks</value>
  </data>
  <data name="ResOptions" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="ResOptions1" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="ResOptionsDetailsCannotbeEmpty" xml:space="preserve">
    <value>Option Details Cannot be Empty</value>
  </data>
  <data name="ResOptionSectionAssociation" xml:space="preserve">
    <value>Option Section Association</value>
  </data>
  <data name="ResOptionSectionAssociation1" xml:space="preserve">
    <value>Option Section Association</value>
  </data>
  <data name="ResOptionsisNotYetAssociated" xml:space="preserve">
    <value>Options is Not Yet Associated</value>
  </data>
  <data name="ResOR" xml:space="preserve">
    <value>OR</value>
  </data>
  <data name="ResOR1" xml:space="preserve">
    <value>OR</value>
  </data>
  <data name="ResOrder" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="ResOrder#" xml:space="preserve">
    <value>Order #</value>
  </data>
  <data name="ResOrder#1" xml:space="preserve">
    <value>Order#</value>
  </data>
  <data name="ResOrder#11" xml:space="preserve">
    <value>Order#</value>
  </data>
  <data name="ResOrder#2" xml:space="preserve">
    <value>Order #</value>
  </data>
  <data name="ResOrder#SearchList" xml:space="preserve">
    <value>Order # Search List</value>
  </data>
  <data name="ResOrder#SearchList1" xml:space="preserve">
    <value>Order # Search List</value>
  </data>
  <data name="ResOrderDate" xml:space="preserve">
    <value>Order Date</value>
  </data>
  <data name="ResOrderDate1" xml:space="preserve">
    <value>Order Date</value>
  </data>
  <data name="ResOrderDetails" xml:space="preserve">
    <value>Order Details</value>
  </data>
  <data name="ResOrderDetails1" xml:space="preserve">
    <value>Order Details</value>
  </data>
  <data name="ResOrderesQty" xml:space="preserve">
    <value>Ordered Qty</value>
  </data>
  <data name="ResOrderList" xml:space="preserve">
    <value>Order List</value>
  </data>
  <data name="ResOrderQty" xml:space="preserve">
    <value>Order Qty</value>
  </data>
  <data name="ResOrderQty1" xml:space="preserve">
    <value>Order Qty</value>
  </data>
  <data name="ResOrderQuantity" xml:space="preserve">
    <value>Order Quantity</value>
  </data>
  <data name="ResOrders" xml:space="preserve">
    <value>Orders</value>
  </data>
  <data name="ResOrderSearch" xml:space="preserve">
    <value>Order Search List</value>
  </data>
  <data name="ResOrderSearch1" xml:space="preserve">
    <value>Order Search List</value>
  </data>
  <data name="ResOrdersSelection" xml:space="preserve">
    <value>Orders Selection</value>
  </data>
  <data name="ResOrderToplevel" xml:space="preserve">
    <value>Order #/Top Level</value>
  </data>
  <data name="ResOriginalColumnName" xml:space="preserve">
    <value>Original Column Name</value>
  </data>
  <data name="ResOwner" xml:space="preserve">
    <value>Owner</value>
  </data>
  <data name="ResOwnershipHistory" xml:space="preserve">
    <value>Ownership History</value>
  </data>
  <data name="ResOwnershipHistory1" xml:space="preserve">
    <value>Ownership History</value>
  </data>
  <data name="ResPage" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="ResPanning" xml:space="preserve">
    <value>Panning</value>
  </data>
  <data name="ResPanning1" xml:space="preserve">
    <value>Panning</value>
  </data>
  <data name="Respanning2" xml:space="preserve">
    <value>ResPanning</value>
  </data>
  <data name="ResParent" xml:space="preserve">
    <value>View Parent</value>
  </data>
  <data name="ResParent1" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="ResParentDetails" xml:space="preserve">
    <value>Parent Details</value>
  </data>
  <data name="ResParentDetails1" xml:space="preserve">
    <value>Parent Details</value>
  </data>
  <data name="ResParentPartDetails" xml:space="preserve">
    <value>Parent Part Details</value>
  </data>
  <data name="ResParentPartDetails1" xml:space="preserve">
    <value>Parent Part Details</value>
  </data>
  <data name="ResParmaCode" xml:space="preserve">
    <value>Parma Code</value>
  </data>
  <data name="ResParmaCode1" xml:space="preserve">
    <value>Parma Code</value>
  </data>
  <data name="ResPart#" xml:space="preserve">
    <value>Part #</value>
  </data>
  <data name="ResPart#1" xml:space="preserve">
    <value>Part #</value>
  </data>
  <data name="ResPartAdd" xml:space="preserve">
    <value>Part Add</value>
  </data>
  <data name="ResPartAlreadyExistsInCart" xml:space="preserve">
    <value>Part already exists in cart</value>
  </data>
  <data name="ResPartAssembly#" xml:space="preserve">
    <value>Part / Assembly #</value>
  </data>
  <data name="ResPartAssembly#1" xml:space="preserve">
    <value>Part / Assembly #</value>
  </data>
  <data name="ResPartDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResPartDescription1" xml:space="preserve">
    <value>Part Description</value>
  </data>
  <data name="ResPartDescriptionLanguageList" xml:space="preserve">
    <value>Part Description Language List</value>
  </data>
  <data name="ResPartDetails" xml:space="preserve">
    <value>Part Details</value>
  </data>
  <data name="ResPartDetails1" xml:space="preserve">
    <value>Part Details</value>
  </data>
  <data name="ResPartDetailsAdd" xml:space="preserve">
    <value>Add Part Details</value>
  </data>
  <data name="ResPartDetailsAdd1" xml:space="preserve">
    <value>Add Part Details</value>
  </data>
  <data name="ResPartDetailsEdit" xml:space="preserve">
    <value>Part Details Edit</value>
  </data>
  <data name="ResPartDocuments" xml:space="preserve">
    <value>Part Documents</value>
  </data>
  <data name="ResPartDocuments1" xml:space="preserve">
    <value>Part Documents</value>
  </data>
  <data name="ResPartImage" xml:space="preserve">
    <value>Part Image</value>
  </data>
  <data name="ResPartImage1" xml:space="preserve">
    <value>Part Image</value>
  </data>
  <data name="ResPartImages" xml:space="preserve">
    <value>Part Images</value>
  </data>
  <data name="ResPartImages1" xml:space="preserve">
    <value>Part Images</value>
  </data>
  <data name="ResPartList" xml:space="preserve">
    <value>Part List</value>
  </data>
  <data name="ResPartList1" xml:space="preserve">
    <value>Part List</value>
  </data>
  <data name="ResPartName" xml:space="preserve">
    <value>Part Name</value>
  </data>
  <data name="ResPartName1" xml:space="preserve">
    <value>Part Name</value>
  </data>
  <data name="ResPartNoCannotBeEmpty" xml:space="preserve">
    <value>Part # cannot be empty</value>
  </data>
  <data name="ResPartNotes" xml:space="preserve">
    <value>Part Notes</value>
  </data>
  <data name="ResPartNotes1" xml:space="preserve">
    <value>Part Notes</value>
  </data>
  <data name="ResPartNotFound" xml:space="preserve">
    <value>Part not Found</value>
  </data>
  <data name="ResPartnumber" xml:space="preserve">
    <value>Part Number</value>
  </data>
  <data name="ResPartNumberDetails" xml:space="preserve">
    <value>PartNumberDetails</value>
  </data>
  <data name="ResPartNumberisAlredaySuperseded" xml:space="preserve">
    <value>Part Number is already Superseded</value>
  </data>
  <data name="ResParts" xml:space="preserve">
    <value>Parts</value>
  </data>
  <data name="ResParts&amp;ServiceSection?" xml:space="preserve">
    <value>Is Parts and Service Section?</value>
  </data>
  <data name="ResParts1" xml:space="preserve">
    <value>Parts</value>
  </data>
  <data name="ResPartsAssist" xml:space="preserve">
    <value>Parts Assist</value>
  </data>
  <data name="ResPartsDescriptionUpdation" xml:space="preserve">
    <value>Parts Description Updation</value>
  </data>
  <data name="ResPartsDescriptionUpdation1" xml:space="preserve">
    <value>Parts Description Updation</value>
  </data>
  <data name="ResPartsDetailsCannotbeEmpty" xml:space="preserve">
    <value>Parts Details Cannot be Empty</value>
  </data>
  <data name="ResPartSearchList" xml:space="preserve">
    <value>Part Search List</value>
  </data>
  <data name="ResPartServiceDocumentLink" xml:space="preserve">
    <value>Part Service Document Link</value>
  </data>
  <data name="ResPartServiceDocumentLink1" xml:space="preserve">
    <value>Part Service Document Link</value>
  </data>
  <data name="ResPartsImageUpload" xml:space="preserve">
    <value>Parts Image Upload</value>
  </data>
  <data name="ResPartsImageUpload1" xml:space="preserve">
    <value>Parts Image Upload</value>
  </data>
  <data name="ResPartsImport" xml:space="preserve">
    <value>Parts Import</value>
  </data>
  <data name="ResPartsImport1" xml:space="preserve">
    <value>Parts Import</value>
  </data>
  <data name="ResPartsInformation" xml:space="preserve">
    <value>Parts  Information</value>
  </data>
  <data name="ResPartsInformation1" xml:space="preserve">
    <value>Parts  Information</value>
  </data>
  <data name="ResPartsManuals" xml:space="preserve">
    <value>Parts Manuals</value>
  </data>
  <data name="ResPartsMaster" xml:space="preserve">
    <value>Parts Master</value>
  </data>
  <data name="ResPartsMaster-CustomerView" xml:space="preserve">
    <value>Parts Master-Customer View</value>
  </data>
  <data name="ResPartsMaster-CustomerView1" xml:space="preserve">
    <value>Parts Master-Customer View</value>
  </data>
  <data name="ResPartsMaster-Header" xml:space="preserve">
    <value>Parts Master - Header</value>
  </data>
  <data name="ResPartsMaster-Header1" xml:space="preserve">
    <value>Parts Master - Header</value>
  </data>
  <data name="ResPartsMaster1" xml:space="preserve">
    <value>Parts Master</value>
  </data>
  <data name="ResPartsSectionAssociation" xml:space="preserve">
    <value>Parts Section Association</value>
  </data>
  <data name="ResPartsSectionAssociation1" xml:space="preserve">
    <value>Parts Section Association</value>
  </data>
  <data name="ResPartStatus" xml:space="preserve">
    <value>Part Status</value>
  </data>
  <data name="ResPartsUsedReport" xml:space="preserve">
    <value>Parts Used Report</value>
  </data>
  <data name="ResPartsViewConfig" xml:space="preserve">
    <value>Parts View Config.</value>
  </data>
  <data name="ResPartsViewConfig1" xml:space="preserve">
    <value>Parts View Config.</value>
  </data>
  <data name="ResPartsViewConfiguration-List" xml:space="preserve">
    <value>Parts View Configuration-List</value>
  </data>
  <data name="ResPartsViewConfiguration-List1" xml:space="preserve">
    <value>Parts View Configuration-List</value>
  </data>
  <data name="ResPartsViewConfigure-Edit" xml:space="preserve">
    <value>Parts View Configure-Edit</value>
  </data>
  <data name="ResPartsViewConfigure-Edit1" xml:space="preserve">
    <value>Parts View Configure-Edit</value>
  </data>
  <data name="ResPartUsageReport" xml:space="preserve">
    <value>Part Usage Report</value>
  </data>
  <data name="ResPartUsedReport" xml:space="preserve">
    <value>Part Used Report</value>
  </data>
  <data name="ResPartWhereUsed" xml:space="preserve">
    <value>Part Where Used</value>
  </data>
  <data name="ResPassword1" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="ResPCodes" xml:space="preserve">
    <value>P Codes</value>
  </data>
  <data name="ResPDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="ResPDFConfigure" xml:space="preserve">
    <value>PDF Configure</value>
  </data>
  <data name="ResPDFPublish" xml:space="preserve">
    <value>PDF Publish</value>
  </data>
  <data name="ResPDFTemplate" xml:space="preserve">
    <value>PDF Template</value>
  </data>
  <data name="ResPDFTemplate1" xml:space="preserve">
    <value>PDF Template</value>
  </data>
  <data name="ResPentagon" xml:space="preserve">
    <value>Pentagon</value>
  </data>
  <data name="ResPharmaCode" xml:space="preserve">
    <value>Parma Code</value>
  </data>
  <data name="ResPhone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="ResPhone1" xml:space="preserve">
    <value>Phone1</value>
  </data>
  <data name="ResPhone2" xml:space="preserve">
    <value>Phone2</value>
  </data>
  <data name="ResPleaseconsultit" xml:space="preserve">
    <value>Please consult it.</value>
  </data>
  <data name="ResPleaseenterlogininformation" xml:space="preserve">
    <value>Please enter your login information</value>
  </data>
  <data name="ResPleaseenterTimeinHH" xml:space="preserve">
    <value>Please enter Time in HH:MM Format Ex:00:10</value>
  </data>
  <data name="ResPleasefillmandatoryfileds" xml:space="preserve">
    <value>Please fill mandatory fields</value>
  </data>
  <data name="ResPleasefindtheorderedlistofpartsinbelowtable" xml:space="preserve">
    <value>Please find the ordered list of parts in below table</value>
  </data>
  <data name="ResPleaseselectcheckbox" xml:space="preserve">
    <value>Please select checkbox</value>
  </data>
  <data name="ResPleaseselectFile" xml:space="preserve">
    <value>Please select File</value>
  </data>
  <data name="ResPleaseSelectheckbox" xml:space="preserve">
    <value>Please Select the Checkbox</value>
  </data>
  <data name="ResPleaseSelectMFGCodeFirst" xml:space="preserve">
    <value>Please select MFG Code first</value>
  </data>
  <data name="ResPleaseSelectTheBrand" xml:space="preserve">
    <value>Please Select The Brand type</value>
  </data>
  <data name="ResPleaseSelectTheRecord" xml:space="preserve">
    <value>Please Select The Record</value>
  </data>
  <data name="ResPleaseSelecttheRecordstoDelete" xml:space="preserve">
    <value>Please select the records to delete</value>
  </data>
  <data name="ResPleaseSelectValue" xml:space="preserve">
    <value>Please select value</value>
  </data>
  <data name="ResPlsSelHotSpotToDelete" xml:space="preserve">
    <value>Please select the Hotspot to delete</value>
  </data>
  <data name="ResPlsSelHotSpotToMove" xml:space="preserve">
    <value>Please select the Hotspot to move</value>
  </data>
  <data name="ResPlsVerifyBeforePublish" xml:space="preserve">
    <value>Check error report before publish</value>
  </data>
  <data name="ResPlsWaitWhilewePublish" xml:space="preserve">
    <value>Please wait while we publish</value>
  </data>
  <data name="ResPneumaticDiagrams" xml:space="preserve">
    <value>Pneumatic Diagrams</value>
  </data>
  <data name="ResPramaCode" xml:space="preserve">
    <value>Parma Code</value>
  </data>
  <data name="ResPreceedingAssembly" xml:space="preserve">
    <value>View Preceding Assembly</value>
  </data>
  <data name="ResPreceedingPart?" xml:space="preserve">
    <value>Preceding Part?</value>
  </data>
  <data name="ResPreceedingPart?1" xml:space="preserve">
    <value>Preceding Part?</value>
  </data>
  <data name="ResPrefix" xml:space="preserve">
    <value>Prefix</value>
  </data>
  <data name="ResPrefix1" xml:space="preserve">
    <value>Prefix</value>
  </data>
  <data name="ResPreview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="ResPrevImage" xml:space="preserve">
    <value>Previous Image</value>
  </data>
  <data name="ResPrevious" xml:space="preserve">
    <value>Previous image</value>
  </data>
  <data name="ResPreviousCommonHistoryNotFound" xml:space="preserve">
    <value>Previous Common History not found</value>
  </data>
  <data name="ResPreviousPurchaseHistory" xml:space="preserve">
    <value>Previous Purchase History</value>
  </data>
  <data name="ResPrevost" xml:space="preserve">
    <value>Prevost</value>
  </data>
  <data name="ResPrevost1" xml:space="preserve">
    <value>Prevost</value>
  </data>
  <data name="ResPrevPage" xml:space="preserve">
    <value>Previous Page</value>
  </data>
  <data name="ResPrint" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="ResPrint1" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="ResPrint2" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="ResPrintBOM" xml:space="preserve">
    <value>Print BOM (PDF)</value>
  </data>
  <data name="ResPrintBOM1" xml:space="preserve">
    <value>Print BOM</value>
  </data>
  <data name="ResPrintBoth" xml:space="preserve">
    <value>Print Both (PDF)</value>
  </data>
  <data name="ResPrintBoth1" xml:space="preserve">
    <value>Print Both</value>
  </data>
  <data name="ResPrintCatalogue" xml:space="preserve">
    <value>Print Catalogue</value>
  </data>
  <data name="ResPrintDrawing" xml:space="preserve">
    <value>Print Drawing (PDF)</value>
  </data>
  <data name="ResPrintDrawing1" xml:space="preserve">
    <value>Print Drawing</value>
  </data>
  <data name="ResProductType" xml:space="preserve">
    <value>Product Type</value>
  </data>
  <data name="ResProductType-Add" xml:space="preserve">
    <value>Product Type - Add</value>
  </data>
  <data name="ResProductType-Add1" xml:space="preserve">
    <value>ResProductType - Add</value>
  </data>
  <data name="ResProductType1" xml:space="preserve">
    <value>Product Type </value>
  </data>
  <data name="ResProductType11" xml:space="preserve">
    <value>Product Type </value>
  </data>
  <data name="ResProductType2" xml:space="preserve">
    <value>Product Type</value>
  </data>
  <data name="ResProductTypeAttachments" xml:space="preserve">
    <value>Product Type Attachments</value>
  </data>
  <data name="ResProductTypeCode" xml:space="preserve">
    <value>Product Type Code</value>
  </data>
  <data name="ResProductTypeCode1" xml:space="preserve">
    <value>Product Type Code</value>
  </data>
  <data name="ResProductTypeImages" xml:space="preserve">
    <value>Product Type Images</value>
  </data>
  <data name="ResProductTypeImages1" xml:space="preserve">
    <value>Product Type Images</value>
  </data>
  <data name="ResProductTypeList" xml:space="preserve">
    <value>Product Type List</value>
  </data>
  <data name="ResProductTypeList1" xml:space="preserve">
    <value>Product Type List</value>
  </data>
  <data name="ResProductTypeName" xml:space="preserve">
    <value>Product Type Name</value>
  </data>
  <data name="ResProductTypeName1" xml:space="preserve">
    <value>Product Type Name</value>
  </data>
  <data name="ResProductTypeNameSearchList" xml:space="preserve">
    <value>Product Type Name Search List</value>
  </data>
  <data name="ResProductTypeNameSearchList1" xml:space="preserve">
    <value>Product Type Name Search List</value>
  </data>
  <data name="ResProdutTypeList" xml:space="preserve">
    <value>Product Type List</value>
  </data>
  <data name="ResProdutTypeList1" xml:space="preserve">
    <value>Product Type List</value>
  </data>
  <data name="ResProgramsandRevisionsList" xml:space="preserve">
    <value>Programs and Revisions List</value>
  </data>
  <data name="ResPublicationandServiceAfterSalesCoordinator" xml:space="preserve">
    <value>Publication and Service After Sales Coordinator</value>
  </data>
  <data name="ResPublish" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="ResPublishedDate" xml:space="preserve">
    <value>Published Date</value>
  </data>
  <data name="ResPublishedDate1" xml:space="preserve">
    <value>Published Date</value>
  </data>
  <data name="ResPublishList" xml:space="preserve">
    <value>Publish List</value>
  </data>
  <data name="ResPublishLog" xml:space="preserve">
    <value>Publish Log</value>
  </data>
  <data name="ResPublishLog1" xml:space="preserve">
    <value>Publish Log</value>
  </data>
  <data name="ResQty" xml:space="preserve">
    <value>Qty.</value>
  </data>
  <data name="ResQty1" xml:space="preserve">
    <value>Qty.</value>
  </data>
  <data name="ResQuantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="ResQueryName" xml:space="preserve">
    <value>Query Name</value>
  </data>
  <data name="ResQueryName1" xml:space="preserve">
    <value>Query Name</value>
  </data>
  <data name="ResQuotationRequest" xml:space="preserve">
    <value>Quotation Request</value>
  </data>
  <data name="ResQuotationRequestAdd" xml:space="preserve">
    <value>Quotation Request - Add</value>
  </data>
  <data name="ResQuotationRequestedSuccessfully" xml:space="preserve">
    <value>Quotation request sent successfully</value>
  </data>
  <data name="ResQuotationRequestPart" xml:space="preserve">
    <value>Quotation Requested Parts</value>
  </data>
  <data name="ResRange" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="ResRange1" xml:space="preserve">
    <value>Range1</value>
  </data>
  <data name="ResRange10" xml:space="preserve">
    <value>Range10</value>
  </data>
  <data name="ResRange2" xml:space="preserve">
    <value>Range2</value>
  </data>
  <data name="ResRange3" xml:space="preserve">
    <value>Range3</value>
  </data>
  <data name="ResRange4" xml:space="preserve">
    <value>Range4</value>
  </data>
  <data name="ResRange5" xml:space="preserve">
    <value>Range5</value>
  </data>
  <data name="ResRange6" xml:space="preserve">
    <value>Range6</value>
  </data>
  <data name="ResRange7" xml:space="preserve">
    <value>Range7</value>
  </data>
  <data name="ResRange8" xml:space="preserve">
    <value>Range8</value>
  </data>
  <data name="ResRange9" xml:space="preserve">
    <value>Range9</value>
  </data>
  <data name="resReachedLastLowOftheGrid" xml:space="preserve">
    <value>Reached last row of Grid</value>
  </data>
  <data name="ResRead" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="ResRead1" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="ResRec" xml:space="preserve">
    <value>Rec</value>
  </data>
  <data name="ResRec1" xml:space="preserve">
    <value>Rec</value>
  </data>
  <data name="ResRecent" xml:space="preserve">
    <value>Recent</value>
  </data>
  <data name="ResRecommendedPartList" xml:space="preserve">
    <value>Recommended Part List</value>
  </data>
  <data name="ResRecommendedPartList1" xml:space="preserve">
    <value>Recommended Part List</value>
  </data>
  <data name="ResRecommendedParts" xml:space="preserve">
    <value>Recommended Parts</value>
  </data>
  <data name="ResRecommendedParts1" xml:space="preserve">
    <value>Recommended Parts</value>
  </data>
  <data name="ResRecommendedPartsDetails" xml:space="preserve">
    <value>Recommended Parts Details</value>
  </data>
  <data name="ResRecomPart" xml:space="preserve">
    <value>Recom. Part</value>
  </data>
  <data name="ResRecomPart1" xml:space="preserve">
    <value>Recom. Part</value>
  </data>
  <data name="ResRecordIsLocked" xml:space="preserve">
    <value>Record Is Locked</value>
  </data>
  <data name="ResRectangle" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="ResRedo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="ResRedoHotspot" xml:space="preserve">
    <value>Redo Hotspot</value>
  </data>
  <data name="ResRedoHotspot1" xml:space="preserve">
    <value>Redo Hotspot</value>
  </data>
  <data name="ResRef.Ass.Description" xml:space="preserve">
    <value>Ref.Ass.Description</value>
  </data>
  <data name="ResRef.AssemblyNo" xml:space="preserve">
    <value>Ref.Ass. No</value>
  </data>
  <data name="ResRefAssembliesInfo" xml:space="preserve">
    <value>Ref. Assemblies Info</value>
  </data>
  <data name="ResRefAssembliesInfo1" xml:space="preserve">
    <value>Reference Assemblies Info</value>
  </data>
  <data name="ResRefAssembliesInfo11" xml:space="preserve">
    <value>Reference Assemblies Info</value>
  </data>
  <data name="ResRefAssembliesInfo2" xml:space="preserve">
    <value>Ref. Assemblies Info</value>
  </data>
  <data name="ResRefAssemblyDes" xml:space="preserve">
    <value>Ref. Assy. Desc.</value>
  </data>
  <data name="ResRefAssemblyDes1" xml:space="preserve">
    <value>Reference Assembly Description</value>
  </data>
  <data name="ResRefAssemblyMFGCode" xml:space="preserve">
    <value>Ref.Assy.  MFG Code</value>
  </data>
  <data name="ResRefAssemblyMFGCode1" xml:space="preserve">
    <value>Reference Assembly MFG Code</value>
  </data>
  <data name="ResRefAssMFGCode" xml:space="preserve">
    <value>Refer. Assembly MFG Code</value>
  </data>
  <data name="ResReferenceAssembly" xml:space="preserve">
    <value>Reference Assembly</value>
  </data>
  <data name="ResReferenceAssembly#" xml:space="preserve">
    <value>Ref. Assy. #</value>
  </data>
  <data name="ResReferenceAssembly#1" xml:space="preserve">
    <value>Reference  Assembly #</value>
  </data>
  <data name="ResReferenceAssemblyDescription" xml:space="preserve">
    <value>Reference  Assembly  Description</value>
  </data>
  <data name="ResReferenceAssemblyName" xml:space="preserve">
    <value>Reference Assembly Name</value>
  </data>
  <data name="ResReferenceAssemblyName1" xml:space="preserve">
    <value>Reference Assembly Name</value>
  </data>
  <data name="ResRefNo" xml:space="preserve">
    <value>Ref.No</value>
  </data>
  <data name="ResRefresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="ResReleaseNoteInformation" xml:space="preserve">
    <value>Release Note Information</value>
  </data>
  <data name="ResRemarks" xml:space="preserve">
    <value>Remarks</value>
  </data>
  <data name="ResRemarks1" xml:space="preserve">
    <value>Remarks</value>
  </data>
  <data name="ResRemarksType" xml:space="preserve">
    <value>Remarks Type</value>
  </data>
  <data name="ResRemove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="ResRemoveBookmark" xml:space="preserve">
    <value>Remove Bookmark</value>
  </data>
  <data name="ResRemoveFilter" xml:space="preserve">
    <value>Remove Filter</value>
  </data>
  <data name="ResRemovefromcart" xml:space="preserve">
    <value>Remove from cart</value>
  </data>
  <data name="ResRemovefromcart1" xml:space="preserve">
    <value>Remove from cart</value>
  </data>
  <data name="ResRenameDrawing" xml:space="preserve">
    <value>Rename Drawing</value>
  </data>
  <data name="ResRenameDrawing1" xml:space="preserve">
    <value>Rename Drawing</value>
  </data>
  <data name="ResRenameDrawingLog" xml:space="preserve">
    <value>Rename Drawing Log</value>
  </data>
  <data name="ResRenameDrawingLog1" xml:space="preserve">
    <value>Rename Drawing Log</value>
  </data>
  <data name="ResRenamePart" xml:space="preserve">
    <value>Rename Part</value>
  </data>
  <data name="ResRenamePart#as" xml:space="preserve">
    <value>Rename Part # as</value>
  </data>
  <data name="ResRenamePart#as1" xml:space="preserve">
    <value>Rename Part # as</value>
  </data>
  <data name="ResRenamePart1" xml:space="preserve">
    <value>Rename Part</value>
  </data>
  <data name="ResRenamePartHistory" xml:space="preserve">
    <value>Rename Part History</value>
  </data>
  <data name="ResRenamePartLog" xml:space="preserve">
    <value>Rename Part Log</value>
  </data>
  <data name="ResRenamePartLog1" xml:space="preserve">
    <value>Rename Part Log</value>
  </data>
  <data name="ResRepalcedPartAddedtoShoppingCart" xml:space="preserve">
    <value>Replaced Part(s) Added to Shopping Cart</value>
  </data>
  <data name="ResReplacebyPart#" xml:space="preserve">
    <value>Replace by Part #</value>
  </data>
  <data name="ResReplacebyPart#1" xml:space="preserve">
    <value>Replace by Part #</value>
  </data>
  <data name="ResReplacePart" xml:space="preserve">
    <value>Replace Part</value>
  </data>
  <data name="ResReplacePart1" xml:space="preserve">
    <value>Replace Part</value>
  </data>
  <data name="ResReplacePartHistory" xml:space="preserve">
    <value>Replace Part History</value>
  </data>
  <data name="ResReplacePartLog" xml:space="preserve">
    <value>Replace Part Log</value>
  </data>
  <data name="ResReplacePartLog1" xml:space="preserve">
    <value>Replace Part Log</value>
  </data>
  <data name="ResReplacingCode" xml:space="preserve">
    <value>Replacing Code</value>
  </data>
  <data name="ResReplacingCode1" xml:space="preserve">
    <value>Replacing Code</value>
  </data>
  <data name="ResReport" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="ResReport1" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="ResReportedAttachments" xml:space="preserve">
    <value>Reported Attachments</value>
  </data>
  <data name="ResReportedBy" xml:space="preserve">
    <value>Reported By</value>
  </data>
  <data name="ResReportedBy1" xml:space="preserve">
    <value>Reported By</value>
  </data>
  <data name="ResReportedDate&amp;Time" xml:space="preserve">
    <value>Reported Date &amp; Time</value>
  </data>
  <data name="ResReportedDate&amp;Time1" xml:space="preserve">
    <value>Reported Date &amp; Time</value>
  </data>
  <data name="ResReportError" xml:space="preserve">
    <value>Report Error</value>
  </data>
  <data name="ResReportError1" xml:space="preserve">
    <value>Report Error</value>
  </data>
  <data name="ResRequest#" xml:space="preserve">
    <value>Request #</value>
  </data>
  <data name="ResRequest#1" xml:space="preserve">
    <value>Request #</value>
  </data>
  <data name="ResRequestDate" xml:space="preserve">
    <value>Request Date</value>
  </data>
  <data name="ResRequestDate1" xml:space="preserve">
    <value>Request Date</value>
  </data>
  <data name="ResRequester" xml:space="preserve">
    <value>Requester</value>
  </data>
  <data name="ResRequesterEmail" xml:space="preserve">
    <value>Requester Email</value>
  </data>
  <data name="ResRequesterPhone" xml:space="preserve">
    <value>Requester Phone #</value>
  </data>
  <data name="ResReset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ResResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="ResResetPassword1" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="ResResolution" xml:space="preserve">
    <value>Resolution</value>
  </data>
  <data name="ResResolution1" xml:space="preserve">
    <value>Resolution</value>
  </data>
  <data name="ResResolvedBy" xml:space="preserve">
    <value>Resolved By</value>
  </data>
  <data name="ResResolvedBy1" xml:space="preserve">
    <value>Resolved By</value>
  </data>
  <data name="ResResolvedDate&amp;Time" xml:space="preserve">
    <value>Resolved Date &amp; Time</value>
  </data>
  <data name="ResResolvedDate&amp;Time1" xml:space="preserve">
    <value>Resolved Date &amp; Time</value>
  </data>
  <data name="ResRespondedAttachments" xml:space="preserve">
    <value>Responded Attachments</value>
  </data>
  <data name="ResRespondedBy" xml:space="preserve">
    <value>Responded By</value>
  </data>
  <data name="ResRespondedBy1" xml:space="preserve">
    <value>Responded By</value>
  </data>
  <data name="ResRespondedDate&amp;Time" xml:space="preserve">
    <value>Responded Date &amp; Time</value>
  </data>
  <data name="ResRespondedDate&amp;Time1" xml:space="preserve">
    <value>Responded Date &amp; Time</value>
  </data>
  <data name="ResRevisionHistory" xml:space="preserve">
    <value>Revision History</value>
  </data>
  <data name="ResRevisionHistoryList" xml:space="preserve">
    <value>Revision History List</value>
  </data>
  <data name="ResRghtClickToCopy" xml:space="preserve">
    <value>Right click on the Hotspot to copy</value>
  </data>
  <data name="ResRoad#" xml:space="preserve">
    <value> Road #</value>
  </data>
  <data name="ResRoad#1" xml:space="preserve">
    <value> Road #</value>
  </data>
  <data name="ResRoad#History" xml:space="preserve">
    <value>Road # History</value>
  </data>
  <data name="ResRoad#History1" xml:space="preserve">
    <value>Road # History</value>
  </data>
  <data name="ResRoad#Search" xml:space="preserve">
    <value>Road # Search</value>
  </data>
  <data name="ResRoad#Search1" xml:space="preserve">
    <value>Road # Search</value>
  </data>
  <data name="ResRoad#SearchList" xml:space="preserve">
    <value>Road # Search List</value>
  </data>
  <data name="ResRoad#SearchList1" xml:space="preserve">
    <value>Road # Search List</value>
  </data>
  <data name="ResRole" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="ResRole1" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="ResRoleAuthor" xml:space="preserve">
    <value>Role-Author</value>
  </data>
  <data name="ResRoleAuthor1" xml:space="preserve">
    <value>Role-Author</value>
  </data>
  <data name="ResRoleDetails" xml:space="preserve">
    <value>Role Details</value>
  </data>
  <data name="ResRoleList" xml:space="preserve">
    <value>Role List</value>
  </data>
  <data name="ResRoleList1" xml:space="preserve">
    <value>Role List</value>
  </data>
  <data name="ResRoleName" xml:space="preserve">
    <value>Role Name</value>
  </data>
  <data name="ResRoleName1" xml:space="preserve">
    <value>Role Name</value>
  </data>
  <data name="ResRoleViewer" xml:space="preserve">
    <value>Role-Viewer</value>
  </data>
  <data name="ResRoleViewer1" xml:space="preserve">
    <value>Role-Viewer</value>
  </data>
  <data name="Ressamepartcannotreplace" xml:space="preserve">
    <value>Same part cannot replace</value>
  </data>
  <data name="RessamePartNopresentinPartDeatils" xml:space="preserve">
    <value>Same Part # Present in Part Details</value>
  </data>
  <data name="RessamePartNopresentinReplaceDeatils" xml:space="preserve">
    <value>Same Part # Present in Replace Part Details</value>
  </data>
  <data name="ResSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ResSave1" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ResSavedCart1" xml:space="preserve">
    <value>Saved Cart</value>
  </data>
  <data name="ResSavedCartName" xml:space="preserve">
    <value>Saved Cart Name</value>
  </data>
  <data name="ResSaveImage" xml:space="preserve">
    <value>Save Image</value>
  </data>
  <data name="ResSaveQuery" xml:space="preserve">
    <value>Save Query</value>
  </data>
  <data name="ResSaveQuery1" xml:space="preserve">
    <value>Save Query</value>
  </data>
  <data name="ResSearch" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="ResSearch1" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="ResSearchAssembly" xml:space="preserve">
    <value>Search Assembly</value>
  </data>
  <data name="ResSearchAssembly1" xml:space="preserve">
    <value>Search Assembly</value>
  </data>
  <data name="ResSearchBy" xml:space="preserve">
    <value>Search By</value>
  </data>
  <data name="ResSearchedCustomerList" xml:space="preserve">
    <value>Searched Customer List</value>
  </data>
  <data name="ResSearchedCustomerList1" xml:space="preserve">
    <value>Searched Customer List</value>
  </data>
  <data name="ResSearchformenu" xml:space="preserve">
    <value>Search for menu ...</value>
  </data>
  <data name="ResSearchNova" xml:space="preserve">
    <value>Search By Order #, Section Page, Part Page, Part Etc </value>
  </data>
  <data name="ResSearchPartAssembly" xml:space="preserve">
    <value>Search parts by assembly</value>
  </data>
  <data name="ResSearchPrevost" xml:space="preserve">
    <value>Search By VIN, Section Page, Part Page, Part Etc</value>
  </data>
  <data name="ResSearchProduct" xml:space="preserve">
    <value>Search product by model</value>
  </data>
  <data name="ResSearchVIN" xml:space="preserve">
    <value>Search VIN</value>
  </data>
  <data name="ResSearchVINToplevel" xml:space="preserve">
    <value>Search VIN, Top levels</value>
  </data>
  <data name="ResSection" xml:space="preserve">
    <value>Section</value>
  </data>
  <data name="ResSection#" xml:space="preserve">
    <value>Section #</value>
  </data>
  <data name="ResSection#1" xml:space="preserve">
    <value>Section #</value>
  </data>
  <data name="ResSectionAssociation" xml:space="preserve">
    <value>Section Association</value>
  </data>
  <data name="ResSectionAssociation1" xml:space="preserve">
    <value>Section Association</value>
  </data>
  <data name="ResSectionAttachments" xml:space="preserve">
    <value>Section Attachments</value>
  </data>
  <data name="ResSectionAttachments1" xml:space="preserve">
    <value>Section Attachments</value>
  </data>
  <data name="ResSectionCode" xml:space="preserve">
    <value>Section Code</value>
  </data>
  <data name="ResSectionCode1" xml:space="preserve">
    <value>Section Code</value>
  </data>
  <data name="ResSectionCustomerLink" xml:space="preserve">
    <value>Section Customer Document</value>
  </data>
  <data name="ResSectionCustomerLinkDocument" xml:space="preserve">
    <value>Section Customer Link Document</value>
  </data>
  <data name="ResSectionCustomerView" xml:space="preserve">
    <value>Section-Customer View</value>
  </data>
  <data name="ResSectionCustomerView1" xml:space="preserve">
    <value>Section-Customer View</value>
  </data>
  <data name="ResSectionDetails" xml:space="preserve">
    <value>Section Details</value>
  </data>
  <data name="ResSectionDrawingLog" xml:space="preserve">
    <value>Section Drawing Log</value>
  </data>
  <data name="ResSectionDrawingLog1" xml:space="preserve">
    <value>Section Drawing Log</value>
  </data>
  <data name="ResSectionImages" xml:space="preserve">
    <value>Section Images</value>
  </data>
  <data name="ResSectionImages1" xml:space="preserve">
    <value>Section Images</value>
  </data>
  <data name="ResSectionImport" xml:space="preserve">
    <value>Section Import</value>
  </data>
  <data name="ResSectionImport1" xml:space="preserve">
    <value>Section Import</value>
  </data>
  <data name="ResSectionLanguage-Add" xml:space="preserve">
    <value>Section Language-Add</value>
  </data>
  <data name="ResSectionLanguage-Add1" xml:space="preserve">
    <value>Section Language-Add</value>
  </data>
  <data name="ResSectionLanguage-Edit" xml:space="preserve">
    <value>Section Language-Edit</value>
  </data>
  <data name="ResSectionLanguage-Edit1" xml:space="preserve">
    <value>Section Language-Edit</value>
  </data>
  <data name="ResSectionLanguage-List" xml:space="preserve">
    <value>Section Language-List</value>
  </data>
  <data name="ResSectionLanguage-List1" xml:space="preserve">
    <value>Section Language-List</value>
  </data>
  <data name="ResSectionLanguageList" xml:space="preserve">
    <value>Section Language List</value>
  </data>
  <data name="ResSectionList" xml:space="preserve">
    <value>Section List</value>
  </data>
  <data name="ResSectionList1" xml:space="preserve">
    <value>Section List</value>
  </data>
  <data name="ResSectionName" xml:space="preserve">
    <value>Section Name</value>
  </data>
  <data name="ResSectionName1" xml:space="preserve">
    <value>Section Name</value>
  </data>
  <data name="ResSectionNameUpdation" xml:space="preserve">
    <value>Section Name Updation</value>
  </data>
  <data name="ResSectionNameUpdation1" xml:space="preserve">
    <value>Section Name Updation</value>
  </data>
  <data name="ResSectionPrint" xml:space="preserve">
    <value>Section Print</value>
  </data>
  <data name="ResSectionPrint1" xml:space="preserve">
    <value>Section Print</value>
  </data>
  <data name="ResSectionRemarks" xml:space="preserve">
    <value>Section Remarks</value>
  </data>
  <data name="ResSections" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="ResSections1" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="ResSectionsAssociation" xml:space="preserve">
    <value>Sections Association</value>
  </data>
  <data name="ResSectionsAssociation1" xml:space="preserve">
    <value>Sections Association</value>
  </data>
  <data name="ResSectionServiceDocumentLink" xml:space="preserve">
    <value>Sec Service Document Link</value>
  </data>
  <data name="ResSectionServiceDocumentLink1" xml:space="preserve">
    <value>Section Service Document Link</value>
  </data>
  <data name="ResSectionServiceDocumentLink2" xml:space="preserve">
    <value>Sec Service Document Link</value>
  </data>
  <data name="ResSectionServiceLink" xml:space="preserve">
    <value>Section Service Link</value>
  </data>
  <data name="ResSectionServiceLinkDocument" xml:space="preserve">
    <value>Section Service Link Document</value>
  </data>
  <data name="ResSelect" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="ResSelect1" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="ResSelectBrandName" xml:space="preserve">
    <value>Select Brand Name</value>
  </data>
  <data name="ResSelectColumn" xml:space="preserve">
    <value>Select Column</value>
  </data>
  <data name="ResSelectColumn1" xml:space="preserve">
    <value>Select Column</value>
  </data>
  <data name="ResSelectCondition" xml:space="preserve">
    <value>Select Condition</value>
  </data>
  <data name="ResSelectCondition1" xml:space="preserve">
    <value>Select Condition</value>
  </data>
  <data name="ResSelectDropdown" xml:space="preserve">
    <value>Select Dropdown</value>
  </data>
  <data name="ResSelectedAssemblyHasNoBOMpartDetails" xml:space="preserve">
    <value>Selected Assembly has no BOM part details</value>
  </data>
  <data name="ResSelectedConditions" xml:space="preserve">
    <value>Selected Conditions</value>
  </data>
  <data name="ResSelectedConditions1" xml:space="preserve">
    <value>Selected Conditions</value>
  </data>
  <data name="ResSelectedFileisnotanExcelFile" xml:space="preserve">
    <value>Selected File is not an Excel File</value>
  </data>
  <data name="ResSelectedFileisnotanTextFile" xml:space="preserve">
    <value>Selected File is not a text (.txt) file</value>
  </data>
  <data name="ResSelectedMaskingCodeNotFound" xml:space="preserve">
    <value>Selected Masking Code Not Found</value>
  </data>
  <data name="ResSelectedOrders" xml:space="preserve">
    <value>Selected Orders</value>
  </data>
  <data name="ResSelectFromList" xml:space="preserve">
    <value>Select From List</value>
  </data>
  <data name="ResSelectFromList1" xml:space="preserve">
    <value>Select From List</value>
  </data>
  <data name="ResSelectMFGCode" xml:space="preserve">
    <value>Select MFG Code</value>
  </data>
  <data name="ResSelectMFGCode1" xml:space="preserve">
    <value>Select MFG Code</value>
  </data>
  <data name="ResSelectMFGcodeFirst" xml:space="preserve">
    <value>Select MFG code first</value>
  </data>
  <data name="ResSelectModel" xml:space="preserve">
    <value>Select Model </value>
  </data>
  <data name="ResSelectOperator" xml:space="preserve">
    <value>Select Operator</value>
  </data>
  <data name="ResSelectOperator1" xml:space="preserve">
    <value>Select Operator</value>
  </data>
  <data name="ResSelectProductType" xml:space="preserve">
    <value>Select Product Type</value>
  </data>
  <data name="ResSelectQuery" xml:space="preserve">
    <value>Select Query</value>
  </data>
  <data name="ResSelectQuery1" xml:space="preserve">
    <value>Select Query</value>
  </data>
  <data name="ResSelectScreen" xml:space="preserve">
    <value>Select Screen</value>
  </data>
  <data name="ResSelectSections" xml:space="preserve">
    <value>Select Sections</value>
  </data>
  <data name="ResSelectSections1" xml:space="preserve">
    <value>Select Sections</value>
  </data>
  <data name="ResSeletedFileCannotbeEmpty" xml:space="preserve">
    <value>Selected File Cannot be Empty</value>
  </data>
  <data name="ResSend" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="ResSend1" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="ResSendMail" xml:space="preserve">
    <value>Send Mail</value>
  </data>
  <data name="ResSendMail1" xml:space="preserve">
    <value>Send Mail</value>
  </data>
  <data name="ResSeq" xml:space="preserve">
    <value>Seq.</value>
  </data>
  <data name="ResSeq1" xml:space="preserve">
    <value>Seq.</value>
  </data>
  <data name="ResSequence" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="ResSequence#" xml:space="preserve">
    <value>Sequence #</value>
  </data>
  <data name="ResSequence#1" xml:space="preserve">
    <value>Sequence #</value>
  </data>
  <data name="ResSequence1" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="Ressequencelessthan255" xml:space="preserve">
    <value>Enter sequence less than 255</value>
  </data>
  <data name="ResSequenceNo" xml:space="preserve">
    <value>Sequence#</value>
  </data>
  <data name="ResSequenceNo1" xml:space="preserve">
    <value>Sequence No.</value>
  </data>
  <data name="ResSequenceNumber" xml:space="preserve">
    <value>Sequence No</value>
  </data>
  <data name="ResSerialAssociation" xml:space="preserve">
    <value>Serial Association</value>
  </data>
  <data name="ResSerialNoAssociation" xml:space="preserve">
    <value>Serial# Association</value>
  </data>
  <data name="ResSerialNumberAndModelAssociation" xml:space="preserve">
    <value>Serial Number &amp; Model Association</value>
  </data>
  <data name="ResServiceDocument" xml:space="preserve">
    <value>Service Document</value>
  </data>
  <data name="ResServiceDocuments" xml:space="preserve">
    <value>Service Documents</value>
  </data>
  <data name="ResServiceLink" xml:space="preserve">
    <value>Service  Link</value>
  </data>
  <data name="ResServiceLink1" xml:space="preserve">
    <value>Service  Link</value>
  </data>
  <data name="ResServiceLinkAttach" xml:space="preserve">
    <value>Service Link Attach.</value>
  </data>
  <data name="ResServiceLinkAttach1" xml:space="preserve">
    <value>Service Link Attach.</value>
  </data>
  <data name="ResServiceLinkAttachments" xml:space="preserve">
    <value>Service Link / Attachments</value>
  </data>
  <data name="ResServiceLinkAttachments1" xml:space="preserve">
    <value>Service Link / Attachments</value>
  </data>
  <data name="ResServiceLinkOrAttachments" xml:space="preserve">
    <value>Service Link / Attachments</value>
  </data>
  <data name="ResServiceLinkOrAttachments1" xml:space="preserve">
    <value>Service Link / Attachments</value>
  </data>
  <data name="ResServiceManuals" xml:space="preserve">
    <value>View Service Manuals</value>
  </data>
  <data name="ResServiceManuals1" xml:space="preserve">
    <value>Service Manuals</value>
  </data>
  <data name="ResServiceManualsSearch" xml:space="preserve">
    <value>Service Manuals Search</value>
  </data>
  <data name="ResServiceProvider" xml:space="preserve">
    <value>Service Provider</value>
  </data>
  <data name="ResServiceSectionAssociation" xml:space="preserve">
    <value>Service Section Association</value>
  </data>
  <data name="ResServiceSectionAssociation1" xml:space="preserve">
    <value>Service Section Association</value>
  </data>
  <data name="ResServiceSub-Section" xml:space="preserve">
    <value>Service Sub-Section</value>
  </data>
  <data name="ResSet" xml:space="preserve">
    <value>Set</value>
  </data>
  <data name="ResSet1" xml:space="preserve">
    <value>Set</value>
  </data>
  <data name="ResSetMember" xml:space="preserve">
    <value>Set Member</value>
  </data>
  <data name="ResSetMember1" xml:space="preserve">
    <value>Set Member</value>
  </data>
  <data name="ResSetMemberDetails" xml:space="preserve">
    <value>Set Member Details</value>
  </data>
  <data name="ResSetMemberList" xml:space="preserve">
    <value>Set Member List</value>
  </data>
  <data name="ResSetMemberList1" xml:space="preserve">
    <value>Set Member List</value>
  </data>
  <data name="ResSetMembers" xml:space="preserve">
    <value>Set Members</value>
  </data>
  <data name="ResSetMembers1" xml:space="preserve">
    <value>Set Members</value>
  </data>
  <data name="ResShipToAddress" xml:space="preserve">
    <value>Ship to Address</value>
  </data>
  <data name="ResShipToAddressDetails" xml:space="preserve">
    <value>Ship To Address Details</value>
  </data>
  <data name="ResShipToAddressLanguageList" xml:space="preserve">
    <value>Ship To Address Language List</value>
  </data>
  <data name="ResShipToAdressAdd" xml:space="preserve">
    <value>Ship to address - Add</value>
  </data>
  <data name="ResShipToAdressEdit" xml:space="preserve">
    <value>Edit Ship To Address</value>
  </data>
  <data name="ResShoppingCart" xml:space="preserve">
    <value>View Shopping Cart</value>
  </data>
  <data name="ResShoppingCart1" xml:space="preserve">
    <value>Shopping Cart</value>
  </data>
  <data name="ResShoppingCartAttachments" xml:space="preserve">
    <value>Shopping Cart Attachments</value>
  </data>
  <data name="ResShoppingCartAttachments1" xml:space="preserve">
    <value>Shopping Cart Attachments</value>
  </data>
  <data name="ResShoppingCartConfig" xml:space="preserve">
    <value>Shopping Cart Config.</value>
  </data>
  <data name="ResShoppingCartConfig1" xml:space="preserve">
    <value>Shopping Cart Config.</value>
  </data>
  <data name="ResShoppingCartConfigure-Edit" xml:space="preserve">
    <value>Shopping Cart Configure-Edit</value>
  </data>
  <data name="ResShoppingCartConfigure-Edit1" xml:space="preserve">
    <value>Shopping Cart Configure-Edit</value>
  </data>
  <data name="ResShoppingCartGUIConfiguration" xml:space="preserve">
    <value>Shopping Cart GUI Configuration</value>
  </data>
  <data name="ResShoppingCartImport" xml:space="preserve">
    <value>Shopping Cart Import</value>
  </data>
  <data name="ResShoppingCartImport1" xml:space="preserve">
    <value>Shopping Cart Import</value>
  </data>
  <data name="ResShoppingCartList" xml:space="preserve">
    <value>Shopping Cart List</value>
  </data>
  <data name="ResShoppingDetails" xml:space="preserve">
    <value>Shopping Cart Details</value>
  </data>
  <data name="ResShoppingList" xml:space="preserve">
    <value>Shopping List</value>
  </data>
  <data name="ResShoppingList1" xml:space="preserve">
    <value>Shopping List</value>
  </data>
  <data name="ResshortCode" xml:space="preserve">
    <value>Short Code</value>
  </data>
  <data name="ResShowGUIConfig" xml:space="preserve">
    <value>Configure BOM data table</value>
  </data>
  <data name="ResShowHideHotspots" xml:space="preserve">
    <value>Show / Hide Hotspots</value>
  </data>
  <data name="ResShowQueryResult" xml:space="preserve">
    <value>Show Query Result</value>
  </data>
  <data name="ResShowResult" xml:space="preserve">
    <value>Show Result</value>
  </data>
  <data name="ResShowResult1" xml:space="preserve">
    <value>Show Result</value>
  </data>
  <data name="ResShowRevisionHistory" xml:space="preserve">
    <value>Show Revision History</value>
  </data>
  <data name="ResShowRevisionHistory1" xml:space="preserve">
    <value>Show Revision History</value>
  </data>
  <data name="ResShowVendorParttoCustomer" xml:space="preserve">
    <value>Show Vendor Part to Customer?</value>
  </data>
  <data name="ResShowvendorParttoCustomer?" xml:space="preserve">
    <value>Show Vend. Part?</value>
  </data>
  <data name="ResShowvendorParttoCustomer?1" xml:space="preserve">
    <value>Show vendor Part to Customer?</value>
  </data>
  <data name="ResSINo" xml:space="preserve">
    <value>SI.No.</value>
  </data>
  <data name="ResSLN" xml:space="preserve">
    <value>SLN</value>
  </data>
  <data name="ResSlNo" xml:space="preserve">
    <value>Sl No.</value>
  </data>
  <data name="ResSlNo1" xml:space="preserve">
    <value>Sl No.</value>
  </data>
  <data name="ResSLNum" xml:space="preserve">
    <value>S.L. Number</value>
  </data>
  <data name="ResSpaceisnotAllowed" xml:space="preserve">
    <value>Space is not allowed</value>
  </data>
  <data name="ResSpec" xml:space="preserve">
    <value>Spec.</value>
  </data>
  <data name="ResSpec1" xml:space="preserve">
    <value>Spec.</value>
  </data>
  <data name="ResSpecialInstructions" xml:space="preserve">
    <value>Special instructions</value>
  </data>
  <data name="ResSpecification" xml:space="preserve">
    <value>Specification</value>
  </data>
  <data name="ResSpecification1" xml:space="preserve">
    <value>Specification</value>
  </data>
  <data name="ResSquare" xml:space="preserve">
    <value>Square</value>
  </data>
  <data name="ResStack" xml:space="preserve">
    <value>Stack</value>
  </data>
  <data name="ResStandardRepairTime" xml:space="preserve">
    <value>Standard Repair Time</value>
  </data>
  <data name="ResStartWith" xml:space="preserve">
    <value>Start With</value>
  </data>
  <data name="ResStartWith1" xml:space="preserve">
    <value>Start With</value>
  </data>
  <data name="ResState" xml:space="preserve">
    <value>State / Province</value>
  </data>
  <data name="ResStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ResStatus1" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ResStatusLegend" xml:space="preserve">
    <value>Status Legend</value>
  </data>
  <data name="ResStatusLegend1" xml:space="preserve">
    <value>Status Legend</value>
  </data>
  <data name="ResStrightLine" xml:space="preserve">
    <value>Straight line</value>
  </data>
  <data name="ResSubject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="ResSubject1" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="ResSubSection" xml:space="preserve">
    <value>Sub Section</value>
  </data>
  <data name="ResSubSectionCustomerLink" xml:space="preserve">
    <value>Sub Section Customer Document</value>
  </data>
  <data name="ResSubSectionRemarks" xml:space="preserve">
    <value>Subsection Remarks</value>
  </data>
  <data name="ResSubSectionServiceLink" xml:space="preserve">
    <value>Sub Section Service Link</value>
  </data>
  <data name="ResSuggestedQty" xml:space="preserve">
    <value>Suggested Qty.</value>
  </data>
  <data name="ResSuggestedQty1" xml:space="preserve">
    <value>Suggested Qty.</value>
  </data>
  <data name="ResSup" xml:space="preserve">
    <value>Sup.</value>
  </data>
  <data name="ResSup1" xml:space="preserve">
    <value>Sup.</value>
  </data>
  <data name="ResSupersceedingPart?" xml:space="preserve">
    <value>Superseding Part?</value>
  </data>
  <data name="ResSupersceedingPart?1" xml:space="preserve">
    <value>Superseding Part?</value>
  </data>
  <data name="ResSupersedingPartsDetail" xml:space="preserve">
    <value>Replaced by Part Details</value>
  </data>
  <data name="ResSupersedingPartsDetail1" xml:space="preserve">
    <value>Superseding Parts Detail</value>
  </data>
  <data name="ResSuperseededPartDetail" xml:space="preserve">
    <value>Selected Part Details</value>
  </data>
  <data name="ResSuperseedingPartDetail" xml:space="preserve">
    <value>Replaced by Part Details</value>
  </data>
  <data name="ResSuperseedingPartDetails" xml:space="preserve">
    <value>Superseding Part Details</value>
  </data>
  <data name="ResSuperseedingPartDetailsAdd" xml:space="preserve">
    <value>Add Superseding Part Details</value>
  </data>
  <data name="ResSuperseedingPartDetailsAdd1" xml:space="preserve">
    <value>Add Superseding Part Details</value>
  </data>
  <data name="ResSupersession" xml:space="preserve">
    <value>Supersession</value>
  </data>
  <data name="ResSupersession1" xml:space="preserve">
    <value>Supersession</value>
  </data>
  <data name="ResSupersession?" xml:space="preserve">
    <value>Supersession?</value>
  </data>
  <data name="ResSupersession?1" xml:space="preserve">
    <value>Supersession?</value>
  </data>
  <data name="ResSupersessionDetails" xml:space="preserve">
    <value>Supersession Details</value>
  </data>
  <data name="ResSupersessionFromPartDeatils" xml:space="preserve">
    <value>From Part Details</value>
  </data>
  <data name="ResSupersessionList" xml:space="preserve">
    <value>Supersession List</value>
  </data>
  <data name="ResSupersessionRemarks" xml:space="preserve">
    <value>Supersession Remarks</value>
  </data>
  <data name="ResSupersessionType" xml:space="preserve">
    <value>Supersession Type</value>
  </data>
  <data name="ResSupersessionType1" xml:space="preserve">
    <value>Supersession Type</value>
  </data>
  <data name="ResSwitchLanguages" xml:space="preserve">
    <value>Switch Language</value>
  </data>
  <data name="ResTelephone4388431764" xml:space="preserve">
    <value>Telephone: ************</value>
  </data>
  <data name="ResTemplateCode" xml:space="preserve">
    <value>Template Code</value>
  </data>
  <data name="ResTemplateCode1" xml:space="preserve">
    <value>Template Code</value>
  </data>
  <data name="ResTemplateName" xml:space="preserve">
    <value>Template Name</value>
  </data>
  <data name="ResTemplateName1" xml:space="preserve">
    <value>Template Name</value>
  </data>
  <data name="ResTemplateSearchList" xml:space="preserve">
    <value>Template Search List</value>
  </data>
  <data name="ResTemplateSearchList1" xml:space="preserve">
    <value>Template Search List</value>
  </data>
  <data name="ResText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="ResThankYou" xml:space="preserve">
    <value>Thank You</value>
  </data>
  <data name="ResThisisasystemgeneratedemail.Pleasedonotreply!" xml:space="preserve">
    <value>This is a system generated email. Please do not reply!</value>
  </data>
  <data name="ResTimeOutException" xml:space="preserve">
    <value>Time out exception</value>
  </data>
  <data name="ResTimeTaken" xml:space="preserve">
    <value>Time Taken</value>
  </data>
  <data name="ResTimeTaken1" xml:space="preserve">
    <value>Time Taken</value>
  </data>
  <data name="ResTimeZone" xml:space="preserve">
    <value>Time Zone</value>
  </data>
  <data name="ResTitleBrandAdd" xml:space="preserve">
    <value>Brand - Add</value>
  </data>
  <data name="ResTo" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="ResTo1" xml:space="preserve">
    <value>To </value>
  </data>
  <data name="ResTOC" xml:space="preserve">
    <value>TOC</value>
  </data>
  <data name="ResTOC1" xml:space="preserve">
    <value>TOC</value>
  </data>
  <data name="ResTOCSelection" xml:space="preserve">
    <value>TOC Selection</value>
  </data>
  <data name="ResTOCSelection1" xml:space="preserve">
    <value>TOC Selection</value>
  </data>
  <data name="ResToggleBOM" xml:space="preserve">
    <value>Toggle BOM</value>
  </data>
  <data name="ResToggleDrawing" xml:space="preserve">
    <value>Toggle Drawing</value>
  </data>
  <data name="ResToggleScreen" xml:space="preserve">
    <value>Toggle Screen</value>
  </data>
  <data name="ResToMFGCode" xml:space="preserve">
    <value>To MFG Code</value>
  </data>
  <data name="ResToMFGCode1" xml:space="preserve">
    <value>To MFG Code</value>
  </data>
  <data name="ResToolbarConfigure-Edit" xml:space="preserve">
    <value>Toolbar Configure-Edit</value>
  </data>
  <data name="ResToolbarConfigure-Edit1" xml:space="preserve">
    <value>Toolbar Configure-Edit</value>
  </data>
  <data name="ResToolBarSettings" xml:space="preserve">
    <value>Tool Bar Settings</value>
  </data>
  <data name="ResToolBarSettings1" xml:space="preserve">
    <value>Tool Bar Settings</value>
  </data>
  <data name="ResToolName" xml:space="preserve">
    <value>Tool Name</value>
  </data>
  <data name="ResToolName1" xml:space="preserve">
    <value>Tool Name</value>
  </data>
  <data name="ResToPart#" xml:space="preserve">
    <value>To Part #</value>
  </data>
  <data name="ResToPart#1" xml:space="preserve">
    <value>To Part #</value>
  </data>
  <data name="ResToPartNumber" xml:space="preserve">
    <value>To Part Number</value>
  </data>
  <data name="ResToPartNumber1" xml:space="preserve">
    <value>To Part Number</value>
  </data>
  <data name="ResTopLevel" xml:space="preserve">
    <value>Top Level</value>
  </data>
  <data name="ResTopLevelCustomerLinkDocument" xml:space="preserve">
    <value>Top Level Customer Link Document</value>
  </data>
  <data name="ResTopLevelServiceLinkDocument" xml:space="preserve">
    <value>Top Level Service Link Document</value>
  </data>
  <data name="ResTopSectionCustomerLink" xml:space="preserve">
    <value>Top Section  Customer Document</value>
  </data>
  <data name="ResTopSectionServiceLink" xml:space="preserve">
    <value>Top Section Service Link</value>
  </data>
  <data name="ResToQty" xml:space="preserve">
    <value>To Qty.</value>
  </data>
  <data name="ResToQty1" xml:space="preserve">
    <value>To Qty.</value>
  </data>
  <data name="ResToSN" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="ResTotalCount" xml:space="preserve">
    <value>Total Count</value>
  </data>
  <data name="ResTotalCount1" xml:space="preserve">
    <value>Total Count</value>
  </data>
  <data name="ResTotalOptionCodes" xml:space="preserve">
    <value>Total Option Codes</value>
  </data>
  <data name="ResTotalOptionCodes1" xml:space="preserve">
    <value>Total Option Codes</value>
  </data>
  <data name="ResTransaction" xml:space="preserve">
    <value>Transaction</value>
  </data>
  <data name="ResTransaction1" xml:space="preserve">
    <value>Transaction</value>
  </data>
  <data name="ResTreeView" xml:space="preserve">
    <value>Tree View</value>
  </data>
  <data name="ResTreeView1" xml:space="preserve">
    <value>Tree View</value>
  </data>
  <data name="ResTriangle" xml:space="preserve">
    <value>Triangle</value>
  </data>
  <data name="ResUnabletoDelete" xml:space="preserve">
    <value>Unable to Delete</value>
  </data>
  <data name="ResUndo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="ResUndoHotspot" xml:space="preserve">
    <value>Undo Hotspot</value>
  </data>
  <data name="ResUndoHotspot1" xml:space="preserve">
    <value>Undo Hotspot</value>
  </data>
  <data name="ResUnlock" xml:space="preserve">
    <value> Unlock</value>
  </data>
  <data name="ResUnlock1" xml:space="preserve">
    <value>Unlock</value>
  </data>
  <data name="ResUnlock11" xml:space="preserve">
    <value>Unlock</value>
  </data>
  <data name="ResUnlock2" xml:space="preserve">
    <value>Unlock</value>
  </data>
  <data name="ResUnlockList" xml:space="preserve">
    <value>Unlock List </value>
  </data>
  <data name="ResUnlockList1" xml:space="preserve">
    <value>Unlock List</value>
  </data>
  <data name="ResUnlockList11" xml:space="preserve">
    <value>Unlock List</value>
  </data>
  <data name="ResUnlockList2" xml:space="preserve">
    <value>Unlock List </value>
  </data>
  <data name="ResUnLockLogDetails" xml:space="preserve">
    <value>Unlock Log Details</value>
  </data>
  <data name="ResUOM" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="ResUOM1" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="ResUOMCode" xml:space="preserve">
    <value>UOM Code</value>
  </data>
  <data name="ResUOMCode1" xml:space="preserve">
    <value>UOM Code</value>
  </data>
  <data name="ResUOMDescription" xml:space="preserve">
    <value>UOM Description</value>
  </data>
  <data name="ResUOMDescription1" xml:space="preserve">
    <value>UOM Description</value>
  </data>
  <data name="ResUOMDescriptionSearchList" xml:space="preserve">
    <value>UOM Description Search List</value>
  </data>
  <data name="ResUOMDescriptionSearchList1" xml:space="preserve">
    <value>UOM Description Search List</value>
  </data>
  <data name="ResUOMDetails" xml:space="preserve">
    <value>UOM Details</value>
  </data>
  <data name="ResUOMList" xml:space="preserve">
    <value>UOM List</value>
  </data>
  <data name="ResUOMList1" xml:space="preserve">
    <value>UOM List</value>
  </data>
  <data name="ResUpdate" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="ResUpdate1" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="ResUpdateConfigureAssemblyBOMUI-Viewer" xml:space="preserve">
    <value>Update Configure Assembly BOM UI-Viewer</value>
  </data>
  <data name="ResUpdateConfigureAssemblyBOMUI-Viewer1" xml:space="preserve">
    <value>Update Configure Assembly BOM UI-Viewer</value>
  </data>
  <data name="ResUpdateConfigureCartUI" xml:space="preserve">
    <value />
  </data>
  <data name="ResUpdateConfigureCartUI1" xml:space="preserve">
    <value />
  </data>
  <data name="ResUpdateConfigurePartsUI" xml:space="preserve">
    <value>Update Configure Parts UI</value>
  </data>
  <data name="ResUpdateConfigurePartsUI1" xml:space="preserve">
    <value>Update Configure Parts UI</value>
  </data>
  <data name="ResUpdateConfigureToolbar" xml:space="preserve">
    <value>Update Configure Toolbar</value>
  </data>
  <data name="ResUpdateConfigureToolbar1" xml:space="preserve">
    <value>Update Configure Toolbar</value>
  </data>
  <data name="ResUpdateCustomerDocument" xml:space="preserve">
    <value>Update Customer Document</value>
  </data>
  <data name="ResUpdatedSuccessfully" xml:space="preserve">
    <value>Updated Successfully</value>
  </data>
  <data name="ResUpdateNote" xml:space="preserve">
    <value>Update Note</value>
  </data>
  <data name="ResUpdateQuery" xml:space="preserve">
    <value>Update Query</value>
  </data>
  <data name="ResUpdateQuery1" xml:space="preserve">
    <value>Update Query</value>
  </data>
  <data name="ResUpdateServiceDocument" xml:space="preserve">
    <value>Update Service Document</value>
  </data>
  <data name="ResUpdateServiceDocument1" xml:space="preserve">
    <value>Update Service Document</value>
  </data>
  <data name="ResUpdateShoppingCartConfiguration" xml:space="preserve">
    <value>Update Shopping Cart Configuration</value>
  </data>
  <data name="ResUpdateShoppingCartConfiguration1" xml:space="preserve">
    <value>Update Shopping Cart Configuration</value>
  </data>
  <data name="ResUpdateStatus" xml:space="preserve">
    <value>Update Status</value>
  </data>
  <data name="ResUpdateToolbar" xml:space="preserve">
    <value>Update Toolbar</value>
  </data>
  <data name="ResUpdateToolbar1" xml:space="preserve">
    <value>Update Toolbar</value>
  </data>
  <data name="ResUpdateViewerToolbarConfigure" xml:space="preserve">
    <value>Update Viewer Toolbar Configuration</value>
  </data>
  <data name="ResUpdateViewerToolbarConfigure1" xml:space="preserve">
    <value>Update Viewer Toolbar Configuration</value>
  </data>
  <data name="ResUpdationConfigureToolbar" xml:space="preserve">
    <value>Updation Configure Toolbar</value>
  </data>
  <data name="ResUpdationConfigureToolbar1" xml:space="preserve">
    <value>Updation Configure Toolbar</value>
  </data>
  <data name="ResUploadDocumnets" xml:space="preserve">
    <value>Upload Service Documents</value>
  </data>
  <data name="ResUploadedBy" xml:space="preserve">
    <value>Uploaded By</value>
  </data>
  <data name="ResUploadedBy1" xml:space="preserve">
    <value>Uploaded By</value>
  </data>
  <data name="ResUploadedDate" xml:space="preserve">
    <value>Uploaded Date</value>
  </data>
  <data name="ResUploadedDate1" xml:space="preserve">
    <value>Uploaded Date</value>
  </data>
  <data name="ResUploadFile" xml:space="preserve">
    <value>Upload File</value>
  </data>
  <data name="ResUploadFile1" xml:space="preserve">
    <value>Upload File</value>
  </data>
  <data name="ResUploadServiceDocument" xml:space="preserve">
    <value>Upload Service Document</value>
  </data>
  <data name="ResUpToSN" xml:space="preserve">
    <value>Up to SN</value>
  </data>
  <data name="ResUpToSNShortCode" xml:space="preserve">
    <value>Up To SN Short Code</value>
  </data>
  <data name="ResURL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="ResUsageCount" xml:space="preserve">
    <value>Usage Count</value>
  </data>
  <data name="ResUser" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="ResUser1" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="ResUserAccessRights" xml:space="preserve">
    <value>User Access Rights</value>
  </data>
  <data name="ResUserAccessRights1" xml:space="preserve">
    <value>User Access Rights</value>
  </data>
  <data name="ResUserCategory" xml:space="preserve">
    <value>User Category</value>
  </data>
  <data name="ResUserDetails" xml:space="preserve">
    <value>User Details</value>
  </data>
  <data name="ResUserList" xml:space="preserve">
    <value>User List</value>
  </data>
  <data name="ResUserList1" xml:space="preserve">
    <value>User List</value>
  </data>
  <data name="ResUserLogReportAuthor" xml:space="preserve">
    <value>User Log Report Author</value>
  </data>
  <data name="ResUserLogReportCustomer" xml:space="preserve">
    <value>User Log Report Customer</value>
  </data>
  <data name="ResUserManual" xml:space="preserve">
    <value>User Manual</value>
  </data>
  <data name="ResUserName" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="ResUserName1" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="ResUserType" xml:space="preserve">
    <value>User Type</value>
  </data>
  <data name="ResValidFile" xml:space="preserve">
    <value>Valid File</value>
  </data>
  <data name="ResValue" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="ResValue1" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="ResVBNAUserRole" xml:space="preserve">
    <value>VBNA User Role</value>
  </data>
  <data name="ResVBNAUserRole1" xml:space="preserve">
    <value>VBNA User Role</value>
  </data>
  <data name="ResVBNAUsers" xml:space="preserve">
    <value>VBNA Users</value>
  </data>
  <data name="ResVBNAUsers1" xml:space="preserve">
    <value>VBNA Users</value>
  </data>
  <data name="ResVehicle" xml:space="preserve">
    <value>Vehicle</value>
  </data>
  <data name="ResVehicle-Add" xml:space="preserve">
    <value>Vehicle - Add</value>
  </data>
  <data name="ResVehicle1" xml:space="preserve">
    <value>Vehicle</value>
  </data>
  <data name="ResVehicleAssociation" xml:space="preserve">
    <value>Vehicle Association</value>
  </data>
  <data name="ResVehicleAssociation1" xml:space="preserve">
    <value>Vehicle Association</value>
  </data>
  <data name="ResVehicleAttachments" xml:space="preserve">
    <value>Vehicle Attachments</value>
  </data>
  <data name="ResVehicleAttachments1" xml:space="preserve">
    <value>Vehicle Attachments</value>
  </data>
  <data name="ResVehicleDetails" xml:space="preserve">
    <value>Vehicle Details</value>
  </data>
  <data name="ResVehicleDetails1" xml:space="preserve">
    <value>Vehicle Details</value>
  </data>
  <data name="ResVehicleDetailsCannotbeEmpty" xml:space="preserve">
    <value>Vehicle Details Cannot be Empty</value>
  </data>
  <data name="ResVehicleImages" xml:space="preserve">
    <value>Vehicle Images</value>
  </data>
  <data name="ResVehicleList" xml:space="preserve">
    <value>Vehicle List</value>
  </data>
  <data name="ResVehicleList1" xml:space="preserve">
    <value>Vehicle List</value>
  </data>
  <data name="ResVehicleOwnerShipHistoryDeatils" xml:space="preserve">
    <value>Vehicle Ownership History Details</value>
  </data>
  <data name="ResVehicleOwnershipHistoryEffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="ResVehicleRoad#ChangeList" xml:space="preserve">
    <value>Vehicle Road #Change List</value>
  </data>
  <data name="ResVehicleRoad#ChangeList1" xml:space="preserve">
    <value>Vehicle Road #Change List</value>
  </data>
  <data name="ResVehicleRoadHistoryDetails" xml:space="preserve">
    <value>Vehicle Road History Details</value>
  </data>
  <data name="ResVehicleVINHistoryDetails" xml:space="preserve">
    <value>Vehicle VIN History Details</value>
  </data>
  <data name="ResVehicle_OrderNo" xml:space="preserve">
    <value>Vehicle Order#</value>
  </data>
  <data name="ResVendor" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="ResVendor1" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="ResVendorAttachments" xml:space="preserve">
    <value>Vendor Attachments</value>
  </data>
  <data name="ResVendorAttachments1" xml:space="preserve">
    <value>Vendor Attachments</value>
  </data>
  <data name="ResVendorCode" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="ResVendorCode1" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="ResVendorDetails" xml:space="preserve">
    <value>Vendor Details</value>
  </data>
  <data name="ResVendorDetailsOtherLanguage" xml:space="preserve">
    <value>Vendor Details - Other Language</value>
  </data>
  <data name="ResVendorDetailsOtherLanguage1" xml:space="preserve">
    <value>Vendor Details - Other Language</value>
  </data>
  <data name="ResVendorDoesNotExist" xml:space="preserve">
    <value>Vendor does not exist</value>
  </data>
  <data name="ResVendorImages" xml:space="preserve">
    <value>Vendor Images</value>
  </data>
  <data name="ResVendorInfo" xml:space="preserve">
    <value>Vendor Info</value>
  </data>
  <data name="ResVendorInfo1" xml:space="preserve">
    <value>Vendor Info</value>
  </data>
  <data name="ResVendorLangaugeList" xml:space="preserve">
    <value>Vendor Language List</value>
  </data>
  <data name="ResVendorList" xml:space="preserve">
    <value>Vendor List</value>
  </data>
  <data name="ResVendorList1" xml:space="preserve">
    <value>Vendor List</value>
  </data>
  <data name="ResVendorListOtherLanguage" xml:space="preserve">
    <value>Vendor List - Other Language</value>
  </data>
  <data name="ResVendorListOtherLanguage1" xml:space="preserve">
    <value>Vendor List - Other Language</value>
  </data>
  <data name="ResVendorLocaleNameList" xml:space="preserve">
    <value>Vendor Locale Name List</value>
  </data>
  <data name="ResVendorMFG" xml:space="preserve">
    <value>Vendor MFG Code</value>
  </data>
  <data name="ResVendorMFGCode" xml:space="preserve">
    <value>Vendor MFG Code</value>
  </data>
  <data name="ResVendorMFGCode1" xml:space="preserve">
    <value>Vendor MFG Code</value>
  </data>
  <data name="ResVendorName" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="ResVendorName1" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="ResVendorNameDoesNOtExist" xml:space="preserve">
    <value>Vendor Name Does Not Exist</value>
  </data>
  <data name="ResVendorPart#" xml:space="preserve">
    <value>Vendor Part #</value>
  </data>
  <data name="ResVendorPart#1" xml:space="preserve">
    <value>Vendor Part #</value>
  </data>
  <data name="ResVendorPartInfo" xml:space="preserve">
    <value>Vendor Part Info</value>
  </data>
  <data name="ResVendorPartInfo1" xml:space="preserve">
    <value>Vendor Part Info</value>
  </data>
  <data name="ResVendorPartInfoDetails" xml:space="preserve">
    <value>VendorPartInfo Details</value>
  </data>
  <data name="ResVendorPartList" xml:space="preserve">
    <value>Vendor Part List</value>
  </data>
  <data name="ResVendorPartList1" xml:space="preserve">
    <value>Vendor Part List</value>
  </data>
  <data name="ResVendorPartNumber" xml:space="preserve">
    <value>Vendor Part Number</value>
  </data>
  <data name="ResVendorPartPrefix" xml:space="preserve">
    <value>Vendor Part Prefix</value>
  </data>
  <data name="ResVendorPartPrefix1" xml:space="preserve">
    <value>Vendor Part Prefix</value>
  </data>
  <data name="ResVendorPrefix" xml:space="preserve">
    <value>Vendor Prefix</value>
  </data>
  <data name="ResVendorPrefix1" xml:space="preserve">
    <value>Vendor Prefix</value>
  </data>
  <data name="ResVENDOR_NAME" xml:space="preserve">
    <value>VENDOR_NAME</value>
  </data>
  <data name="ResVENDOR_NO" xml:space="preserve">
    <value>VENDOR_NO</value>
  </data>
  <data name="ResVerHistory" xml:space="preserve">
    <value>Ver. History</value>
  </data>
  <data name="ResVerHistory1" xml:space="preserve">
    <value>Ver. History</value>
  </data>
  <data name="ResVersion" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="ResVersion#" xml:space="preserve">
    <value>Version #</value>
  </data>
  <data name="ResVersion#1" xml:space="preserve">
    <value>Version #</value>
  </data>
  <data name="ResVersion1" xml:space="preserve">
    <value>Version 1</value>
  </data>
  <data name="ResVersion2" xml:space="preserve">
    <value>Version 2</value>
  </data>
  <data name="ResVersionHistoryList" xml:space="preserve">
    <value>Version History List</value>
  </data>
  <data name="ResVersionHistoryList1" xml:space="preserve">
    <value>Version History List</value>
  </data>
  <data name="ResVersionUpdate" xml:space="preserve">
    <value>Version Update</value>
  </data>
  <data name="ResVerticleSplitter" xml:space="preserve">
    <value>Vertical Splitter</value>
  </data>
  <data name="ResVerticleSplitter1" xml:space="preserve">
    <value>Vertical Splitter</value>
  </data>
  <data name="ResView" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="ResView1" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="ResViewBOMOnly" xml:space="preserve">
    <value>View only BOM</value>
  </data>
  <data name="ResViewConfigurationList" xml:space="preserve">
    <value>View Tool Bar Configuration</value>
  </data>
  <data name="ResViewConfigurationList1" xml:space="preserve">
    <value>View Tool Bar Configuration</value>
  </data>
  <data name="ResViewDrawing" xml:space="preserve">
    <value>View Drawing</value>
  </data>
  <data name="ResViewer" xml:space="preserve">
    <value>Viewer</value>
  </data>
  <data name="ResViewerActivity" xml:space="preserve">
    <value>Viewer Activity</value>
  </data>
  <data name="ResViewerActivity1" xml:space="preserve">
    <value>Viewer Activity</value>
  </data>
  <data name="ResViewerActivityDefinition" xml:space="preserve">
    <value>Viewer Activity Definition</value>
  </data>
  <data name="ResViewerActivityDefinition1" xml:space="preserve">
    <value>Viewer Activity Definition</value>
  </data>
  <data name="ResViewerActivityList" xml:space="preserve">
    <value>Viewer Activity List</value>
  </data>
  <data name="ResViewerActivityList1" xml:space="preserve">
    <value>Viewer Activity List</value>
  </data>
  <data name="ResViewErrorLogFile" xml:space="preserve">
    <value>View Error Log File</value>
  </data>
  <data name="ResViewErrorReport" xml:space="preserve">
    <value>View Error Report</value>
  </data>
  <data name="ResViewErrorReport1" xml:space="preserve">
    <value>View Error Report</value>
  </data>
  <data name="ResViewerTheme" xml:space="preserve">
    <value>Viewer Theme</value>
  </data>
  <data name="ResViewerTheme1" xml:space="preserve">
    <value>Viewer Theme</value>
  </data>
  <data name="ResViewerToolbarConfig" xml:space="preserve">
    <value>Viewer Toolbar Config.</value>
  </data>
  <data name="ResViewerToolbarConfig1" xml:space="preserve">
    <value>Viewer Toolbar Config.</value>
  </data>
  <data name="ResViewerToolbarConfigure-Edit" xml:space="preserve">
    <value>Viewer Toolbar Configure-Edit</value>
  </data>
  <data name="ResViewerToolbarConfigure-Edit1" xml:space="preserve">
    <value>Viewer Toolbar Configure-Edit</value>
  </data>
  <data name="ResViewerUsers" xml:space="preserve">
    <value>Viewer Users</value>
  </data>
  <data name="ResViewerUsers1" xml:space="preserve">
    <value>Viewer Users</value>
  </data>
  <data name="ResViewerview" xml:space="preserve">
    <value>Viewer view</value>
  </data>
  <data name="ResViewFile" xml:space="preserve">
    <value>View File</value>
  </data>
  <data name="ResViewFile1" xml:space="preserve">
    <value>View File</value>
  </data>
  <data name="ResViewImage" xml:space="preserve">
    <value>View Image</value>
  </data>
  <data name="ResViewImage1" xml:space="preserve">
    <value>View Image</value>
  </data>
  <data name="ResViewImages" xml:space="preserve">
    <value>View Images</value>
  </data>
  <data name="ResViewImages1" xml:space="preserve">
    <value>View Images</value>
  </data>
  <data name="ResViewOnlyDrawing" xml:space="preserve">
    <value>View Only Drawing</value>
  </data>
  <data name="ResViewOrDownLoad" xml:space="preserve">
    <value>View / Download</value>
  </data>
  <data name="ResViewPartImage" xml:space="preserve">
    <value>View Part Image</value>
  </data>
  <data name="ResViewPartImage1" xml:space="preserve">
    <value>View Part Image</value>
  </data>
  <data name="ResViewSectionImage" xml:space="preserve">
    <value>View Section Image</value>
  </data>
  <data name="ResViewSectionImage1" xml:space="preserve">
    <value>View Section Image</value>
  </data>
  <data name="ResViewToolbarConfiguration" xml:space="preserve">
    <value>View Toolbar Configuration</value>
  </data>
  <data name="ResViewToolbarConfiguration1" xml:space="preserve">
    <value>View Toolbar Configuration</value>
  </data>
  <data name="ResViewTree" xml:space="preserve">
    <value>View Tree</value>
  </data>
  <data name="ResVIN" xml:space="preserve">
    <value>VIN#</value>
  </data>
  <data name="ResVIN#" xml:space="preserve">
    <value>VIN #</value>
  </data>
  <data name="ResVIN#1" xml:space="preserve">
    <value>VIN #</value>
  </data>
  <data name="ResVIN#History" xml:space="preserve">
    <value>VIN # History</value>
  </data>
  <data name="ResVIN#History1" xml:space="preserve">
    <value>VIN # History</value>
  </data>
  <data name="ResVIN#Import" xml:space="preserve">
    <value>VIN # Import</value>
  </data>
  <data name="ResVIN#Import1" xml:space="preserve">
    <value>VIN # Import</value>
  </data>
  <data name="ResVIN#Search" xml:space="preserve">
    <value>VIN # Search</value>
  </data>
  <data name="ResVIN#Search1" xml:space="preserve">
    <value>VIN # Search</value>
  </data>
  <data name="ResVIN#SearchList" xml:space="preserve">
    <value>VIN # Search List</value>
  </data>
  <data name="ResVIN#SearchList1" xml:space="preserve">
    <value>VIN # Search List</value>
  </data>
  <data name="ResVINAssociation" xml:space="preserve">
    <value>VIN Association</value>
  </data>
  <data name="ResVINAssociation1" xml:space="preserve">
    <value>VIN Association</value>
  </data>
  <data name="ResVINDetails" xml:space="preserve">
    <value>VIN Details</value>
  </data>
  <data name="ResVINImport" xml:space="preserve">
    <value>VIN# Import</value>
  </data>
  <data name="ResVINLevel" xml:space="preserve">
    <value>VIN Level</value>
  </data>
  <data name="ResVINLevel1" xml:space="preserve">
    <value>VIN Level</value>
  </data>
  <data name="ResVINList" xml:space="preserve">
    <value>VIN List</value>
  </data>
  <data name="ResVINNumberDoesNOtExist" xml:space="preserve">
    <value>VIN Number Does Not Exist</value>
  </data>
  <data name="ResVinOrderDetails" xml:space="preserve">
    <value>VIN Order Details</value>
  </data>
  <data name="ResVINShort#" xml:space="preserve">
    <value>VIN Short #</value>
  </data>
  <data name="ResVINShortCode" xml:space="preserve">
    <value>VIN Short Code</value>
  </data>
  <data name="ResVinShortName" xml:space="preserve">
    <value>VIN Short Name</value>
  </data>
  <data name="ResWarrantyPoliciesandProceduresManual" xml:space="preserve">
    <value>Warranty Policies and Procedures Manual</value>
  </data>
  <data name="ReswasreleasedontheNovaBusExtranet" xml:space="preserve">
    <value>was released on the Nova Bus Extranet / Parts Assist:</value>
  </data>
  <data name="ResWearetakingthisactionas" xml:space="preserve">
    <value>We are taking this action as part of our ongoing efforts to keep you informed and maintain owner confidence in our product. We hope this action confirms our commitment to your satisfaction.</value>
  </data>
  <data name="ResWelcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="ResWhereUsed" xml:space="preserve">
    <value>Where Used</value>
  </data>
  <data name="ResWidth" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="ResWindowZoom" xml:space="preserve">
    <value>Show Zoom Window</value>
  </data>
  <data name="ResWindowZoom1" xml:space="preserve">
    <value>Window Zoom</value>
  </data>
  <data name="ResWithVersion" xml:space="preserve">
    <value>With Version</value>
  </data>
  <data name="ResWithVersion1" xml:space="preserve">
    <value>With Version</value>
  </data>
  <data name="ResXML" xml:space="preserve">
    <value>XML</value>
  </data>
  <data name="ResYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="ResYes1" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="ResYoursSelectedRecordIsLocked" xml:space="preserve">
    <value>Selected Record Is Locked</value>
  </data>
  <data name="ResYoursSelectedRecordIsUnLocked" xml:space="preserve">
    <value>Selected Record Is UnLocked</value>
  </data>
  <data name="ResYoursverytruly," xml:space="preserve">
    <value>Yours very truly,</value>
  </data>
  <data name="ResZipCode" xml:space="preserve">
    <value>Zip Code</value>
  </data>
  <data name="ResZoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="ResZoomIn1" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="ResZoomIn2" xml:space="preserve">
    <value>Zoom In </value>
  </data>
  <data name="ResZoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="ResZoomOut1" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="ResZoomOut2" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="Res_Ignore_error" xml:space="preserve">
    <value>Ignore Error</value>
  </data>
  <data name="REV" xml:space="preserve">
    <value>REV.</value>
  </data>
  <data name="RevisionandDate" xml:space="preserve">
    <value>Revision and Date</value>
  </data>
  <data name="ReWindowZoom" xml:space="preserve">
    <value>WindowZoom</value>
  </data>
  <data name="Road#" xml:space="preserve">
    <value>Road #</value>
  </data>
  <data name="roadnumberpresent" xml:space="preserve">
    <value>Given Road# is already available in vehicle details</value>
  </data>
  <data name="RoleAdd" xml:space="preserve">
    <value>Role - Add</value>
  </data>
  <data name="Section" xml:space="preserve">
    <value>Section</value>
  </data>
  <data name="SectionNo" xml:space="preserve">
    <value>Section #</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>Select Column</value>
  </data>
  <data name="SerailNumberModelFilter" xml:space="preserve">
    <value>Apply Vin# and Model Filter</value>
  </data>
  <data name="ServiceManualFileName" xml:space="preserve">
    <value>Service Manual PDF File Name</value>
  </data>
  <data name="ServiceSections" xml:space="preserve">
    <value>Service Sections</value>
  </data>
  <data name="StartWith" xml:space="preserve">
    <value>Start With</value>
  </data>
  <data name="StockNo" xml:space="preserve">
    <value>Stock #</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>Is Active?</value>
  </data>
  <data name="String2" xml:space="preserve">
    <value />
  </data>
  <data name="String3" xml:space="preserve">
    <value>Common Notes</value>
  </data>
  <data name="String4" xml:space="preserve">
    <value />
  </data>
  <data name="String5" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="SubDiv" xml:space="preserve">
    <value>Sub-Div.</value>
  </data>
  <data name="SubSectionMissingTopLevelMaster" xml:space="preserve">
    <value>Sub Section Missing - TopLevelMaster</value>
  </data>
  <data name="SysDivSDBOMID" xml:space="preserve">
    <value>SYS DIV SDBOM ID</value>
  </data>
  <data name="Syst" xml:space="preserve">
    <value>Syst.</value>
  </data>
  <data name="Template" xml:space="preserve">
    <value>Template</value>
  </data>
  <data name="TemplateCode" xml:space="preserve">
    <value>Template Code</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="TOC" xml:space="preserve">
    <value>TABLE OF CONTENTS</value>
  </data>
  <data name="TOCLbl" xml:space="preserve">
    <value>Table of Contents ?</value>
  </data>
  <data name="TreeBuildEnd" xml:space="preserve">
    <value>Tree Build Completed, Generating Log...</value>
  </data>
  <data name="TreeBuildStart" xml:space="preserve">
    <value>All Options Found , Tree Build Starting</value>
  </data>
  <data name="TreeBuildSuccess" xml:space="preserve">
    <value>Tree Build successfully Done !</value>
  </data>
  <data name="UnabletoMoveHotspot" xml:space="preserve">
    <value>Unable to Move Hotspot</value>
  </data>
  <data name="Unabletoperformaction" xml:space="preserve">
    <value>Unable to perform action</value>
  </data>
  <data name="UOM" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="UOMAdd" xml:space="preserve">
    <value>UOM - Add</value>
  </data>
  <data name="UOM_Name" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="UpdatedSuccessfully" xml:space="preserve">
    <value>Updated Successfully...!</value>
  </data>
  <data name="UserAdd" xml:space="preserve">
    <value>User - Add</value>
  </data>
  <data name="UserCode" xml:space="preserve">
    <value>User Code</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="VarnetandDatabaseCompareList" xml:space="preserve">
    <value>Varnet and Database Compare List</value>
  </data>
  <data name="VarnetAssemblyNumber" xml:space="preserve">
    <value>Varnet Assembly/Section #</value>
  </data>
  <data name="VarnetImportLogList" xml:space="preserve">
    <value>Varnet Import Log List</value>
  </data>
  <data name="VarnetMainSection" xml:space="preserve">
    <value>Varnet Main Section</value>
  </data>
  <data name="VarnetOptionCode" xml:space="preserve">
    <value>Varnet Option Code</value>
  </data>
  <data name="VarnetOrderNuber" xml:space="preserve">
    <value>Varnet Order #</value>
  </data>
  <data name="VarnetSubSection" xml:space="preserve">
    <value>Varnet Sub Section</value>
  </data>
  <data name="VarNetTextFile" xml:space="preserve">
    <value>Invalid Order Text File</value>
  </data>
  <data name="VehicleDeletedSuccessfully" xml:space="preserve">
    <value>Deleted Successfully..!</value>
  </data>
  <data name="VENDOR" xml:space="preserve">
    <value>VENDOR</value>
  </data>
  <data name="Vendor Name" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="Vendor Parma Code" xml:space="preserve">
    <value>Vendor Parma Code</value>
  </data>
  <data name="Vendor Part MFG Code" xml:space="preserve">
    <value>Vendor Part MFG Code</value>
  </data>
  <data name="Vendor Part Number" xml:space="preserve">
    <value>Vendor Part Number</value>
  </data>
  <data name="VENDORINDEX" xml:space="preserve">
    <value>VENDOR INDEX</value>
  </data>
  <data name="VENDORNo" xml:space="preserve">
    <value>#VENDOR</value>
  </data>
  <data name="VendorPartInfo_MFGCode" xml:space="preserve">
    <value>Vendor MFG Code</value>
  </data>
  <data name="VendorPartInfo_PartNo" xml:space="preserve">
    <value>Vendor Part #</value>
  </data>
  <data name="Vendor_Code" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="Vendor_Name" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>Version Number</value>
  </data>
  <data name="VIN" xml:space="preserve">
    <value>Vendor Index</value>
  </data>
  <data name="VIN#" xml:space="preserve">
    <value>VIN #</value>
  </data>
  <data name="vinalreadypresent" xml:space="preserve">
    <value>Given VIN # is already available in vehicle details</value>
  </data>
  <data name="warning" xml:space="preserve">
    <value>warning</value>
  </data>
  <data name="WIPNEWOrderReImport" xml:space="preserve">
    <value>Its a New Order - Re-Import, Please Confirm - Re-Importing Missing Option Codes</value>
  </data>
  <data name="WIPNOTPUBLISHEDReImport" xml:space="preserve">
    <value>Order Already Exists but not Published - Re-Import , Please Confirm - Re-Importing Missing Option Codes</value>
  </data>
  <data name="WIPPUBLISHEDReImport" xml:space="preserve">
    <value>Order Already Exists and Published- Version No. - Re-Import , Please Confirm - Re-Importing Missing Option Codes</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>You have been Logged out successfully</value>
  </data>
  <data name="Yourrecordsaresafe" xml:space="preserve">
    <value>Your records are safe</value>
  </data>
  <data name="YouwanttoLogout" xml:space="preserve">
    <value>You want to Logout</value>
  </data>
  <data name="Youwillnotbeabletorecoverthisdata!" xml:space="preserve">
    <value>You will not be able to recover this data!</value>
  </data>
  <data name="NotinTree" xml:space="preserve">
    <value>Not in Tree</value>
  </data>
  <data name="ReplacedBy" xml:space="preserve">
    <value>REPLACED BY:</value>
  </data>
  <data name="LogoHeight" xml:space="preserve">
    <value>Logo Width &amp; Height</value>
  </data>
  <data name="ResDeleteAllHighlighter" xml:space="preserve">
    <value>Delete all highlighter</value>
  </data>
  <data name="ResBacktologin" xml:space="preserve">
    <value>Back to login</value>
  </data>
  <data name="ResPleaseEnterRegisteredEmailID" xml:space="preserve">
    <value>Please Enter Registered Email ID</value>
  </data>
  <data name="ResRetrievepassword" xml:space="preserve">
    <value>Retrieve Password</value>
  </data>
  <data name="ResSendMe" xml:space="preserve">
    <value>Send Password</value>
  </data>
  <data name="ResSerial#AssociationDetails" xml:space="preserve">
    <value>Serial # Association Details</value>
  </data>
  <data name="ResErrorLogReportDetails" xml:space="preserve">
    <value>Error Log Report Details</value>
  </data>
  <data name="ResCustomerPartInfoDetails" xml:space="preserve">
    <value>Customer Part Info Details</value>
  </data>
  <data name="ResViewMode" xml:space="preserve">
    <value>View Mode</value>
  </data>
  <data name="String7" xml:space="preserve">
    <value />
  </data>
  <data name="ResViewerDashboard" xml:space="preserve">
    <value>Viewer Dashboard</value>
  </data>
  <data name="Alternate Part Number" xml:space="preserve">
    <value>Alternate Part Number</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="Common Notes" xml:space="preserve">
    <value>Common Notes</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>CustomerName</value>
  </data>
  <data name="Is Active" xml:space="preserve">
    <value>Is Active</value>
  </data>
  <data name="Is Main Section" xml:space="preserve">
    <value>Is Main Section</value>
  </data>
  <data name="Is Part Section" xml:space="preserve">
    <value>Is Part Section</value>
  </data>
  <data name="Is Purchasable" xml:space="preserve">
    <value>Is Purchasable</value>
  </data>
  <data name="Is Section" xml:space="preserve">
    <value>Is Section</value>
  </data>
  <data name="Is Service Section" xml:space="preserve">
    <value>Is Service Section</value>
  </data>
  <data name="Is Visible" xml:space="preserve">
    <value>Is Visible</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>IsActive</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="MFGYear" xml:space="preserve">
    <value>MFGYear</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="RoadNo" xml:space="preserve">
    <value>RoadNo</value>
  </data>
  <data name="Sequence" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="Show Vendor Part" xml:space="preserve">
    <value>Show Vendor Part</value>
  </data>
  <data name="Specification" xml:space="preserve">
    <value>Specification</value>
  </data>
  <data name="VINShortNo" xml:space="preserve">
    <value>VINShortNo</value>
  </data>
  <data name="Customer Name" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="Customer Part MFG Code" xml:space="preserve">
    <value>Customer Part MFG Code</value>
  </data>
  <data name="Customer Part Number" xml:space="preserve">
    <value>Customer Part Number</value>
  </data>
  <data name="EnglishOptionDescription" xml:space="preserve">
    <value>EnglishOptionDescription</value>
  </data>
  <data name="FrenchOptionDescription" xml:space="preserve">
    <value>FrenchOptionDescription</value>
  </data>
  <data name="FromMFGCode" xml:space="preserve">
    <value>FromMFGCode</value>
  </data>
  <data name="FromPartNumber" xml:space="preserve">
    <value>FromPartNumber</value>
  </data>
  <data name="FromQty" xml:space="preserve">
    <value>FromQty</value>
  </data>
  <data name="OptionCode" xml:space="preserve">
    <value>OptionCode</value>
  </data>
  <data name="OrderNo" xml:space="preserve">
    <value>OrderNo</value>
  </data>
  <data name="OrderQuantity" xml:space="preserve">
    <value>OrderQuantity</value>
  </data>
  <data name="Part Or Assembly No" xml:space="preserve">
    <value>Part Or Assembly No</value>
  </data>
  <data name="Part Status" xml:space="preserve">
    <value>Part Status</value>
  </data>
  <data name="ToMFGCode" xml:space="preserve">
    <value>ToMFGCode</value>
  </data>
  <data name="ToPartNumber" xml:space="preserve">
    <value>ToPartNumber</value>
  </data>
  <data name="ToQty" xml:space="preserve">
    <value>ToQty</value>
  </data>
  <data name="ResAbbreviationFrenchDescription" xml:space="preserve">
    <value>French  Abbreviation Description</value>
  </data>
  <data name="ResIsConfigVisiable" xml:space="preserve">
    <value>Is Visible</value>
  </data>
  <data name="ResFrenchDescription" xml:space="preserve">
    <value>Description in French</value>
  </data>
  <data name="ResDrawingBOMDetails" xml:space="preserve">
    <value>Drawing BOM Details</value>
  </data>
  <data name="ResBOMDetails" xml:space="preserve">
    <value>BOM Details</value>
  </data>
  <data name="ResRoadNumberDetails" xml:space="preserve">
    <value>Road Number Details</value>
  </data>
  <data name="ResVINNumberDetails" xml:space="preserve">
    <value>VIN Number Details</value>
  </data>
  <data name="OpeningsLog" xml:space="preserve">
    <value>Openings Log</value>
  </data>
  <data name="Openedby" xml:space="preserve">
    <value>Opened by</value>
  </data>
  <data name="NotOpenedby" xml:space="preserve">
    <value>Not Opened by</value>
  </data>
  <data name="ViewedDateTime" xml:space="preserve">
    <value>Viewed Date and Time</value>
  </data>
  <data name="ResFrenchRemarks" xml:space="preserve">
    <value>French Remarks</value>
  </data>
  <data name="ResDetail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="ResEmployeeName" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="ResFromDate" xml:space="preserve">
    <value>From Date</value>
  </data>
  <data name="ResGenerateReport" xml:space="preserve">
    <value>Generate Report</value>
  </data>
  <data name="ResMenuName" xml:space="preserve">
    <value>Menu Name</value>
  </data>
  <data name="ResMonth" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="ResMsgEnterEmployeeName" xml:space="preserve">
    <value>Enter Employee Name</value>
  </data>
  <data name="ResMsgFromDate" xml:space="preserve">
    <value>From Date</value>
  </data>
  <data name="ResMsgToDate" xml:space="preserve">
    <value>To Date</value>
  </data>
  <data name="ResSelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="ResSummary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="ResTimesheetReport" xml:space="preserve">
    <value>Time Sheet Report</value>
  </data>
  <data name="ResToDate" xml:space="preserve">
    <value>To Date</value>
  </data>
  <data name="ResTotalHours" xml:space="preserve">
    <value>Total Hours</value>
  </data>
  <data name="ResYear" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="ResEmployeeDetails" xml:space="preserve">
    <value>Employee Details</value>
  </data>
  <data name="ResPleaseSelectEmployee" xml:space="preserve">
    <value>Please Select Employee</value>
  </data>
  <data name="MsgSelectAssemblytPublish" xml:space="preserve">
    <value>Please Select Assembly to publish</value>
  </data>
  <data name="Part_Is_Published" xml:space="preserve">
    <value>Is Published?</value>
  </data>
  <data name="Function Group Code" xml:space="preserve">
    <value>Function Group Code</value>
  </data>
  <data name="MsgCurrentName" xml:space="preserve">
    <value>No change in Part # found</value>
  </data>
  <data name="MsgNumberAlreadyFound" xml:space="preserve">
    <value>Part Number Already Exists</value>
  </data>
  <data name="MsgOrderNumber" xml:space="preserve">
    <value>OrderNumber</value>
  </data>
  <data name="MsgVIN" xml:space="preserve">
    <value>VIN</value>
  </data>
  <data name="ResManageMFGCode" xml:space="preserve">
    <value>MFGCode</value>
  </data>
  <data name="ResManagePartNumber" xml:space="preserve">
    <value>PartNumber</value>
  </data>
  <data name="ResDearSirMadam" xml:space="preserve">
    <value>Dear Sir/Madam</value>
  </data>
  <data name="Thank You" xml:space="preserve">
    <value>Thank You</value>
  </data>
  <data name="ResRequestforCustomerAssociation" xml:space="preserve">
    <value>Request For Quote Customer Association</value>
  </data>
  <data name="ResRoadNumber" xml:space="preserve">
    <value>RoadNumber</value>
  </data>
  <data name="ResVINNumber" xml:space="preserve">
    <value>VINNumber</value>
  </data>
  <data name="French_Part_Assembly_Description" xml:space="preserve">
    <value>Description in French</value>
  </data>
  <data name="ResDocumentCategory" xml:space="preserve">
    <value>Document Category</value>
  </data>
  <data name="ResOrdernum" xml:space="preserve">
    <value>Order #</value>
  </data>
  <data name="ShowSetPartinBOM" xml:space="preserve">
    <value>Set Parts in BOM?</value>
  </data>
  <data name="ResAdmin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="ResAnyQueries" xml:space="preserve">
    <value>For any queries or clarifications write in to </value>
  </data>
  <data name="ResAutogenerationEmail" xml:space="preserve">
    <value>This is autogenerated mail. Please do not reply back to this e-mail.</value>
  </data>
  <data name="ResFor" xml:space="preserve">
    <value>For</value>
  </data>
  <data name="ResHello" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="ResIgnoreAllError" xml:space="preserve">
    <value>Ignore All Error</value>
  </data>
  <data name="ResThanksRegards" xml:space="preserve">
    <value>Thanks and Regards</value>
  </data>
  <data name="ResVendorNo" xml:space="preserve">
    <value>Vendor No.</value>
  </data>
  <data name="ResPartsInShoppingCart" xml:space="preserve">
    <value>Parts In Shopping Cart</value>
  </data>
  <data name="ResShoppingDetailsXML" xml:space="preserve">
    <value>ShoppingDetails</value>
  </data>
  <data name="ResAsslyDescriptionXML" xml:space="preserve">
    <value>Assembly_Description</value>
  </data>
  <data name="ResCustomerPart#XML" xml:space="preserve">
    <value>Customer_Part_No</value>
  </data>
  <data name="ResFunctionGroupXML" xml:space="preserve">
    <value>Function_Group</value>
  </data>
  <data name="ResMFGCodeXML" xml:space="preserve">
    <value>MFG_Code</value>
  </data>
  <data name="ResOrderQtyXML" xml:space="preserve">
    <value>Order_Qty</value>
  </data>
  <data name="ResPart#XML" xml:space="preserve">
    <value>Assembly_Part_No</value>
  </data>
  <data name="ResPartDetailsXML" xml:space="preserve">
    <value>Part_Details</value>
  </data>
  <data name="ResShoppingCartXML" xml:space="preserve">
    <value>Shopping_Cart</value>
  </data>
  <data name="ResSuggestedQtyXML" xml:space="preserve">
    <value>Suggested_Qty</value>
  </data>
  <data name="ResMsgEnterDocumentDescription" xml:space="preserve">
    <value>Enter Document Title</value>
  </data>
  <data name="Is_SubSection" xml:space="preserve">
    <value>Is Section?</value>
  </data>
  <data name="ResSelectUserType" xml:space="preserve">
    <value>Please Select User Type</value>
  </data>
  <data name="ResAreYouSureYouWantToClose" xml:space="preserve">
    <value>Are You Sure You Want To Close?</value>
  </data>
  <data name="ResRolesAssociatedToUser" xml:space="preserve">
    <value>Selected role(s) is(are) already associated to user</value>
  </data>
  <data name="ResShowVendorDetails?" xml:space="preserve">
    <value>Show Vendor Details?</value>
  </data>
  <data name="ResCreatedBy" xml:space="preserve">
    <value>Created By</value>
  </data>
  <data name="ResCreatedDate" xml:space="preserve">
    <value>Created Date</value>
  </data>
  <data name="ResModifiedBy" xml:space="preserve">
    <value>Modified By</value>
  </data>
  <data name="ResPleaseEntervalidOptions" xml:space="preserve">
    <value>Please enter valid options</value>
  </data>
  <data name="ResCustomerPartInfoReport" xml:space="preserve">
    <value>Customer Part Info Report</value>
  </data>
  <data name="ResCustomerPartMFGCode" xml:space="preserve">
    <value>Customer Part MFG Code</value>
  </data>
  <data name="ResCustomerPartNumber" xml:space="preserve">
    <value>Customer Part Number</value>
  </data>
  <data name="ResCustomerPartInfoReports" xml:space="preserve">
    <value>Customer Part Info Report</value>
  </data>
  <data name="ResIsEmailNotification" xml:space="preserve">
    <value>Is Email Notification?</value>
  </data>
  <data name="ServiceLinkAdd" xml:space="preserve">
    <value>Service link Add</value>
  </data>
  <data name="ServiceLinkRemove" xml:space="preserve">
    <value>Service link Remove</value>
  </data>
  <data name="MsgNoInformationEnterValidVinnumber" xml:space="preserve">
    <value>No Information found to Generate Vinshortnum Enter valid Vin number</value>
  </data>
  <data name="ResCustomerPartInfoTemplate" xml:space="preserve">
    <value>CustomerPartInfoTemplate</value>
  </data>
  <data name="PleaseWaitTreeBuildforMissingSectionSSMAI" xml:space="preserve">
    <value>Please Wait Tree Build for Missing Section,Sub-Section &amp; Main Assembly In-Progress</value>
  </data>
  <data name="PleaseWaitTreeCompareandWorkLogInProgress" xml:space="preserve">
    <value>Please wait, Tree Compare and Work-Log In-Progress</value>
  </data>
  <data name="MsgDoYouWanttoremoveAllAssociatedOrders" xml:space="preserve">
    <value>Do you want remove all associated order?</value>
  </data>
  <data name="MsgPleaseAssociateOrders" xml:space="preserve">
    <value>Please Associate Orders</value>
  </data>
  <data name="ResActualFont" xml:space="preserve">
    <value>Actual Font</value>
  </data>
  <data name="PWMOFAP" xml:space="preserve">
    <value>Please wait, Order files are processing....</value>
  </data>
  <data name="ResVendorPartInfoReport" xml:space="preserve">
    <value>Vendor Part Info Report</value>
  </data>
  <data name="ResVendorPartInfoReports" xml:space="preserve">
    <value>Vendor Part Info Reports</value>
  </data>
  <data name="ErrorDetails" xml:space="preserve">
    <value>Error Details</value>
  </data>
  <data name="ONFCD" xml:space="preserve">
    <value>Cannot delete this Order,Dependency found/ Order not found!</value>
  </data>
  <data name="ResYesDelVarReCrtTree" xml:space="preserve">
    <value>Yes, Delete and Re-Create Tree !</value>
  </data>
  <data name="ResAllDocuments" xml:space="preserve">
    <value>All Documents</value>
  </data>
  <data name="VarnetOrderImportError" xml:space="preserve">
    <value>File &amp; Data in Table not found for Import!</value>
  </data>
  <data name="TreeBuildFailed" xml:space="preserve">
    <value>VarnetFile not found. Tree build failed!</value>
  </data>
  <data name="ResAssociationofnovaorderswillberemoveddoyouwanttocontinue" xml:space="preserve">
    <value>Association of NOVA orders will be removed. Do you want to continue?</value>
  </data>
  <data name="ResOrderorVINLevel" xml:space="preserve">
    <value>Order | VIN Level</value>
  </data>
  <data name="ActionNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="ResAllVINs" xml:space="preserve">
    <value>All VINs</value>
  </data>
  <data name="ResOrdersVINSelection" xml:space="preserve">
    <value>Order/VIN Selection</value>
  </data>
  <data name="ResUploadDocumentDetails" xml:space="preserve">
    <value>Upload Document Details</value>
  </data>
  <data name="MsgPleaseSelectOrderVINtoChange" xml:space="preserve">
    <value>Please select Order/VIN to change new owner</value>
  </data>
  <data name="ResCurrentNumberOfVehicles" xml:space="preserve">
    <value>Count of Vehicles</value>
  </data>
  <data name="ResNewNumberOfVehicles" xml:space="preserve">
    <value>New Owner No. of Vehicles</value>
  </data>
  <data name="ResCurrentVINS" xml:space="preserve">
    <value>Current Owner VINs</value>
  </data>
  <data name="ResNewVINS" xml:space="preserve">
    <value>New Owner VINs</value>
  </data>
  <data name="MsgNewOwnershouldnotbesamehascurrentOwner" xml:space="preserve">
    <value>New owner should not be same as current owner</value>
  </data>
  <data name="ResAddOrderDetails" xml:space="preserve">
    <value>Add Order Details</value>
  </data>
  <data name="ResDeleteOrderDetails" xml:space="preserve">
    <value>Delete Order Details</value>
  </data>
  <data name="LastUpdatedDate" xml:space="preserve">
    <value>Last Purged Date</value>
  </data>
  <data name="ResCataloguePartDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResParentDescriptions" xml:space="preserve">
    <value>Parent Descriptions</value>
  </data>
  <data name="ResParentMFGCode" xml:space="preserve">
    <value>Parent MFG Code</value>
  </data>
  <data name="ResParentPart#" xml:space="preserve">
    <value>Parent Assembly #</value>
  </data>
  <data name="ResOrderNP#" xml:space="preserve">
    <value>Order/TopLevel #</value>
  </data>
  <data name="Is_Purchasable" xml:space="preserve">
    <value>Is Purchasable?</value>
  </data>
  <data name="AreYouSureYouWantToDeleteDoc" xml:space="preserve">
    <value>Are you sure you want to Delete?</value>
  </data>
  <data name="ResChooseFilewhichBeginswithDocumenttypeCode" xml:space="preserve">
    <value>Choose File which Begins with Document type Code</value>
  </data>
  <data name="French_Description" xml:space="preserve">
    <value>Description in French</value>
  </data>
  <data name="ResManagePartsDetails" xml:space="preserve">
    <value>Manage Part Details</value>
  </data>
  <data name="OfflineDataDownLoad" xml:space="preserve">
    <value>Offline Data Download</value>
  </data>
  <data name="OfflineEXEDownload" xml:space="preserve">
    <value>Offline EXE Download</value>
  </data>
  <data name="ITPDocuments" xml:space="preserve">
    <value>ITP Documents</value>
  </data>
  <data name="IsReplacedBy" xml:space="preserve">
    <value>Is Replaced By ?</value>
  </data>
  <data name="ResSectionDesc" xml:space="preserve">
    <value>Section Desc</value>
  </data>
  <data name="ResSectionNumber" xml:space="preserve">
    <value>Section Number</value>
  </data>
  <data name="ResServiceSubSection" xml:space="preserve">
    <value>Service Sub-Section</value>
  </data>
  <data name="ResSectionFileName" xml:space="preserve">
    <value>Section File Name</value>
  </data>
  <data name="ResRecordCount" xml:space="preserve">
    <value>Count of Orders - </value>
  </data>
  <data name="MsgOrderAssociationNotFound" xml:space="preserve">
    <value>Order Association is not available for this option</value>
  </data>
  <data name="ResAssociatedOrderDetails" xml:space="preserve">
    <value>Associated Order Details</value>
  </data>
  <data name="ResAssociatedOrders" xml:space="preserve">
    <value>Associated Orders</value>
  </data>
  <data name="Show_Set_Mem_In_Part_List" xml:space="preserve">
    <value>Set Parts in BOM?</value>
  </data>
  <data name="ResSectionDescriptionchangeswillapplytoallApplicableOrders" xml:space="preserve">
    <value>Section Description changes will apply to all Applicable Orders</value>
  </data>
  <data name="NoChangeinCartNameNorDate" xml:space="preserve">
    <value>No change in cart name or date</value>
  </data>
  <data name="ResReportedByEmail" xml:space="preserve">
    <value>Reported By Email</value>
  </data>
  <data name="ResDefaultSearchForNova" xml:space="preserve">
    <value>Search for NOVA</value>
  </data>
  <data name="ResDefaultSearchForPrevost" xml:space="preserve">
    <value>Search for PREVOST</value>
  </data>
  <data name="ResViewerDisplay" xml:space="preserve">
    <value>Viewer Display</value>
  </data>
  <data name="First" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="Last" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="MsgPleaseSelectCheckbox" xml:space="preserve">
    <value>Please Select Checkbox</value>
  </data>
  <data name="MsgSendnotificationUpdatedSuccessfull" xml:space="preserve">
    <value>Updated Successfully</value>
  </data>
  <data name="ResDocumentNotification" xml:space="preserve">
    <value>Document Notification</value>
  </data>
  <data name="ResDocumentTypeName" xml:space="preserve">
    <value>DocumentType Name</value>
  </data>
  <data name="ResIsSendNotification" xml:space="preserve">
    <value>Send Notification?</value>
  </data>
  <data name="ResFontFontName" xml:space="preserve">
    <value>FontName</value>
  </data>
  <data name="ResImportFromSN" xml:space="preserve">
    <value>FromSN</value>
  </data>
  <data name="ResImportMaskingCode" xml:space="preserve">
    <value>MaskingCode</value>
  </data>
  <data name="ResImportUpToSN" xml:space="preserve">
    <value>UpToSN</value>
  </data>
  <data name="ResShareAnnotation" xml:space="preserve">
    <value>Share Annotation</value>
  </data>
  <data name="RecentAssemblyPart" xml:space="preserve">
    <value>Recent Assembly / Part</value>
  </data>
  <data name="ResViewerDisplayNOVA" xml:space="preserve">
    <value>Viewer Display Nova</value>
  </data>
  <data name="ResViewerDisplayPREVOST" xml:space="preserve">
    <value>Viewer Display Prevost</value>
  </data>
  <data name="HideItemNumber" xml:space="preserve">
    <value>HideItemNumber</value>
  </data>
  <data name="NotinAssembly" xml:space="preserve">
    <value>NotinAssembly</value>
  </data>
  <data name="ResIsNotinTree" xml:space="preserve">
    <value>NotinTree</value>
  </data>
  <data name="LocalNote" xml:space="preserve">
    <value>LocalNote</value>
  </data>
  <data name="ResDocumentTypeNameUser" xml:space="preserve">
    <value>Document Type Name</value>
  </data>
  <data name="ResDivAssembliesAssociatedDrawing" xml:space="preserve">
    <value>Assemblies Associated Drawing</value>
  </data>
  <data name="ResAssembliesAssociatedDrawingDetails" xml:space="preserve">
    <value>Assemblies Associated to Drawing Details</value>
  </data>
  <data name="ResAssembliesAssociatedtoDrawingReport" xml:space="preserve">
    <value>Assemblies Associated To Drawing Report</value>
  </data>
  <data name="ResEn-Description" xml:space="preserve">
    <value>En-Description</value>
  </data>
  <data name="ResFn-Description" xml:space="preserve">
    <value>Fn-Description</value>
  </data>
  <data name="MsgNoImageFound" xml:space="preserve">
    <value>No Image Found</value>
  </data>
  <data name="ResImageName" xml:space="preserve">
    <value>Image Name</value>
  </data>
  <data name="ResMsgEnterImageName" xml:space="preserve">
    <value>Enter Image Name</value>
  </data>
  <data name="ResAssembliesAssociatedDrawingList" xml:space="preserve">
    <value>Assemblies Associated to Drawing List</value>
  </data>
  <data name="ResManypartselectedManagepartwith" xml:space="preserve">
    <value>Any Part Selected in Manage Part with this masking action will not be able to purchase in viewer</value>
  </data>
  <data name="ResEnglishCretedBy" xml:space="preserve">
    <value>EN - Created By</value>
  </data>
  <data name="ResEnglishCretedDate" xml:space="preserve">
    <value>EN - Created Date</value>
  </data>
  <data name="ResENRevisedBy" xml:space="preserve">
    <value>EN - Revised By</value>
  </data>
  <data name="ResENRevisionDate" xml:space="preserve">
    <value>EN - Revision Date</value>
  </data>
  <data name="ResFNCretedBy" xml:space="preserve">
    <value>FN - Created By</value>
  </data>
  <data name="ResFNCretedDate" xml:space="preserve">
    <value>FN - Created Date</value>
  </data>
  <data name="ResFNRevisedBy" xml:space="preserve">
    <value>FN - Revised By</value>
  </data>
  <data name="ResManageParts_ImportLog" xml:space="preserve">
    <value>ManageParts_ImportLog</value>
  </data>
  <data name="ResMisMatchRecords" xml:space="preserve">
    <value>MisMatch Records</value>
  </data>
  <data name="ResFNRevisionDate" xml:space="preserve">
    <value>FN - Revision Date</value>
  </data>
  <data name="ResFavourite" xml:space="preserve">
    <value>View Favorites</value>
  </data>
  <data name="ResFavourites" xml:space="preserve">
    <value>View favorites</value>
  </data>
  <data name="ResAllVINNumberInfoReport" xml:space="preserve">
    <value>All VIN Number Info Report</value>
  </data>
  <data name="ResPartIndexReport" xml:space="preserve">
    <value>Part Index Report</value>
  </data>
  <data name="ResMsgEnterOrder#/TopLevel" xml:space="preserve">
    <value>Enter Order # / Top Level</value>
  </data>
  <data name="ResOrder#/TopLevel" xml:space="preserve">
    <value>Order # / Top Level</value>
  </data>
  <data name="ResSetSerail#Range" xml:space="preserve">
    <value>Set Serial # Range</value>
  </data>
  <data name="ResPartIndex" xml:space="preserve">
    <value>Part Index</value>
  </data>
  <data name="ResPartIndexDetails" xml:space="preserve">
    <value>Part Index Details</value>
  </data>
  <data name="MsgInvalidAssembly#" xml:space="preserve">
    <value>Invalid Assembly #</value>
  </data>
  <data name="MsgInvalidImageName" xml:space="preserve">
    <value>Invalid Image Name</value>
  </data>
  <data name="ResSuperSessionReport" xml:space="preserve">
    <value>Supersession Details Report</value>
  </data>
  <data name="FilterApplied" xml:space="preserve">
    <value>Filter Applied</value>
  </data>
  <data name="OfflineDataDatetime" xml:space="preserve">
    <value>Offline Data File Latest Updated Date</value>
  </data>
  <data name="MsgRecordsHaveBeenShared" xml:space="preserve">
    <value>Shared Sucessfully</value>
  </data>
  <data name="ResSuperSessionReportDetails" xml:space="preserve">
    <value>Supersession Report Details</value>
  </data>
  <data name="CannotImportAsThereAreUnpublishedBaseAssemblies" xml:space="preserve">
    <value>Selected file cannot be imported,  since we have unpublished base assemblies listed in excel</value>
  </data>
  <data name="Pleaseenterfilterdetails" xml:space="preserve">
    <value>Please enter filter details</value>
  </data>
  <data name="ResENDesc" xml:space="preserve">
    <value>EN Desc</value>
  </data>
  <data name="ResENRev" xml:space="preserve">
    <value>EN Rev</value>
  </data>
  <data name="ResFRDesc" xml:space="preserve">
    <value>FR Desc</value>
  </data>
  <data name="ResFRRev" xml:space="preserve">
    <value>FR Rev</value>
  </data>
  <data name="ResLastImportDate" xml:space="preserve">
    <value>Last Purged Date</value>
  </data>
  <data name="ResPartsSectionRevisionReport" xml:space="preserve">
    <value>Parts Section Revision Report</value>
  </data>
  <data name="ResServiceSectionRevisionReport" xml:space="preserve">
    <value>Service Section Revision Report</value>
  </data>
  <data name="ResEnglishRevisionDate" xml:space="preserve">
    <value>English Revision Date</value>
  </data>
  <data name="ResENNewPDFName" xml:space="preserve">
    <value>EN-New PDF Name</value>
  </data>
  <data name="ResFNNewPDFName" xml:space="preserve">
    <value>FN-New PDF Name</value>
  </data>
  <data name="ResFrenchRevisionDate" xml:space="preserve">
    <value>French Revision Date</value>
  </data>
  <data name="ResIgnore" xml:space="preserve">
    <value>Ignore</value>
  </data>
  <data name="ResRevise" xml:space="preserve">
    <value>Revise</value>
  </data>
  <data name="ResSectionRevisionDetails" xml:space="preserve">
    <value>Section Revision Details</value>
  </data>
  <data name="ResNew" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="ResRevised" xml:space="preserve">
    <value>Revised</value>
  </data>
  <data name="ResHotSpotMissing?" xml:space="preserve">
    <value>Hotspot Missing?</value>
  </data>
  <data name="ResItem#Missing?" xml:space="preserve">
    <value>Item# Missing?</value>
  </data>
  <data name="ResPublishDeviationErrorReportDetails" xml:space="preserve">
    <value>Publish Deviation Error Report Details</value>
  </data>
  <data name="ResTranslationMissing?" xml:space="preserve">
    <value>Translation Missing?</value>
  </data>
  <data name="ResBOMMFGCode" xml:space="preserve">
    <value>BOM MFG Code</value>
  </data>
  <data name="ResBOMPart#" xml:space="preserve">
    <value>BOM Part#</value>
  </data>
  <data name="ResFromSNNum" xml:space="preserve">
    <value>From SN</value>
  </data>
  <data name="ResHasRecommendedPart?" xml:space="preserve">
    <value>Has Recommended Part?</value>
  </data>
  <data name="ResHasSetMember?" xml:space="preserve">
    <value>Has SetMember?</value>
  </data>
  <data name="ResIgnoredDate" xml:space="preserve">
    <value>Ignored Date</value>
  </data>
  <data name="ResItemNum#" xml:space="preserve">
    <value>Item #</value>
  </data>
  <data name="ResParentPart#Description" xml:space="preserve">
    <value>Parent Part# Description</value>
  </data>
  <data name="ResParentParts#" xml:space="preserve">
    <value>Parent Part#</value>
  </data>
  <data name="ResQty." xml:space="preserve">
    <value>Qty.</value>
  </data>
  <data name="ResQtyMissing" xml:space="preserve">
    <value>Qty Missing</value>
  </data>
  <data name="ResSeq." xml:space="preserve">
    <value>Seq.</value>
  </data>
  <data name="ResSpec." xml:space="preserve">
    <value>Spec.</value>
  </data>
  <data name="ResToSNNum" xml:space="preserve">
    <value>To SN</value>
  </data>
  <data name="cannotdeletetherecords" xml:space="preserve">
    <value>cannot delete the records</value>
  </data>
  <data name="Dependencyfound" xml:space="preserve">
    <value>Dependency found</value>
  </data>
  <data name="MsgIgnoredSuccessfully" xml:space="preserve">
    <value>Ignored Successfully</value>
  </data>
  <data name="MsgRevisedSuccessfully" xml:space="preserve">
    <value>Revised Successfully</value>
  </data>
  <data name="ResPleasefillmandatoryfield" xml:space="preserve">
    <value>Please fill mandatory field</value>
  </data>
  <data name="ResPleaseSelecttheCheckbox" xml:space="preserve">
    <value>Please Select the Checkbox</value>
  </data>
  <data name="ResDependency" xml:space="preserve">
    <value>Dependency</value>
  </data>
  <data name="ResDependencyList" xml:space="preserve">
    <value>Dependency List</value>
  </data>
  <data name="ResAdditionalPartDetails" xml:space="preserve">
    <value>Additional Part Details</value>
  </data>
  <data name="ResFilterforwithoutPriceParts" xml:space="preserve">
    <value>Filter for without Price Parts</value>
  </data>
  <data name="ResFilterforwithoutVendorParts" xml:space="preserve">
    <value>Filter for without Vendor Parts</value>
  </data>
  <data name="ResFilterforwithPriceParts" xml:space="preserve">
    <value>Filter for with Price Parts</value>
  </data>
  <data name="ResFilterforwithVendorParts" xml:space="preserve">
    <value>Filter for with Vendor Parts</value>
  </data>
  <data name="ResHideColumn" xml:space="preserve">
    <value>Hide Column</value>
  </data>
  <data name="ResLeadTime" xml:space="preserve">
    <value>Lead Time</value>
  </data>
  <data name="ResPage#" xml:space="preserve">
    <value>Page #</value>
  </data>
  <data name="ResPrice" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="ResPriceDetails" xml:space="preserve">
    <value>Price Details</value>
  </data>
  <data name="ResRecommendedBuyList" xml:space="preserve">
    <value>Recommended Buy List</value>
  </data>
  <data name="ResShowNonPurchasableParts?" xml:space="preserve">
    <value>Show Non Purchasable Parts?</value>
  </data>
  <data name="ResShowRecommendedParts?" xml:space="preserve">
    <value>Show Recommended Parts?</value>
  </data>
  <data name="ResShowSetMembers?" xml:space="preserve">
    <value>Show Set Members?</value>
  </data>
  <data name="ResU/M" xml:space="preserve">
    <value>U/M</value>
  </data>
  <data name="ResAllwitandwithoutPriceParts" xml:space="preserve">
    <value>All ( with and without Price Parts)</value>
  </data>
  <data name="ResAllwitandwithoutVendorParts" xml:space="preserve">
    <value>All ( with and without Vendor Parts)</value>
  </data>
  <data name="ResFilterations" xml:space="preserve">
    <value>Filterations</value>
  </data>
  <data name="ResApply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ResBOMList" xml:space="preserve">
    <value>BOM List</value>
  </data>
  <data name="PartsSectionNotif" xml:space="preserve">
    <value>New/Modified Orders/Assemblies are available, please generate "Parts Section Revision Report" for more detail</value>
  </data>
  <data name="ResGoahead" xml:space="preserve">
    <value>Go ahead</value>
  </data>
  <data name="ResIsIgnore" xml:space="preserve">
    <value>Is Ignore?</value>
  </data>
  <data name="ResOtherDependency" xml:space="preserve">
    <value>Other Dependency</value>
  </data>
  <data name="ResSelfDependency" xml:space="preserve">
    <value>Self Dependency</value>
  </data>
  <data name="ResSelfOrOthers" xml:space="preserve">
    <value>Self or Others</value>
  </data>
  <data name="Reswillbereplacedwithselectedparts" xml:space="preserve">
    <value>will be replaced with selected parts</value>
  </data>
  <data name="ServiceSecMnlNoti" xml:space="preserve">
    <value>New/Modified Service Manuals PDF files are available, please generate "Service Section Revision Report" for more detail</value>
  </data>
  <data name="ResFilterationsselectioncriteria" xml:space="preserve">
    <value>Selection Criteria</value>
  </data>
  <data name="ResPriceDetailsSelection" xml:space="preserve">
    <value>Selection Criteria-Price Details</value>
  </data>
  <data name="ResPlsWaitFinishImpactedOrders" xml:space="preserve">
    <value>Please wait-finishing for all impacted orders-Top Levels</value>
  </data>
  <data name="ResIsDrawingmissing" xml:space="preserve">
    <value>Is Drawing missing?</value>
  </data>
  <data name="MsgAddtoNewCartaddtotheexitingCart" xml:space="preserve">
    <value>Create New/Load Exitsting Shopping  Cart</value>
  </data>
  <data name="ResIsAddToShoppinCart" xml:space="preserve">
    <value>IsAddToShoppingCart</value>
  </data>
  <data name="ResNYCT" xml:space="preserve">
    <value>NYCT</value>
  </data>
  <data name="Resource_Fr" xml:space="preserve">
    <value>Set Ship to Address</value>
  </data>
  <data name="ResSetShipToAddress" xml:space="preserve">
    <value>Shopping Cart List-Set Ship to Address</value>
  </data>
  <data name="ResSelectShipToAddress" xml:space="preserve">
    <value>Select Ship to Address</value>
  </data>
  <data name="ResSetShipAddress" xml:space="preserve">
    <value>Set Ship to Address</value>
  </data>
  <data name="MsgShipToAddressisSetForAllParts" xml:space="preserve">
    <value>Ship to Address is Set for all Parts</value>
  </data>
  <data name="MsgSelectShippingAddressForAllParts" xml:space="preserve">
    <value>Please Select Ship to Address for all parts</value>
  </data>
  <data name="MsgMFGRemovedSuccessfull" xml:space="preserve">
    <value>Removed Successfully</value>
  </data>
  <data name="MsgSaveCartandmove" xml:space="preserve">
    <value>Please Save Merged Parts for Carts</value>
  </data>
  <data name="MsgShipToAddressisSetForAllPartsAlready" xml:space="preserve">
    <value>Ship to Address is Set for all Parts Already</value>
  </data>
  <data name="ParentItemNo" xml:space="preserve">
    <value>Parent Item #</value>
  </data>
  <data name="ResNewSpecification" xml:space="preserve">
    <value>New Specification</value>
  </data>
  <data name="ResOldSpecification" xml:space="preserve">
    <value>Old Specification</value>
  </data>
  <data name="ResActionCancelled" xml:space="preserve">
    <value>Action Cancelled !</value>
  </data>
  <data name="ResFromSNPrnt" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="ItemNoprint" xml:space="preserve">
    <value>Item#</value>
  </data>
  <data name="QTYprint" xml:space="preserve">
    <value>QTY</value>
  </data>
  <data name="MsgPleaseEnternumberafter" xml:space="preserve">
    <value>Please enter only numbers after - </value>
  </data>
  <data name="PartsNotification" xml:space="preserve">
    <value>Parts Notification - </value>
  </data>
  <data name="ResUpdates" xml:space="preserve">
    <value> Updates</value>
  </data>
  <data name="ServiceManualsNotification" xml:space="preserve">
    <value>Service Manuals Notification -</value>
  </data>
  <data name="Forinquiresortocreateanaccount(US&amp;Canada),pleasecontacttheappropriateadministratorbelow" xml:space="preserve">
    <value>For inquires or to create an account (US &amp; Canada), please contact the appropriate administrator below</value>
  </data>
  <data name="ResAddorUpdate" xml:space="preserve">
    <value>Add/Update</value>
  </data>
  <data name="ResLocalNotesLanguageList" xml:space="preserve">
    <value>Local Notes language List</value>
  </data>
  <data name="ResOrderAccessControl" xml:space="preserve">
    <value>Order Access Control</value>
  </data>
  <data name="ResOrderUserAccess" xml:space="preserve">
    <value>Order - User Access</value>
  </data>
  <data name="ResSearchOrder" xml:space="preserve">
    <value>Search Order#</value>
  </data>
  <data name="ResSearchSection" xml:space="preserve">
    <value>Search Section#</value>
  </data>
  <data name="ResDocuments" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="ResSearchSubSection" xml:space="preserve">
    <value>Search SubSection#</value>
  </data>
  <data name="ResADDefectNotification" xml:space="preserve">
    <value>AD - Defect Notification</value>
  </data>
  <data name="ResAddOrSave" xml:space="preserve">
    <value>Add/Save</value>
  </data>
  <data name="ResEDElectricDiagrams" xml:space="preserve">
    <value>Electrical Diagrams</value>
  </data>
  <data name="ResITPManual" xml:space="preserve">
    <value>Inspection and Test Plan</value>
  </data>
  <data name="ResOPOperatorManual" xml:space="preserve">
    <value>Operator's Manual</value>
  </data>
  <data name="ResPDPneumaticdiagrams" xml:space="preserve">
    <value>Pneumatic diagrams</value>
  </data>
  <data name="ResPMPartsManual" xml:space="preserve">
    <value>PM - Parts Manual</value>
  </data>
  <data name="ResSRTStandardRepairTime" xml:space="preserve">
    <value>SRT - Standard Repair Time</value>
  </data>
  <data name="ResDocumentSelection" xml:space="preserve">
    <value>Document Selection</value>
  </data>
  <data name="ResPleaseSelectAtleastoneSubSectionunderthisSection" xml:space="preserve">
    <value>Please select atleast one subsection under this section </value>
  </data>
  <data name="ResOrderAccessDetails" xml:space="preserve">
    <value>Order Access Details</value>
  </data>
  <data name="ResPleaseSelectAtleastoneOrder" xml:space="preserve">
    <value>Please select an order</value>
  </data>
  <data name="ResPlsWaitOrderAssociationInProgress" xml:space="preserve">
    <value>Please wait order association in progress</value>
  </data>
  <data name="ResProgramsandRevisionList" xml:space="preserve">
    <value>Programs and Revision List</value>
  </data>
  <data name="ResWarantyandPolicymanual" xml:space="preserve">
    <value>Warranty Plocies and Procedures Manual</value>
  </data>
  <data name="ResNoChangesInOrderAssociation" xml:space="preserve">
    <value>No changes in order association</value>
  </data>
  <data name="AreYouSureYouWantreselectAll" xml:space="preserve">
    <value>Are you sure you want to re-select All Orders</value>
  </data>
  <data name="AreYouSureYouWantToUnSelectAll" xml:space="preserve">
    <value>Are you sure you want to unselect all</value>
  </data>
  <data name="ResPleaseSelectAtleastoneOrderOrNoChangesinOrderSelection" xml:space="preserve">
    <value>Please select atleast one order / No changes in order selection</value>
  </data>
  <data name="ResPleaseSelectAtleastonesectionandsubsection" xml:space="preserve">
    <value>Please selcet atleast one section and subsection</value>
  </data>
  <data name="ResServManuals" xml:space="preserve">
    <value>ServManuals</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="Qty/Vehiclecnt" xml:space="preserve">
    <value>Qty./No. of VIN's</value>
  </data>
  <data name="ResImportOrderWiseDeliverDate" xml:space="preserve">
    <value>Import Order Wise Delivery Date</value>
  </data>
  <data name="ResOrderDetailsCannotbeEmpty" xml:space="preserve">
    <value>Order Details Cannot Be Empty</value>
  </data>
  <data name="ResSecSelected" xml:space="preserve">
    <value>Selected</value>
  </data>
  <data name="ResSelSeccode" xml:space="preserve">
    <value>Select Section Code</value>
  </data>
  <data name="ResReleaseinformation" xml:space="preserve">
    <value>Release Information</value>
  </data>
  <data name="ResPassword" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="ChasisSpecificCatalogue" xml:space="preserve">
    <value>Chassis Unique Order</value>
  </data>
  <data name="ModelOrderSpecificCatalogue" xml:space="preserve">
    <value>Model and Order specific catalogue</value>
  </data>
  <data name="ResMaskingActionDetails" xml:space="preserve">
    <value>Masking action details</value>
  </data>
  <data name="ResProductTypeDetails" xml:space="preserve">
    <value>Product type details</value>
  </data>
  <data name="Enteryourpasswordhere" xml:space="preserve">
    <value>Enter your password here</value>
  </data>
  <data name="Enteryourusernamehere" xml:space="preserve">
    <value>Enter your user name here</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Forget Password</value>
  </data>
  <data name="Rememberme" xml:space="preserve">
    <value>Remember me</value>
  </data>
  <data name="ResOnlyslsxfile" xml:space="preserve">
    <value>Only an .xlsx|.xls|.csv file of 30 MB or less containing the required column headers.</value>
  </data>
  <data name="ResAssemblyPartList" xml:space="preserve">
    <value>Assembly Parts List</value>
  </data>
  <data name="ResDownloadFile" xml:space="preserve">
    <value>Download File</value>
  </data>
  <data name="ResImportHistory" xml:space="preserve">
    <value>View Import History</value>
  </data>
  <data name="ResMenuSwitch" xml:space="preserve">
    <value>Menu Switch</value>
  </data>
  <data name="ResDownloadPDFFile" xml:space="preserve">
    <value>Download PDF File</value>
  </data>
  <data name="ResReportProblemorFeedback" xml:space="preserve">
    <value>Report a problem or feedback</value>
  </data>
  <data name="ResShowOnlyImage" xml:space="preserve">
    <value>Show Only Image</value>
  </data>
  <data name="ResUploadAttachments" xml:space="preserve">
    <value>Upload Attachments</value>
  </data>
  <data name="ResViewServiceManuals" xml:space="preserve">
    <value>ViewServiceManuals</value>
  </data>
  <data name="ResMFGCode2" xml:space="preserve">
    <value>Manufacturer Code</value>
  </data>
  <data name="TokenGenError" xml:space="preserve">
    <value>Token Generation Error !</value>
  </data>
</root>