﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreUploadOpeningBalanceServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreUploadOpeningBalanceController : ApiController
    {

        #region ::: ImportPartsTemplate Uday Kumar J B 16-08-2024:::
        /// <summary>
        /// ImportPartsTemplate
        /// </summary>
        [Route("api/CoreUploadOpeningBalance/ImportPartsTemplate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ImportPartsTemplate()
        {
            var Response = default(dynamic);
            try
            {
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreUploadOpeningBalanceServices.ImportPartsTemplate(LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion 


        #region ::: UploadFile Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// UploadFile
        /// </summary>
        /// 
        [Route("api/CoreUploadOpeningBalance/UploadFile")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> UploadFile(UploadFileCoreUploadOpeningBalanceList UploadFileCoreUploadOpeningBalanceobj)
        {
            String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                string branch = null;
                string companyID = null;
                string userID = null;
                string ObjectID = null;
                string effectiveFrom = null;
                string roleName = null;
                string userCulture = null;
                IFormFile partsPriceFile = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "branch":
                                branch = value;
                                break;
                            case "companyID":
                                companyID = value;
                                break;
                            case "userID":
                                userID = value;
                                break;
                            case "ObjectID":
                                ObjectID = value;
                                break;
                            case "effectiveFrom":
                                effectiveFrom = value;
                                break;
                            case "roleName":
                                roleName = value;
                                break;
                            case "userCulture":
                                userCulture = value;
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            partsPriceFile = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = CoreUploadOpeningBalanceServices.UploadFile(partsPriceFile, UploadFileCoreUploadOpeningBalanceobj, connString, LogException);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }
        #endregion



        #region ::: SelectPart Uday Kumar J B 16-08-2024  :::
        /// <summary>
        /// SelectPart
        /// </summary>
        ///
        [Route("api/CoreUploadOpeningBalance/SelectPart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPart([FromBody] SelectPartCoreUploadOpeningBalanceList SelectPartCoreUploadOpeningBalanceobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreUploadOpeningBalanceServices.SelectPart(connString, SelectPartCoreUploadOpeningBalanceobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckIFPartExists Uday Kumar J B 16-08-2024  :::
        /// <summary>
        /// CheckIFPartExists
        /// </summary>
        ///
        [Route("api/CoreUploadOpeningBalance/CheckIFPartExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckIFPartExists([FromBody] CheckIFPartExistsCoreUploadOpeningBalanceList CheckIFPartExistsCoreUploadOpeningBalanceobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreUploadOpeningBalanceServices.CheckIFPartExists(connString, CheckIFPartExistsCoreUploadOpeningBalanceobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckStockLedgerTable  Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// CheckStockLedgerTable
        /// </summary>
        ///
        [Route("api/CoreUploadOpeningBalance/CheckStockLedgerTable")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckStockLedgerTable()
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreUploadOpeningBalanceServices.CheckStockLedgerTable(connString);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}