﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Rpt_IssueArea_IssueSubAreaServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Tr_AverageResolutionTimeServices
    {


        #region ::: Select Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To select Year wise Details
        /// </summary>
        public static IActionResult Select(HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.ToDate);
                TDate = TDate.AddDays(1);
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_AverageResolutionSelectobj.BranchIDs.TrimEnd(new char[] { ',' });
                string ValueArray = (HelpDesk_Tr_AverageResolutionSelectobj.ValueArray == "" || HelpDesk_Tr_AverageResolutionSelectobj.ValueArray == null) ? string.Empty : HelpDesk_Tr_AverageResolutionSelectobj.ValueArray.ToString();
                string TextArray = (HelpDesk_Tr_AverageResolutionSelectobj.TextArray == "" || HelpDesk_Tr_AverageResolutionSelectobj.TextArray == null) ? string.Empty : Common.DecryptString(HelpDesk_Tr_AverageResolutionSelectobj.TextArray.ToString());
                //List<ServiceRequest> ServiceRequestList = null;
                IEnumerable<AverageResolutionTimeObject> IEAverageResolutionTimeReport = null;
                IQueryable<AverageResolutionTimeObject> IQAverageResolutionTimeReport = null;


                IEAverageResolutionTimeReport = GetAllServiceRequest(HelpDesk_Tr_AverageResolutionSelectobj, connString, LogException, ValueArray, CompanyIDs, BranchIDs, HelpDesk_Tr_AverageResolutionSelectobj.Mode, FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));

                IEAverageResolutionTimeReport = (from a in IEAverageResolutionTimeReport
                                                 group a by new { Convert.ToDateTime(a.CallClosureDate).Year, a.Company_ID, a.Branch_ID } into g
                                                 select new AverageResolutionTimeObject
                                                 {
                                                     Year = Convert.ToDateTime(g.FirstOrDefault().CallClosureDate).Year,
                                                     AverageTimestring = ConvertToHours(g.Sum(S => S.AverageTimeMinutes) / g.Count()),
                                                     AverageTimestring24Hours = ConvertToHours(g.Sum(S => S.AverageTimestring24HoursMinutes) / g.Count()),
                                                     SRCount = g.Count(),
                                                     Region = g.FirstOrDefault().Region,
                                                     CompanyName = g.FirstOrDefault().CompanyName,
                                                     BranchName = g.FirstOrDefault().BranchName
                                                 });

                IQAverageResolutionTimeReport = IEAverageResolutionTimeReport.AsQueryable<AverageResolutionTimeObject>();

                IQAverageResolutionTimeReport = IQAverageResolutionTimeReport.OrderByField<AverageResolutionTimeObject>(sidx, sord);

                Count = IQAverageResolutionTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from a in IQAverageResolutionTimeReport.AsEnumerable()
                            select new
                            {
                                a.Year,
                                a.AverageTimestring,
                                a.AverageTimestring24Hours,
                                a.SRCount,
                                a.Region,
                                a.CompanyName,
                                a.BranchName
                            }).ToList().Paginate(page, rows)
                };
                //  gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectobj.Company_ID.ToString()), Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectobj.Branch), HelpDesk_Tr_AverageResolutionSelectobj.User_ID, Common.GetObjectID("HelpDesk_Tr_AverageResolutionTime"), 0, 0, 0, "Generated-Average Resolution Time Report ", false, Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.LoggedINDateTime));

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        public static string ConvertToHours(int Minutes)
        {
            double a = Convert.ToDouble(Minutes);
            string InTimeFormat = Math.Floor(a / 60).ToString() + ":" + (Math.Floor(a % 60).ToString().Length == 1 ? "0" + Math.Floor(a % 60).ToString() : Math.Floor(a % 60).ToString());
            return InTimeFormat;
        }
        public static int ConvertToMinutes(string Hours)
        {
            int Minutes = 0;
            Minutes = Convert.ToInt32(Hours.Split(':')[0]) * 60 + Convert.ToInt32(Hours.Split(':')[1]);
            return Minutes;
        }

        #endregion



        #region ::: Get All Service Requests Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// Get All Service Requests
        /// </summary>
        /// 

        private static IEnumerable<AverageResolutionTimeObject> GetAllServiceRequest(HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj, string connString, int LogException, string ValueArray, string CompanyIDs, string BranchIDs, int Mode, string FromDate, string ToDate)
        {
            List<AverageResolutionTimeObject> IEAverageResolutionTimeReport = new List<AverageResolutionTimeObject>();
            try
            {
                int UserLang = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetAllServiceRequests", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@ValueArray", ValueArray.TrimEnd(','));
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs.TrimEnd(','));
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs.TrimEnd(','));
                        cmd.Parameters.AddWithValue("@Mode", Mode);
                        cmd.Parameters.AddWithValue("@FromDate", DateTime.Parse(FromDate));
                        cmd.Parameters.AddWithValue("@ToDate", DateTime.Parse(ToDate));

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                AverageResolutionTimeObject item = new AverageResolutionTimeObject
                                {
                                    ServiceRequestID = reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID")),
                                    CallDate = reader.GetDateTime(reader.GetOrdinal("CallDateAndTime")),
                                    CallClosureDate = reader.GetDateTime(reader.GetOrdinal("CallClosureDateAndTime")),
                                    AverageTimeMinutes = ConvertToMinutes(reader["ResolutionTime"].ToString()),
                                    AverageTimestring24HoursMinutes = Math.Max(0, reader.GetInt32(reader.GetOrdinal("AverageTimestring24HoursMinutes"))),
                                    AverageTimestring = reader["ResolutionTime"].ToString(),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Region = Common.getRegionName(connString, LogException, UserLang, GeneralLang, reader.GetInt32(reader.GetOrdinal("Branch_ID")))
                                };

                                // Fetch CompanyName and BranchName separately
                                item.CompanyName = GetCompanyName(connString, item.Company_ID);
                                item.BranchName = GetBranchName(connString, item.Branch_ID);

                                IEAverageResolutionTimeReport.Add(item);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return IEAverageResolutionTimeReport;
        }

        // Helper methods to fetch Company and Branch names
        private static string GetCompanyName(string connString, int companyId)
        {
            using (SqlConnection conn = new SqlConnection(connString))
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyNameByIDServiceRequest", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                // Add parameter for CompanyID
                cmd.Parameters.AddWithValue("@CompanyID", companyId);

                conn.Open();
                return cmd.ExecuteScalar()?.ToString();
            }
        }


        private static string GetBranchName(string connString, int branchId)
        {
            using (SqlConnection conn = new SqlConnection(connString))
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetBranchNameByIDServiceRequest", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                // Add parameter for BranchID
                cmd.Parameters.AddWithValue("@BranchID", branchId);

                conn.Open();
                return cmd.ExecuteScalar()?.ToString();
            }
        }
        #endregion


        #region ::: SelectMonthWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Month Wise Avearage Resolution Time
        /// </summary>
        public static IActionResult SelectMonthWise(HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            IEnumerable<AverageResolutionTimeObject> IEAverageResolutionTimeReport = null;
            IEnumerable<AverageResolutionTimeObject> ReportData = null;
            var jsonData = default(dynamic);
            try
            {

                int Count = 0;
                int Total = 0;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.ToDate);
                TDate = TDate.AddDays(1);
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_AverageResolutionSelectobj.BranchIDs.TrimEnd(new char[] { ',' });
                string ValueArray = (HelpDesk_Tr_AverageResolutionSelectobj.ValueArray == "" || HelpDesk_Tr_AverageResolutionSelectobj.ValueArray == null) ? string.Empty : HelpDesk_Tr_AverageResolutionSelectobj.ValueArray.ToString();
                string TextArray = (HelpDesk_Tr_AverageResolutionSelectobj.TextArray == "" || HelpDesk_Tr_AverageResolutionSelectobj.TextArray == null) ? string.Empty : Common.DecryptString(HelpDesk_Tr_AverageResolutionSelectobj.TextArray.ToString());
                ReportData = GetAllServiceRequest(HelpDesk_Tr_AverageResolutionSelectobj, connString, LogException, ValueArray, CompanyIDs, BranchIDs, HelpDesk_Tr_AverageResolutionSelectobj.Mode, FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));
                ReportData = ReportData.Where(R => Convert.ToDateTime(R.CallClosureDate).Year == HelpDesk_Tr_AverageResolutionSelectobj.Year).ToList();

                IQueryable<AverageResolutionTimeObject> IQAverageResolutionTimeReport = null;

                IEAverageResolutionTimeReport = (from a in ReportData
                                                 group a by new { Convert.ToDateTime(a.CallClosureDate).Year, Convert.ToDateTime(a.CallClosureDate).Month } into g
                                                 select new AverageResolutionTimeObject
                                                 {
                                                     Month = Convert.ToDateTime(g.FirstOrDefault().CallClosureDate).Month,
                                                     MonthName = GetMonthName(HelpDesk_Tr_AverageResolutionSelectobj, Convert.ToDateTime(g.FirstOrDefault().CallClosureDate).Month),
                                                     AverageTimestring = ConvertToHours(g.Sum(S => S.AverageTimeMinutes) / g.Count()),
                                                     AverageTimestring24Hours = ConvertToHours(g.Sum(S => S.AverageTimestring24HoursMinutes) / g.Count()),
                                                     SRCount = g.Count()
                                                 });

                IQAverageResolutionTimeReport = IEAverageResolutionTimeReport.AsQueryable<AverageResolutionTimeObject>();

                IQAverageResolutionTimeReport = IQAverageResolutionTimeReport.OrderByField<AverageResolutionTimeObject>(sidx, sord);

                Count = IQAverageResolutionTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from a in IQAverageResolutionTimeReport.AsEnumerable()
                            select new
                            {
                                a.Month,
                                a.MonthName,
                                a.AverageTimestring,
                                a.AverageTimestring24Hours,
                                a.SRCount
                            }).ToList().Paginate(page, rows)
                };


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: GetMonthName Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj, int ID)
        {
            string MonthName = string.Empty;
            try
            {
                switch (ID)
                {
                    case 1:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "January").ToString();
                        break;
                    case 2:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "February").ToString();
                        break;
                    case 3:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "March").ToString();
                        break;
                    case 4:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "April").ToString();
                        break;
                    case 5:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "May").ToString();
                        break;
                    case 6:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "June").ToString();
                        break;
                    case 7:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "July").ToString();
                        break;
                    case 8:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "August").ToString();
                        break;
                    case 9:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "September").ToString();
                        break;
                    case 10:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "October").ToString();
                        break;
                    case 11:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "November").ToString();
                        break;
                    case 12:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionSelectobj.UserCulture.ToString(), "December").ToString();
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {

            }
            return MonthName;
        }
        #endregion



        #region ::: SelectDateWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Wise
        /// </summary>
        public static IActionResult SelectDateWise(HelpDesk_Tr_AverageResolutionSelectList HelpDesk_Tr_AverageResolutionSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            IEnumerable<AverageResolutionTimeObject> ReportData = null;
            try
            {

                int Count = 0;
                int Total = 0;

                IEnumerable<AverageResolutionTimeObject> IEAverageResolutionTimeReport = null;
                IQueryable<AverageResolutionTimeObject> IQAverageResolutionTimeReport = null;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectobj.ToDate);
                TDate = TDate.AddDays(1);
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_AverageResolutionSelectobj.BranchIDs.TrimEnd(new char[] { ',' });
                string ValueArray = (HelpDesk_Tr_AverageResolutionSelectobj.ValueArray == "" || HelpDesk_Tr_AverageResolutionSelectobj.ValueArray == null) ? string.Empty : HelpDesk_Tr_AverageResolutionSelectobj.ValueArray.ToString();
                string TextArray = (HelpDesk_Tr_AverageResolutionSelectobj.TextArray == "" || HelpDesk_Tr_AverageResolutionSelectobj.TextArray == null) ? string.Empty : Common.DecryptString(HelpDesk_Tr_AverageResolutionSelectobj.TextArray.ToString());
                ReportData = GetAllServiceRequest(HelpDesk_Tr_AverageResolutionSelectobj, connString, LogException, ValueArray, CompanyIDs, BranchIDs, HelpDesk_Tr_AverageResolutionSelectobj.Mode, FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));
                ReportData = ReportData.Where(R => Convert.ToDateTime(R.CallClosureDate).Year == HelpDesk_Tr_AverageResolutionSelectobj.Year && Convert.ToDateTime(R.CallClosureDate).Month == HelpDesk_Tr_AverageResolutionSelectobj.Month).ToList();

                IEAverageResolutionTimeReport = (from a in ReportData
                                                 group a by new { Convert.ToDateTime(a.CallClosureDate).Year, Convert.ToDateTime(a.CallClosureDate).Month, Convert.ToDateTime(a.CallClosureDate).Date } into g
                                                 select new AverageResolutionTimeObject
                                                 {
                                                     Date = Convert.ToDateTime((g.FirstOrDefault().CallClosureDate)).ToString("dd-MMM-yyyy"),
                                                     AverageTimestring = ConvertToHours(g.Sum(S => S.AverageTimeMinutes) / g.Count()),
                                                     AverageTimestring24Hours = ConvertToHours(g.Sum(S => S.AverageTimestring24HoursMinutes) / g.Count()),
                                                     SRCount = g.Count()
                                                 });

                IQAverageResolutionTimeReport = IEAverageResolutionTimeReport.AsQueryable<AverageResolutionTimeObject>();

                IQAverageResolutionTimeReport = IQAverageResolutionTimeReport.OrderByField<AverageResolutionTimeObject>(sidx, sord);

                Count = IQAverageResolutionTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from a in IQAverageResolutionTimeReport.AsEnumerable()
                            select new
                            {
                                a.Date,
                                a.SRCount,
                                a.AverageTimestring,
                                a.AverageTimestring24Hours
                            }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion



        #region ::: LoadBranchDD Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        /// 
        public static IActionResult LoadBranchDD(HelpDesk_Tr_AverageResolutionLoadBranchDDList HelpDesk_Tr_AverageResolutionLoadBranchDDobj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            int languageID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionLoadBranchDDobj.UserLanguageID);
            int companyID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionLoadBranchDDobj.Company_ID);
            bool isSameLanguage = HelpDesk_Tr_AverageResolutionLoadBranchDDobj.GeneralLanguageCode.ToString() == HelpDesk_Tr_AverageResolutionLoadBranchDDobj.UserLanguageCode.ToString();

            try
            {
                // Get Employee Branches
                List<int> employeeBranchIDs = new List<int>();
                using (SqlConnection conn = new SqlConnection(connString))
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetEmployeeBranches", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@EmployeeID", HelpDesk_Tr_AverageResolutionLoadBranchDDobj.Employee_ID);
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            employeeBranchIDs.Add(Convert.ToInt32(reader["Branch_ID"]));
                        }
                    }
                }

                // Choose which branch data to load based on the language preference
                List<object> branches = new List<object>();
                string storedProcedure = isSameLanguage ? "SP_AMERP_HelpDesk_GetBranchByCompanyID" : "SP_AMERP_HelpDesk_GetBranchLocaleByCompanyID";

                using (SqlConnection conn = new SqlConnection(connString))
                using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            int branchID = Convert.ToInt32(reader["Branch_ID"]);
                            if (employeeBranchIDs.Contains(branchID))
                            {
                                branches.Add(new
                                {
                                    ID = branchID,
                                    Name = reader["Branch_Name"].ToString()
                                });
                            }
                        }
                    }
                }

                jsonobj = new { Branch = branches };

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: SelectCustomer Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To Select Customer
        /// </summary>
        /// 
        public static IActionResult SelectCustomer(HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {

                int languageID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageID);
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectCustomerobj.CompanyIDs.TrimEnd(',');

                // Fetch customer details
                List<GNM_Party> customerDetails = new List<GNM_Party>();
                using (SqlConnection conn = new SqlConnection(connString))
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCustomerDetailsByCompanyIDs", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                    cmd.Parameters.AddWithValue("@PartyType", 1); // PartyType is hardcoded to 1 as per the original query
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customerDetails.Add(new GNM_Party
                            {
                                Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                Party_Name = reader["Party_Name"].ToString(),
                                Company_ID = Convert.ToInt32(reader["Company_ID"])
                            });
                        }
                    }
                }

                if (HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResolutionSelectCustomerobj.GeneralLanguageCode.ToString())
                {
                    // If the user language matches the general language, return the data directly
                    Masterdata = new
                    {
                        CustomerData = customerDetails
                            .OrderBy(c => c.Party_Name)
                            .Select(c => new
                            {
                                ID = c.Party_ID,
                                Name = c.Party_Name
                            })
                            .Distinct()
                    };
                }
                else
                {
                    // Fetch localized customer details if languages do not match
                    List<GNM_PartyLocale> customerLocaleDetails = new List<GNM_PartyLocale>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCustomerLocaleDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", languageID);
                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                customerLocaleDetails.Add(new GNM_PartyLocale
                                {
                                    Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                    Party_Name = reader["Party_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Join customer details with locale details
                    Masterdata = new
                    {
                        CustomerData = (from cust in customerDetails
                                        join custL in customerLocaleDetails on cust.Party_ID equals custL.Party_ID
                                        orderby custL.Party_Name
                                        select new
                                        {
                                            ID = custL.Party_ID,
                                            Name = custL.Party_Name
                                        }).Distinct()
                    };
                }


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion




        #region ::: SelectModel Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Model
        /// </summary> 
        /// 

        public static IActionResult SelectModel(HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {

                int languageID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageID);
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectCustomerobj.CompanyIDs.TrimEnd(',');

                // Fetch company brands based on CompanyIDs
                List<GNM_CompanyBrands> companyBrands = new List<GNM_CompanyBrands>();
                using (SqlConnection conn = new SqlConnection(connString))
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyBrandsByCompanyIDs", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            companyBrands.Add(new GNM_CompanyBrands
                            {
                                Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                Brand_ID = Convert.ToInt32(reader["Brand_ID"])
                            });
                        }
                    }
                }

                // Prepare a comma-separated list of Brand_IDs for fetching models
                string brandIDs = string.Join(",", companyBrands.Select(b => b.Brand_ID).Distinct());

                // Fetch models based on Brand_IDs
                List<GNM_Model> models = new List<GNM_Model>();
                using (SqlConnection conn = new SqlConnection(connString))
                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelsSelectModel", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@BrandIDs", brandIDs);
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            models.Add(new GNM_Model
                            {
                                Model_ID = Convert.ToInt32(reader["Model_ID"]),
                                Brand_ID = Convert.ToInt32(reader["Brand_ID"]),
                                Model_Name = reader["Model_Name"].ToString()
                            });
                        }
                    }
                }

                if (HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResolutionSelectCustomerobj.GeneralLanguageCode.ToString())
                {
                    // No localization required
                    Masterdata = new
                    {
                        ModelData = models
                            .Where(m => companyBrands.Any(cb => cb.Brand_ID == m.Brand_ID))
                            .OrderBy(m => m.Model_Name)
                            .Select(m => new
                            {
                                ID = m.Model_ID,
                                Name = m.Model_Name
                            })
                            .Distinct()
                    };
                }
                else
                {
                    // Fetch localized model data
                    List<GNM_ModelLocale> modelLocales = new List<GNM_ModelLocale>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelLocaleDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", languageID);
                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                modelLocales.Add(new GNM_ModelLocale
                                {
                                    Model_ID = Convert.ToInt32(reader["Model_ID"]),
                                    Model_Name = reader["Model_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Join models with localized data
                    Masterdata = new
                    {
                        ModelData = (from m in models
                                     join cb in companyBrands on m.Brand_ID equals cb.Brand_ID
                                     join ml in modelLocales on m.Model_ID equals ml.Model_ID
                                     orderby ml.Model_Name
                                     select new
                                     {
                                         ID = ml.Model_ID,
                                         Name = ml.Model_Name
                                     }).Distinct()
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion



        #region ::: SelectISSUEAREA Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To Select ISSUEAREA
        /// </summary> 
        /// 
        public static IActionResult SelectISSUEAREA(HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectCustomerobj.CompanyIDs.TrimEnd(new char[] { ',' });
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageID);
                string UserLanguageCode = HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageCode.ToString();
                string GeneralLanguageCode = HelpDesk_Tr_AverageResolutionSelectCustomerobj.GeneralLanguageCode.ToString();

                DataTable dt = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetISSUEAREAData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@LanguageCode", UserLanguageCode);
                        cmd.Parameters.AddWithValue("@GeneralLanguageCode", GeneralLanguageCode);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dt);
                    }
                }

                // Process the DataTable into the desired JSON format
                if (dt.Rows.Count > 0)
                {
                    var issueAreaData = dt.AsEnumerable().Select(row => new
                    {
                        ID = row.Field<int>("ID"),
                        Name = row.Field<string>("Name")
                    }).Distinct().OrderBy(x => x.Name);

                    Masterdata = new
                    {
                        ISSUEAREAData = issueAreaData
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion



        #region ::: SelectCallProirity Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select CallPriority
        /// </summary> 
        /// 
        public static IActionResult SelectCallPriority(HelpDesk_Tr_AverageResolutionSelectCustomerList HelpDesk_Tr_AverageResolutionSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {
                string CompanyIDs = HelpDesk_Tr_AverageResolutionSelectCustomerobj.CompanyIDs.TrimEnd(new char[] { ',' });
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageID);
                string UserLanguageCode = HelpDesk_Tr_AverageResolutionSelectCustomerobj.UserLanguageCode.ToString();
                string GeneralLanguageCode = HelpDesk_Tr_AverageResolutionSelectCustomerobj.GeneralLanguageCode.ToString();

                DataTable dt = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCallPriorityData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@LanguageCode", UserLanguageCode);
                        cmd.Parameters.AddWithValue("@GeneralLanguageCode", GeneralLanguageCode);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dt);
                    }
                }

                // Process the DataTable into the desired JSON format
                if (dt.Rows.Count > 0)
                {
                    var callPriorityData = dt.AsEnumerable().Select(row => new
                    {
                        ID = row.Field<int>("ID"),
                        Name = row.Field<string>("Name")
                    }).Distinct().OrderBy(x => x.Name);

                    Masterdata = new
                    {
                        CallPriorityData = callPriorityData
                    };
                }
                else
                {
                    // Handle the null or empty DataTable case
                    Masterdata = new
                    {
                        CallPriorityData = Enumerable.Empty<object>() // Return an empty list
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Masterdata);
        }
        #endregion



        #region ::: SelectDateDetails  Uday Kmar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Details
        /// </summary>
        /// 

        public static IActionResult SelectDateDetails(HelpDesk_Tr_AverageResolutionSelectDateDetailsList HelpDesk_Tr_AverageResolutionSelectDateDetailsobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                DateTime DDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionSelectDateDetailsobj.Date);
                string Company_ID = Convert.ToString(HelpDesk_Tr_AverageResolutionSelectDateDetailsobj.CompanyID);
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionSelectDateDetailsobj.LanguageID);

                // Choose SP based on language code
                string spName = (HelpDesk_Tr_AverageResolutionSelectDateDetailsobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResolutionSelectDateDetailsobj.GeneralLanguageCode.ToString())
                                ? "SP_AMERP_HelpDesk_SelectDateDetails_DefaultLanguage"
                                : "SP_AMERP_HelpDesk_SelectDateDetails_LocaleLanguage";

                // Create SQL Connection and Command
                using (SqlConnection conn = new SqlConnection(connString))
                using (SqlCommand cmd = new SqlCommand(spName, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                    cmd.Parameters.AddWithValue("@DDate", DDate);
                    if (spName == "SP_AMERP_HelpDesk_SelectDateDetails_LocaleLanguage")
                    {
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                    }

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        var resultList = new List<AverageResolutionTimeObject>();
                        IQueryable<AverageResolutionTimeObject> IQAverageResolutionTimeReport = null;
                        while (reader.Read())
                        {
                            resultList.Add(new AverageResolutionTimeObject
                            {
                                ServiceRequestID = reader.GetInt32(reader.GetOrdinal("ServiceRequestID")),
                                SRNumber = reader.GetString(reader.GetOrdinal("SRNumber")),
                                CallDate = reader.GetDateTime(reader.GetOrdinal("CallDate")),
                                CustomerName = reader.GetString(reader.GetOrdinal("CustomerName")),
                                CustomerLocation = reader.GetString(reader.GetOrdinal("CustomerLocation")),
                                Priority = reader.GetString(reader.GetOrdinal("Priority")),
                                ISSUEAREA = reader.GetString(reader.GetOrdinal("ISSUEAREA")),
                                Brand = reader.GetString(reader.GetOrdinal("Brand")),
                                ProductType = reader.GetString(reader.GetOrdinal("ProductType")),
                                ModelName = reader.GetString(reader.GetOrdinal("ModelName")),
                                AverageTimestring = reader.GetString(reader.GetOrdinal("AverageTimestring")),
                                AverageTimestring24Hours = reader.GetString(reader.GetOrdinal("AverageTimestring24Hours")),
                                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
                            });
                        }
                        IQAverageResolutionTimeReport = resultList.AsQueryable<AverageResolutionTimeObject>();

                        if (_search)
                        {
                            Filters filterobj = JObject.Parse(Uri.UnescapeDataString(filters)).ToObject<Filters>();
                            IQAverageResolutionTimeReport = IQAverageResolutionTimeReport.FilterSearch<AverageResolutionTimeObject>(filterobj);
                        }
                        IQAverageResolutionTimeReport = IQAverageResolutionTimeReport.OrderByField<AverageResolutionTimeObject>(sidx, sord);

                        int Count = IQAverageResolutionTimeReport.Count();
                        int Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                        if (Count < (rows * page) && Count != 0)
                        {
                            page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                        }

                        // Convert result to the JSON format as in your original code
                        jsonData = new
                        {
                            total = Total,
                            page = page,
                            records = Count,
                            data = (from a in IQAverageResolutionTimeReport.AsEnumerable()
                                    select new
                                    {
                                        SRNumber = "<span  key='" + a.ServiceRequestID + "' style='color:blue;text-decoration:underline;cursor:pointer'>" + a.SRNumber + "</span>",
                                        Date = a.CallDate.ToString("dd-MMM-yyyy"),
                                        a.CustomerName,
                                        a.CustomerLocation,
                                        a.Priority,
                                        a.ISSUEAREA,
                                        a.Brand,
                                        a.ProductType,
                                        a.ModelName,
                                        a.AverageTimestring,
                                        a.AverageTimestring24Hours,
                                        a.Company_ID,
                                        a.ServiceRequestID,
                                    }).ToList().Paginate(page, rows)
                        };


                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion



        #region ::: Export  Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// Exporting Company Grid
        /// </summary>
        /// 
        public static async Task<object> Export(HelpDesk_Tr_AverageResolutionExportList HelpDesk_Tr_AverageResolutionExportobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            int userID = HelpDesk_Tr_AverageResolutionExportobj.User_ID;
            int companyID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionExportobj.Company_ID);
            int branchID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionExportobj.Branch);
            string BranchName = string.Empty;
            string All = string.Empty;
            DataTable Dt;
            try
            {
                All = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "All").ToString();
                Dt = new DataTable();
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Region").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Company").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Branch").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "CaseNumber").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "date").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Party").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "PartyLocation").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Priority").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "ISSUEAREA").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "brand").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "producttype").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "model").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "resolutiontime").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "averageresolutiontime24Hours").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("CompanyName");
                DtAlignment.Columns.Add("BranchName");
                DtAlignment.Columns.Add("CaseNumber");
                DtAlignment.Columns.Add("date");
                DtAlignment.Columns.Add("customername");
                DtAlignment.Columns.Add("customerlocation");
                DtAlignment.Columns.Add("Priority");
                DtAlignment.Columns.Add("ISSUEAREA");
                DtAlignment.Columns.Add("brand");
                DtAlignment.Columns.Add("producttype");
                DtAlignment.Columns.Add("model");
                DtAlignment.Columns.Add("resolutiontime");
                DtAlignment.Columns.Add("averageresolutiontime24Hours");
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1);

                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResolutionExportobj.LanguageID);
                string UserLang = HelpDesk_Tr_AverageResolutionExportobj.UserLanguageCode;
                string GeneralLang = HelpDesk_Tr_AverageResolutionExportobj.GeneralLanguageCode;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionExportobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResolutionExportobj.ToDate);
                string CompanyIDs = HelpDesk_Tr_AverageResolutionExportobj.CompanyIDs.ToString();
                string BranchIDs = HelpDesk_Tr_AverageResolutionExportobj.BranchIDs.ToString();
                string ValueArray = HelpDesk_Tr_AverageResolutionExportobj.HD_Tr_AverageResolutionTime_ValueArray.ToString();
                string TextArray = HelpDesk_Tr_AverageResolutionExportobj.HD_Tr_AverageResolutionTime_TextArray.ToString();
                int Mode = Convert.ToInt32(HelpDesk_Tr_AverageResolutionExportobj.Mode);
                TDate = TDate.AddDays(1);
                string Branch_ID = Convert.ToString(HelpDesk_Tr_AverageResolutionExportobj.HD_Tr_AverageResolutionTime_Branch_ID);

                SqlConnection conn = new SqlConnection(connString);
                conn.Open();

                SqlCommand cmdServiceRequest = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestsForExport", conn);
                cmdServiceRequest.CommandType = CommandType.StoredProcedure;
                cmdServiceRequest.Parameters.AddWithValue("@UserLang", UserLang);
                cmdServiceRequest.Parameters.AddWithValue("@GeneralLang", GeneralLang);
                cmdServiceRequest.Parameters.AddWithValue("@FromDate", FDate.ToString("yyyy-MM-dd"));
                cmdServiceRequest.Parameters.AddWithValue("@ToDate", TDate.ToString("yyyy-MM-dd"));
                cmdServiceRequest.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                cmdServiceRequest.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                cmdServiceRequest.Parameters.AddWithValue("@LanguageID", Language_ID);
                SqlDataReader readerServiceRequest = cmdServiceRequest.ExecuteReader();

                List<AverageResolutionTimeObject> ServiceRequests = new List<AverageResolutionTimeObject>();


                while (readerServiceRequest.Read())
                {
                    ServiceRequests.Add(new AverageResolutionTimeObject
                    {
                        Region = readerServiceRequest["Region"].ToString(),
                        CompanyName = readerServiceRequest["Company_Name"].ToString(),
                        BranchName = readerServiceRequest["Branch_Name"].ToString(),
                        ServiceRequestID = Convert.ToInt32(readerServiceRequest["ServiceRequest_ID"]),
                        SRNumber = readerServiceRequest["ServiceRequestNumber"].ToString(),
                        CallDate = Convert.ToDateTime(readerServiceRequest["CallDateAndTime"]),
                        CustomerName = readerServiceRequest["CustomerName"].ToString(),
                        CustomerLocation = readerServiceRequest["CustomerLocation"].ToString(),
                        Priority = readerServiceRequest["Priority"].ToString(),
                        ISSUEAREA = readerServiceRequest["ISSUEAREA"].ToString(),
                        Brand = readerServiceRequest["Brand"].ToString(),
                        ProductType = readerServiceRequest["ProductType"].ToString(),
                        ModelName = readerServiceRequest["ModelName"].ToString(),
                        AverageTimestring = readerServiceRequest["ResolutionTime"].ToString(),
                        AverageTimestring24Hours = readerServiceRequest["CallClosureDateAndTime"].ToString()
                    });
                }

                readerServiceRequest.Close();

                IQueryable<AverageResolutionTimeObject> queryableServiceRequests = ServiceRequests.AsQueryable();

                if (!string.IsNullOrEmpty(filter) && filter != "null" && filter != "undefined")
                {
                    Filters filterobj = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                    if (filterobj.rules.Any())
                    {
                        queryableServiceRequests = queryableServiceRequests.FilterSearch<AverageResolutionTimeObject>(filterobj);
                    }
                }

                // Add data to the DataTable
                foreach (var request in queryableServiceRequests)
                {
                    Dt.Rows.Add(request.Region, request.CompanyName, request.BranchName, request.SRNumber, request.CallDate.ToString("dd-MMM-yyyy"),
                                request.CustomerName, request.CustomerLocation, request.Priority, request.ISSUEAREA, request.Brand,
                                request.ProductType, request.ModelName, request.AverageTimestring, request.AverageTimestring24Hours);
                }
                DataSet DsSelection = new DataSet();
                // Add other details (e.g., Date Range, Value Array, etc.)
                if (ValueArray != "Blank")
                {
                    DataTable DtDetails = new DataTable();

                    if (Mode == 1) DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Priority").ToString());
                    else if (Mode == 2) DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "model").ToString());
                    else if (Mode == 3) DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Customer").ToString());
                    else if (Mode == 4) DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "ISSUEAREA").ToString());
                    else if (Mode == 5) DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "Dealer").ToString());

                    DtDetails.Rows.Add(TextArray.TrimEnd(','));
                    DsSelection.Tables.Add(DtDetails);
                }

                // Date Range for export
                DataTable DateRange = new DataTable();
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "fromdate").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "todate").ToString());
                DateRange.Rows.Add(FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));
                ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                {
                    FileName = "AverageResolutionTime",
                    Branch = Convert.ToString(HelpDesk_Tr_AverageResolutionExportobj.Branch),
                    Company_ID = HelpDesk_Tr_AverageResolutionExportobj.Company_ID,
                    UserCulture = HelpDesk_Tr_AverageResolutionExportobj.UserCulture,
                    dt = Dt, // You can populate this with actual data as needed
                    exprtType = HelpDesk_Tr_AverageResolutionExportobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                    Header = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "averageresolutiontime").ToString(),
                    Options = DateRange, // Populate this with your report options
                    selection = DsSelection, // Add selection-related data here
                    Alignment = DtAlignment // Define alignment details for table columns
                };
                var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                return result.Value;
                // ReportExportCR5.Export(HelpDesk_Tr_AverageResolutionExportobj.exprtType, Dt, DateRange, DsSelection, DtAlignment, "AverageResolutionTime",
                //  CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResolutionExportobj.UserCulture.ToString(), "averageresolutiontime").ToString());

                // Insert log details
                //  gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_AverageResolutionExportobj.Company_ID.ToString()), branchID, HelpDesk_Tr_AverageResolutionExportobj.User_ID, Common.GetObjectID("HelpDesk_Tr_AverageResolutionTime"), 0, 0, 0, "Average Resolution Time-Export", false, Convert.ToInt32(HelpDesk_Tr_AverageResolutionExportobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_AverageResolutionExportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion



        #region ::: HelpDesk_Tr_AverageResolution List and obj classes  Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_AverageResolution
        /// </summary>
        /// 
        public class HelpDesk_Tr_AverageResolutionExportList
        {
            public int User_ID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public int Mode { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }

            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int LanguageID { get; set; }
            public string UserCulture { get; set; }
            public int UserLanguageID { get; set; }
            public DateTime HD_Tr_AverageResolutionTime_FromDate { get; set; }
            public DateTime HD_Tr_AverageResolutionTime_ToDate { get; set; }
            public string HD_Tr_AverageResolutionTime_ValueArray { get; set; }
            public string HD_Tr_AverageResolutionTime_TextArray { get; set; }
            public int HD_Tr_AverageResolutionTime_Mode { get; set; }
            public int HD_Tr_AverageResolutionTime_Branch_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int exprtType { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }

        public class HelpDesk_Tr_AverageResolutionSelectDateDetailsList
        {
            public string Date { get; set; }
            public int CompanyID { get; set; }
            public int LanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }

        }


        public class HelpDesk_Tr_AverageResolutionSelectCustomerList
        {
            public int UserLanguageID { get; set; }
            public string CompanyIDs { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }

        }

        public class HelpDesk_Tr_AverageResolutionLoadBranchDDList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Employee_ID { get; set; }

        }

        public class HelpDesk_Tr_AverageResolutionSelectList
        {

            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public int Mode { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public string ValueArray { get; set; }
            public string TextArray { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Year { get; set; }
            public int Month { get; set; }
            public string UserCulture { get; set; }
        }
        #endregion



        #region ::: HelpDesk_Tr_AverageResolution  classes  Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_AverageResolution
        /// </summary>
        /// 
        public partial class GNM_Model
        {

            public int Model_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string Model_Name { get; set; }
            public bool Model_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> ServiceFrequency { get; set; }
            public string Model_Description { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public string Series { get; set; }
        }

        public partial class GNM_ModelLocale
        {
            public int ModelLocale_ID { get; set; }
            public int Model_ID { get; set; }
            public string Model_Name { get; set; }
            public int Language_ID { get; set; }
            public string Model_Description { get; set; }
        }

        public class AverageResolutionTimeObject
        {
            public string CustomerName
            {
                get;
                set;

            }
            public string Brand
            {
                get;
                set;
            }
            public string Model
            {
                get;
                set;
            }
            public string Priority
            {
                get;
                set;
            }
            public string SRNumber
            {
                get;
                set;
            }
            public string CustomerLocation
            {
                get;
                set;
            }
            public string ProductType
            {
                get;
                set;
            }
            public int ServiceRequestID
            {
                get;
                set;
            }
            public DateTime ServiceRequestDate
            {
                get;
                set;
            }
            public DateTime CallDate
            {
                get;
                set;
            }
            public DateTime? CallClosureDate
            {
                get;
                set;
            }
            public double AverageTime
            {
                get;
                set;
            }
            public int AverageTimeMinutes
            {
                get;
                set;
            }
            public string AverageTimestring
            {
                get;
                set;
            }
            public int Year
            {
                get;
                set;
            }

            public int Month
            {
                get;
                set;
            }
            public string MonthName
            {
                get;
                set;
            }
            public int Hours
            {
                get;
                set;
            }
            public int Minutes
            {
                get;
                set;
            }
            public string Date
            {
                get;
                set;
            }
            public int SRCount
            {
                get;
                set;
            }
            public string ISSUEAREA
            {
                get;
                set;
            }
            public string ModelName
            {
                get;
                set;
            }
            public int Company_ID
            {
                get;
                set;
            }
            public int Branch_ID
            {
                get;
                set;
            }
            public string CompanyName
            {
                get;
                set;

            }
            public string BranchName
            {
                get;
                set;
            }
            public string Region
            {
                get;
                set;
            }
            public int AverageTimestring24HoursMinutes { get; set; }

            public string AverageTimestring24Hours { get; set; }
        }
        #endregion


    }
}
