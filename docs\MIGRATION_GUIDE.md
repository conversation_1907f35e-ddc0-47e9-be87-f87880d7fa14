# SharedAPIClassLibrary_DC Migration Guide - Puneeth

## Overview
This guide provides step-by-step instructions for migrating services from the legacy `SharedAPIClassLibrary_DC` to three new .NET 8 Web API projects:
- **PBC.CoreService** - For Core* services
- **PBC.HelpdeskService** - For HelpDesk* services
- **PBC.UtilityService** - For Utility services

## Project Structure Analysis

### Services Categorization

#### Core Services (59 services)
Services starting with "Core" that handle core business functionality:
- CoreChangePasswordServices
- CoreCompanyMasterServices
- CoreEmployeeMasterServices
- CorePartsMasterServices
- CoreUserMasterServices
- And 54 more Core* services...

#### HelpDesk Services (21 services)
Services starting with "HelpDesk" that handle help desk functionality:
- HelpDeskCustomerFeedBackServices
- HelpDeskServiceRequestServices
- HelpDeskTicketReportServices
- HelpDesk_Rpt_CallLogServices
- HelpDesk_Tr_AverageResolutionTimeServices
- And 16 more HelpDesk* services...

#### Other Services (Requires Manual Categorization)
- AllocationLogicServices
- CalculationServices
- CompanyParameterServices
- JwtHelper/JwtService
- PRM_* services
- PRT_* services
- PST_* services
- SRM_* services
- YANMARAPISERVICES
- _PrevostDashboardServices

## Migration Steps

### Phase 1: Prepare Migration Environment

1. **Verify Project Setup**
   ```bash
   # Test both projects build successfully
   dotnet build Core_SharedAPIClass
   dotnet build Helpdesk_SharedAPIClass
   
   # Test both projects run
   dotnet run --project Core_SharedAPIClass
   dotnet run --project Helpdesk_SharedAPIClass
   ```

2. **Create Backup**
   ```bash
   # Create backup of original library
   cp -r SharedAPIClassLibrary_DC SharedAPIClassLibrary_DC_BACKUP
   ```

### Phase 2: Migrate Shared Resources

#### Step 1: Migrate App_GlobalResources
```bash
# Copy resource files to both projects
cp SharedAPIClassLibrary_DC/App_GlobalResources/* Core_SharedAPIClass/App_GlobalResources/
cp SharedAPIClassLibrary_DC/App_GlobalResources/* Helpdesk_SharedAPIClass/App_GlobalResources/
```

#### Step 2: Migrate Common Utilities
Utilities that are shared between both services should be duplicated or moved to a shared library:
- Common.cs
- CommonFunctionalities.cs
- ExtensionMethods.cs
- Utilities.cs

### Phase 3: Service Migration (Folder by Folder)

#### Folder 1: Core Services Migration

**Services to migrate to Core_SharedAPIClass:**
1. CoreChangePasswordServices.cs
2. CoreChangeVehicleOwnershipServices.cs
3. CoreCompanyCalenderMasterServices.cs
4. CoreCompanyFinancialYearServices.cs
5. CoreCompanyMasterServices.cs
6. CoreConsigneeServices.cs
7. CoreDefectGroupMasterServices.cs
8. CoreDefectNameMasterServices.cs
9. CoreEmailTemplateService.cs
10. CoreEmployeeMasterServices.cs
... (continue with all Core* services)

**Migration Process for Each Service:**
1. Copy service file to `PBC.CoreService/Services/`
2. Update namespace from `SharedAPIClassLibrary_AMERP` to `PBC.CoreService.Services`
3. Update using statements to reference new namespaces
4. Create corresponding controller in `PBC.CoreService/Controllers/`
5. Register service in `Program.cs` dependency injection
6. Test compilation and basic functionality

#### Folder 2: HelpDesk Services Migration

**Services to migrate to PBC.HelpdeskService:**
1. HelpDeskCustomerFeedBackServices.cs
2. HelpDeskCustomerFeedBackQuestionsServices.cs
3. HelpDeskCustomerFeedBackReportsServices.cs
4. HelpDeskFollowUpServices.cs
5. HelpDeskManagerLandingPageServices.cs
... (continue with all HelpDesk* services)

**Migration Process:** (Same as Core services but to Helpdesk project)

#### Folder 3: Specialized Services Migration

**Services requiring manual categorization:**
- **Business Logic Services** → Core_SharedAPIClass
  - AllocationLogicServices.cs
  - CalculationServices.cs
  - CompanyParameterServices.cs

- **Authentication Services** → Both projects (as shared dependency)
  - JwtHelper.cs
  - JwtService.cs

- **Module-Specific Services** → Categorize based on functionality
  - PRM_* services → Core_SharedAPIClass (Parts/Resource Management)
  - PRT_* services → Core_SharedAPIClass (Purchase/Procurement)
  - PST_* services → Core_SharedAPIClass (Post-Sales/Service)
  - SRM_* services → Core_SharedAPIClass (Service Resource Management)

### Phase 4: Utilities Migration

#### Step 1: Analyze Utility Dependencies
```bash
# Analyze which utilities are used by which services
grep -r "using.*Utilities" SharedAPIClassLibrary_DC/Services/
```

#### Step 2: Migrate Utilities
- **Core-specific utilities** → PBC.CoreService/Utilities/
- **HelpDesk-specific utilities** → PBC.HelpdeskService/Utilities/
- **Shared utilities** → PBC.UtilityService/Utilities/

**Utility Files:**
- Common.cs → PBC.UtilityService
- CommonFunctionalities.cs → PBC.UtilityService
- HelpDeskCommon.cs → PBC.UtilityService
- PartsCommon.cs → PBC.CoreService
- WorkFlowCommon.cs → PBC.UtilityService

### Phase 5: Inter-Service Communication Setup

#### Step 1: Configure HTTP Clients
Both projects are already configured with HttpClient for inter-service communication.

#### Step 2: Create Service Communication Interfaces
```csharp
// In Core_SharedAPIClass
public interface IHelpdeskServiceClient
{
    Task<T> GetAsync<T>(string endpoint);
    Task<T> PostAsync<T>(string endpoint, object data);
}

// In Helpdesk_SharedAPIClass  
public interface ICoreServiceClient
{
    Task<T> GetAsync<T>(string endpoint);
    Task<T> PostAsync<T>(string endpoint, object data);
}
```

#### Step 3: Update Service URLs
- Core Service: https://localhost:7001
- Helpdesk Service: https://localhost:7002

### Phase 6: Testing and Validation

#### Step 1: Unit Testing
Create unit tests for each migrated service to ensure functionality is preserved.

#### Step 2: Integration Testing
Test inter-service communication between Core and Helpdesk services.

#### Step 3: Performance Testing
Compare performance before and after migration.

## Detailed Migration Instructions - Folder by Folder

### FOLDER 1: Core Authentication Services (Start Here)

**Services to migrate first (simplest dependencies):**
1. CoreChangePasswordServices.cs
2. CoreUserMasterServices.cs

**Step-by-step for CoreChangePasswordServices.cs:**

```bash
# Step 1: Copy the service file
cp SharedAPIClassLibrary_DC/Services/CoreChangePasswordServices.cs Core_SharedAPIClass/Services/

# Step 2: Update the service file
# - Change namespace from SharedAPIClassLibrary_AMERP to Core_SharedAPIClass.Services
# - Update using statements
# - Fix any compilation errors

# Step 3: Create a controller
# Create Core_SharedAPIClass/Controllers/CoreChangePasswordController.cs

# Step 4: Test compilation
dotnet build Core_SharedAPIClass

# Step 5: Test the endpoint
dotnet run --project Core_SharedAPIClass
# Test: GET https://localhost:7001/api/CoreChangePassword/health
```

### FOLDER 2: Core Master Data Services

**Services to migrate next:**
1. CoreCompanyMasterServices.cs
2. CoreEmployeeMasterServices.cs
3. CoreStateMasterServices.cs

### FOLDER 3: Core Parts and Product Services

**Services to migrate:**
1. CorePartsMasterServices.cs
2. CoreProductMasterServices.cs
3. CoreModelMasterServices.cs

### FOLDER 4: HelpDesk Core Services

**Services to migrate to Helpdesk_SharedAPIClass:**
1. HelpDeskServiceRequestServices.cs
2. HelpDeskCustomerFeedBackServices.cs

### FOLDER 5: HelpDesk Reporting Services

**Services to migrate:**
1. HelpDesk_Rpt_CallLogServices.cs
2. HelpDesk_Rpt_SLAExceededServices.cs
3. HelpDeskTicketReportServices.cs

### FOLDER 6: Specialized Business Services

**Services requiring careful analysis:**
1. AllocationLogicServices.cs → Core_SharedAPIClass
2. CalculationServices.cs → Core_SharedAPIClass
3. JwtHelper.cs → Both projects (shared)
4. JwtService.cs → Both projects (shared)

## Sample Migration Template

### Template for Service Migration:

```csharp
// Original namespace: SharedAPIClassLibrary_AMERP
// New namespace: Core_SharedAPIClass.Services or Helpdesk_SharedAPIClass.Services

// Update using statements:
// OLD: using SharedAPIClassLibrary_AMERP.Utilities;
// NEW: using Core_SharedAPIClass.Utilities;

// OLD: using SharedAPIClassLibrary_DC.Utilities;
// NEW: using Core_SharedAPIClass.Utilities;
```

### Template for Controller Creation:

```csharp
using Microsoft.AspNetCore.Mvc;
using Core_SharedAPIClass.Services;

namespace Core_SharedAPIClass.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CoreChangePasswordController : ControllerBase
    {
        [HttpPost("change")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            // Call the migrated service
            var result = CoreChangePasswordServices.ChangePassword(request);
            return Ok(result);
        }
    }
}
```

## Next Steps After Scaffolding

1. **Start with FOLDER 1** - Core Authentication Services
2. **Migrate one service at a time** - Test each migration
3. **Follow the folder order** - Dependencies are organized by complexity
4. **Create controllers** - Expose each service via REST API
5. **Test inter-service communication** - Once both projects have services

## Important Notes

- **Do not modify** the original SharedAPIClassLibrary_DC during migration
- **Test thoroughly** after each service migration
- **Update connection strings** in appsettings.json for each project
- **Consider creating shared models** in a separate class library
- **Plan for database separation** if services use different data contexts

## Troubleshooting

### Common Issues:
1. **Namespace conflicts** - Update using statements
2. **Missing dependencies** - Add required NuGet packages
3. **Circular dependencies** - Refactor to use interfaces
4. **Configuration issues** - Update appsettings.json

### Build Warnings:
- JWT vulnerability warning - Consider upgrading to newer version
- .NET Framework compatibility - Some packages may need updates for .NET 8
