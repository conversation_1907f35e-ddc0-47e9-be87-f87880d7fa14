﻿@{
    ViewBag.Title = "CoreMainView";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<!DOCTYPE html>

<html oncontextmenu="return false">
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Content/annotation.css" rel="stylesheet" />
    <style type="text/css">
        .jqplot-axis {
    font-size: 0.75em;
}
 
.jqplot-xaxis {
    margin-top: 10px;
}
 
.jqplot-yaxis {
    margin-right: 10px;
}


    </style>
    <script type="text/javascript">

        $(document).ready(function () {

            var fn = function (e) {
                if (!e)
                    var e = window.event;

                var keycode = e.keyCode;
                if (e.which)
                    keycode = e.which;

                var src = e.srcElement;
                if (e.target)
                    src = e.target;

                if (116 == keycode || 115 == keycode || 117 == keycode || 118 == keycode || 123 == keycode || (83 == (e.ctrlKey && keycode)) || (115 == (e.ctrlKey && keycode))) {
                    // Firefox and other non IE browsers
                    if (e.preventDefault) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                        // Internet Explorer
                    else if (e.keyCode) {
                        e.keyCode = 0;
                        e.returnValue = false;
                        e.cancelBubble = true;
                    }
                    return false;
                }
            }

            try {
                window.moveTo(0, 0);
                window.resizeTo(screen.availWidth, screen.availHeight);
            }
            catch (e) {
            }
            document.onkeydown = fn;
          
        });

    </script>
</head>
<body>
    <table style="width: 100%; text-align: left;">
        <tr>
            <td rowspan="3" style="width: 70%;">
                <table width="100%" style="display:none">
                    <tr style="background-color:#DFDFDF; height:35px">
                        <td colspan="2" style="color:#4E4E4E; vertical-align:central; font-size:15.4px; font-family:'Trebuchet MS'">  &nbsp;&nbsp;&nbsp;&nbsp; Select Year: 
                            <select style="background-color:#DFDFDF;height:20px" id="DdlFinYear">
                                <option value="0">---Select---</option>                               
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="100%" colspan="2">                                              
                            <div id="pieChart" style="height: 300px;width:600px; margin: 0 auto"></div>
                            <br />
                            <hr />                          
                        </td>
                    </tr>
                    <tr>
                        <td ><div id="BarChart"></div></td>
                        <td><div id="BarChartCallType"></div></td>
                    </tr>
                </table>
                 
            </td>
            <td style="width: 25%;">
                <div id="divTest"></div>
                <br />
            </td>
        </tr>
        <tr>
          
            <td style="width: 25%;">
                 <div id="divCustQuot"></div>
             
            </td>
        </tr>
        <tr>   
            <td style="width: 25%;">
                <div id="divJobDash"></div>              
               
            </td>
        </tr>
    </table>
</body>
</html>
