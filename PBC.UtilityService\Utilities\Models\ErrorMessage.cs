namespace PBC.UtilityService.Utilities.Models
{
    /// <summary>
    /// Represents an error message with validation status
    /// </summary>
    public class ErrorMessage
    {
        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the validation is valid
        /// </summary>
        public bool IsValid { get; set; }
    }
}
