﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html oncontextmenu="return false">
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Login</title>
    <link type="image/x-icon" rel="Shortcut Icon" href="~/images/favicon.ico" />
    <link type="image/x-icon" rel="Icon" href="~/images/favicon.ico" />
    <link href="~/Content/Themes/Blue/Blue.css" rel="stylesheet" />
    <script src="~/Scripts/JQuery/jquery-1.8.2.js"></script>
    <script type="text/javascript">

        $(document).ready(function () {
            $.url = function (url) {
                var path = '@Request.ApplicationPath'
                   if (path == '/')
                       return url;
                   return path + url;
               }
           
            $("#HelpLineEmail").attr('href', 'mailto' + ':' + '@System.Configuration.ConfigurationManager.AppSettings.Get("HelpLineEmail")');
            $("#chrome").attr("src", $.url("/Images/pic_chrome.png"));
            $("#firefox").attr("src", $.url("/Images/pic_firefox.png"));
            $("#IE").attr("src", $.url("/Images/pic_ie.png"));
            $("#safari").attr("src", $.url("/Images/pic_safari.png"));
            $("#btnSubmit").attr("src", $.url("/Images/New-loginbutton.png"));
            $("#Keyman").attr("src", $.url("/Images/Keyman.png"));

            $.fn.LoadOptions = function (data, name, value) {
                if (data != undefined && data != null) {
                    this.sel = $(this);
                    for (i = 0; i < data.length; i++) {
                        this.sel.append($("<option value='" + $(data[i]).attr(value) + "' >" + $(data[i]).attr(name) + "</option>"));
                    }
                }
            }

            var fn = function (e) {
                if (!e)
                    var e = window.event;

                var keycode = e.keyCode;
                if (e.which)
                    keycode = e.which;

                var src = e.srcElement;
                if (e.target)
                    src = e.target;

                if (116 == keycode || 115 == keycode || 117 == keycode || 118 == keycode || 123 == keycode || (83 == (e.ctrlKey && keycode)) || (115 == (e.ctrlKey && keycode))) {

                    // Firefox and other non IE browsers
                    if (e.preventDefault) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                        // Internet Explorer
                    else if (e.keyCode) {
                        e.keyCode = 0;
                        e.returnValue = false;
                        e.cancelBubble = true;
                    }

                    return false;
                }
                else if (13 == keycode) {
                    $("#btnSubmit").click();
                }
            }


            try {
                window.moveTo(0, 0);
                window.resizeTo(screen.availWidth, screen.availHeight);
                $('#HCopyRight').html('Copyright @@ ' + ((new Date()).getFullYear()) + ' Quest Informatics Private Limited');
            }
            catch (e) {
            }

            document.onkeydown = fn;



            $.ClearDropDown = function (id) {
                var selDropDown = document.getElementById(id);
                var optionDropDown = selDropDown.options;
                for (var j = 1; j < optionDropDown.length; j++) {
                    selDropDown.remove(j);
                    j--;
                }
            }


            $("#TxtUserID").focus();
            var win = null;

            $("#btnSubmit").click(function () {
                var userid = $("#TxtUserID").attr("value");
                var pwd = $("#TxtPassword").attr("value");
                var Branch = $("#SelEmployeeBranch option:selected");

                if (userid == '') {
                    alert("@HttpContext.GetGlobalResourceObject("Resource_en", "PleaseEnterLoginID")");
                    $("#TxtUserID").focus();
                }
                else if (pwd == '') {
                    alert("@HttpContext.GetGlobalResourceObject("Resource_en", "PleaseEnterPassword")");
                    $("#TxtPassword").focus();
                }
                else if (Branch.index() == 0) {
                    alert("@HttpContext.GetGlobalResourceObject("Resource_en", "PleaseSelectBranch")");
                    $("#SelEmployeeBranch").focus();
                }

                else {
                    var JsonObj = '{UserId:\'' + userid + '\',Password:\'' + pwd + '\',Branch:\'' + $("#SelEmployeeBranch option:selected").val() + '\'}';
                    $.ajax({
                        url: $.url('/CoreWFLogin/LoginAuthenticate'),
                        datatype: 'Json',
                        cache: false,
                        data: { LoginDetails: JsonObj },
                        success: function (response) {
                            alert("TESTdd")
                            var Resp = response.Result;

                            if (Resp == 'Fail') {
                                alert('@HttpContext.GetGlobalResourceObject("Resource_en", "UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain")');
                            }
                            else if (Resp == 'Success') {
                                if (response.IsActive) {
                                    if (!(response.IsLock)) {
                                        if (win == null) {
                                            alert("TESTdd")
                                            //win = window.open($.url('/WFHome/HomePage'), '_blank', 'height=' + screen.availHeight + ' , width=' + screen.availWidth + ' , resizable=1 , scrollbars=1 , top=0 , left=0 , menubar=0 ,navigationtoolbar=0,oncontextmenu=0, status=0, titlebar=0,location=0', '');

                                            window.location.href = $.url("/WFHome/HomePage");

                                            //win.moveTo(0, 0);
                                            //var par = window.open('', '_self', '', '');
                                            //window.opener = null;
                                            //par.close();
                                        }
                                    }
                                    else {
                                        alert('@HttpContext.GetGlobalResourceObject("Resource_en", "Userislocked")');
                                    }
                                }
                                else {
                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "Userisnotactive")');
                                }
                            }
                            else {
                                alert("Error..");
                            }

                        }

                    });
            }
            });

        });

//Binding Branch Drop Down based on User ID
function BindBranchDropDown() {
    $.ajax({
        url: $.url('/CoreWFLogin/BindBranchDropDown?UserName=' + $("#TxtUserID").attr("value")), type: "POST",
        success: function (response) {
            $.ClearDropDown('SelEmployeeBranch');
            if (response != null) {
                $('#SelEmployeeBranch').LoadOptions(response, 'Branch_Name', 'Branch_ID');
                if (response.length == 1) {
                    document.getElementById('SelEmployeeBranch').value = response[0].Branch_ID;
                }
            }

        },
        error: function (p, q, r) { alert('Error') }
    });
}

function startTime() {
    var today = new Date();
    var h = today.getHours();
    var m = today.getMinutes();
    var s = today.getSeconds();
    // add a zero in front of numbers<10
    m = checkTime(m);
    s = checkTime(s);
    document.getElementById('txt').innerHTML = toStandardDate((new Date())) + " " + h + ":" + m + ":" + s;
    t = setTimeout('startTime()', 500);
}

function checkTime(i) {
    if (i < 10) {
        i = "0" + i;
    }
    return i;
}
        //Coded By Muralidhara H M :-P
function toStandardDate(date) {
    var monthNamesShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    var thisdate = new Date(date);

    var Year = thisdate.getFullYear();
    var Month = monthNamesShort[thisdate.getMonth()];
    var Day = thisdate.getDate();
    Day = Day.toString().length == 1 ? ('0' + Day) : Day;
    return (Day + '-' + Month + '-' + Year);
}



    </script>
</head>
<body id="body" class="Loginbody" width="100%" onload="startTime()">
    <div id="pagewrap">
        <header id="header">
            <div class="productlogo"></div>
            <div class="comlogo" onclick="window.open('http://www.questinformatics.com')"></div>
            <div class="companyname" onclick="window.open('http://www.questinformatics.com')" style="cursor: pointer;">@HttpContext.GetGlobalResourceObject("Resource_en", "QuestInformaticsPrivateLimited")</div>
        </header>
        <section>
            <article>

                <div class="loginbg "></div>
                <div class="sidebutton">
                    <img id="btnSubmit" alt="Quest" style="cursor: pointer;" src="" />
                </div>
                <div class="loginform cf">
                    <form name="login" action="index_submit" method="get" accept-charset="utf-8">
                        <ul>
                            <li>
                                <label for="usermail">@HttpContext.GetGlobalResourceObject("Resource_en", "LoginID")</label><br />
                                <input id="TxtUserID" type="text" placeholder="Login ID" onchange="BindBranchDropDown()" maxlength="30" style="width: 150px" />
                            </li>
                            <li>
                                <label for="password">@HttpContext.GetGlobalResourceObject("Resource_en", "Password")</label><br />
                                <input id="TxtPassword" type="password" placeholder="Password" maxlength="100" style="width: 150px" /></li>
                            <li></li>
                        </ul>
                    </form>

                </div>
                <div class="loginform radiobutton">
                    <table>
                        <tr>
                            <td>
                                <label for="Branch">@HttpContext.GetGlobalResourceObject("Resource_en", "BranchName")</label><br />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select id="SelEmployeeBranch" style="width: 338px;">
                                    <option>--@HttpContext.GetGlobalResourceObject("Resource_en", "select")--</option>
                                </select></td>
                        </tr>
                    </table>
                </div>
            </article>
        </section>
        <aside>
            <div class="round">
                <div style="color:white;vertical-align:top;" id="txt" ></div>
                <div class="sidebar Sidetext">
                    <br />
                    &nbsp;&nbsp;@HttpContext.GetGlobalResourceObject("Resource_en", "VersionNumberandDate")
                </div>
                <br />
                <div class="childtext">
                    <br />
                    &nbsp;&nbsp;@HttpContext.GetGlobalResourceObject("Resource_en", "VersionNumber"): <span id="SpnVersionNumber">@System.Configuration.ConfigurationManager.AppSettings.Get("VersionNumber").ToString()</span>
                    <br />
                    &nbsp;&nbsp;@HttpContext.GetGlobalResourceObject("Resource_en", "VersionDate"): <span id="SpnVersionDate">@System.Configuration.ConfigurationManager.AppSettings.Get("VersionDate").ToString()</span>
                </div>
                <div class="sidebar Sidetext">
                    <br />
                    &nbsp;&nbsp;Upcoming Product
                </div>
                <div class="childtext">
                    <br />
                    <marquee scrollamount="1" width="100%" direction="right" onmouseover="this.stop();" onmouseout="this.start();"><label style="width:30px">@HttpContext.GetGlobalResourceObject("Resource_en", "Service")</label></marquee>
                    <marquee width="100%" direction="right" scrollamount="1" onmouseover="this.stop();" onmouseout="this.start();"><label style="width:30px">@HttpContext.GetGlobalResourceObject("Resource_en", "Parts")</label>&nbsp;&nbsp;&nbsp;</marquee>
                    <br />
                </div>
                <div class="sidebar Sidetext">
                    <br />
                    &nbsp;&nbsp;Help Line
                </div>
                <br />
                <div class="childtext">
                    <br />
                    <a id="HelpLineEmail" style="color: white; vertical-align: middle; cursor: pointer;">@System.Configuration.ConfigurationManager.AppSettings.Get("HelpLineEmail").ToString()</a>
                    <br />
                    <span style="text-decoration: none; vertical-align: middle; color: white;" id="SpnHelpLineNumber">@System.Configuration.ConfigurationManager.AppSettings.Get("HelpLineNumber")</span>
                </div>
            </div>
        </aside>
 
       
        <footer>
            <h2 id="HCopyRight" style="color: white; cursor: pointer;" onclick="window.open('http://www.questinformatics.com')"></h2>
            <a class="stuts">
                <img id="firefox" src="" onclick="window.open('http://www.mozilla.org/en-US/firefox/new/')" style="cursor: pointer;" title="Firefox" />&nbsp;&nbsp;&nbsp;
        <img id="chrome" src="" style="cursor: pointer;" title="Chrome" onclick="window.open('http://www.google.com/chrome')" />&nbsp;&nbsp;&nbsp;
        <img id="IE" src="" onclick="window.open('http://windows.microsoft.com/en-US/internet-explorer/download-ie')" style="cursor: pointer;" title="Internet Explorer 9 & above" />
            </a>
        </footer>
    </div>
</body>
</html>

