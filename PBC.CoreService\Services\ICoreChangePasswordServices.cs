using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Services
{
    public interface ICoreChangePasswordServices
    {
        /// <summary>
        /// Check if old password matches the stored password
        /// </summary>
        /// <param name="obj">CheckOldPasswordList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with count (1 if password matches, 0 if not)</returns>
        Task<IActionResult> CheckOldPassword(CheckOldPasswordList obj, string connString, int logException);

        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="obj">ChangePasswordList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>Task</returns>
        Task ChangePassword(ChangePasswordList obj, string connString, int logException);
    }
}
