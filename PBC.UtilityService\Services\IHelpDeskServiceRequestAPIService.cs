using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Interface for Help Desk Service Request API Service
    /// </summary>
    public interface IHelpDeskServiceRequestAPIService
    {
        /// <summary>
        /// Gets the service request query for help desk operations
        /// </summary>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable/disable exception logging</param>
        /// <param name="langID">Language ID</param>
        /// <param name="genLangCode">General language code</param>
        /// <param name="userLangCode">User language code</param>
        /// <param name="mode">Query mode (0=All, 1=My-Q, 2=Group-Q, 3=All-Q)</param>
        /// <param name="userId">User ID</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="branchId">Branch ID</param>
        /// <param name="sidx">Sort index</param>
        /// <param name="sord">Sort order</param>
        /// <param name="dbName">Database name</param>
        /// <returns>SQL query string for service requests</returns>
        Task<string> GetServiceRequestQueryAsync(string connectionString, int logException, int langID, 
            string genLangCode = "en", string userLangCode = "en", int mode = 0, int userId = 0, 
            int companyId = 0, int branchId = 0, string sidx = "", string sord = "", string dbName = "");
    }
}
