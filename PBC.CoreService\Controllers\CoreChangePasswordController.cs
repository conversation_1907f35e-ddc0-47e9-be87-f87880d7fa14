using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Services;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CoreChangePasswordController : ControllerBase
    {
        private readonly ICoreChangePasswordServices _coreChangePasswordServices;
        private readonly ILogger<CoreChangePasswordController> _logger;
        private readonly IConfiguration _configuration;

        public CoreChangePasswordController(
            ICoreChangePasswordServices coreChangePasswordServices, 
            ILogger<CoreChangePasswordController> logger,
            IConfiguration configuration)
        {
            _coreChangePasswordServices = coreChangePasswordServices;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Check if old password matches the stored password
        /// </summary>
        /// <param name="request">CheckOldPasswordList object</param>
        /// <returns>JsonResult with count (1 if password matches, 0 if not)</returns>
        /// <response code="200">Returns the password check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-old-password")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckOldPassword([FromBody] CheckOldPasswordRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/corechangepassword/check-old-password");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use connection string and log exception from gateway request
                var checkRequest = new CheckOldPasswordList
                {
                    User_ID = request.User_ID,
                    OldPassword = request.OldPassword
                };

                var result = await _coreChangePasswordServices.CheckOldPassword(checkRequest, request.ConnString, request.LogException);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking old password");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking old password");
            }
        }

        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="request">ChangePasswordList object with encrypted password data</param>
        /// <returns>Void - matches original implementation</returns>
        /// <response code="200">Password changed successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corechangepassword/change-password
        ///     {
        ///        "user_ID": 123,
        ///        "data": "{\"OldPassword\":\"encrypted_old_password_value\",\"NewPassword\":\"encrypted_new_password_value\"}",
        ///        "company_ID": 1,
        ///        "branch": 1,
        ///        "menuID": 10,
        ///        "loggedINDateTime": "2025-06-17T09:20:46.694Z"
        ///     }
        /// </remarks>
        [HttpPost("change-password")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task ChangePassword([FromBody] ChangePasswordRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/corechangepassword/change-password");

                // Use connection string and log exception from gateway request
                var changeRequest = new ChangePasswordList
                {
                    User_ID = request.User_ID,
                    data = request.data,
                    Company_ID = request.Company_ID,
                    Branch = request.Branch,
                    MenuID = request.MenuID,
                    LoggedINDateTime = request.LoggedINDateTime
                };

                await _coreChangePasswordServices.ChangePassword(changeRequest, request.ConnString, request.LogException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password");
                throw;
            }
        }
    }
}
