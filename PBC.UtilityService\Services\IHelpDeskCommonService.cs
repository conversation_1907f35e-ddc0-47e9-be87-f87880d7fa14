using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Interface for Help Desk Common Service
    /// </summary>
    public interface IHelpDeskCommonService
    {
        /// <summary>
        /// Gets the end step status ID for a workflow
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable/disable exception logging</param>
        /// <returns>End step status ID</returns>
        Task<int> GetEndStepStatusIDAsync(int workflowId, string connectionString, int logException);

        /// <summary>
        /// Gets the end step status name for a workflow
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable/disable exception logging</param>
        /// <returns>End step status name</returns>
        Task<string> GetEndStepStatusNameAsync(int workflowId, string connectionString, int logException);
    }
}
