using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Utilities;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Services
{
    public class PartsCommonService : IPartsCommonService
    {
        private readonly ILogger<PartsCommonService> _logger;

        public PartsCommonService(ILogger<PartsCommonService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<IActionResult> SaveBLAsync(Save_BLPartsInvoiceList obj, string connString, int logException, List<dynamic> jsonObj)
        {
            try
            {
                _logger.LogInformation("Processing Save BL request for Company_ID: {CompanyId}, User_ID: {UserId}", obj.Company_ID, obj.User_ID);
                
                await Task.Delay(1); // Simulate async operation
                
                // Call the static method from PartsCommon
                var result = PartsCommon.Save_BL(obj, connString, logException, jsonObj);
                
                _logger.LogInformation("Save BL request completed successfully");
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Save BL request for Company_ID: {CompanyId}", obj.Company_ID);
                throw;
            }
        }
    }
}
