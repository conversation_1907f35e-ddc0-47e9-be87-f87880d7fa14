﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;


namespace PBC.AggregatorService.Controllers
{
    
    [ApiController]
    [Route("api/[controller]")]
    public class SecurityController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly List<ApiClient> _allowedClients;


        public SecurityController(IConfiguration configuration)
        {
            _configuration = configuration;
            _allowedClients = _configuration.GetSection("AllowedClients").Get<List<ApiClient>>();

        }

        // [HttpPost("JWTGenOnLogin")]
        // [AllowAnonymous]
        // public IActionResult JWTGenOnLogin([FromBody] UserLoginModel model)
        // {
        //     var token = GenerateToken(model.Username);
        //     return Ok(new { token });
        // }

        [HttpPost("JWTGenOnLogin")]
        [AllowAnonymous]
        public IActionResult JWTGenOnLogin()
        {
            if (!Request.Headers.TryGetValue("X-API-Key", out var receivedApiKeyHeader))
            {
                return Unauthorized(new { message = "API Key header is missing." });
            }
            var receivedApiKey = receivedApiKeyHeader.ToString();

            var client = FindClientByApiKey(receivedApiKey);

            if (client == null)
            {
                return Unauthorized(new { message = "Invalid API Key." });
            }

            try
            {
                var token = GenerateToken(client.ClientName);
                return Ok(new { token });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An unexpected error occurred." });
            }
        }

        private ApiClient FindClientByApiKey(string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                return null;
            }

            var receivedApiKeyBytes = Encoding.UTF8.GetBytes(apiKey);

            foreach (var client in _allowedClients)
            {
                var expectedApiKeyBytes = Encoding.UTF8.GetBytes(client.ApiKey);
                if (CryptographicOperations.FixedTimeEquals(receivedApiKeyBytes, expectedApiKeyBytes))
                {
                    return client;
                }
            }

            return null;
        }


        /// <summary>
        /// DK - 24-Jun-2023 - Generates a JWT if a valid API Key is provided in the 'X-API-Key' request header token for a service client.
        /// </summary>
        /// <param name="clientName"></param>
        /// <returns></returns>
        private string GenerateToken(string username)
        {
            var secretKey = _configuration["Jwt:SecretKey"];
            var issuer = _configuration["Jwt:Issuer"];
            var audience = _configuration["Jwt:Audience"];
            var expiryMinutes = int.Parse(_configuration["Jwt:ExpiryMinutes"]);

            var securityKey = new SymmetricSecurityKey(Convert.FromBase64String(secretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, username ?? ""),
                new Claim(JwtRegisteredClaimNames.Iss, issuer),
                new Claim(JwtRegisteredClaimNames.Aud, audience)
            };

            var now = DateTime.UtcNow;
            var expires = now.AddMinutes(expiryMinutes);

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                notBefore: now,
                expires: expires,
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }

    public class UserLoginModel
    {
        public string ClientNameForToken { get; set; }
        // Add other properties as needed
    }
    public class ApiClient
    {
        public string ClientName { get; set; }
        public string ApiKey { get; set; }
    }
}