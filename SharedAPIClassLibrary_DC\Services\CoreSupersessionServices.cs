﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreSupersessionServices
    {

        #region ::: Select Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Select All Supersession 
        /// </summary>
        /// 

        public static IActionResult Select(string connstring, SelectCoreSupersessionList SelectCoreSupersessionobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Establish SQL connection
                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    conn.Open();

                    // Initialize variables
                    int Language_ID = Convert.ToInt32(SelectCoreSupersessionobj.UserLanguageID);
                    int GeneralLanguage_ID = Convert.ToInt32(SelectCoreSupersessionobj.GeneralLanguageID);
                    int Company_ID = Convert.ToInt32(SelectCoreSupersessionobj.Company_ID);
                    int Count = 0, Total = 0;
                    string YesE = CommonFunctionalities.GetResourceString(SelectCoreSupersessionobj.GeneralCulture.ToString(), "Yes").ToString();
                    string NoE = CommonFunctionalities.GetResourceString(SelectCoreSupersessionobj.GeneralCulture.ToString(), "No").ToString();
                    string YesL = CommonFunctionalities.GetResourceString(SelectCoreSupersessionobj.UserCulture.ToString(), "Yes").ToString();
                    string NoL = CommonFunctionalities.GetResourceString(SelectCoreSupersessionobj.UserCulture.ToString(), "No").ToString();

                    // Execute SP to get Supersession details
                    List<Supersession> SupersessionList = new List<Supersession>();
                    using (SqlCommand cmd = new SqlCommand("sp_GetSupersessionDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                        cmd.Parameters.AddWithValue("@GeneralLanguage_ID", GeneralLanguage_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SupersessionList.Add(new Supersession
                                {
                                    Supersession_ID = Convert.ToInt32(reader["Supersession_ID"]),
                                    Supersession_Type = reader["Supersession_Type"].ToString(),
                                    Consumption_Code = reader["Consumption_Code"].ToString(),
                                    Replacing_Code = reader["Replacing_Code"].ToString(),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Remark = reader["Remark"].ToString(),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Active = Convert.ToBoolean(reader["IsActive"]) ? YesL : NoL // Ensure that IsActive is properly retrieved
                                });
                            }
                        }
                    }

                    IQueryable<Supersession> iqSupersession = SupersessionList.AsQueryable();

                    // Sorting and filtering handled on the retrieved data (if applicable)
                    if (_search && !string.IsNullOrEmpty(filters))
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {
                            iqSupersession = iqSupersession.FilterSearch(filtersObj);
                        }
                    }
                    if (advnce && !string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        iqSupersession = iqSupersession.AdvanceSearch(advnfilter);
                        page = 1;
                    }

                    if (Count < (rows * page) && Count != 0)
                    {
                        page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                    }

                    // Sorting based on sidx and sord
                    iqSupersession = iqSupersession.OrderByField(sidx, sord);

                    // Pagination logic
                    Count = iqSupersession.Count();
                    Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                    var paginatedData = iqSupersession.Skip((page - 1) * rows).Take(rows).ToList();

                    jsonData = new
                    {
                        total = Total,
                        page = page,
                        rows = paginatedData.Select(a => new
                        {
                            edit = "<a title='Edit' href='#' id='" + a.Supersession_ID + "' key='" + a.Supersession_ID + "' class='EditSupersession' Company_ID='" + a.Company_ID + "' IsEditable='" + (a.Company_ID == Company_ID ? "1" : "0") + "'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            Supersession_Type = a.Supersession_Type,
                            Replacing_Code = a.Replacing_Code,
                            Consumption_Code = a.Consumption_Code,
                            Company_Name = a.Company_Name,
                            Active = a.Active,
                            Remark = a.Remark
                        }).ToList(),
                        records = Count
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }

        #endregion


        #region ::: Save Uday Kumar J B 18-07-2024:::
        /// <summary>
        /// To Save Supersession Details 
        /// </summary>

        public static IActionResult Save(string connString, SaveCoreSupersessionList SaveCoreSupersessionobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var response = new { Supersession_ID = 0 };
            try
            {
                GNM_Supersession Supersession = JObject.Parse(SaveCoreSupersessionobj.data).ToObject<GNM_Supersession>();
                int userId = Convert.ToInt32(SaveCoreSupersessionobj.User_ID);
                int companyId = Convert.ToInt32(SaveCoreSupersessionobj.Company_ID);
                int branch = Convert.ToInt32(SaveCoreSupersessionobj.Branch);
                int menuId = Convert.ToInt32(SaveCoreSupersessionobj.MenuID);
                DateTime loggedInDateTime = Convert.ToDateTime(SaveCoreSupersessionobj.LoggedINDateTime);
                DateTime now = DateTime.Now;

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    if (Supersession.Supersession_ID != 0)
                    {
                        using (var cmd = new SqlCommand("UpdateSupersession", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Supersession_ID", Supersession.Supersession_ID);
                            cmd.Parameters.AddWithValue("@ReplacingCode_ID", Supersession.ReplacingCode_ID);
                            cmd.Parameters.AddWithValue("@Remark", Uri.UnescapeDataString(Common.DecryptString(Supersession.Remark)));
                            cmd.Parameters.AddWithValue("@ModifiedBy", userId);
                            cmd.Parameters.AddWithValue("@ModifiedDate", now);
                            cmd.Parameters.AddWithValue("@IsActive", Supersession.IsActive);
                            cmd.ExecuteNonQuery();
                        }

                        response = new { Supersession.Supersession_ID };
                    }
                    else
                    {
                        int newSupersessionId;
                        using (var cmd = new SqlCommand("InsertSupersession", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@SupersessionType_ID", Supersession.SupersessionType_ID);
                            cmd.Parameters.AddWithValue("@ConsumptionCode_ID", Supersession.ConsumptionCode_ID);
                            cmd.Parameters.AddWithValue("@ReplacingCode_ID", Supersession.ReplacingCode_ID);
                            cmd.Parameters.AddWithValue("@Company_ID", Supersession.Company_ID);
                            cmd.Parameters.AddWithValue("@Remark", Uri.UnescapeDataString(Common.DecryptString(Supersession.Remark)));
                            cmd.Parameters.AddWithValue("@IsActive", Supersession.IsActive);
                            cmd.Parameters.AddWithValue("@ModifiedBy", userId);
                            cmd.Parameters.AddWithValue("@ModifiedDate", now);
                            cmd.Parameters.AddWithValue("@ReplacmentDate", now);
                            SqlParameter outputIdParam = new SqlParameter("@NewSupersession_ID", SqlDbType.Int)
                            {
                                Direction = ParameterDirection.Output
                            };
                            cmd.Parameters.Add(outputIdParam);
                            cmd.ExecuteNonQuery();
                            newSupersessionId = (int)outputIdParam.Value;
                        }

                        // Update GNM_Parts based on the new Supersession
                        UpdatePartsFrom(connection, Supersession.GNM_SupersessionFrom, newSupersessionId);
                        UpdatePartsTo(connection, Supersession.GNM_SupersessionTo, newSupersessionId);

                        response = new { Supersession_ID = newSupersessionId };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(response);
        }

        private static void UpdatePartsFrom(SqlConnection connection, ICollection<GNM_SupersessionFrom> supersessionParts, int supersessionId)
        {
            foreach (var part in supersessionParts)
            {
                UpdatePart(connection, part.Parts_ID, supersessionId, "Superceeding", part.Quantity);
            }
        }

        private static void UpdatePartsTo(SqlConnection connection, ICollection<GNM_SupersessionTo> supersessionParts, int supersessionId)
        {
            foreach (var part in supersessionParts)
            {
                UpdatePart(connection, part.Parts_ID, supersessionId, "Superceeded", part.Quantity);
            }
        }

        private static void UpdatePart(SqlConnection connection, int partsId, int supersessionId, string updateType, int Quantity)
        {
            using (var cmd = new SqlCommand("UpdateParts", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Parts_ID", partsId);
                cmd.Parameters.AddWithValue("@Supersession_ID", supersessionId);
                cmd.Parameters.AddWithValue("@UpdateType", updateType);
                cmd.Parameters.AddWithValue("@Quantity", Quantity);

                // Execute the stored procedure
                cmd.ExecuteNonQuery();
            }
        }

        #endregion


        #region ::: Export Supersession Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// Exporting Supersession  
        /// </summary>
        /// 

        public static async Task<object> Export(ExportCoreSupersessionList ExportCoreSupersessionobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                DataTable DtData = new DataTable();

                // Retrieve the data from SupersessionDataStore
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Initialize variables
                    int Language_ID = Convert.ToInt32(ExportCoreSupersessionobj.UserLanguageID);
                    int GeneralLanguage_ID = Convert.ToInt32(ExportCoreSupersessionobj.GeneralLanguageID);
                    int Company_ID = Convert.ToInt32(ExportCoreSupersessionobj.Company_ID);
                    string YesE = CommonFunctionalities.GetResourceString(ExportCoreSupersessionobj.GeneralCulture.ToString(), "Yes").ToString();
                    string NoE = CommonFunctionalities.GetResourceString(ExportCoreSupersessionobj.GeneralCulture.ToString(), "No").ToString();
                    string YesL = CommonFunctionalities.GetResourceString(ExportCoreSupersessionobj.UserCulture.ToString(), "Yes").ToString();
                    string NoL = CommonFunctionalities.GetResourceString(ExportCoreSupersessionobj.UserCulture.ToString(), "No").ToString();

                    // Execute SP to get Supersession details
                    List<Supersession> SupersessionList = new List<Supersession>();
                    using (SqlCommand cmd = new SqlCommand("sp_GetSupersessionDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                        cmd.Parameters.AddWithValue("@GeneralLanguage_ID", GeneralLanguage_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SupersessionList.Add(new Supersession
                                {
                                    Supersession_ID = Convert.ToInt32(reader["Supersession_ID"]),
                                    Supersession_Type = reader["Supersession_Type"].ToString(),
                                    Consumption_Code = reader["Consumption_Code"].ToString(),
                                    Replacing_Code = reader["Replacing_Code"].ToString(),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Remark = reader["Remark"].ToString(),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Active = Convert.ToBoolean(reader["IsActive"]) ? YesL : NoL // Ensure that IsActive is properly retrieved
                                });
                            }
                        }
                    }


                    var iqSupersession = SupersessionList.AsQueryable().OrderByField(sidx, sord);


                    // Apply standard filters if present
                    if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        if (filtersObj.rules.Count > 0)
                            iqSupersession = iqSupersession.FilterSearch(filtersObj);
                    }

                    // Apply advanced filters if present
                    if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                        iqSupersession = iqSupersession.AdvanceSearch(advnfilter);
                    }


                    // Convert IQueryable to List
                    List<Supersession> SupersessionArray = iqSupersession.ToList();

                    // Define columns in DataTable DtData
                    DtData.Columns.Add("Company_Name");
                    DtData.Columns.Add("Supersession_Type");
                    DtData.Columns.Add("Consumption_Code");
                    DtData.Columns.Add("Replacing_Code");
                    DtData.Columns.Add("Remark");
                    DtData.Columns.Add("Active");

                    // Populate DataTable DtData with Supersession data
                    foreach (var supersession in SupersessionArray)
                    {
                        DtData.Rows.Add(
                            supersession.Company_Name,
                            supersession.Supersession_Type,
                            supersession.Consumption_Code,
                            supersession.Replacing_Code,
                            supersession.Remark,
                            supersession.Active
                        );
                    }

                    // Prepare empty DataTables if needed (DtCriteria, DtAlignment)
                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("Company");
                    DtAlignment.Columns.Add("SupersessionType");
                    DtAlignment.Columns.Add("ConsumptionCode");
                    DtAlignment.Columns.Add("ReplacingCode");
                    DtAlignment.Columns.Add("Remarks");
                    DtAlignment.Columns.Add("Active");
                    DtAlignment.Rows.Add(0, 0, 0, 0, 0, 1);

                    ExportList reportExportList = new ExportList
                    {
                        Company_ID = ExportCoreSupersessionobj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportCoreSupersessionobj.Branch,
                        dt1 = DtAlignment,


                        dt = DtData,

                        FileName = "Supersession", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportCoreSupersessionobj.UserCulture.ToString(), "Supersession").ToString(), // Set a default or dynamic header
                        exprtType = ExportCoreSupersessionobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportCoreSupersessionobj.UserCulture
                    };

                    var result = await DocumentExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //return DocumentExport.Export(reportExportList, connString, LogException);
                }

                // Example resource object for column headers
                // string columnHeaderText = CommonFunctionalities.GetResourceString(ExportCoreSupersessionobj.UserCulture.ToString(), "Supersession").ToString();

                //ReportExport.Export(ExportCoreSupersessionobj.exprtType, DtData, DtCriteria, DtAlignment, "Supersession", columnHeaderText);

                // Log export details
                //gbl.InsertGPSDetails(Convert.ToInt32(ExportCoreSupersessionobj.Company_ID), Convert.ToInt32(ExportCoreSupersessionobj.Branch), User.User_ID, Common.GetObjectID("CoreSupersession"), 0, 0, 0, "Supersession-Export", false, Convert.ToInt32(ExportCoreSupersessionobj.MenuID), Convert.ToDateTime(ExportCoreSupersessionobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Handle exception and log it
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        #endregion


        #region ::: SelectSupersessionFromDetails  Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// SelectSupersessionFromDetails  
        /// </summary>
        /// 

        public static IActionResult SelectSupersessionFromDetails(string connString, SelectSupersessionFromDetailsList SelectSupersessionFromDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Initialize variables
                int Language_ID = Convert.ToInt32(SelectSupersessionFromDetailsobj.UserLanguageID);
                int GeneralLanguage_ID = Convert.ToInt32(SelectSupersessionFromDetailsobj.GeneralLanguageID);

                // DataTable to hold the result
                DataTable dt = new DataTable();

                // Fetch data using ADO.NET
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("GetSupersessionFromDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@SupersessionID", SelectSupersessionFromDetailsobj.SupersessionID);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);
                        command.Parameters.AddWithValue("@GeneralLanguage_ID", GeneralLanguage_ID);

                        // Execute command and fill DataTable
                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        adapter.Fill(dt);
                    }
                }

                // Convert DataTable to JSON format
                jsonData = new
                {
                    total = (rows > 0) ? Convert.ToInt32(Math.Ceiling((double)dt.Rows.Count / rows)) : 0,
                    page = page,
                    rows = (from DataRow dr in dt.Rows
                            select new
                            {
                                ID = Convert.ToInt32(dr["SupersessionDetails_ID"]),
                                edit = "<a title='View' href='#' id='" + dr["SupersessionDetails_ID"] + "'  key='" + dr["SupersessionDetails_ID"] + "' class='EditSupersessionFrom' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + dr["SupersessionDetails_ID"] + "' defaultchecked=''  id='chk" + dr["SupersessionDetails_ID"] + "' class='SupersessionFromDelete'  />",
                                Parts_ID = Convert.ToInt32(dr["Parts_ID"]),
                                Parts_Prefix = dr["Parts_Prefix"].ToString(),
                                Parts_Number = dr["Parts_Number"].ToString(),
                                Search = "",
                                Parts_Description = dr["Parts_Description"].ToString(),
                                Quantity = Convert.ToInt32(dr["Quantity"])
                            }).ToList().Paginate(page, rows),
                    records = dt.Rows.Count
                };
            }
            catch (Exception ex)
            {
                // Log exception if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectSupersessionToDetails Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// SelectSupersessionToDetails   
        /// </summary>
        /// 

        public static IActionResult SelectSupersessionToDetails(string connString, SelectSupersessionToDetailsList SelectSupersessionToDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            string AppPath = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectSupersessionToDetailsobj.UserLanguageID);
                int GeneralLanguage_ID = Convert.ToInt32(SelectSupersessionToDetailsobj.GeneralLanguageID);
                List<object> rowsList = new List<object>();
                int totalCount = 0;
                int totalPages = 0;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("GetSupersessionToDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@SupersessionID", SelectSupersessionToDetailsobj.SupersessionID);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);
                        command.Parameters.AddWithValue("@GeneralLanguage_ID", GeneralLanguage_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // Handle SupersessionDetails_ID, default to 0 if null
                                int supersessionDetailsID = reader.IsDBNull(reader.GetOrdinal("SupersessionDetails_ID"))
                                    ? 0
                                    : reader.GetInt32(reader.GetOrdinal("SupersessionDetails_ID"));

                                // Handle Parts_ID, default to 0 if null
                                int partsID = reader.IsDBNull(reader.GetOrdinal("Parts_ID"))
                                    ? 0
                                    : reader.GetInt32(reader.GetOrdinal("Parts_ID"));

                                // Handle Parts_Prefix, default to empty string if null
                                string partsPrefix = reader.IsDBNull(reader.GetOrdinal("Parts_Prefix"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Parts_Prefix"));

                                // Handle Parts_Number, default to empty string if null
                                string partsNumber = reader.IsDBNull(reader.GetOrdinal("Parts_Number"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Parts_Number"));

                                // Handle Parts_Description, default to empty string if null
                                string partsDescription = reader.IsDBNull(reader.GetOrdinal("Parts_Description"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Parts_Description"));

                                // Handle Quantity, default to 0 if null
                                int quantity = reader.IsDBNull(reader.GetOrdinal("Quantity"))
                                    ? 0
                                    : reader.GetInt32(reader.GetOrdinal("Quantity"));

                                // Add to rowsList with null checks
                                rowsList.Add(new
                                {
                                    ID = supersessionDetailsID,
                                    edit = $"<img id='{supersessionDetailsID}' src='{AppPath}/Content/edit.gif' key='{supersessionDetailsID}' class='EditSupersessionTo' editmode='false' />",
                                    delete = $"<input type='checkbox' key='{supersessionDetailsID}' defaultchecked='' id='chk{supersessionDetailsID}' class='SupersessionToDelete' />",
                                    Parts_ID = partsID,
                                    Parts_Prefix = partsPrefix,
                                    Parts_Number = partsNumber,
                                    Search = "",
                                    Parts_Description = partsDescription,
                                    Quantity = quantity
                                });
                            }
                        }
                    }
                }

                totalCount = rowsList.Count;
                totalPages = rows > 0 ? (int)Math.Ceiling((double)totalCount / rows) : 0;

                // Prepare JSON data to return
                jsonData = new
                {
                    total = totalPages,
                    page = page,
                    rows = rowsList.Skip((page - 1) * rows).Take(rows).ToList(),
                    records = totalCount
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // Return JSON response
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectMultiplePartPrefix   Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// SelectMultiplePartPrefix
        /// </summary>

        public static IActionResult SelectMultiplePartPrefix(string connString, SelectMultiplePartPrefixList SelectMultiplePartPrefixobj)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string Part_Number = Common.DecryptString(SelectMultiplePartPrefixobj.Part_Number);
                int Company_ID = Convert.ToInt32(SelectMultiplePartPrefixobj.Company_ID);

                List<object> resultList = new List<object>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create SqlCommand for stored procedure
                    using (SqlCommand command = new SqlCommand("SelectPartsByPartNumberAndCompany", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartNumber", Part_Number);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int partsId = Convert.ToInt32(reader["Parts_ID"]);
                                string partPrefix = Convert.ToString(reader["Parts_PartPrefix"]);
                                string partsNumber = Convert.ToString(reader["Parts_PartsNumber"]);
                                string partsDescription = Convert.ToString(reader["Parts_PartsDescription"]);

                                resultList.Add(new
                                {
                                    Select = $"<a title='Select' href='#' style='font-size: 13px;' id='{partsId}' PartsDescription='{partsDescription}' PartID='{partsId}' PartNumber='{partsNumber}' PartPrefix='{partPrefix}' class='PrefixPopUp' style='cursor:pointer'><i class='fa fa-check'></i></a>",
                                    PartPrefix = partPrefix,
                                    PartsNumber = partsNumber,
                                    PartsDescription = partsDescription
                                });
                            }
                        }
                    }
                }

                if (resultList.Count >= 1)
                {
                    return new JsonResult(new
                    {
                        IsMultiPrefixPart = true,
                        PartDetail = resultList
                    });
                }
                else if (resultList.Count == 0)
                {
                    var firstResult = (dynamic)resultList.FirstOrDefault();
                    if (firstResult != null)
                    {
                        jsonData = new
                        {
                            IsMultiPrefixPart = false,
                            IsValidPart = true,
                            Parts_ID = firstResult.PartID, // Ensure this matches the property name in your LINQ or stored procedure result
                            PartNumber = firstResult.PartsNumber,
                            PartPrefix = firstResult.PartPrefix
                        };
                    }
                    else
                    {
                        jsonData = new
                        {
                            IsMultiPrefixPart = false,
                            IsValidPart = false,
                            PartNumber = Part_Number,
                            Parts_ID = 0
                        };
                    }
                }
                else
                {
                    jsonData = new
                    {
                        IsMultiPrefixPart = false,
                        IsValidPart = false,
                        PartNumber = Part_Number,
                        Parts_ID = 0
                    };
                }
            }
            catch (Exception ex)
            {
                // Log the exception or handle it as needed
                jsonData = new
                {
                    IsMultiPrefixPart = false,
                    IsValidPart = false,
                    PartNumber = SelectMultiplePartPrefixobj.Part_Number,
                    Parts_ID = 0
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: GetFromPartDetails Uday Kumar J B 18-07-2024  :::
        /// <summary>
        /// To Select Part Number Details           
        /// </summary>
        /// 


        public static IActionResult GetFromPartDetails(string connString, GetFromPartDetailsList GetFromPartDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = new object();
            try
            {
                bool IsValidPart = false;
                bool IsExists = false;

                var queryDictionary = System.Web.HttpUtility.ParseQueryString(GetFromPartDetailsobj.extra);

                // Extract values from the parsed query string
                string idValue = queryDictionary["ID"];
                string keyValue = queryDictionary["Key"];
                string valueValue = queryDictionary["Value"];
                string modeValue = queryDictionary["mode"];

                // Convert and assign to variables
                int SupersessionID = Convert.ToInt32(idValue);
                int PartID = Convert.ToInt32(keyValue);
                string PartNumber = Common.DecryptString(valueValue);
                string mode = modeValue;
                int Company_ID = Convert.ToInt32(GetFromPartDetailsobj.Company_ID);

                // Initialize variables for data retrieval
                List<GNM_Parts> parentPartsList = new List<GNM_Parts>();
                GNM_Parts partsList = null;
                GNM_PartsLocale PartLocaleDetails = null;

                // Call stored procedure to fetch parts list
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string storedProcedure = PartID == 0 ? "GetPartsByPartNumberTO" : "GetPartsByIDTO";
                    SqlParameter[] parameters = PartID == 0 ? new SqlParameter[]
                    {
                new SqlParameter("@PartNumber", PartNumber),
                new SqlParameter("@Company_ID", Company_ID)
                    } :
                    new SqlParameter[]
                    {
                new SqlParameter("@PartID", PartID),
                new SqlParameter("@Company_ID", Company_ID)
                    };

                    using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddRange(parameters);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                parentPartsList.Add(new GNM_Parts
                                {
                                    Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                    Parts_PartsNumber = reader.GetString(reader.GetOrdinal("Parts_PartsNumber")),
                                    Parts_PartPrefix = reader.GetString(reader.GetOrdinal("Parts_PartPrefix")),
                                    Parts_PartsDescription = reader.GetString(reader.GetOrdinal("Parts_PartsDescription"))
                                });
                            }
                        }
                    }
                }

                // Process results based on PartID and parentPartsList count
                if (PartID != 0)
                {
                    partsList = parentPartsList.FirstOrDefault(p => p.Parts_ID == PartID);
                }
                else
                {
                    if (parentPartsList.Count > 0)
                    {
                        if (parentPartsList.Count == 1)
                        {
                            partsList = parentPartsList.FirstOrDefault(p => p.Parts_PartsNumber.ToUpper().Contains(PartNumber.ToUpper()));
                        }
                        else
                        {
                            jsonData = new
                            {
                                Count = parentPartsList.Count(),
                                Name = parentPartsList.Count > 0 ? PartNumber : ""
                            };
                            return new JsonResult(jsonData);
                        }
                    }
                }

                // Validate and prepare JSON response based on partsList
                if (partsList != null)
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();

                        if (mode == "add")
                        {
                            using (SqlCommand cmd = new SqlCommand("CheckPartExistsInSupersessionAdd", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@SupersessionID", SupersessionID);
                                cmd.Parameters.AddWithValue("@PartID", PartID);
                                IsExists = (int)cmd.ExecuteScalar() > 0;
                            }
                        }
                        else if (mode == "edit")
                        {
                            int PrmID = Convert.ToInt32(GetFromPartDetailsobj.primID);
                            using (SqlCommand cmd = new SqlCommand("CheckPartExistsInSupersessionEdit", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@SupersessionID", SupersessionID);
                                cmd.Parameters.AddWithValue("@PartID", PartID);
                                cmd.Parameters.AddWithValue("@PrmID", PrmID);
                                IsExists = (int)cmd.ExecuteScalar() > 0;
                            }
                        }

                        // Construct JSON data based on session language comparison
                        if (GetFromPartDetailsobj.GeneralLanguageID.ToString() == GetFromPartDetailsobj.UserLanguageID.ToString())
                        {
                            jsonData = new
                            {
                                Parts_ID = partsList.Parts_ID,
                                Name = partsList.Parts_PartsNumber,
                                Parts_Prefix = partsList.Parts_PartPrefix,
                                Parts_Description = partsList.Parts_PartsDescription,
                                IsExists,
                                IsValidPart,
                                Count = 1
                            };
                        }
                        else
                        {
                            // Fetch localized part details if different languages are used
                            using (SqlCommand cmd = new SqlCommand("GetPartsLocaleDetailsTO", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddRange(new SqlParameter[]
                                {
                            new SqlParameter("@PartID", partsList.Parts_ID),
                            new SqlParameter("@Company_ID", Company_ID)
                                });
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        PartLocaleDetails = new GNM_PartsLocale
                                        {
                                            Parts_PartsDescription = reader.GetString(reader.GetOrdinal("Parts_PartsDescription"))
                                        };
                                    }
                                }
                            }

                            IsValidPart = true; // Assuming partsList != null implies valid part

                            jsonData = new
                            {
                                Parts_ID = partsList.Parts_ID,
                                Name = partsList.Parts_PartsNumber,
                                Parts_Prefix = partsList.Parts_PartPrefix,
                                Parts_Description = PartLocaleDetails != null ? PartLocaleDetails.Parts_PartsDescription : "",
                                IsExists,
                                IsValidPart,
                                Count = 1
                            };
                        }
                    }
                }
                else
                {
                    jsonData = new
                    {
                        Parts_ID = 0,
                        Name = "",
                        Parts_Prefix = "",
                        Parts_Description = "",
                        IsExists = false,
                        IsValidPart = false,
                        Count = 0
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // Return JSON data to client
            return new JsonResult(jsonData);
        }

        #endregion


        #region ::: GetToPartDetails  Uday Kumar J B 18-07-2024:::
        /// <summary>
        /// To Select Part Number Details        
        /// </summary>
        /// 

        public static IActionResult GetToPartDetails(string connString, GetToPartDetailsList GetToPartDetailsobj)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var queryDictionary = System.Web.HttpUtility.ParseQueryString(GetToPartDetailsobj.extra);

                // Now, extract the values from the parsed query string
                string idValue = queryDictionary["ID"];
                string keyValue = queryDictionary["Key"];
                string valueValue = queryDictionary["Value"];
                string modeValue = queryDictionary["mode"];
                // Initialize necessary variables
                int SupersessionID = Convert.ToInt32(idValue);
                int PartID = Convert.ToInt32(keyValue);
                string PartNumber = Common.DecryptString(valueValue);
                string mode = modeValue;
                int Company_ID = Convert.ToInt32(GetToPartDetailsobj.Company_ID);
                int UserLanguageID = Convert.ToInt32(GetToPartDetailsobj.UserLanguageID);
                int GeneralLanguageID = Convert.ToInt32(GetToPartDetailsobj.GeneralLanguageID);

                // Initialize SQL connection and command
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand();
                    command.Connection = connection;
                    command.CommandType = CommandType.StoredProcedure;

                    // Retrieve parts based on PartID or PartNumber
                    if (PartID == 0)
                    {
                        command.CommandText = "GetPartsByPartNumber";
                        command.Parameters.AddWithValue("@PartNumber", PartNumber);
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    }
                    else
                    {
                        command.CommandText = "GetPartsByID";
                        command.Parameters.AddWithValue("@PartID", PartID);
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    }

                    // Execute the stored procedure to fetch parts data
                    SqlDataReader reader = command.ExecuteReader();
                    List<GNM_Parts> parentPartsList = new List<GNM_Parts>();

                    while (reader.Read())
                    {
                        GNM_Parts part = new GNM_Parts();
                        part.Parts_ID = Convert.ToInt32(reader["Parts_ID"]);
                        part.Parts_PartsNumber = Convert.ToString(reader["Parts_PartsNumber"]);
                        part.Parts_PartPrefix = Convert.ToString(reader["Parts_PartPrefix"]);
                        part.Parts_PartsDescription = Convert.ToString(reader["Parts_PartsDescription"]);
                        parentPartsList.Add(part);
                    }
                    reader.Close();

                    GNM_Parts partsList = null;
                    bool IsExists = false;

                    // Determine the appropriate action based on the retrieved parts
                    if (PartID != 0)
                    {
                        partsList = parentPartsList.FirstOrDefault(a => a.Parts_ID == PartID);
                    }
                    else
                    {
                        if (parentPartsList.Count > 0)
                        {
                            if (parentPartsList.Count == 1)
                            {
                                partsList = parentPartsList.FirstOrDefault(a => a.Parts_PartsNumber.ToUpper().Contains(PartNumber.ToUpper()));
                            }
                            else
                            {
                                jsonData = new
                                {
                                    Count = parentPartsList.Count(),
                                    Name = parentPartsList.Count() > 0 ? PartNumber : ""
                                };
                                return new JsonResult(jsonData);
                            }
                        }
                    }

                    // Check existence of the part in supersession table if necessary
                    if (partsList != null)
                    {
                        if (mode == "add")
                        {
                            command.CommandText = "CheckSupersessionExistsForAdd";
                            command.Parameters.Clear();
                            command.Parameters.AddWithValue("@SupersessionID", SupersessionID);
                            command.Parameters.AddWithValue("@PartID", PartID);
                            IsExists = Convert.ToInt32(command.ExecuteScalar()) > 0;
                        }
                        else if (mode == "edit")
                        {
                            int PrmID = Convert.ToInt32(GetToPartDetailsobj.primID);
                            command.CommandText = "CheckSupersessionExistsForEdit";
                            command.Parameters.Clear();
                            command.Parameters.AddWithValue("@SupersessionID", SupersessionID);
                            command.Parameters.AddWithValue("@PartID", PartID);
                            command.Parameters.AddWithValue("@PrmID", PrmID);
                            IsExists = Convert.ToInt32(command.ExecuteScalar()) > 0;
                        }

                        // Construct JSON data based on session language
                        if (UserLanguageID == GeneralLanguageID)
                        {
                            jsonData = new
                            {
                                Parts_ID = (partsList == null ? 0 : partsList.Parts_ID),
                                Name = (partsList == null ? "" : partsList.Parts_PartsNumber),
                                Parts_Prefix = (partsList == null ? "" : partsList.Parts_PartPrefix),
                                Parts_Description = (partsList == null ? "" : partsList.Parts_PartsDescription),
                                IsExists = IsExists,
                                Count = 1
                            };
                        }
                        else
                        {
                            command.CommandText = "GetPartsLocaleDetails";
                            command.Parameters.Clear();
                            command.Parameters.AddWithValue("@PartID", PartID);
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);
                            SqlDataReader localeReader = command.ExecuteReader();

                            GNM_PartsLocale PartLocaleDetails = null;
                            if (localeReader.Read())
                            {
                                PartLocaleDetails = new GNM_PartsLocale();
                                PartLocaleDetails.Language_ID = Convert.ToInt32(localeReader["Language_ID"]);
                                // Populate other properties as needed
                            }
                            localeReader.Close();

                            jsonData = new
                            {
                                Parts_ID = (partsList == null ? 0 : partsList.Parts_ID),
                                Name = (partsList == null ? "" : partsList.Parts_PartsNumber),
                                Parts_Prefix = (partsList == null ? "" : partsList.Parts_PartPrefix),
                                Parts_Description = (PartLocaleDetails == null ? "" : ""),
                                IsExists = IsExists,
                                Count = 1
                            };
                        }
                    }
                    else
                    {
                        jsonData = new
                        {
                            Parts_ID = 0,
                            Name = "",
                            Parts_Prefix = "",
                            Parts_Description = "",
                            IsExists = false,
                            Count = 0
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception or handle it as needed
                jsonData = new
                {
                    Parts_ID = 0,
                    Name = "",
                    Parts_Prefix = "",
                    Parts_Description = "",
                    IsExists = false,
                    Count = 0
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Select Refrence Master records       
        /// </summary> 
        /// 
        public static IActionResult SelectReferenceMaster(string connString, SelectReferenceMasterCoreSupersessionList SelectReferenceMasterCoreSupersessionobj)
        {
            List<object> Masterdata = new List<object>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Log the session variables
                int Language_ID = Convert.ToInt32(SelectReferenceMasterCoreSupersessionobj.UserLanguageID);
                int GeneralLanguage_ID = Convert.ToInt32(SelectReferenceMasterCoreSupersessionobj.GeneralLanguageID);
                if (Language_ID == 0 || GeneralLanguage_ID == 0)
                {
                    throw new Exception("Session variables 'UserLanguageID' or 'GeneralLanguageID' are not set.");
                }

                int RefMasterID = 0;
                bool IsCompanySpecific = false;
                DataTable refMasterDetailTable = new DataTable();
                DataTable refMasterDetailLocaleTable = new DataTable();

                // Fetch ReferenceMaster details using ADO.NET
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("GetReferenceMasterDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ReferenceMasterName", SelectReferenceMasterCoreSupersessionobj.ReferenceMasterName);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                RefMasterID = Convert.ToInt32(reader["RefMaster_ID"]);
                                IsCompanySpecific = Convert.ToBoolean(reader["IsCompanySpecific"]);
                            }
                        }
                    }
                }

                if (RefMasterID == 0)
                {
                    throw new Exception("ReferenceMasterID not found.");
                }

                // Fetch ReferenceMasterDetail using ADO.NET
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(IsCompanySpecific ? "GetReferenceMasterDetailByCompany" : "GetReferenceMasterDetail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                        if (IsCompanySpecific)
                        {
                            command.Parameters.AddWithValue("@CompanyID", SelectReferenceMasterCoreSupersessionobj.CompanyID);
                        }
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(refMasterDetailTable);
                        }
                    }
                }

                // Fetch ReferenceMasterDetailLocale if needed using ADO.NET
                if (Language_ID != GeneralLanguage_ID)
                {
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand("GetReferenceMasterDetailLocale", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            command.Parameters.AddWithValue("@LanguageID", Language_ID);
                            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                            {
                                adapter.Fill(refMasterDetailLocaleTable);
                            }
                        }
                    }
                }

                // Build Masterdata object based on ADO.NET data
                if (Language_ID == GeneralLanguage_ID)
                {
                    Masterdata = refMasterDetailTable.AsEnumerable()
                        .OrderBy(row => row["RefMasterDetail_Name"])
                        .Select(row => new
                        {
                            ID = Convert.ToInt32(row["RefMasterDetail_ID"]),
                            Name = row["RefMasterDetail_Name"] != DBNull.Value ? row["RefMasterDetail_Name"].ToString() : "None"
                        }).ToList<object>();
                }
                else
                {
                    Masterdata = (from detail in refMasterDetailTable.AsEnumerable()
                                  join locale in refMasterDetailLocaleTable.AsEnumerable()
                                  on detail["RefMasterDetail_ID"] equals locale["RefMasterDetail_ID"]
                                  orderby locale["RefMasterDetail_Name"]
                                  select new
                                  {
                                      ID = Convert.ToInt32(locale["RefMasterDetail_ID"]),
                                      Name = locale["RefMasterDetail_Name"] != DBNull.Value ? locale["RefMasterDetail_Name"].ToString() : "None"
                                  }).ToList<object>();
                }

                // Build session variables based on ADO.NET data
                DataRow[] selectedDetailRows = refMasterDetailTable.Select($"RefMasterDetail_ID = {SelectReferenceMasterCoreSupersessionobj.RefMasterDetail_ID}");
                //if (selectedDetailRows.Length > 0)
                //{
                //    string detailName = selectedDetailRows[0]["RefMasterDetail_Name"] != DBNull.Value ? selectedDetailRows[0]["RefMasterDetail_Name"].ToString() : "None";
                //    if (SelectReferenceMasterCoreSupersessionobj.ReferenceMasterName == "CONSUMPTIONCODE")
                //    {
                //        SelectReferenceMasterCoreSupersessionobj.CurrentConsumptionCode = detailName;
                //    }
                //    else if (SelectReferenceMasterCoreSupersessionobj.ReferenceMasterName == "REPLACINGCODE")
                //    {
                //        SelectReferenceMasterCoreSupersessionobj.CurrentReplacingCode = detailName;
                //    }
                //    else if (SelectReferenceMasterCoreSupersessionobj.ReferenceMasterName == "SUPERSESSIONTYPE")
                //    {
                //        SelectReferenceMasterCoreSupersessionobj.CurrentSupersessionType = detailName;
                //    }
                //}
                //else
                //{
                //    throw new Exception("ReferenceMasterDetail not found.");
                //}

                // Log the data being sent to the client for debugging
                var result = new { ReferenceMasterData = Masterdata };

                // Return JSON result with Masterdata
                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                // Log exception if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: SelectParticularSupersession  Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To get Model master data       
        /// </summary> 
        ///

        public static IActionResult SelectParticularSupersession(string connString, SelectParticularSupersessionList SelectParticularSupersessionobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Initialize variables
                int languageID = Convert.ToInt32(SelectParticularSupersessionobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectParticularSupersessionobj.GeneralLanguageID);

                // DataTables to hold results
                DataTable supersessionTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Retrieve supersession details
                    using (SqlCommand command = new SqlCommand("GetSupersessionByID", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@SupersessionID", SelectParticularSupersessionobj.SupersessionID);
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(supersessionTable);
                        }
                    }
                }

                if (supersessionTable.Rows.Count == 0)
                {
                    return new JsonResult(new { Error = "Supersession not found" });
                }

                DataRow supersessionRow = supersessionTable.Rows[0];

                // Extract IDs from the supersession row
                int companyID = Convert.ToInt32(supersessionRow["Company_ID"]);
                int consumptionCodeID = Convert.ToInt32(supersessionRow["ConsumptionCode_ID"]);
                int replacingCodeID = Convert.ToInt32(supersessionRow["ReplacingCode_ID"]);
                int supersessionTypeID = Convert.ToInt32(supersessionRow["SupersessionType_ID"]);

                // Get reference master data for dropdowns
                var consumptionCodeData = GetReferenceMasterDatas(connString, "CONSUMPTIONCODE", companyID, consumptionCodeID, languageID, generalLanguageID);
                var replacingCodeData = GetReferenceMasterDatas(connString, "REPLACINGCODE", companyID, replacingCodeID, languageID, generalLanguageID);
                var supersessionTypeData = GetReferenceMasterDatas(connString, "SUPERSESSIONTYPE", companyID, supersessionTypeID, languageID, generalLanguageID);

                // Check if any data retrieval failed
                if (consumptionCodeData == null || replacingCodeData == null || supersessionTypeData == null)
                {
                    throw new Exception("One or more JSON results from GetReferenceMasterDatas are null.");
                }

                // Set the Current* properties based on the retrieved data
                SelectParticularSupersessionobj.CurrentConsumptionCode = consumptionCodeData.DetailName;
                SelectParticularSupersessionobj.CurrentReplacingCode = replacingCodeData.DetailName;
                SelectParticularSupersessionobj.CurrentSupersessionType = supersessionTypeData.DetailName;

                // Prepare JSON result
                var result = new
                {
                    Supersession_ID = supersessionRow["Supersession_ID"],
                    ConsumptionCode_ID = consumptionCodeID,
                    IsActive = Convert.ToBoolean(supersessionRow["IsActive"]),
                    ReplacingCode_ID = replacingCodeID,
                    SupersessionType_ID = supersessionTypeID,
                    ConsumptionCode = consumptionCodeData.ReferenceMasterData,
                    ReplacingCode = replacingCodeData.ReferenceMasterData,
                    SupersessionType = supersessionTypeData.ReferenceMasterData,
                    Remark = supersessionRow["Remark"].ToString(),
                    CurrentConsumptionCode = SelectParticularSupersessionobj.CurrentConsumptionCode,
                    CurrentReplacingCode = SelectParticularSupersessionobj.CurrentReplacingCode,
                    CurrentSupersessionType = SelectParticularSupersessionobj.CurrentSupersessionType
                };

                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = ex.Message });
            }
        }


        private static dynamic GetReferenceMasterDatas(string connString, string referenceMasterName, int companyID, int referenceMasterDetailID, int languageID, int generalLanguageID)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                DataTable refMasterDetailTable = new DataTable();
                DataTable refMasterDetailLocaleTable = new DataTable();

                int refMasterID = 0;
                bool isCompanySpecific = false;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch the Reference Master details
                    using (SqlCommand command = new SqlCommand("GetReferenceMasterDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ReferenceMasterName", referenceMasterName);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                refMasterID = Convert.ToInt32(reader["RefMaster_ID"]);
                                isCompanySpecific = Convert.ToBoolean(reader["IsCompanySpecific"]);
                            }
                        }
                    }

                    if (refMasterID == 0)
                    {
                        throw new Exception("ReferenceMasterID not found.");
                    }

                    // Fetch the reference master details based on whether it's company-specific
                    using (SqlCommand command = new SqlCommand(isCompanySpecific ? "GetReferenceMasterDetailByCompany" : "GetReferenceMasterDetail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", refMasterID);
                        if (isCompanySpecific)
                        {
                            command.Parameters.AddWithValue("@CompanyID", companyID);
                        }
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(refMasterDetailTable);
                        }
                    }

                    // Fetch locale-specific data if the language is different
                    if (languageID != generalLanguageID)
                    {
                        using (SqlCommand command = new SqlCommand("GetReferenceMasterDetailLocale", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterID", refMasterID);
                            command.Parameters.AddWithValue("@LanguageID", languageID);
                            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                            {
                                adapter.Fill(refMasterDetailLocaleTable);
                            }
                        }
                    }
                }

                // Prepare the data for the dropdown list
                var referenceMasterData = languageID == generalLanguageID ?
                    refMasterDetailTable.AsEnumerable()
                        .OrderBy(row => row["RefMasterDetail_Name"])
                        .Select(row => new
                        {
                            ID = Convert.ToInt32(row["RefMasterDetail_ID"]),
                            Name = row["RefMasterDetail_Name"] != DBNull.Value ? row["RefMasterDetail_Name"].ToString() : "None"
                        }).ToList() :
                    (from detail in refMasterDetailTable.AsEnumerable()
                     join locale in refMasterDetailLocaleTable.AsEnumerable()
                     on detail["RefMasterDetail_ID"] equals locale["RefMasterDetail_ID"]
                     orderby locale["RefMasterDetail_Name"]
                     select new
                     {
                         ID = Convert.ToInt32(locale["RefMasterDetail_ID"]),
                         Name = locale["RefMasterDetail_Name"] != DBNull.Value ? locale["RefMasterDetail_Name"].ToString() : "None"
                     }).ToList();

                // Find the detail name for the selected referenceMasterDetailID
                string detailName = referenceMasterData
                    .Where(data => data.ID == referenceMasterDetailID)
                    .Select(data => data.Name)
                    .FirstOrDefault() ?? "None";

                return new
                {
                    ReferenceMasterData = referenceMasterData,
                    DetailName = detailName
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
        }


        #endregion


        #region ::: InitializeSupersessionType Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Initialize Supersession Type     
        /// </summary> 
        /// 

        public static IActionResult InitializeSupersessionType(string connString, InitializeSupersessionTypeList InitializeSupersessionTypeobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //ReferenceClient = new ReferenceMasterEntities();
                int ReferenceMasterID = 0;

                // Retrieve ReferenceMasterID for "SUPERSESSIONTYPE" using stored procedure
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("GetReferenceMasterID", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    if (reader.Read())
                    {
                        ReferenceMasterID = Convert.ToInt32(reader["RefMaster_ID"]);
                    }
                    reader.Close();
                }

                // Initialize session variables based on ReferenceMasterID and Short Names
                string[] shortNames = { "One to One", "One to Many", "Many to One", "Many to Many" };
                string[] sessionKeys = { "One2One", "One2Many", "Many2One", "Many2Many" };

                for (int i = 0; i < shortNames.Length; i++)
                {
                    string shortName = shortNames[i].ToUpper();
                    string sessionKey = sessionKeys[i];

                    // Retrieve RefMasterDetail_IDs using stored procedure
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        SqlCommand command = new SqlCommand("GetRefMasterDetailID", connection);
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@RefMasterID", ReferenceMasterID);
                        command.Parameters.AddWithValue("@ShortName", shortName);
                        connection.Open();
                        SqlDataReader reader = command.ExecuteReader();

                        if (reader.Read())
                        {
                            InitializeSupersessionTypeobj.sessionKey = Convert.ToInt32(reader["RefMasterDetail_ID"]);
                        }
                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle or log the exception as needed
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: CheckPartsForSupersessionFrom Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Check Parts For Supersession From        
        /// </summary>
        /// 
        public static IActionResult CheckPartsForSupersessionFrom(string connString, CheckPartsForSupersessionFromList CheckPartsForSupersessionFromobj)
        {
            int count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("CheckPartsForSupersessionFrom", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@PartsID", CheckPartsForSupersessionFromobj.Parts_ID);

                    connection.Open();
                    count = (int)command.ExecuteScalar();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                count = 0; // Reset count in case of exception
            }
            return new JsonResult(count);
        }
        #endregion


        #region ::: CheckPartsForSupersessionTo Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Check Part For Supersession To   
        /// </summary>
        /// 
        public static IActionResult CheckPartsForSupersessionTo(string connString, CheckPartsForSupersessionToList CheckPartsForSupersessionToobj)
        {
            int count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("CheckPartsForSupersessionTo", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@PartsID", CheckPartsForSupersessionToobj.Parts_ID);

                    connection.Open();
                    count = (int)command.ExecuteScalar();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                count = 0; // Reset count in case of exception
            }
            return new JsonResult(count);
        }
        #endregion


        #region ::: For CoreSupersession obj and List classes Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Check Part For Supersession To   
        /// </summary>
        /// 
        public class CheckPartsForSupersessionToList
        {
            public int Parts_ID { get; set; }

        }
        public class CheckPartsForSupersessionFromList
        {
            public int Parts_ID { get; set; }

        }

        public class InitializeSupersessionTypeList
        {
            public int sessionKey { get; set; }

        }
        public class SelectParticularSupersessionList
        {
            public int SupersessionID { get; set; }

            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }

            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string CurrentConsumptionCode { get; set; }

            public string CurrentReplacingCode { get; set; }
            public string CurrentSupersessionType { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }

        }
        public class SelectReferenceMasterCoreSupersessionList
        {
            public string ReferenceMasterName { get; set; }
            public int CompanyID { get; set; }
            public int RefMasterDetail_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public string CurrentConsumptionCode { get; set; }

            public string CurrentReplacingCode { get; set; }
            public string CurrentSupersessionType { get; set; }

        }
        public class GetToPartDetailsList
        {
            public int ID { get; set; }
            public int Key { get; set; }
            public string Value { get; set; }
            public string mode { get; set; }
            public int Company_ID { get; set; }
            public int primID { get; set; }
            public string extra { get; set; }

            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }

        }



        public class GetFromPartDetailsList
        {
            public int ID { get; set; }
            public int Key { get; set; }
            public string Value { get; set; }
            public string mode { get; set; }
            public string extra { get; set; }
            public int Company_ID { get; set; }
            public int primID { get; set; }

            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }

        }

        public class SelectMultiplePartPrefixList
        {
            public string Part_Number { get; set; }
            public int Company_ID { get; set; }
        }

        public class SelectSupersessionToDetailsList
        {
            public int SupersessionID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }

        public class SelectSupersessionFromDetailsList
        {
            public int SupersessionID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }

        }

        public class ExportCoreSupersessionList
        {
            public int exprtType { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }

            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string GeneralCulture { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }

        public static class SupersessionDataStore
        {
            public static List<Supersession> Supersessions { get; set; }
        }


        public class SaveCoreSupersessionList
        {
            public string data { get; set; }

            public List<GNM_User> UserDetails { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }

            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }


        }

        public class SelectCoreSupersessionList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public string Company_Name { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }

        }

        #endregion


        #region ::: CoreSupersession Class 18-07-2024 :::
        /// <summary>
        /// CoreSupersession class
        /// </summary>
        /// 
        public partial class GNM_Supersession
        {
            public GNM_Supersession()
            {
                this.GNM_SupersessionFrom = new HashSet<GNM_SupersessionFrom>();
                this.GNM_SupersessionTo = new HashSet<GNM_SupersessionTo>();
            }

            public int Supersession_ID { get; set; }

            public int Company_ID { get; set; }
            public System.DateTime ReplacmentDate { get; set; }
            public int SupersessionType_ID { get; set; }
            public int ReplacingCode_ID { get; set; }
            public int ConsumptionCode_ID { get; set; }
            public bool IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public string Remark { get; set; }

            public virtual ICollection<GNM_SupersessionFrom> GNM_SupersessionFrom { get; set; }
            public virtual ICollection<GNM_SupersessionTo> GNM_SupersessionTo { get; set; }
        }
        public partial class GNM_SupersessionTo
        {
            public int SupersessionTo_ID { get; set; }
            public int Supersession_ID { get; set; }
            public int Parts_ID { get; set; }
            public int Quantity { get; set; }

            public virtual GNM_Supersession GNM_Supersession { get; set; }
        }
        public partial class GNM_SupersessionFrom
        {
            public int SupersessionFrom_ID { get; set; }
            public int Supersession_ID { get; set; }
            public int Parts_ID { get; set; }
            public int Quantity { get; set; }

            public virtual GNM_Supersession GNM_Supersession { get; set; }
        }
        public class Supersession
        {
            public int Supersession_ID
            {
                get;
                set;
            }
            public string Supersession_Type
            {
                get;
                set;
            }
            public string Consumption_Code
            {
                get;
                set;
            }
            public string Replacing_Code
            {
                get;
                set;
            }
            public string Active
            {
                get;
                set;
            }
            public string Remark
            {
                get;
                set;
            }
            public int Company_ID
            {
                get;
                set;
            }
            public string Company_Name
            {
                get;
                set;
            }
        }
        public class ParentCompanyObject
        {
            public int Company_ID
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public int Company_Parent_ID
            {
                get;
                set;
            }
        }
        #endregion


        #region ::: GNM_Parts Class 18-07-2024 :::
        /// <summary>
        /// GNM_Parts class
        /// </summary>
        /// 
        public partial class GNM_Parts
        {
            public GNM_Parts()
            {
                this.GNM_PARTSATTACHMENTDETAIL = new HashSet<GNM_PARTSATTACHMENTDETAIL>();
                this.GNM_PartsLocale = new HashSet<GNM_PartsLocale>();
                this.GNM_PartsPriceDetail = new HashSet<GNM_PartsPriceDetail>();
                this.GNM_PartsProductAssociation = new HashSet<GNM_PartsProductAssociation>();
                this.GNM_PartsSupplierDetail = new HashSet<GNM_PartsSupplierDetail>();
                this.REM_CORETYPE = new HashSet<REM_CORETYPE>();
                this.REM_CORETYPE1 = new HashSet<REM_CORETYPE>();
                this.REM_CORETYPE2 = new HashSet<REM_CORETYPE>();
                this.GNM_PartsStockDetail = new HashSet<GNM_PartsStockDetail>();
                this.GNM_CompetitorPriceDetail = new HashSet<GNM_CompetitorPriceDetail>();
            }

            public int Parts_ID { get; set; }
            public string Parts_PartPrefix { get; set; }
            public string Parts_PartsNumber { get; set; }
            public string Parts_PartsDescription { get; set; }
            public string Parts_AliasPartPrefix { get; set; }
            public string Parts_AliasPartNumber { get; set; }
            public Nullable<int> SuperceededPart_ID { get; set; }
            public Nullable<int> SuperceedingPart_ID { get; set; }
            public int MovementType_ID { get; set; }
            public Nullable<decimal> Parts_Weight { get; set; }
            public string Parts_Dimensions { get; set; }
            public Nullable<int> PartsCategory_ID { get; set; }
            public Nullable<int> PartsFunctionGroup_ID { get; set; }
            public Nullable<int> PartsCustomsCode_ID { get; set; }
            public int UnitOfMeasurement_ID { get; set; }
            public Nullable<int> SupersessionType_ID { get; set; }
            public bool Parts_IsActive { get; set; }
            public bool Parts_IsHazardousGood { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public int Company_ID { get; set; }
            public string Parts_Name { get; set; }
            public bool Parts_IsLocal { get; set; }
            public bool Parts_IsComponent { get; set; }
            public bool IsKitPart { get; set; }
            public Nullable<int> ExciseDuty_ID { get; set; }
            public Nullable<int> SalvagePart_ID { get; set; }
            public Nullable<int> PartType { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public Nullable<int> PartsDisposal_ID { get; set; }
            public Nullable<System.DateTime> IntroductionDate { get; set; }
            public Nullable<int> LatestSupersession_ID { get; set; }
            public Nullable<int> CustomerWarrantyInDays { get; set; }
            public Nullable<int> ReadingLimit { get; set; }

            public virtual ICollection<GNM_PARTSATTACHMENTDETAIL> GNM_PARTSATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_PartsLocale> GNM_PartsLocale { get; set; }
            public virtual ICollection<GNM_PartsPriceDetail> GNM_PartsPriceDetail { get; set; }
            public virtual ICollection<GNM_PartsProductAssociation> GNM_PartsProductAssociation { get; set; }
            public virtual ICollection<GNM_PartsSupplierDetail> GNM_PartsSupplierDetail { get; set; }
            public virtual ICollection<REM_CORETYPE> REM_CORETYPE { get; set; }
            public virtual ICollection<REM_CORETYPE> REM_CORETYPE1 { get; set; }
            public virtual ICollection<REM_CORETYPE> REM_CORETYPE2 { get; set; }
            public virtual ICollection<GNM_PartsStockDetail> GNM_PartsStockDetail { get; set; }
            public virtual ICollection<GNM_CompetitorPriceDetail> GNM_CompetitorPriceDetail { get; set; }
        }

        public partial class GNM_PARTSATTACHMENTDETAIL
        {
            public int PARTSATTACHMENTDETAIL_ID { get; set; }
            public int PARTS_ID { get; set; }
            public string FILENAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public int FILETYPE { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }

        public partial class GNM_PartsLocale
        {
            public int Parts_Locale_ID { get; set; }
            public int Parts_ID { get; set; }
            public string Parts_PartsDescription { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }

        public partial class GNM_PartsPriceDetail
        {
            public int PartsPriceDetail_ID { get; set; }
            public int Parts_ID { get; set; }
            public decimal PartsPriceDetail_ListPrice { get; set; }
            public Nullable<decimal> PartsPriceDetail_FormulaCostPrice { get; set; }
            public decimal PartsPriceDetail_MRP { get; set; }
            public System.DateTime PartsPriceDetail_EffectiveFrom { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> CustomerWarranty { get; set; }
            public Nullable<int> Currency_ID { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }
        public partial class GNM_PartsProductAssociation
        {
            public int PartsProductAssociation_ID { get; set; }
            public int Parts_ID { get; set; }
            public int Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public int Company_ID { get; set; }
            public string FromSerialNumber { get; set; }
            public string ToSerialNumber { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }
        public partial class REM_CORETYPE
        {
            public int CORETYPE_ID { get; set; }
            public int COMPANY_ID { get; set; }
            public int COREPARTS_ID { get; set; }
            public int ORIGINALPARTS_ID { get; set; }
            public int REMANPARTS_ID { get; set; }
            public string REMARKS { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
            public virtual GNM_Parts GNM_Parts1 { get; set; }
            public virtual GNM_Parts GNM_Parts2 { get; set; }
        }
        public partial class GNM_PartsStockDetail
        {
            public int PartsStockDetail_ID { get; set; }
            public int Parts_ID { get; set; }
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> WareHouse_ID { get; set; }
            public Nullable<decimal> FreeStock { get; set; }
            public Nullable<decimal> AllocatedQuantity { get; set; }
            public Nullable<decimal> PickedQuantity { get; set; }
            public Nullable<decimal> ReservedQuantity { get; set; }
            public Nullable<decimal> BackOrderQuantity { get; set; }
            public Nullable<decimal> PendingPurchaseOrderQuantity { get; set; }
            public Nullable<decimal> PendingPartsOrderQuantity { get; set; }
            public Nullable<decimal> DeviationStock { get; set; }
            public Nullable<decimal> StockUsedInKits { get; set; }
            public int ReOrderLevel { get; set; }
            public decimal ReOrderLevelQuantity { get; set; }
            public decimal MinOrderQty { get; set; }
            public Nullable<decimal> GITQuantity { get; set; }
            public Nullable<decimal> BinStock { get; set; }
            public Nullable<decimal> TotalStock { get; set; }
            public Nullable<decimal> WeightedAverageCost { get; set; }
            public int BinLocation_ID { get; set; }
            public Nullable<int> BinlocationBuffer_ID { get; set; }
            public Nullable<int> OldBinLocation_ID { get; set; }
            public Nullable<int> OldBufferBinLocation_ID { get; set; }
            public bool IsBlocked { get; set; }
            public Nullable<System.DateTime> LastStockUpdatedDate { get; set; }
            public Nullable<int> Movement_ID { get; set; }
            public Nullable<System.DateTime> FirstDemandDate { get; set; }
            public Nullable<System.DateTime> LastDemandDate { get; set; }
            public Nullable<System.DateTime> FirstIssuedDate { get; set; }
            public Nullable<System.DateTime> LastIssuedDate { get; set; }
            public Nullable<System.DateTime> LastStockCheckDate { get; set; }
            public Nullable<decimal> MaximumStockLevel { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }
        public partial class GNM_CompetitorPriceDetail
        {
            public int CompetitorPriceDetail_ID { get; set; }
            public Nullable<int> Parts_ID { get; set; }
            public string CompetitorName { get; set; }
            public Nullable<decimal> MRP { get; set; }
            public Nullable<decimal> MarketRulingPrice { get; set; }
            public Nullable<System.DateTime> Competitor_EffectiveFrom { get; set; }
            public string Remarks { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> ModifiedBy { get; set; }
            public Nullable<System.DateTime> ModifiedDate { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }
        public partial class GNM_PartsSupplierDetail
        {
            public int PartsSupplierDetail_ID { get; set; }
            public int Parts_ID { get; set; }
            public int Supplier_ID { get; set; }
            public Nullable<decimal> StandardPackingQuantity { get; set; }
            public Nullable<decimal> SupplierPrice { get; set; }
            public System.DateTime Effectivefrom { get; set; }
            public Nullable<decimal> CostPrice { get; set; }
            public int Currency_ID { get; set; }
            public Nullable<System.DateTime> LastInvoicedDate { get; set; }
            public string SupplierPartNumber { get; set; }
            public int Company_ID { get; set; }
            public string SupplierPartPrefix { get; set; }
            public Nullable<int> ManufacturerWarranty { get; set; }
            public Nullable<bool> IsWarrantyIntimation { get; set; }
            public Nullable<decimal> LatestPurchaseCost { get; set; }
            public Nullable<System.DateTime> FirstGRNDate { get; set; }
            public Nullable<System.DateTime> LastGRNDate { get; set; }
            public Nullable<decimal> SupplierYTAPrice { get; set; }
            public Nullable<decimal> SupplierYEAPrice { get; set; }

            public virtual GNM_Parts GNM_Parts { get; set; }
        }

        #endregion
    }

}

