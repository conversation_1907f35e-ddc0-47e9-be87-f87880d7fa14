using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Services;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CoreChangeVehicleOwnershipController : ControllerBase
    {
        private readonly ICoreChangeVehicleOwnershipServices _coreChangeVehicleOwnershipServices;
        private readonly ILogger<CoreChangeVehicleOwnershipController> _logger;
        private readonly IConfiguration _configuration;

        public CoreChangeVehicleOwnershipController(
            ICoreChangeVehicleOwnershipServices coreChangeVehicleOwnershipServices, 
            ILogger<CoreChangeVehicleOwnershipController> logger,
            IConfiguration configuration)
        {
            _coreChangeVehicleOwnershipServices = coreChangeVehicleOwnershipServices;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Select change vehicle ownership logs
        /// </summary>
        /// <param name="request">Select request parameters</param>
        /// <param name="sidx">Sort field</param>
        /// <param name="rows">Rows per page</param>
        /// <param name="page">Page number</param>
        /// <param name="sord">Sort direction</param>
        /// <param name="_search">Search flag</param>
        /// <param name="nd">Timestamp</param>
        /// <param name="filters">Filter criteria</param>
        /// <param name="advnce">Advanced search flag</param>
        /// <param name="advnceFilters">Advanced filter criteria</param>
        /// <returns>JsonResult with change vehicle ownership logs</returns>
        [HttpPost("select")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Select([FromBody] SelectCoreChangeVehicleOwnershipList request, 
            [FromQuery] string sidx = "ChangeVehicleOwnership_ID", 
            [FromQuery] int rows = 10, 
            [FromQuery] int page = 1, 
            [FromQuery] string sord = "asc", 
            [FromQuery] bool _search = false, 
            [FromQuery] long nd = 0, 
            [FromQuery] string filters = "", 
            [FromQuery] bool advnce = false, 
            [FromQuery] string advnceFilters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/select");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                
                var result = await _coreChangeVehicleOwnershipServices.SelectAsync(connString, request, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting change vehicle ownership logs");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while selecting change vehicle ownership logs");
            }
        }

        /// <summary>
        /// Save product customer details
        /// </summary>
        /// <param name="request">Save request with customer data</param>
        /// <returns>JsonResult with save result message</returns>
        [HttpPost("save-product-customer-details")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveProductCustomerDetails([FromBody] SaveProductCustomerDetailsCoreChangeVehicleOwnershipList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/save-product-customer-details");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                
                var result = await _coreChangeVehicleOwnershipServices.SaveProductCustomerDetailsAsync(connString, request);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving product customer details");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while saving product customer details");
            }
        }

        /// <summary>
        /// Search serial numbers with field search
        /// </summary>
        /// <param name="request">Search request parameters</param>
        /// <param name="sidx">Sort field</param>
        /// <param name="rows">Rows per page</param>
        /// <param name="page">Page number</param>
        /// <param name="sord">Sort direction</param>
        /// <param name="_search">Search flag</param>
        /// <param name="nd">Timestamp</param>
        /// <param name="filters">Filter criteria</param>
        /// <param name="advnce">Advanced search flag</param>
        /// <param name="advnceFilters">Advanced filter criteria</param>
        /// <returns>JsonResult with serial number search results</returns>
        [HttpPost("select-field-search-serial-number")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SelectFieldSearchSerialNumber([FromBody] SelectFieldSearchSerialNumberList request, 
            [FromQuery] string sidx = "ID", 
            [FromQuery] int rows = 10, 
            [FromQuery] int page = 1, 
            [FromQuery] string sord = "asc", 
            [FromQuery] bool _search = false, 
            [FromQuery] long nd = 0, 
            [FromQuery] string filters = "", 
            [FromQuery] bool advnce = false, 
            [FromQuery] string advnceFilters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/select-field-search-serial-number");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                
                var result = await _coreChangeVehicleOwnershipServices.SelectFieldSearchSerialNumberAsync(connString, request, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching serial numbers");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while searching serial numbers");
            }
        }

        /// <summary>
        /// Get product details by serial number
        /// </summary>
        /// <param name="request">Request with serial number</param>
        /// <returns>JsonResult with product details</returns>
        [HttpPost("get-product-for-serial")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetProductForSerial([FromBody] GetProductForSerialList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/get-product-for-serial");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                
                var result = await _coreChangeVehicleOwnershipServices.GetProductForSerialAsync(connString, request);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product for serial");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting product for serial");
            }
        }

        /// <summary>
        /// Get party details by name
        /// </summary>
        /// <param name="request">Request with party name</param>
        /// <returns>JsonResult with party details</returns>
        [HttpPost("get-party-details")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetPartyDetails([FromBody] GetPartyDetailsCoreChangeVehicleOwnershipList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/get-party-details");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                
                var result = await _coreChangeVehicleOwnershipServices.GetPartyDetailsAsync(connString, request);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting party details");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting party details");
            }
        }

        /// <summary>
        /// Get party details by ID
        /// </summary>
        /// <param name="request">Request with party ID</param>
        /// <returns>JsonResult with party details</returns>
        [HttpPost("get-party-details-by-id")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetPartyDetailsbyID([FromBody] GetPartyDetailsbyIDaList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/get-party-details-by-id");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;

                var result = await _coreChangeVehicleOwnershipServices.GetPartyDetailsbyIDAsync(connString, request);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting party details by ID");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting party details by ID");
            }
        }

        /// <summary>
        /// Select party detail grid
        /// </summary>
        /// <param name="request">Select party detail grid request parameters</param>
        /// <param name="sidx">Sort field</param>
        /// <param name="rows">Rows per page</param>
        /// <param name="page">Page number</param>
        /// <param name="sord">Sort direction</param>
        /// <param name="_search">Search flag</param>
        /// <param name="nd">Timestamp</param>
        /// <param name="filters">Filter criteria</param>
        /// <param name="advnce">Advanced search flag</param>
        /// <param name="advnceFilters">Advanced filter criteria</param>
        /// <returns>JsonResult with party detail grid</returns>
        [HttpPost("select-party-detail-grid")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SelectPartyDetailGrid([FromBody] SelectPartyDetailGridCList request,
            [FromQuery] string sidx = "Party_ID",
            [FromQuery] int rows = 10,
            [FromQuery] int page = 1,
            [FromQuery] string sord = "asc",
            [FromQuery] bool _search = false,
            [FromQuery] long nd = 0,
            [FromQuery] string filters = "",
            [FromQuery] bool advnce = false,
            [FromQuery] string advnceFilters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/select-party-detail-grid");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;

                var result = await _coreChangeVehicleOwnershipServices.SelectPartyDetailGridAsync(connString, request, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting party detail grid");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while selecting party detail grid");
            }
        }

        /// <summary>
        /// Search parties with field search
        /// </summary>
        /// <param name="request">Search request parameters</param>
        /// <param name="sidx">Sort field</param>
        /// <param name="rows">Rows per page</param>
        /// <param name="page">Page number</param>
        /// <param name="sord">Sort direction</param>
        /// <param name="_search">Search flag</param>
        /// <param name="nd">Timestamp</param>
        /// <param name="filters">Filter criteria</param>
        /// <param name="advnce">Advanced search flag</param>
        /// <param name="advnceFilters">Advanced filter criteria</param>
        /// <returns>JsonResult with party search results</returns>
        [HttpPost("select-field-search-party")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SelectFieldSearchParty([FromBody] SelectFieldSearchPartyList request,
            [FromQuery] string sidx = "ID",
            [FromQuery] int rows = 10,
            [FromQuery] int page = 1,
            [FromQuery] string sord = "asc",
            [FromQuery] bool _search = false,
            [FromQuery] long nd = 0,
            [FromQuery] string filters = "",
            [FromQuery] bool advnce = false,
            [FromQuery] string advnceFilters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/corechangevehicleownership/select-field-search-party");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;

                var result = await _coreChangeVehicleOwnershipServices.SelectFieldSearchPartyAsync(connString, request, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching parties");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while searching parties");
            }
        }
    }
}
