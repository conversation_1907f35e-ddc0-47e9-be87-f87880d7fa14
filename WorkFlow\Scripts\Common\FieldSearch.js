﻿
var isSelect = false;

$.widget("ui.FieldSearch", {

    options:
    {
        grd:null,
        FieldSearchDiv: null,
        width: null,
        height: null,
        parameters: null,
        Title: null,
        url: null,
        bindAction:null,
        ResultTextField: null,
        ResultValueField: null,
        id: null,
        headerNames: { id: null, Name: null },
        colModels: [],
        colName: [],
        FieldSearchName: null,
        DefaultSortColName: null,
       
    },
    _init: function () {
       
        this.options.width = this.options.width == undefined ? 'auto' : this.options.width;
        this.options.height = this.options.height == undefined ? 'auto' : this.options.height;
    },
    _create: function () { 
        var widg = this;
        var main = $(this.element);
        this.FieldSearchDiv = $("<div id='" + (main.attr('id')+'dlg' )+ "' style='display:none'></div>");
        this.options.FieldSearchDiv = this.FieldSearchDiv;
        this.id = main.attr('id') + 'FieldSrch'
        this.table = $("<table id='" + this.id + "'><tr><td /></tr></table>");
        this.FieldSearchDiv.append(this.table);
        this.pagerid = main.attr('id') + 'DivFieldSearch'
        this.FieldSearchPagerDiv = $("<div id='" + this.pagerid + "'></div>");
        this.FieldSearchDiv.append(this.FieldSearchPagerDiv);

        var table = this.table;
       
        main.append(this.FieldSearchDiv);

        var FSrhDiv = this.options.FieldSearchDiv;

        var id = this.id
        this.options.id = this.id;
        
        var colName = '';

        if (this.options.headerNames.Name != undefined && this.options.headerNames.Name != null) {
            colName = this.options.headerNames.Name;
        }
        else {
            colName = 'Name';
        }
        $('#' + this.id).GridUnload();
        $('#'+this.id).jqGrid({
            //url: $.url(url + '?value=' + val),
            datatype: 'clientSide',
            mtype: 'POST',          
            colModel: this.options.colModels,
            colNames: this.options.colName,
            pager: '#' + this.pagerid,
            rowNum: 5,
            rowList: [5, 10],
            viewrecords: true,
            sortname: this.options.DefaultSortColName,
            sortorder: 'desc',
            caption: this.options.FieldSearchName,
            jsonReader: {
                root: "rows",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false
            },
            loadComplete: function () {
                $('.FieldSrch').click(function () {
                    isSelect = true;
                    var x = this;
                    x.rowid = $(x).parent('td').parent('tr').attr('id');                   
                    var grd = $('#' + id);
                    x.ID = grd.getCell(x.rowid, 'ID');
                    x.name = grd.getCell(x.rowid, 'Name');

                    if (widg.getParameters('ResultValueField') != "Dropdown") {
                        var ResultTField = $('#' + widg.getParameters('ResultTextField'));
                        var ResultVField = $('#' + widg.getParameters('ResultValueField'));

                        ResultTField.attr("value", x.name);
                        ResultVField.attr("value", x.ID);
                       
                        ResultTField.change();
                        ResultTField.focusout();
                    }
                    else {
                        var isValid = true;
                        var ResultTField =  widg.getParameters('ResultTextField');
                        var DropdownID = document.getElementById(ResultTField);

                        for (var i = 1; i < DropdownID.length; i++) {
                            if (DropdownID.options[i].value == x.ID) {
                                isValid = false;
                            }                           
                        }

                        if (isValid) {
                            var opt = new Option(x.name, x.ID, false, false)
                            DropdownID.add(opt, 1);
                            document.getElementById(widg.getParameters('ResultTextField')).selectedIndex = 1;

                            var ResultTField = $('#' + widg.getParameters('ResultTextField'));
                            ResultTField.change();
                        }
                    }
                    FSrhDiv.dialog("close");
                    
                   
                });
            },
            width: '100%',
            height: '100%',
        });
        $('#' + this.id).navGrid('#' + this.pagerid, { view: false, add: false, edit: false, del: false, search: false, refresh: false });
        $('#' + this.id).jqGrid('filterToolbar', { searchOnEnter: true, stringResult: true });
     
    },
    Search: function (par) {
        var grdid = this.options.id;
        var widg = this;
        
        $('#' + this.options.FieldSearchDiv.attr('id')).dialog({
            close: function () {               
                
                if (!(isSelect)) {
                    $('#' + grdid).clearGridData(true);
                    var ResultTField = $('#' + widg.getParameters('ResultTextField'));
                    ResultTField.attr("value", "");
                    isSelect = false;
                }
            },
            modal: true, position: 'center', width: this.options.width, height: this.options.height, title: this.options.Title,
        });

        var url = this.options.url;

        this.options.ResultTextField = par.ResultTextField;
        this.options.ResultValueField = par.ResultValueField;
        var  FSrhDiv = this.options.FieldSearchDiv;
        var ResultTField = $('#' + par.ResultTextField);
        var val='';
        if (this.options.ResultValueField != 'Dropdown') {
            val = ResultTField.attr("value");
        }       

        var gridUrl = '';

      
        if (par.ExtraParam != 'undefined' && par.ExtraParam != null) {
           
            gridUrl = url + this.getgrdUrlCond() + 'value=' +$.EncryptString( val) + par.ExtraParam;
        }
        else {
            gridUrl = url + this.getgrdUrlCond() + 'value=' + $.EncryptString(val);
        }

        $('#' + grdid).setGridParam({
            datatype: 'json',
            url: gridUrl,
            sortorder: 'desc'
        });
        $('#' + grdid).setColProp('Name', { searchoptions: { defaultValue: val } });
        $('#' + grdid).clearGridData(true);
       
        $('#' + grdid).trigger('reloadGrid');
      
    },
    getParameters: function (key) {

        return this.options[key];
    },
    getgrdUrlCond: function () {

        this.grdUrl = this.options.url;
        this.HasUrlPars = false;
        this.urlCond = '';
        if (this.grdUrl.indexOf("?") != '-1') {
            this.HasUrlPars = true;
            this.urlCond = '&';
        }
        else {
            this.urlCond = '?';
        }
        return this.urlCond;
    },
});
