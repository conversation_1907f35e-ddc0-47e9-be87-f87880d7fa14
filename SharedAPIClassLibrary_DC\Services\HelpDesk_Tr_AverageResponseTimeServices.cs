﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Tr_AverageResponseTimeServices
    {



        #region ::: Select Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To select Year wise Details
        /// </summary>
        public static IActionResult Select(HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.ToDate);
                TDate = TDate.AddDays(1);
                string BranchID = string.Empty;
                string ValueArray = (HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray == "" || HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray == null) ? string.Empty : HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray.ToString();
                string TextArray = (HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray == "" || HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray == null) ? string.Empty : Common.DecryptString(HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray.ToString());

                string CompanyIDs = HelpDesk_Tr_AverageResponseTimeSelectobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_AverageResponseTimeSelectobj.BranchIDs.TrimEnd(new char[] { ',' });

                IEnumerable<AverageResponseTimeObject> IEAverageResponseTimeReport = null;
                IQueryable<AverageResponseTimeObject> IQAverageResponseTimeReport = null;


                IEAverageResponseTimeReport = GetAllServiceRequest(HelpDesk_Tr_AverageResponseTimeSelectobj, connString, LogException, ValueArray, HelpDesk_Tr_AverageResponseTimeSelectobj.Mode, BranchIDs, CompanyIDs, FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));

                IEAverageResponseTimeReport = (from a in IEAverageResponseTimeReport
                                               group a by new { a.SCallDate.Year, a.Company_ID, a.Branch_ID } into g
                                               select new AverageResponseTimeObject
                                               {
                                                   Year = g.FirstOrDefault().CallDate.Year,
                                                   AverageTimestring = ConvertToHours(g.Sum(b => b.AverageTimeMinutes) / g.Count()),
                                                   SRCount = g.Count(),
                                                   CompanyName = g.FirstOrDefault().CompanyName,
                                                   BranchName = g.FirstOrDefault().BranchName,
                                                   Region = g.FirstOrDefault().Region
                                               });

                IQAverageResponseTimeReport = IEAverageResponseTimeReport.AsQueryable<AverageResponseTimeObject>();

                IQAverageResponseTimeReport = IQAverageResponseTimeReport.OrderByField<AverageResponseTimeObject>(sidx, sord);

                Count = IQAverageResponseTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from a in IQAverageResponseTimeReport.AsEnumerable()
                            select new
                            {
                                a.Year,
                                a.AverageTimestring,
                                a.SRCount,
                                a.CompanyName,
                                a.BranchName,
                                a.Region
                            }).ToList().Paginate(page, rows)
                };
                // gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeSelectobj.Company_ID.ToString()), Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeSelectobj.Branch), HelpDesk_Tr_AverageResponseTimeSelectobj.User_ID, Common.GetObjectID("HelpDesk_Tr_AverageResponseTime"), 0, 0, 0, "Generated-Average Response Time Report ", false, Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeSelectobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Get All Service Requests Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// Get All Service Requests
        /// </summary>
        /// 
        private static IEnumerable<AverageResponseTimeObject> GetAllServiceRequest(HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj, string connString, int LogException, string ValueArray, int Mode, string BranchIDs, string CompanyIDs, string FromDate, string ToDate)
        {
            List<AverageResponseTimeObject> IEAverageResponseTimeReport = new List<AverageResponseTimeObject>();

            try
            {
                int UserLang = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeSelectobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeSelectobj.GeneralLanguageID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseTimeGetAllServiceRequests", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@ValueArray", ValueArray);
                        cmd.Parameters.AddWithValue("@Mode", Mode);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@FromDate", Convert.ToDateTime(FromDate));
                        cmd.Parameters.AddWithValue("@ToDate", Convert.ToDateTime(ToDate));
                        cmd.Parameters.AddWithValue("@UserLang", UserLang);
                        cmd.Parameters.AddWithValue("@GeneralLang", GeneralLang);

                        connection.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var serviceRequest = new AverageResponseTimeObject
                                {
                                    ServiceRequestID = Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    CallDate = Convert.ToDateTime(reader["CallDateAndTime"]),
                                    AverageTimeMinutes = ConvertToMinutes(reader["ResponseTime"].ToString()),
                                    AverageTimestring = reader["ResponseTime"].ToString(),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                    Region = Common.getRegionName(connString, LogException, UserLang, GeneralLang, Convert.ToInt32(reader["Branch_ID"])),
                                };

                                // Fetch CompanyName using ADO.NET
                                serviceRequest.CompanyName = GetCompanyName(serviceRequest.Company_ID, connection);

                                // Fetch BranchName using ADO.NET
                                serviceRequest.BranchName = GetBranchName(serviceRequest.Branch_ID, connection);

                                IEAverageResponseTimeReport.Add(serviceRequest);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return IEAverageResponseTimeReport;
        }

        private static string GetCompanyName(int companyId, SqlConnection connection)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseTimeGetCompanyName", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyId);

                object result = cmd.ExecuteScalar();
                return result != null ? result.ToString() : string.Empty;
            }
        }

        private static string GetBranchName(int branchId, SqlConnection connection)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseTimeGetBranchName", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@BranchID", branchId);

                object result = cmd.ExecuteScalar();
                return result != null ? result.ToString() : string.Empty;
            }
        }
        public static string ConvertToHours(int Minutes)
        {
            double a = Convert.ToDouble(Minutes);
            string InTimeFormat = Math.Floor(a / 60).ToString() + ":" + (Math.Floor(a % 60).ToString().Length == 1 ? "0" + Math.Floor(a % 60).ToString() : Math.Floor(a % 60).ToString());
            return InTimeFormat;
        }
        public static int ConvertToMinutes(string Hours)
        {
            int Minutes = 0;
            Minutes = Convert.ToInt32(Hours.Split(':')[0]) * 60 + Convert.ToInt32(Hours.Split(':')[1]);
            return Minutes;
        }
        #endregion


        #region ::: SelectMonthWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Month Wise Avearage Response Time
        /// </summary>
        public static IActionResult SelectMonthWise(HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.ToDate);
                TDate = TDate.AddDays(1);
                string BranchID = string.Empty;
                string ValueArray = (HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray == "" || HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray == null) ? string.Empty : HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray.ToString();
                string TextArray = (HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray == "" || HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray == null) ? string.Empty : Common.DecryptString(HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray.ToString());

                string CompanyIDs = HelpDesk_Tr_AverageResponseTimeSelectobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_AverageResponseTimeSelectobj.BranchIDs.TrimEnd(new char[] { ',' });

                IEnumerable<AverageResponseTimeObject> IEAverageResponseTimeReport = null;
                IQueryable<AverageResponseTimeObject> IQAverageResponseTimeReport = null;
                IEnumerable<AverageResponseTimeObject> ReportData = null;

                ReportData = GetAllServiceRequest(HelpDesk_Tr_AverageResponseTimeSelectobj, connString, LogException, ValueArray, HelpDesk_Tr_AverageResponseTimeSelectobj.Mode, BranchIDs, CompanyIDs, FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));
                ReportData = ReportData.Where(R => R.CallDate.Year == HelpDesk_Tr_AverageResponseTimeSelectobj.Year & R.BranchName == HelpDesk_Tr_AverageResponseTimeSelectobj.BranchName).ToList();

                IEAverageResponseTimeReport = (from a in ReportData
                                               group a by new { a.CallDate.Year, a.CallDate.Month, a.Branch_ID } into g
                                               select new AverageResponseTimeObject
                                               {
                                                   Month = g.FirstOrDefault().CallDate.Month.ToString(),
                                                   MonthName = GetMonthName(HelpDesk_Tr_AverageResponseTimeSelectobj, g.FirstOrDefault().CallDate.Month),
                                                   AverageTimestring = ConvertToHours(g.Sum(S => S.AverageTimeMinutes) / g.Count()),
                                                   SRCount = g.Count()
                                               });

                IQAverageResponseTimeReport = IEAverageResponseTimeReport.AsQueryable<AverageResponseTimeObject>();

                IQAverageResponseTimeReport = IQAverageResponseTimeReport.OrderByField<AverageResponseTimeObject>(sidx, sord);

                Count = IQAverageResponseTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from a in IQAverageResponseTimeReport.AsEnumerable()
                            select new
                            {
                                a.Month,
                                a.MonthName,
                                a.AverageTimestring,
                                a.SRCount
                            }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: GetMonthName Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj, int ID)
        {
            string MonthName = string.Empty;
            try
            {
                switch (ID)
                {
                    case 1:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "January").ToString();
                        break;
                    case 2:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "February").ToString();
                        break;
                    case 3:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "March").ToString();
                        break;
                    case 4:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "April").ToString();
                        break;
                    case 5:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "May").ToString();
                        break;
                    case 6:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "June").ToString();
                        break;
                    case 7:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "July").ToString();
                        break;
                    case 8:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "August").ToString();
                        break;
                    case 9:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "September").ToString();
                        break;
                    case 10:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "October").ToString();
                        break;
                    case 11:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "November").ToString();
                        break;
                    case 12:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeSelectobj.UserCulture.ToString(), "December").ToString();
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {

            }
            return MonthName;
        }
        #endregion


        #region ::: SelectDateWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Wise
        /// </summary>
        public static IActionResult SelectDateWise(HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                DateTime FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.FromDate);
                DateTime TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeSelectobj.ToDate);
                TDate = TDate.AddDays(1);
                string BranchID = string.Empty;
                string ValueArray = (HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray == "" || HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray == null) ? string.Empty : HelpDesk_Tr_AverageResponseTimeSelectobj.ValueArray.ToString();
                string TextArray = (HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray == "" || HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray == null) ? string.Empty : Common.DecryptString(HelpDesk_Tr_AverageResponseTimeSelectobj.TextArray.ToString());

                string CompanyIDs = HelpDesk_Tr_AverageResponseTimeSelectobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_AverageResponseTimeSelectobj.BranchIDs.TrimEnd(new char[] { ',' });

                IEnumerable<AverageResponseTimeObject> IEAverageResponseTimeReport = null;
                IQueryable<AverageResponseTimeObject> IQAverageResponseTimeReport = null;

                IEnumerable<AverageResponseTimeObject> ReportData = null;

                ReportData = GetAllServiceRequest(HelpDesk_Tr_AverageResponseTimeSelectobj, connString, LogException, ValueArray, HelpDesk_Tr_AverageResponseTimeSelectobj.Mode, BranchIDs, CompanyIDs, FDate.ToString("dd-MMM-yyyy"), TDate.ToString("dd-MMM-yyyy"));
                ReportData = ReportData.Where(R => R.CallDate.Year == HelpDesk_Tr_AverageResponseTimeSelectobj.Year && R.CallDate.Month == HelpDesk_Tr_AverageResponseTimeSelectobj.Month && R.BranchName == HelpDesk_Tr_AverageResponseTimeSelectobj.BranchName).ToList();

                IEAverageResponseTimeReport = (from a in ReportData
                                               group a by new { a.CallDate.Date } into g
                                               select new AverageResponseTimeObject
                                               {
                                                   Date = (g.FirstOrDefault().CallDate).ToString("dd-MMM-yyyy"),
                                                   AverageTimestring = ConvertToHours(g.Sum(S => S.AverageTimeMinutes) / g.Count()),
                                                   SRCount = g.Count()
                                               });

                IQAverageResponseTimeReport = IEAverageResponseTimeReport.AsQueryable<AverageResponseTimeObject>();

                IQAverageResponseTimeReport = IQAverageResponseTimeReport.OrderByField<AverageResponseTimeObject>(sidx, sord);

                Count = IQAverageResponseTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from a in IQAverageResponseTimeReport.AsEnumerable()
                            select new
                            {
                                a.Date,
                                a.SRCount,
                                a.AverageTimestring
                            }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectCustomer Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To get Customers
        /// </summary> 
        /// 
        public static IActionResult SelectCustomer(HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {

                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageID);
                int Company_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.Company_ID);
                bool FilterBasedonCompany = false;

                // Get FilterBasedonCompany
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetFilterPartyBasedOnCompany", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@Param_Name", "FILTERPARTYBASEDONCOMPANY");

                        // Execute the stored procedure and get the result
                        object result = cmd.ExecuteScalar();
                        FilterBasedonCompany = result != null && result.ToString().ToUpper() == "TRUE";
                    }
                }


                // Fetch Customer Details
                List<GNM_Party> CustomerDetailDetail;
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetCustomerDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Adding parameters with explicit type handling
                        if (FilterBasedonCompany && !string.IsNullOrEmpty(HelpDesk_Tr_AverageResponseSelectCustomerobj.CompanyIDs))
                        {
                            cmd.Parameters.AddWithValue("@CompanyIDs", HelpDesk_Tr_AverageResponseSelectCustomerobj.CompanyIDs.TrimEnd(','));
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@CompanyIDs", DBNull.Value);
                        }

                        cmd.Parameters.AddWithValue("@PartyType", 1);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            CustomerDetailDetail = new List<GNM_Party>();
                            while (reader.Read())
                            {
                                CustomerDetailDetail.Add(new GNM_Party
                                {
                                    Party_ID = reader["Party_ID"] != DBNull.Value ? Convert.ToInt32(reader["Party_ID"]) : 0,
                                    Company_ID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0,
                                    Party_Name = reader["Party_Name"] != DBNull.Value ? reader["Party_Name"].ToString() : string.Empty,
                                    PartyType = reader["PartyType"] != DBNull.Value ? Convert.ToByte(reader["PartyType"]) : (byte)0,
                                });
                            }
                        }
                    }
                }

                if (HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResponseSelectCustomerobj.GeneralLanguageCode.ToString())
                {
                    Masterdata = new
                    {
                        CustomerData = CustomerDetailDetail
                            .OrderBy(cust => cust.Party_Name)
                            .Select(cust => new
                            {
                                ID = cust.Party_ID,
                                Name = cust.Party_Name
                            })
                            .Distinct()
                    };
                }
                else
                {
                    List<GNM_PartyLocale> CustomerDetailLocale;
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetLocalizedCustomerDetails", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                CustomerDetailLocale = new List<GNM_PartyLocale>();
                                while (reader.Read())
                                {
                                    CustomerDetailLocale.Add(new GNM_PartyLocale
                                    {
                                        Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                        Party_Name = reader["Party_Name"].ToString()
                                    });
                                }
                            }
                        }
                    }

                    Masterdata = new
                    {
                        CustomerData = (from cust in CustomerDetailDetail
                                        join custL in CustomerDetailLocale on cust.Party_ID equals custL.Party_ID
                                        orderby custL.Party_Name
                                        select new
                                        {
                                            ID = custL.Party_ID,
                                            Name = custL.Party_Name
                                        }).Distinct()
                    };
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }

        #endregion



        #region ::: SelectDealer Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To get Dealer 
        /// </summary> 
        /// 
        public static IActionResult SelectDealer(HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = new object();
            try
            {
                // Placeholder for the master data

                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageID);
                int branchID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.Branch);

                List<GNM_Branch> dealerDetails = new List<GNM_Branch>();

                // Database connection and stored procedure execution
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetDealerDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchID", branchID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                dealerDetails.Add(new GNM_Branch
                                {
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                    Branch_Name = reader["Branch_Name"].ToString(),
                                });
                            }
                        }
                    }
                }

                // Language-based processing
                if (HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResponseSelectCustomerobj.GeneralLanguageCode.ToString())
                {
                    Masterdata = new
                    {
                        CustomerData = (from dealer in dealerDetails
                                        orderby dealer.Branch_Name
                                        select new
                                        {
                                            ID = dealer.Branch_ID,
                                            Name = dealer.Branch_Name
                                        }).Distinct()
                    };
                }

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }

        #endregion



        #region ::: SelectModel Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To Select Model
        /// </summary> 
        /// 
        public static IActionResult SelectModel(HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = new object();
            try
            {
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageID);
                string CompanyIDs = HelpDesk_Tr_AverageResponseSelectCustomerobj.CompanyIDs.TrimEnd(new char[] { ',' });


                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch CompanyBrands using SP_GetCompanyBrands
                    List<GNM_CompanyBrands> CompBrands = new List<GNM_CompanyBrands>();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetCompanyBrands", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CompBrands.Add(new GNM_CompanyBrands
                                {
                                    Brand_ID = Convert.ToInt32(reader["Brand_ID"]),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    // Add other fields as necessary
                                });
                            }
                        }
                    }

                    // Fetch Models using SP_GetModels
                    List<GNM_Model> Models = new List<GNM_Model>();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetModels", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Models.Add(new GNM_Model
                                {
                                    Model_ID = Convert.ToInt32(reader["Model_ID"]),
                                    Model_Name = reader["Model_Name"].ToString(),
                                    Brand_ID = Convert.ToInt32(reader["Brand_ID"]),
                                    // Add other fields as necessary
                                });
                            }
                        }
                    }

                    // Determine data to return based on language
                    if (HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResponseSelectCustomerobj.GeneralLanguageCode.ToString())
                    {
                        Masterdata = new
                        {
                            ModelData = (from M in Models
                                         join cb in CompBrands on M.Brand_ID equals cb.Brand_ID
                                         orderby M.Model_Name
                                         select new
                                         {
                                             ID = M.Model_ID,
                                             Name = M.Model_Name
                                         }).Distinct()
                        };
                    }
                    else
                    {
                        // Fetch ModelLocales using SP_GetModelLocales
                        List<GNM_ModelLocale> ModelLocales = new List<GNM_ModelLocale>();
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponseGetModelLocales", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ModelLocales.Add(new GNM_ModelLocale
                                    {
                                        Model_ID = Convert.ToInt32(reader["Model_ID"]),
                                        Model_Name = reader["Model_Name"].ToString(),
                                        Language_ID = Convert.ToInt32(reader["Language_ID"]),
                                        // Add other fields as necessary
                                    });
                                }
                            }
                        }

                        Masterdata = new
                        {
                            ModelData = (from M in Models
                                         join ML in ModelLocales on M.Model_ID equals ML.Model_ID
                                         join cb in CompBrands on M.Brand_ID equals cb.Brand_ID
                                         orderby ML.Model_Name
                                         select new
                                         {
                                             ID = ML.Model_ID,
                                             Name = ML.Model_Name
                                         }).Distinct()
                        };
                    }
                }


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }

        #endregion


        #region ::: SelectIssueArea Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        /// 
        public static IActionResult SelectIssueArea(HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = new object();
            try
            {
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageID);
                string CompanyIDs = HelpDesk_Tr_AverageResponseSelectCustomerobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string refMasterName = "ISSUEAREA";

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch RefMasterDetails using SP_GetRefMasterDetails
                    List<GNM_RefMasterDetail> RefMasterDetails = new List<GNM_RefMasterDetail>();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponse_GetRefMasterDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RefMaster_Name", refMasterName);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterDetails.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                                    RefMaster_ID = Convert.ToInt32(reader["RefMaster_ID"]),
                                    // Add other fields as necessary
                                });
                            }
                        }
                    }

                    if (HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResponseSelectCustomerobj.GeneralLanguageCode.ToString())
                    {
                        Masterdata = new
                        {
                            IssueAreaData = (from Ref in RefMasterDetails
                                             orderby Ref.RefMasterDetail_Name
                                             select new
                                             {
                                                 ID = Ref.RefMasterDetail_ID,
                                                 Name = Ref.RefMasterDetail_Name
                                             }).Distinct()
                        };
                    }
                    else
                    {
                        // Fetch localized RefMasterDetails using SP_GetRefMasterDetailLocales
                        List<GNM_RefMasterDetailLocale> RefMasterDetailLocales = new List<GNM_RefMasterDetailLocale>();
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponse_GetRefMasterDetailLocales", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMaster_Name", refMasterName);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocales.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                                        Language_ID = Convert.ToInt32(reader["Language_ID"]),
                                        // Add other fields as necessary
                                    });
                                }
                            }
                        }

                        Masterdata = new
                        {
                            IssueAreaData = (from Ref in RefMasterDetails
                                             join RefL in RefMasterDetailLocales on Ref.RefMasterDetail_ID equals RefL.RefMasterDetail_ID
                                             orderby RefL.RefMasterDetail_Name
                                             select new
                                             {
                                                 ID = RefL.RefMasterDetail_ID,
                                                 Name = RefL.RefMasterDetail_Name
                                             }).Distinct()
                        };
                    }
                }


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }

        #endregion


        #region ::: SelectCallProirity Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select CallPriority
        /// </summary> 
        /// 
        public static IActionResult SelectCallPriority(HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj, string connString, int LogException)
        {
            var Masterdata = new object();
            try
            {
                string CompanyIDs = HelpDesk_Tr_AverageResponseSelectCustomerobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string refMasterName = "CALLPRIORITY";

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch CallPriority details using SP_GetCallPriorityDetails
                    List<GNM_RefMasterDetail> RefMasterDetails = new List<GNM_RefMasterDetail>();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponse_GetCallPriorityDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RefMaster_Name", refMasterName);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterDetails.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                                    RefMaster_ID = Convert.ToInt32(reader["RefMaster_ID"]),
                                    // Add other fields as necessary
                                });
                            }
                        }
                    }

                    if (HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResponseSelectCustomerobj.GeneralLanguageCode.ToString())
                    {
                        Masterdata = new
                        {
                            CallPriorityData = (from Ref in RefMasterDetails
                                                orderby Ref.RefMasterDetail_Name
                                                select new
                                                {
                                                    ID = Ref.RefMasterDetail_ID,
                                                    Name = Ref.RefMasterDetail_Name
                                                }).Distinct()
                        };
                    }
                    else
                    {
                        // Fetch localized CallPriority details using SP_GetCallPriorityDetailLocales
                        List<GNM_RefMasterDetailLocale> RefMasterDetailLocales = new List<GNM_RefMasterDetailLocale>();
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_Tr_AverageResponse_GetCallPriorityDetailLocales", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMaster_Name", refMasterName);
                            cmd.Parameters.AddWithValue("@Language_ID", Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectCustomerobj.UserLanguageID));
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocales.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                                        Language_ID = Convert.ToInt32(reader["Language_ID"]),
                                        // Add other fields as necessary
                                    });
                                }
                            }
                        }

                        Masterdata = new
                        {
                            CallPriorityData = (from Ref in RefMasterDetails
                                                join RefL in RefMasterDetailLocales on Ref.RefMasterDetail_ID equals RefL.RefMasterDetail_ID
                                                orderby RefL.RefMasterDetail_Name
                                                select new
                                                {
                                                    ID = RefL.RefMasterDetail_ID,
                                                    Name = RefL.RefMasterDetail_Name
                                                }).Distinct()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }

        #endregion



        #region ::: SelectDateDetails Uday kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Details
        /// </summary>
        /// 
        public static IActionResult SelectDateDetails(HelpDesk_Tr_AverageResponseSelectDateDetailsList HelpDesk_Tr_AverageResponseSelectDateDetailsobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonData = new object();
            try
            {
                int Count = 0, Total = 0;
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectDateDetailsobj.UserLanguageID);
                DateTime DDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseSelectDateDetailsobj.Date);
                IQueryable<AverageResponseTimeObject> IQAverageResponseTimeReport = null;
                DataTable resultTable = new DataTable();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand
                    {
                        Connection = conn,
                        CommandType = CommandType.StoredProcedure
                    };

                    if (HelpDesk_Tr_AverageResponseSelectDateDetailsobj.UserLanguageCode.ToString() == HelpDesk_Tr_AverageResponseSelectDateDetailsobj.GeneralLanguageCode.ToString())
                    {
                        cmd.CommandText = "SP_AMERP_HelpDesk_SelectDateDetailsForGeneralLanguage";
                        cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectDateDetailsobj.CompanyID));
                        cmd.Parameters.AddWithValue("@Date", DDate);
                    }
                    else
                    {
                        cmd.CommandText = "SP_AMERP_HelpDesk_SelectDateDetailsForUserLanguage";
                        cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(HelpDesk_Tr_AverageResponseSelectDateDetailsobj.CompanyID));
                        cmd.Parameters.AddWithValue("@Date", DDate);
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                    }

                    SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                    adapter.Fill(resultTable);
                }

                var data = resultTable.AsEnumerable().Select(row => new AverageResponseTimeObject
                {
                    SRNumber = $"<span class='ServiceRequest' key='{row["ServiceRequestID"]}' style='color:blue;text-decoration:underline;cursor:pointer'>{row["SRNumber"]}</span>",
                    Date = row["Date"] != DBNull.Value ? Convert.ToDateTime(row["Date"]).ToString("yyyy-MM-dd") : null, // Format the DateTime to string
                    CustomerName = row["CustomerName"]?.ToString(),
                    CustomerLocation = row["CustomerLocation"]?.ToString(),
                    Priority = row["Priority"]?.ToString(),
                    IssueArea = row["IssueArea"]?.ToString(),
                    Brand = row["Brand"]?.ToString(),
                    ProductType = row["ProductType"]?.ToString(),
                    ModelName = row["ModelName"]?.ToString(),
                    AverageTimestring = row["AverageTimestring"]?.ToString(),
                    Company_ID = (int)(row["Company_ID"] != DBNull.Value ? Convert.ToInt32(row["Company_ID"]) : (int?)null),
                    ServiceRequestID = (int)(row["ServiceRequestID"] != DBNull.Value ? Convert.ToInt32(row["ServiceRequestID"]) : (int?)null)
                }).ToList();

                // Use the data directly or convert it to IQueryable if needed
                IQAverageResponseTimeReport = data.AsQueryable();

                if (_search)
                {
                    Filters filterobj = JObject.Parse(Uri.UnescapeDataString(filters)).ToObject<Filters>();
                    IQAverageResponseTimeReport = IQAverageResponseTimeReport.FilterSearch<AverageResponseTimeObject>(filterobj);
                }
                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(Query)).ToObject<AdvanceFilter>();
                    IQAverageResponseTimeReport = IQAverageResponseTimeReport.AdvanceSearch<AverageResponseTimeObject>(advnfilter);
                }
                IQAverageResponseTimeReport = IQAverageResponseTimeReport.OrderByField<AverageResponseTimeObject>(sidx, sord);

                Count = IQAverageResponseTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                Count = IQAverageResponseTimeReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = Total,
                    page,
                    records = Count,
                    data = IQAverageResponseTimeReport
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion




        #region ::: Export Uday Kumar J B  15-11-2024:::
        /// <summary>
        /// Exporting Company Grid
        /// </summary>
        /// 
        public static async Task<object> Export(HelpDesk_Tr_AverageResponseTimeExportList HelpDesk_Tr_AverageResponseTimeExportobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            DataTable Dt;
            string ValueArray = string.Empty;
            string TextArray = string.Empty;
            int Mode = 0;
            DateTime FDate = DateTime.Now;
            DateTime TDate = DateTime.Now;
            int userID = HelpDesk_Tr_AverageResponseTimeExportobj.User_ID;
            int companyID = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.Company_ID);
            int branchID = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.Branch);
            try
            {
                string BranchName = string.Empty;
                Dt = new DataTable();
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Region").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Company").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Branch").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Casenumber").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "date").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Party").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "PartyLocation").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Priority").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "ISSUEAREA").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "brand").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "producttype").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "model").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "responsetime").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("CompanyName");
                DtAlignment.Columns.Add("BranchName");
                DtAlignment.Columns.Add("ServiceRequestNumber");
                DtAlignment.Columns.Add("date");
                DtAlignment.Columns.Add("customername");
                DtAlignment.Columns.Add("customerlocation");
                DtAlignment.Columns.Add("Priority");
                DtAlignment.Columns.Add("ISSUEAREA");
                DtAlignment.Columns.Add("brand");
                DtAlignment.Columns.Add("producttype");
                DtAlignment.Columns.Add("model");
                DtAlignment.Columns.Add("responsetime");
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1);

                TextArray = HelpDesk_Tr_AverageResponseTimeExportobj.HD_Tr_AverageResponseTime_TextArray.ToString();
                int Language_ID = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.LanguageID);
                int UserLang = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.GeneralLanguageID);
                DateTime DDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeExportobj.Date);
                //// Company_ID = Session["CompanyID"].ToString();//FSm CR 5
                FDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeExportobj.FromDate);
                TDate = Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeExportobj.ToDate);
                ValueArray = HelpDesk_Tr_AverageResponseTimeExportobj.HD_Tr_AverageResponseTime_ValueArray.ToString();
                Mode = Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.Mode);
                string BranchID = Convert.ToString(HelpDesk_Tr_AverageResponseTimeExportobj.HD_Tr_AverageResponseTime_Branch_ID);
                TDate = TDate.AddDays(1);
                string BranchIDs = HelpDesk_Tr_AverageResponseTimeExportobj.BranchIDs.ToString();
                string CompanyIDs = HelpDesk_Tr_AverageResponseTimeExportobj.CompanyIDs.ToString();
                string UserLanguageCode = HelpDesk_Tr_AverageResponseTimeExportobj.UserLanguageCode.ToString();
                string GeneralLanguageCode = HelpDesk_Tr_AverageResponseTimeExportobj.GeneralLanguageCode.ToString();
                var averageResponseList = new List<AverageResponseTimeObject>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetAverageResponseTimeReportExport", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        cmd.Parameters.AddWithValue("@FromDate", FDate);
                        cmd.Parameters.AddWithValue("@ToDate", TDate);
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                        cmd.Parameters.AddWithValue("@UserLanguageCode", UserLanguageCode);
                        cmd.Parameters.AddWithValue("@GeneralLanguageCode", GeneralLanguageCode);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                averageResponseList.Add(new AverageResponseTimeObject
                                {
                                    Region = reader["Region"].ToString(),
                                    CompanyName = reader["Company_Name"].ToString(),
                                    BranchName = reader["Branch_Name"].ToString(),
                                    ServiceRequestID = Convert.ToInt32(reader["ServiceRequestID"]),
                                    SRNumber = reader["SRNumber"].ToString(),
                                    CallDate = Convert.ToDateTime(reader["CallDate"]),
                                    CustomerName = reader["CustomerName"].ToString(),
                                    CustomerLocation = reader["CustomerLocation"].ToString(),
                                    Priority = reader["Priority"].ToString(),
                                    IssueArea = reader["IssueArea"].ToString(),
                                    Brand = reader["Brand"].ToString(),
                                    ProductType = reader["ProductType"].ToString(),
                                    ModelName = reader["ModelName"].ToString(),
                                    AverageTimestring = reader["AverageTimestring"].ToString()
                                });
                            }
                        }
                    }
                }



                var ServiceRequestArray = averageResponseList.ToList();
                int Count = ServiceRequestArray.Count;
                for (int i = 0; i < Count; i++)
                {
                    Dt.Rows.Add(ServiceRequestArray[i].Region, ServiceRequestArray[i].CompanyName, ServiceRequestArray[i].BranchName, ServiceRequestArray[i].SRNumber, ServiceRequestArray[i].CallDate.ToString("dd-MMM-yyyy"), ServiceRequestArray[i].CustomerName, ServiceRequestArray[i].CustomerLocation, ServiceRequestArray[i].Priority, ServiceRequestArray[i].IssueArea, ServiceRequestArray[i].Brand, ServiceRequestArray[i].ProductType, ServiceRequestArray[i].ModelName, ServiceRequestArray[i].AverageTimestring);
                }

                DataSet ds = new DataSet();

                DataTable DtDetails = new DataTable();

                if (ValueArray != "Blank")
                {
                    if (Mode == 1)
                    {
                        DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Priority").ToString());
                    }
                    else if (Mode == 2)
                    {
                        DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "model").ToString());
                    }
                    else if (Mode == 3)
                    {
                        DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Customer").ToString());
                    }
                    else if (Mode == 4)
                    {
                        DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "issuearea").ToString());
                    }
                    else if (Mode == 5)
                    {
                        DtDetails.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "Dealer").ToString());
                    }
                    DtDetails.Rows.Add(TextArray.TrimEnd(new char[] { ',' }).ToString());
                    ds.Tables.Add(DtDetails);
                }
                DataTable DateRange = new DataTable();
                //DateRange.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Branch").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "fromdate").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "todate").ToString());

                DateRange.Rows.Add(Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeExportobj.FromDate).ToString("dd-MMM-yyyy"), Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeExportobj.ToDate).ToString("dd-MMM-yyyy"));
                ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                {
                    FileName = "AverageResponseTime",
                    Branch = Convert.ToString(HelpDesk_Tr_AverageResponseTimeExportobj.Branch),
                    Company_ID = HelpDesk_Tr_AverageResponseTimeExportobj.Company_ID,
                    UserCulture = HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture,
                    dt = Dt, // You can populate this with actual data as needed
                    exprtType = HelpDesk_Tr_AverageResponseTimeExportobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                    Header = CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "averageresponsetime").ToString(),
                    Options = DateRange, // Populate this with your report options
                    selection = ds, // Add selection-related data here
                    Alignment = DtAlignment // Define alignment details for table columns
                };
                var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                return result.Value;
                // ReportExportCR5.Export(HelpDesk_Tr_AverageResponseTimeExportobj.exprtType, Dt, DateRange, ds, DtAlignment, "AverageResponseTime", CommonFunctionalities.GetResourceString(HelpDesk_Tr_AverageResponseTimeExportobj.UserCulture.ToString(), "averageresponsetime").ToString());
                //  gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.Company_ID.ToString()), branchID, HelpDesk_Tr_AverageResponseTimeExportobj.User_ID, Common.GetObjectID("HelpDesk_Tr_AverageResponseTime"), 0, 0, 0, "Average Response Time-Export ", false, Convert.ToInt32(HelpDesk_Tr_AverageResponseTimeExportobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_AverageResponseTimeExportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return false;
        }
        #endregion





        #region ::: HelpDesk_Tr_AverageResponse List and obj Classes Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// classes
        /// </summary> 
        /// 

        public class HelpDesk_Tr_AverageResponseTimeExportList
        {

            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string UserCulture { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public int LanguageID { get; set; }
            public int Mode { get; set; }
            public string HD_Tr_AverageResponseTime_TextArray { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string Date { get; set; }
            public string HD_Tr_AverageResponseTime_FromDate { get; set; }
            public string HD_Tr_AverageResponseTime_ToDate { get; set; }
            public string HD_Tr_AverageResponseTime_ValueArray { get; set; }
            public string HD_Tr_AverageResponseTime_Mode { get; set; }
            public string HD_Tr_AverageResponseTime_Branch_ID { get; set; }
            public string BranchIDs { get; set; }
            public string CompanyIDs { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
            public int exprtType { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }
        public class HelpDesk_Tr_AverageResponseSelectDateDetailsList
        {
            public int UserLanguageID { get; set; }
            public string Date { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string CompanyID { get; set; }

        }

        public class HelpDesk_Tr_AverageResponseSelectCustomerList
        {
            public string CompanyIDs { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
            public int Branch { get; set; }

        }

        public class HelpDesk_Tr_AverageResponseTimeSelectList
        {

            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public int Mode { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public string ValueArray { get; set; }
            public string TextArray { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string BranchName { get; set; }
            public int Year { get; set; }
            public int Month { get; set; }
            public string UserCulture { get; set; }


        }
        #endregion


        #region ::: HelpDesk_Tr_AverageResponse  Classes Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// classes
        /// </summary> 
        /// 
        public partial class GNM_ModelLocale
        {
            public int ModelLocale_ID { get; set; }
            public int Model_ID { get; set; }
            public string Model_Name { get; set; }
            public int Language_ID { get; set; }
            public string Model_Description { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_CompanyBrands
        {
            public int Company_Brand_ID { get; set; }
            public int Company_ID { get; set; }
            public int Brand_ID { get; set; }
        }

        public partial class GNM_Model
        {
            public int Model_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string Model_Name { get; set; }
            public bool Model_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> ServiceFrequency { get; set; }
            public string Model_Description { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public string Series { get; set; }
        }
        public partial class GNM_Branch
        {
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public string Branch_Name { get; set; }
            public string Branch_ShortName { get; set; }
            public string Branch_ZipCode { get; set; }
            public int Country_ID { get; set; }
            public int State_ID { get; set; }
            public string Branch_Phone { get; set; }
            public string Branch_Fax { get; set; }
            public bool Branch_HeadOffice { get; set; }
            public bool Branch_Active { get; set; }
            public string Branch_Address { get; set; }
            public string Branch_Location { get; set; }
            public string Branch_Email { get; set; }
            public string Branch_Mobile { get; set; }
            public Nullable<bool> Branch_External { get; set; }
            public Nullable<int> TimeZoneID { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public Nullable<int> Currency_ID { get; set; }
            public Nullable<int> LanguageID { get; set; }
            public Nullable<byte> IsOverTimeDWM { get; set; }
            public Nullable<decimal> Yearly_Sales_Target { get; set; }
            public Nullable<decimal> Rework_Target { get; set; }
            public Nullable<decimal> Cust_Satisfaction_Target { get; set; }
            public Nullable<decimal> RO_with_Rework_Target { get; set; }
            public Nullable<decimal> RO_with_Cust_Satisfaction_Target { get; set; }
            public Nullable<int> DueDays { get; set; }
            public Nullable<int> PayrollSystem_ID { get; set; }
            public Nullable<int> MaxCarryOverHours { get; set; }
            public Nullable<System.DateTime> ConsumedCarryOverByDate { get; set; }
            public Nullable<bool> IsHourlyRateBranchWise { get; set; }
            public Nullable<int> PayrollFileType_ID { get; set; }
            public Nullable<decimal> Yearly_Parts_Target { get; set; }
            public Nullable<decimal> Variance_Percentage { get; set; }
            public Nullable<decimal> Variance_Value { get; set; }
            public Nullable<int> ETOExtensionHours { get; set; }
            public Nullable<int> ETOMultiples { get; set; }
            public Nullable<decimal> BilledVsActualVariance_Percentage { get; set; }
            public Nullable<byte> TypeofPayroll { get; set; }
            public Nullable<decimal> LessVariance_Percentage { get; set; }
            public Nullable<decimal> LessVariance_Value { get; set; }
            public Nullable<decimal> IIMoreVariance_Percentage { get; set; }
            public Nullable<decimal> IIMoreVariance_Value { get; set; }
            public Nullable<decimal> IILessVariance_Percentage { get; set; }
            public Nullable<decimal> IILessVariance_Value { get; set; }
            public Nullable<int> WorkingMinutes { get; set; }
        }

        public class AverageResponseTimeObject
        {
            public string CustomerName { get; set; }
            public string Brand { get; set; }
            public string Model { get; set; }
            public string Priority { get; set; }
            public string SRNumber { get; set; }
            public string CustomerLocation { get; set; }
            public int ServiceRequestID { get; set; }
            public DateTime ServiceRequestDate { get; set; }
            public DateTime CallDate { get; set; }
            public DateTime? CallClosureDate { get; set; }
            public double AverageTime { get; set; }
            public int AverageTimeMinutes { get; set; }
            public string AverageTimestring { get; set; }
            public int Year { get; set; }
            public string Month { get; set; }
            public string MonthName { get; set; }
            public int Hours { get; set; }
            public int Minutes { get; set; }
            public string Date { get; set; }
            public int SRCount { get; set; }
            public string IssueArea { get; set; }
            public string ModelName { get; set; }
            public string ProductType { get; set; }
            public DateTime SCallDate { get; set; }
            public int Party_ID { get; set; }
            public int IssueArea_ID { get; set; }
            public int? Brand_ID { get; set; }
            public int? ProductType_ID { get; set; }
            public int? Model_ID { get; set; }
            public byte Service_Priority { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
            public string Region { get; set; }
        }
        #endregion

    }
}
