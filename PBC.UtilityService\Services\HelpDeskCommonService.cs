using PBC.UtilityService.Utilities;
using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Service implementation for Help Desk Common operations
    /// </summary>
    public class HelpDeskCommonService : IHelpDeskCommonService
    {
        private readonly ILogger<HelpDeskCommonService> _logger;

        public HelpDeskCommonService(ILogger<HelpDeskCommonService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Gets the end step status ID for a workflow
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable/disable exception logging</param>
        /// <returns>End step status ID</returns>
        public async Task<int> GetEndStepStatusIDAsync(int workflowId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Getting end step status ID for workflow: {WorkflowId}", workflowId);
                
                // Execute the static method asynchronously
                await Task.Delay(1); // Simulate async operation
                var result = HelpDeskCommon.GetEndStepStatusID(workflowId, connectionString, logException);
                
                _logger.LogInformation("Successfully retrieved end step status ID: {StatusId} for workflow: {WorkflowId}", result, workflowId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting end step status ID for workflow: {WorkflowId}", workflowId);
                throw;
            }
        }

        /// <summary>
        /// Gets the end step status name for a workflow
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable/disable exception logging</param>
        /// <returns>End step status name</returns>
        public async Task<string> GetEndStepStatusNameAsync(int workflowId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Getting end step status name for workflow: {WorkflowId}", workflowId);
                
                // Execute the static method asynchronously
                await Task.Delay(1); // Simulate async operation
                var result = HelpDeskCommon.GetEndStepStatusName(workflowId, connectionString, logException);
                
                _logger.LogInformation("Successfully retrieved end step status name: {StatusName} for workflow: {WorkflowId}", result, workflowId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting end step status name for workflow: {WorkflowId}", workflowId);
                throw;
            }
        }
    }
}
