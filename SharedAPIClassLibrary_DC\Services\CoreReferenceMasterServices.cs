﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreReferenceMasterServices
    {
        static string AppPath = string.Empty;

        #region ::: Landing Grid /Mithun:::
        /// <summary>
        ///  Landing Grid
        /// </summary>
        /// <returns></returns>

        public static IActionResult SelectReferenceMaster(SelectCoreReferenceMasterList SelectReferenceMasterObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonData = new object();
            try
            {
                int count = 0;
                int total = 0;

                List<ReferenceMaster> referenceMasterList = new List<ReferenceMaster>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string storedProcedure = "[dbo].[UP_SEL_AMERP_GetCustomRefMasters]";
                    SqlCommand cmd = new SqlCommand(storedProcedure, conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            referenceMasterList.Add(new ReferenceMaster
                            {
                                RefMaster_ID = (int)reader["RefMaster_ID"],
                                RefMaster_Name = (string)reader["RefMaster_Name"],
                                IsCompanySpecific = (bool)reader["IsCompanySpecific"] ? "Yes" : "No"
                            });
                        }
                    }
                }

                IQueryable<ReferenceMaster> gnmPrtySpcl = referenceMasterList.AsQueryable();

                // FilterToolBar Search

                if (_search)
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    gnmPrtySpcl = gnmPrtySpcl.FilterSearch(filtersobj);
                }
                // Advance Search
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    gnmPrtySpcl = gnmPrtySpcl.AdvanceSearch(advnfilter);
                }

                // Sorting
                gnmPrtySpcl = gnmPrtySpcl.OrderByField(sidx, sord);

                // Pagination
                count = gnmPrtySpcl.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                string Lbl_Refresh = CommonFunctionalities.GetResourceString(SelectReferenceMasterObj.UserCulture.ToString(), "refresh").ToString();
                string Lbl_AdvanceSearch = CommonFunctionalities.GetResourceString(SelectReferenceMasterObj.UserCulture.ToString(), "advancesearch").ToString();

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = gnmPrtySpcl.Skip((page - 1) * rows).Take(rows).Select(q => new
                    {
                        edit = $"<a title='{CommonFunctionalities.GetResourceString(SelectReferenceMasterObj.UserCulture.ToString(), "view")}' href='#' key='{q.RefMaster_ID}' Name='{q.RefMaster_Name}' class='edtClick font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = $"<input key='{q.RefMaster_ID}' type='checkbox' defaultchecked='' class='chkClick' />",
                        q.RefMaster_ID,
                        q.RefMaster_Name,
                        q.IsCompanySpecific,
                    }).ToList(),
                    Lbl_Refresh,
                    Lbl_AdvanceSearch
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SaveMasterDetail /Mithun:::
        /// <summary>
        /// Save Master Detail
        /// </summary>
        /// <returns></returns>

        public static IActionResult SaveMasterDetail(SaveMasterDetailList SaveMasterDetailObj, string constring, int LogException)
        {
            var jsonData = new { IsSuccess = false, ID = 0 };
            try
            {
                // Parse the JSON object from the request
                JObject jObj = JObject.Parse(SaveMasterDetailObj.key);
                GNM_RefMaster gnmRefMstr = jObj.ToObject<GNM_RefMaster>();

                int? CompID = Convert.ToInt32(SaveMasterDetailObj.Company_ID);
                int rowcount = gnmRefMstr.GNM_RefMasterDetail.Count;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            // Modify details
                            for (int i = 0; i < rowcount; i++)
                            {
                                var detail = gnmRefMstr.GNM_RefMasterDetail.ElementAt(i);
                                detail.RefMasterDetail_Short_Name = Common.DecryptString(detail.RefMasterDetail_Short_Name);
                                detail.RefMasterDetail_Name = Common.DecryptString(detail.RefMasterDetail_Name);
                                detail.ModifiedBy = Convert.ToInt32(SaveMasterDetailObj.User_ID);
                                detail.ModifiedDate = ValidateSqlDateTime(DateTime.Now);
                                detail.Company_ID = gnmRefMstr.IsCompanySpecific == false ? null : CompID;
                            }

                            gnmRefMstr.ModifiedBy = Convert.ToInt32(SaveMasterDetailObj.User_ID);
                            gnmRefMstr.ModifiedDate = ValidateSqlDateTime(DateTime.Now);

                            if (gnmRefMstr.RefMaster_ID == 0)
                            {
                                // Create XML for master record
                                string masterXml = $@"
                        <data>
                            <RefMaster_Name>{gnmRefMstr.RefMaster_Name}</RefMaster_Name>
                            <IsCompanySpecific>{(gnmRefMstr.IsCompanySpecific ?? false ? 1 : 0)}</IsCompanySpecific>
                            <ModifiedBy>{gnmRefMstr.ModifiedBy}</ModifiedBy>
                            <ModifiedDate>{gnmRefMstr.ModifiedDate:yyyy-MM-ddTHH:mm:ss}</ModifiedDate>
                            <IsSystemMaster>{(gnmRefMstr.IsSystemMaster ? 1 : 0)}</IsSystemMaster>
                        </data>";

                                string insertMasterProc = "Up_INS_AM_ERP_InsertGNM_RefMaster_XML";
                                using (SqlCommand cmd = new SqlCommand(insertMasterProc, conn, transaction))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@InputData", masterXml);

                                    SqlParameter refMasterIDParam = new SqlParameter("@RefMaster_ID", SqlDbType.Int)
                                    {
                                        Direction = ParameterDirection.Output
                                    };
                                    cmd.Parameters.Add(refMasterIDParam);

                                    cmd.ExecuteNonQuery();

                                    gnmRefMstr.RefMaster_ID = (int)refMasterIDParam.Value;
                                }

                                // Insert details
                                foreach (var detail in gnmRefMstr.GNM_RefMasterDetail)
                                {
                                    string detailXml = $@"
                            <data>
                                <RefMasterDetail_IsActive>{(detail.RefMasterDetail_IsActive ? 1 : 0)}</RefMasterDetail_IsActive>
                                <RefMasterDetail_Short_Name>{detail.RefMasterDetail_Short_Name}</RefMasterDetail_Short_Name>
                                <RefMasterDetail_Name>{detail.RefMasterDetail_Name}</RefMasterDetail_Name>
                                <Company_ID>{(object)detail.Company_ID ?? DBNull.Value}</Company_ID>
                                <ModifiedBy>{detail.ModifiedBy}</ModifiedBy>
                                <ModifiedDate>{detail.ModifiedDate:yyyy-MM-ddTHH:mm:ss}</ModifiedDate>
                                <RefMaster_ID>{gnmRefMstr.RefMaster_ID}</RefMaster_ID>
                                <RefMasterDetail_IsDefault>{(detail.RefMasterDetail_IsDefault ? 1 : 0)}</RefMasterDetail_IsDefault>
                                <Region_ID>{(object)detail.Region_ID ?? DBNull.Value}</Region_ID>
                                <SystemCondition>{detail.SystemCondition}</SystemCondition>
                            </data>";

                                    string insertDetailProc = "Up_INS_AM_ERP_InsertGNM_RefMasterDetail_XML";
                                    using (SqlCommand cmd = new SqlCommand(insertDetailProc, conn, transaction))
                                    {
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.Parameters.AddWithValue("@InputData", detailXml);

                                        cmd.ExecuteNonQuery();
                                    }
                                }

                                //gbl.InsertGPSDetails(Convert.ToInt32(SaveMasterDetailObj.Company_ID), Convert.ToInt32(SaveMasterDetailObj.Branch), Convert.ToInt32(SaveMasterDetailObj.User_ID),
                                //    Convert.ToInt32(Common.GetObjectID("Master",constring)), gnmRefMstr.RefMaster_ID, 0, 0, "Inserted", false,
                                //    Convert.ToInt32(SaveMasterDetailObj.MenuID), Convert.ToDateTime(SaveMasterDetailObj.LoggedINDateTime));
                            }
                            else
                            {
                                // Create XML for master record update
                                string masterXml = $@"
                        <data>
                            <RefMaster_ID>{gnmRefMstr.RefMaster_ID}</RefMaster_ID>
                            <RefMaster_Name>{gnmRefMstr.RefMaster_Name}</RefMaster_Name>
                            <ModifiedBy>{gnmRefMstr.ModifiedBy}</ModifiedBy>
                            <ModifiedDate>{gnmRefMstr.ModifiedDate:yyyy-MM-ddTHH:mm:ss}</ModifiedDate>
                            <IsSystemMaster>{(gnmRefMstr.IsSystemMaster ? 1 : 0)}</IsSystemMaster>
                        </data>";

                                string updateMasterProc = "Up_UPD_AM_ERP_UpdateGNM_RefMaster_XML";
                                using (SqlCommand cmd = new SqlCommand(updateMasterProc, conn, transaction))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@InputData", masterXml);

                                    cmd.ExecuteNonQuery();
                                }

                                // Update or insert details
                                foreach (var detail in gnmRefMstr.GNM_RefMasterDetail)
                                {
                                    if (detail.RefMasterDetail_ID == 0)
                                    {
                                        string detailXml = $@"
                                <data>
                                    <RefMasterDetail_IsActive>{(detail.RefMasterDetail_IsActive ? 1 : 0)}</RefMasterDetail_IsActive>
                                    <RefMasterDetail_Short_Name>{detail.RefMasterDetail_Short_Name}</RefMasterDetail_Short_Name>
                                    <RefMasterDetail_Name>{detail.RefMasterDetail_Name}</RefMasterDetail_Name>
                                    <Company_ID>{(object)detail.Company_ID ?? DBNull.Value}</Company_ID>
                                    <ModifiedBy>{detail.ModifiedBy}</ModifiedBy>
                                    <ModifiedDate>{detail.ModifiedDate:yyyy-MM-ddTHH:mm:ss}</ModifiedDate>
                                    <RefMaster_ID>{gnmRefMstr.RefMaster_ID}</RefMaster_ID>
                                    <RefMasterDetail_IsDefault>{(detail.RefMasterDetail_IsDefault ? 1 : 0)}</RefMasterDetail_IsDefault>
                                    <Region_ID>{(object)detail.Region_ID ?? DBNull.Value}</Region_ID>
                                    <SystemCondition>{detail.SystemCondition}</SystemCondition>
                                </data>";

                                        string insertDetailProc = "Up_INS_AM_ERP_InsertGNM_RefMasterDetail_XML";
                                        using (SqlCommand cmd = new SqlCommand(insertDetailProc, conn, transaction))
                                        {
                                            cmd.CommandType = CommandType.StoredProcedure;
                                            cmd.Parameters.AddWithValue("@InputData", detailXml);

                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                    else
                                    {
                                        string detailXml = $@"
                                <data>
                                    <RefMasterDetail_ID>{detail.RefMasterDetail_ID}</RefMasterDetail_ID>
                                    <RefMasterDetail_IsActive>{(detail.RefMasterDetail_IsActive ? 1 : 0)}</RefMasterDetail_IsActive>
                                    <RefMasterDetail_Short_Name>{detail.RefMasterDetail_Short_Name}</RefMasterDetail_Short_Name>
                                    <RefMasterDetail_Name>{detail.RefMasterDetail_Name}</RefMasterDetail_Name>
                                    <Company_ID>{(object)detail.Company_ID ?? DBNull.Value}</Company_ID>
                                    <ModifiedBy>{detail.ModifiedBy}</ModifiedBy>
                                    <ModifiedDate>{detail.ModifiedDate:yyyy-MM-ddTHH:mm:ss}</ModifiedDate>
                                    <RefMasterDetail_IsDefault>{(detail.RefMasterDetail_IsDefault ? 1 : 0)}</RefMasterDetail_IsDefault>
                                    <Region_ID>{(object)detail.Region_ID ?? DBNull.Value}</Region_ID>
                                    <SystemCondition>{detail.SystemCondition}</SystemCondition>
                                </data>";

                                        string updateDetailProc = "Up_UPD_AM_ERP_UpdateGNM_RefMasterDetail_XML";
                                        using (SqlCommand cmd = new SqlCommand(updateDetailProc, conn, transaction))
                                        {
                                            cmd.CommandType = CommandType.StoredProcedure;
                                            cmd.Parameters.AddWithValue("@InputData", detailXml);

                                            cmd.ExecuteNonQuery();
                                        }

                                        if (detail.RefMasterDetail_IsDefault)
                                        {
                                            string resetDefaultProc = "Up_UPD_AM_ERP_ResetGNM_RefMasterDetailDefault";
                                            using (SqlCommand cmd = new SqlCommand(resetDefaultProc, conn, transaction))
                                            {
                                                cmd.CommandType = CommandType.StoredProcedure;
                                                cmd.Parameters.AddWithValue("@RefMaster_ID", gnmRefMstr.RefMaster_ID);

                                                cmd.ExecuteNonQuery();
                                            }

                                            detail.RefMasterDetail_IsDefault = true;
                                        }
                                    }
                                }

                                //gbl.InsertGPSDetails(Convert.ToInt32(SaveMasterDetailObj.Company_ID), Convert.ToInt32(SaveMasterDetailObj.Branch), Convert.ToInt32(SaveMasterDetailObj.User_ID),
                                //                                   Convert.ToInt32(Common.GetObjectID("Master", constring)), gnmRefMstr.RefMaster_ID, 0, 0, "Updated", false,
                                //                                   Convert.ToInt32(SaveMasterDetailObj.MenuID), Convert.ToDateTime(SaveMasterDetailObj.LoggedINDateTime));
                            }

                            transaction.Commit();

                            jsonData = new
                            {
                                IsSuccess = true,
                                ID = gnmRefMstr.RefMaster_ID
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();

                            if (LogException == 0)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                            jsonData = new
                            {
                                IsSuccess = false,
                                ID = 0
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                jsonData = new
                {
                    IsSuccess = false,
                    ID = 0
                };
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        private static DateTime ValidateSqlDateTime(DateTime dateTime)
        {
            if (dateTime < (DateTime)System.Data.SqlTypes.SqlDateTime.MinValue)
            {
                return (DateTime)System.Data.SqlTypes.SqlDateTime.MinValue;
            }
            if (dateTime > (DateTime)System.Data.SqlTypes.SqlDateTime.MaxValue)
            {
                return (DateTime)System.Data.SqlTypes.SqlDateTime.MaxValue;
            }
            return dateTime;
        }


        #endregion

        #region ::: Detail Grid /Mithun:::
        /// <summary>
        /// Detail Grid
        /// </summary>
        /// <returns></returns>
        public static IActionResult MasterDetailData(MasterDetailDataList MasterDetailDataObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var refid = MasterDetailDataObj.id;
            var jsonData = new object();
            try
            {
                int Company_ID = Convert.ToInt32(MasterDetailDataObj.Company_ID);

                // Get GNM_RefMaster
                GNM_RefMaster gnmRefMaster = null;
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "SELECT * FROM GNM_RefMaster WHERE RefMaster_ID = @RefMaster_ID";
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.Parameters.AddWithValue("@RefMaster_ID", MasterDetailDataObj.id);
                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            gnmRefMaster = new GNM_RefMaster
                            {
                                RefMaster_ID = (int)reader["RefMaster_ID"],
                                RefMaster_Name = (string)reader["RefMaster_Name"],
                                IsCompanySpecific = (bool)reader["IsCompanySpecific"]

                            };
                        }
                    }
                }

                if (gnmRefMaster == null)
                {
                    // Handle the case where gnmRefMaster is not found
                    //return Json(new { error = "RefMaster not found" }, JsonRequestBehavior.AllowGet);
                    return new JsonResult(new { error = "RefMaster not found" });
                }

                // Get GNM_RefMasterDetail
                List<GNM_RefMasterDetail> gnmRefMstrDtlList = new List<GNM_RefMasterDetail>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = (bool)gnmRefMaster.IsCompanySpecific ?
                        "SELECT * FROM GNM_RefMasterDetail WHERE RefMaster_ID = @RefMaster_ID AND Company_ID = @Company_ID" :
                        "SELECT * FROM GNM_RefMasterDetail WHERE RefMaster_ID = @RefMaster_ID";
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.Parameters.AddWithValue("@RefMaster_ID", MasterDetailDataObj.id);
                    if ((bool)gnmRefMaster.IsCompanySpecific)
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                    }
                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            gnmRefMstrDtlList.Add(new GNM_RefMasterDetail
                            {
                                RefMasterDetail_ID = (int)reader["RefMasterDetail_ID"],
                                RefMasterDetail_Short_Name = ((string)reader["RefMasterDetail_Short_Name"]),
                                RefMasterDetail_Name = ((string)reader["RefMasterDetail_Name"]),
                                RefMasterDetail_IsActive = (bool)reader["RefMasterDetail_IsActive"],
                                RefMasterDetail_IsDefault = (bool)reader["RefMasterDetail_IsDefault"]

                            });
                        }
                    }
                }

                IQueryable<GNM_RefMasterDetail> gnmRefMstrDtl = gnmRefMstrDtlList.AsQueryable();

                // FilterToolBar Search

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        gnmRefMstrDtl = gnmRefMstrDtl.FilterSearch(filtersObj);
                }
                // Advance Search

                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    gnmRefMstrDtl = gnmRefMstrDtl.AdvanceSearch(advnfilter);
                }

                // Sorting
                gnmRefMstrDtl = gnmRefMstrDtl.OrderByField(sidx, sord);

                // Pagination
                int count = gnmRefMstrDtl.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    Master = gnmRefMaster.RefMaster_Name,
                    IsCompanySpecific = gnmRefMaster.IsCompanySpecific,
                    rows = gnmRefMstrDtl.Skip((page - 1) * rows).Take(rows).Select(q => new
                    {
                        Detailid = q.RefMasterDetail_ID,
                        RefMasterDetail_Short_Name = q.RefMasterDetail_Short_Name,
                        RefMasterDetail_Name = q.RefMasterDetail_Name,
                        RefMasterDetail_IsActive = q.RefMasterDetail_IsActive ? "Yes" : "No",
                        RefMasterDetail_IsDefault = q.RefMasterDetail_IsDefault ? "Yes" : "No",
                        edit = $"<a title='view' href='#' key='{q.RefMasterDetail_ID}' class='edtDtlClick font-icon-class' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = $"<input key='{q.RefMasterDetail_ID}' type='checkbox' defaultchecked='' class='chkdtlClick' />",
                        locale = $"<a key='{q.RefMasterDetail_ID}' src='{AppPath}/Content/local.png' class='locale' alt='Localize' width='20' height='20' title='Localize' ><i class='fa fa-globe'></i></a>"
                    }).ToList(),
                    refid = refid,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }




        #endregion

        #region ::: MasterDetailLocaleData /Mithun:::
        /// <summary>
        /// Master Detail Locale Data
        /// </summary>
        /// <returns></returns>
        public static IActionResult MasterDetailLocaleData(MasterDetailLocaleDataList MasterDetailLocaleDataObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonData = default(dynamic);
            try
            {
                int count = 0;
                int total = 0;
                int companyID = Convert.ToInt32(MasterDetailLocaleDataObj.Company_ID);
                int languageID = Convert.ToInt32(MasterDetailLocaleDataObj.LanguageID);
                bool isCompanySpecific = false;
                string refMasterName = "";

                List<GNM_RefMasterDetailLocale> details = new List<GNM_RefMasterDetailLocale>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Retrieve GNM_RefMaster
                    using (SqlCommand cmd = new SqlCommand("SELECT IsCompanySpecific, RefMaster_Name FROM GNM_RefMaster WHERE RefMaster_ID = @RefMaster_ID", conn))
                    {
                        cmd.Parameters.AddWithValue("@RefMaster_ID", MasterDetailLocaleDataObj.id);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                isCompanySpecific = Convert.ToBoolean(reader["IsCompanySpecific"]);
                                refMasterName = reader["RefMaster_Name"].ToString();
                            }
                        }
                    }

                    // Retrieve GNM_RefMasterDetailLocale
                    string query = isCompanySpecific
                        ? "SELECT RefMasterDetail_ID, RefMasterDetail_Short_Name, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMaster_ID AND Company_ID = @Company_ID AND Language_ID = @Language_ID"
                        : "SELECT RefMasterDetail_ID, RefMasterDetail_Short_Name, RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMaster_ID = @RefMaster_ID AND Language_ID = @Language_ID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RefMaster_ID", MasterDetailLocaleDataObj.id);
                        cmd.Parameters.AddWithValue("@Language_ID", languageID);
                        if (isCompanySpecific)
                        {
                            cmd.Parameters.AddWithValue("@Company_ID", companyID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                details.Add(new GNM_RefMasterDetailLocale
                                {
                                    RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    RefMasterDetail_Short_Name = (reader["RefMasterDetail_Short_Name"].ToString()),
                                    RefMasterDetail_Name = (reader["RefMasterDetail_Name"].ToString())
                                });
                            }
                        }
                    }
                }

                IQueryable<GNM_RefMasterDetailLocale> queryableDetails = details.AsQueryable();
                // Filter ToolBar Search

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        queryableDetails = queryableDetails.FilterSearch(filtersObj);
                }
                // Advance Search

                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    queryableDetails = queryableDetails.AdvanceSearch(advnfilter);
                }

                // Sorting
                //details = details.OrderByField(sidx, sord).ToList();

                count = details.Count();
                total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                if (languageID != Convert.ToInt32(MasterDetailLocaleDataObj.GeneralLanguageID))
                {
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(MasterDetailLocaleDataObj.Company_ID),
                    //    Convert.ToInt32(MasterDetailLocaleDataObj.Branch),
                    //    Convert.ToInt32(MasterDetailLocaleDataObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("Master",constring)),
                    //    MasterDetailLocaleDataObj.id, 0, 0, "Viewed: " + refMasterName, false,
                    //    Convert.ToInt32(MasterDetailLocaleDataObj.MenuID), Convert.ToDateTime(MasterDetailLocaleDataObj.LoggedINDateTime));
                }

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    Master = refMasterName,
                    isCompanySpecific,
                    rows = details.Skip((page - 1) * rows).Take(rows).ToList()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: MasterDetailLocale /Mithun:::
        /// <summary>
        /// MasterDetailLocale
        /// </summary>
        /// <returns></returns>

        public static IActionResult MasterDetailLocale(MasterDetailLocaleList MasterDetailLocaleObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand();
                    command.Connection = connection;

                    // Retrieve Master Detail Locale
                    string query = "SELECT * FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID = @Id";
                    command.CommandText = query;
                    command.Parameters.AddWithValue("@Id", MasterDetailLocaleObj.id);
                    SqlDataReader reader = command.ExecuteReader();

                    GNM_RefMasterDetailLocale mstlcl = null;
                    if (reader.Read())
                    {
                        mstlcl = new GNM_RefMasterDetailLocale
                        {
                            RefMasterDetailLocale_ID = Convert.ToInt32(reader["RefMasterDetailLocale_ID"]),
                            RefMasterDetail_Short_Name = reader["RefMasterDetail_Short_Name"].ToString(),
                            RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                        };
                    }
                    reader.Close();

                    // Retrieve Master Detail
                    query = "SELECT * FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @Id";
                    command.CommandText = query;
                    reader = command.ExecuteReader();

                    GNM_RefMasterDetail mstdtl = null;
                    if (reader.Read())
                    {
                        mstdtl = new GNM_RefMasterDetail
                        {
                            RefMasterDetail_Short_Name = reader["RefMasterDetail_Short_Name"].ToString(),
                            RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                            RefMasterDetail_IsActive = Convert.ToBoolean(reader["RefMasterDetail_IsActive"]),
                            RefMasterDetail_IsDefault = Convert.ToBoolean(reader["RefMasterDetail_IsDefault"])
                        };
                    }
                    reader.Close();

                    if (mstlcl != null)
                    {
                        jsonData = new
                        {
                            code = mstdtl.RefMasterDetail_Short_Name,
                            desc = mstdtl.RefMasterDetail_Name,
                            lclid = mstlcl.RefMasterDetailLocale_ID,
                            lclcode = mstlcl.RefMasterDetail_Short_Name,
                            lcldesc = mstlcl.RefMasterDetail_Name,
                            ActiveG = mstdtl.RefMasterDetail_IsActive,
                            DefaultG = mstdtl.RefMasterDetail_IsDefault
                        };
                    }
                    else
                    {
                        jsonData = new
                        {
                            code = mstdtl.RefMasterDetail_Short_Name,
                            desc = mstdtl.RefMasterDetail_Name,
                            ActiveG = mstdtl.RefMasterDetail_IsActive,
                            DefaultG = mstdtl.RefMasterDetail_IsDefault,
                            lclid = "",
                            lclcode = "",
                            lcldesc = "",
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: LocaleUpdate /Mithun:::
        /// <summary>
        /// Locale Update
        /// </summary>
        /// <param name="LocaleUpdateObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult LocaleUpdate(LocaleUpdateList LocaleUpdateObj, string constring, int LogException)
        {
            var jsonData = new
            {
                IsSuccess = true
            };
            try
            {
                JObject jObj = JObject.Parse(LocaleUpdateObj.key);

                int refMasterDetailLocale_ID;
                using (var jr = new JTokenReader(jObj["dtlid"]))
                {
                    jr.Read();
                    refMasterDetailLocale_ID = jr.Value == "" ? 0 : Convert.ToInt32(jr.Value);
                }

                if (refMasterDetailLocale_ID == 0)
                {
                    string refMasterDetail_Short_Name, refMasterDetail_Name;
                    int refMasterDetail_ID, language_ID;

                    using (var jr = new JTokenReader(jObj["code"]))
                    {
                        jr.Read();
                        refMasterDetail_Short_Name = Common.DecryptString(jr.Value.ToString());
                    }
                    using (var jr = new JTokenReader(jObj["desc"]))
                    {
                        jr.Read();
                        refMasterDetail_Name = Common.DecryptString(jr.Value.ToString());
                    }
                    using (var jr = new JTokenReader(jObj["id"]))
                    {
                        jr.Read();
                        refMasterDetail_ID = Convert.ToInt32(jr.Value);
                    }
                    language_ID = Convert.ToInt32(LocaleUpdateObj.UserLanguageID);

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("INSERT INTO GNM_RefMasterDetailLocale (RefMasterDetail_Short_Name, RefMasterDetail_Name, RefMasterDetail_ID, Language_ID, RefMaster_ID) VALUES (@ShortName, @Name, @DetailID, @LanguageID, @MasterID)", conn))
                        {
                            cmd.Parameters.AddWithValue("@ShortName", refMasterDetail_Short_Name);
                            cmd.Parameters.AddWithValue("@Name", refMasterDetail_Name);
                            cmd.Parameters.AddWithValue("@DetailID", refMasterDetail_ID);
                            cmd.Parameters.AddWithValue("@LanguageID", language_ID);
                            cmd.Parameters.AddWithValue("@MasterID", LocaleUpdateObj.mid);

                            cmd.ExecuteNonQuery();
                        }
                    }
                }
                else
                {
                    string refMasterDetail_Short_Name, refMasterDetail_Name;
                    using (var jr = new JTokenReader(jObj["code"]))
                    {
                        jr.Read();
                        refMasterDetail_Short_Name = Common.DecryptString(jr.Value.ToString());
                    }
                    using (var jr = new JTokenReader(jObj["desc"]))
                    {
                        jr.Read();
                        refMasterDetail_Name = Common.DecryptString(jr.Value.ToString());
                    }

                    using (SqlConnection conn = new SqlConnection("your_connection_string"))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("UPDATE GNM_RefMasterDetailLocale SET RefMasterDetail_Short_Name = @ShortName, RefMasterDetail_Name = @Name, RefMaster_ID = @MasterID WHERE RefMasterDetailLocale_ID = @LocaleID", conn))
                        {
                            cmd.Parameters.AddWithValue("@ShortName", refMasterDetail_Short_Name);
                            cmd.Parameters.AddWithValue("@Name", refMasterDetail_Name);
                            cmd.Parameters.AddWithValue("@MasterID", LocaleUpdateObj.mid);
                            cmd.Parameters.AddWithValue("@LocaleID", refMasterDetailLocale_ID);

                            cmd.ExecuteNonQuery();
                        }
                    }
                }

                // Assuming gbl and other dependencies are properly initialized and accessible
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(LocaleUpdateObj.Company_ID),
                //    Convert.ToInt32(LocaleUpdateObj.Branch),
                //    Convert.ToInt32(LocaleUpdateObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("Master",constring)),
                //    LocaleUpdateObj.mid, 0, 0, "Update", false, Convert.ToInt32(LocaleUpdateObj.MenuID)
                //);

                jsonData = new
                {
                    IsSuccess = true
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonData = new
                {
                    IsSuccess = true
                };
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: DeleteReferenceMasterDetail /Mithun:::
        /// <summary>
        /// Delete Reference MasterDetail
        /// </summary>
        /// <returns></returns>

        public static IActionResult DeleteReferenceMasterDetail(DeleteReferenceMasterDetailList DeleteReferenceMasterDetailObj, string constring, int LogException)
        {
            var jsonData = new { IsSuccess = false, IsDependent = false, Msg = string.Empty };
            string exMsg = string.Empty;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction
                    transaction = connection.BeginTransaction("DeleteReferenceTransaction");

                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        string key = DeleteReferenceMasterDetailObj.key;
                        JObject jObj = JObject.Parse(key);
                        int rowcount = jObj["rows"].Count();
                        int id = 0;
                        for (int i = 0; i < rowcount; i++)
                        {
                            id = Convert.ToInt32(jObj["rows"][i]["id"]);

                            command.CommandText = "SELECT * FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @id";
                            command.Parameters.Clear();
                            command.Parameters.AddWithValue("@id", id);
                            SqlDataReader reader = command.ExecuteReader();
                            if (reader.HasRows)
                            {
                                reader.Read();
                                int refMaster_ID = (int)reader["RefMaster_ID"];
                                string refMasterDetail_Name = reader["RefMasterDetail_Name"].ToString();
                                reader.Close();

                                command.CommandText = "DELETE FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @id";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@id", id);
                                command.ExecuteNonQuery();

                                //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteReferenceMasterDetailObj.Company_ID), Convert.ToInt32(DeleteReferenceMasterDetailObj.Branch), Convert.ToInt32(DeleteReferenceMasterDetailObj.User_ID), Convert.ToInt32(Common.GetObjectID("Master",constring)), refMaster_ID, 0, 0, "Delete " + refMasterDetail_Name, false, Convert.ToInt32(DeleteReferenceMasterDetailObj.MenuID), Convert.ToDateTime(DeleteReferenceMasterDetailObj.LoggedINDateTime));
                            }
                            else
                            {
                                reader.Close();
                            }
                        }
                        transaction.Commit();

                        jsonData = new
                        {
                            IsSuccess = true,
                            IsDependent = false,
                            Msg = CommonFunctionalities.GetResourceString(DeleteReferenceMasterDetailObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString()
                        };
                        exMsg = CommonFunctionalities.GetResourceString(DeleteReferenceMasterDetailObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            exMsg += CommonFunctionalities.GetResourceString(DeleteReferenceMasterDetailObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            jsonData = new
                            {
                                IsSuccess = false,
                                IsDependent = true,
                                Msg = CommonFunctionalities.GetResourceString(DeleteReferenceMasterDetailObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString()
                            };
                        }
                        else
                        {
                            exMsg = "Error";
                            jsonData = new
                            {
                                IsSuccess = false,
                                IsDependent = true,
                                Msg = "Error"
                            };
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                exMsg = "SQL Error: " + ex.Message;
                jsonData = new
                {
                    IsSuccess = false,
                    IsDependent = true,
                    Msg = "SQL Error: " + ex.Message
                };
            }
            //return exMsg;
            return new JsonResult(exMsg);
        }

        #endregion

        #region ::: Delete Reference Master /Mithun:::
        /// <summary>
        ///  Delete Reference Master
        /// </summary>
        /// <returns></returns>
        public static IActionResult DeleteReferenceMaster(DeleteReferenceMasterList DeleteReferenceMasterObj, string constring, int LogException)
        {
            string exMsg = string.Empty;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction
                    transaction = connection.BeginTransaction("DeleteReferenceTransaction");

                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        string key = DeleteReferenceMasterObj.key;
                        JObject jObj = JObject.Parse(key);
                        int rowcount = jObj["rows"].Count();
                        int id = 0;
                        for (int i = 0; i < rowcount; i++)
                        {
                            id = Convert.ToInt32(jObj["rows"][i]["id"]);

                            command.CommandText = "SELECT * FROM GNM_RefMaster WHERE RefMaster_ID = @id";
                            command.Parameters.Clear();
                            command.Parameters.AddWithValue("@id", id);
                            SqlDataReader reader = command.ExecuteReader();
                            if (reader.HasRows)
                            {
                                reader.Read();
                                int refMaster_ID = (int)reader["RefMaster_ID"];
                                reader.Close();

                                command.CommandText = "DELETE FROM GNM_RefMaster WHERE RefMaster_ID = @id";
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@id", id);
                                command.ExecuteNonQuery();

                                //   gbl.InsertGPSDetails(Convert.ToInt32(DeleteReferenceMasterObj.Company_ID), Convert.ToInt32(DeleteReferenceMasterObj.Branch), Convert.ToInt32(DeleteReferenceMasterObj.User_ID), Convert.ToInt32(Common.GetObjectID("Master",constring)), refMaster_ID, 0, 0, "Delete", false, Convert.ToInt32(DeleteReferenceMasterObj.MenuID), Convert.ToDateTime(DeleteReferenceMasterObj.LoggedINDateTime));
                            }
                            else
                            {
                                reader.Close();
                            }
                        }
                        transaction.Commit();

                        exMsg = CommonFunctionalities.GetResourceString(DeleteReferenceMasterObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            exMsg += CommonFunctionalities.GetResourceString(DeleteReferenceMasterObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            exMsg = CommonFunctionalities.GetResourceString(DeleteReferenceMasterObj.GeneralCulture.ToString(), "Error").ToString();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                exMsg = "SQL Error: " + ex.Message;
            }
            //return exMsg;
            return new JsonResult(exMsg);
        }


        #endregion

        #region ::: LocaleDelete /Mithun :::
        /// <summary>
        /// LocaleDelete
        /// </summary>
        /// <param name="id"></param>

        public static IActionResult LocaleDelete(LocaleDeleteList LocaleDeleteObj, string constring, int LogException)
        {
            string msg = "Error";
            try
            {

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = "DELETE FROM GNM_RefMasterDetailLocale WHERE RefMasterDetailLocale_ID = @id";

                    using (var cmd = new SqlCommand(query, connection))
                    {
                        cmd.Parameters.AddWithValue("@id", LocaleDeleteObj.id);
                        cmd.ExecuteNonQuery();
                    }
                }
                msg = "Deleted";
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(new { message = msg });
        }
        #endregion

        #region ::: Check Master Exists /Mithun :::
        /// <summary>
        /// Check Master Exists
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>

        public static IActionResult CheckMasterExists(CheckMasterExistsList CheckMasterExistsObj, string constring, int LogException)
        {
            string data = "false";

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "UP_AMERP_CheckMasterExists";
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@name", CheckMasterExistsObj.name);

                    SqlDataReader reader = command.ExecuteReader();
                    if (reader.HasRows)
                    {
                        reader.Read();
                        data = reader["Result"].ToString();
                    }
                    reader.Close();
                }
            }
            catch (SqlException ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return data;
            return new JsonResult(data);
        }


        #endregion

        #region :::  Check If Description Exists /Mithun:::
        /// <summary>
        ///  Check If Description Exists
        /// </summary>
        /// <returns></returns>
        public static IActionResult CheckDescriptionExists(CheckDescriptionExistsList CheckDescriptionExistsObj, string constring, int LogException)
        {
            string data = "false";
            string name = Common.DecryptString(CheckDescriptionExistsObj.name);

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();

                    int Company_ID = Convert.ToInt32(CheckDescriptionExistsObj.Company_ID);

                    command.CommandText = "CountRefMasterDetail";
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@id", CheckDescriptionExistsObj.id);
                    command.Parameters.AddWithValue("@pk", CheckDescriptionExistsObj.pk);
                    command.Parameters.AddWithValue("@name", name.ToLower());
                    command.Parameters.AddWithValue("@companyId", Company_ID);

                    int count = (int)command.ExecuteScalar();
                    data = count > 0 ? "true" : "false";
                }
            }
            catch (SqlException ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return data;
            return new JsonResult(data);
        }

        #endregion

        #region ::: Check Code Exist /Mithun:::
        /// <summary>
        /// Check Code Exist
        /// </summary>
        /// <returns></returns>

        public string CheckCodeExists(CheckCodeExistsList CheckCodeExistsObj, string constring, int LogException)
        {
            string data = string.Empty;
            string name = Common.DecryptString(CheckCodeExistsObj.name);
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    int Company_ID = Convert.ToInt32(CheckCodeExistsObj.Company_ID);

                    command.CommandText = "CountRefMasterDetailByCode";
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@id", CheckCodeExistsObj.id);
                    command.Parameters.AddWithValue("@pk", CheckCodeExistsObj.pk);
                    command.Parameters.AddWithValue("@name", name.ToLower());
                    command.Parameters.AddWithValue("@companyId", Company_ID);

                    int count = (int)command.ExecuteScalar();
                    data = count > 0 ? "true" : "false";
                }
            }
            catch (SqlException ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return data;
        }


        #endregion

        #region ::: CheckLocaleExists /Mithun:::
        /// <summary>
        /// CheckLocaleExists
        /// </summary>
        public static IActionResult CheckLocaleExists(CheckLocaleExistsList CheckLocaleExistsOBj, string constring, int LogException)
        {
            var jsonData = new { IsCode = false, IsDescrip = false }; // Initialize with default values
            try
            {
                string code = Common.DecryptString(CheckLocaleExistsOBj.code);
                string Description = Common.DecryptString(CheckLocaleExistsOBj.Description);
                int Language_ID = Convert.ToInt32(CheckLocaleExistsOBj.LanguageID);
                int Company_ID = Convert.ToInt32(CheckLocaleExistsOBj.Company_ID);

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Check if the code exists
                    bool IsCode = false;
                    string codeQuery = @"
                SELECT COUNT(*) 
                FROM GNM_RefMasterDetailLocale a
                JOIN GNM_RefMaster b ON a.RefMaster_ID = b.RefMaster_ID
                JOIN GNM_RefMasterDetail c ON a.RefMasterDetail_ID = c.RefMasterDetail_ID
                WHERE a.RefMaster_ID = @RefMasterID 
                AND a.Language_ID = @LanguageID 
                AND a.RefMasterDetailLocale_ID != @LocaleID 
                AND a.RefMasterDetail_Short_Name = @Code 
                AND (@IsCompanySpecific = 0 OR c.Company_ID = @CompanyID)";

                    using (var cmd = new SqlCommand(codeQuery, connection))
                    {
                        cmd.Parameters.AddWithValue("@RefMasterID", CheckLocaleExistsOBj.id);
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                        cmd.Parameters.AddWithValue("@LocaleID", CheckLocaleExistsOBj.localeid);
                        cmd.Parameters.AddWithValue("@Code", code);
                        cmd.Parameters.AddWithValue("@IsCompanySpecific", Company_ID);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);

                        IsCode = (int)cmd.ExecuteScalar() > 0;
                    }

                    // Check if the description exists
                    bool IsDescrip = false;
                    string descriptionQuery = @"
                SELECT COUNT(*) 
                FROM GNM_RefMasterDetailLocale a
                JOIN GNM_RefMaster b ON a.RefMaster_ID = b.RefMaster_ID
                JOIN GNM_RefMasterDetail c ON a.RefMasterDetail_ID = c.RefMasterDetail_ID
                WHERE a.RefMaster_ID = @RefMasterID 
                AND a.Language_ID = @LanguageID 
                AND a.RefMasterDetailLocale_ID != @LocaleID 
                AND a.RefMasterDetail_Name = @Description 
                AND (@IsCompanySpecific = 0 OR c.Company_ID = @CompanyID)";

                    using (var cmd = new SqlCommand(descriptionQuery, connection))
                    {
                        cmd.Parameters.AddWithValue("@RefMasterID", CheckLocaleExistsOBj.id);
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                        cmd.Parameters.AddWithValue("@LocaleID", CheckLocaleExistsOBj.localeid);
                        cmd.Parameters.AddWithValue("@Description", Description);
                        cmd.Parameters.AddWithValue("@IsCompanySpecific", Company_ID);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);

                        IsDescrip = (int)cmd.ExecuteScalar() > 0;
                    }

                    jsonData = new
                    {
                        IsCode = IsCode,
                        IsDescrip = IsDescrip
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion



        private static IQueryable<ReferenceMaster> GetReferenceMasterData(SelectCoreReferenceMasterList Obj, string constring)
        {
            List<ReferenceMaster> referenceMasterList = new List<ReferenceMaster>();

            using (SqlConnection conn = new SqlConnection(constring))
            {
                string storedProcedure = "[dbo].[UP_SEL_AMERP_GetCustomRefMasters]";
                SqlCommand cmd = new SqlCommand(storedProcedure, conn);
                cmd.CommandType = CommandType.StoredProcedure;
                conn.Open();

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        referenceMasterList.Add(new ReferenceMaster
                        {
                            RefMaster_ID = (int)reader["RefMaster_ID"],
                            RefMaster_Name = (string)reader["RefMaster_Name"],
                            IsCompanySpecific = (bool)reader["IsCompanySpecific"] ? "Yes" : "No"
                        });
                    }
                }
            }

            IQueryable<ReferenceMaster> gnmPrtySpcl = referenceMasterList.AsQueryable();

            return gnmPrtySpcl;
        }


        public static async Task<object> Export(SelectCoreReferenceMasterList Obj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                DataTable DtData = new DataTable();
                int BranchID = Convert.ToInt32(Obj.Branch);
                // GNM_User User = (GNM_User)Session["UserDetails"];

                // Fetch filtered and sorted data using the new method
                IQueryable<ReferenceMaster> wf = GetReferenceMasterData(Obj, constring);

                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    wf = wf.FilterSearch(filtersobj);
                }

                // Advance Search
                if (Query != "null" && Query != "undefined")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    wf = wf.AdvanceSearch(advnfilter);
                }

                // Sorting
                wf = wf.OrderByField(sidx, sord);


                int Count = wf.Count();
                if (Count > 0)
                {
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "MasterName").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsCompanySpecific").ToString() + "?");

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("RefMaster_Name");
                    DtAlignment.Columns.Add("IsCompanySpecific");

                    DtAlignment.Rows.Add(0, 1);
                    foreach (var item in wf)
                    {
                        DtData.Rows.Add(item.RefMaster_Name, item.IsCompanySpecific);
                    }

                    ExportList reportExportList = new ExportList
                    {
                        Company_ID = Obj.Company_ID, // Assuming this is available in ExportObj
                        Branch = Obj.Branch,
                        dt1 = DtData,


                        dt = DtData,

                        FileName = "ReferenceMaster", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ReferenceMasters").ToString(), // Set a default or dynamic header
                        exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = Obj.UserCulture
                    };


                    var Result = await DocumentExport.Export(reportExportList, constring, LogException);
                    return Result.Value;



                    //DocumentExport.Export(exprtType, DtData, DtAlignment,
                    //    HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "ReferenceMasters").ToString(),
                    //    HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "ReferenceMasters").ToString());

                    //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), BranchID, User.User_ID,
                    //    Common.GetObjectID("Master"), 0, 0, 0,
                    //    HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "ReferenceMasters").ToString() + "-Export",
                    //    false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }




        public class CheckLocaleExistsList
        {
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public int id { get; set; }
            public int localeid { get; set; }
            public string code { get; set; }
            public string Description { get; set; }

        }
        public class CheckCodeExistsList
        {
            public string name { get; set; }
            public int id { get; set; }
            public int pk { get; set; }
            public int Company_ID { get; set; }
        }
        public class CheckDescriptionExistsList
        {
            public string name { get; set; }
            public int id { get; set; }
            public int pk { get; set; }
            public int Company_ID { get; set; }
        }
        public class CheckMasterExistsList
        {
            public string name { get; set; }
        }
        public class LocaleDeleteList
        {
            public int id { get; set; }
        }

        public class DeleteReferenceMasterList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public string GeneralCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class DeleteReferenceMasterDetailList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public string GeneralCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class LocaleUpdateList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int mid { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class MasterDetailLocaleList
        {
            public int id { get; set; }
        }
        public class MasterDetailLocaleDataList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int id { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class MasterDetailDataList
        {
            public int Company_ID { get; set; }
            public int id { get; set; }
        }
        public class SaveMasterDetailList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
        }
        public class SelectCoreReferenceMasterList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int exprtType { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
            public bool _search { get; set; }
        }

        public class ReferenceMaster
        {


            public int RefMaster_ID
            {
                get;

                set;

            }
            public string RefMaster_Name
            {
                get;

                set;

            }
            public string IsCompanySpecific
            {
                get;

                set;
            }

        }
    }
}
